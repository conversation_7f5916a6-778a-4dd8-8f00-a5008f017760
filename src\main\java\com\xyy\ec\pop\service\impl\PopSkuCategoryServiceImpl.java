package com.xyy.ec.pop.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.config.ProductCommissionUpdateConfig;
import com.xyy.ec.pop.excel.verify.ProductCommissionUpdateValid;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.ProductCommissionHelper;
import com.xyy.ec.pop.remote.PopSkuCategoryRemote;
import com.xyy.ec.pop.remote.ProductSkuRemoteAdapter;
import com.xyy.ec.pop.server.api.Enum.CommissionRatioOperateEnum;
import com.xyy.ec.pop.server.api.product.dto.PopSkuCategoryDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuCommissionRatioOperateLogDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.server.api.product.dto.ProductCommissionDto;
import com.xyy.ec.pop.server.api.product.enums.ActivityTypeEnum;
import com.xyy.ec.pop.server.api.product.enums.PopSkuStatus;
import com.xyy.ec.pop.service.FastDfsUtilService;
import com.xyy.ec.pop.service.PopSkuCategoryService;
import com.xyy.ec.pop.service.ProductCategoryService;
import com.xyy.ec.pop.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PopSkuCategoryServiceImpl implements PopSkuCategoryService {
    @Autowired
    private PopSkuCategoryRemote popSkuCategoryRemote;
    @Autowired
    private ProductCommissionUpdateConfig productCommissionUpdateConfig;
    @Autowired
    private FastDfsUtilService fastDfsUtilService;
    @Autowired
    private ProductCategoryService productCategoryService;
    @Autowired
    private ProductSkuRemoteAdapter productSkuRemoteAdapter;

    @Override
    public Page<ProductCommissionVo> list(ProductCommissionQueryVo queryVo) {
        PageInfo<ProductCommissionDto> pageInfo = popSkuCategoryRemote.commissionPage(ProductCommissionHelper.convertToQuery(queryVo));
        Page<ProductCommissionVo> page = new Page();
        page.setTotal(pageInfo.getTotal());
        page.setRows(ProductCommissionHelper.convertToCommissionVos(pageInfo.getList()));
        page.setPageCount(pageInfo.getPages());
        page.setCurrentPage(pageInfo.getPageNum());
        //设置分类名称
        List<Integer> cateIds = page.getRows().stream().map(item -> NumberUtils.toInt(item.getBusinessFirstCategoryCode())).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(cateIds)){
            return page;
        }
        Map<String, String> cateMap = productCategoryService.getFourLevelProductCategoryByIds(cateIds).stream().collect(Collectors.toMap(item -> item.getId().toString(), item -> item.getDictName(), (item1, item2) -> item1));
        for (ProductCommissionVo vo : page.getRows()) {
            vo.setBusinessFirstCategoryName(cateMap.get(vo.getBusinessFirstCategoryCode()));
        }
        return page;
    }

    @Override
    public BatchUpdateResultVo batchUpdate(List<ProductCommissionBatchUpdateVo> vos, String userName) throws ServiceException {
        ProductCommissionUpdateValid.trim(vos);
        //填充barcode
        fillBarcodes(vos);
//        Map<String, Integer> barcodeActivityTypeMap = getBarcodeActivityTypeMap(vos);
        Map<String, PopSkuDto> barcodeSkuMap = getBarcodeSkuMap(vos);
        Map<String, Integer> barcodeActivityTypeMap = getBarcodeActivityTypeMapBySkuMap(barcodeSkuMap);
        Map<String, PopSkuCategoryDto> activityCateMap = getActivitySkuCateInfo(barcodeActivityTypeMap);
        //高毛的门槛，活动品设置高毛需大于此值，如果没有活动品，则不需要查
        BigDecimal highGrossThreshold = activityCateMap.isEmpty()?BigDecimal.ZERO:popSkuCategoryRemote.getHighGrossThreshold();
        //校验佣金比例
        ProductCommissionUpdateValid.valid(vos, productCommissionUpdateConfig, barcodeSkuMap,activityCateMap,highGrossThreshold);
        List<ProductCommissionBatchUpdateVo> toUpdate = vos.stream().filter(item -> item.getErrorMessage() == null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(toUpdate)){
            log.info("批量设置佣金比例：没有可设置商品:userName:{},result:{}",userName, JSON.toJSONString(vos));
            return batchUpdateResultVoBase(vos.size(),vos);
        }
        //远程更新
        List<ProductCommissionDto> dtos = ProductCommissionHelper.convertToCommissionUpdateDtos(toUpdate);
        List<ProductCommissionDto> productCommissionDtos = popSkuCategoryRemote.batchUpdateCommission(dtos, userName);
        Map<String, ProductCommissionDto> resultMap = productCommissionDtos.stream().collect(Collectors.toMap(item -> item.getBarcode(), item -> item));
        //返回更新结果
        toUpdate.forEach(item->{
            ProductCommissionDto result = resultMap.get(item.getBarcode());
            item.setErrorMessage(result==null?null:result.getErrorMessage());
        });
        List<ProductCommissionBatchUpdateVo> errors = vos.stream().filter(item -> item.getErrorMessage() != null).collect(Collectors.toList());
        return batchUpdateResultVoBase(vos.size(),errors);
    }

    /**
     * 获取活动品分类信息
     * @param barcodeActivityTypeMap
     */
    private Map<String,PopSkuCategoryDto> getActivitySkuCateInfo(Map<String, Integer> barcodeActivityTypeMap) {
        List<String> barcodes = barcodeActivityTypeMap.entrySet().stream().filter(item -> Objects.equals(item.getValue(), ActivityTypeEnum.GROUP.getCode())).map(item -> item.getKey()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(barcodes)){
            return new HashMap<>();
        }
        List<PopSkuCategoryDto> commissionDtos = popSkuCategoryRemote.categoryByBarcodes(barcodes);
        return commissionDtos.stream().collect(Collectors.toMap(item->item.getBarcode(),item->item));
    }

    private Map<String, Integer> getBarcodeActivityTypeMap(List<ProductCommissionBatchUpdateVo> vos) throws ServiceException {
        List<String> barcodes = vos.stream().map(item -> item.getBarcode()).distinct().collect(Collectors.toList());
        Map<String, Integer> allBarcodeActivityTypeMap = Maps.newHashMap();
        List<List<String>> partition = Lists.partition(barcodes, 200);
        for (List<String> barcodeSubList : partition) {
            List<PopSkuDto> popSkuDtos = productSkuRemoteAdapter.findSkuByBarCodes(barcodeSubList);
            Map<String, Integer> barcodeActivityTypeMap = popSkuDtos.stream().filter(f -> f.getActivityType() != null).collect(Collectors.toMap(item -> item.getBarcode(), item -> item.getActivityType()));
            allBarcodeActivityTypeMap.putAll(barcodeActivityTypeMap);
        }
        return allBarcodeActivityTypeMap;
    }

    private Map<String, Integer> getBarcodeActivityTypeMapBySkuMap(Map<String, PopSkuDto> barcodeSkuMap) throws ServiceException {
        if(MapUtils.isEmpty(barcodeSkuMap)){
            return Maps.newHashMap();
        }
        Map<String, Integer> allBarcodeActivityTypeMap = Maps.newHashMap();
        for(PopSkuDto skuDto : barcodeSkuMap.values()){
            Integer activityType = skuDto.getActivityType();
            if(null != activityType){
                allBarcodeActivityTypeMap.put(skuDto.getBarcode(), activityType);
            }
        }
        return allBarcodeActivityTypeMap;
    }

    private Map<String, PopSkuDto> getBarcodeSkuMap(List<ProductCommissionBatchUpdateVo> vos) throws ServiceException {
        List<String> barcodes = vos.stream().map(item -> item.getBarcode()).distinct().collect(Collectors.toList());
        Map<String, PopSkuDto> allBarcodeSkuMap = Maps.newHashMap();
        List<List<String>> partition = Lists.partition(barcodes, 200);
        for (List<String> barcodeSubList : partition) {
            List<PopSkuDto> popSkuDtos = productSkuRemoteAdapter.findSkuByBarCodes(barcodeSubList);
            Map<String, PopSkuDto> barcodeActivityTypeMap = popSkuDtos.stream().collect(Collectors.toMap(PopSkuDto::getBarcode, Function.identity(),(o1, o2) -> o1));
            allBarcodeSkuMap.putAll(barcodeActivityTypeMap);
        }
        return allBarcodeSkuMap;
    }

    /**
     * 填充barcode
     * @param vos
     */
    private void fillBarcodes(List<ProductCommissionBatchUpdateVo> vos){
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        List<Long> csuidList = vos.stream().map(vo -> vo.getCsuid()).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(csuidList)){
            return;
        }
        Map<Long, PopSkuDto> popSkuByCsuidMap = productSkuRemoteAdapter.getPopSkuByCsuidList(csuidList);
        if(MapUtils.isEmpty(popSkuByCsuidMap)){
            return;
        }
        for(ProductCommissionBatchUpdateVo vo : vos){
            PopSkuDto skuDto = popSkuByCsuidMap.get(vo.getCsuid());
            if(null != skuDto){
                vo.setBarcode(skuDto.getBarcode());
            }
        }
    }

    @Override
    public boolean updateCommission(ProductCommissionBatchUpdateVo updateVo, String username) throws ServiceException {
        if(StringUtils.isEmpty(updateVo.getBarcode())){
            throw new ServiceException("缺少更新参数");
        }
        //校验商品是否是拼团商品
        PopSkuDto popSkuDto = productSkuRemoteAdapter.getSkuByBarcode(updateVo.getBarcode());
        if (popSkuDto == null) {
            throw new ServiceException("不存在该商品");
        }
        if (Objects.equals(popSkuDto.getActivityType(), ActivityTypeEnum.GIFT.getCode())) {
            throw new ServiceException(popSkuDto.getShowName() + "商品为"+(ActivityTypeEnum.getOuterNameByCode(popSkuDto.getActivityType()))+"，不允许设置佣金比例");
        }
        if(StringUtils.isEmpty(updateVo.getCommissionRatio())){
            throw new ServiceException("请填写佣金比例");
        }
        if(!updateVo.getCommissionRatio().matches(productCommissionUpdateConfig.getCommissionPattern())){
            throw new ServiceException("0<佣金比例<100，仅允许录入整数");
        }
        ProductCommissionDto dto = ProductCommissionHelper.convertToCommissionUpdateDto(updateVo);
        return popSkuCategoryRemote.updateCommission(dto,username);
    }

    @Override
    public CommissionRatioOperateLogVo queryCommissionRatioLog(String barcode) throws ServiceException {
        PopSkuDto popSkuDto = productSkuRemoteAdapter.getSkuByBarcode(barcode);
        if (popSkuDto == null) {
            throw new ServiceException("不存在该商品");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<PopSkuCommissionRatioOperateLogDto> popSkuCommissionRatioOperateLogDtoList = popSkuCategoryRemote.queryCommissionRatioLog(barcode);
        List<CommissionLogVo> commissionLogVoList = popSkuCommissionRatioOperateLogDtoList.stream().map(operateLogDto -> {
            String operateType = CommissionRatioOperateEnum.getMsgByCode(operateLogDto.getOperateType()).getMsg();
            if (Objects.equals(operateLogDto.getOperateType(), CommissionRatioOperateEnum.AUTO.getCode())) {
                operateType = operateType + "\n" + "框架"  + "\n" + "ID:" + operateLogDto.getMarketingId();
            }
            return CommissionLogVo.builder()
                    .operateContent(operateLogDto.getOperateContent())
                    .operateType(operateType)
                    .createTime(sdf.format(operateLogDto.getCreateTime()))
                    .operateUser(operateLogDto.getOperateUser())
                    .build();
        }).collect(Collectors.toList());
        return CommissionRatioOperateLogVo.builder()
                .barcode(barcode)
                .csuId(popSkuDto.getCsuid())
                .showName(popSkuDto.getShowName())
                .popSkuCommissionRatioOperateLogDtoList(CollectionUtils.isEmpty(commissionLogVoList) ? Lists.newArrayList() : commissionLogVoList).build();
    }

    @Override
    public boolean deleteCommission(List<String> barcodes, String username) throws ServiceException {
        //校验商品是否是拼团商品
        List<PopSkuDto> dtos = productSkuRemoteAdapter.findSkuByBarCodes(barcodes);
        List<String> has = dtos.stream().filter(item -> !Objects.equals(item.getStatus(), PopSkuStatus.DELETE.getValue())).map(item -> item.getBarcode()).collect(Collectors.toList());
        List<String> notHav = barcodes.stream().filter(item -> !has.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notHav)) {
            throw new ServiceException("商品"+StringUtils.join(notHav,",")+"不存在");
        }
        return popSkuCategoryRemote.deleteCommission(barcodes, username);
    }


    /**
     * 记录失败文件
     * @param totalSize
     * @param errorVos
     * @return
     */
    private BatchUpdateResultVo batchUpdateResultVoBase(int totalSize, List<ProductCommissionBatchUpdateVo> errorVos) {
        BatchUpdateResultVo resultVo = new BatchUpdateResultVo();
        resultVo.setError(errorVos.size());
        resultVo.setSuccess(totalSize - resultVo.getError());
        //将数据写入excel文件
        if(errorVos.size()>0){
            String fileUrl = fastDfsUtilService.writeDateToFastDfs(new ExportParams(null, "设置失败商品", ExcelType.XSSF), ProductCommissionBatchUpdateVo.class, new ArrayList<>(errorVos));
            resultVo.setErrorFileUrl(fileUrl);
        }
        return resultVo;
    }
}
