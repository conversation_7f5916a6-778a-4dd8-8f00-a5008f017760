package com.xyy.ec.pop.marketing.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarketingActivitySaleDataStatusCountInfoVO implements Serializable {

    /**
     * 待付款数量
     */
    private Long unpaidCount;

    /**
     * 已付款数量
     */
    private Long paidCount;

}
