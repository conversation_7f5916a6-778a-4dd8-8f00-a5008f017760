package com.xyy.ec.pop.helper;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.enums.PopAnnounceStatusEnum;
import com.xyy.ec.pop.server.api.Enum.AnnounceTypeEnum;
import com.xyy.ec.pop.server.api.announce.dto.PopAnnounceDto;
import com.xyy.ec.pop.server.api.announce.dto.PopAnnounceQueryParamDto;
import com.xyy.ec.pop.vo.PopAnnounceQueryParamVo;
import com.xyy.ec.pop.vo.PopAnnounceVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: Jincheng.Li
 * @Date: 2021/07/14/14:41
 * @Description:
 */
public class PopAnnounceHelper {
    public static PopAnnounceVo covertDtoToVo(PopAnnounceDto dto) {
        if (dto == null) {
            return null;
        }
        PopAnnounceVo popAnnounceVo = new PopAnnounceVo();
        popAnnounceVo.setId(dto.getId());
        popAnnounceVo.setTitle(dto.getTitle());
        popAnnounceVo.setUrl(dto.getImageUrl());
        popAnnounceVo.setStatus(dto.getStatus());
        popAnnounceVo.setStatusName(PopAnnounceStatusEnum.get(dto.getStatus()));
        popAnnounceVo.setSort(dto.getSort());
        popAnnounceVo.setContent(dto.getContent());
        popAnnounceVo.setCreateTime(dateSdf(dto.getCreateTime()));
        popAnnounceVo.setUpdateTime(dateSdf(dto.getUpdateTime()));
        popAnnounceVo.setCreateBy(dto.getCreateBy());
        popAnnounceVo.setUpdateBy(dto.getUpdateBy());
        popAnnounceVo.setIsShowBanner(dto.getIsShowBanner());
        popAnnounceVo.setAnnounceType(dto.getType());
        popAnnounceVo.setRedTitle(dto.getRedTitle());
        popAnnounceVo.setAnnounceTypeDesc(AnnounceTypeEnum.getNameByCode(dto.getType()));
        popAnnounceVo.setShowDialog(dto.getShowDialog());
        popAnnounceVo.setShowDialogStr(dto.getShowDialogStr());
        popAnnounceVo.setApplicableTypeName(dto.getApplicableTypeName());
        popAnnounceVo.setApplicableCode(dto.getApplicableCode());
        popAnnounceVo.setApplicableType(dto.getApplicableType());
        return popAnnounceVo;
    }

    public static List<PopAnnounceVo> covertDtoListToVoList(List<PopAnnounceDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Lists.newArrayList();
        }
        return dtos.stream().map(PopAnnounceHelper::covertDtoToVo).collect(Collectors.toList());
    }

    public static PageInfo<PopAnnounceVo> covertPage(PageInfo<PopAnnounceDto> pageInfo) {
        PageInfo<PopAnnounceVo> voPageInfo = new PageInfo<>();
        if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
            voPageInfo.setPageNum(0);
            voPageInfo.setPages(0);
            voPageInfo.setTotal(0);
            voPageInfo.setPageSize(10);
            voPageInfo.setList(Lists.newArrayList());
            return voPageInfo;
        }
        voPageInfo.setPageNum(pageInfo.getPageNum());
        voPageInfo.setPageSize(pageInfo.getPageSize());
        voPageInfo.setPages(pageInfo.getPages());
        voPageInfo.setTotal(pageInfo.getTotal());
        voPageInfo.setList(covertDtoListToVoList(pageInfo.getList()));
        return voPageInfo;
    }

    public static PopAnnounceQueryParamDto covertQueryParamVoToDto(PopAnnounceQueryParamVo paramVo) {
        if (paramVo == null) {
            return null;
        }
        PopAnnounceQueryParamDto paramDto = new PopAnnounceQueryParamDto();
        paramDto.setTitle(paramVo.getTitle());

        paramDto.setCreateTimeStart(paramVo.getCreateTimeStart() == null ? null : new Date(paramVo.getCreateTimeStart()));
        paramDto.setCreateTimeEnd(paramVo.getCreateTimeEnd() == null ? null : new Date(paramVo.getCreateTimeEnd()));
        paramDto.setUpdateTimeStart(paramVo.getUpdateTimeStart() == null ? null : new Date(paramVo.getUpdateTimeStart()));
        paramDto.setUpdateTimeEnd(paramVo.getUpdateTimeEnd() == null ? null : new Date(paramVo.getUpdateTimeEnd()));
        paramDto.setStatus(paramVo.getStatus());
        paramDto.setPageNum(paramVo.getPageNum());
        paramDto.setPageSize(paramVo.getPageSize());
        paramDto.setType(paramVo.getType());
        paramDto.setIsShowBanner(paramVo.getIsShowBanner());
        paramDto.setRedTitle(paramVo.getRedTitle());
        paramDto.setApplicableType(paramVo.getApplicableType());
        paramDto.setShowDialog(paramVo.getShowDialog());
        paramDto.setApplicableCode(paramVo.getApplicableCode());
        paramDto.setSortType(paramVo.getSortType());
        return paramDto;
    }

    public static PopAnnounceDto covertVoToDto(PopAnnounceVo vo, String user) {
        if (vo == null) {
            return null;
        }
        PopAnnounceDto dto = new PopAnnounceDto();
        if (vo.getId() == null) {
            dto.setCreateBy(user);
        }
        dto.setId(vo.getId());
        dto.setTitle(vo.getTitle());
        dto.setImageUrl(vo.getUrl());
        dto.setStatus(vo.getStatus());
        dto.setSort(vo.getSort());
        dto.setContent(vo.getContent());
        dto.setValid(1);
        dto.setUpdateBy(user);
        dto.setIsShowBanner(vo.getIsShowBanner());
        dto.setType(vo.getAnnounceType());
        dto.setRedTitle(vo.getRedTitle());
        dto.setApplicableType(vo.getApplicableType());
        dto.setShowDialog(vo.getShowDialog());
        dto.setApplicableCode(vo.getApplicableCode());
        return dto;
    }

    public static String dateSdf(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String nowStr = localDateTime.format(format);
        return nowStr;
    }
}
