//package com.xyy.ec.pop.service.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.google.common.collect.Lists;
//import com.xyy.ec.pop.exception.PopAdminException;
//import com.xyy.ec.pop.remote.CommissionDiscountConfigRemote;
//import com.xyy.ec.pop.server.api.Enum.EnjoyCommissionDiscountEnum;
//import com.xyy.ec.pop.server.api.commission.dto.CommissionDiscountConfigDto;
//import com.xyy.ec.pop.service.CommissionDiscountConfigService;
//import com.xyy.ec.pop.vo.CommissionDiscountSetVo;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.math.BigDecimal;
//import java.util.List;
//import java.util.Objects;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @Auther: lijincheng
// * @Date: 2021/09/13/15:16
// * @Description:
// */
//@Service
//public class CommissionDiscountConfigServiceImpl implements CommissionDiscountConfigService {
//    @Autowired
//    private CommissionDiscountConfigRemote configRemote;
//
//    @Override
//    public Boolean saveCommissionDiscountConfig(String orgId, String userName, List<CommissionDiscountSetVo> vos, Integer flag) {
//        if (CollectionUtils.isNotEmpty(vos)) {
//            validCommissionConfig(vos);
//        }
//        CommissionDiscountConfigDto dto = new CommissionDiscountConfigDto();
//        dto.setOrgId(orgId);
//        dto.setCreateUser(userName);
//        dto.setUpdateUser(userName);
//        dto.setEnjoy(EnjoyCommissionDiscountEnum.getCodeByFlag(flag).byteValue());
//        dto.setConfig(CollectionUtils.isEmpty(vos) ? null : JSON.toJSONString(vos));
//        return configRemote.saveCommissionDiscountConfig(dto);
//    }
//
//    @Override
//    public List<CommissionDiscountSetVo> getCommissionDiscountConfigByOrgId(String orgId) {
//        try {
//            CommissionDiscountConfigDto commissionDiscountConfigDto = configRemote.getCommissionDiscountConfigByOrgId(orgId);
//            if (commissionDiscountConfigDto == null || commissionDiscountConfigDto.getEnjoy() == EnjoyCommissionDiscountEnum.ENJOY_FALSE.getCode().byteValue()) {
//                return Lists.newArrayList();
//            }
//            String config = commissionDiscountConfigDto.getConfig();
//            return JSONArray.parseArray(config, CommissionDiscountSetVo.class);
//        } catch (Exception e) {
//            return Lists.newArrayList();
//        }
//    }
//
//    private static void validCommissionConfig(List<CommissionDiscountSetVo> vos) {
//        if (vos.size() == 1) {
//            if (!bigDecimalFlag(vos.get(0))) {
//                throw new IllegalArgumentException("数据字段不能为空且必须大于0");
//            }
//        }
//        int l = vos.size() - 1;
//        for (int i = 0; i < l; i++) {
//            CommissionDiscountSetVo vo1 = vos.get(i);
//            CommissionDiscountSetVo vo2 = vos.get(i+1);
//            if (!bigDecimalFlag(vo1) || !bigDecimalFlag(vo2)) {
//                throw new IllegalArgumentException("数据字段不能为空且必须大于0");
//            }
//            if (vo1.getLimitMoney().compareTo(vo2.getLimitMoney()) > -1) {
//                throw new IllegalArgumentException("下一层的数据不能小于上一层的数据");
//            }
//        }
//    }
//
//    private static Boolean bigDecimalFlag(CommissionDiscountSetVo vo) {
//        if (Objects.isNull(vo)) {
//            return false;
//        }
//        if (vo.getLimitMoney() == null || vo.getDiscountRate() == null) {
//            return false;
//        }
//        if (vo.getLimitMoney().compareTo(BigDecimal.ZERO) < 1) {
//            return false;
//        }
//        if (vo.getDiscountRate().compareTo(BigDecimal.ZERO) < 1 || vo.getDiscountRate().compareTo(BigDecimal.ONE) == 1) {
//            return false;
//        }
//        return true;
//    }
//}
