package com.xyy.ec.pop.service;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationTypeEnum;
import com.xyy.ec.pop.vo.QualificationsCategoryRelationVo;

import java.util.List;

/**
 * @version v1
 * @Description 资质配置接口，逐步替代service项目中方法
 * <AUTHOR>
 */
public interface QualificationsConfigService {
    /**
     * 查询企业公共配置
     * @param corporationType
     * @return
     */
    QualificationsCategoryRelationVo commonQualificationConfigs(CorporationTypeEnum corporationType);

    /**
     * 经营类资质
     * @param corporationType
     * @return
     */
    List<QualificationsCategoryRelationVo> getBusinessQualificationConfigs(CorporationTypeEnum corporationType);

    void savePopQualifications(List<QualificationsCategoryRelationVo> relationVoList, byte corporationType, String username) throws ServiceException;
}
