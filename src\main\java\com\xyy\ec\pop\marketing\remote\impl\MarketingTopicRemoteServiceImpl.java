package com.xyy.ec.pop.marketing.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.marketing.remote.MarketingTopicRemoteService;
import com.xyy.ms.promotion.business.api.admin.MarketingTopicApi;
import com.xyy.ms.promotion.business.dto.marketingactivity.MarketingTopicDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class MarketingTopicRemoteServiceImpl implements MarketingTopicRemoteService {

    @Reference(version = "1.0.0")
    private MarketingTopicApi marketingTopicApi;


    @Override
    public List<MarketingTopicDTO> listTopicsByType(Integer type) {
        try {
            if (Objects.isNull(type)) {
                return Lists.newArrayList();
            }
            ApiRPCResult<List<MarketingTopicDTO>> apiRPCResult = marketingTopicApi.getTopicList(type);
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
                log.error("listTopicsByType 根据类型查询主题信息失败，type：{}，响应：{}", type, JSONObject.toJSONString(apiRPCResult));
                return Lists.newArrayList();
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("listTopicsByType 根据类型查询主题信息异常，type：{}", type, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<MarketingTopicDTO> listAllTopic() {
        try {
            ApiRPCResult<List<MarketingTopicDTO>> apiRPCResult = marketingTopicApi.getAllTopicList();
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
                log.error("listAllTopic 查询所有主题信息失败，响应：{}", JSONObject.toJSONString(apiRPCResult));
                return Lists.newArrayList();
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("listTopicsByType 查询所有主题信息异常", e);
            return Lists.newArrayList();
        }
    }
}
