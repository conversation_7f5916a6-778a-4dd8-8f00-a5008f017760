package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 账期状态
 * <AUTHOR>
 */
public enum PrePaymentFlowLogEnum {

    TYPE_ADD(1,"金额增加"),
    TYPE_SUB(2,"金额减少");
    private  int id;
    private  String value;
    PrePaymentFlowLogEnum(int id, String value){
        this.id = id;
        this.value = value;
    }
    private static Map<Integer, PrePaymentFlowLogEnum> controlMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(PrePaymentFlowLogEnum apEnum : PrePaymentFlowLogEnum.values()) {
        	controlMaps.put(apEnum.getId(), apEnum);
            maps.put(apEnum.getId(),apEnum.getValue());
        }
    }
    public static String get(int id) {
        return controlMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
