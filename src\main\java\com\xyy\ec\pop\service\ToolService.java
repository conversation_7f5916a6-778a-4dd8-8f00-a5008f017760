package com.xyy.ec.pop.service;

import com.xyy.ec.pop.dto.PopCorporationDto;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.vo.DeductFundToolVo;
import com.xyy.ec.pop.vo.OfflinePayWhitelistVo;

import java.util.List;
import java.util.Map;

public interface ToolService {
    boolean deleteErpSkusByOrgId(String orgId,Integer deleteType);

    String listPushOrderByOrgId(String orgId);

    Map<String,Object> getProductByStandardId(String standardId) throws ServiceException;

    boolean deleteLogicByBarcodes(List<String> barcodes, String user);

    void moveDeletedSku();

    boolean updateOrderSynStatus(List<String> orderNos);

    boolean updateCustomerSynStatus(String orgId, List<String> customerNames);

    void setControlUserType(List<String> orgs);

    boolean delErpClientInfoByOrgId(String orgId);

    void moveDeletedSkuOtherInfo(String orgId);

    boolean autoApplyInvoice(String autoApplyInvoiceDate);

    void downNoStockSku(String orgId) throws InterruptedException;

    String startRepairPopOrderStatus(String orderNo);

    boolean updateCorporationInfo(PopCorporationDto popCorporationDto);

    Boolean updateOfflinePayData(OfflinePayWhitelistVo offlinePayWhitelistVo);
    void deductFund(DeductFundToolVo deductFundToolVo);

}
