package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.seller.api.PopCommissionInvoiceApi;
import com.xyy.me.product.common.beans.ResponseInfo;
import com.xyy.me.product.general.api.dto.pictrue.PictrueCondition;
import com.xyy.me.product.general.api.dto.pictrue.PictureProResult;
import com.xyy.me.product.general.api.dto.product.GeneralProductDto;
import com.xyy.me.product.general.api.facade.picture.ProductPictureFacade;
import com.xyy.me.product.general.api.facade.product.sku.SkuGenReadApi;
import com.xyy.me.product.general.api.vo.product.GeneralProductVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/09/22/17:07
 * @Description:
 */
@Component
@Slf4j
public class ToolRemoteAdapter {
    @Reference(registry = "me")
    private SkuGenReadApi skuGenReadApi;
    @Reference(registry = "me")
    private ProductPictureFacade productPictureFacade;
    @Reference
    private PopCommissionInvoiceApi popCommissionInvoiceApi;

    public GeneralProductDto getGeneralProduct(String standardId) throws ServiceException {
        try {
            log.info("ToolRemoteAdapter.getGeneralProduct param standardId:{}",standardId);
            GeneralProductVo vo = new GeneralProductVo();
            vo.setProductId(standardId);
            ResponseInfo<GeneralProductDto> generalProduct = skuGenReadApi.getGeneralProduct(vo);
            log.info("ToolRemoteAdapter.getGeneralProduct response result:{}", JSON.toJSONString(generalProduct));
            if (generalProduct == null || generalProduct.isFailure()){
                throw new ServiceException("调用查询中台商品API失败");
            }
            return generalProduct.getData();
        }catch (ServiceException s){
            log.error("ToolRemoteAdapter.getGeneralProduct error standardId:{}",standardId,s);
            throw s;
        }catch (Exception e){
            log.error("ToolRemoteAdapter.getGeneralProduct error standardId:{}",standardId,e);
            throw new ServiceException("查询中台商品失败");
        }
    }

    public List<PictureProResult> getProductImg(String standardId) throws ServiceException {
        try {
            log.info("ToolRemoteAdapter.getProductImg param standardId:{}",standardId);
            PictrueCondition pictrueCondition = new PictrueCondition();
            pictrueCondition.setProductIds(Collections.singletonList(standardId));
            com.xyy.me.product.general.api.common.ResponseInfo<List<PictureProResult>> proPicture = productPictureFacade.findProPicture(pictrueCondition);
            log.info("ToolRemoteAdapter.getProductImg response result:{}", JSON.toJSONString(proPicture));
            if (proPicture == null || proPicture.isFailure()){
                throw new ServiceException("调用查询中台商品图片API失败");
            }
            return proPicture.getData();
        }catch (ServiceException s){
            log.error("ToolRemoteAdapter.getProductImg error standardId:{}",standardId,s);
            throw s;
        }catch (Exception e){
            log.error("ToolRemoteAdapter.getProductImg error standardId:{}",standardId,e);
            throw new ServiceException("查询中台商品图片失败");
        }
    }

    public boolean autoApplyInvoice(String autoApplyInvoiceDate) {
        log.info("ToolRemoteAdapter.autoApplyInvoice#autoApplyInvoiceDate:{}", autoApplyInvoiceDate);
        ApiRPCResult apiRPCResult = popCommissionInvoiceApi.autoApplyInvoice(autoApplyInvoiceDate);
        log.info("ToolRemoteAdapter.autoApplyInvoice#autoApplyInvoiceDate:{},result:{}", autoApplyInvoiceDate, JSON.toJSONString(apiRPCResult));
        if (apiRPCResult.isFail()) {
            throw new PopAdminException(apiRPCResult.getErrMsg());
        }
        return true;
    }
}
