package com.xyy.ec.pop.dto.mop;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.xyy.scm.constant.annotation.Explain;
import lombok.Data;

import java.io.Serializable;


@Data
// 头背景设置
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL, fillForegroundColor = 10)
// 头字体设置成20
@HeadFontStyle(fontHeightInPoints = 12)
public class AccountImportDTO implements Serializable {
    @Explain("工号")
    @ExcelProperty(value = "工号",index = 0)
    private String accountId;
    @Explain("名称")
    @ExcelProperty(value = "名称",index = 1)
    private String accountName;

    @Explain("岗位ID")
    @ExcelProperty(value = "岗位ID",index = 2)
    private Long positionId;

    @Explain("部门名称")
    @ExcelProperty(value = "部门名称",index = 3)
    private String departmentName;

    /**
     * 非必填。如果有全国权限，该列填写全国；如果有几个省份的全国城市的权限，则在该列填写对应的省份名称，不同省份之间用英文逗号隔开。
     */
    @Explain("区域权限")
    @ExcelProperty(value = "区域权限",index = 4)
    private String regionPermission;
}
