package com.xyy.ec.pop.marketing.helpers;

import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataStatusCountInfoVO;
import com.xyy.ms.promotion.business.result.MarketingActivitySaleDataStatusCountInfoDTO;

import java.util.Objects;

public class MarketingActivitySaleDataStatusCountInfoDTOHelper {

    public static MarketingActivitySaleDataStatusCountInfoVO create(MarketingActivitySaleDataStatusCountInfoDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        return MarketingActivitySaleDataStatusCountInfoVO.builder()
                .unpaidCount(dto.getUnpaidCount())
                .paidCount(dto.getPaidCount())
                .build();
    }

}
