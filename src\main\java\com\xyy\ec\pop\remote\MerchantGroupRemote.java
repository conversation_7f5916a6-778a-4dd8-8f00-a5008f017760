package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.product.back.end.ecp.pop.api.PopMerchantGroupApi;
import com.xyy.ec.product.back.end.ecp.pop.dto.*;
import com.xyy.ec.product.back.end.ecp.stock.dto.BPageDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @Description 用户组
 */
@Service
@Slf4j
public class MerchantGroupRemote {
    @Reference
    private PopMerchantGroupApi popMerchantGroupApi;

    public BPageDto<MerchantGroupRelationDTO> selectMerchantGroupRelationPage(MerchantGroupRelationDTO param) throws ServiceException {
        try {
            log.info("MerchantGroupRemote.selectMerchantGroupRelationPage#param:{}", JSON.toJSONString(param));
            ApiRPCResult<BPageDto<MerchantGroupRelationDTO>> result = popMerchantGroupApi.selectMerchantGroupRelationPage(param);
            log.info("MerchantGroupRemote.selectMerchantGroupRelationPage#param:{} return {}", JSON.toJSONString(param), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("MerchantGroupRemote.selectMerchantGroupRelationPage#param:{} 异常", JSON.toJSONString(param), e);
        }
        throw new ServiceException("查询控销店铺id失败");
    }

}
