package com.xyy.ec.pop.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.product.dto.*;

import java.util.List;

public interface PopFirstSaleQualificationService {
     PageInfo<PopFirstSaleQualificationResDto> query(PopFirstSaleQualificationDto dto);
     String downZipUrl(String orgId, String barCode,String sealStatus) throws Exception;
     ApiRPCResult<PageInfo<PopFirstSaleQualificationResDto>> queryFormEs(PopFirstSaleQualificationDto dto);
}
