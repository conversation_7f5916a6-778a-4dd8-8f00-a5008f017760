package com.xyy.ec.pop.vo.fdd;

import lombok.Data;

import java.io.Serializable;

@Data
public class FddEnterpriseEmpowerVo implements Serializable {
    /**
     * 商家ID
     */
    private String orgId;

    /**
     * 商家名称
     */
    private String orgName;

    /**
     * 法大大的经办人帐号建立免登关系的clientUserId
     */
    private String clientUserId;

    /**
     * 企业在法大大应用中的唯一标识
     */
    private String clientCorpId;

    /**
     * 法大大平台为该企业在该应用appId范围内分配的唯一标识
     */
    private String openCorpId;
    /**
     * 法大大开通状态(1:已开通，0：未开通)
     */
    private Integer fddStatus;

    /**
     * 管理员状态：1管理员,0非管理员
     */
    private Integer adminStatus;
    /**
     * 法大大对应企业管理员的手机号
     */
    private String account;

    private static final long serialVersionUID = 1L;
}
