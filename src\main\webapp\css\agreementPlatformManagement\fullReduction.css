input{
  background: -webkit-gradient(linear,0 0,0 100%,from(#fff),to(#fff));
}
input:focus {
    outline:none;
}
.hd-tips{
    padding: 10px 0;
}
.hd-setp{
    height: 55px;
    line-height: 55px;
    background: rgba(242, 242, 242, 1);
}
.hd-setp-item {
    display: inline-block;
    width: 30%;
    height: 55px;
    float: left;
    text-align: center;
    font-size: 18px;
    cursor: pointer;
}
.hd-setp-item-cur{
    color: #169BD5;
}
.hd-setp-cont{
    padding: 40px 0;
}
.hd-setp-cont .form-control-feedback{
    font-size: 22px;
    right: 12px;
}
.table-list thead{
    background: rgba(242, 242, 242, 1);
}
.hds-tab{
    height: 30px;
}
.hds-tab-cont{
    border:1px solid rgba(0, 153, 255, 1);
    padding: 20px 0;
}
.hds-tab-item{
    display: inline-block;
    width: 100px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    float: left;
    margin-right: 10px;
    background: #ccc;
    position: relative;
    cursor: pointer;
}
.hds-tab-item-inp{
    display: inline-block;
    width: 98px;
    height: 30px;
    border: none;
    text-align: center;
    background: #ccc;
    color: #fff;
    cursor: pointer;
}
.hds-tab-item-cur{
    height: 31px;
    background: #fff;
    margin-bottom: -1px;
    border-left: 1px solid rgba(0, 153, 255, 1);
    border-right: 1px solid rgba(0, 153, 255, 1);
    border-top: 1px solid rgba(0, 153, 255, 1);
    cursor: pointer;
}
.hds-tab-item-cur .hds-tab-item-inp{
    background: #fff;
    color: #333;
    cursor: pointer;
}
.del-tab-btn{
    display: inline-block;
    width: 17px;
    height: 17px;
    background: #999;
    color: #fff;
    border-radius: 50%;
    text-align: center;
    line-height: 16px;
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 16px;
    z-index: 99;
}
.add-tab-btn{
    display: inline-block;
    width: 100px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: rgba(0, 153, 255, 1);
    text-decoration: none;
    color: #fff;
}
.add-tab-btn:hover,.add-tab-btn:visited,.add-tab-btn:link{
    color: #fff;
    text-decoration: none; 
}
.set-area-btn:hover,.set-area-btn:visited,.set-area-btn:link{
    
    text-decoration: none; 
}
.set-area-btn{
    display: none;
    width: 100px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    border: 1px solid rgba(0, 153, 255, 1);
    color: rgba(0, 153, 255, 1);
    position: relative;
    top: 5px;
}
.radio-block{
    margin-bottom: 10px;
}
.alert-tips{
    color: #f00;
    padding: 5px 0;
}
.remarkeimg{
    margin-top: 8px;
}
.hd-setp-box {
    display: none;
}

.hd-setp-box .button-box{
     height: 54px;
     padding: 10px ;
     background: rgba(242, 242, 242, 1);
 }
 .hd-setp-box .button-box-tit{
     font-size: 20px;
     color: #000; 
     padding-right: 30px;
 }
.wancheng-box{
    padding: 30px 0;
    text-align: center;
    font-size: 20px;
}
.next-btn4{
    margin: 20px auto;
    width: 20%;
    display:block;
}
.form-horizontal-box{
    display: none;
}
.image_input{
    opacity:0;
    filter:alpha(opacity=0);
    height: 95px;
    width: 100px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
}
.image_div{
    position: relative;
    min-height: 40px;
}