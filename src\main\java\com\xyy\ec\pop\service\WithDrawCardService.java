package com.xyy.ec.pop.service;

import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.vo.WithDrawCardVo;

import java.util.List;

/**
 * @ClassName CashAccountService
 * <AUTHOR>
 * @Date 2024/5/13 18:35
 * @Version 1.0
 */
public interface WithDrawCardService {

  void addOrUpdateWithDrawCard(WithDrawCardVo withDrawCardVo, Long cid, SysUser user);

  List<WithDrawCardVo> selectWithDrawCardByOrgId(String orgId);
}