package com.xyy.ec.pop.marketing.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.elephant.api.ElephantMarketingActivitySaleDataAdminApi;
import com.xyy.ec.marketing.elephant.params.MarketingActivitySaleDataExportPagingQueryParam;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.search.api.remote.enums.PopOrderStatusEnum;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.helper.*;
import com.xyy.ec.pop.marketing.helpers.MarketingActivitySaleDataStatisticsInfoVOHelper;
import com.xyy.ec.pop.marketing.helpers.MarketingActivitySaleDataStatusCountInfoDTOHelper;
import com.xyy.ec.pop.marketing.helpers.MarketingActivitySaleDataVOHelper;
import com.xyy.ec.pop.marketing.service.MarketingActivitySaleDataService;
import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataStatisticsInfoVO;
import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataStatusCountInfoVO;
import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataVO;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.redis.impl.RedisService;
import com.xyy.ec.pop.remote.BaseRegionBusinessRPCService;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.remote.MerchantRpcService;
import com.xyy.ec.pop.remote.ProductCsuForOtherSystemRemoteService;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuDTO;
import com.xyy.ms.promotion.business.api.admin.MarketingActivitySaleDataAdminApi;
import com.xyy.ms.promotion.business.params.*;
import com.xyy.ms.promotion.business.result.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.keyvalue.DefaultKeyValue;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MarketingActivitySaleDataServiceImpl implements MarketingActivitySaleDataService {

    @Reference(version = "1.0.0")
    private MarketingActivitySaleDataAdminApi marketingActivitySaleDataAdminApi;

    @Autowired
    private BaseRegionBusinessRPCService baseRegionBusinessRPCService;

    @Reference(version = "1.0.0")
    private ElephantMarketingActivitySaleDataAdminApi elephantMarketingActivitySaleDataAdminApi;

    private static final Integer ALL_PROVINCE_CODE = -1;

    private static final Integer ALL_ORDER_STATUS = -1;

    @Resource
    private RedisService redisService;

    @Autowired
    private DownloadRemote downloadRemote;

    @Autowired
    private ProductCsuForOtherSystemRemoteService productCsuForOtherSystemRemoteService;

    @Autowired
    private MerchantRpcService merchantRpcService;

    @Override
    public List<DefaultKeyValue<Integer, String>> listProvinces() {
        List<XyyRegionBusinessDto> xyyRegionBusinessDtos = baseRegionBusinessRPCService.listProvinceRegions();
        List<DefaultKeyValue<Integer, String>> result = Lists.newArrayListWithExpectedSize(16);
        result.add(new DefaultKeyValue<>(ALL_PROVINCE_CODE, "全部"));
        if (CollectionUtils.isEmpty(xyyRegionBusinessDtos)) {
            return result;
        }
        List<DefaultKeyValue<Integer, String>> provinces = xyyRegionBusinessDtos.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getAreaCode()) && Objects.nonNull(item.getAreaName()))
                .map(item -> new DefaultKeyValue<>(item.getAreaCode(), item.getAreaName()))
                .collect(Collectors.toList());
        result.addAll(provinces);
        return result;
    }

    @Override
    public List<DefaultKeyValue<Integer, String>> listOrderStatus() {
        List<DefaultKeyValue<Integer, String>> result = Lists.newArrayListWithExpectedSize(16);
        // 全部、待付款、待审核、正在开单、分拣中、待配送、配送中、已完成、已取消、已退款
        result.add(new DefaultKeyValue<>(ALL_ORDER_STATUS, "全部"));
        result.add(new DefaultKeyValue<>(PopOrderStatusEnum.NOT_PAYMENT.getCode(), PopOrderStatusEnum.NOT_PAYMENT.getAlias()));
        result.add(new DefaultKeyValue<>(PopOrderStatusEnum.WAIT_SHIP.getCode(), PopOrderStatusEnum.WAIT_SHIP.getAlias()));
        result.add(new DefaultKeyValue<>(PopOrderStatusEnum.STORAGE.getCode(), PopOrderStatusEnum.STORAGE.getAlias()));
        result.add(new DefaultKeyValue<>(PopOrderStatusEnum.SORTING.getCode(), PopOrderStatusEnum.SORTING.getAlias()));
        result.add(new DefaultKeyValue<>(PopOrderStatusEnum.WAIT_DELIVERY.getCode(), PopOrderStatusEnum.WAIT_DELIVERY.getAlias()));
        result.add(new DefaultKeyValue<>(PopOrderStatusEnum.DELIVERYING.getCode(), PopOrderStatusEnum.DELIVERYING.getAlias()));
        result.add(new DefaultKeyValue<>(PopOrderStatusEnum.SHIPPED.getCode(), PopOrderStatusEnum.SHIPPED.getAlias()));
        result.add(new DefaultKeyValue<>(PopOrderStatusEnum.CANCEL.getCode(), PopOrderStatusEnum.CANCEL.getAlias()));
        result.add(new DefaultKeyValue<>(PopOrderStatusEnum.REFUNDED.getCode(), PopOrderStatusEnum.REFUNDED.getAlias()));
        return result;
    }

    @Override
    public MarketingActivitySaleDataSummaryInfoDTO getSummaryInfo(MarketingActivitySaleDataSummaryInfoQueryParam queryParam) {
        // 参数校验
        Boolean isSuccess = MarketingActivitySaleDataSummaryInfoQueryParamHelper.validate(queryParam);
        if (BooleanUtils.isNotTrue(isSuccess)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }

        // 查询
        ApiRPCResult<MarketingActivitySaleDataSummaryInfoDTO> apiRPCResult = marketingActivitySaleDataAdminApi.getSummaryInfo(queryParam);
        if (log.isDebugEnabled()) {
            log.debug("调用 MarketingActivitySaleDataAdminApi.getSummaryInfo，入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
        }
        if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail()) {
            log.error("调用 MarketingActivitySaleDataAdminApi.getSummaryInfo 失败，入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
            String msg = Objects.isNull(apiRPCResult) ? "网络错误，请稍后重试" : apiRPCResult.getMsg();
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, msg);
        }

        // 响应
        return apiRPCResult.getData();
    }

    @Override
    public MarketingActivitySaleDataSearchInfoDTO getSearchInfo(MarketingActivitySaleDataSearchInfoQueryParam queryParam) {
        // 参数校验
        Boolean isSuccess = MarketingActivitySaleDataSearchInfoQueryParamHelper.validate(queryParam);
        if (BooleanUtils.isNotTrue(isSuccess)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }

        // 查询
        ApiRPCResult<MarketingActivitySaleDataSearchInfoDTO> apiRPCResult = marketingActivitySaleDataAdminApi.getSearchInfo(queryParam);
        if (log.isDebugEnabled()) {
            log.debug("调用 MarketingActivitySaleDataAdminApi.getSearchInfo，入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
        }
        if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail()) {
            log.error("调用 MarketingActivitySaleDataAdminApi.getSearchInfo 失败，入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
            String msg = Objects.isNull(apiRPCResult) ? "网络错误，请稍后重试" : apiRPCResult.getMsg();
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, msg);
        }

        // 响应
        return apiRPCResult.getData();
    }

    @Override
    public PageInfo<MarketingActivitySaleDataVO> paging(MarketingActivitySaleDataListQueryParam queryParam) {
        // 参数校验
        Boolean isSuccess = MarketingActivitySaleDataListQueryParamHelper.validate(queryParam);
        if (BooleanUtils.isNotTrue(isSuccess)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }

        // 处理参数
        this.dealMarketingActivitySaleDataListQueryParam(queryParam);

        // 查询
        ApiRPCResult<PageInfo<MarketingActivitySaleDataDTO>> apiRPCResult = marketingActivitySaleDataAdminApi.paging(queryParam);
        if (log.isDebugEnabled()) {
            log.debug("调用 MarketingActivitySaleDataAdminApi.paging，入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
        }
        if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail()) {
            log.error("调用 MarketingActivitySaleDataAdminApi.paging 失败，入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
            String msg = Objects.isNull(apiRPCResult) ? "网络错误，请稍后重试" : apiRPCResult.getMsg();
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, msg);
        }
        PageInfo<MarketingActivitySaleDataDTO> apiPageInfo = apiRPCResult.getData();
        List<MarketingActivitySaleDataDTO> apiList = apiPageInfo.getList();

        // 转换数据结构
        List<MarketingActivitySaleDataVO> marketingActivitySaleDataVOS = MarketingActivitySaleDataVOHelper.creates(apiList);

        // 填充VO
        // 商品信息
        this.fillCsuInfo(marketingActivitySaleDataVOS);
        // 药店信息
        this.fillMerchantName(marketingActivitySaleDataVOS);
        // 省份信息
        this.fillProvinceName(marketingActivitySaleDataVOS);

        // 响应
        PageInfo<MarketingActivitySaleDataVO> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(apiPageInfo.getPageNum());
        pageInfo.setPageSize(apiPageInfo.getPageSize());
        pageInfo.setPages(apiPageInfo.getPages());
        pageInfo.setTotal(apiPageInfo.getTotal());
        pageInfo.setList(marketingActivitySaleDataVOS);
        return pageInfo;
    }

    private void fillCsuInfo(List<MarketingActivitySaleDataVO> marketingActivitySaleDataVOS) {
        if (CollectionUtils.isEmpty(marketingActivitySaleDataVOS)) {
            return;
        }
        Set<Long> csuIds = marketingActivitySaleDataVOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCsuId()))
                .map(MarketingActivitySaleDataVO::getCsuId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(csuIds)) {
            return;
        }
        List<CsuDTO> csuDTOS = productCsuForOtherSystemRemoteService.listCsuInfosByCsuIds(Lists.newArrayList(csuIds));
        if (CollectionUtils.isEmpty(csuIds)) {
            return;
        }
        Map<Long, CsuDTO> csuIdToInfoMap = csuDTOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getId()))
                .collect(Collectors.toMap(CsuDTO::getId, Function.identity(), (s, f) -> s));
        if (MapUtils.isEmpty(csuIdToInfoMap)) {
            return;
        }
        marketingActivitySaleDataVOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCsuId()))
                .forEach(item -> {
                    CsuDTO csuDTO = csuIdToInfoMap.get(item.getCsuId());
                    if (Objects.nonNull(csuDTO)) {
                        item.setCsuBarcode(csuDTO.getBarcode());
                        // 商品ERP编码：POP取ERP编码，自营取神农编码；
                        item.setCsuErpCode(Objects.equals(csuDTO.getIsThirdCompany(), 1) ? csuDTO.getErpCode() : csuDTO.getProductCode());
                        item.setCsuProductName(csuDTO.getShowName());
                        item.setCsuSpec(csuDTO.getSpec());
                        item.setCsuManufacturer(csuDTO.getManufacturer());
                    }
                });
    }

    private void fillMerchantName(List<MarketingActivitySaleDataVO> marketingActivitySaleDataVOS) {
        if (CollectionUtils.isEmpty(marketingActivitySaleDataVOS)) {
            return;
        }
        Set<Long> merchantIds = marketingActivitySaleDataVOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getMerchantId()))
                .map(MarketingActivitySaleDataVO::getMerchantId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(merchantIds)) {
            return;
        }
        List<MerchantBussinessDto> merchantBussinessDtos = merchantRpcService.selectMerchantByIdList(Lists.newArrayList(merchantIds));
        if (CollectionUtils.isEmpty(merchantBussinessDtos)) {
            return;
        }
        Map<Long, String> merchantIdToRealNameMap = merchantBussinessDtos
                .stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getId()) && StringUtils.isNotEmpty(item.getRealName()))
                .collect(Collectors.toMap(MerchantBussinessDto::getId, MerchantBussinessDto::getRealName, (s, f) -> s));
        if (MapUtils.isEmpty(merchantIdToRealNameMap)) {
            return;
        }
        marketingActivitySaleDataVOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getMerchantId()))
                .forEach(item -> {
                    String merchantRealName = merchantIdToRealNameMap.get(item.getMerchantId());
                    item.setMerchantName(merchantRealName);
                });
    }

    private void fillProvinceName(List<MarketingActivitySaleDataVO> marketingActivitySaleDataVOS) {
        if (CollectionUtils.isEmpty(marketingActivitySaleDataVOS)) {
            return;
        }
        Set<Integer> provinceCodes = marketingActivitySaleDataVOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getProvinceCode()))
                .map(MarketingActivitySaleDataVO::getProvinceCode)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(provinceCodes)) {
            return;
        }
        List<XyyRegionBusinessDto> xyyRegionBusinessDtos = baseRegionBusinessRPCService.listRegionsByCodes(Lists.newArrayList(provinceCodes));
        if (CollectionUtils.isEmpty(xyyRegionBusinessDtos)) {
            return;
        }
        Map<Integer, String> areaCodeToNameMap = xyyRegionBusinessDtos
                .stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getAreaCode()) && StringUtils.isNotEmpty(item.getAreaName()))
                .collect(Collectors.toMap(XyyRegionBusinessDto::getAreaCode, XyyRegionBusinessDto::getAreaName, (s, f) -> s));
        if (MapUtils.isEmpty(areaCodeToNameMap)) {
            return;
        }
        marketingActivitySaleDataVOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getProvinceCode()))
                .forEach(item -> {
                    String provinceName = areaCodeToNameMap.get(item.getProvinceCode());
                    item.setProvinceName(provinceName);
                });
    }

    @Override
    public MarketingActivitySaleDataStatisticsInfoVO getStatisticsInfo(MarketingActivitySaleDataStatisticsQueryParam queryParam) {
        // 参数校验
        Boolean isSuccess = MarketingActivitySaleDataStatisticsQueryParamHelper.validate(queryParam);
        if (BooleanUtils.isNotTrue(isSuccess)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }

        // 处理参数
        this.dealMarketingActivitySaleDataStatisticsQueryParam(queryParam);

        // 查询
        ApiRPCResult<MarketingActivitySaleDataStatisticsInfoDTO> apiRPCResult = marketingActivitySaleDataAdminApi.getStatisticsInfo(queryParam);
        if (log.isDebugEnabled()) {
            log.debug("调用 MarketingActivitySaleDataAdminApi.getStatisticsInfo，入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
        }
        if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail()) {
            log.error("调用 MarketingActivitySaleDataAdminApi.getStatisticsInfo 失败，入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
            String msg = Objects.isNull(apiRPCResult) ? "网络错误，请稍后重试" : apiRPCResult.getMsg();
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, msg);
        }
        MarketingActivitySaleDataStatisticsInfoDTO saleDataStatisticsInfoDTO = apiRPCResult.getData();

        // 转换数据结构
        MarketingActivitySaleDataStatisticsInfoVO saleDataStatisticsInfoVO = MarketingActivitySaleDataStatisticsInfoVOHelper.create(saleDataStatisticsInfoDTO);

        // 响应
        return saleDataStatisticsInfoVO;
    }

    @Override
    public MarketingActivitySaleDataStatusCountInfoVO getStatusCountInfo(MarketingActivitySaleDataOrderStatusCountQueryParam queryParam) {
        // 参数校验
        Boolean isSuccess = MarketingActivitySaleDataOrderStatusCountQueryParamHelper.validate(queryParam);
        if (BooleanUtils.isNotTrue(isSuccess)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }

        // 处理参数
        this.dealMarketingActivitySaleDataOrderStatusCountQueryParam(queryParam);

        // 查询
        ApiRPCResult<MarketingActivitySaleDataStatusCountInfoDTO> apiRPCResult = marketingActivitySaleDataAdminApi.getStatusCountInfo(queryParam);
        if (log.isDebugEnabled()) {
            log.debug("调用 MarketingActivitySaleDataAdminApi.getStatisticsInfo，入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
        }
        if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail()) {
            log.error("调用 MarketingActivitySaleDataAdminApi.getStatisticsInfo 失败，入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
            String msg = Objects.isNull(apiRPCResult) ? "网络错误，请稍后重试" : apiRPCResult.getMsg();
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, msg);
        }
        MarketingActivitySaleDataStatusCountInfoDTO saleDataStatusCountInfoDTO = apiRPCResult.getData();

        // 转换数据结构
        MarketingActivitySaleDataStatusCountInfoVO saleDataStatusCountInfoVO = MarketingActivitySaleDataStatusCountInfoDTOHelper.create(saleDataStatusCountInfoDTO);

        // 响应
        return saleDataStatusCountInfoVO;
    }

    @Override
    public void asyncExport(SysUser user, MarketingActivitySaleDataExportPagingQueryParam queryParam) {
        String redisCacheKey = null;
        Boolean isHaveLock = false;
        try {
            // 导出频次限制
            redisCacheKey = "marketingSaleData:popAdmin:export_" + user.getId();
            // 最多锁60秒
            isHaveLock = redisService.setNx(redisCacheKey, StringUtils.EMPTY, 60);
            if (BooleanUtils.isNotTrue(isHaveLock)) {
                throw new PopAdminException(XyyJsonResultCodeEnum.EXPORT_FREQUENCY_LIMIT, "您目前有正在导出的任务，请等待其完成后再进行导出！");
            }
            log.info("活动销售数据导出频次，标记：{}", redisCacheKey);
            // 导出
            // 参数校验
            Boolean isSuccess = MarketingActivitySaleDataExportPagingQueryParamHelper.validate(queryParam);
            if (BooleanUtils.isNotTrue(isSuccess)) {
                String msg = "参数非法";
                throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }

            // 处理参数
            this.dealMarketingActivitySaleDataExportPagingQueryParam(queryParam);

            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryParam)
                    .operator(user.getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.MARKETING_SALE_DATA_LIST)
                    .build();
            try {
                Boolean asyncResult = downloadRemote.saveTask(content);
                if (BooleanUtils.isNotTrue(asyncResult)) {
                    log.error("活动销售数据导出失败，DownloadRemote.saveTask，content：{}", JSONObject.toJSONString(content));
                    throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "导出失败，请稍后重试！");
                }
            } catch (ServiceRuntimeException e) {
                throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, e.getMessage());
            }
        } finally {
            if (StringUtils.isNotEmpty(redisCacheKey) && BooleanUtils.isTrue(isHaveLock)) {
                // 释放锁
                try {
                    redisService.deleteKey(redisCacheKey);
                } catch (Exception e) {
                    log.error("释放活动销售数据导出频次锁失败，redisCacheKey：{}", redisCacheKey, e);
                }
            }
        }
    }

    private void dealMarketingActivitySaleDataListQueryParam(MarketingActivitySaleDataListQueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            return;
        }
        // 处理全部
        if (Objects.equals(queryParam.getProvinceCode(), ALL_PROVINCE_CODE)) {
            queryParam.setProvinceCode(null);
        }
        if (Objects.equals(queryParam.getOrderStatus(), ALL_ORDER_STATUS)) {
            queryParam.setOrderStatus(null);
        }
    }

    private void dealMarketingActivitySaleDataStatisticsQueryParam(MarketingActivitySaleDataStatisticsQueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            return;
        }
        // 处理全部
        if (Objects.equals(queryParam.getProvinceCode(), ALL_PROVINCE_CODE)) {
            queryParam.setProvinceCode(null);
        }
        if (Objects.equals(queryParam.getOrderStatus(), ALL_ORDER_STATUS)) {
            queryParam.setOrderStatus(null);
        }
    }

    private void dealMarketingActivitySaleDataOrderStatusCountQueryParam(MarketingActivitySaleDataOrderStatusCountQueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            return;
        }
        // 处理全部
        if (Objects.equals(queryParam.getProvinceCode(), ALL_PROVINCE_CODE)) {
            queryParam.setProvinceCode(null);
        }
        if (Objects.equals(queryParam.getOrderStatus(), ALL_ORDER_STATUS)) {
            queryParam.setOrderStatus(null);
        }
    }

    private void dealMarketingActivitySaleDataExportPagingQueryParam(MarketingActivitySaleDataExportPagingQueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            return;
        }
        // 处理全部
        if (Objects.equals(queryParam.getProvinceCode(), ALL_PROVINCE_CODE)) {
            queryParam.setProvinceCode(null);
        }
        if (Objects.equals(queryParam.getOrderStatus(), ALL_ORDER_STATUS)) {
            queryParam.setOrderStatus(null);
        }
    }

}
