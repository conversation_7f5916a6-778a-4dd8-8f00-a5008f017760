package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

public enum BusinessTypeEnum {
	SELF(1,"个体药店"),
	CHAIN_JOIN(2,"连锁药店（加盟）"),
	CHAIN_DIRECTLY(3,"连锁药店（直营）"),
	CLINIC(4,"诊所"),
	DRUG_WHOLESALE(5,"药品批发"),
	PRIVATE_HOSPITAL(6,"民营医院"),
	PUBLIC_HOSPITAL(7,"公立医院"),
	TOWNSHIP_HOSPITAL(8,"乡镇卫生院"),
	COMMUNITY_HEALTH_STATION(9,"社区卫生服务站"),
	RURAL_HEALTH_ROOM(10,"乡村卫生室"),
	MEDICAL_INSTITUTION(11,"医疗机构"),
	TERMINAL(12,"终端");
	
    private int id;
    private  String value;

    BusinessTypeEnum(int id, String value){
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, BusinessTypeEnum> enumMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(BusinessTypeEnum e : BusinessTypeEnum.values()) {
        	enumMaps.put(e.getId(), e);
            maps.put(e.getId(),e.getValue());
        }
    }

    public static String get(int id) {
        BusinessTypeEnum businessTypeEnum = enumMaps.get(id);
        if (businessTypeEnum != null) {
            return businessTypeEnum.getValue();
        }
        return "";
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
