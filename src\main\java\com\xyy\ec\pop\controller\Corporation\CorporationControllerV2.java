package com.xyy.ec.pop.controller.Corporation;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantCustomerTypeBusinessDto;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.ResultVO;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.dto.ShopNextdayConfDTO;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.remote.ShopAdminRemoteService;
import com.xyy.ec.pop.server.api.Enum.WithDrawCardEnum;
import com.xyy.ec.pop.server.api.merchant.api.enums.CheckCorporationStateEnum;
import com.xyy.ec.pop.constants.ZTreeByCategoryRelation;
import com.xyy.ec.pop.enums.ResultCodeEnum;
import com.xyy.ec.pop.helper.CorporationHelper;
import com.xyy.ec.pop.model.*;
import com.xyy.ec.pop.remote.MerchantCustomerTypeRemote;
import com.xyy.ec.pop.remote.ShopUserTypeRemote;
import com.xyy.ec.pop.server.api.Enum.PaymentChannelEnum;
import com.xyy.ec.pop.server.api.Enum.PopDeliveryBusinessTypeEnum;
import com.xyy.ec.pop.server.api.Enum.PopDeliveryEnum;
import com.xyy.ec.pop.server.api.bank.api.PopBankApi;
import com.xyy.ec.pop.server.api.bank.dto.BankAccountDto;
import com.xyy.ec.pop.server.api.merchant.api.admin.BusinessCategoryAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationBusinessAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationStateEnum;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationTypeEnum;
import com.xyy.ec.pop.server.api.merchant.api.seller.AddressAreaApi;
import com.xyy.ec.pop.server.api.merchant.dto.*;
import com.xyy.ec.pop.server.api.seller.api.PopCashAdvanceApi;
import com.xyy.ec.pop.server.api.seller.api.PopDeliveryApi;
import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceDto;
import com.xyy.ec.pop.server.api.seller.dto.PopDeliveryBusinessTypeDto;
import com.xyy.ec.pop.server.api.seller.dto.PopDeliveryDto;
import com.xyy.ec.pop.server.api.shop.dto.PopCheckLogDto;
import com.xyy.ec.pop.service.PopCorporationService;
import com.xyy.ec.pop.service.WithDrawCardService;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.WithDrawCardVo;
import com.xyy.ec.pop.vo.ZTree;
import com.xyy.ec.pop.vo.corporation.CorporationVo;
import com.xyy.ec.shop.server.business.api.ShopAdminApi;
import com.xyy.ec.shop.server.business.api.ShopNextdayConfApi;
import com.xyy.ec.shop.server.business.params.ShopNextdayConfQueryParam;
import com.xyy.ec.shop.server.business.results.ShopNextdayConfInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * POP提现个人卡
 * <AUTHOR>
 * @date 2020/12/07
 */
@RequestMapping(value = "/corporation/v2")
@Slf4j
@RestController
public class CorporationControllerV2 extends BaseController {
    @Reference
    private CorporationAdminApi corporationAdminApi;
    @Reference
    private CorporationBusinessAdminApi corporationBusinessAdminApi;
    @Reference
    private BusinessCategoryAdminApi businessCategoryAdminApi;
    @Reference
    private AddressAreaApi addressAreaApi;

    @Autowired
    private PopCorporationService popCorporationService;
    @Autowired
    private MerchantCustomerTypeRemote merchantCustomerTypeRemote;
    @Autowired
    private ShopUserTypeRemote shopUserTypeRemote;
    @Value("#{'${shop.status.operation.users:}'.split(',')}")
    private List<String> operationUsers;
    @Reference(version = "1.0.0")
    private PopDeliveryApi popDeliveryApi;

    @Reference(version = "1.0.0")
    private PopBankApi popBankApi;
    @Reference(version = "1.0.0")
    private PopCashAdvanceApi popCashAdvanceApi;
    @Value("${zhiguan.users}")
    private String zhiguanUser;
    @Value("${select.items.flag:false}")
    private Boolean selectItemsFlag;
    @Value("${instrument.categoryIds:21443,21444,37659,37867}")
    private String instrumentCategoryIds;
    @Value("${corp.drug.production.license.regular}")
    private String drugProductionLicenseRegular;
    @Value("${corp.drug.business.license.regular}")
    private String drugBusinessLicenseRegular;

    @Autowired
    private WithDrawCardService withDrawCardService;

    @Reference(version = "1.0.0")
    private ShopAdminApi shopAdminApi;

    @Reference
    private ShopNextdayConfApi shopNextdayConfApi;

    @PostMapping("authCorporation")
    @ResponseBody
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo authCorporation(@RequestBody CorporationDto bean) {
        log.info("CorporationControllerV2.authCorporation#bean:{}", JSON.toJSONString(bean));
        //查询企业信息
        ApiRPCResult<CorporationDto> apiRPCResult = corporationAdminApi.queryCorporation(bean.getId());
        if (apiRPCResult.isFail()) {
            return ResponseVo.errRest("未找到Id对应信息");
        }
        if (apiRPCResult.isSuccess() && apiRPCResult.getData().getState() != CorporationStateEnum.NOT_CERTIFIED.getCode()) {
            return ResponseVo.errRest("非待审核状态");
        }

        String realName = this.getUser().getRealName();
        if (!zhiguanUser.contains(realName)){
            return ResponseVo.errRest("审核失败，暂无审核权限！");
        }

        //是否有药品生产或经营许可证
        boolean hasDrugLicense = false;
        List<CorporationQualificationDto> qualifications = bean.getCorporationQualifications();

        //修改信息
        bean.setSearch(bean.getName());

        bean.setCreateId(this.getUser().getId());
        bean.setCreateName(this.getUser().getRealName());
        bean.setUpdateId(this.getUser().getId());
        bean.setUpdateName(this.getUser().getRealName());
        ApiRPCResult<Boolean> rpcResult = corporationAdminApi.authCorporation(bean);
        if (rpcResult.isFail() || !rpcResult.getData()) {
            return ResponseVo.errRest("修改信息失败");
        }
        //判断状态,调用审核
        boolean approval = true;
        StringBuffer stringBuffer = new StringBuffer();
        Integer status = bean.getStatus();
        if (status == null) {
            return ResponseVo.errRest("企业基本信息审核未勾选");
        }
        if (CorporationStateEnum.STASTS_NO_ADOPT.getCode() == status) {
            approval = false;
            stringBuffer.append("企业基本信息:审核未通过(");
            stringBuffer.append(bean.getRemarks() + ")。");
        }

        for (CorporationQualificationDto qualification : qualifications) {
            Byte state = qualification.getState();
            if (state == null) {
                return ResponseVo.errRest("资质审核未勾选:" + qualification.getName());
            }
            if (CheckCorporationStateEnum.AUDIT_NO_ADOPT.getCode() == state) {
                approval = false;
                stringBuffer.append(qualification.getName() + ":" + "审核未通过:(" + qualification.getRemarks() + "),");
            }
        }
        if (stringBuffer.length() > 0) {
            stringBuffer.deleteCharAt(stringBuffer.length() - 1);
        }
        if (approval) {
            for (CorporationQualificationDto qualificationDto : qualifications) {
                //校验药品生产或经营许可证号格式
                if (bean.getCorporationType() == CorporationTypeEnum.PRODUCTION.getCode() && qualificationDto.getName().contains("药品生产许可证")) {
                    if (StringUtils.isNotBlank(qualificationDto.getCode()) && !Pattern.matches(drugProductionLicenseRegular, qualificationDto.getCode())) {
                        return ResponseVo.errRest("药品生产许可证证照号格式不规范，请检查");
                    } else if (StringUtils.isNotBlank(qualificationDto.getLegalPersonName()) && !Pattern.matches(drugProductionLicenseRegular, qualificationDto.getLegalPersonName())) {
                        return ResponseVo.errRest("药品生产许可证证照号格式不规范，请检查");
                    }
                    hasDrugLicense = true;
                } else if (bean.getCorporationType() == CorporationTypeEnum.MANAGEMENT.getCode() && qualificationDto.getName().contains("药品经营许可证")) {
                    if (StringUtils.isNotBlank(qualificationDto.getCode()) && !Pattern.matches(drugBusinessLicenseRegular, qualificationDto.getCode())) {
                        return ResponseVo.errRest("药品经营许可证证照号格式不规范，请检查");
                    } else if (StringUtils.isNotBlank(qualificationDto.getLegalPersonName()) && !Pattern.matches(drugBusinessLicenseRegular, qualificationDto.getLegalPersonName())) {
                        return ResponseVo.errRest("药品经营许可证证照号格式不规范，请检查");
                    }
                    hasDrugLicense = true;
                }
            }

            //如果上传了药品生产或经营许可证，则需要校验法人授权委托书有效期不可以超过一年
            if (hasDrugLicense) {
                List<CorporationQualificationDto> corporationQualificationDtos = qualifications.stream().filter(item -> Objects.equals(item.getName(), "法人授权委托书")).collect(Collectors.toList());
                CorporationQualificationDto corporationQualificationDto = CollectionUtils.isEmpty(corporationQualificationDtos) ? null : corporationQualificationDtos.get(0);
                if (corporationQualificationDto != null) {
                    Date startDate = corporationQualificationDto.getStartDate();
                    Date endDate = corporationQualificationDto.getEndDate();
                    Date finalDate = DateUtil.getDate(startDate, 1, Calendar.YEAR);
                    //如果法人授权委托书有效期超过一年，校验不通过
                    if (endDate != null && finalDate != null && endDate.compareTo(finalDate) >= 0) {
                        return ResponseVo.errRest("法人授权委托书有效期不可以超过一年");
                    }
                }
            }



            //各项都审核通过,调用审核通过接口
            ApiRPCResult<Boolean> result = corporationAdminApi.completeTask(bean.getId(), this.getUser().getRealName());
            if (result.isFail() || (result.isSuccess() && !result.getData())) {
                return ResponseVo.errRest("审批失败");
            }
            return ResponseVo.successResultNotData("审批成功");
        }
        //调用审批驳回接口
        ApiRPCResult<Boolean> processnewResult = corporationAdminApi.commitProcessnew(bean.getId(), this.getUser().getRealName(), stringBuffer.toString());
        if (processnewResult.isFail() || (processnewResult.isSuccess() && !processnewResult.getData())) {
            return ResponseVo.errRest("驳回失败");
        }
        return ResponseVo.successResultNotData("驳回成功");
    }

    /**
     * 校验是否勾选二类、三类医疗器械细项
     *
     * @param corporationBusinessDtos
     * @return
     */
    private boolean verifyInstrument(List<CorporationBusinessDto> corporationBusinessDtos) {
        log.info("CorporationControllerV2.verifyInstrument#corporationBusinessDtos:{}", JSON.toJSONString(corporationBusinessDtos));
        if (CollectionUtils.isEmpty(corporationBusinessDtos)) {
            return true;
        }
        //如果有II类医疗器械(新版)、III类医疗器械(新版)、II类医疗器械、III类医疗器械，则对应的细项必须至少勾选一项(必须勾选到最细粒度)
        if (StringUtils.isBlank(instrumentCategoryIds)) {
            return true;
        }
        List<Long> instrumentCategoryIdList = Splitter.on(",").splitToList(instrumentCategoryIds).stream().map(Long::parseLong).collect(Collectors.toList());
        //是否存在II类医疗器械(新版)、III类医疗器械(新版)、II类医疗器械、III类医疗器械的数据 一级数据
        List<Long> firstCategoryIds = corporationBusinessDtos.stream().filter(f -> instrumentCategoryIdList.contains(f.getCategoryId())).map(m -> m.getCategoryId()).distinct().collect(Collectors.toList());
        log.info("CorporationControllerV2.verifyInstrument#instrumentCategoryIdList:{}", JSON.toJSONString(instrumentCategoryIdList));
        if (CollectionUtils.isEmpty(firstCategoryIds)) {
            return true;
        }

        //<firstCategoryId, 对应的二级分类数量> 如果有一级分类，看是否有二级分类数据，没有的返回false
        Map<Long, Long> firstCategoryIdCountMap = corporationBusinessDtos.stream().filter(f -> firstCategoryIds.contains(f.getPId())).collect(Collectors.groupingBy(item -> item.getPId(), Collectors.counting()));
        log.info("CorporationControllerV2.verifyInstrument#firstCategoryIdCountMap:{}", JSON.toJSONString(firstCategoryIdCountMap));
        if (MapUtils.isEmpty(firstCategoryIdCountMap)) {
            return false;
        }
        //某个一级分类下，没有二级分类
        List<Long> noSecondCategoryList = firstCategoryIdCountMap.values().stream().filter(item -> item <= 0).collect(Collectors.toList());
        log.info("CorporationControllerV2.verifyInstrument#noSecondCategoryList:{}", JSON.toJSONString(noSecondCategoryList));
        if (CollectionUtils.isNotEmpty(noSecondCategoryList)) {
            return false;
        }

        //二级分类id
        List<Long> secondCategoryIds = corporationBusinessDtos.stream().filter(f -> firstCategoryIds.contains(f.getPId())).map(m -> m.getCategoryId()).distinct().collect(Collectors.toList());
        log.info("CorporationControllerV2.verifyInstrument#secondCategoryIds:{}", JSON.toJSONString(secondCategoryIds));
        //<secondCategoryId, 对应的三级分类数量> 如果有二级分类，看是否有三级分类数据，没有的返回false
        Map<Long, Long> secondCategoryIdCountMap = corporationBusinessDtos.stream().filter(f -> secondCategoryIds.contains(f.getPId())).collect(Collectors.groupingBy(item -> item.getPId(), Collectors.counting()));
        log.info("CorporationControllerV2.verifyInstrument#secondCategoryIdCountMap:{}", JSON.toJSONString(secondCategoryIdCountMap));
        if (MapUtils.isEmpty(secondCategoryIdCountMap)) {
            return false;
        }
        //某个二级分类下，没有三级分类
        List<Long> noThirdCategoryList = secondCategoryIdCountMap.values().stream().filter(item -> item <= 0).collect(Collectors.toList());
        log.info("CorporationControllerV2.verifyInstrument#noThirdCategoryList:{}", JSON.toJSONString(noThirdCategoryList));
        if (CollectionUtils.isNotEmpty(noThirdCategoryList)) {
            return false;
        }

        return true;
    }

    @GetMapping("queryCorporation")
    @ResponseBody
    public ResponseVo queryCorporation(Long id) {
        if (null == id) {
            return ResponseVo.errRest("缺少重要查询参数");
        }
        ApiRPCResult<CorporationDto> apiRPCResult = corporationAdminApi.queryCorporation(id);
        if (apiRPCResult.isFail()||apiRPCResult.getData()==null) {
            return ResponseVo.errRest("查询失败");
        }
        CorporationVo vo = CorporationHelper.convertToVo(apiRPCResult.getData());
        //医疗器械二类和三类若勾选，对应的细项是否默认勾选开关：true-不勾选 false-勾选
        vo.setSelectItemsFlag(selectItemsFlag);
        //查供货对象
        if(StringUtils.isEmpty(vo.getShopCode())){
            return ResponseVo.successResult(vo);
        }
        ApiRPCResult<PopDeliveryDto> result =  popDeliveryApi.getPopDeliveryInfo(vo.getOrgId());
        if (result.isSuccess() && Objects.nonNull(result.getData())){
            vo.setShipperCode(result.getData().getShipperCode());
        }
        List<MerchantCustomerTypeBusinessDto> customerTypes = merchantCustomerTypeRemote.getBusinessType();
        Map<Integer, MerchantCustomerTypeBusinessDto> typeMap = customerTypes.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
        Map<String, String> userTypeMap = shopUserTypeRemote.getShopUserTypeByShopCode(Arrays.asList(vo.getShopCode()));
        String userType = userTypeMap.get(vo.getShopCode());
        List<Integer> list = StringUtils.isEmpty(userType)?new ArrayList<>(0):Arrays.stream(userType.split(",")).map(item-> NumberUtils.toInt(item)).collect(Collectors.toList());
        String supplyCustomerType = list.stream().map(item -> typeMap.containsKey(item) ? typeMap.get(item).getName() : null)
                .filter(item -> item != null).collect(Collectors.joining(","));
        vo.setSupplyCustomerType(supplyCustomerType);
        return ResponseVo.successResult(vo);
    }

    /**
     * 经营范围
     *
     * @param corporationType
     * @return
     */
    @GetMapping("listBusinessCategory")
    @ResponseBody
    public ResponseVo listBusinessCategory(int corporationType) {
        ApiRPCResult<List<BusinessCategoryDictDto>> apiRPCResult = corporationAdminApi.selectBusinessScope(corporationType);
        if (apiRPCResult.isFail()) {
            return ResponseVo.errRest("获取经营类目失败");
        }
        return ResponseVo.successResult(apiRPCResult.getData());
    }

    /**
     * 获取资质信息
     *
     * @param corporationType
     * @return
     */
    @GetMapping("listPopBusinessQualificationConfigs")
    @ResponseBody
    public ResponseVo listPopBusinessQualificationConfigs(int corporationType) {
        ApiRPCResult<PopQualificationsCategoryExtDto> apiRPCResult = corporationAdminApi.listPopBusinessQualificationConfigs(corporationType);
        if (apiRPCResult.isFail()) {
            return ResponseVo.errRest("获取资质信息失败");
        }
        return ResponseVo.successResult(apiRPCResult.getData());
    }

    /**
     * 经营范围类目树
     *
     * @param corporationType
     * @return
     */
    @ResponseBody
    @RequestMapping("/getBusinessTree")
    public ResponseVo getBusinessTree(Integer cId, Integer corporationType, Long category) {
        try {
            if (null == cId || corporationType == null || null == category) {
                return ResponseVo.errRest("缺少参数");
            }
            ApiRPCResult<List<BusinessCategoryDictDto>> scopeTree = businessCategoryAdminApi.getBusinessTree(corporationType);
            if (scopeTree.isFail()) {
                log.warn("获取经营类目字典信息失败,error:{}", scopeTree.getErrMsg());
                return ResponseVo.errRest("获取经营类目字典信息失败");
            }
            List<BusinessCategoryDictDto> businessScopeTree = scopeTree.getData();
            ApiRPCResult<List<CorporationBusinessDto>> apiRPCResult = corporationBusinessAdminApi.listCorporationBusiness(cId);
            if (apiRPCResult.isFail()) {
                log.warn("获取经营类目信息失败,error:{}", apiRPCResult.getErrMsg());
                return ResponseVo.errRest("获取经营类目信息失败");
            }
            List<CorporationBusinessDto> businessDtos = apiRPCResult.getData();
            List<ZTree> arrayList = new ArrayList<>();
            List<Long> categoryIdList = businessDtos.stream().map(CorporationBusinessDto::getCategoryId).collect(Collectors.toList());
            for (BusinessCategoryDictDto businessCategoryDictDto : businessScopeTree) {
                if (category != businessCategoryDictDto.getCategoryId()) {
                    continue;
                }
                Long categoryId = businessCategoryDictDto.getCategoryId();
                String categoryName = businessCategoryDictDto.getCategoryName();
                ZTree zTree = new ZTree();
                zTree.setId(categoryId);
                zTree.setName(categoryName);
                if (categoryIdList.contains(categoryId)) {
                    zTree.setChecked(true);
                }
                zTree.setCheckDisable(true);
                List<BusinessScopeDto> businessScopeDtoList = businessCategoryDictDto.getBusinessScopeDtoList();
                //设定树型结构中只允许修改同种类型下类目,"其他" 类目除外  specialCategoryList
                for (BusinessScopeDto businessScopeDto : businessScopeDtoList) {
                    Long businessScopeDtoId = businessScopeDto.getId();
                    if (categoryIdList.contains(businessScopeDtoId)) {
                        zTree.setCheckDisable(false);
                    }
                    if (categoryIdList.contains(businessScopeDtoId)) {
                        zTree.setChecked(true);
                    }
                }
                //如果树结构下有选中数据那么相应类目也选中
                if (!zTree.isChecked()) {
                    zTree.setChecked(true);
                }
                List<ZTree> relation = ZTreeByCategoryRelation.getZTreeByCategoryRelation(businessScopeDtoList, categoryIdList);
                zTree.setChildren(relation);
                arrayList.add(zTree);
            }
            return ResponseVo.successResult(arrayList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ResponseVo.errRest("获取失败");
    }

    /**
     * 领取
     *
     * @param taskId
     * @return
     */
    @ResponseBody
    @RequestMapping("/claim")
    @AvoidRepeatableCommit(timeout = 2)
    @Deprecated
    public ResponseVo claim(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            return ResponseVo.errRest("缺少重要参数");
        }
        String oaId = getUser().getOaId();
        ApiRPCResult<Boolean> rpcResult = corporationAdminApi.claim(oaId, taskId);
        if (rpcResult.isFail()) {
            return ResponseVo.errRest("领取失败");
        }
        return ResponseVo.successResultNotData();
    }

    /**
     * 查询流程信息
     * processInstanceId
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/queryProcess")
    @Deprecated
    public ResponseVo queryProcess(String processInstanceId) {
        if (StringUtils.isEmpty(processInstanceId)) {
            return ResponseVo.errRest("暂无流程信息");
        }
        ApiRPCResult<JSONObject> rpcResult = corporationAdminApi.queryProcess(processInstanceId);
        if (rpcResult.isFail()) {
            return ResponseVo.errRest("获取流程信息失败");
        }
        return ResponseVo.successResult(rpcResult.getData());
    }

    /**
     * 查询是否要显示审核按钮
     * processInstanceId
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/getIsAuditable")
    @Deprecated
    public ResponseVo getIsAuditable(String processInstanceId) {
        if (StringUtils.isBlank(processInstanceId)) {
            return ResponseVo.errRest("缺少重要参数");
        }
        ApiRPCResult<ErpApprovalProcessRestDto> apiRPCResult = corporationAdminApi.queryAuditNodeInfoNow(processInstanceId);
        if (apiRPCResult.isFail()) {
            return ResponseVo.errRest("获取流程信息失败");
        }
        String oaId = getUser().getOaId();
        String handlerId = apiRPCResult.getData().getHandlerId();
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("showExamine", false);
        hashMap.put("needClaim", false);
        hashMap.put("taskId", "");
        if (StringUtils.isNotBlank(handlerId) && handlerId.contains(oaId)) {
            //可以审核
            hashMap.put("showExamine", true);
            hashMap.put("taskId", apiRPCResult.getData().getTaskId());
            if (handlerId.contains(",")) {
                //ID中有逗号说明还未领取需要先领取
                hashMap.put("needClaim", false);
            }
        }
        return ResponseVo.successResult(hashMap);
    }

    @GetMapping("/listCorporation")
    @ResponseBody
    public ResponseVo listCorporation(QueryCorporationParam queryCorporationParam) {
        List<Long> provIds = getProvIds(queryCorporationParam.getProvId());
        if(CollectionUtils.isEmpty(provIds)){
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageNum(queryCorporationParam.getPageNum());
            pageInfo.setPageSize(queryCorporationParam.getPageSize());
            pageInfo.setTotal(0);
            return ResponseVo.successResult(pageInfo);
        }
        queryCorporationParam.setProvIds(provIds);
        ApiRPCResult<PageInfo<CorporationAndUserExtDto>> rpcResult = corporationAdminApi.listCorporations(queryCorporationParam);
        log.info("CorporationControllerV2.listCorporation # queryCorporationParam:{}, rpcResult:{}", JSON.toJSONString(queryCorporationParam),
                JSON.toJSONString(rpcResult));
        if (rpcResult.isFail()) {
            return ResponseVo.errRest("查询数据异常");
        }
        return  popCorporationService.queryShopInfoShopCodes(rpcResult.getData());
    }

    @PostMapping("/nextday/setNextdayEnabed")
    public ResponseVo setNextdayEnabed(@RequestBody ShopNextdayConfDTO nextdayConfDTO) {
        log.info("#CorporationControllerV2.setNextdayEnabed#nextdayConfDTO:{}", JSON.toJSONString(nextdayConfDTO));
        try {
            SysUser user = this.getUser();
            if(Objects.isNull(user)){
                return  ResponseVo.errRest(ResultCodeEnum.USER_NOT_EXISTS.getMsg());
            }
            if (Objects.isNull(nextdayConfDTO) || StrUtil.isEmpty(nextdayConfDTO.getShopCode())
                    || Objects.isNull(nextdayConfDTO.getNextdayEnabled())){
                return  ResponseVo.errRest(ApiResultCodeEum.PARAMETER_ERROR.getMsg());
            }

            ApiRPCResult result = shopAdminApi.setNextdayEnabed(nextdayConfDTO.getShopCode(), nextdayConfDTO.getNextdayEnabled(), user.getId(), user.getUsername());
            if (result.isFail()){
                return  ResponseVo.errRest(result.getMsg());
            }
            log.info("更新店铺次日达开关入参corporationAdminApi-updateShopNextdayEnable，shopCode：{}, nextdayEnabed：{}", nextdayConfDTO.getShopCode(), nextdayConfDTO.getNextdayEnabled());
            corporationAdminApi.updateShopNextdayEnable(nextdayConfDTO.getShopCode(), nextdayConfDTO.getNextdayEnabled());
            return ResponseVo.successResult(null);
        } catch (Exception e) {
            log.error("次日达开通异常", e);
            return ResponseVo.errRest("次日达开通异常");
        }
    }

    @GetMapping("/nextday/queryConf")
    public ResponseVo<ShopNextdayConfInfoDTO> queryConf(@RequestParam(value = "shopCode") String shopCode) {
        if (StrUtil.isEmpty(shopCode)){
            return  ResponseVo.errRest(ApiResultCodeEum.PARAMETER_ERROR.getMsg());
        }
        log.info("#CorporationControllerV2.queryConf#shopCode:{}", shopCode);
        try {
            ShopNextdayConfQueryParam queryParam = new ShopNextdayConfQueryParam();
            queryParam.setShopCode(shopCode);
            queryParam.setIsNeedAreaConf(Boolean.TRUE);
            queryParam.setIsNeedTimeConf(Boolean.TRUE);
            ApiRPCResult<ShopNextdayConfInfoDTO> result = shopNextdayConfApi.queryConf(queryParam);
            if (result.isSuccess()) {
                return ResponseVo.successResult(result.getData());
            }
            return ResponseVo.errRest(result.getMsg());
        } catch (Exception e) {
            log.error("次日达查询配置异常", e);
            return ResponseVo.errRest("次日达查询配置异常");
        }
    }


    @GetMapping("/updateShopStatusByShopCode")
    @ResponseBody
    public ResponseVo updateShopStatusByShopCode(@RequestParam(value = "shopCode",required = false) String shopCode,
                                                 @RequestParam(value = "status",required = false) Integer status,
                                                 @RequestParam(value = "remark",required = false)String remark) {
        try {
            SysUser user = this.getUser();
            if(Objects.isNull(user)){
                return  ResponseVo.errRest(ResultCodeEnum.USER_NOT_EXISTS.getMsg());
            }

            if(CollectionUtils.isEmpty(operationUsers) || ! operationUsers.contains(user.getUsername())){
                return  ResponseVo.errRest(ResultCodeEnum.USER_HAS_NO_PERMISSION.getMsg());
            }

            if(StringUtils.isEmpty(shopCode) || Objects.isNull(status)){
                return ResponseVo.errRest(ApiResultCodeEum.PARAMETER_ERROR.getMsg());
            }
            return popCorporationService.updateShopStatusByShopCode(user, shopCode, status,remark);
        } catch (Exception e) {
            log.info("CorporationControllerV2.updateShopStatusByShopCode # shopCode:{}, status:{}", shopCode, status, e);
            return ResponseVo.errRest(ResultCodeEnum.SHOP_QUERY_ERROR.getMsg());
        }
    }

    @GetMapping("dicAreas")
    public ResponseVo dicAreas(String parentCode) {
        if (StringUtils.isEmpty(parentCode)) {
            return ResponseVo.errRest("缺少重要参数");
        }
        SysUser user = getUser();
        String regMobile = user.getPhone();
        ApiRPCResult<List<RegionBusinessDto>> rpcResult = addressAreaApi.queryRegionByParentCodeList(parentCode, regMobile);
        if (rpcResult.isFail()) {
            return ResponseVo.errRest("获取地址区域失败");
        }
        return ResponseVo.successResult(rpcResult.getData());
    }

    @GetMapping("checkShopName")
    @ResponseBody
    public ResponseVo checkShopName(String name,Long id) {
        if (StringUtils.isEmpty(name) || id == null) {
            return ResponseVo.errRest("缺少重要查询参数");
        }
        ApiRPCResult<Integer> apiRPCResult = corporationAdminApi.checkShopName(name.trim(),id);
        if (apiRPCResult.isFail()) {
            return ResponseVo.errRest("查询失败");
        }
        return ResponseVo.successResult(apiRPCResult.getData());
    }

    /**
     * 修改店铺名称
     * @param name
     * @param id
     * @return
     */
    @GetMapping("updateName")
    @ResponseBody
    public ResponseVo updateName(String name,Long id,String orgId) {
        if (StringUtils.isEmpty(name) || id == null || StringUtils.isEmpty(orgId)) {
            return ResponseVo.errRest("缺少重要查询参数");
        }
        ApiRPCResult apiRPCResult = corporationAdminApi.updateName(name,id,orgId);
        if (apiRPCResult.isFail()) {
            return ResponseVo.errRest("修改失败失败");
        }
        return ResponseVo.successResult("修改成功");
    }

    @GetMapping("/getPopDeliveryInfo")
    public ResponseVo<?> getPopDeliveryInfo(@RequestParam(value = "orgId",required = false)String orgId){
        if (StringUtils.isEmpty(orgId)) {
            return ResponseVo.errRest("缺少重要查询参数");
        }
        ApiRPCResult<PopDeliveryDto> result =  popDeliveryApi.getPopDeliveryInfo(orgId);
        if (result.isFail()){
            return ResponseVo.errRest(result.getErrMsg());
        }
        return ResponseVo.successResult(result.getData());
    }


    /**
     * 发货配置信息只保存手机号
     * @param popDeliveryDto
     * @return
     */
    @PostMapping("/addOnlyPopDeliveryMobile")
    public ResponseVo<?> addOrUpdatePopDeliveryInfo(@RequestBody PopDeliveryDto popDeliveryDto){
        try {
            if (Objects.isNull(popDeliveryDto) || StringUtils.isBlank(popDeliveryDto.getDeliveryMobile()) || StringUtils.isBlank(popDeliveryDto.getOrgId())){
                return ResponseVo.errRest("参数为空");
            }
            log.info("CorporationControllerV2.addOrUpdatePopDeliveryInfo request:{}", JSONObject.toJSONString(popDeliveryDto));
            SysUser user = this.getUser();
            if(Objects.isNull(user)){
                return  ResponseVo.errRest(ResultCodeEnum.USER_NOT_EXISTS.getMsg());
            }
            popDeliveryDto.setCreator(user.getRealName());
            popDeliveryDto.setUpdator(user.getRealName());

            ApiRPCResult<Boolean> result = popDeliveryApi.addOnlyPopDeliveryMobile(popDeliveryDto);
            if (result.isFail()){
                return ResponseVo.errRest(result.getErrMsg());
            }
            return ResponseVo.successResult("添加成功");
        } catch (Exception e) {
            log.info("PopDeliveryInfoController.addOrUpdatePopDeliveryInfo error，popDeliveryDto:{}",JSONObject.toJSONString(popDeliveryDto),e);
            return ResponseVo.errRest("系统错误");
        }
    }

    /**
     * 获取承运方业务类型
     * @return
     */
    @GetMapping("/getBusinessTypes")
    public ResponseVo<?> getBusinessTypes(@RequestParam(value = "orgId",required = false)String orgId){
        if (StringUtils.isBlank(orgId)){
            return ResponseVo.errRest("orgId为空");
        }
        ApiRPCResult<List<PopDeliveryBusinessTypeDto>> apiRPCResult = popDeliveryApi.getBusinessTypes(orgId);
        return ResponseVo.successResult(apiRPCResult.getData());
    }

    @GetMapping("/findCorporationAccount")
    public ResponseVo<?> findCorporationAccount(@RequestParam(value = "orgId",required = false)String orgId){
        if (StringUtils.isEmpty(orgId)) {
            return ResponseVo.errRest("缺少重要查询参数");
        }
        ApiRPCResult<CorporationDto> apiRPCResult = corporationAdminApi.queryCorporation(orgId);
        if (apiRPCResult.isFail() || apiRPCResult.getData() == null) {
            return ResponseVo.errRest("未查询到企业信息");
        }
        CorporationDto corporationDto = apiRPCResult.getData();
        CorporationAccountDto corporationAccountDto = new CorporationAccountDto();
        //富民支付商家提现账户信息查询
        if (Objects.equals(corporationDto.getPaymentChannel(), PaymentChannelEnum.FUMIN.getCode()) || Objects.equals(corporationDto.getPaymentChannel(), PaymentChannelEnum.PINGAN.getCode())) {
            ApiRPCResult<BankAccountDto> result = popBankApi.queryBankAccountByOrgId(orgId);
            if (result.isSuccess() && result.getData() != null){
                BankAccountDto data = result.getData();
                corporationAccountDto.setRegisteredName(data.getRegisteredName());
                corporationAccountDto.setBankName(data.getBankName());
                corporationAccountDto.setSubBankName(data.getSubBankName());
                corporationAccountDto.setRegisteredBankAccount(data.getRegisteredBankAccount());
            }
        }else{
            ApiRPCResult<PopCashAdvanceDto> result = popCashAdvanceApi.getLastCashAdvance(orgId);
            if (result.isSuccess() && result.getData() != null){
                PopCashAdvanceDto data = result.getData();
                String accountBank = data.getAccountBank();
                String bankName = accountBank.substring(0, accountBank.indexOf("银行") + 2);
                if (bankName.length() == 1){
                    bankName = accountBank.substring(0, accountBank.indexOf("社") + 1);
                }
                corporationAccountDto.setRegisteredName(data.getAccountName());
                corporationAccountDto.setBankName(bankName);
                corporationAccountDto.setSubBankName(data.getAccountBank());
                corporationAccountDto.setRegisteredBankAccount(data.getAccountNum());
            }
        }
        return ResponseVo.successResult(corporationAccountDto);
    }

    @PostMapping("/updateAccount")
    public ResponseVo<?> updateAccount(@RequestBody @Valid CorporationPhoneUpdateDto param, BindingResult result) {
        log.info("Corporation.CorporationControllerV2.updateAccount#param:{}", JSON.toJSONString(param));
        try {
            if (result.hasErrors()) {
                String errorMessage = result.getAllErrors().stream().map(ObjectError::getDefaultMessage).collect(Collectors.joining(","));
                return ResponseVo.errRest(errorMessage);
            }
            ApiRPCResult apiRPCResult = corporationAdminApi.updateAccount(param.getOrgId(), param.getOldPhone(), param.getNewPhone());
            if (apiRPCResult.isFail()) {
                return ResponseVo.errRest(apiRPCResult.getErrMsg());
            }
            return ResponseVo.successResult(null);
        } catch (Exception e) {
            log.error("Corporation.CorporationControllerV2.updateAccount#error.param:{}", JSON.toJSONString(param), e);
            return ResponseVo.errRest("更新账号失败");
        }
    }

    @GetMapping("/offlineShop")
    public ResponseVo<?> offlineShop(@RequestParam(value = "orgId",required = false)String orgId){
        try {
            if (StringUtils.isEmpty(orgId)){
                return ResponseVo.errRest("orgId不能为空");
            }
            ApiRPCResult<CorporationDto> apiRPCResult = corporationAdminApi.queryCorporation(orgId);
            if (Objects.isNull(apiRPCResult)|| apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())){
                return ResponseVo.errRest("查询不到商业信息");
            }
            SysUser user = this.getUser();
            return popCorporationService.offlineShop(user,apiRPCResult.getData());
        } catch (Exception e) {
            log.error("Corporation.CorporationControllerV2.offlineShop#error.orgId:{}", orgId, e);
            return ResponseVo.errRest("账户下线失败");
        }
    }

    @GetMapping("/updatePaymentProve")
    public ResponseVo<?> updatePaymentProve(@RequestParam(value = "orgId",required = false)String orgId,
                                            @RequestParam(value = "money",required = false)BigDecimal money){
        try {
            if (StringUtils.isEmpty(orgId) || Objects.isNull(money) || money.compareTo(BigDecimal.ZERO) != 1){
                return ResponseVo.errRest("入参不正确");
            }
            ApiRPCResult<CorporationDto> apiRPCResult = corporationAdminApi.queryCorporation(orgId);
            if (Objects.isNull(apiRPCResult)|| apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())){
                return ResponseVo.errRest("查询不到商业信息");
            }
            SysUser user = getUser();
            CorporationDto corporationDto = apiRPCResult.getData();
            corporationAdminApi.updatePaymentProve(corporationDto.getId(),money,user.getRealName(),user.getId());
            return ResponseVo.successResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("Corporation.CorporationControllerV2.updatePaymentProve#error.orgId:{},money:{}", orgId,money, e);
            return ResponseVo.errRest("修改保证金失败");
        }
    }

    @GetMapping("/initShop")
    public ResponseVo initShop(@RequestParam(value = "orgId",required = false)String orgId) {
        List<String> orgIds = new ArrayList<>();
        if (StringUtils.isNotBlank(orgId)){
            String[] split = orgId.split(",");
            for (int i = 0 ;i<split.length;i++){
                orgIds.add(split[i]);
            }
        }
        ApiRPCResult<Boolean> apiRPCResult = corporationAdminApi.initShop(orgIds);
        if (apiRPCResult.isFail()) {
            return ResponseVo.errRest("创建失败");
        }
        return ResponseVo.successResult(apiRPCResult.getData());
    }
    @GetMapping("/initShopStatus")
    public ResponseVo initShopStatus() {
        ApiRPCResult<Boolean> apiRPCResult = corporationAdminApi.initShopStatus();
        if (apiRPCResult.isFail()) {
            return ResponseVo.errRest("创建失败");
        }
        return ResponseVo.successResult(apiRPCResult.getData());
    }

    @GetMapping("/getShopStatusLog")
    public ResponseVo getShopStatusLog(@RequestParam(value = "cId",required = false)Long cId,
                                       @RequestParam(value = "pageNum",required = false)Integer pageNum,
                                       @RequestParam(value = "pageSize",required = false)Integer pageSize){
        if (Objects.isNull(cId)){
            return ResponseVo.errRest("入参不能为空");
        }

        ApiRPCResult<PageInfo<PopCheckLogDto>> apiRPCResult = corporationAdminApi.getShopStatusLog(cId, pageNum, pageSize);
        if (apiRPCResult == null && apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())){
            return ResponseVo.errRest("查询失败");
        }
        return ResponseVo.successResult(apiRPCResult.getData());
    }




    @ResponseBody
    @PostMapping(value = "/addOrUpdateWithDrawCard")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> AddOrUpdateWithDrawCard(@RequestBody WithDrawCardVo withDrawCardVo) {
        log.info("CorporationController.AddOrUpdateWithDrawCard#orgId:{},withDrawCardVo:{}", withDrawCardVo.getOrgId(), JSON.toJSONString(withDrawCardVo));
        try {

            SysUser user = this.getUser();
            if(Objects.isNull(user)){
                return  ResponseVo.errRest(ResultCodeEnum.USER_NOT_EXISTS.getMsg());
            }

            if (Objects.isNull(withDrawCardVo)) {
                return ResponseVo.errRest("缺少必传参数");
            }

            if (StringUtils.isEmpty(withDrawCardVo.getAccountName()) || withDrawCardVo.getAccountName().length() > 30) {
                return ResponseVo.errRest("账户名称必填且最多30个字符");
            }

            if (StringUtils.isEmpty(withDrawCardVo.getBankCode()) || withDrawCardVo.getBankCode().length() > 60) {
                return ResponseVo.errRest("银行卡号必填且最多60个字符");
            }

            if (StringUtils.isEmpty(withDrawCardVo.getBankName()) || withDrawCardVo.getBankName().length() > 300) {
                return ResponseVo.errRest("开户行必填且最多300个字符");

            }

            if (StringUtils.isEmpty(withDrawCardVo.getSubBankName()) || withDrawCardVo.getSubBankName().length() > 300) {
                return ResponseVo.errRest("开户支行必填且最多300个字符");
            }

            if (Objects.isNull(withDrawCardVo.getCardType())) {
                return ResponseVo.errRest("提现方式不允许为空");
            }

            ApiRPCResult<CorporationDto> apiCorporation = corporationAdminApi.queryCorporation(withDrawCardVo.getOrgId());
            if (apiCorporation.isFail() || apiCorporation.getData() == null) {
                return ResponseVo.errRest("未查询到企业信息");
            }

            //校验名称
            if (Objects.equals(withDrawCardVo.getCardType(), WithDrawCardEnum.CORPORATE_CARD.getCode()) && (apiCorporation.getData().getCompanyName().length() >= 5 && withDrawCardVo.getAccountName().length() >= 5) && !withDrawCardVo.getAccountName().substring(0, 5).equals(apiCorporation.getData().getCompanyName().substring(0, 5))) {
                return ResponseVo.errRest("账户名称”需填写企业名称，请修改后重新提交");
            }

            if(Objects.equals(withDrawCardVo.getCardType(),WithDrawCardEnum.PERSONAL_CARD.getCode()) && StringUtils.isEmpty(withDrawCardVo.getApplicationUrl())){
                return ResponseVo.errRest("个人提现卡方式下申请函不允许为空");
            }
            withDrawCardService.addOrUpdateWithDrawCard(withDrawCardVo,apiCorporation.getData().getId(),user);
            return ResponseVo.successResult(true);
        } catch (Exception e) {
            log.error("CorporationController.AddOrUpdateWithDrawCard#未知异常 orgId:{},withDrawCardVo:{}", withDrawCardVo.getOrgId(), JSON.toJSONString(withDrawCardVo), e);
            return ResponseVo.errRest("店铺提现方式更改失败");
        }
    }


    /**
     * 根据orgId查询提现卡信息
     * @param orgId
     */
    @ResponseBody
    @GetMapping(value = "/withDrawCardInfo")
    public ResponseVo<List<WithDrawCardVo>> selectWithDrawCardByOrgId(@RequestParam String orgId) {
        try {
            List<WithDrawCardVo> withDrawCardVoList = withDrawCardService.selectWithDrawCardByOrgId(orgId);
            return ResponseVo.successResult(withDrawCardVoList);
        } catch (Exception e) {
            log.error("查询提现卡信息异常", e);
            return ResponseVo.errRest("查询提现卡信息异常");
        }
    }
}
