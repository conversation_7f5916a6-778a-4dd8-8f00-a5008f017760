package com.xyy.ec.pop.dto.mop;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.xyy.scm.constant.annotation.Explain;
import lombok.Data;

import java.io.Serializable;

/**
 * 目标管理Excel
 *
 * @author: duHao
 * @since: 16:55 2024/12/18
 */
@Data
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL, fillForegroundColor = 10)
@HeadFontStyle(fontHeightInPoints = 12)
public class TargetManagerExcelDTO implements Serializable {

    @Explain("目标月份")
    @ExcelProperty(value = "目标月份",index = 0)
    private String targetMonth;

    @Explain("考核对象")
    @ExcelProperty(value = "考核对象",index = 1)
    private String objTypeStr;

    @Explain("工号/商户编码")
    @ExcelProperty(value = "工号/商户编码",index = 2)
    private String objCode;

    @Explain("目标值")
    @ExcelProperty(value = "目标值",index = 3)
    private String targetValueStr;

}
