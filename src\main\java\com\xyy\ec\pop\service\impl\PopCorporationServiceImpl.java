package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.enums.ResultCodeEnum;
import com.xyy.ec.pop.enums.ShopStatusEnum;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.CorporationConverHelper;
import com.xyy.ec.pop.model.CorporationAndUserExtVo;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.CorporationPriceTypeRemote;
import com.xyy.ec.pop.remote.CorporationSettleCycleAdapter;
import com.xyy.ec.pop.remote.PopCommissionSettlementRemoteAdapter;
import com.xyy.ec.pop.remote.PopCorporationRemoteAdapter;
import com.xyy.ec.pop.server.api.erpUtil.api.ErpPopCorporationUtilApi;
import com.xyy.ec.pop.server.api.fdd.api.PlatformServiceAgreementApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationAreaSaleTypeEnum;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationPriceTypeEnum;
import com.xyy.ec.pop.server.api.merchant.api.seller.SupplierUserApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationAndUserExtDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.merchant.dto.PopCorporationErrorPriceCountDto;
import com.xyy.ec.pop.server.api.merchant.dto.SupplierUserDto;
import com.xyy.ec.pop.server.api.seller.api.PopDeliveryApi;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleSetDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCorporationDto;
import com.xyy.ec.pop.server.api.seller.dto.PopDeliveryDto;
import com.xyy.ec.pop.server.api.shop.dto.PopCheckLogDto;
import com.xyy.ec.pop.service.BondService;
import com.xyy.ec.pop.service.BusinessCategoryCommissionService;
import com.xyy.ec.pop.service.CorporationPriceTypeService;
import com.xyy.ec.pop.service.PopCorporationAreaService;
import com.xyy.ec.pop.service.PopCorporationService;
import com.xyy.ec.pop.service.PopCorporationToCorTransferService;
import com.xyy.ec.pop.service.RemoteService;
import com.xyy.ec.pop.vo.BusinessCategoryCommissionVo;
import com.xyy.ec.pop.vo.CommissionsSettlementTypeVo;
import com.xyy.ec.pop.vo.CooperationInfoVo;
import com.xyy.ec.pop.vo.CorporationAreaInfoVo;
import com.xyy.ec.pop.vo.CorporationSettleCycleVo;
import com.xyy.ec.pop.vo.CorporationVo;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 店铺信息，替换
 * <AUTHOR>
 */
@Service
@Slf4j
public class PopCorporationServiceImpl implements PopCorporationService {

    @Autowired
    private BondService bondService;
    @Autowired
    private BusinessCategoryCommissionService commissionService;
    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;
    @Autowired
    private PopCorporationAreaService corporationAreaService;
    @Autowired
    private PopCorporationToCorTransferService popCorporationToCorTransferService;
    @Autowired
    private PopCommissionSettlementRemoteAdapter popCommissionSettlementRemoteAdapter;
    @Autowired
    private RemoteService remoteService;
    @Reference
    private SupplierUserApi supplierUserApi;
    @Reference
    private ErpPopCorporationUtilApi erpPopCorporationUtilApi;
    @Resource
    private CorporationSettleCycleAdapter corporationSettleCycleAdapter;

    @Autowired
    private CorporationPriceTypeService corporationSaleTypeService;
    @Autowired
    private CorporationPriceTypeRemote corporationPriceTypeRemote;

    @Reference(version = "1.0.0")
    private PlatformServiceAgreementApi platformServiceAgreementApi;

    @Reference(version = "1.0.0")
    private PopDeliveryApi popDeliveryApi;

    @Reference(version = "1.0.0")
    private CorporationAdminApi corporationAdminApi;

    @Override
    public CooperationInfoVo cooperationInfo(String orgId) throws ServiceException {
        CooperationInfoVo vo = new CooperationInfoVo();
        CorporationDto corporationDto = popCorporationRemoteAdapter.queryByOrgId(orgId);
        if (corporationDto == null) {
            throw new ServiceException("错误的机构编码");
        }


        vo.setCorporation(convertToVo(corporationDto));
        vo.setBond(bondService.getBond(orgId));
        vo.setCommissions(commissionService.getCommissions(orgId));
        List<CorporationAreaInfoVo> areaVos = corporationAreaService.getCorporationArea(corporationDto.getId());
        vo.setDrugAreas(areaVos.stream().filter(item -> item.getSaleType() == CorporationAreaSaleTypeEnum.DRUG.getCode()).collect(Collectors.toList()));
        vo.setNonDrugAreas(areaVos.stream().filter(item -> item.getSaleType() == CorporationAreaSaleTypeEnum.NON_DRUG.getCode()).collect(Collectors.toList()));
        //药品区域和非药区域应该是相同的
        vo.setAreas(vo.getDrugAreas());
        vo.setSettlementTypes(convertSettlementTypeListToVoList(popCommissionSettlementRemoteAdapter.queryCooperationCommissionsSettlementType(orgId)));
        vo.setToCorTransferVo(popCorporationToCorTransferService.getTransferVo(orgId));
        vo.setCorporationPriceTypeVo(corporationSaleTypeService.getPriceType(orgId));
        vo.setSettleCycle(CorporationSettleCycleVo.from(corporationSettleCycleAdapter.findSettleCycle(orgId)));
        return vo;
    }


    private CorporationVo convertToVo(CorporationDto dto) {
        CorporationVo vo = new CorporationVo();
        vo.setId(dto.getId());
        vo.setOrgId(dto.getOrgId());
        vo.setName(dto.getName());
        vo.setCompanyName(dto.getCompanyName());
        vo.setShopCategory(dto.getShopCategory());
        ApiRPCResult<PopDeliveryDto> popDeliveryInfo = popDeliveryApi.getPopDeliveryInfo(dto.getOrgId());
        if (popDeliveryInfo.isSuccess() && Objects.nonNull(popDeliveryInfo.getData())){
            vo.setShipperCode(popDeliveryInfo.getData().getShipperCode());
        }
        vo.setBusinessAttribute(dto.getBusinessAttribute());
        return vo;
    }

    //转换企业的 佣金结算方式
    public static List<CommissionsSettlementTypeVo> convertSettlementTypeListToVoList(List<PopCommissionSettleSetDto> setDtoList) {
        if (CollectionUtils.isEmpty(setDtoList)) {
            return new ArrayList<>();
        }
        List<CommissionsSettlementTypeVo> commissionSettleTypeVoList = setDtoList.stream().map(setDto -> {
            CommissionsSettlementTypeVo commissionsSettlementTypeVo = new CommissionsSettlementTypeVo();
            commissionsSettlementTypeVo.setMonthType(setDto.getMonthType());
            commissionsSettlementTypeVo.setSettlementType(setDto.getSettlementType());
            return commissionsSettlementTypeVo;
        }).collect(Collectors.toList());
        return commissionSettleTypeVoList;
    }

    @Override
    public ResponseVo<PageInfo<CorporationAndUserExtVo>> queryShopInfoShopCodes(PageInfo<CorporationAndUserExtDto> data) {
        List<CorporationAndUserExtDto> corporationAndUserExtDtos = data.getList();
        List<CorporationAndUserExtVo> corporationAndUserExtVos = CorporationConverHelper.convertCorporationAndUserExtDtosToVoList(corporationAndUserExtDtos);

        PageInfo<CorporationAndUserExtVo> extVoPageInfo = new PageInfo<>(corporationAndUserExtVos);
        extVoPageInfo.setPageNum(data.getPageNum());
        extVoPageInfo.setPageSize(data.getPageSize());
        extVoPageInfo.setTotal(data.getTotal());
        if (CollectionUtils.isEmpty(corporationAndUserExtDtos)) {
            return ResponseVo.successResult(extVoPageInfo);
        }

        List<String> shopCodes = corporationAndUserExtDtos.stream().map(CorporationAndUserExtDto::getShopCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopCodes)) {
            return ResponseVo.successResult(extVoPageInfo);
        }

        List<ShopInfoDTO> shopInfoDTOS = remoteService.queryInfoByShopCodes(shopCodes);
        log.info("PopCorporationServiceImpl.queryShopInfoShopCodes # shopInfoDTOS:{}", JSON.toJSONString(shopInfoDTOS));
        if (CollectionUtils.isEmpty(shopInfoDTOS)) {
            return ResponseVo.successResult(extVoPageInfo);
        }
        Map<String, Integer> shopInfoMap = shopInfoDTOS.stream().collect(Collectors.toMap(ShopInfoDTO::getShopCode, ShopInfoDTO::getStatus, (o, n) -> n));
        for (CorporationAndUserExtVo vo : corporationAndUserExtVos) {
            Integer status = shopInfoMap.get(vo.getShopCode());
            vo.setShopStatus(status);
            vo.setShopStatusStr(ShopStatusEnum.getShopStatusValue(status));
        }
        log.info("PopCorporationServiceImpl.queryShopInfoShopCodes # extVoPageInfo:{}", JSON.toJSONString(extVoPageInfo));
        return ResponseVo.successResult(extVoPageInfo);
    }

    @Override
    public ResponseVo<Boolean> updateShopStatusByShopCode(SysUser user, String shopCode, Integer status,String remark) throws ServiceException {
        List<ShopInfoDTO> infoDTOS = remoteService.queryInfoByShopCodes(Collections.singletonList(shopCode));
        if (CollectionUtils.isEmpty(infoDTOS)) {
            log.error("PopCorporationServiceImpl.updateShopStatusByShopCode.queryInfoByShopCodes#shopCode:{}", shopCode);
            return ResponseVo.errRest(ApiResultCodeEum.NETWORK_ERROR.getMsg());
        }
        ShopInfoDTO infoDTO = infoDTOS.get(0);
        log.info("PopCorporationServiceImpl.updateShopStatusByShopCode.queryInfoByShopCodes#shopCode:{},infoDTO:{}",
                shopCode, JSON.toJSONString(infoDTO));
        if (Objects.isNull(infoDTO.getStatus())) {
            return ResponseVo.errRest(ResultCodeEnum.SHOP_QUERY_ERROR.getMsg());
        }

        ApiRPCResult<CorporationDto> apiRPCResult = corporationAdminApi.queryCorporation(infoDTO.getOrgId());
        if (apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())){
            log.error("PopCorporationServiceImpl.updateShopStatusByShopCode.queryInfoByShopCodes#shopCode:{}", shopCode);
            return ResponseVo.errRest("查询不到商业信息");
        }
        CorporationDto corporationDto = apiRPCResult.getData();
        if (infoDTO.getStatus() == ShopStatusEnum.CLOSED.getCode()) {
            return ResponseVo.errRest(ResultCodeEnum.SHOP_CLOSED.getMsg());
        }

        if (infoDTO.getStatus() == status) {
            String value = ShopStatusEnum.getShopStatusValue(status);
            return ResponseVo.errRest(String.format(ResultCodeEnum.SHOP_STATUS_CHANGE_ERROR.getMsg(), value));
        }
        //店铺上线判断0价格商品数
        if(status==2){
            PopCorporationErrorPriceCountDto priceCountDto = corporationPriceTypeRemote.errorPriceCount(infoDTO.getOrgId());
            if(priceCountDto.getPriceType()==0){
                return ResponseVo.errRest("请先设置售价模式");
            }
            if(priceCountDto.getErrorCount()>0){
                return ResponseVo.errRest(priceCountDto.getPriceType()== CorporationPriceTypeEnum.SELLING_PRICE.getCode() ?"上线失败，销售中的商品单体采购价不能为空/0":"上线失败，销售中商品底价不能为空/0、销售中商品单体毛利率不能为空/0、销售中商品连锁毛利率不能为0");
            }
        }


        Boolean aBoolean = remoteService.updateShopStatusByShopCode(shopCode, status);
        if (aBoolean) {
            log.info("PopCorporationServiceImpl.updateShopStatusByShopCode.CookieUser#user:{}",
                    JSON.toJSONString(user));
            PopCheckLogDto dto = new PopCheckLogDto();
            dto.setTableId(corporationDto.getId());
            dto.setTableName("checkPopUpdateShopStatus");
            dto.setCreateId(Long.parseLong(user.getOaId()));
            dto.setCreateName(user.getUsername());
            dto.setState(status.byteValue());
            dto.setRemarks(remark);
            remoteService.savePopCheckLogDto(dto);
            String orgId = infoDTO.getOrgId();
            if (StringUtils.isBlank(orgId)) {
                ApiRPCResult<String> orgIdByShopCode = erpPopCorporationUtilApi.getOrgIdByShopCode(infoDTO.getShopCode());
                log.info("PopCorporationServiceImpl.updateShopStatusByShopCode orgIdByShopCode:{}", JSON.toJSONString(orgIdByShopCode));
                if (orgIdByShopCode.isSuccess() && StringUtils.isNotBlank(orgIdByShopCode.getData())) {
                    orgId = orgIdByShopCode.getData();
                }
            }
            updateOrgAccountStatus(orgId, status);

        }
        PopCorporationDto popCorporationDto = new PopCorporationDto();
        popCorporationDto.setShopStatus(status);
        popCorporationDto.setId(corporationDto.getId());
        corporationAdminApi.updateShopStatus(popCorporationDto);
        return ResponseVo.successResult(aBoolean);
    }

    private void updateOrgAccountStatus(String orgId, Integer status) throws ServiceException {
        if (ShopStatusEnum.CLOSED.getCode() != status){
            return;
        }
//        ApiRPCResult<PopSupplierUserDto> popSupplierUser = supplierUserApi.getPopSupplierUser(orgId);
//        if (popSupplierUser.isFail() || popSupplierUser.getData() == null) {
//            throw new ServiceException("根据OrgId查询商户信息失败");
//        }
//        PopSupplierUserDto data = popSupplierUser.getData();
        //更新用户账号状态
        SupplierUserDto userDto = new SupplierUserDto();
        userDto.setOrganizationId(orgId);
        if (ShopStatusEnum.CLOSED.getCode() == status) {
            userDto.setUserStatus(1);
        }
        //根据orgId更新用户状态，主账号和子账号都要停用掉
        ApiRPCResult<Boolean> result = supplierUserApi.updateUserStatusByOrgId(userDto);
        if (result == null || result.isFail()) {
            throw new ServiceException("停用商户账号失败");
        }
    }

    @Override
    public void setDrugAreaInfo(List<CorporationAndUserExtDto> extDtos) {
        if (CollectionUtils.isEmpty(extDtos)){
            return;
        }

        try {
            List<Long> ids = extDtos.stream().map(CorporationAndUserExtDto::getId).collect(Collectors.toList());
            List<String> orgIds = extDtos.stream().map(CorporationAndUserExtDto::getOrgId).collect(Collectors.toList());

            List<CorporationAreaInfoVo> areaInfoVos = corporationAreaService.getCorporationAreaByCids(ids);
            Map<String, List<BusinessCategoryCommissionVo>> commissionVoMap = commissionService.getCommissionsByorgIds(orgIds);

            Map<Long,List<CorporationAreaInfoVo>> areaInfoVoMap = Maps.newHashMap();

            if (CollectionUtils.isNotEmpty(areaInfoVos)){
                areaInfoVoMap  =  areaInfoVos.stream().collect(Collectors.toMap(CorporationAreaInfoVo::getCId,corporationAreaInfoVo -> {
                    List<CorporationAreaInfoVo> list = new ArrayList<>();
                    list.add(corporationAreaInfoVo);
                    return list;
                },(List<CorporationAreaInfoVo> value1,List<CorporationAreaInfoVo> value2) ->{
                    value1.addAll(value2);
                    return value1;
                }));
            }

            for (CorporationAndUserExtDto corporationAndUserExtDto:extDtos) {
                List<CorporationAreaInfoVo> areaVos = areaInfoVoMap.get(corporationAndUserExtDto.getId());
                List<CorporationAreaInfoVo> drugAreas = null;
                if (CollectionUtils.isNotEmpty(areaVos)) {
                    drugAreas = areaVos.stream().filter(item -> Objects.equals(CorporationAreaSaleTypeEnum.DRUG.getCode(),item.getSaleType())).collect(Collectors.toList());
                }
                StringBuffer drugAreaSb = new StringBuffer();
                if (CollectionUtils.isNotEmpty(drugAreas)){
                    for (CorporationAreaInfoVo  corporationAreaInfoVo:drugAreas) {
                        drugAreaSb.append(corporationAreaInfoVo.getProv()).append(",");
                    }
                }
                corporationAndUserExtDto.setDrugAreasStr(drugAreaSb.length() == 0 ? StringUtils.EMPTY:drugAreaSb.substring(0,drugAreaSb.length() -1));
                List<CorporationAreaInfoVo> nonDrugAreas = null;
                if (CollectionUtils.isNotEmpty(areaVos)) {
                    nonDrugAreas = areaVos.stream().filter(item -> Objects.equals(CorporationAreaSaleTypeEnum.NON_DRUG.getCode(),item.getSaleType())).collect(Collectors.toList());
                }
                StringBuffer nonDrugAreaSb = new StringBuffer();
                if (CollectionUtils.isNotEmpty(nonDrugAreas)){
                    for (CorporationAreaInfoVo  corporationAreaInfoVo:nonDrugAreas) {
                        nonDrugAreaSb.append(corporationAreaInfoVo.getProv()).append(",");
                    }
                }
                corporationAndUserExtDto.setNonDrugAreas(nonDrugAreaSb.length() == 0 ? StringUtils.EMPTY:nonDrugAreaSb.substring(0,nonDrugAreaSb.length() -1));
                List<BusinessCategoryCommissionVo> commissionVos1 = commissionVoMap.get(corporationAndUserExtDto.getOrgId());

                StringBuffer sb = new StringBuffer();
                if (CollectionUtils.isNotEmpty(commissionVos1)) {
                    for (BusinessCategoryCommissionVo businessCategoryCommissionVo : commissionVos1) {
                        sb.append(businessCategoryCommissionVo.getCategoryName()).append("：").append(businessCategoryCommissionVo.getCommissionRatio().setScale(2, BigDecimal.ROUND_HALF_UP)).append("%，");
                    }
                }
                corporationAndUserExtDto.setCommissionsStr(sb.length() == 0 ? StringUtils.EMPTY: sb.substring(0,sb.length() -1));
            }
        } catch (Exception e) {
            log.info("###商户认证管理 导出设置 可售范围 非可售范围 佣金比例错误",e);
        }
    }

    @Override
    public ResponseVo<Boolean> offlineShop(SysUser user,CorporationDto corporationDto) {
        String shopCode = corporationDto.getShopCode();
        String orgId = corporationDto.getOrgId();

        List<ShopInfoDTO> infoDTOS = remoteService.queryInfoByShopCodes(Collections.singletonList(shopCode));
        if (CollectionUtils.isEmpty(infoDTOS)) {
            log.error("PopCorporationServiceImpl.offlineShop.queryInfoByShopCodes#shopCode:{}", shopCode);
            return ResponseVo.errRest(ApiResultCodeEum.NETWORK_ERROR.getMsg());
        }
        ShopInfoDTO infoDTO = infoDTOS.get(0);
        log.info("PopCorporationServiceImpl.offlineShop.queryInfoByShopCodes#shopCode:{},infoDTO:{}",
                shopCode, JSON.toJSONString(infoDTO));
        if (Objects.isNull(infoDTO.getStatus())) {
            return ResponseVo.errRest(ResultCodeEnum.SHOP_QUERY_ERROR.getMsg());
        }
        Integer status = ShopStatusEnum.OFFLINE.getCode();
        //店铺设置为下线
        Boolean aBoolean = remoteService.updateShopStatusByShopCode(shopCode, status);

        if (!aBoolean){
            return ResponseVo.errRest("修改店铺状态失败");
        }
        if (aBoolean) {
            log.info("PopCorporationServiceImpl.offlineShop.CookieUser#user:{}",
                    JSON.toJSONString(user));
            PopCheckLogDto dto = new PopCheckLogDto();
            dto.setTableId(corporationDto.getId());
            dto.setTableName("checkPopUpdateShopStatus");
            dto.setCreateId(Long.parseLong(user.getOaId()));
            dto.setCreateName(user.getUsername());
            dto.setState(status.byteValue());
            dto.setRemarks(ShopStatusEnum.getShopStatusValue(status));
            remoteService.savePopCheckLogDto(dto);
            SupplierUserDto supplierUserDto = new SupplierUserDto();
            supplierUserDto.setOrganizationId(corporationDto.getOrgId());
            supplierUserDto.setUserStatus(0);
            ApiRPCResult<Boolean> result = supplierUserApi.updateUserStatusByOrgId(supplierUserDto);

        }

        PopCorporationDto popCorporationDto = new PopCorporationDto();
        popCorporationDto.setShopStatus(status);
        popCorporationDto.setId(corporationDto.getId());
        corporationAdminApi.updateShopStatus(popCorporationDto);
        return ResponseVo.successResult(Boolean.TRUE);
    }

    @Override
    public int updateBusinessAttribute(String orgId, Byte businessAttribute, Long updateId, String updateName) {
        return popCorporationRemoteAdapter.updateBusinessAttribute(orgId, businessAttribute, updateId, updateName);
    }

    @Override
    public void updateShopCategory(String orgId, Integer shopCategory, Long id, String user) {
        popCorporationRemoteAdapter.updateShopCategory(orgId, shopCategory, id, user);
    }

    @Override
    public void updateSettleCycle(String orgId, Integer settleCycle, Integer settleValue, String operator){
        corporationSettleCycleAdapter.updateSettleCycle(orgId, settleCycle, settleValue, operator);
    }
}
