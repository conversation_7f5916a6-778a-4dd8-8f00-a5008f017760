package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.address.api.BaseRegionBusinessApi;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.address.dto.XyyRegionParams;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.merchant.api.seller.CorporationAreaApi;
import com.xyy.ec.pop.server.api.merchant.api.seller.ShippingApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationAreaDto;
import com.xyy.ec.pop.server.api.merchant.dto.ShippingDto;
import com.xyy.ec.pop.server.api.product.dto.PopBusAreaDto;
import com.xyy.ec.pop.service.BranchService;
import com.xyy.ec.pop.vo.BranchVo;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping("/shipping")
public class ShippingController {

    @Reference
    private ShippingApi shippingApi;

    @Autowired
    private BranchService branchService;

    @Reference(version = "1.0.0")
    private CorporationAreaApi corporationAreaApi;

    @Reference(version = "1.0.0")
    private BaseRegionBusinessApi baseRegionBusinessApi;

    @ResponseBody
    @RequestMapping(value = "/v1/list")
    public Object selectShippingList(@RequestParam(value = "supplierId",required = false) Integer supplierId ,
                                     @RequestParam(value = "orgId",required = false) String orgId,
                                     @RequestParam(value = "pageNum",required = false) Integer pageNum,
                                     @RequestParam(value = "pageSize",required = false)Integer pageSize) {
        if (Objects.isNull(supplierId) || StringUtils.isEmpty(orgId)) {
            return ResponseVo.errRest("参数为空");
        }
        log.info("ShippingController.selectShippingList supplierId:{},orgId:{}", supplierId,orgId);
        try {
            List<BranchVo> branchVos = branchService.getAllBranchs();
            ShippingDto shippingDto = new ShippingDto();
            shippingDto.setSupplierId(supplierId);
            shippingDto.setOrgId(orgId);
            shippingDto.setPageNum(pageNum);
            shippingDto.setPageSize(pageSize);
            ApiRPCResult<PageInfo<ShippingDto>> apiRPCResult = shippingApi.getShippingList(shippingDto);
            if (apiRPCResult.isFail()) {
                return ResponseVo.errRest(apiRPCResult.getErrMsg());
            }
            return ResponseVo.successResult(convertPageShipping(apiRPCResult.getData(), branchVos));
        } catch (Exception e) {
            log.error("查询运费模板列表数据错误！", e);
            return ResponseVo.errRest("查询运费模板列表数据错误");
        }
    }

    private PageInfo<ShippingDto> convertPageShipping(PageInfo<ShippingDto> pageInfo,List<BranchVo> branchVos) {
        PageInfo<ShippingDto> shippingPage = new PageInfo<>();
        List<ShippingDto> list = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(pageInfo.getList()) && CollectionUtils.isNotEmpty(branchVos)) {
            Map<String,String> branchMap = branchVos.stream().collect(Collectors.toMap(BranchVo::getBranchCode, BranchVo::getBranchName));
            //组装branchName
            for (ShippingDto shippingDto : list) {
                List<String> branchName = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(shippingDto.getBranchCodes())) {
                    for (String branchCode : shippingDto.getBranchCodes()) {
                        branchName.add(branchMap.get(branchCode));
                    }
                    shippingDto.setBranchCodes(branchName);
                }
            }
        }
        shippingPage.setPageNum(pageInfo.getPageNum());
        shippingPage.setPageSize(pageInfo.getPageSize());
        shippingPage.setSize(pageInfo.getSize());
        shippingPage.setStartRow(pageInfo.getStartRow());
        shippingPage.setEndRow(pageInfo.getEndRow());
        shippingPage.setPages(pageInfo.getPages());
        shippingPage.setPrePage(pageInfo.getPrePage());
        shippingPage.setNextPage(pageInfo.getNextPage());
        shippingPage.setIsFirstPage(pageInfo.isIsFirstPage());
        shippingPage.setIsLastPage(pageInfo.isIsLastPage());
        shippingPage.setHasPreviousPage(pageInfo.isHasPreviousPage());
        shippingPage.setHasNextPage(pageInfo.isHasNextPage());
        shippingPage.setNavigateFirstPage(pageInfo.getNavigateFirstPage());
        shippingPage.setNavigateLastPage(pageInfo.getNavigateLastPage());
        shippingPage.setTotal(pageInfo.getTotal());
        shippingPage.setList(list);
        return shippingPage;
    }

    @GetMapping(value = "/getAreaCode")
    @ResponseBody
    public ResponseVo getAreaCode(@RequestParam(value = "supplierId",required = false) Integer supplierId){
        Set<PopBusAreaDto> busAreas = new LinkedHashSet<>();
        ApiRPCResult<List<CorporationAreaDto>> listApiRPCResult = corporationAreaApi.listAllCreatedCorporationArea(Long.valueOf(supplierId));
        if (!listApiRPCResult.isSuccess() || CollectionUtils.isEmpty(listApiRPCResult.getData())){
            return ResponseVo.errRest("查询企业经验地址失败");
        }
        Set<Integer> provCodes = listApiRPCResult.getData().stream()
                .filter(corporationAreaDto -> Objects.nonNull(corporationAreaDto) && !corporationAreaDto.isDeleted())
                .map(corporationAreaDto -> { return corporationAreaDto.getProvId().intValue();})
                .collect(Collectors.toSet());

        if(CollectionUtils.isNotEmpty(provCodes)){
            XyyRegionParams params = new XyyRegionParams();
            params.setAreaCodes(new ArrayList<>(provCodes));
            List<XyyRegionBusinessDto> provinces = baseRegionBusinessApi.queryRegion(params);
            this.build(provinces, busAreas);
        }
        List<Integer> cityCodes = new ArrayList<>(0);
        if(CollectionUtils.isNotEmpty(provCodes)){
            XyyRegionParams params = new XyyRegionParams();
            params.setAreaCodes(null);
            params.setParentCodes(new ArrayList<>(provCodes));
            List<XyyRegionBusinessDto> citys = baseRegionBusinessApi.queryRegion(params);
            this.build(citys, busAreas);
            cityCodes = citys.stream().map(XyyRegionBusinessDto::getAreaCode).collect(Collectors.toList());
        }

        if(CollectionUtils.isNotEmpty(cityCodes)){
            XyyRegionParams params = new XyyRegionParams();
            params.setParentCodes(cityCodes);
            List<XyyRegionBusinessDto> areas = baseRegionBusinessApi.queryRegion(params);
            this.build(areas, busAreas);

        }
        List<PopBusAreaDto> areaCode = new ArrayList<>();
        try {
            areaCode = buildAreaTree(new ArrayList<>(busAreas));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseVo.successResult(areaCode);
    }

    public List<PopBusAreaDto> buildAreaTree(List<PopBusAreaDto> areaDtos){
        List<PopBusAreaDto> provAreaDtos = areaDtos.stream().filter(popBusAreaDto -> Objects.nonNull(popBusAreaDto) && Objects.equals(popBusAreaDto.getAreaLevel(), 1)).collect(Collectors.toList());
        List<PopBusAreaDto> cityAreaDtos = areaDtos.stream().filter(popBusAreaDto -> Objects.nonNull(popBusAreaDto) && Objects.equals(popBusAreaDto.getAreaLevel(), 2)).collect(Collectors.toList());
        List<PopBusAreaDto> regionAreaDtos = areaDtos.stream().filter(popBusAreaDto -> Objects.nonNull(popBusAreaDto) && Objects.equals(popBusAreaDto.getAreaLevel(), 3)).collect(Collectors.toList());

        Map<Integer, List<PopBusAreaDto>> regionMap = regionAreaDtos.stream().collect(Collectors.groupingBy(PopBusAreaDto::getParentId));
        for (PopBusAreaDto popBusAreaDto:cityAreaDtos) {
            List<PopBusAreaDto> popBusAreaDtos = regionMap.get(popBusAreaDto.getAreaCode());
            if (CollectionUtils.isNotEmpty(popBusAreaDtos)) {
                popBusAreaDto.setChildren(popBusAreaDtos);
            }
        }

        Map<Integer, List<PopBusAreaDto>> cityMap = cityAreaDtos.stream().collect(Collectors.groupingBy(PopBusAreaDto::getParentId));
        for (PopBusAreaDto popBusAreaDto : provAreaDtos) {
            List<PopBusAreaDto> popBusAreaDtos = cityMap.get(popBusAreaDto.getAreaCode());
            if (CollectionUtils.isNotEmpty(popBusAreaDtos)) {
                popBusAreaDto.setChildren(popBusAreaDtos);
            }
        }
        return provAreaDtos;
    }

    private void build(List<XyyRegionBusinessDto> regions, Set<PopBusAreaDto> busAreas) {
        regions.stream().forEach(item -> {
            PopBusAreaDto popBusAreaDto = new PopBusAreaDto(item.getId().longValue(), item.getParentCode(), item.getLevel().intValue(), item.getAreaCode(), item.getAreaName(), null);
            busAreas.add(popBusAreaDto);
        });
    }
}
