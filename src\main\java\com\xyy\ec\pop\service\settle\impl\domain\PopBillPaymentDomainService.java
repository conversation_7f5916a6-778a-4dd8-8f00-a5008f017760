package com.xyy.ec.pop.service.settle.impl.domain;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.excel.entity.OfflinePopBillPaymentDetailExportVo;
import com.xyy.ec.pop.excel.entity.OnlinePayPopBillPaymentDetailExportVo;
import com.xyy.ec.pop.excel.style.RefundExcelExportStyler;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.helper.PopBillPaymentHelper;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.po.PopBillPaymentDetailPo;
import com.xyy.ec.pop.po.PopBillPaymentPo;
import com.xyy.ec.pop.remote.PopCorporationRemoteAdapter;
import com.xyy.ec.pop.server.api.Enum.ShareStatusEnum;
import com.xyy.ec.pop.server.api.seller.api.PopBillPaymentApi;
import com.xyy.ec.pop.server.api.seller.dto.PopBillPaymentDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCorporationDto;
import com.xyy.ec.pop.server.api.seller.dto.PopShareProfitDto;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.service.settle.PopBillPaymentDetailService;
import com.xyy.ec.pop.service.settle.PopBillPaymentService;
import com.xyy.ec.pop.utils.CollectionUtil;
import com.xyy.ec.pop.utils.EncodeUtil;
import com.xyy.ec.pop.vo.settle.PopBillPayStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillPayVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date  2020/12/1 14:15
* @table
*/
@Slf4j
@Service
public class PopBillPaymentDomainService {

    @Autowired
    private PopBillPaymentService popBillPaymentService;
    @Autowired
    private PopBillPaymentDetailService popBillPaymentDetailService;
    @Reference
    private PopBillPaymentApi popBillPaymentApi;

    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;

    public Long queryPopBillListCount(PopBillPayVo popBillPayVo){
       if (handleParam(popBillPayVo)){
           return 0L;
       }
       return popBillPaymentService.queryPopBillListCount(popBillPayVo);
    }

    private boolean handleParam(PopBillPayVo popBillPayVo) {
        if (StringUtils.isNotEmpty(popBillPayVo.getName()) || StringUtils.isNotEmpty(popBillPayVo.getOrgName())){
            List<String> orgIds = popCorporationRemoteAdapter.getOrgIdByName(popBillPayVo.getName(),popBillPayVo.getOrgName());
            popBillPayVo.setName(null);
            popBillPayVo.setOrgName(null);
            if (CollectionUtil.isEmpty(orgIds)){
                return true;
            }else{
                popBillPayVo.setOrgIds(orgIds);
            }
        }
        return false;
    }

    public PageInfo<PopBillPaymentPo> queryPopBillList(PopBillPayVo popBillPayVo, PageInfo pageInfo) {
        if (handleParam(popBillPayVo)){
            return new PageInfo<>();
        }
        List<PopBillPaymentPo> popBillPaymentPos;
        if (pageInfo != null) {
            int pageNum = ((pageInfo.getPageNum() == 0 ? 1 : pageInfo.getPageNum()) - 1) * pageInfo.getPageSize();
            popBillPaymentPos = popBillPaymentService.queryPopBillList(popBillPayVo, pageNum, pageInfo.getPageSize());
        }else {
            popBillPaymentPos = popBillPaymentService.queryPopBillList(popBillPayVo, null, null);
        }
        PageInfo<PopBillPaymentPo> billPaymentPoPageInfo = new PageInfo<>(popBillPaymentPos);
        List<String> orgIds = popBillPaymentPos.stream()
                .filter(Objects::nonNull)
                .map(PopBillPaymentPo::getOrgId)
                .collect(Collectors.toList());
        Map<String, PopCorporationDto> popCorporationDtoMap = popCorporationRemoteAdapter.queryPopCorporationDtoMap(orgIds);
        if (CollectionUtil.isNotEmpty(popCorporationDtoMap)){
            for (PopBillPaymentPo po:popBillPaymentPos) {
                PopCorporationDto popCorporationDto = popCorporationDtoMap.get(po.getOrgId());
                if (popCorporationDto != null) {
                    po.setName(popCorporationDto.getName());
                    po.setOrgName(popCorporationDto.getCompanyName());
                }
                if (StringUtils.isNotEmpty(po.getUpdaterCode())) {
                    po.setUpdaterName(po.getUpdaterCode() + "/" + po.getUpdaterName());
                }
            }
        }

        if (pageInfo != null) {
            billPaymentPoPageInfo.setPageSize(pageInfo.getPageSize());
            billPaymentPoPageInfo.setPageNum(pageInfo.getPageNum());
        }
        Long count = popBillPaymentService.queryPopBillListCount(popBillPayVo);
        billPaymentPoPageInfo.setTotal(count);

        if (pageInfo != null) {
            Long totalPageNum = (count + pageInfo.getPageSize() - 1) / pageInfo.getPageSize();
            billPaymentPoPageInfo.setPages(totalPageNum.intValue());
        }
        return billPaymentPoPageInfo;
    }

    public PageInfo<PopBillPaymentDetailPo> queryPopBillPaymentDetail(String flowNo, PageInfo pageInfos){
        PageInfo<PopBillPaymentDetailPo> pageInfo = new PageInfo<>();
        Page<PopBillPaymentDetailPo> page = PageMethod.offsetPage((pageInfos.getPageNum()-1)*pageInfos.getPageSize(),pageInfos.getPageSize());
        List<PopBillPaymentDetailPo> popBillPaymentDetailPos = popBillPaymentDetailService.queryPopBillPaymentDetail(PopBillPaymentDetailPo.builder().flowNo(flowNo).build(),pageInfos.getPageNum(),pageInfos.getPageSize());
        //获取入账单入账时间
        getPopBillPaymentTime(popBillPaymentDetailPos);

        if(CollectionUtils.isEmpty(popBillPaymentDetailPos)){
            pageInfo.setList(Collections.emptyList());
            pageInfo.setTotal(0);
            pageInfo.setPages(0);
            return pageInfo;
        }
        pageInfo.setPages(page.getPages());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setList(popBillPaymentDetailPos);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    private void getPopBillPaymentTime(List<PopBillPaymentDetailPo> popBillPaymentDetailPos) {
        if(CollectionUtils.isEmpty(popBillPaymentDetailPos)){
            return;
        }
        List<String> flowNoList = popBillPaymentDetailPos.stream().map(o -> o.getFlowNo()).distinct().collect(Collectors.toList());
        List<PopBillPaymentPo> popBillPaymentPoList = popBillPaymentService.queryPopBillPayByFlowNoList(flowNoList);
        if(CollectionUtil.isEmpty(popBillPaymentPoList)){
            return;
        }
        Map<String, PopBillPaymentPo> popBillPaymentPoMap = popBillPaymentPoList.stream().collect(Collectors.toMap(PopBillPaymentPo::getFlowNo, Function.identity()));
        popBillPaymentDetailPos.forEach(popBillPaymentDetailPo -> {
            //非月结和月结佣金字段合并
            if (popBillPaymentDetailPo.getSettlementType() == CommissionSettleTypeEnum.EVERY_MONTH.getCode()) {
                popBillPaymentDetailPo.setHireMoney(popBillPaymentDetailPo.getPayableCommission());
            }
            PopBillPaymentPo popBillPaymentPo = popBillPaymentPoMap.get(popBillPaymentDetailPo.getFlowNo());
            popBillPaymentDetailPo.setBillPaymentTime(null != popBillPaymentPo?popBillPaymentPo.getBillPaymentTime():null);
        });
    }

    public void exportOfflinePopBillPaymentDetail(PopBillPayVo popBillPayVo, HttpServletResponse res) throws IOException {
        if (handleParam(popBillPayVo)){
            return;
        }

        PopBillPaymentPo popBillPaymentPo = PopBillPaymentHelper.convertPopBillPay(popBillPayVo);
        List<String> flowNoList = popBillPaymentService.queryByOrderNos(popBillPaymentPo);
        if(CollectionUtils.isEmpty(flowNoList)){
            return;
        }
        Workbook workbook = null;
        ExportParams params = new ExportParams();
        params.setSheetName("入账单明细");
        params.setType(ExcelType.XSSF);
        params.setColor(IndexedColors.BLUE_GREY.index);
        params.setFreezeCol(2);
        params.setStyle(RefundExcelExportStyler.class);

        List<List<String>> pageList = Lists.partition(flowNoList, 200);
        for(List<String> flowNos: pageList){
            List<PopBillPaymentDetailPo> popBillPaymentDetailPos = popBillPaymentDetailService.queryPopBillPaymentDetailByFlowNoList(flowNos);
            if (CollectionUtil.isNotEmpty(popBillPaymentDetailPos)) {
                List<String> orgIds = popBillPaymentDetailPos.stream()
                        .filter(Objects::nonNull)
                        .map(PopBillPaymentDetailPo::getOrgId)
                        .collect(Collectors.toList());

                Map<String, PopCorporationDto> popCorporationDtoMap = popCorporationRemoteAdapter.queryPopCorporationDtoMap(orgIds);
                if (CollectionUtil.isNotEmpty(popCorporationDtoMap)){
                    for (PopBillPaymentDetailPo po:popBillPaymentDetailPos) {
                        PopCorporationDto popCorporationDto = popCorporationDtoMap.get(po.getOrgId());
                        if (popCorporationDto != null) {
                            po.setName(popCorporationDto.getName());
                        }
                    }
                }
            }
            List<OfflinePopBillPaymentDetailExportVo> offlinePopBillPaymentDetailExportVos = getOfflinePopBillPaymentDetailExportVo(popBillPaymentDetailPos);
            workbook = ExcelExportUtil.exportBigExcel(params, OfflinePopBillPaymentDetailExportVo.class,offlinePopBillPaymentDetailExportVos);
        }
        ExcelExportUtil.closeExportBigExcel();
        ServletOutputStream out = res.getOutputStream();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = URLEncoder.encode("入账单明细-", EncodeUtil.DEFAULT_URL_ENCODING) + sdf.format(new Date());	//对中文进行URL编码，防止乱码
        res.setHeader("Content-type", "application/x-xls; charset=" + EncodeUtil.DEFAULT_URL_ENCODING);
        res.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xlsx");
        res.setContentType("application/msexcel");
        if(null != workbook){
            workbook.write(out);
        }
    }

    private List<OfflinePopBillPaymentDetailExportVo> getOfflinePopBillPaymentDetailExportVo(List<PopBillPaymentDetailPo> popBillPaymentDetailPos) {
        if(CollectionUtils.isEmpty(popBillPaymentDetailPos)){
            return Lists.newArrayList();
        }
        List<String> flowNoList = popBillPaymentDetailPos.stream().map(o -> o.getFlowNo()).distinct().collect(Collectors.toList());
        List<PopBillPaymentPo> popBillPaymentPoList = popBillPaymentService.queryPopBillPayByFlowNoList(flowNoList);
        if(CollectionUtils.isEmpty(popBillPaymentPoList)){
            return Lists.newArrayList();
        }
        Map<String, PopBillPaymentPo> popBillPaymentPoMap = popBillPaymentPoList.stream().collect(Collectors.toMap(PopBillPaymentPo::getFlowNo, Function.identity()));
        return PopBillPaymentHelper.convertOfflinePopBillPaymentDetailExportVo(popBillPaymentPoMap,popBillPaymentDetailPos);
    }

    public Long queryExprotBillPaymentDetailCount(PopBillPayVo popBillPayVo) {
        if (handleParam(popBillPayVo)){
            return 0L;
        }
        Long popBillPaymenDetailtCount = 0L;
        PopBillPaymentPo popBillPaymentPo = PopBillPaymentHelper.convertPopBillPay(popBillPayVo);
        Long popBillPaymentCount = popBillPaymentService.queryPopBillListCount(popBillPayVo);
        if(popBillPaymentCount > 5000){
            return popBillPaymentCount;
        }
        List<String> flowNoList = popBillPaymentService.queryByOrderNos(popBillPaymentPo);
        if(CollectionUtils.isEmpty(flowNoList)){
            return popBillPaymenDetailtCount;
        }
        List<List<String>> partition = Lists.partition(flowNoList, 200);
        for(List<String> flowNos : partition){
            popBillPaymenDetailtCount+= popBillPaymentDetailService.queryPopBillPaymentDetailCount(flowNos);
        }
        return popBillPaymenDetailtCount;
    }

    public PopBillPayStatisVo queryPopBillPayStatis(PopBillPayVo popBillPayVo) {
        if (handleParam(popBillPayVo)){
            PopBillPayStatisVo popBillPayStatisVo = new PopBillPayStatisVo();
            return popBillPayStatisVo;
        }
        return popBillPaymentService.queryPopBillPayStatis(popBillPayVo);
    }

    public void updateBatchPopBillPay(List<PopBillPaymentPo> popBillPaymentPoList, Date remitTime, SysUser user) {
        popBillPaymentPoList.forEach(popBillPaymentPo->{
            popBillPaymentPo.setRemitStatus((byte) 1);

            popBillPaymentPo.setRemitTime(remitTime);
            popBillPaymentPo.setUpdaterCode(user.getEmail());
            popBillPaymentPo.setUpdaterName(user.getRealName());
        });
        List<PopBillPaymentDto> popBillPaymentDtoList = PopBillPaymentHelper.convertPopBillPaymentDto(popBillPaymentPoList);
        popBillPaymentApi.batchUpdatePopBillPaymentAndAccountSummary(popBillPaymentDtoList);
    }

    public void exportOnlineBillPaymemtDetailList(PopBillPayVo popBillPayVo, HttpServletResponse res)throws IOException {
        if (handleParam(popBillPayVo)){
            return;
        }
        List<String> flowNoList = popBillPaymentService.queryByOrderNos(PopBillPaymentHelper.convertPopBillPay(popBillPayVo));
        if(CollectionUtils.isEmpty(flowNoList)){
            return;
        }
        Workbook workbook = null;
        ExportParams params = new ExportParams();
        params.setSheetName("入账单明细");
        params.setType(ExcelType.XSSF);
        params.setColor(IndexedColors.BLUE_GREY.index);
        params.setFreezeCol(2);
        params.setStyle(RefundExcelExportStyler.class);

        List<List<String>> pageList = Lists.partition(flowNoList, 200);
        for(List<String> flowNos: pageList){
            List<PopBillPaymentDetailPo> popBillPaymentDetailPos = popBillPaymentDetailService.queryPopBillPaymentDetailByFlowNoList(flowNos);
            List<String> orgIds = popBillPaymentDetailPos.stream()
                    .filter(Objects::nonNull)
                    .map(PopBillPaymentDetailPo::getOrgId)
                    .collect(Collectors.toList());
            Map<String, PopCorporationDto> popCorporationDtoMap = popCorporationRemoteAdapter.queryPopCorporationDtoMap(orgIds);
            if (CollectionUtil.isNotEmpty(popCorporationDtoMap)){
                for (PopBillPaymentDetailPo po:popBillPaymentDetailPos) {
                    PopCorporationDto popCorporationDto = popCorporationDtoMap.get(po.getOrgId());
                    if (popCorporationDto != null) {
                        po.setName(popCorporationDto.getName());
                    }
                }
            }

            List<OnlinePayPopBillPaymentDetailExportVo> onlinePopBillPaymentDetailExportVos = getOnlinePopBillPaymentDetailExportVo(popBillPaymentDetailPos);
            workbook = ExcelExportUtil.exportBigExcel(params, OnlinePayPopBillPaymentDetailExportVo.class,onlinePopBillPaymentDetailExportVos);
        }
        ExcelExportUtil.closeExportBigExcel();
        ServletOutputStream out = res.getOutputStream();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = URLEncoder.encode("入账单明细-", EncodeUtil.DEFAULT_URL_ENCODING) + sdf.format(new Date());	//对中文进行URL编码，防止乱码
        res.setHeader("Content-type", "application/x-xls; charset=" + EncodeUtil.DEFAULT_URL_ENCODING);
        res.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xlsx");
        res.setContentType("application/msexcel");
        if(null != workbook){
            workbook.write(out);
        }
    }

    private List<OnlinePayPopBillPaymentDetailExportVo> getOnlinePopBillPaymentDetailExportVo(List<PopBillPaymentDetailPo> popBillPaymentDetailPos) {
        if(CollectionUtils.isEmpty(popBillPaymentDetailPos)){
            return Lists.newArrayList();
        }
        List<String> flowNoList = popBillPaymentDetailPos.stream().map(o -> o.getFlowNo()).distinct().collect(Collectors.toList());
        List<PopBillPaymentPo> popBillPaymentPoList = popBillPaymentService.queryPopBillPayByFlowNoList(flowNoList);
        if(CollectionUtils.isEmpty(popBillPaymentPoList)){
            return Lists.newArrayList();
        }
        Map<String, PopBillPaymentPo> popBillPaymentPoMap = popBillPaymentPoList.stream().collect(Collectors.toMap(PopBillPaymentPo::getFlowNo, Function.identity()));
        return PopBillPaymentHelper.convertOnlinePayPopBillPaymentDetailExportVo(popBillPaymentPoMap,popBillPaymentDetailPos);
    }

    public Integer queryBillPaymentCount() {
        PopBillPaymentDto paymentDto = new PopBillPaymentDto();
        paymentDto.setBillShareStatus(ShareStatusEnum.SHARE_FAIL.getCode());
        log.info("PopBillPaymentDomainService.queryBillPaymentCount param:{}", JSON.toJSONString(paymentDto));
        ApiRPCResult<Integer> result = popBillPaymentApi.queryBillPaymentCount(paymentDto);
        log.info("PopBillPaymentDomainService.queryBillPaymentCount param:{}, result:{}", JSON.toJSONString(paymentDto), JSON.toJSONString(result));
        return result.isSuccess() ? result.getData() : NumberUtils.INTEGER_ZERO;
    }

    public void againShareProfit(List<PopShareProfitDto> shareProfitDtos) {
        log.info("PopBillPaymentDomainService.againShareProfit shareProfitDtos:{}", JSON.toJSONString(shareProfitDtos));
        ApiRPCResult apiRPCResult = popBillPaymentApi.againShareProfit(shareProfitDtos);
        log.info("PopBillPaymentDomainService.againShareProfit shareProfitDtos:{},apiRPCResult:{}", JSON.toJSONString(shareProfitDtos), JSON.toJSONString(apiRPCResult));
        if (apiRPCResult == null || apiRPCResult.isFail()) {
            throw new ServiceRuntimeException(apiRPCResult == null ? "重新分润推送失败，请稍后重试" : apiRPCResult.getErrMsg());
        }
    }
}
