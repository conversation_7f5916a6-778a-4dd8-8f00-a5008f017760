package com.xyy.ec.pop.utils.rsa;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.io.Serializable;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.SecureRandom;
import java.security.interfaces.RSAPrivateKey;
import java.util.Arrays;

/**
 * RSA加密算法公钥
 * <AUTHOR>
 */
public class RSAPublicKey implements Serializable {

    private static final long serialVersionUID = -3114130608943953805L;
    private String modulus;
    private String exponent;

    /**
     * 构造方法
     */
    public RSAPublicKey() {

    }

    /**
     * 带参数的构造方法
     *
     * @param modulus
     * @param exponent
     */
    public RSAPublicKey(String modulus, String exponent) {

        this.modulus = modulus;
        this.exponent = exponent;
    }

    /**
     * @param exponent the exponent to set
     */
    public void setExponent(String exponent) {
        this.exponent = exponent;
    }

    /**
     * @return the exponent
     */
    public String getExponent() {
        return exponent;
    }

    /**
     * @param modulus the modulus to set
     */
    public void setModulus(String modulus) {
        this.modulus = modulus;
    }

    /**
     * @return the modulus
     */
    public String getModulus() {
        return modulus;
    }

    /**
     * RSA加密
     * <AUTHOR>
     */
    public static class RSASecurity {

        private static final Logger LOG = LoggerFactory.getLogger(RSASecurity.class);


        private static final BouncyCastleProvider provider = new BouncyCastleProvider();

        /**
         * generateKey密钥对生成
         *
         */
        public static KeyPair generateKey() throws Exception {

            KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
            // 返回生成指定算法的public/private 密钥对的KeyPairGenerator对象
            keyPairGen.initialize(1024, new SecureRandom());
            // 使用给定的随机源(和默认的参数集合)
            KeyPair keyPair = keyPairGen.generateKeyPair();// 生成密钥对
            return keyPair;
        }

        /**
         * 加密
         *
         */
        public static byte[] encrypt(java.security.interfaces.RSAPublicKey publicKey, byte[] data) {

            if (null != publicKey) {
                try {

                    Cipher cipher = Cipher.getInstance("RSA", provider);
                    cipher.init(Cipher.ENCRYPT_MODE, publicKey);
                    int blockSize = cipher.getBlockSize();
                    ByteArrayOutputStream bout = new ByteArrayOutputStream(64);
                    int j = 0;
                    while (data.length - j * blockSize > 0) {
                        bout.write(cipher.doFinal(data, j * blockSize, blockSize));
                        j++;
                    }
                    return bout.toByteArray();
                } catch (Exception e) {
                    //e.printStackTrace();
                    LOG.error("加密失败, err {} , detail is {}", e.getMessage(), e);
                }
            }
            return new byte[0];
        }

        /**
         * 解密
         *
         */
        public static byte[] decrypt(RSAPrivateKey privateKey, byte[] data) {

            if (null != privateKey) {
                try {

                    Cipher cipher = Cipher.getInstance("RSA", provider);
                    cipher.init(Cipher.DECRYPT_MODE, privateKey);
                    if (data.length > cipher.getBlockSize() && data[0] == 0) {

                        data = Arrays.copyOfRange(data, 1, data.length);
                    }
                    return cipher.doFinal(data);
                } catch (Exception e) {
                    //e.printStackTrace();
                    LOG.error("解密失败, err {} , detail is {}", e.getMessage(), e);
                }
            }
            return new byte[0];
        }

    }
}
