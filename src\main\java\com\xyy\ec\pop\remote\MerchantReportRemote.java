package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.report.MerchantReportApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantReportDetailsDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantReportRecordsDto;
import com.xyy.ec.merchant.bussiness.dto.ReportOperationLogDto;
import com.xyy.ec.merchant.bussiness.dto.report.MerchantReportAuditDto;
import com.xyy.ec.merchant.bussiness.dto.report.MerchantReportDetailQueryDto;
import com.xyy.ec.merchant.bussiness.dto.report.MerchantReportRecordQueryDto;
import com.xyy.ec.merchant.bussiness.dto.report.ReportOperationLogQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MerchantReportRemote {
    @Reference(version = "1.0.0", check = false)
    private MerchantReportApi merchantReportApi;

    /**
     * 分页查询
     *
     * @param merchantReportRecordQueryDto
     * @return
     */
    public PageInfo<MerchantReportRecordsDto> pageMerchantReportRecord(MerchantReportRecordQueryDto merchantReportRecordQueryDto) {
        try {
            ApiRPCResult<PageInfo<MerchantReportRecordsDto>> result = merchantReportApi.pageMerchantReportRecord(merchantReportRecordQueryDto);
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("分页查询举报记录异常，merchantReportRecordQueryDto={},e=", JSON.toJSONString(merchantReportRecordQueryDto), e);
        }
        return new PageInfo<>(Collections.emptyList());
    }

    public List<MerchantReportRecordsDto> queryReportRecords(MerchantReportRecordQueryDto merchantReportRecordQueryDto) {
        try {
            ApiRPCResult<List<MerchantReportRecordsDto>> result = merchantReportApi.selectMerchantReportRecords(merchantReportRecordQueryDto);
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("查询举报记录列表异常，merchantReportRecordQueryDto={},e=", JSON.toJSONString(merchantReportRecordQueryDto), e);
        }
        return null;
    }

    public Map<Long, MerchantReportRecordsDto> queryReportRecordMap(MerchantReportRecordQueryDto merchantReportRecordQueryDto) {
        List<MerchantReportRecordsDto> list = queryReportRecords(merchantReportRecordQueryDto);
        //转map
        // 将列表转换为 Map，使用 getId() 作为键
        return list.stream()
                .collect(Collectors.toMap(MerchantReportRecordsDto::getMerchantId, dto -> dto));
    }

    public PageInfo<MerchantReportDetailsDto> pageMerchantReportDetail(MerchantReportDetailQueryDto queryDto) {
        MerchantReportDetailQueryDto detailQueryDto = new MerchantReportDetailQueryDto();
        detailQueryDto.setPageNum(queryDto.getPageNum());
        detailQueryDto.setPageSize(queryDto.getPageSize());
        detailQueryDto.setMerchantId(queryDto.getMerchantId());
        ApiRPCResult<PageInfo<MerchantReportDetailsDto>> result = merchantReportApi.pageMerchantReportDetail(detailQueryDto);
        if (result.isSuccess()) {
            return result.getData();
        }
        log.error("分页查询举报明细异常，detailQueryDto={},e=", JSON.toJSONString(detailQueryDto));
        return new PageInfo<>(Collections.emptyList());
    }

    /**
     * 审核
     *
     * @param auditDto
     * @return
     */
    public boolean audit(MerchantReportAuditDto auditDto) {
        try {
            ApiRPCResult<Boolean> apiRPCResult = merchantReportApi.audit(auditDto);
            if (apiRPCResult.isSuccess()) {
                return apiRPCResult.getData();
            }
            log.info("审核返回异常，apiRPCResult={}", JSON.toJSONString(apiRPCResult));
        } catch (Exception e) {
            log.error("审核异常，auditDto={},e=", JSON.toJSONString(auditDto));

        }
        return false;
    }

    /**
     * 批量审核
     *
     * @param auditDtos
     * @return
     */
    public boolean batchAudit(List<MerchantReportAuditDto> auditDtos) {
        ApiRPCResult<Boolean> apiRPCResult = merchantReportApi.batchAudit(auditDtos);
        if (apiRPCResult.isSuccess()) {
            return apiRPCResult.getData();
        }
        log.info("批量审核返回异常，apiRPCResult={}", JSON.toJSONString(apiRPCResult));
        return false;
    }

    public PageInfo<ReportOperationLogDto> pageLogs(ReportOperationLogQueryDto queryDto) {
        try {
            ApiRPCResult<PageInfo<ReportOperationLogDto>> apiRPCResult = merchantReportApi.pageLogs(queryDto);
            if (apiRPCResult.isSuccess()) {
                return apiRPCResult.getData();
            }
        } catch (Exception e) {
            log.error("pageLogs异常，queryDto={}", JSON.toJSONString(queryDto));
        }
        return null;
    }


}
