package com.xyy.ec.pop.controller.mop;

import com.xyy.ec.pop.adapter.mop.BusinessOperatorLogAdapter;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.parameter.query.BusinessOperatorQueryParame;
import com.xyy.pop.mop.api.remote.result.BusinessOperatorLogBasicDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RequestMapping("/mop/business/log")
@RestController
public class BusinessOperatorLogController extends BaseController {

    @Resource
    private BusinessOperatorLogAdapter businessOperatorLogAdapter;

    @PostMapping("/list")
    public ResponseVo<List<BusinessOperatorLogBasicDTO>> list(@RequestBody BusinessOperatorQueryParame parame) {
        return businessOperatorLogAdapter.listBusinessOperatorLog(parame);
    }
}
