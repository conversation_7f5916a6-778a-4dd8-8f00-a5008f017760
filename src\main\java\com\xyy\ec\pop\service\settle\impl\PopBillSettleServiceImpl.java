package com.xyy.ec.pop.service.settle.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.qcloud.cos.exception.CosServiceException;
import com.xyy.ec.order.backend.pop.dto.PopOrderRefundDto;
import com.xyy.ec.pop.dao.PopBillSettleMapper;
import com.xyy.ec.pop.helper.PopBillSettleHelper;
import com.xyy.ec.pop.po.PopBillSettlePo;
import com.xyy.ec.pop.remote.BillSettleRemote;
import com.xyy.ec.pop.remote.EcOrderRemote;
import com.xyy.ec.pop.remote.PopCorporationRemoteAdapter;
import com.xyy.ec.pop.server.api.admin.dto.AdjustiveBillSettleDto;
import com.xyy.ec.pop.server.api.admin.dto.CommissionSettleSetDto;
import com.xyy.ec.pop.server.api.order.dto.PopBillSettleDto;
import com.xyy.ec.pop.server.api.seller.dto.PopBillSettleBatchDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCorporationDto;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.service.FastDfsUtilService;
import com.xyy.ec.pop.service.settle.PopBillSettleService;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.utils.cos.CosUploadPopConfig;
import com.xyy.ec.pop.utils.cos.CosUploadPopUtils;
import com.xyy.ec.pop.utils.excel.entity.PopBillSettleReasonVO;
import com.xyy.ec.pop.utils.fastdfs.FDFSClient;
import com.xyy.ec.pop.vo.AdjustiveBillSettleResultVo;
import com.xyy.ec.pop.vo.AdjustiveBillSettleVo;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.activity.UpdateBatchResultVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date  2020/12/1 10:52
* @table
*/
@Slf4j
@Service
public class PopBillSettleServiceImpl implements PopBillSettleService {
    @Autowired
    private PopBillSettleMapper popBillSettleMapper;

    @Autowired
    private FastDfsUtilService fastDfsUtilService;
    @Autowired
    private BillSettleRemote billSettleRemote;
    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;
    @Autowired
    private EcOrderRemote ecOrderRemote;

    @Autowired
    private CosUploadPopConfig cosUploadPopConfig;
    @Autowired
    private FDFSClient fdfsClient;


    @Value("${commission_calc_template_url}")
    public String calcPath;

    @Value("${commission_fail_template_url}")
    public String failPath;

    @Override
    public int insert(PopBillSettlePo record) {
        return popBillSettleMapper.insert(record);
    }

    @Override
    public int insertSelective(PopBillSettlePo record) {
        return popBillSettleMapper.insertSelective(record);
    }

    @Override
    public PopBillSettlePo selectByPrimaryKey(Long id) {
        return popBillSettleMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(PopBillSettlePo record) {
        return popBillSettleMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PopBillSettlePo record) {
        return popBillSettleMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<PopBillSettlePo> queryPopBillSettleList(PopBillSettleVo popBillSettleVo, Integer pageNum, Integer pageSize) {
        return popBillSettleMapper.queryPopBillSettleList(popBillSettleVo,pageNum,pageSize);
    }

    @Override
    public Long queryPopBillSettleListCount(PopBillSettleVo popBillSettleVo) {
        return popBillSettleMapper.queryPopBillSettleListCount(popBillSettleVo);
    }

    @Override
    public PopBillSettleStatisVo queryPopBillSettleStatis(PopBillSettleVo popBillSettleVo) {
        return popBillSettleMapper.queryPopBillSettleStatis(popBillSettleVo);
    }

    @Override
    public List<PopBillSettlePo> selectByBusinessNos(List<String> businessNos) {
        return popBillSettleMapper.selectByBusinessNos(businessNos);
    }

    @Override
    public AdjustiveBillSettleResultVo batchImportAdjustiveBillSettle(List<AdjustiveBillSettleVo> vos, String userName, Map<String, CommissionSettleSetDto> orgIdSettleSetMap) {
        //过滤可以更新的商品
        List<AdjustiveBillSettleVo> okVos = vos.stream().filter(item -> !item.isFailed()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(okVos)) {
            AdjustiveBillSettleResultVo resultVo = getAdjustiveBillSettleResultVo(vos.size(), vos);
            log.info("批量导入调账单,没有有效数据. resultVo:{}", JSON.toJSONString(resultVo));
            return resultVo;
        }
        List<PopBillSettleDto> billSettleDtos = PopBillSettleHelper.buildBillSettleDtos(okVos, userName, orgIdSettleSetMap);
        //导入调账单
        billSettleRemote.batchImportAdjustiveBillSettle(billSettleDtos);
        //错误信息写入文件
        List<AdjustiveBillSettleVo> errorsVos = vos.stream().filter(item -> item.isFailed()).collect(Collectors.toList());
        AdjustiveBillSettleResultVo resultVo = getAdjustiveBillSettleResultVo(vos.size(), errorsVos);
        log.info("批量导入调账单,更新结果:result:{}", JSON.toJSONString(resultVo));
        return resultVo;
    }

    @Override
    public void batchUpdateAdjustiveBillSettle(List<AdjustiveBillSettleDto> params) {
        billSettleRemote.batchUpdateAdjustiveBillSettle(params);
    }

    @Override
    public List<PopBillSettleDto> listImportAdjustiveBillSettle(String userName) {
        //查询结算单信息
        List<PopBillSettleDto> billSettleDtos = billSettleRemote.listImportAdjustiveBillSettle(userName);
        billSettleDtos.stream().forEach(m -> {
            if (m.getSettlementType() == CommissionSettleTypeEnum.EVERY_MONTH.getCode()) {
                //页面上佣金金额统一取hireMoney字段
                m.setHireMoney(m.getPayableCommission());
            }
        });

        List<String> orgIds = billSettleDtos.stream().map(PopBillSettleDto::getOrgId).collect(Collectors.toList());
        //查询商业信息
        Map<String, PopCorporationDto> popCorporationDtoMap = popCorporationRemoteAdapter.queryPopCorporationDtoMap(orgIds);
        if (MapUtils.isEmpty(popCorporationDtoMap)) {
            return billSettleDtos;
        }
        for (PopBillSettleDto dto : billSettleDtos) {
            PopCorporationDto popCorporationDto = popCorporationDtoMap.get(dto.getOrgId());
            if (popCorporationDto != null) {
                dto.setName(popCorporationDto.getName());
                dto.setOrgName(popCorporationDto.getCompanyName());
            }
        }
        return billSettleDtos;
    }

    @Override
    public Boolean deleteAdjustiveBillSettleByIds(List<String> ids, String userName) {
        return billSettleRemote.deleteAdjustiveBillSettleByIds(ids, userName);
    }

    /**
     * 记录失败文件
     * @param totalSize
     * @param errorVos
     * @return
     */
    private AdjustiveBillSettleResultVo getAdjustiveBillSettleResultVo(int totalSize, List<AdjustiveBillSettleVo> errorVos) {
        AdjustiveBillSettleResultVo resultVo = new AdjustiveBillSettleResultVo();
        resultVo.setErrorCount(errorVos.size());
        resultVo.setSuccessCount(totalSize - resultVo.getErrorCount());
        if (errorVos.size() == 0) {
            return resultVo;
        }
        String uuid = UUID.randomUUID().toString();
        resultVo.setErrorFileUrl(uuid);
        //将数据写入excel文件
        String fileUrl = fastDfsUtilService.writeDateToFastDfs(new ExportParams(null, "新增调账单模版-错误文件", ExcelType.XSSF), AdjustiveBillSettleVo.class, new ArrayList<>(errorVos));
        resultVo.setErrorFileUrl(fileUrl);
        return resultVo;
    }

    @Override
    public ResponseVo updateCommissionCalcFlag(PopBillSettleVo vo,String operator) {
        final PopBillSettlePo popBillSettlePo = popBillSettleMapper.selectByPrimaryKey(vo.getId());
        if (popBillSettlePo == null) {
            return ResponseVo.errRest("未查询到结算单");
        }
        if (popBillSettlePo.getBusinessType() == 2) {
            return ResponseVo.errRest("退款单不能单独设置是否参与佣金折扣,您可以修改它对应的正向单");
        }
        final Date orderPayTime = popBillSettlePo.getOrderPayTime();
        final Date endDayOfMonth = DateUtil.getEndDayOfMonth(orderPayTime);
        final Date now = new Date();
        if (now.after(endDayOfMonth)) {
            //拦截
            return ResponseVo.errRest("佣金折扣数据已生成无法进行修改");
        }
        List<String> businessNos = new ArrayList<>(16);
        businessNos.add(vo.getBusinessNo());
        final List<PopOrderRefundDto> popOrderRefundDtos = ecOrderRemote.queryRefundByOrderNo(vo.getBusinessNo());
        if (CollectionUtils.isNotEmpty(popOrderRefundDtos)) {
            final List<String> refundOrderNos = popOrderRefundDtos.stream().filter(k -> StringUtils.isNotBlank(k.getRefundOrderNo())).map(x -> x.getRefundOrderNo()).collect(Collectors.toList());
            businessNos.addAll(refundOrderNos);
        }
        Boolean res = billSettleRemote.updateCommissionCalcFlag(businessNos, vo.getCommissionCalcFlag(), operator);

        return ResponseVo.successResult(res);
    }

    @Override
    public ResponseVo<UpdateBatchResultVo> updateBatchCommissionCalcFlag(MultipartFile file, String userName) {
        List<PopBillSettleReasonVO> reasonList = new ArrayList<>();
        List<PopBillSettleBatchDto> batchList = new ArrayList<>();

        if (file == null || file.isEmpty()) {
            return ResponseVo.errRest("上传文件不能为空");
        }
        long size = file.getSize();
        if (size / (1024 * 1024) > 3) {
            return ResponseVo.errRest("单个文件限3M");
        }

        String relativePath = null;
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            int lastRowNum = sheet.getLastRowNum();
            if (lastRowNum > 1000) {
                return ResponseVo.errRest("最多可导入1000条数据");
            }
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    break;
                }

                Cell first = row.getCell(0);
                Cell second = row.getCell(1);
                if (first == null || second == null) {
                    break;
                }

                String businessNo = first.getStringCellValue();
                String commissionFlag = second.getStringCellValue();
                if (StringUtils.isNotBlank(businessNo) && StringUtils.isNotBlank(commissionFlag)) {
                    analyzingData(businessNo.trim(), commissionFlag.trim(), reasonList, batchList, userName);
                } else if (StringUtils.isBlank(businessNo) && StringUtils.isBlank(commissionFlag)) {
                    //遇到空行结束循环
                    break;
                } else {
                    generateReason(reasonList, businessNo, commissionFlag, userName);
                }
            }
            if (CollectionUtils.isNotEmpty(batchList)) {
                billSettleRemote.updateBatchCommissionCalcFlag(batchList);
            }

            //失败文件上传
            relativePath = uploadFailedExcel(reasonList);
        } catch (Exception e) {
            log.error("批量上传失败，操作人：{}", userName, e);
            return ResponseVo.errRest("批量上传失败");
        }
        return ResponseVo.successResult(new UpdateBatchResultVo(batchList.size(), reasonList.size(), relativePath));
    }

    public void analyzingData(String businessNo, String commissionFlag,
                              List<PopBillSettleReasonVO> reasonList, List<PopBillSettleBatchDto> batchList, String userName) {
        businessNo = businessNo.trim();
        commissionFlag = commissionFlag.trim();

        //失败数据
        PopBillSettleReasonVO popBillSettleReasonDto = verifyPopBillSettlePo(businessNo, commissionFlag, userName);
        if (popBillSettleReasonDto != null) {
            reasonList.add(popBillSettleReasonDto);
            return;
        }

        PopBillSettleBatchDto popBillSettleBatchVo = new PopBillSettleBatchDto();
        popBillSettleBatchVo.setBusinessNo(businessNo);
        popBillSettleBatchVo.setCommissionCalcFlag(Objects.equals(commissionFlag, "参与") ? 1 : 0);
        popBillSettleBatchVo.setUserName(userName);
        batchList.add(popBillSettleBatchVo);

        final List<PopOrderRefundDto> popOrderRefundDtos = ecOrderRemote.queryRefundByOrderNo(businessNo);
        if (CollectionUtils.isNotEmpty(popOrderRefundDtos)) {
            final List<String> refundOrderNos = popOrderRefundDtos.stream().filter(k -> StringUtils.isNotBlank(k.getRefundOrderNo())).map(x -> x.getRefundOrderNo()).collect(Collectors.toList());
            for (String no : refundOrderNos) {
                PopBillSettleBatchDto vo = new PopBillSettleBatchDto();
                vo.setBusinessNo(no);
                vo.setCommissionCalcFlag(Objects.equals(commissionFlag, "参与") ? 1 : 0);
                vo.setUserName(userName);
                batchList.add(vo);
            }
        }
    }

    public void generateReason(List<PopBillSettleReasonVO> reasonList, String businessNo, String commissionFlag, String userName) {
        PopBillSettleReasonVO reason = new PopBillSettleReasonVO();
        reason.setBusinessNo(businessNo);
        reason.setCommissionCalcFlag(commissionFlag);
        reason.setReason("存在未填写数据");
        reason.setUserName(userName);
        reasonList.add(reason);
    }


    public PopBillSettleReasonVO verifyPopBillSettlePo(String businessNo, String commissionFlag, String userName) {
        final PopBillSettlePo popBillSettlePo = popBillSettleMapper.selectByBusinessNo(businessNo);
        PopBillSettleReasonVO reason = null;
        if (popBillSettlePo == null) {
            reason = new PopBillSettleReasonVO(businessNo, commissionFlag, userName);
            reason.setReason("订单号不存在");
            return reason;
        }
        if (popBillSettlePo.getBusinessType() == 2) {
            reason = new PopBillSettleReasonVO(businessNo, commissionFlag, userName);
            reason.setReason("退款单不能单独设置是否参与佣金折扣,您可以修改它对应的正向单");
            return reason;
        }
        final Date orderPayTime = popBillSettlePo.getOrderPayTime();
        final Date endDayOfMonth = DateUtil.getEndDayOfMonth(orderPayTime);
        final Date now = new Date();
        if (now.after(endDayOfMonth)) {
            reason = new PopBillSettleReasonVO(businessNo, commissionFlag, userName);
            reason.setReason("佣金折扣数据已生成无法进行修改");
        }
        return reason;
    }

    public String uploadFailedExcel(List<PopBillSettleReasonVO> reasonList) throws IOException {
        String relativePath = null;

        if (CollectionUtils.isEmpty(reasonList)) {
            return relativePath;
        }
        byte[] download = null;
        try {
            download = CosUploadPopUtils.downloadFile(failPath);
        } catch (CosServiceException e) {
            log.error("文件不存在");
            return relativePath;
        }

        if (download == null) {
            log.error("文件模板下载失败");
            return relativePath;
        }
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             Workbook workbook = WorkbookFactory.create(new ByteArrayInputStream(download))) {
            Sheet sheet = workbook.getSheetAt(0);

            int rowNum = 0;
            for (PopBillSettleReasonVO reason : reasonList) {
                Row row = sheet.createRow(++rowNum);
                Cell cell0 = row.createCell(0);
                cell0.setCellValue(reason.getBusinessNo());
                Cell cell1 = row.createCell(1);
                cell1.setCellValue(reason.getCommissionCalcFlag());
                Cell cell2 = row.createCell(2);
                cell2.setCellValue(reason.getReason());
            }

            workbook.write(outputStream);
            byte[] content = outputStream.toByteArray();
            InputStream inputStream = new ByteArrayInputStream(content);
            String[] split = failPath.split("/");
            String fileName = split[split.length - 1];
            relativePath = CosUploadPopUtils.uploadAndGetRelativePath(inputStream, fileName);
            inputStream.close();
        } catch (Exception e) {
            log.error("文件上传失败");
        }

        return relativePath;
    }

    @Override
    public void downloadCommissionCalcFlag(HttpServletResponse response) {
        String fileName = "批量佣金折扣配置.xlsx";
        if (Objects.equals(cosUploadPopConfig.getSwitchFlag(), true)) {
            fdfsClient.download(response, calcPath, fileName);
        } else {
            try (OutputStream out = response.getOutputStream()) {
                response.reset();
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1));
                out.write(Objects.requireNonNull(CosUploadPopUtils.downloadFile(calcPath)));
            } catch (IOException e) {
                log.error("FileUploadController.downloadFromFastDfs#IO异常. path:{},fileName:{}", calcPath, fileName, e);
            }
        }
    }

}
