package com.xyy.ec.pop.controller;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.CommissionActivityRemote;
import com.xyy.ec.pop.remote.CommissionSettleSetRemote;
import com.xyy.ec.pop.server.api.admin.dto.*;
import com.xyy.ec.pop.server.api.seller.enums.ActivityStatusEnum;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.activity.ActivityQueryVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController()
@RequestMapping("/commissionActivity")
@Slf4j
@Api(tags = "佣金活动管理类")
public class CommissionActivityController extends BaseController {

    @Autowired
    private CommissionActivityRemote commissionActivityRemote;

    @Autowired
    private CommissionSettleSetRemote commissionSettleSetRemote;

    @GetMapping("/listByPage")
    public ResponseVo<PageInfo<CommissionActivityRespDto>> listByPage(ActivityQueryVo activityQueryVo) {
        try {
            activityQueryVo.setCreateUser(getSysUserName());
            return ResponseVo.successResult(commissionActivityRemote.listByPage(activityQueryVo));
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("CommissionActivityController.listByPage() 查询异常", e);
            return ResponseVo.errRest("查询佣金活动列表失败");
        }
    }

    @GetMapping("/businessTypeList")
    public ResponseVo<Map<Integer, String>> businessTypeList() {
        try {
            return ResponseVo.successResult(commissionActivityRemote.businessTypeList());
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("CommissionActivityController.businessTypeList() 查询异常", e);
            return ResponseVo.errRest("佣金活动-查询客户类型失败");
        }
    }

    @GetMapping("/listCorporationByPage")
    public ResponseVo<PageInfo<CommissionActivitySettleDto>> listCorporationByPage(CommissionActivitySettleDto commissionActivitySettleDto) {
        try {
            return ResponseVo.successResult(commissionSettleSetRemote.listCorporationByPage(commissionActivitySettleDto));
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("CommissionActivityController.listCorporationByPage() 查询异常", e);
            return ResponseVo.errRest("佣金活动-查询商业信息列表失败");
        }
    }

    @GetMapping("/listActivityMonthByPage")
    public ResponseVo<PageInfo<CommissionActivityMonthDto>> listActivityMonthByPage(Long activityId, Integer pageNum, Integer pageSize) {
        try {
            return ResponseVo.successResult(commissionActivityRemote.listActivityMonthByPage(activityId, pageNum, pageSize));
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("CommissionActivityController.listActivityMonthByPage() 查询异常", e);
            return ResponseVo.errRest("查看活动数据列表失败");
        }
    }

    @GetMapping("/listActivityLog")
    public ResponseVo<List<CommissionActivityLogDto>> listActivityLog(Long activityId) {
        try {
            return ResponseVo.successResult(commissionActivityRemote.listActivityLog(activityId));
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("CommissionActivityController.listActivityLog() 查询异常", e);
            return ResponseVo.errRest("查看活动日志列表失败");
        }
    }


    @PostMapping("/save")
    public ResponseVo save(@RequestBody CommissionActivityDto commissionActivityDto) {
        try {
            commissionActivityDto.setCreateUser(getUserName());
            commissionActivityRemote.save(commissionActivityDto);
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errCodeRest(e.getMessage());
        } catch (Exception e) {
            log.error("CommissionActivityController.save() 新增佣金活动数据操作异常", e);
            return ResponseVo.errRest("保存失败");
        }
        return ResponseVo.successResultNotData("保存成功");
    }

    @PostMapping("/update")
    public ResponseVo update(@RequestBody CommissionActivityDto commissionActivityDto) {
        try {
            commissionActivityDto.setUpdateUser(getUserName());
            commissionActivityDto.setCreateUser(getUserName());
            commissionActivityRemote.update(commissionActivityDto);
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errCodeRest(e.getMessage());
        } catch (Exception e) {
            log.error("CommissionActivityController.update() 编辑佣金活动数据操作异常", e);
            return ResponseVo.errRest("保存失败");
        }
        return ResponseVo.successResultNotData("保存成功");
    }

    @PostMapping("/updateOffline")
    public ResponseVo updateOffline(Long activityId) {
        try {
            commissionActivityRemote.updateOffline(activityId, getSysUserName());
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("CommissionActivityController.updateOffline() 活动下线操作异常", e);
            return ResponseVo.errRest("操作失败");
        }
        return ResponseVo.successResultNotData("操作成功");
    }

    @GetMapping("/popCommissionActivityJob")
    public ResponseVo popCommissionActivityJob() {
        LOGGER.info("PopCommissionActivityJob, started...");
        try {
            return ResponseVo.successResult(commissionActivityRemote.updateCommissionActivity(ActivityStatusEnum.NOT_STARTED.getState(), ActivityStatusEnum.IN_PROGRESS.getState()));
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errRest(e.getMessage());
        }
    }

}
