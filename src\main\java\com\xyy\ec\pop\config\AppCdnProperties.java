package com.xyy.ec.pop.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * 应用CDN属性
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties("xyy.cdn")
@Validated
@Getter
@Setter
public class AppCdnProperties {

    private String domain;

}
