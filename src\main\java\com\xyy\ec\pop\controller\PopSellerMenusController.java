package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.server.api.merchant.api.admin.PopSupplierMenuAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.PopSupplierMenuAdminQueryParam;
import com.xyy.ec.pop.server.api.merchant.dto.PopWhiteMenuDto;
import com.xyy.ec.pop.server.api.merchant.dto.WhiteMenuAdminQueryParam;
import com.xyy.ec.pop.server.api.seller.dto.PopSupplierMenuDto;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/popSellerMenus")
@Slf4j
public class PopSellerMenusController extends BaseController {

    @Reference(version = "1.0.0")
    private PopSupplierMenuAdminApi popSupplierMenuAdminApi;

    //查询菜单列表页
    @RequestMapping(value = "/queryListByPage",method = RequestMethod.POST)
    public Object queryListByPage(@RequestBody PopSupplierMenuAdminQueryParam popSupplierMenuAdminQueryParam){
        try {
            if (popSupplierMenuAdminQueryParam == null || popSupplierMenuAdminQueryParam.getPageNum() == null || popSupplierMenuAdminQueryParam.getPageSize() == null){
                return ResponseVo.errRest("分页不能为空");
            }
            ApiRPCResult<PageInfo<PopSupplierMenuDto>> apiRPCResult = popSupplierMenuAdminApi.queryListByPage(popSupplierMenuAdminQueryParam);
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail()){
                return ResponseVo.errRest("查询失败");
            }
            return ResponseVo.successResult(apiRPCResult.getData());
        } catch (Exception e) {
            log.error("PopSellerMenusController queryListByPage error,queryParamDto :{}", JSONObject.toJSONString(popSupplierMenuAdminQueryParam),e);
            return ResponseVo.errRest("系统错误");
        }
    }

    public static void main(String[] args) {
        PageInfo<PopWhiteMenuDto> pageInfo = new PageInfo();
        List<PopWhiteMenuDto> list = new ArrayList<>();
        PopWhiteMenuDto popSupplierMenuDto = new PopWhiteMenuDto();
        popSupplierMenuDto.setMenuId(10L);
        popSupplierMenuDto.setName("测试");
        popSupplierMenuDto.setOrgId("测试");
        popSupplierMenuDto.setName("测试");
        popSupplierMenuDto.setCompanyName("测试");
        popSupplierMenuDto.setName("测试");
        list.add(popSupplierMenuDto);

        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
        pageInfo.setList(list);
        pageInfo.setTotal(100L);
        ResponseVo<PageInfo> tResponseVo = ResponseVo.successResult(pageInfo);
        System.out.println(JSONObject.toJSONString(tResponseVo));
    }
    //查询菜单白名单列表
    @RequestMapping(value = "/queryWhiteMenuListByPage",method = RequestMethod.POST)
    public Object queryWhiteMenuListByPage(@RequestBody WhiteMenuAdminQueryParam whiteMenuAdminQueryParam){
        try {
            if (whiteMenuAdminQueryParam == null || whiteMenuAdminQueryParam.getPageNum() == null || whiteMenuAdminQueryParam.getPageSize() == null){
                return ResponseVo.errRest("分页不能为空");
            }
            ApiRPCResult<PageInfo<PopWhiteMenuDto>> apiRPCResult = popSupplierMenuAdminApi.queryWhiteMenuListByPage(whiteMenuAdminQueryParam);
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail()){
                return ResponseVo.errRest("查询失败");
            }
            return ResponseVo.successResult(apiRPCResult.getData());
        } catch (Exception e) {
            log.error("PopSellerMenusController queryWhiteMenuListByPage error,queryParamDto :{}", JSONObject.toJSONString(whiteMenuAdminQueryParam),e);
            return ResponseVo.errRest("系统错误");
        }
    }

    //新增白名单
    @RequestMapping(value = "/addWhiteMenu",method = RequestMethod.GET)
    public Object addWhiteMenu(@RequestParam(value = "orgIds",required = false) String orgIds,@RequestParam(value = "menuId",required = false)  Long menuId){
        try {
            if (StringUtils.isBlank(orgIds)){
                return ResponseVo.errRest("orgId不能为空");
            }
            if (Objects.isNull(menuId)){
                return ResponseVo.errRest("菜单ID不能为空");
            }

            String[] orgId = StringUtils.split(orgIds, ",");
            List<String> orgIdList = new ArrayList<>();
            Collections.addAll(orgIdList,orgId);
            ApiRPCResult<Boolean> apiRPCResult = popSupplierMenuAdminApi.addWhiteMenu(orgIdList, menuId,getUser().getRealName());
            if (Objects.isNull(apiRPCResult)){
                return ResponseVo.errRest("新增失败");
            }
            if (apiRPCResult.isFail()){
                ResponseVo.errRest(apiRPCResult.getErrMsg());
            }
            return ResponseVo.successResult("新增成功");
        } catch (Exception e) {
            log.error("PopSellerMenusController addWhiteMenu error,orgIds :{}  menuId：{}", JSONObject.toJSONString(orgIds),menuId,e);
            return ResponseVo.errRest("系统错误");
        }
    }
    //删除白名单
    @RequestMapping(value = "/deleteWhiteMenu",method = RequestMethod.GET)
    public Object deleteWhiteMenu(@RequestParam(value = "whiteMenuId",required = false)  Long whiteMenuId){
        try {
            if (Objects.isNull(whiteMenuId)){
                return ResponseVo.errRest("白名单ID不能为空");
            }

            ApiRPCResult<Boolean> apiRPCResult = popSupplierMenuAdminApi.deleteWhiteMenu(whiteMenuId);
            if (Objects.isNull(apiRPCResult)){
                return ResponseVo.errRest("删除失败");
            }
            if (apiRPCResult.isFail()){
                ResponseVo.errRest(apiRPCResult.getErrMsg());
            }
            return ResponseVo.successResult("删除成功");
        } catch (Exception e) {
            log.error("PopSellerMenusController deleteWhiteMenu error,whiteMenuId：{}", whiteMenuId,e);
            return ResponseVo.errRest("系统错误");
        }
    }

    //修改菜单白名单配置
    @RequestMapping(value = "/updateMenuHideStatus",method = RequestMethod.GET)
    public Object updateMenuHideStatus(@RequestParam(value = "menuId",required = false)  Long menuId,
                                       @RequestParam(value = "hideStatus",required = false) Integer hideStatus){
        try {
            if (Objects.isNull(menuId)){
                return ResponseVo.errRest("菜单ID不能为空");
            }

            if (Objects.isNull(hideStatus)){
                return ResponseVo.errRest("选项不能为空");
            }

            ApiRPCResult<Boolean> apiRPCResult = popSupplierMenuAdminApi.updateMenuHideStatus(menuId,hideStatus);
            if (Objects.isNull(apiRPCResult)){
                return ResponseVo.errRest("修改失败");
            }
            if (apiRPCResult.isFail()){
                ResponseVo.errRest(apiRPCResult.getErrMsg());
            }
            return ResponseVo.successResult("修改成功");
        } catch (Exception e) {
            log.error("PopSellerMenusController updateMenuHideStatus error,menuId：{}  hideStatus：{}", menuId,hideStatus,e);
            return ResponseVo.errRest("系统错误");
        }
    }

}
