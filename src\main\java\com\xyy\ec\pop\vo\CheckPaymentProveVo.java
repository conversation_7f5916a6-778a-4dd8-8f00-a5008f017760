package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CheckPaymentProveVo implements Serializable {
    private Long id;
    private Long cId;
    private Byte state;
    private String phone;
    private String merchantName;
    private String name;
    private String corporat;
    private String merchantNo;
    private BigDecimal money;
    private Integer transacIype;
    private Byte payWay;
    private String url;
    private Date createTime;
    private Date payDate;
    private String receiptUrl;
    private Long provId;
    private String prov;
}