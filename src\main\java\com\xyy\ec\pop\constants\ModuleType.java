package com.xyy.ec.pop.constants;


/**
 * 订单支付类型 (1:在线支付 2:货到付款)
 */
public class ModuleType {


    /** 会员管理区域 */

    public static final int CUSTOMER_MANAGEMENT_RESET = 1001;      // 密码重置

    public static final int CUSTOMER_MANAGEMENT_EXPORT = 1002;      // 导出会员信息

    public static final int CUSTOMER_MANAGEMENT_AUDIT = 1003;      // 商户审核

    public static final int CUSTOMER_MANAGEMENT_IMPORT = 1004;      // 导入表格的方式绑定药店和销售人员

    public static final int CUSTOMER_MANAGEMENT_FROZENORTHAW = 1005;      // 冻结解冻

    public static final int CUSTOMER_MANAGEMENT_UPDATEALLMERCHANTCACHE = 1006;      // 冻结解冻

    /** 订单管理区域 */

    public static final int ORDER_MANAGEMENT_UPDATE_NUM = 2001;  // 更新订单明细数量结果

    public static final int ORDER_MANAGEMENT_UPDATE_STATUS = 2002; //更新订单明细的操作结果

    public static final int ORDER_MANAGEMENT_SHIP = 2003; //订单发货

    public static final int ORDER_MANAGEMENT_FINISH = 2004; //订单完成

    public static final int ORDER_MANAGEMENT_EXPORT_DATA = 2005;//导出订单数据

    public static final int ORDER_MANAGEMENT_UPDATE_ORDER = 2006;//更新订单信息

    public static final int ORDER_MANAGEMENT_ADD_ORDER = 2007;// 后台新增订单

    public static final int ORDER_MANAGEMENT_CONFIRM_SEPARATE = 2008; //订单拆单

    public static final int ORDER_MANAGEMENT_SEPARATE_REDUCTION = 2009; //订单拆单还原

    public static final int ORDER_MANAGEMENT_CANCEL = 2010;//取消订单

    public static final int ORDER_MANAGEMENT_UPDATE_REMARK_DETAIL = 2011;//变更退款单拦截状态

    public static final int ORDER_MANAGEMENT_APPLY_CUSTOMER_SERVICE_INTERVENTION = 2012;//申请客服介入单

    public static final int ORDER_MANAGEMENT_CUSTOMER_SERVICE_REFUND_SAVE = 2013;//后台提交退款申请

    public static final int ORDER_MANAGEMENT_CLOSE_CUSTOMER_SERVICE_ORDER = 2014;//后台关闭客服介入单

    public static final int ORDER_MANAGEMENT_UPDATE_ORDER_URGENCY_INFO = 2015;//更新订单紧急状态和订单备注信息

    public static final int ORDER_MANAGEMENT_CHECK_PAY_ORDER = 2016;//后台确认收款

    public static final int ORDER_MANAGEMENT_CHECK_SERVED_OR_NO_SERVED = 2017;//后台确认送达/确认拒签

    public static final int ORDER_MANAGEMENT_EXPORT_REFUND_ORDER_DATA = 2018;//导出订单数据

    public static final int ORDER_MANAGEMENT_UPDATE_SHIPPING_REMARK = 2019;//更新gsp地址备注信息

    public static final int ORDER_MANAGEMENT_PASS_SHIPPING_REMARK = 2020;//确认通过gsp地址备注信息

    /** 商品管理区域 */

    public static final int PRODUCT_MANAGEMENT_SAVE_OR_UPDATE_CATEGORY_INFO = 3001; //保存更新商品分类信息

    public static final int PRODUCT_MANAGEMENT_SAVE_OR_REMOVE_CATEGORY_INFO = 3002; //删除商品分类信息

    public static final int PRODUCT_MANAGEMENT_SAVE_PRODUCT_INFO = 3003; //保存商品信息

    public static final int PRODUCT_MANAGEMENT_UPDATE_PRODUCT_INFO = 3004; //更新商品信息

    public static final int PRODUCT_MANAGEMENT_EXPORT = 3005;//导出商品信息
    
 

    public static final int PRODUCT_MANAGEMENT_DELETE_PRODUCT = 3006; //删除商品分类信息

    public static final int PRODUCT_MANAGEMENT_UPDATE_AND_DOWN = 3007; //更新商品信息
    
    public static final int PRODUCT_MANAGEMENT_IMPORT = 3008;//批量修改商品信息

    /** 控销管理区域 */
    public static final int PRODUCT_MANAGEMENT_EDIT_SALE_CATEGORY = 4001; //编辑控销分类信息






    /** 套餐相关 */
    public static final int ACTIVITY_PACKAGE_UPDATE_PACKAGEINFO= 5001; //修改套餐搭配信息


    /** 反馈相关 */
    public static final int APP_FEEDBACK_DELETE = 5101;// 删除反馈

    public static final int APP_FEEDBACK_UPDATE = 5102;// 编辑反馈

    /** App页面布局 */
    public static final int APP_LAYOUT_MANAGEMENT_UPDATE_APP_MODULE_CATEGORY_SORT = 5201;// 更新栏目分类信息

    public static final int APP_LAYOUT_MANAGEMENT_UPDATE_APP_MODULE = 5202;// 修改栏目

    public static final int APP_LAYOUT_MANAGEMENT_DELETE_APP_MODULE = 5203;// 删除栏目功能,用来删除布局界面里对应的布局项

    public static final int APP_LAYOUT_MANAGEMENT_UPDATE_APP_MODULE_CATEGORY = 5204;// 更新栏目分类

    public static final int APP_LAYOUT_MANAGEMENT_REMOVE_APP_MODULE_CATEGORY = 5205;// 删除栏目分类

    public static final int APP_LAYOUT_MANAGEMENT_CLIENT_MODULE_MANAGEMENT_EDIT = 5206;// 布局管理新增或修改

    public static final int APP_LAYOUT_MANAGEMENT_APP_ACTIVITY_UPDATE = 5207;// 新增或修改活动

    public static final int APP_LAYOUT_MANAGEMENT_PUSH_APP_ACTIVITY = 5208;// 布局版本发布

    public static final int APP_LAYOUT_MANAGEMENT_APP_ACTIVITY_GROUP_ROMOVE = 5209;// 删除活动分组

    public static final int APP_LAYOUT_MANAGEMENT_APP_ACTIVITY_GROUP_UPDATE = 5210;// 删除活动分组

    public static final int APP_LAYOUT_MANAGEMENT_COPY_ACTIVITY_MODULE = 5211;// 通过版本号新复制一套活动模板出来

    public static final int APP_LAYOUT_MANAGEMENT_COPY_HEADER_GUIDE = 5212; //通过页面编号复制一套PC导航

    public static final int APP_START_PAGE_UPDATE = 5301;// 修改app启动页

    public static final int CLIENT_PAGE_MANAGE_DELETE = 5401;// ClientPageManage删除

    public static final int CLIENT_PAGE_MANAGE_UPDATE = 5402;// ClientPageManage编辑

    public static final int REPORT_MANAGE_DELETE = 5403;// 报表模板删除

    public static final int REPORT_MANAGE_UPDATE = 5404;// 报表模板编辑

    /** 商品控销管理 */
    public static final int CONTROL_SALES_MANAGEMENT_UPDATE_CONTROLSALES = 5501;// 更新控销商品

    public static final int CONTROL_SALES_MANAGEMENT_DELETE_CONTROLSALES = 5502;// 删除控销商品

    public static final int CONTROL_SALES_MANAGEMENT_UPLOAD = 5503;// 上传会员信息

    public static final int CONTROL_SALES_MANAGEMENT_DELETE_CONTROL_SALES_MERCHANT_RELATION = 5504;// 移除控销会员关联商品

    public static final int CONTROL_SALES_MANAGEMENT_EXCHANGE_BUY_STATE = 5505;// 更新控销会员关联商品

    /** 展示类型 */
    public static final int EXHIBITION_TYPE_MANAGEMENT_SAVE = 5601;// 添加修改展示类型


    /** 商品易碎品管理 */
    public static final int FRAGILE_PRODUCT_MANAGEMENT_DELETE = 5701;// 商品易碎品删除

    /** 金币工厂 */
    public static final int GOLD_FACTORY_MANAGEMENT_SAVEORUPDATE = 5801;// 添加修改金币工厂

    public static final int GOLD_FACTORY_MANAGEMENT_DELETE = 5802;// 删除金币工厂


    /** 金币挖取页面 */
    public static final int GOLD_PAGE_MANAGEMENT_SAVEORUPDATE = 5901;// 添加修改金币挖取页面

    public static final int GOLD_PAGE_MANAGEMENT_DELETE = 5902;// 删除金币挖取页面


    /** 热搜管理 */
    public static final int HOT_SEARCH_MANAGEMENT_SAVEHOTSEARCH = 6001;// 保存修改热搜管理

    public static final int HOT_SEARCH_MANAGEMENT_DELETE = 6002;// 删除热搜管理


    /** 极光推送消息管理 */
    public static final int JPUSH_MANAGEMENT_SAVEORUPDATE = 6101; //保存或修改极光推送消息管理

    public static final int JPUSH_MANAGEMENT_DODELETE = 6102; // 删除极光推送消息管理


    /** 资质管理 */
    public static final int LICENSE_MANAGEMENT_CHECKLICENSEINFO = 6201; // 资质审核

    public static final int LICENSE_MANAGEMENT_SAVELICENSECATEGORY = 6202; // 保存资质分类信息

    public static final int LICENSE_MANAGEMENT_UPDATELICENSECATEGORY= 6203; // 更新资质分类信息


    /** 营销统计 */
    public static final int MARKETING_STATISTICS_DELETE = 6301; // 删除营销统计

    public static final int MARKETING_STATISTICS_UPDATE = 6302; // 编辑营销统计



    /** 公告管理 */
    public static final int NOTICE_MANAGEMENT_EDIT = 6401; // 公告编辑





    /** 订单轨迹 */
    public static final int ORDER_WORK_ITEM_DELETE = 6501; // 删除订单轨迹

    public static final int ORDER_WORK_ITEM_UPDATE = 6502; // 编辑订单轨迹





    /** 支付管理 */
    public static final int PAYMENT_MANAGEMENT_SAVPAYMENT = 6601; // 保存支付渠道

    public static final int PAYMENT_MANAGEMENT_REFUND = 6602; // 退款


    /** 商品提成 */
    public static final int PRODUCT_COMMISSION_DELETE = 6701; //删除商品提成

    public static final int PRODUCT_COMMISSION_UPDATE = 6702; //修改提成时间




    /** 优惠促销 */
    public static final int PROMO_MANAGEMENT_DELETE = 6801; //删除优惠促销

    public static final int PROMO_MANAGEMENT_VIEWANDUPDATE = 6802; //编辑优惠促销

    public static final int PROMO_MANAGEMENT_SAVEORUPDATE = 6803; //保存或更新优惠促销

    public static final int PROMO_MANAGEMENT_DELETE_PROMO_RULE = 6804; // 删除计划规则

    public static final int PROMO_MANAGEMENT_DELETE_PROMO_RULE_VOUCHER = 6805; // 删除优惠券



    /** 促销 */
    public static final int PROMOTION_MANAGEMENT_DELETE = 6901; // 删除促销
    public static final int PROMOTION_MANAGEMENT_SAVE_PROMOTION = 6902; // 修改促销保存


    /** 销售计划 */
    public static final int SECKILL_MANAGEMENT_DELETE = 6902; // 删除销售计划


    /** 任务调度 */
    public static final int SCHEDULE_TASK_DELETE = 7001; // 删除调度任务

    public static final int SCHEDULE_TASK_UPDATE = 7002; // 编辑调度任务



    /**秒杀活动*/
    public static final int SECKILL_MANAGEMENT_UPDATE_SECKILL_ACTIVITY = 7101; // 更新秒杀活动

    public static final int SECKILL_MANAGEMENT_SAVE = 7102; // 编辑秒杀活动分组相关信息



    /** 分组 */
    public static final int SYS_GROUP_MANAGEMENT_SAVEGROUP = 7201; //分组编辑

    public static final int SYS_GROUP_MANAGEMENT_DELETGROUP = 7202; //删除分组



    /** 权限 */
    public static final int SYS_SECURITY_MANAGEMENT_EDITRESOURCE = 7301; //跟新权限

    public static final int SYS_SECURITY_MANAGEMENT_SAVEEDITROLEINFO = 7302; //更新角色




    /** 系统管理 */
    public static final int SYSTEM_MANAGEMENT_CHANGEPASSWORD = 7401; // 修改密码

    public static final int SYSTEM_MANAGEMENT_EDITUSERINFO = 7402; // 修改员工信息

    public static final int SYSTEM_MANAGEMENT_DELETEUSER = 7403; // 删除员工信息

    public static final int SYSTEM_MANAGEMENT_MODIFITY_USER_STATUS = 7404; // 冻结员工信息

    public static final int SYSTEM_MANAGEMENT_EDIT_DEPARTINFO = 7405; // 修改部门信息

    public static final int SYSTEM_MANAGEMENT_DELETE_DEPART = 7406; // 删除部门信息

    public static final int SYSTEM_MANAGEMENT_CODEMAP_EDIT = 7407; // 新增或修改字典数据

    public static final int SYSTEM_MANAGEMENT_CODEITEM_EDIT = 7408; //新增或者修改数据字典项操作




    /** 券模板 */
    public static final int  VOUCHER_MANAGEMENT_UPDATE_VOUCHERTEMPLATE = 7501; //更新券模板信息

    public static final int  VOUCHER_MANAGEMENT_DELETE_VOUCHERTEMPLATE = 7502; //删除券模板(逻辑删除)

    public static final int  VOUCHER_MANAGEMENT_EXCLUDEPRODUCT = 7503; // 移除指定商品

    public static final int  VOUCHER_MANAGEMENT_EXCLUDE_MERCHANT = 7504; // 移除指定商户




    /** 优惠券红包雨 */
    public static final int  VOUCHER_REDBAG_RAIN_RECORD_DELETE = 7601; // 删除优惠券红包雨

    public static final int  VOUCHER_REDBAG_RAIN_RECORD_UPDATE = 7602; // 编辑优惠券红包雨

    
    /** 连锁计划单 */
    public static final int PLANNED_ORDER_MANAGEMENT_SAVEORUPDATE = 7701;	//保存或更新
    
    /** 其他管理功能 */
    public static final int JOB_MANAGEMENT_START = 9001; // 启动定时任务

    public static final int USER_LOGIN = 9002; // 用户登录

    public static final int IMPORT_PRODUCT = 9003; // 用户登录

    public static final int CLEAR_PRODUCT_PURCHASE_DATA = 9004; // 用户登录

    public static final int EXPORT_OTHER_DATA = 9005; // 导出报表数据
    
    public static final int ELECTRONIC_PLAN_ORDER_SKU_IMPORT = 10000;//批量导入电子计划商品信息
    
    public static final int ELECTRONIC_PLAN_ORDER_SKU_CHANGE_STATUS = 10001;//电子计划单推送

    public static final int ELECTRONIC_PLAN_ORDER_SKU_PUSH = 10002;//电子计划单保存并且推送

    
    public static final int ELECTRONIC_PLAN_ORDER_SKU_SAVE = 10003; //电子计划单保存
}
