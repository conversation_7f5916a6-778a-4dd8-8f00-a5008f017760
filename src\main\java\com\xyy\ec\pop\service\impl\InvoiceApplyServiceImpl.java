package com.xyy.ec.pop.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.helper.PopBillSettleHelper;
import com.xyy.ec.pop.server.api.order.dto.PopBillSettleDto;
import com.xyy.ec.pop.server.api.seller.api.PopCommissionInvoiceAdminApi;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleBillDto;
import com.xyy.ec.pop.server.api.seller.dto.PopInvoiceApplyDto;
import com.xyy.ec.pop.server.api.seller.dto.PopInvoiceApplyForExportDto;
import com.xyy.ec.pop.server.api.seller.param.PopInvoiceApplyAdminParam;
import com.xyy.ec.pop.service.FastDfsUtilService;
import com.xyy.ec.pop.service.InvoiceApplyService;
import com.xyy.ec.pop.vo.AdjustiveBillSettleResultVo;
import com.xyy.ec.pop.vo.AdjustiveBillSettleVo;
import com.xyy.ec.pop.vo.InvoiceApplyExportVo;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InvoiceApplyServiceImpl implements InvoiceApplyService {
    @Reference
    private PopCommissionInvoiceAdminApi popCommissionInvoiceAdminApi;

    @Autowired
    private FastDfsUtilService fastDfsUtilService;

    @Override
    public PageInfo<PopInvoiceApplyDto> queryInvoiceListForPage(PopInvoiceApplyAdminParam popInvoiceApplyAdminParam) {
        log.info("InvoiceApplyServiceImpl.queryInvoiceListForPage#popInvoiceApplyAdminParam:{}", JSON.toJSONString(popInvoiceApplyAdminParam));
        ApiRPCResult<PageInfo<PopInvoiceApplyDto>> pageInfoApiRPCResult = popCommissionInvoiceAdminApi.queryInvoiceApplyListForPage(popInvoiceApplyAdminParam);
        log.info("InvoiceApplyServiceImpl.queryInvoiceListForPage#popInvoiceApplyAdminParam:{},pageInfoApiRPCResult:{}", JSON.toJSONString(popInvoiceApplyAdminParam), JSON.toJSONString(pageInfoApiRPCResult));
        if (pageInfoApiRPCResult == null || pageInfoApiRPCResult.isFail()) {
            throw new XyyEcPopException(pageInfoApiRPCResult == null ? ApiResultCodeEum.QUERY_ERROR.getMsg() : pageInfoApiRPCResult.getErrMsg());
        }
        return pageInfoApiRPCResult.getData();
    }

    @Override
    public PopInvoiceApplyDto getInvoiceBaseInfo(String invoiceApplyNo) {
        log.info("InvoiceApplyServiceImpl.getInvoiceBaseInfo#invoiceApplyNo:{}", invoiceApplyNo);
        ApiRPCResult<PopInvoiceApplyDto> apiRPCResult = popCommissionInvoiceAdminApi.getInvoiceApplyBaseInfo(invoiceApplyNo);
        log.info("InvoiceApplyServiceImpl.getInvoiceBaseInfo#invoiceApplyNo:{},apiRPCResult:{}", invoiceApplyNo, JSON.toJSONString(apiRPCResult));
        if (apiRPCResult == null || apiRPCResult.isFail()) {
            throw new XyyEcPopException(apiRPCResult == null ? ApiResultCodeEum.QUERY_ERROR.getMsg() : apiRPCResult.getErrMsg());
        }
        return apiRPCResult.getData();
    }

    @Override
    public PageInfo<PopCommissionSettleBillDto> queryInvoiceDetailListForPage(String invoiceApplyNo, Integer pageNum, Integer pageSize) {
        log.info("InvoiceApplyServiceImpl.queryInvoiceDetailListForPage#invoiceApplyNo:{},pageNum:{},pageSize:{}", invoiceApplyNo, pageNum, pageSize);
        ApiRPCResult<PageInfo<PopCommissionSettleBillDto>> pageInfoApiRPCResult = popCommissionInvoiceAdminApi.queryInvoiceApplyDetailListForPage(invoiceApplyNo, pageNum, pageSize);
        log.info("InvoiceApplyServiceImpl.queryInvoiceDetailListForPage#invoiceApplyNo:{},pageNum:{},pageSize:{},pageInfoApiRPCResult:{}", invoiceApplyNo, pageNum, pageSize, JSON.toJSONString(pageInfoApiRPCResult));
        if (pageInfoApiRPCResult == null || pageInfoApiRPCResult.isFail()) {
            throw new XyyEcPopException(pageInfoApiRPCResult == null ? ApiResultCodeEum.QUERY_ERROR.getMsg() : pageInfoApiRPCResult.getErrMsg());
        }
        return pageInfoApiRPCResult.getData();
    }

    @Override
    public List<PopInvoiceApplyForExportDto> exportInvoiceList(PopInvoiceApplyAdminParam popInvoiceApplyAdminParam) {
        log.info("InvoiceApplyServiceImpl.exportInvoiceList#popInvoiceApplyAdminParam:{}", JSON.toJSONString(popInvoiceApplyAdminParam));
        ApiRPCResult<PageInfo<PopInvoiceApplyForExportDto>> pageInfoApiRPCResult = popCommissionInvoiceAdminApi.exportInvoiceApplyList(popInvoiceApplyAdminParam);
        log.info("InvoiceApplyServiceImpl.exportInvoiceList#popInvoiceApplyAdminParam:{},pageInfoApiRPCResult:{}", JSON.toJSONString(popInvoiceApplyAdminParam), JSON.toJSONString(pageInfoApiRPCResult));
        if (pageInfoApiRPCResult == null || pageInfoApiRPCResult.isFail()) {
            throw new XyyEcPopException(pageInfoApiRPCResult == null ? ApiResultCodeEum.QUERY_ERROR.getMsg() : pageInfoApiRPCResult.getErrMsg());
        }
        PageInfo<PopInvoiceApplyForExportDto> pageInfo = pageInfoApiRPCResult.getData();
        if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
            return Lists.newArrayList();
        }
        return pageInfo.getList();
    }

    @Override
    public int queryExportCount(PopInvoiceApplyAdminParam popInvoiceApplyAdminParam) {
        log.info("InvoiceApplyServiceImpl.queryExportCount#popInvoiceApplyAdminParam:{}", JSON.toJSONString(popInvoiceApplyAdminParam));
        ApiRPCResult<Integer> apiRPCResult = popCommissionInvoiceAdminApi.queryExportCount(popInvoiceApplyAdminParam);
        log.info("InvoiceApplyServiceImpl.queryExportCount#popInvoiceApplyAdminParam:{},apiRPCResult:{}", JSON.toJSONString(popInvoiceApplyAdminParam), JSON.toJSONString(apiRPCResult));
        if (apiRPCResult == null || apiRPCResult.isFail()) {
            throw new XyyEcPopException(apiRPCResult == null ? ApiResultCodeEum.QUERY_ERROR.getMsg() : apiRPCResult.getErrMsg());
        }
        return apiRPCResult.getData();
    }

    @Override
    public List<String> selectEffectiveInvoiceApplyNo(List<String> invoiceApplyNos) {
        ApiRPCResult<List<String>> apiRPCResult = popCommissionInvoiceAdminApi.selectEffectiveInvoiceApplyNo(invoiceApplyNos);
        log.info("InvoiceApplyServiceImpl.selectEffectiveInvoiceApplyNo#invoiceApplyNos:{},apiRPCResult:{}", JSON.toJSONString(invoiceApplyNos), JSON.toJSONString(apiRPCResult));
        if (apiRPCResult == null || apiRPCResult.isFail()) {
            throw new XyyEcPopException(apiRPCResult == null ? ApiResultCodeEum.QUERY_ERROR.getMsg() : apiRPCResult.getErrMsg());
        }
        return apiRPCResult.getData();
    }
    @Override
    public void batchUpdateTrackingNo(List<PopInvoiceApplyDto> popInvoiceApplyDtos) {
        ApiRPCResult<Boolean> apiRPCResult = popCommissionInvoiceAdminApi.batchUpdateTrackingNo(popInvoiceApplyDtos);
        if (apiRPCResult == null || apiRPCResult.isFail()) {
            throw new XyyEcPopException(apiRPCResult == null ? ApiResultCodeEum.QUERY_ERROR.getMsg() : apiRPCResult.getErrMsg());
        }
    }

    @Override
    public AdjustiveBillSettleResultVo batchImport(List<InvoiceApplyExportVo> vos) {
        //过滤可以更新的商品
        List<InvoiceApplyExportVo> okVos = vos.stream().filter(item -> !item.isFailed()).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(okVos)) {
            AdjustiveBillSettleResultVo resultVo = getAdjustiveBillSettleResultVo(vos.size(), vos);
            log.info("批量导入调账单,没有有效数据. resultVo:{}", JSON.toJSONString(resultVo));
            return resultVo;
        }
        List<PopInvoiceApplyDto> list = new ArrayList<>();
        for (InvoiceApplyExportVo invoiceApplyExportVo : okVos){
            PopInvoiceApplyDto popInvoiceApplyDto = new PopInvoiceApplyDto();
            popInvoiceApplyDto.setInvoiceApplyNo(invoiceApplyExportVo.getInvoiceApplyNo());
            popInvoiceApplyDto.setTrackingNo(invoiceApplyExportVo.getTrackingNo());
            popInvoiceApplyDto.setLogisticsCompanyName(invoiceApplyExportVo.getLogisticsCompanyName());
            list.add(popInvoiceApplyDto);
        }
        //导入调账单
        batchUpdateTrackingNo(list);
        //错误信息写入文件
        List<InvoiceApplyExportVo> errorsVos = vos.stream().filter(item -> item.isFailed()).collect(Collectors.toList());
        AdjustiveBillSettleResultVo resultVo = getAdjustiveBillSettleResultVo(vos.size(), errorsVos);
        log.info("批量导入调账单,更新结果:result:{}", JSON.toJSONString(resultVo));
        return resultVo;
    }

    /**
     * 记录失败文件
     * @param totalSize
     * @param errorVos
     * @return
     */
    private AdjustiveBillSettleResultVo getAdjustiveBillSettleResultVo(int totalSize, List<InvoiceApplyExportVo> errorVos) {
        AdjustiveBillSettleResultVo resultVo = new AdjustiveBillSettleResultVo();
        resultVo.setErrorCount(errorVos.size());
        resultVo.setSuccessCount(totalSize - resultVo.getErrorCount());
        if (errorVos.size() == 0) {
            return resultVo;
        }
        String uuid = UUID.randomUUID().toString();
        resultVo.setErrorFileUrl(uuid);
        //将数据写入excel文件
        String fileUrl = fastDfsUtilService.writeDateToFastDfs(new ExportParams(null, "佣金发票物流信息上传模版_错误文件", ExcelType.XSSF), InvoiceApplyExportVo.class, new ArrayList<>(errorVos));
        resultVo.setErrorFileUrl(fileUrl);
        return resultVo;
    }
}
