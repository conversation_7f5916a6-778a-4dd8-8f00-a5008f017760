package com.xyy.ec.pop.service;

import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.login.Menu;
import com.xyy.ec.pop.vo.login.UserInfo;
import com.xyy.ec.pop.vo.zhongtai.PopProvinceVo;
import com.xyy.ec.pop.vo.zhongtai.ZhongTaiProvinceVo;
import com.xyy.ec.pop.vo.zhongtai.ZhongTaiResource;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: LoginService
 * @Package
 * @Description: 对接中台登录
 * @date 2020/5/28
 */
public interface SsoService {
    /**
     * 获取用户token
     * @param username 用户名
     * @param password 密码
     * @return
     */
    ResponseVo<Object> getToken(String username, String password);
    /**
     * 用户登录（中台）
     * @param username 用户名
     * @param password 密码
     * @return
     */
    ResponseVo<UserInfo> login(String username, String password);
    /**
     * 用户退出（中台）
     * @param token token
     * @return
     */
    ResponseVo<Object> logout(String token);

    /**
     * 根据oaid获取权限菜单
     * @param oaId 中台返回的oaid
     * @param token token对应中台返回的TGC
     * @return
     */
    List<Menu> getUserMenuByOaId(String oaId, String token);

    /**
     * 获取用户信息
     * @param username 用户登录名
     * @param token token
     * @return 用户信息
     */
    ResponseVo<UserInfo> getUser(String username, String token);

    /**
     * 获取用户省份权限
     * @param token 用户的token
     * @return
     */
    List<PopProvinceVo> getProvinces(String token);

    /**
     * 获取用户菜单按钮权限
     * @param token 用户的token
     * @return
     */
    List<ZhongTaiResource> initButtons(String token);
}
