package com.xyy.ec.pop.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel("商品vo实体")
@Data
public class ProductVo implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("关键字")
    private String keyword;
    /**
     * 商品编码
     */
    private String barcode;
    /**
     * 原商品编码
     */
    private String originalBarcode;
    /**
     * ec商品id
     */
    private Long csuid;
    /**
     * 商品名称
     */
    private String showName;
    /**
     * 生产厂家
     */
    private String manufacturer;

    private String shopCode;

    /**
     * 商户名称
     */
    @ApiModelProperty("商户名称")
    private String companyName;
    @ApiModelProperty("店铺名称")
    private String name;
    @ApiModelProperty("商品状态")
    private Integer status;
    @ApiModelProperty("创建开始时间")
    private String createBeginTime;
    @ApiModelProperty("创建结束时间")
    private String createEndTime;

    //0：全部1待审核2审核通过3审核不通过
    @ApiModelProperty("审核状态")
    private Integer auditingState;
    //批准文号
    private String approvalNumber;
    //标准库ID
    private String standardProductId;

    //第三方商品标志
    @ApiModelProperty("第三方商品标志")
    private Integer isThirdCompany;
    private Integer pageSize;
    private Integer PageNum;
    //排序字段
    private String property;
    //排序类型
    private String direction;

    //商品来源 1 商家自建 2 商品库 空全部
    private Byte source;

    //纠错审核状态 2通过 3驳回 空全部
    private Integer authReason;
    /**
     * 是否已上报中台 0 否 1 是
     */
    private Boolean reportToStandard;
    /**
     * 库存状态 1、有库存 2、已售罄
     */
    private Integer stockStatus;

    private Long provId;

    private String branchCode;

    private List<Long> provIds;

    private List<String> branchCodes;


    private String highGross;
    /**
     * 商品类型 0-普通商品 1-活动商品
     */
    private Integer activityType;

    /**
     * 是否停用:0-否、1-是
     */
    private Integer disable;

    /**
     * 是否有追溯码(查询追溯码增加了 2和null,实际库里只有null,0,1): 0-否,1-是,2-未录入,null-全部
     */
    private Integer tracingCode;
}
