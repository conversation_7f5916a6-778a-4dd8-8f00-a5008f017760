package com.xyy.ec.pop.dto;

import lombok.Data;

import java.util.Date;

/**
 * 商家扣罚dto
 *
 * <AUTHOR>
 * @date 2024/9/2 09:42
 */
@Data
public class PopMerchantCommercialPenaltyQueryDto {
    //扣罚日期 开始
    private Date startTime;
    //扣罚日期 结束
    private Date endTime;

    private String orderNo;
    //商户（商户编码/商户名称/店铺名称）
    private String businessSearch;
    //商户联系电话
    private String merchantPhone;
    //商户注册省份
    private Long merchantProvId;
    // 当前页
    private Integer pageNum;
    // 每页条数
    private Integer pageSize;
}
