package com.xyy.ec.pop.vo.corporation;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CorporationQualificationVo implements Serializable {
    private Long id;

    /**
     * 企业主表ID
     */
    private Long cId;

    /**
     * 资质证书名称
     */
    private String name;

    /**
     * 资质证书号
     */
    private String code;

    /**
     * 资质起始日期
     */
    private Date startDate;

    /**
     * 资质终止日期
     */
    private Date endDate;

    /**
     * 证书文件URL
     */
    private String url;
    /**
     * 法人姓名
     */
    private String legalPersonName;

    private String bankName;

    private String subBankName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 状态（1-待审核；2-审核不通过；3-审核通过；4-已过期；）
     */
    private Byte state;

    /**
     * 逻辑删除：0-删除
     */
    private Byte del;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改人姓名
     */
    private String updateName;

    /**
     * 资质明细Id
     */
    private Long qualificationsDetailId;
    /**
     * 是否长期
     */
    private Integer longTerm ;
}
