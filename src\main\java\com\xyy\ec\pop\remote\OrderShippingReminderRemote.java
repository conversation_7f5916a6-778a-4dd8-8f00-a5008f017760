package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.exception.XyyEcOrderBizNoneCheckRTException;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.backend.shippingReminder.api.OrderShippingReminderBackendApi;
import com.xyy.ec.order.backend.shippingReminder.dto.OrderShippingReminderDto;
import com.xyy.ec.order.backend.shippingReminder.dto.OrderShippingReminderHistoryBackendDto;
import com.xyy.ec.order.backend.shippingReminder.params.OrderShippingReminderAuditBatchParams;
import com.xyy.ec.order.backend.shippingReminder.params.OrderShippingReminderAuditParams;
import com.xyy.ec.order.backend.shippingReminder.params.OrderShippingReminderParams;
import com.xyy.ec.order.business.api.OrderShippingReminderApi;
import com.xyy.ec.order.business.dto.shippingReminder.*;
import com.xyy.ec.order.business.enums.orderShippingReminder.OrderShippingReminderEvent;
import com.xyy.ec.pop.vo.OrderShippingReminderAuditBatchVo;
import com.xyy.ec.pop.vo.OrderShippingReminderVo;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 订单催发货远程服务类，用于调用订单催发货相关的远程接口。
 *
 * <AUTHOR>
 * @date 2024/10/16 19:53
 */
@Slf4j
@Component
public class OrderShippingReminderRemote {

    /**
     * 订单催发货后台API接口引用。
     */
    @Reference
    private OrderShippingReminderBackendApi orderShippingReminderBackendApi;

    /**
     * 订单催发货后台API接口引用。
     */
    @Reference
    private OrderShippingReminderApi orderShippingReminderApi;

    /**
     * 分页查询催发货提醒列表。
     *
     * @param params 查询参数。
     * @return 返回分页的催发货提醒列表。
     */
    public PageInfo<OrderShippingReminderDto> queryShippingRemindersPage(OrderShippingReminderParams params) {
        try {
            ApiRPCResult<PageInfo<OrderShippingReminderDto>> result = orderShippingReminderBackendApi.queryOrderShippingReminderList(params);
            if (result == null || result.isFail()) {
                log.error("OrderShippingReminderRemote.queryShippingRemindersPage: 查询失败，params: {}", params);
                return null;
            }
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("OrderShippingReminderRemote.queryShippingRemindersPage: 异常，params: {}", params, e);
        }
        return null;
    }

    /**
     * 查询催发货状态枚举
     * @return
     */
    public List<Map<String, String>> queryStatusList() {
        try {
            ApiRPCResult<List<Map<String, String>>> eventStatusMap = orderShippingReminderBackendApi.getEventStatusMap();

            if (eventStatusMap == null || eventStatusMap.isFail()) {
                log.error("OrderShippingReminderRemote.getEventStatusMap: 查询失败");
                return null;
            }
            if (eventStatusMap.isSuccess()) {
                return eventStatusMap.getData();
            }
        } catch (Exception e) {
            log.error("OrderShippingReminderRemote.getEventStatusMap: 异常", e);
        }
        return null;
    }

    /**
     * 加载催发货提醒的状态计数。
     *
     * @param params 查询参数。
     * @return 返回状态计数的列表，每个元素是一个包含状态和计数的映射。
     */
    public List<Map<String, Number>> loadShippingReminderStatusCount(OrderShippingReminderParams params) {
        try {
            ApiRPCResult<List<Map<String, Number>>> result = orderShippingReminderBackendApi.loadShippingReminderStatusCount(params);
            if (result == null || result.isFail()) {
                log.error("OrderShippingReminderRemote.queryShippingRemindersPage: 查询失败，params: {}", params);
                return null;
            }
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("OrderShippingReminderRemote.queryShippingRemindersPage: 异常，params: {}", params, e);
        }
        return null;
    }

    /**
     * 查询催发货进度
     * @param reminderId 催发货单id
     * @return 催发货进度信息
     */
    public OrderShippingReminderHistoryBackendDto queryHistory(Long reminderId) {
        try {
            OrderShippingReminderHistoryBackendDto reminderHistoryVo = new OrderShippingReminderHistoryBackendDto();
            ApiRPCResult<OrderShippingReminderHistoryBackendDto> orderShippingReminderHistoryBackendDtoApiRPCResult = orderShippingReminderBackendApi.queryOrderHistory(reminderId);
            if (orderShippingReminderHistoryBackendDtoApiRPCResult.isSuccess()) {
                reminderHistoryVo = orderShippingReminderHistoryBackendDtoApiRPCResult.getData();
            }
            return reminderHistoryVo;
        } catch (Exception e) {
            log.error("调用orderShippingReminderApi查询催发货历史失败", e);
            throw new RuntimeException("查询催发货历史失败");
        }
    }

    /**
     * 催发货审核详情
     * @param reminderId 催发货单id
     * @return 催发货进度信息
     */
    public OrderShippingReminderHistoryBackendDto.CustomFields queryAuditDetail(Long reminderId) {
        try {
            OrderShippingReminderHistoryBackendDto.CustomFields result=new OrderShippingReminderHistoryBackendDto.CustomFields();
            OrderShippingReminderHistoryBackendDto reminderHistoryVo = new OrderShippingReminderHistoryBackendDto();
            ApiRPCResult<OrderShippingReminderHistoryBackendDto> orderShippingReminderHistoryBackendDtoApiRPCResult = orderShippingReminderBackendApi.queryOrderHistory(reminderId);
            if (orderShippingReminderHistoryBackendDtoApiRPCResult.isSuccess()) {
                reminderHistoryVo = orderShippingReminderHistoryBackendDtoApiRPCResult.getData();
                List<OrderShippingReminderHistoryBackendDto.ShippingReminderHistory> shippingReminderHistory = reminderHistoryVo.shippingReminderHistory;
                for (OrderShippingReminderHistoryBackendDto.ShippingReminderHistory history : shippingReminderHistory) {
                    if (OrderShippingReminderEvent.MERCHANT_APPEAL.getEventCode().equals(history.getEventStatus())) {
                        result.setAppealCategory(history.getCustomFields().getAppealCategory());
                        result.setPrompt(history.getCustomFields().getPrompt());
                        result.setAppealEvidence(history.getCustomFields().getAppealEvidence());
                        result.setAppealDescription(history.getCustomFields().getAppealDescription());
                        break;
                    }
                }
            }
            return result;
        } catch (Exception e) {
            log.error("调用orderShippingReminderApi查询催发货历史失败", e);
            throw new RuntimeException("查询催发货历史失败");
        }
    }

    /**
     * 催发货单审核
     * @param orderShippingReminderAuditParams
     * @return
     */
    public Boolean audit(OrderShippingReminderAuditParams orderShippingReminderAuditParams,String operator) {

        ApiRPCResult<Boolean> result = orderShippingReminderBackendApi.audit(orderShippingReminderAuditParams, operator);
        if (result == null || result.isFail()) {
            throw new XyyEcOrderBizNoneCheckRTException(result.getErrMsg());
        }
        return true;


    }

    public ResponseVo<String> auditBatch(OrderShippingReminderAuditBatchVo orderShippingReminderAuditBatchvo, String operator){
        try {
            OrderShippingReminderAuditBatchParams orderShippingReminderAuditBatchParams=new OrderShippingReminderAuditBatchParams();
            List<OrderShippingReminderParams> reminders=new ArrayList<>();
            for (OrderShippingReminderVo reminder : orderShippingReminderAuditBatchvo.getReminders()) {
                OrderShippingReminderParams param=new OrderShippingReminderParams();
                BeanUtils.copyProperties(reminder,param);
                param.setId(reminder.getReminderId());
                reminders.add(param);
            }
            orderShippingReminderAuditBatchParams.setReminders(reminders);
            orderShippingReminderAuditBatchParams.setAuditType(orderShippingReminderAuditBatchvo.getAuditType());
            orderShippingReminderAuditBatchParams.setAuditInstructions(orderShippingReminderAuditBatchvo.getAuditInstructions());
            orderShippingReminderAuditBatchParams.setExpireTime(orderShippingReminderAuditBatchvo.getExpireTime());
            ApiRPCResult<List<String>> batchAuditResult = orderShippingReminderBackendApi.auditBatch(orderShippingReminderAuditBatchParams, operator);
            if (batchAuditResult.isSuccess()){
                List<String> failOrderNos = batchAuditResult.getData();
                if (CollectionUtils.isEmpty(failOrderNos)){
                    return  ResponseVo.successResult("全部订单审核完成!");
                }else{
                    return ResponseVo.errRest("部分订单审核失败，失败订单号："+ StringUtils.join(failOrderNos,","));
                }
            }
            return ResponseVo.errRest("批量审核失败!");
        }catch (Exception e){
            log.error("批量审核催发货单失败",e);
           throw new RuntimeException("批量审核失败!");
        }
    }

    public Boolean applyWorkdayExceptionRule(WorkdayExceptionRuleDto workdayExceptionRuleDto) {
        try {
            ApiRPCResult<Boolean> result = orderShippingReminderApi.applyWorkdayExceptionRule(workdayExceptionRuleDto);
            if (result.isSuccess()) {
                return result.getData();
            }
        }catch (Exception e){
            log.error("OrderShippingReminderRemote.applyWorkdayExceptionRule: 异常，params: {}", workdayExceptionRuleDto, e);
        }
        return false;
    }

    public List<WorkDayDateInfoVo> getWorkDayDateInfo(WorkdayExceptionRuleDto workdayExceptionRuleDto) {
        try {
            ApiRPCResult<List<WorkDayDateInfoVo>> result = orderShippingReminderApi.getWorkDayDateInfo(workdayExceptionRuleDto);
            if (result.isSuccess()) {
                return result.getData();
            }
        }catch (Exception e){
            log.error("OrderShippingReminderRemote.getWorkDayDateInfo: 异常，params: {}", workdayExceptionRuleDto, e);
        }
        return new ArrayList<>();
    }

}
