package com.xyy.ec.pop.service.admin;

import com.xyy.ec.pop.dto.PopCashAdvanceBatchConfirmImportDTO;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceDto;
import com.xyy.ec.pop.vo.BatchUpdateResultVo;
import com.xyy.ec.pop.vo.ResponseVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> lizhiwei
 * @description: 商户提现
 * create at:  2020/12/7  19:39
 */
public interface AdminCashAdvanceService {

    boolean confirmPayment(List<Integer> ids, String updateBy, Date paymentTime) throws ServiceException;

    PopCashAdvanceDto queryById(Integer id);

    ResponseVo<BatchUpdateResultVo> batchConfirm(List<PopCashAdvanceBatchConfirmImportDTO> dataList, String userName, MultipartFile file);

    boolean confirmPayment(List<Integer> ids, String updateBy) throws ServiceException;
}
