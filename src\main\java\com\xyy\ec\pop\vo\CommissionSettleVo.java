package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 佣金结算记录
 * 后台接口查询返回用的实体
 * tb_xyy_pop_commission_settle
 * <AUTHOR>
@Data
public class CommissionSettleVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 结算月份
     */
    private String hireSettleMonth;
    /**
     * 佣金结算方式，1：非月结；2：月结
     */
    private Byte settlementType;
    /**
     * 佣金结算流水编号
     */
    private String hireNo;

    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 店铺名称
     */
    private String name;

    /**
     * 佣金
     */
    private BigDecimal hireMoney;

    /**
     * 应缴纳佣金
     */
    private BigDecimal deductedCommission;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 佣金收取月份，逗号分隔
     */
    private String hireMonths;

    /**
     * 付款期限：最后付款时间
     */
    private Date paymentTerm;

    /**
     * 确认收款时间
     */
    private Date paymentTime;

    /**
     * 付款凭证图片
     */
    private String paymentCertificate;

    /**
     * 付款凭证上传时间
     */
    private Date uploadTime;

    /**
     * 状态：0：待商业付款；1-待平台审核；2：审核通过；3：审核未通过；4：待结转;5:已结转
     */
    private Integer state;

    /**
     * 备注原因
     */
    private String remarks;

    /**
     * 是否逾期 0：全部 1：逾期  2：不逾期
     */

    private Integer overdue;
    /**
     * 佣金折扣
     */
    private BigDecimal commissionDiscount;
    /**
     * 优惠佣金金额
     */
    private BigDecimal discountHireMoney;

    /**
     * 实际需缴纳佣金金额
     */
    private BigDecimal actualHireMoney;

    /**
     * 省ID
     */
    private Long provId;

    /**
     * 省名称
     */
    private String prov;

    /**
     * 商业上传的发票文件名
     */
    private String invoiceFilename;

}