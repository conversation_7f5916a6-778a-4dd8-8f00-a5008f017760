package com.xyy.ec.pop.dao;

import com.xyy.ec.pop.po.PopBillPaymentPo;
import com.xyy.ec.pop.vo.settle.PopBillPayStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillPayVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface PopBillPaymentMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PopBillPaymentPo record);

    int insertSelective(PopBillPaymentPo record);

    PopBillPaymentPo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PopBillPaymentPo record);

    int updateByPrimaryKey(PopBillPaymentPo record);

    List<PopBillPaymentPo> queryPopBillList(@Param("popBillPay") PopBillPayVo popBillPayVo, @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    Long queryPopBillListCount(@Param("popBillPay") PopBillPayVo popBillPayVo);

    PopBillPayStatisVo queryPopBillPayStatis(@Param("popBillPay") PopBillPayVo popBillPayVo);

    PopBillPaymentPo selectByFlowNo(@Param("flowNo") String flowNo);

    List<String> queryByOrderNos (@Param("popBillPay") PopBillPaymentPo popBillPaymentPo);

    List<PopBillPaymentPo> queryPopBillPayByFlowNoList(@Param("list") List<String> flowNoList);

    void batchUpdateById(@Param("list") List<PopBillPaymentPo> popBillPaymentPos);
}