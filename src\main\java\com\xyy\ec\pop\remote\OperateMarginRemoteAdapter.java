package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.api.admin.OperateMarginAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.merchant.dto.OperateMarginLogDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.rmi.ServerException;


/**
 * @version v1
 * @Description 保证金审核通过，修改集合
 * <AUTHOR>
 */
@Component
@Slf4j
public class OperateMarginRemoteAdapter {
    @Reference
    private OperateMarginAdminApi operateMarginAdminApi;
    public void updateCheckResult(OperateMarginLogDto logDto) throws ServiceException {
        ApiRPCResult<Boolean> result;
        try {
            log.info("OperateMarginRemoteAdapter.updateCheckResult(logDto:{})",JSON.toJSONString(logDto));
            result = operateMarginAdminApi.updateCheckResult(logDto);
            log.info("OperateMarginRemoteAdapter.updateCheckResult(logDto:{}) return {}",JSON.toJSONString(logDto), JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("OperateMarginRemoteAdapter.updateCheckResult(logDto:{}) 异常",JSON.toJSONString(logDto), e);
            throw new ServiceException("审核保证金异常");
        }
        if(result.isFail()){
            throw new ServiceException(result.getErrMsg());
        }
    }
}
