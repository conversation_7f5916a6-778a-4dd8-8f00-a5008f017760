package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.ErpApprovalAgencyPageDto;
import com.xyy.ec.pop.utils.cookie.CookieUser;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.Cookie;
import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 2020/12/09
 */
@RequestMapping(value = "/corporation/v2")
@Slf4j
@Controller
public class HomePageController extends BaseController {
    @Reference
    private CorporationAdminApi corporationAdminApi;

    @GetMapping("listAgency")
    @ResponseBody
    public ResponseVo listAgency(Long provId,Integer pageNum, Integer pageSize) {
        if(pageSize == null || pageNum == null ){
            return ResponseVo.errRest("参数异常");
        }
        List<Long> provIds = getProvIds(provId);
        if(CollectionUtils.isEmpty(provIds)){
            return ResponseVo.successResultNotData();
        }

        String oaId = getUser().getOaId();
        ApiRPCResult<ErpApprovalAgencyPageDto> rpcResult = corporationAdminApi.listAgency(oaId, getProvIds(provId), pageNum, pageSize);
        if (rpcResult.isFail()) {
            return ResponseVo.errRest(rpcResult.getMsg());
        }
        return ResponseVo.successResult(rpcResult.getData());
    }
}
