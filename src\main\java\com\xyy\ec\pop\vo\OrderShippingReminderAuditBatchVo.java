package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class OrderShippingReminderAuditBatchVo implements Serializable {

    /**
     * 催单id
     */
    private List<OrderShippingReminderVo> reminders;
    /**
     * 31-审核通过  32-审核不通过
     */
    private Integer auditType;
    /**
     * 回复商家
     */
    private String auditInstructions;
    /**
     * 最晚发货时间
     */
    private Date expireTime;
}
