package com.xyy.ec.pop.base;

import java.io.IOException;
import java.io.UnsupportedEncodingException;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;

/**
 * I/O 流
 * <AUTHOR>
 * @version 创建时间：2018年4月27日 上午10:12:56
 */
public class Stream {
	private static Logger logger = LoggerFactory.getLogger(Stream.class);
	public static String Charset = "UTF-8";	//设置响应流的编码格式

	/**
	 * 响应JSON文本信息
	 * @param res {@link HttpServletResponse}
	 * @param json {@link Object}：将被解析为JSON字符的对象；
	 * @return {@link Boolean}：响应状态。
	 * @version V1.0.1
	 * <AUTHOR>
	 * @date 2018年3月13日 下午7:26:11
	 */
	public static boolean ResponseJSON(HttpServletResponse res, Object json) {
		return Response(res, JSON.toJSONString(json));
	}
	/**
	 * 响应一个自定义文本信息
	 * @param res {@link HttpServletResponse}
	 * @param info {@link String}：文本信息；
	 * @return {@link Boolean}：响应状态。
	 * @version V1.0.1
	 * <AUTHOR>
	 * @date 2018年3月13日 下午7:26:11
	 */
	public static boolean Response(HttpServletResponse res, String info) {
		ServletOutputStream out = CreateOutputStream(res);
		if (out != null) {
			return StrOutputStream(out, info);
		}
		return false;
	}
	/**
	 * 创建响应客户端的输出流
	 * <AUTHOR>
	 * @version 创建时间：2018年1月21日 下午12:08:27
	 * @param res {@link HttpServletResponse}
	 * @return {@link ServletOutputStream}：Servlet响应流。
	 */
	public static ServletOutputStream CreateOutputStream(HttpServletResponse res) {
		ServletOutputStream out = null;
		try {
			res.setContentType("text/html;charset=" + Charset);
			res.setCharacterEncoding(Charset);
			out = res.getOutputStream();
			return out;
		} catch (IOException e) {
			logger.error("提示：创建输出流出错！", e);
			ServletOutputStreamClose(out);	//尝试关闭此流
			return null;
		}
	}
	/**
	 * 输出设定编码格式json字符流
	 * <AUTHOR>
	 * @version 创建时间：2018年1月27日 上午9:56:06
	 * @param out {@link ServletOutputStream}
	 * @param info {@link String}：文本信息；
	 * @return {@link Boolean}：响应状态。
	 */
	public static boolean StrOutputStream(ServletOutputStream out, String info) {
		if (out == null) {
			logger.info("提示：输出流异常。");
		} else {
			byte[] bytes;
			try {
				bytes = info.getBytes(Charset);
			} catch (UnsupportedEncodingException e) {
				bytes = info.getBytes();	//如果在UTF-8编码时失败，则取消编码
				logger.error("提示：输出json时，在" + Charset + "编码时出错！已转为普通编码。", e);
			}
			try {
				out.write(bytes);
				return true;
			} catch (IOException e) {
				logger.error("提示：输出流响应异常！", e);
			} finally {
				ServletOutputStreamClose(out);	//尝试关闭此流
			}
		}
		return false;
	}
	/**
	 * 尝试关闭输出流
	 * <AUTHOR>
	 * @version 创建时间：2018年1月21日 下午12:10:19
	 * @param out {@link ServletOutputStream}
	 */
	public static void ServletOutputStreamClose(ServletOutputStream out) {
		if (out != null) {
			try {
				out.flush();
			} catch (IOException e) {
				logger.error("提示：输出I/O流，刷新此流的缓冲出错！", e);
			}
			try {
				out.close();
			} catch (IOException e) {
				logger.error("提示：输出json I/O流（O流）出错！流未关闭。", e);
			}
		}
	}

}
