package com.xyy.ec.pop.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.helper.PopReceivingReportHelper;
import com.xyy.ec.pop.remote.PopReceivingReportRemote;
import com.xyy.ec.pop.server.api.seller.dto.PopReceivingReportDto;
import com.xyy.ec.pop.server.api.seller.param.PopReceivingReportParam;
import com.xyy.ec.pop.service.ReceivingReportService;
import com.xyy.ec.pop.utils.PageInfoUtils;
import com.xyy.ec.pop.vo.PopReceivingReportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReceivingReportServiceImpl implements ReceivingReportService {

    @Autowired
    private PopReceivingReportRemote reportRemote;


    @Override
    public PageInfo<PopReceivingReportVo> popReceivingReportList(PopReceivingReportParam reportParam) {
        PageInfo<PopReceivingReportDto> pageInfo = reportRemote.popReceivingReportList(reportParam);
        PageInfo<PopReceivingReportVo> pageInfoVo = new PageInfo<>();
        if(CollectionUtils.isEmpty(pageInfo.getList())){
            pageInfoVo.setList(Collections.EMPTY_LIST);
            pageInfoVo.setTotal(0);
            pageInfoVo.setPages(0);
            return pageInfoVo;
        }
        List<PopReceivingReportDto> list = pageInfo.getList();
        List<PopReceivingReportVo> reportVos = list.stream().map(PopReceivingReportHelper::convertDtoToVo).collect(Collectors.toList());
        pageInfoVo.setPages(pageInfo.getPages());
        pageInfoVo.setPageNum(pageInfo.getPageNum());
        pageInfoVo.setPageSize(pageInfo.getPageSize());
        pageInfoVo.setList(reportVos);
        pageInfoVo.setTotal(pageInfo.getTotal());
        return pageInfoVo;
    }

    @Override
    public void syncMerchant() {
        reportRemote.syncMerchant();
    }

    @Override
    public BigDecimal popReceivingReportSummation(PopReceivingReportParam receivingReportParam) {

        return reportRemote.popReceivingReportSummation(receivingReportParam);
    }
}
