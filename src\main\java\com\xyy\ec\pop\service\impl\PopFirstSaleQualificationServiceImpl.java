package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.server.api.product.api.PopFirstSaleQualificationApi;
import com.xyy.ec.pop.server.api.product.dto.*;
import com.xyy.ec.pop.service.PopFirstSaleQualificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PopFirstSaleQualificationServiceImpl implements PopFirstSaleQualificationService {

    @Reference(version = "1.0.0")
    private PopFirstSaleQualificationApi popFirstSaleQualificationApi;

    @Override
    public PageInfo<PopFirstSaleQualificationResDto> query(PopFirstSaleQualificationDto dto) {
        return popFirstSaleQualificationApi.FirstSaleQualificationQuery(dto);
    }

    @Override
    public String downZipUrl(String orgId, String barCode,String sealStatus) throws Exception{
        return popFirstSaleQualificationApi.downZipUrl(orgId, barCode,sealStatus);
    }
}
