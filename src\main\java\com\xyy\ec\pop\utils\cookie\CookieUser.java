package com.xyy.ec.pop.utils.cookie;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.redis.XyyJedisCluster;
import com.xyy.ec.pop.utils.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

public class CookieUser {
	@Autowired
	private XyyJedisCluster xyyJedisCluster;

	private Logger logger = LoggerFactory.getLogger(BaseController.class);

	/**
	 * 功能：cookie中取出Session_id 然后去redis中取当前登录用户.
	 * @return SupplierUser 用户
	 */
	public SysUser getUser(){
		RequestAttributes ra = RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = ((ServletRequestAttributes) ra).getRequest();
		Cookie cookie = CookieTool.getCookie(request, Constants.SESSION_ID);
		if (cookie != null) {
			try {
				String json = xyyJedisCluster.get(cookie.getValue());
				SysUser user = (SysUser) JSON.parseObject(json, SysUser.class);
				return user;
			} catch (Exception e) {
				logger.error(
						"分布式缓存中没有sid=" + cookie.getValue() + "的用户"
								+ e.getMessage(), e);
			}

		}
		return null;
	}
}
