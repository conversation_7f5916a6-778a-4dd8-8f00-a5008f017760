package com.xyy.ec.pop.service;

import com.xyy.ec.pop.vo.PopSkuQualificationVo;
import com.xyy.ec.pop.vo.PopSkuQualificationsDetailVo;

import java.util.List;

public interface SkuQualificationService {

    /**
     * 根据商品编码获取商品首营资质
     * @param barcode
     * @return
     */
    List<PopSkuQualificationVo> selectByBarcode(String barcode);

    /**
     * 根据一级二级商品分类获取首营资质配置
     * @param firstCategoryId
     * @param secondCategoryId
     * @return
     */
    List<PopSkuQualificationsDetailVo> selectQualificationDetailByCategoryId(Long firstCategoryId, Long secondCategoryId);
}
