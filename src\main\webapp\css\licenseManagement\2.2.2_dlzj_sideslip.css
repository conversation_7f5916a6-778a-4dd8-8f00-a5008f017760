html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, menu, nav, output, ruby, section, summary, time, mark, audio, video{
    margin: 0;
    padding: 0;
    border: 0;
    text-decoration: none;
    font: normal;
    font-size: 12px;
    font-family: "Microsoft YaHei", "微软雅黑";
}

.red{
    color: red;
}
.blue {
    color: #089AD6;
}

.green {
    color: #0EAF12;
}

.green2 {
    color: #67A462;
}

.orange {
    color: #FF5000;
}
.dlzj_box {
    /*    width: 630px;
        padding-bottom: 10px;
        border-radius: 5px;
        overflow: hidden;
        font-size: 12px;
        box-shadow: 0px 0px 15px #666;*/
    width: 48%;
    left: 100%;
    width: calc(50%- 4px);
    height: 670px;
    border: 1px solid #e9e9e9;
    display: none;
    position: absolute;
    background-color: #fff;
    margin-top: 5px;
}
.dlzj_box_title {
    width: 100%;
    background-color: #0690CE;
    color: #fff;
    text-align: center;
    height: 36px;
    line-height: 36px;
}
.dlzj_box_title span{
    float: right;
    margin-right: 10px;
    cursor: pointer;
}
.dlzj_box_button{
    width: 200px;
    margin: 0 auto;
    margin-top: 10px;
}
.dlzj_box_button div{
    height: 30px;
    line-height: 30px;
    width: 90px;
    text-align: center;
    float: left;
    cursor: pointer;
    border-radius: 5px;
}
.dlzj_box_button_left{
    background-color: #E9E9E9;
    border: 1px solid #ccc;
    background: linear-gradient(#E9E9E9, #ccc); /* 标准的语法 */
}
.dlzj_box_button_left:hover{
    background: linear-gradient(#ccc, #E9E9E9); /* 标准的语法 */
}
.dlzj_box_button_right{
    background-color: #0178B1;
    border: 1px solid #0178B1;
    margin-left: 10px;
    color: #fff;
    background: linear-gradient(#0784cb, #0075a9); /* 标准的语法 */
}
.dlzj_box_button_right:hover{
    background: linear-gradient(#0075a9, #0784cb); /* 标准的语法 */
}
.dlzj_box_center{
    width: 95%;
    margin: 0 auto;
    border: 1px solid #F0F0F0;
    margin-top: 10px;
    height: 600px;
    overflow: hidden;
    overflow-y: scroll;
    font-size: 14px;
    padding-bottom: 10px;
}
.dlzj_box_center table tr td{
    height: 40px;
    line-height: 40px;
}
.dlzj_box_center table tr td:nth-child(1){
    vertical-align:top;
    padding-left: 10px;
}
.dlzj_box_center table tr td:nth-child(2){
    width: 80%;
}
.dlzj_box_center table tr:nth-child(3) td:nth-child(2){
    vertical-align:top;

}
.dlzj_box_center table tr:nth-child(8) td:nth-child(2){
    vertical-align:top;
    line-height: 15px;
}
.dlzj_box_center table tr:nth-child(13) td:nth-child(2){
    vertical-align:top;
    line-height: 15px;
}
.dlzj_box_center table tr:nth-child(18) td:nth-child(2){
    vertical-align:top;
    line-height: 15px;
}
.dlzj_box_center table tr:nth-child(2) td:nth-child(2) input{
    width: 250px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.dlzj_box_center table textarea{
    width: 440px;
    height: 120px;
    resize: none;
    border: 1px solid #D5D5D5;
}