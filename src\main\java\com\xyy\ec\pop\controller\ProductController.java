package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.config.ProductBatchUpdateConfig;
import com.xyy.ec.pop.config.ProductConfig;
import com.xyy.ec.pop.config.XyyConfig;
import com.xyy.ec.pop.config.XyyPopBaseConfig;
import com.xyy.ec.pop.dto.ProductAuditDTO;
import com.xyy.ec.pop.excel.verify.ProductBatchUpdateExcelVerifyHandler;
import com.xyy.ec.pop.excel.verify.ProductBatchUpdateValid;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.PopSkuOperationLogHelper;
import com.xyy.ec.pop.helper.ProductSkuConvertHelper;
import com.xyy.ec.pop.model.*;
import com.xyy.ec.pop.remote.*;
import com.xyy.ec.pop.server.api.admin.api.PopSkuAuditApi;
import com.xyy.ec.pop.server.api.admin.dto.ResentCallbackDto;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationPriceTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.product.api.PopErpSkuApi;
import com.xyy.ec.pop.server.api.product.api.activity.PopSkuActivityApi;
import com.xyy.ec.pop.server.api.product.dto.*;
import com.xyy.ec.pop.server.api.product.dto.activity.PopSkuCopyParamDto;
import com.xyy.ec.pop.server.api.product.dto.activity.PopSkuCopyResultDto;
import com.xyy.ec.pop.server.api.product.enums.ActivityTypeEnum;
import com.xyy.ec.pop.server.api.product.enums.PopSkuStatus;
import com.xyy.ec.pop.service.*;
import com.xyy.ec.pop.utils.*;
import com.xyy.ec.pop.vo.*;
import com.xyy.ec.product.back.end.ecp.skucategory.dto.CategoryBackEndDTO;
import com.xyy.ec.product.back.end.ecp.stock.SkuStockLogParamDTO;
import com.xyy.ec.product.back.end.ecp.stock.dto.BPageDto;
import com.xyy.ec.product.back.end.ecp.stock.dto.EcpProductOwnerStockLogDTO;
import com.xyy.me.product.general.api.dto.product.GeneralProductDto;
import com.xyy.me.product.service.read.dto.TotalDictionaryReadDto;
import com.xyy.ms.promotion.business.result.MarketingGroupBuyingBaseInfoDTO;
import io.swagger.annotations.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/product")
@Api(tags = "商品控制类")
public class ProductController extends BaseController {
    private static final Logger LOG = LoggerFactory.getLogger(ProductController.class);

    @Autowired
    private XyyConfig xyyConfig;
    @Autowired
    private XyyPopBaseConfig popBaseConfig;
    @Autowired
    private ProductCategoryService productCategoryService;
    @Autowired
    private ProductService productService;
    @Autowired
    private ProductSkuRemoteAdapter productSkuRemoteAdapter;
    @Autowired
    private ProductReportingService productReportingService;
    @Autowired
    private PopSkuOperationLogRemoteAdapter skuOperationLogRemoteAdapter;
    @Autowired
    private MeProductApiRemote meProductApiRemote;
    @Autowired
    private EcSkuRemoteAdapter ecSkuRemoteAdapter;
    @Reference
    private PopErpSkuApi popErpSkuApi;
    @Autowired
    private BusinessCategoryCommissionRemoteAdapter businessCategoryCommissionRemoteAdapter;
    @Value("${product.export.maxQueryMidStep:50}")
    private int maxQueryMidStep;
    @Value("${product.batchUpdate.users}")
    private String batchUpdateUsers;
    @Value("${aliyun.oss.downloadPath}")
    private String hostUrl;
    @Value("${product.default.picture}")
    private String pictureDefault;
    @Value("${product.me.copy.barcode.maxSize:1000}")
    private int maxCopySizeByBarcode;
    @Value("${product.me.copy.StandardId.maxSize:200}")
    private int maxCopySizeByStandardId;
    @Value("${product.me.copy.users}")
    private String meCopyUsers;
    @Value("${product.erp.reMatchAll.users}")
    private String erpReMatchUser;
    @Value("${pop-base.bigDescImgUrlPrefix}")
    private String bigDescImgUrlPrefix;
    @Reference
    private PopSkuActivityApi popSkuActivityApi;
    @Value("${product.activity.copy.test.user}")
    private String activityCopyTestUser;
    @ApolloJsonValue("${product.dosageForm.option}")
    public List<String> dosageList;
    @Autowired
    private ProductBatchUpdateConfig productBatchUpdateConfig;
    @Autowired
    private PopErpTaskRemote popErpTaskRemote;
    @Autowired
    private PopBusAreaRemoteAdapter popBusAreaRemoteAdapter;
    @Autowired
    private CorporationPriceTypeRemote corporationPriceTypeRemote;
    @Autowired
    private CorporationRemote corporationRemote;
    @Autowired
    private PromotionRemote promotionRemote;
    @Autowired
    private MeDicService meDicService;
    @Reference(version = "1.0.0")
    private PopSkuAuditApi popSkuAuditApi;

    @GetMapping(value = "/index")
    @ApiOperation("跳转商品页面")
    public String index(){
        return "product/index";
    }

    @ResponseBody
    @PostMapping(value = "/syncDisableInfo")
    @ApiOperation("同步商品停用类型")
    public ResponseVo<List<SyncPopSkuInfoRspVo>> syncDisableInfo(@RequestBody List<SyncPopSkuInfoVo> syncPopSkuInfoVoList) {
        if(CollectionUtils.isEmpty(syncPopSkuInfoVoList)){
            return new ResponseVo<>(Lists.newArrayList());
        }
        List<SyncPopSkuInfoRspVo> syncPopSkuInfoRspVoList = Lists.newArrayList();
        syncPopSkuInfoVoList.forEach(syncPopSkuInfoVo -> {
            // 返回值
            SyncPopSkuInfoRspVo syncPopSkuInfoRspVo = productSkuRemoteAdapter.syncPopSkuInfo(syncPopSkuInfoVo);
            if(null == syncPopSkuInfoRspVo){
                return;
            }
            syncPopSkuInfoRspVoList.add(syncPopSkuInfoRspVo);
        });
        // 返回结果
        return new ResponseVo<>(syncPopSkuInfoRspVoList);
    }

    @ResponseBody
    @GetMapping(value = "/list")
    @ApiOperation("商品列表")
    public Object list(@ApiParam(name = "page",value = "分页") Page<ProductVo> page,@ApiParam(name = "po",value = "商品入参实体")  ProductVo po) {
        po.setPageNum(page.getOffset()/page.getLimit()+1);
        po.setPageSize(page.getLimit());
        po.setProperty(page.getProperty());
        po.setDirection(page.getDirection());
        po.setIsThirdCompany(com.xyy.ec.pop.constants.Constants.IS1);
        List<Long> provIds = getProvIds(po.getProvId());
        if(CollectionUtils.isEmpty(provIds)){
            return getEmptyPageList();
        }
        po.setProvIds(provIds);
        return productListVo(po);
    }

    private  Page<ProductListVo> productListVo(ProductVo productVo){
       SkuAdminPageQuery query = ProductSkuConvertHelper.convertToQuery(productVo);
        PageInfo<PopSkuDetailDto> detailPage = productSkuRemoteAdapter.adminQuery(query);
        Page<ProductListVo> ecSkuList = ProductSkuConvertHelper.convertToPage(detailPage);

        if(CollectionUtils.isEmpty(ecSkuList.getRows())){
            return getEmptyPageList();
        }else {
            remarkActivity(ecSkuList.getRows());
            //调用库存接口查询商品库存
            //调用营销接口查询拼团价格和总限购库存
            fillGroupPriceAndTotalLimitQty(ecSkuList.getRows());
            //填充ec库存
            fillEcQty(ecSkuList.getRows());
            productService.fillPriceMerchantGroup(detailPage.getList(),ecSkuList);
            return ecSkuList;
        }
    }


    private void fillEcQty(List<ProductListVo> ecSkuList) {
        List<Long> csuIds = ecSkuList.stream().map(item -> item.getCsuid()).filter(item->item!=null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(csuIds)){
            return;
        }
        Map<Long, Integer> ecStock = ecSkuRemoteAdapter.getStockBySkuIdList(csuIds).stream().collect(Collectors.toMap(item -> item.getCsuId(), item -> item.getAvailableQty()));
        ecSkuList.forEach(item->{
            Integer stock = ecStock.get(item.getCsuid());
            if(stock!=null){
                item.setAvailableQty(stock.toString());
            }
        });
    }

    private void fillGroupPriceAndTotalLimitQty(List<ProductListVo> rows) {
        //活动品的活动id集合
        List<Long> activityIdList = rows.stream().filter(f -> Objects.equals(f.getActivityType(), ActivityTypeEnum.GROUP.getCode())).map(item -> item.getActivityId()).collect(Collectors.toList());
        //查询商品活动信息
        List<MarketingGroupBuyingBaseInfoDTO> skuActivities = promotionRemote.listMarketingGroupBuyingBaseInfo(activityIdList);
        Map<Long, MarketingGroupBuyingBaseInfoDTO> csuIdEntityMap = skuActivities.stream().collect(Collectors.toMap(item -> item.getCsuId(), Function.identity()));
        rows.stream().forEach(item -> {
            MarketingGroupBuyingBaseInfoDTO groupSkuInfo = csuIdEntityMap.get(item.getCsuid());
            if (groupSkuInfo != null) {
                item.setGroupPrice(groupSkuInfo.getDiscountPrice());
                item.setTotalLimitQty(groupSkuInfo.getTotalQty());
            }
        });
    }

    /**
     * 标记商品活动
     * @param rows
     */
    private void remarkActivity(List<ProductListVo> rows) {
        String orgId = rows.get(0).getOrgId();
        CorporationDto corporationDto = corporationRemote.queryCorpBaseByOrgId(orgId);
        if (corporationDto == null) {
            return;
        }
        List<Long> csuIds = rows.stream().map(item -> item.getCsuid()).collect(Collectors.toList());
        List<EcpSkuActiveVo> acts = productSkuRemoteAdapter.getActivityCsuIds(csuIds);
        rows.stream().forEach(item->{
            for (EcpSkuActiveVo vo : acts) {
                if (item.getCsuid().equals(vo.getCsuId())) {
                    item.setActiveName(vo.getActive().getTitle());
                    break;
                }
            }
        });
    }

    private Page<ProductListVo> getEmptyPageList() {
        Page<ProductListVo> pageInfo = new Page<>();
        pageInfo.setTotal(0L);
        pageInfo.setRows(Lists.newArrayList());
        pageInfo.setPageCount(0);
        return pageInfo;
    }


    /**
     * 设置商品经营分类
     * @param tempList
     * @param cateMap
     */
    private void setProductCategory(List<ProductListVo> tempList, Map<Integer, String> cateMap) {
        List<ProductListVo> standardList = tempList.stream().filter(item->StringUtils.isNotEmpty(item.getStandardProductId())).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(standardList)){
            //查询有标准库的商品分类
            Map<String, GeneralProductDto> map = queryMidInfo(standardList.stream().map(item->item.getStandardProductId()).collect(Collectors.toSet()));
            standardList.forEach(item->{
                GeneralProductDto dto = map.get(item.getStandardProductId());
                if(dto==null){
                    return;
                }
                item.setCategoryFirstName(dto.getFirstCategoryName());
                item.setCategorySecondName(dto.getSecondCategoryName());
                item.setCategoryThirdName(dto.getThirdCategoryName());
                item.setCategoryFourthName(dto.getFourthCategoryName());
            });
        }

        //没有标准库id的根据已有分类 id查
        tempList.stream().filter(item->StringUtils.isEmpty(item.getCategoryFirstName())).
                forEach(item->{
                    item.setCategoryFirstName(cateMap.get(NumberUtils.toInt(item.getCategoryFirstId())));
                    item.setCategorySecondName(cateMap.get(NumberUtils.toInt(item.getCategorySecondId())));
                    item.setCategoryThirdName(cateMap.get(NumberUtils.toInt(item.getCategoryThirdId())));
                    item.setCategoryFourthName(cateMap.get(NumberUtils.toInt(item.getCategoryFourthId())));
                });
    }

    private void setBusAreaName(List<ProductListVo> tempList) {
        List<String> barCodes = tempList.stream().map(productListVo -> productListVo.getBarcode()).collect(Collectors.toList());
        Map<String, PopBusAreaProductRelationDto> popBusAreaProductRelationMap = popBusAreaRemoteAdapter.queryBusAreaProductByBarcodes(barCodes);

        tempList.forEach(productListVo -> {
            PopBusAreaProductRelationDto popBusAreaProductRelationDto = popBusAreaProductRelationMap.get(productListVo.getBarcode());
            String busAreaName = ObjectUtils.isEmpty(popBusAreaProductRelationDto) ? "" : popBusAreaProductRelationDto.getBusAreaName();
            productListVo.setBusAreaName(busAreaName);
        });
    }

    private Map<String, GeneralProductDto> queryMidInfo(Set<String> ids) {
        List<List<String>> idLists = Lists.partition(new ArrayList<>(ids), maxQueryMidStep);
        Map<String, GeneralProductDto> map = new HashMap<>(ids.size());
        for (List<String> idList : idLists) {
            List<GeneralProductDto> list = meProductApiRemote.getGeneralProduct(idList);
            if(CollectionUtils.isEmpty(list)){
               continue;
            }
            list.forEach(item->{
                map.put(item.getProductId(),item);
            });
        }
        return map;
    }

    @GetMapping(value = "/detail")
    @ApiOperation("商品明细")
    public String detail(@ApiParam(name = "barcode",value = "商品id") String barcode, @ApiIgnore Model model, @ApiParam(name = "isAuditing",value = "是否审核标识")Integer isAuditing,
                         @ApiParam(name = "supplierId)",value = "机构id")String orgId){
        try {
            ProductSkuVo productVo = productService.getProductInfo(orgId,barcode);
            long erpId = NumberUtils.toLong(productVo.getErpThirdCategoryId());
            CategoryBackEndDTO catDto = ecSkuRemoteAdapter.getCategoryInfoByErpCategoryId(erpId==0?null:erpId);
            Byte priceType = corporationPriceTypeRemote.queryPriceTypeByOrgId(orgId);
            if(catDto!=null){
                productVo.setSkuRelationCategoryName(catDto.getName());
                productVo.setSkuRelationCategory(catDto.getId());
            }
            model.addAttribute("id",productVo.getId());
            //有设置同步erp
            if(Objects.equals(productVo.getStockSyncErp(),1)||Objects.equals(productVo.getPriceSyncErp(),1)){
                boolean hasConfig = popErpTaskRemote.hasConfig(productVo.getOrgId());
                if(!hasConfig){//配置为空表示没有对接erp
                    productVo.setStockSyncErp(0);
                    productVo.setPriceSyncErp(0);
                }
            }
            //调中台获取 单位
            List<String> productUnitList = meDicService.productUnitList();
            productVo.setProductUnitList(productUnitList);
            //获取apollo配置 剂型
            productVo.setDosageFormList(dosageList);
            model.addAttribute("host", xyyConfig.getEcHost());
            model.addAttribute("bigImgUrlPrefix", popBaseConfig.getBigImgUrlPrefix());
            model.addAttribute("smallImgUrlPrefix", popBaseConfig.getSmallImgUrlPrefix());
            model.addAttribute("smallDescImgUrlPrefix",popBaseConfig.getSmallDescImgUrlPrefix());
            model.addAttribute("bigDescImgUrlPrefix",bigDescImgUrlPrefix);
            String certicatePrefix = xyyConfig.getCdnConfig().getShowUrl();
            model.addAttribute("certicatePrefix", certicatePrefix);
            BigDecimal commissionRatio = productVo.getCommissionRatio();
            model.addAttribute("productVo",productVo);
            model.addAttribute("isAuditing",isAuditing);
            model.addAttribute("isInstrumentCategory", ProductConfig.config.instrumentCategory.equals(productVo.getErpFirstCategoryId()));
            model.addAttribute("isCosmetics", ProductConfig.config.cosmeticsCategory.contains(productVo.getErpFirstCategoryId()));
            model.addAttribute("isDrug", ProductConfig.config.drugCategory.contains(productVo.getErpFirstCategoryId()));
            model.addAttribute("isChi", ProductConfig.config.chiMedCategory.contains(productVo.getErpFirstCategoryId()));
            model.addAttribute("isCommon", ProductConfig.config.commonCategory.contains(productVo.getErpFirstCategoryId()));
            model.addAttribute("corOrgId",orgId);
            model.addAttribute("priceType",priceType==null? CorporationPriceTypeEnum.defaultType.getCode():priceType);
            model.addAttribute("commissionRatio", commissionRatio);
            model.addAttribute("commissionConfig", businessCategoryCommissionRemoteAdapter.getPopCommissionConfig());
            //获取ec全部发布分类
            List<TotalDictionaryReadDto> fourLevelProductCategory = productCategoryService.getFourLevelProductCategory();
            LOG.info("中台四级分类"+JSON.toJSONString(fourLevelProductCategory));
            List<EcDeployCategoryVO> fourCategoryVOS = convertCategoryVO(fourLevelProductCategory);
            Map<String, List<EcDeployCategoryVO>> fourLevelProductCategoryMap = fourCategoryVOS.stream().collect(Collectors.groupingBy(data -> String.valueOf(data.getParentId())));
            model.addAttribute("categoryMap", JSON.toJSON(fourLevelProductCategoryMap));
            return "product/detail";
        }catch (Exception e){
            LOG.error("查询商品信息异常. barcode:{},orgId:{}",barcode,orgId,e);
            return "product/detail";
        }
    }

    private List<EcDeployCategoryVO> convertCategoryVO(List<TotalDictionaryReadDto> list) {
        List<EcDeployCategoryVO> categoryList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        list.forEach(val -> {
            EcDeployCategoryVO vo = new EcDeployCategoryVO();
            vo.setId(val.getId().longValue());
            vo.setName(val.getDictName());
            vo.setParentId(val.getParentId().longValue());
            vo.setLevel(val.getLevelNode().intValue());
            categoryList.add(vo);
        });
        return categoryList;
    }
    /**
     * 审核
     */
    @GetMapping(value = "/auditingPage")
    @ApiOperation("跳转审核页面")
    public String auditingPage(@ApiParam(name = "id",value = "商品id") Integer id,@ApiParam(name = "productCategoryIds",value = "关联分类") String productCategoryIds,
                               @ApiParam(name = "skuCategoryId",value = "经营分类id") String skuCategoryId,
                               @ApiParam(name = "skuCategory",value = "经营分类") String skuCategory,
                               @ApiParam(name = "barcode",value = "商品编码") @RequestParam(value = "barcode",required = false) String barcode,
                               @ApiParam(name = "firstCategoryId",value = "一级发布分类") @RequestParam(value = "firstCategoryId",required = false) Long firstCategoryId,
                               @ApiParam(name = "secondCategoryId",value = "二级发布分类") @RequestParam(value = "secondCategoryId",required = false) String secondCategoryId,
                               @ApiParam(name = "thirdCategoryId",value = "三级发布分类") @RequestParam(value = "thirdCategoryId",required = false) String thirdCategoryId,
                               @ApiParam(name = "fourthCategoryId",value = "四级发布分类") @RequestParam(value = "fourthCategoryId",required = false) String fourthCategoryId,
                               @ApiParam(name = "productUnit",value = "包装单位") @RequestParam(value = "productUnit",required = false) String productUnit,
                               @ApiParam(name = "dosageForm",value = "剂型") @RequestParam(value = "dosageForm",required = false) String dosageForm,
                               @ApiParam(name = "storageCondition",value = "存储条件") @RequestParam(value = "storageCondition",required = false) String storageCondition,
                               @ApiParam(name = "drugClassification",value = "处方类型") @RequestParam(value = "drugClassification",required = false) Integer drugClassification,
                               @ApiParam(name = "productName",value = "商品名称") @RequestParam(value = "productName",required = false) String productName,
                               @ApiParam(name = "showName",value = "展示名称") @RequestParam(value = "showName",required = false) String showName,
                               @ApiParam(name = "commonName",value = "通用名称") @RequestParam(value = "commonName",required = false) String commonName,
                               @ApiParam(name = "manufacturer",value = "生产厂家") @RequestParam(value = "manufacturer",required = false) String manufacturer,
                               @ApiParam(name = "brand",value = "品牌") @RequestParam(value = "brand",required = false) String brand,
                               @ApiParam(name = "code",value = "69码") @RequestParam(value = "code",required = false) String code,
                               @ApiParam(name = "approvalNumber",value = "批文") @RequestParam(value = "approvalNumber",required = false) String approvalNumber,
                               @ApiParam(name = "spec",value = "规格") @RequestParam(value = "spec",required = false) String spec,
                               @ApiParam(name = "marketAuthor",value = "上市许可持有人") @RequestParam(value = "marketAuthor",required = false) String marketAuthor,
                               @ApiParam(name = "term",value = "有效期/保质期") @RequestParam(value = "term",required = false) String term,
                               @ApiParam(name = "producer",value = "产地") @RequestParam(value = "producer",required = false) String producer,
                               @ApiParam(name = "manufacturingLicenseNo",value = "生产许可证号或备案凭证编号") @RequestParam(value = "manufacturingLicenseNo",required = false) String manufacturingLicenseNo,
                               @ApiIgnore Model model){
        model.addAttribute("id",id);
        model.addAttribute("productCategoryIds",productCategoryIds);
        model.addAttribute("skuCategoryId",skuCategoryId);
        model.addAttribute("skuCategory",skuCategory);
        model.addAttribute("barcode",barcode);
        model.addAttribute("firstCategoryId",firstCategoryId);
        model.addAttribute("secondCategoryId",secondCategoryId);
        model.addAttribute("thirdCategoryId",thirdCategoryId);
        model.addAttribute("fourthCategoryId",fourthCategoryId);
        model.addAttribute("productUnit",productUnit);
        model.addAttribute("dosageForm",dosageForm);
        model.addAttribute("storageCondition",storageCondition);
        model.addAttribute("drugClassification",drugClassification);
        try {
            productName = URLDecoder.decode(productName,"UTF-8");
            showName =  URLDecoder.decode(showName,"UTF-8");
            commonName =  URLDecoder.decode(commonName,"UTF-8");
        }catch (Exception e){
            LOG.error("decode product name error, productName:{}, showName:{}, commonName:{}, msg:{}", productName, showName, commonName, e);
        }
        model.addAttribute("productName", productName);
        model.addAttribute("showName",showName);
        model.addAttribute("commonName",commonName);
        model.addAttribute("manufacturer",manufacturer);
        model.addAttribute("brand",brand);
        model.addAttribute("code",code);
        model.addAttribute("approvalNumber",approvalNumber);
        model.addAttribute("spec",spec);
        model.addAttribute("marketAuthor",marketAuthor);
        model.addAttribute("term",term);
        model.addAttribute("producer",producer);
        model.addAttribute("manufacturingLicenseNo",manufacturingLicenseNo);
        return "product/auditing";
    }


    @PostMapping(value = "/auditing")
    @ResponseBody
    @ApiOperation("商品审核")
    public Object auditing(ProductAuditDTO productAuditDTO) {
        if (StringUtils.isBlank(productAuditDTO.getSkuCategory()) || productAuditDTO.getSkuCategory() == "0") {
            return this.addError("缺少经营分类！");
        }
        if (productAuditDTO.getStatus()==6 && productAuditDTO.getErpFirstCategoryId()==null ) {
            return this.addError("缺少一级分类参数！");
        }
        if (productAuditDTO.getStatus()==6 && productAuditDTO.getErpSecondCategoryId() == null) {
            return this.addError("缺少二级分类参数！");
        }
        try {
            SysUser user = getUser();
            PopSkuDetailDto skuDetailDto = productSkuRemoteAdapter.getDetailByBarcode(productAuditDTO.getBarcode());
            if(skuDetailDto==null||skuDetailDto.getPopSku().getStatus()!=PopSkuStatus.TO_AUDITED.getValue()){
                return this.addError("当前商品不能审核！请刷新页面查看商品最新状态");
            }
            String errorTip = productService.validAudit(productAuditDTO,skuDetailDto);
            if(errorTip!=null){
                return this.addError(errorTip);
            }
            productService.skuAuditingByBarcode(productAuditDTO, user);
            try {
                if (productAuditDTO.getStatus() == PopSkuStatus.STAY_ON_SHELF.getValue()) {
                    String uid = productReportingService.report(productAuditDTO.getBarcode(), user.getUsername());
                    if (StringUtils.isNotEmpty(uid)) {
                        productService.afterProductReport(productAuditDTO.getBarcode(), uid);
                    }
                }
            } catch (Exception e) {
                LOG.error("ProductController.auditing 新品上报失败 barcode:{} ", productAuditDTO.getBarcode(), e);
            }
            //写日志
        } catch (Exception e){
            LOG.error("ProductController.auditing 审核失败 barcode:{} ",productAuditDTO.getBarcode(),e);
            String paramKey = "barcode:"+productAuditDTO.getBarcode();
            return this.addError("操作失败！");
        }
        return this.addResult("操作成功！");
    }
    @GetMapping(value = "/skuUpdateLog")
    @ApiOperation("商品审核日志页面")
    @ApiImplicitParams({@ApiImplicitParam(name = "skuId",value = "商品id",dataTypeClass = Integer.class),
            @ApiImplicitParam (name = "model",value = "请求过程中传递处理的数据")})
    public String skuUpdateLog(String barcode,Model model){
        List<PopSkuOperationLogDto> list =skuOperationLogRemoteAdapter.getByBarcode(barcode);
        List<SkuOperationLog> skuOperationLogs = PopSkuOperationLogHelper.convertToVo(list);
        model.addAttribute("data",skuOperationLogs);
        return "product/skuLog";
    }

    /**
     * 展示分类树
     * @param skuCategoryId
     * @param orgId 机构编码
     * @return
     */
    @ResponseBody
    @PostMapping(value = "skuCategory")
    public List<ZTree> getSkuCategory(Long skuCategoryId, String orgId){
        List<ZTree> list ;
        try{
            list = productService.getSkuCategoryTree(skuCategoryId,orgId);
            return list;
        }catch (Exception e){
            LOG.error("ProductController.productCategory(productCategoryId:{},orgId:{}) 出现错误",skuCategoryId,orgId,e);
            return new ArrayList<>();
        }
    }
    /**
     * 获取商品经营分类
     * @return
     */
    @GetMapping(value = "loadSkuCategory")
    @ResponseBody
    @ApiOperation("获取商品经营分类")
    public Object loadSkuCategory(){
        return productSkuRemoteAdapter.selectAllSystemDictionaryDto();
    }

    /**
     * 商品信息 预览
     * @param barcode
     * @return
     */
    @RequestMapping(value = "/skuInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResponseVo skuInfo(String orgId,String barcode) {
        LOG.info("#ProductController.skuInfo#info,参数：barcode:{}", barcode);
        try {
            ProductEcVo productVo = productService.getProductPreviewInfo(orgId, barcode);
            productVo.setProductDescImgBaseUrl(popBaseConfig.getSmallDescImgUrlPrefix());
            productVo.setProductImgBaseUrl(popBaseConfig.getSmallImgUrlPrefix());
            return ResponseVo.successResult(productVo);
        } catch (Exception e) {
            LOG.error("#ProductController.skuInfo#error,参数：barcode:{}", barcode, e);
            return ResponseVo.errRest("网络异常，稍后重试!");
        }
    }

    /**
     * 重新匹配所有机构下异常状态商品
     * @return
     */
    @RequestMapping(value = "/reMatchAllErpSku", method = RequestMethod.POST)
    @ResponseBody
    public ResponseVo reMatchAllErpSku(){
        try {
            LOG.info("ProductController.reMatchAllErpSku#:"+getUser().getUsername());
            validAuthority(erpReMatchUser,"重新匹配erp商品");
            ApiRPCResult<Boolean> result = popErpSkuApi.reMatchAllErpSku();
            LOG.info("ProductController.reMatchAllErpSku# return {}",  JSON.toJSONString(result));
            return ResponseVo.successResult(result.getData());
        }catch (ServiceException e){
            LOG.warn("ProductController.reMatchAllErpSku# 失败", e.getMessage());
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            LOG.error("ProductController.reMatchAllErpSku# 异常", e);
            return ResponseVo.errRest("重新匹配失败");
        }


    }

    @Value("${product.pop.audit.callback.new.switch:{}}")
    private String popAuditCallbackSwitch;

    @PostMapping(value = "/batchUpdateInfoFromExcel")
    @ResponseBody
    public ResponseVo<BatchUpdateResultVo> batchUpdateInfoFromExcel(@RequestParam("file") MultipartFile file){
        String user = getUser().getUsername();
        try {
            validAuthority(batchUpdateUsers,"批量更新商品信息");
            ImportParams importParams = new ImportParams();
            importParams.setVerifyHandler(new ProductBatchUpdateExcelVerifyHandler());
            List<SkuBatchUpdateVo> vos = ExceImportWarpUtil.importExcel(file,SkuBatchUpdateVo.class,importParams,productBatchUpdateConfig.getMaxFileSize(),productBatchUpdateConfig.getMaxRows(),productBatchUpdateConfig.getUpdateTitles());


            ProductBatchUpdateValid.trim(vos);
            Map<String, String> barcodeErpCodeMap = Maps.newHashMap();
            Map<String, Integer> barcodeActivityTypeMap = getBarcodeActivityTypeMap(vos, barcodeErpCodeMap);
            //未变更或无需校验erpCode重复barcodes
            List<String> ignoreBarcodes = Lists.newLinkedList();
            //查询orgId_erpCode数量 <orgId_erpCode,数量>
            Map<String, Integer> orgIdErpCodeCountMap = getErpCodeCountMap(vos, barcodeErpCodeMap, ignoreBarcodes);
            List<String> orgIdList = new ArrayList<>();
            boolean allResentCallback = false;
            ResentCallbackDto resentCallbackDto= JSON.parseObject(popAuditCallbackSwitch,ResentCallbackDto.class);
            if(Objects.nonNull(resentCallbackDto) && resentCallbackDto.isUserNew()){
                allResentCallback = true;
                orgIdList = resentCallbackDto.getOrgIds();
            }
            ProductBatchUpdateValid.valid(vos, barcodeActivityTypeMap, orgIdErpCodeCountMap, ignoreBarcodes,orgIdList,allResentCallback);


            BatchUpdateResultVo resultVo =productService.batchUpdate(user,vos);
            resultVo.setErrorFileName(FileNameUtils.getErrorFileName(file));
            String downLoadUrl = FileNameUtils.getErrorFileDownUrl(resultVo.getErrorFileUrl(),resultVo.getErrorFileName());
            resultVo.setErrorFileUrl(downLoadUrl);
            return ResponseVo.successResult(resultVo);
        }catch (ServiceException e){
            LOG.warn("ProductController.batchUpdateInfoFromExcel,user:{}, 失败",user,e);
            return ResponseVo.errRest(StringUtils.isEmpty(e.getMessage())?"系统异常":e.getMessage());
        }catch(Exception e){
            LOG.error("ProductController.batchUpdateInfoFromExcel,user:{}, 出现异常",user,e);
            return ResponseVo.errRest("批量修改商品信息导入异常");
        }
    }

    private Map<String, Integer> getErpCodeCountMap(List<SkuBatchUpdateVo> vos, Map<String, String> barcodeErpCodeMap, List<String> ignoreBarcodes) {
        Map<String, Integer> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(vos)) {
            return resultMap;
        }
        List<String> erpCodeList = vos.stream().filter(item -> {
            String erpCodeInDB = barcodeErpCodeMap.get(item.getBarcode());
            String erpCode = item.getErpCode();
            if (!Objects.equals(erpCodeInDB, erpCode) && StringUtils.isNotBlank(erpCode) && !erpCode.equals("*")) {
                return true;
            }
            ignoreBarcodes.add(item.getBarcode());
            return false;
        }).map(item -> item.getErpCode()).distinct().collect(Collectors.toList());
        List<List<String>> partition = Lists.partition(erpCodeList, 200);
        for (List<String> erpCodes : partition) {
            Map<String, Integer> orgIdErpCodeCountMap = productSkuRemoteAdapter.getErpCodeCountMap(erpCodes);
            if (MapUtils.isNotEmpty(orgIdErpCodeCountMap)) {
                resultMap.putAll(orgIdErpCodeCountMap);
            }
        }
        return resultMap;
    }

    private Map<String, Integer> getBarcodeActivityTypeMap(List<SkuBatchUpdateVo> vos, Map<String, String> barcodeErpCodeMap) throws ServiceException {
        List<String> barcodes = vos.stream().map(item -> item.getBarcode()).distinct().collect(Collectors.toList());
        Map<String, Integer> allBarcodeActivityTypeMap = Maps.newHashMap();
        List<List<String>> partition = Lists.partition(barcodes, 200);
        for (List<String> barcodeSubList : partition) {
            List<PopSkuDto> popSkuDtos = productSkuRemoteAdapter.findSkuByBarCodes(barcodeSubList);
            Map<Object, PopSkuDto> barcodeEntityMap = popSkuDtos.stream().collect(Collectors.toMap(item -> item.getBarcode(), Function.identity()));
            Map<String, Integer> barcodeActivityTypeMap = popSkuDtos.stream().filter(f -> f.getActivityType() != null).collect(Collectors.toMap(item -> item.getBarcode(), item -> item.getActivityType()));
            allBarcodeActivityTypeMap.putAll(barcodeActivityTypeMap);

            //<barcode,erpCode>
            Map<String, String> barcodeErpCodeSubMap = popSkuDtos.stream().filter(f -> f != null && f.getBarcode() != null && f.getErpCode() != null).collect(Collectors.toMap(item -> item.getBarcode(), item -> item.getErpCode()));
            barcodeErpCodeMap.putAll(barcodeErpCodeSubMap);

            vos.stream().filter(item -> barcodeSubList.contains(item.getBarcode())).forEach(item -> {
                PopSkuDto popSkuDto = barcodeEntityMap.get(item.getBarcode());
                if (popSkuDto != null) {
                    item.setOrgId(popSkuDto.getOrgId());
                }
            });
        }
        return allBarcodeActivityTypeMap;
    }


    @GetMapping(value = "/batchUpdateSkuView")
    public String batchUpdateSkuView(Model model){
        model.addAttribute("fileHost",hostUrl);
        model.addAttribute("rowSize",productBatchUpdateConfig.getMaxRows());
        model.addAttribute("fileSize",productBatchUpdateConfig.getMaxFileSize());
        return "product/batchUpdate";
    }


    @GetMapping(value = "/copyMeView")
    public String copyMeView(Model model){
        model.addAttribute("fileHost",hostUrl);
        model.addAttribute("barcodeRowSize",maxCopySizeByBarcode);
        model.addAttribute("standardIdRowSize",maxCopySizeByStandardId);
        model.addAttribute("fileSize",productBatchUpdateConfig.getMaxFileSize());
        return "product/copyMeSku";
    }

    @PostMapping(value = "/copyMeSkuWithBarcode")
    @ResponseBody
    public ResponseVo<Integer> copyMeSkuWithBarcode(String barcodeStr,String fieldStr){
         try{
             validAuthority(meCopyUsers,"商品编码拷贝中台商品信息");
             LOG.info("ProductController.copyMeSkuWithBarcode#barcodeStr:{},fieldStr:{}",barcodeStr,fieldStr);
             if(StringUtils.isEmpty(barcodeStr)||StringUtils.isEmpty(fieldStr)){
                 return ResponseVo.errRest("请填写要输入的商品编码和字段");
             }
             List<String> barcodes = Arrays.stream(barcodeStr.split(","))
                     .map(StringUtils::trimToNull).filter(item->item!=null).collect(Collectors.toList());
             List<String> fields = Arrays.stream(fieldStr.split(","))
                     .map(StringUtils::trimToNull).filter(item->item!=null).collect(Collectors.toList());
             if(CollectionUtils.isEmpty(barcodes)||CollectionUtils.isEmpty(fields)){
                 return ResponseVo.errRest("请填写要输入的商品编码和字段");
             }
             if(barcodes.size()>maxCopySizeByBarcode){
                 return ResponseVo.errRest("一次最多拷贝"+maxCopySizeByBarcode+"个商品");
             }
              int result = productSkuRemoteAdapter.synProductFromMe(getUser().getRealName(),barcodes,null,fields);
             LOG.info("ProductController.copyMeSkuWithBarcode#barcodeStr:{},fieldStr:{} return {}",barcodeStr,fieldStr,JSON.toJSONString(result));
             return ResponseVo.successResult(result);
         }catch (Exception e){
             LOG.error("ProductController.copyMeSkuWithBarcode#barcodeStr:{},fieldStr:{} 异常",barcodeStr,fieldStr,e);
             return ResponseVo.errRest(e.getMessage());
         }
    }

    @PostMapping(value = "/copyMeSkuWithStandardId")
    @ResponseBody
    public ResponseVo<Integer> copyMeSkuWithStandardId(String standardIdStr,String fieldStr){
        try{
            validAuthority(meCopyUsers,"标准库Id拷贝中台信息");
            LOG.info("ProductController.copyMeSkuWithStandardId#standardIdStr:{},fieldStr:{}",standardIdStr,fieldStr);
            if(StringUtils.isEmpty(standardIdStr)||StringUtils.isEmpty(fieldStr)){
                return ResponseVo.errRest("请填写要输入的商品字段和标准库ID");
            }
            List<String> standardIds = Arrays.stream(standardIdStr.split(","))
                    .map(StringUtils::trimToNull).filter(item->item!=null).collect(Collectors.toList());
            List<String> fields = Arrays.stream(fieldStr.split(","))
                    .map(StringUtils::trimToNull).filter(item->item!=null).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(standardIds)||CollectionUtils.isEmpty(fields)){
                return ResponseVo.errRest("请填写要输入的商品字段和标准库ID");
            }
            if(standardIds.size()>maxCopySizeByStandardId){
                return ResponseVo.errRest("一次最多拷贝"+maxCopySizeByStandardId+"个标准库ID");
            }
            int result = productSkuRemoteAdapter.synProductFromMe(getUser().getRealName(),null,standardIds,fields);
            LOG.info("ProductController.copyMeSkuWithStandardId#standardIdStr:{},fieldStr:{} return {}",standardIdStr,fieldStr,JSON.toJSONString(result));
            return ResponseVo.successResult(result);
        }catch (Exception e){
            LOG.error("ProductController.copyMeSkuWithStandardId#standardIdStr:{},fieldStr:{} 异常",standardIdStr,fieldStr,e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    /**
     * 状态下拉选项
     * @return
     */
    @GetMapping(value = "/statusSelect")
    @ResponseBody
    public ResponseVo<List<DictVo>> statusSelect() {
        try {
            LOG.info("ProductController.statusSelect#:");
            List<DictVo> result = Arrays.stream(PopSkuStatus.values()).filter(item->item.getValue()!=7).map(item -> new DictVo(String.valueOf(item.getValue()), item.getName())).collect(Collectors.toList());
            LOG.info("ProductController.statusSelect# return {}", JSON.toJSONString(result));
            return ResponseVo.successResult(result);

        } catch (Exception e) {
            LOG.error("ProductController.statusSelect# 异常", e);
            return ResponseVo.errRest("查询异常");
        }
    }

    @PostMapping(value = "/copyMeSkuFromExcel")
    @ResponseBody
    public ResponseVo<Integer> copyMeSkuFromExcel(String fieldStr,@RequestParam("file") MultipartFile file){
        try{
            validAuthority(meCopyUsers,"excel拷贝中台数据");
            ImportParams importParams = new ImportParams();
            List<SkuMeCopyVo> vos = ExceImportWarpUtil.importExcel(file,SkuMeCopyVo.class,importParams,productBatchUpdateConfig.getMaxFileSize(),Math.max(maxCopySizeByBarcode,maxCopySizeByStandardId),null);
            vos.forEach(item->{
                item.setBarcode(StringUtils.trimToNull(item.getBarcode()));
                item.setStandardId(StringUtils.trimToNull(item.getStandardId()));
                if(item.getBarcode()!=null){
                    item.setStandardId(null);
                }
            });
            List<String> barcodes = vos.stream().filter(item->item.getBarcode()!=null).map(item->item.getBarcode()).collect(Collectors.toList());
            List<String> standardIds = vos.stream().filter(item->item.getStandardId()!=null).map(item->item.getStandardId()).collect(Collectors.toList());
            List<String> fields = Arrays.stream(fieldStr.split(","))
                    .map(StringUtils::trimToNull).filter(item->item!=null).collect(Collectors.toList());
            if((CollectionUtils.isEmpty(standardIds)&&CollectionUtils.isEmpty(barcodes))||CollectionUtils.isEmpty(fields)){
                return ResponseVo.errRest("请填写要输入的商品字段，商品编码或标准库ID");
            }
            if(standardIds.size()>maxCopySizeByStandardId){
                return ResponseVo.errRest("一次最多拷贝"+maxCopySizeByStandardId+"个标准库ID");
            }
            if(barcodes.size()>maxCopySizeByBarcode){
                return ResponseVo.errRest("一次最多拷贝"+maxCopySizeByBarcode+"个商品");
            }
            int result = productSkuRemoteAdapter.synProductFromMe(getUser().getRealName(),barcodes,standardIds,fields);
            LOG.info("ProductController.copyMeSkuFromExcel#fieldStr:{} return {}",fieldStr,JSON.toJSONString(result));
            return ResponseVo.successResult(result);
        }catch (Exception e){
            LOG.error("ProductController.copyMeSkuFromExcel#fieldStr:{} 异常",fieldStr,e);
            return ResponseVo.errRest(e.getMessage());
        }
    }


    @GetMapping(value = "/stockLogView")
    @ApiOperation("查看库存日志")
    public String stockLogView(String barcode,Model model){
        model.addAttribute("barcode",barcode);
        PopSkuDto sku = productSkuRemoteAdapter.getSkuByBarcode(barcode);
        if(sku!=null){
            model.addAttribute("showName",sku.getShowName());
            model.addAttribute("erpCode",sku.getErpCode());
        }
        return "product/stockLogView";
    }

    @GetMapping(value = "/stockLogs")
    @ResponseBody
    public Page<ProductStockLogVo> stockLogs(String barcode, @RequestParam(defaultValue = "0") Integer offset,@RequestParam(defaultValue = "10") Integer limit){
        try {
            LOG.info("ProductV2Controller.stockLogs#barcode:{},pageNum:{},pageSize:{}", JSON.toJSONString(barcode), JSON.toJSONString(offset), JSON.toJSONString(limit));
            if (StringUtils.isBlank(barcode)) {
                return new Page<>();
            }
            PopSkuDto popSkuDto = productSkuRemoteAdapter.getSkuByBarcode(barcode);
            if (popSkuDto == null) {
                return new Page<>();
            }
            CorporationDto corporationDto = corporationRemote.queryCorpBaseByOrgId(popSkuDto.getOrgId());
            LOG.info("ProductV2Controller.stockLogs#orgId:{},corporationDto:{}", popSkuDto.getOrgId(), JSON.toJSONString(corporationDto));
            if (corporationDto == null) {
                return new Page<>();
            }
            SkuStockLogParamDTO param = new SkuStockLogParamDTO();
            if (Objects.equals(popSkuDto.getActivityType(), ActivityTypeEnum.COMMON.getCode())) {
                param.setSkuId(popSkuDto.getCsuid());
            } else {
                param.setSkuId(popSkuDto.getOriginalCsuid());
            }
            param.setProductOwnerId(corporationDto.getProductOwnerId());
            param.setBranchCode(corporationDto.getBranchCode());
            param.setPageNum(offset/limit+1);
            param.setPageSize(limit);
            BPageDto<EcpProductOwnerStockLogDTO> pages = ecSkuRemoteAdapter.selectStock(param);
            Page<ProductStockLogVo> result = ProductSkuConvertHelper.convertStockLog(pages,param);
            List<ProductStockLogVo> rows = result.getRows();
            if (!CollectionUtils.isEmpty(rows)) {
                rows.stream().forEach(item -> item.setBarcode(barcode));
            }
            LOG.info("ProductV2Controller.stockLogs#barcode:{},pageNum:{},pageSize:{} return {}", JSON.toJSONString(barcode), JSON.toJSONString(offset), JSON.toJSONString(limit), JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            LOG.error("ProductV2Controller.stockLogs#barcode:{},pageNum:{},pageSize:{} 异常", JSON.toJSONString(barcode), JSON.toJSONString(offset), JSON.toJSONString(limit), e);
            Page<ProductStockLogVo> pageInfo = new Page<>();
            pageInfo.setTotal(0L);
            pageInfo.setRows(Lists.newArrayList());
            pageInfo.setPageCount(0);
            return pageInfo;
        }
    }

    @ResponseBody
    @GetMapping(value = "/resentCallback/{orgId}")
    public ResponseVo<Boolean> resentCallback(@PathVariable("orgId") String orgId) {
        try {
            Boolean resentCallback = popSkuAuditApi.getResentCallbackOrgIds(orgId);
            return ResponseVo.successResult(resentCallback);
        }catch (Exception e){
            LOG.error("resentCallback orgId:{} 异常", orgId, e);
            return ResponseVo.errRest("出现未知错误");
        }
    }


    @PostMapping(value = "/copyActivitySkuTest")
    @ResponseBody
    public ResponseVo<List<PopSkuCopyResultDto>> copyActivitySkuTest(@RequestBody List<PopSkuCopyParamDto> params) {
        try {
            String user = getUser().getUsername();
            LOG.info("copyActivitySkuTest user:{}", user);
            if (!Objects.equals(activityCopyTestUser, user)) {
                return ResponseVo.errRest("没有操作权限");
            }
            LOG.info("copyActivitySkuTest params:{}", JSON.toJSONString(params));
            ApiRPCResult<List<PopSkuCopyResultDto>> rest = popSkuActivityApi.copySku(params);
            LOG.info("copyActivitySkuTest params:{} rest:{}", JSON.toJSONString(params), JSON.toJSONString(rest));
            if (rest.isSuccess()) {
                return ResponseVo.successResult(rest.getData());
            }
            return ResponseVo.errRest(rest.getErrMsg());
        } catch (Exception e) {
            LOG.error("copyActivitySkuTest params:{} 异常", JSON.toJSONString(params), e);
            return ResponseVo.errRest("出现未知错误");
        }
    }
}
