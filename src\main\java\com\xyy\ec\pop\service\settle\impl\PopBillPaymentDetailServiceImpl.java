package com.xyy.ec.pop.service.settle.impl;

import com.xyy.ec.pop.dao.PopBillPaymentDetailMapper;
import com.xyy.ec.pop.po.PopBillPaymentDetailPo;
import com.xyy.ec.pop.service.settle.PopBillPaymentDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/1 10:50
* @table
*/
@Slf4j
@Service
public class PopBillPaymentDetailServiceImpl implements PopBillPaymentDetailService {

    @Autowired
    private PopBillPaymentDetailMapper popBillPaymentDetailMapper;

    @Override
    public int insert(PopBillPaymentDetailPo record) {
        return popBillPaymentDetailMapper.insert(record);
    }

    @Override
    public int insertSelective(PopBillPaymentDetailPo record) {
        return popBillPaymentDetailMapper.insertSelective(record);
    }

    @Override
    public PopBillPaymentDetailPo selectByPrimaryKey(Long id) {
        return popBillPaymentDetailMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(PopBillPaymentDetailPo record) {
        return popBillPaymentDetailMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PopBillPaymentDetailPo record) {
        return popBillPaymentDetailMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<PopBillPaymentDetailPo> queryPopBillPaymentDetail(PopBillPaymentDetailPo popBillPaymentDetailPo, Integer pageNum, Integer pageSize) {
        return popBillPaymentDetailMapper.queryPopBillPaymentDetail(popBillPaymentDetailPo,pageNum,pageSize);
    }

    @Override
    public Long queryPopBillPaymentDetailCount(List<String> flowNoList) {
        return popBillPaymentDetailMapper.queryPopBillPaymentDetailCount(flowNoList);
    }

    @Override
    public List<PopBillPaymentDetailPo> queryPopBillPaymentDetailByFlowNoList(List<String> flowNoList) {
        return popBillPaymentDetailMapper.queryPopBillPaymentDetailByFlowNoList(flowNoList);
    }
}
