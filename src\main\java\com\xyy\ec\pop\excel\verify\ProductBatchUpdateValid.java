package com.xyy.ec.pop.excel.verify;

import com.xyy.ec.pop.config.ProductBatchUpdateConfig;
import com.xyy.ec.pop.server.api.product.enums.ActivityTypeEnum;
import com.xyy.ec.pop.vo.SkuBatchUpdateVo;
import com.xyy.me.product.service.read.dto.TotalDictionaryReadDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;


import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 商品批量更新校验
 * <AUTHOR>
 */
public class ProductBatchUpdateValid {
    /**
     * 小数
     */
    public final static String NUM_DOUBLE_WITH_ZERO = "^(0|[1-9][0-9]*)(\\.\\d{1,2})?$";
    public static int sellingPropositionMaxLength = 25;
    public static int remarkLength = 100;


    public static void trim(List<SkuBatchUpdateVo> vos) {
        vos.forEach(vo -> {
            vo.setErpCode(StringUtils.trimToNull(vo.getErpCode()));
            vo.setCommonName(StringUtils.trimToNull(vo.getCommonName()));
            vo.setProductName(StringUtils.trimToNull(vo.getProductName()));
            vo.setShowName(StringUtils.trimToNull(vo.getShowName()));
            vo.setBarcode(StringUtils.trimToNull(vo.getBarcode()));
            vo.setStatus(StringUtils.trimToNull(vo.getStatus()));
            vo.setSkuCategory(StringUtils.trimToNull(vo.getSkuCategory()));
            vo.setBusinessFirstCategoryName(StringUtils.trimToNull(vo.getBusinessFirstCategoryName()));
            vo.setBusinessSecondCategoryName(StringUtils.trimToNull(vo.getBusinessSecondCategoryName()));
            vo.setBusinessThirdCategoryName(StringUtils.trimToNull(vo.getBusinessThirdCategoryName()));
            vo.setBusinessFourthCategoryName(StringUtils.trimToNull(vo.getBusinessFourthCategoryName()));
            vo.setCode(StringUtils.trimToNull(vo.getCode()));
            vo.setApprovalNumber(StringUtils.trimToNull(vo.getApprovalNumber()));
            vo.setManufacturer(StringUtils.trimToNull(vo.getManufacturer()));
            vo.setDosageForm(StringUtils.trimToNull(vo.getDosageForm()));
            vo.setDrugClassification(StringUtils.trimToNull(vo.getDrugClassification()));
            vo.setProductUnit(StringUtils.trimToNull(vo.getProductUnit()));
            vo.setSpec(StringUtils.trimToNull(vo.getSpec()));
            vo.setSuggestPrice(StringUtils.trimToNull(vo.getSuggestPrice()));
            vo.setSellingProposition1(StringUtils.trimToNull(vo.getSellingProposition1()));
            vo.setSellingProposition2(StringUtils.trimToNull(vo.getSellingProposition2()));
            vo.setSellingProposition3(StringUtils.trimToNull(vo.getSellingProposition3()));
        });
    }

    public static void valid(List<SkuBatchUpdateVo> vos, Map<String, Integer> barcodeActivityTypeMap, Map<String, Integer> orgIdErpCodeCountMap, List<String> ignoreBarcodes,List<String> orgIdList,boolean allResentCallback) {
        Map<String,Long> countMap = vos.stream().collect(Collectors.groupingBy(item->item.getBarcode(),Collectors.counting()));
        vos.forEach(vo -> {
            StringBuilder errMsg = new StringBuilder();
            Integer activityType = barcodeActivityTypeMap.get(vo.getBarcode());
            if (!Objects.equals(activityType, ActivityTypeEnum.COMMON.getCode())) {
                errMsg.append("商品为"+(ActivityTypeEnum.getOuterNameByCode(activityType))+"，请在表格里剔除这类商品后再提交");
                vo.setErrorMsg(errMsg.toString());
                vo.setFailed(true);
                //如果拼团品，无需再往下校验
                return;
            }
            String key = String.format("%s_%s", vo.getOrgId(), vo.getErpCode());
            if (orgIdErpCodeCountMap.get(key) != null && orgIdErpCodeCountMap.get(key) > 0 && !ignoreBarcodes.contains(vo.getBarcode())) {
                errMsg.append("商品ERP编码与已发布的普通商品ERP编码重复，请重新填写商品ERP编码,");
            }
            if(countMap.get(vo.getBarcode())>1){
                errMsg.append("商品重复导入,");
            }
            if(vo.getStatus()!=null&& !ProductBatchUpdateConfig.statusMap.containsKey(vo.getStatus())){
                errMsg.append("错误的商品状态,");
            }
            //灰度商业不允许更改状态
            if (allResentCallback && vo.getStatus() != null  && (orgIdList.contains(vo.getOrgId()) || CollectionUtils.isEmpty(orgIdList))){
                errMsg.append("该商业为上品流程灰度店铺，已上报中台不允许审核,");
            }
             else if (vo.getStatus() != null && vo.getStatus().equals("审核未通过") && StringUtils.isEmpty(vo.getRemark())) {
                errMsg.append("审核不通过必须填写备注,");
            }
            if (vo.getDrugClassification() != null && !ProductBatchUpdateConfig.drugsMap.containsKey(vo.getDrugClassification())) {
                errMsg.append("错误的处方类型,");
            }
            if(vo.getSuggestPrice()!=null&&!vo.getSuggestPrice().matches(NUM_DOUBLE_WITH_ZERO)){
                errMsg.append("建议零售价格式错误,");
            }
            if (vo.getDosageForm() != null && !ProductBatchUpdateConfig.DOSAGE_FORM_VALUE.contains(vo.getDosageForm())) {
                errMsg.append("错误的剂型,");
            }
            if (vo.getSellingProposition1() == null && (vo.getSellingProposition2()!=null||vo.getSellingProposition3()!=null)) {
                errMsg.append("若维护卖点，则必须维护卖点1,");
            }
            if((vo.getSellingProposition1()!=null&&vo.getSellingProposition1().length()>sellingPropositionMaxLength)){
                errMsg.append("每个卖点信息最多"+sellingPropositionMaxLength+"个字符");
            }

            if (vo.getRemark() != null && vo.getRemark().length() > remarkLength) {
                errMsg.append("备注最多不超过100字,");
            }
            if (vo.getCommonName() == null && vo.getProductName() == null &&
                    vo.getShowName() == null && vo.getErpCode() == null &&
                    vo.getStatus() == null &&vo.getSkuCategory()==null&&
                    vo.getBusinessFirstCategoryName()==null&&vo.getBusinessSecondCategoryName()==null&&
                    vo.getBusinessThirdCategoryName()==null&&vo.getBusinessFourthCategoryName()==null&&
                    vo.getCode()==null&&vo.getApprovalNumber()==null&&
                    vo.getManufacturer()==null&&vo.getDrugClassification()==null&&
                    vo.getDosageForm()==null&&vo.getProductUnit()==null&&vo.getSpec()==null
                    &&vo.getSuggestPrice()==null&&vo.getSellingProposition1()==null
                    &&vo.getSellingProposition2()==null&&vo.getSellingProposition3()==null) {
                errMsg.append("没有可更新项,");
            }
            //4级分类填写的数量
            int businessCount = 0;
            businessCount = vo.getBusinessFirstCategoryName()==null?businessCount:businessCount+1;
            businessCount = vo.getBusinessSecondCategoryName()==null?businessCount:businessCount+1;
            businessCount = vo.getBusinessThirdCategoryName()==null?businessCount:businessCount+1;
            businessCount = vo.getBusinessFourthCategoryName()==null?businessCount:businessCount+1;
            if(businessCount!=0&&businessCount!=4){
                errMsg.append("四级分类必须同时更新,");
            }
            if (errMsg.length() > 0) {
                vo.setErrorMsg(errMsg.toString().substring(0, errMsg.length() - 1));
                vo.setFailed(true);
            }
        });
    }

    public static void validAndSetBusiness(List<SkuBatchUpdateVo> vos, Map<Integer, List<TotalDictionaryReadDto>> map) {
        vos.forEach(item->{
            item.setBusinessFirstCategoryCode(getCateCodeByName("0",item.getBusinessFirstCategoryName(),map));
            if(item.getBusinessFirstCategoryCode()==null){
                item.setFailed(true);
                item.setErrorMsg("没有找到对应一级分类");
                return;
            }
            item.setBusinessSecondCategoryCode(getCateCodeByName(item.getBusinessFirstCategoryCode(),item.getBusinessSecondCategoryName(),map));
            if(item.getBusinessSecondCategoryCode()==null){
                item.setFailed(true);
                item.setErrorMsg("一级分类下没有找到设置的二级分类");
                return;
            }
            item.setBusinessThirdCategoryCode(getCateCodeByName(item.getBusinessSecondCategoryCode(),item.getBusinessThirdCategoryName(),map));
            if(item.getBusinessThirdCategoryCode()==null){
                item.setFailed(true);
                item.setErrorMsg("二级分类下没有找到设置的三级分类");
                return;
            }
            item.setBusinessFourthCategoryCode(getCateCodeByName(item.getBusinessThirdCategoryCode(),item.getBusinessFourthCategoryName(),map));
            if(item.getBusinessFourthCategoryCode()==null){
                item.setFailed(true);
                item.setErrorMsg("三级分类下没有找到设置的四级分类");
                return;
            }
        });
    }

    private static String getCateCodeByName(String parentId, String categoryName, Map<Integer, List<TotalDictionaryReadDto>> map) {
        if(parentId==null){
            return null;
        }
        List<TotalDictionaryReadDto> list = map.get(NumberUtils.toInt(parentId));
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        for(TotalDictionaryReadDto dto:list){
            if(Objects.equals(dto.getDictName(),categoryName)){
                return dto.getId().toString();
            }
        }
        return null;
    }
}
