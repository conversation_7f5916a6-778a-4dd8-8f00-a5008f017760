package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.server.api.seller.dto.PopReceivingReportDto;
import com.xyy.ec.pop.vo.PopReceivingReportVo;

public class PopReceivingReportHelper {

    public static PopReceivingReportVo convertDtoToVo(PopReceivingReportDto dto) {
        PopReceivingReportVo popReceivingReportVo = new PopReceivingReportVo();
        popReceivingReportVo.setId(dto.getId());
        popReceivingReportVo.setActuallyReceivedDate(dto.getActuallyReceivedDate());
        popReceivingReportVo.setReceivablesType(dto.getReceivablesType());
        popReceivingReportVo.setBranchCode(dto.getBranchCode());
        popReceivingReportVo.setOrgId(dto.getOrgId());
        popReceivingReportVo.setBusinessName(dto.getBusinessName());
        popReceivingReportVo.setOrderNo(dto.getOrderNo());
        popReceivingReportVo.setPayWay(dto.getPayWay());
        popReceivingReportVo.setCommodityAmount(dto.getCommodityAmount());
        popReceivingReportVo.setFreightAmount(dto.getFreightAmount());
        popReceivingReportVo.setOrderAmount(dto.getOrderAmount());
        popReceivingReportVo.setStoresDiscount(dto.getStoresDiscount());
        popReceivingReportVo.setPlatformDiscount(dto.getPlatformDiscount());
        popReceivingReportVo.setStoresActivityDiscount(dto.getStoresActivityDiscount());
        popReceivingReportVo.setPlatformActivityDiscount(dto.getPlatformActivityDiscount());
        popReceivingReportVo.setActuallyPaidAmount(dto.getActuallyPaidAmount());
        popReceivingReportVo.setCommission(dto.getCommission());
        popReceivingReportVo.setActuallyReceivedAmount(dto.getActuallyReceivedAmount());
        popReceivingReportVo.setRemark(dto.getRemark());
        popReceivingReportVo.setCreateTime(dto.getCreateTime());
        popReceivingReportVo.setCreateBy(dto.getCreateBy());
        popReceivingReportVo.setUpdateTime(dto.getUpdateTime());
        popReceivingReportVo.setUpdateBy(dto.getUpdateBy());
        popReceivingReportVo.setMetaData1(dto.getMetaData1());
        popReceivingReportVo.setMetaData2(dto.getMetaData2());
        popReceivingReportVo.setMetaData3(dto.getMetaData3());
        popReceivingReportVo.setProvinceCode(dto.getProvinceCode());
        popReceivingReportVo.setPaymentChannel(dto.getPaymentChannel());
        popReceivingReportVo.setBranchName(dto.getBranchName());
        popReceivingReportVo.setName(dto.getName());
        popReceivingReportVo.setMerchantId(dto.getMerchantId());
        popReceivingReportVo.setMerchantName(dto.getMerchantName());
        return popReceivingReportVo;
    }

}
