package com.xyy.ec.pop.model;



import com.xyy.ec.pop.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 订单明细信息
 * <AUTHOR>
 */
@ApiModel("退款VO实体")
public class OrderRefundVO extends BaseEntity {
	
	private static final long serialVersionUID = -8523201784532583137L;
	
	/** 商品图片 */
	@ApiModelProperty("商品图片")
	private String imageUrl;			
	
	/** 生产厂家 */
	@ApiModelProperty("生产厂家")
	private String manufacturer;
	
	/** 商品编号 */
	@ApiModelProperty("商品编号")
	private Long productId;
	
	/** 商品名称 */
	@ApiModelProperty("商品名称")
	private String productName;
	
	/** 商品数量 */
	@ApiModelProperty("商品数量")
	private Integer purchaseNumber;
	
	/** 规格 */
	@ApiModelProperty("规格")
	private String spec;
	
	/** 批准文号 */
	@ApiModelProperty("批准文号")
	private String approvalNumber;
	
	/** 商品状态 */
	@ApiModelProperty("商品状态")
	private Integer productStatus;
	
	/** 商品可购买数量 */
	@ApiModelProperty("商品可购买数量")
	private Integer productAvailableqty;
	
	/*****************扩展字段********************/
    /** 是否属于返利商品 0:不是 1:是 */
	@ApiModelProperty("是否属于返利商品")
    private Integer balanceFlag;

    /** 不返利商品文案 */
	@ApiModelProperty("不返利商品文案")
    private String blackProductText;

	@ApiModelProperty("抵扣余额明细")
    private BigDecimal useBalanceAmount;   /** 抵扣余额明细 */
	@ApiModelProperty("单品优惠金额")
    private BigDecimal discountAmount;     /** 单品优惠金额, 单品优惠总金额 = 单品优惠金额 + 抵扣余额明细 */
	@ApiModelProperty("预返余额明细")
    private BigDecimal balanceAmount;     /** 预返余额明细 */
	@ApiModelProperty("单品实付金额")
    private BigDecimal realPayAmount;       //单品实付金额
    
    /** 商品单价 */
	@ApiModelProperty("商品单价")
    private BigDecimal productPrice;

    /** 商品数量 */
	@ApiModelProperty("商品数量")
    private Integer productAmount;


    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getPurchaseNumber() {
        return purchaseNumber;
    }

    public void setPurchaseNumber(Integer purchaseNumber) {
        this.purchaseNumber = purchaseNumber;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }
    


	public String getApprovalNumber() {
		return approvalNumber;
	}

	public void setApprovalNumber(String approvalNumber) {
		this.approvalNumber = approvalNumber;
	}

	public Integer getProductStatus() {
		return productStatus;
	}

	public void setProductStatus(Integer productStatus) {
		this.productStatus = productStatus;
	}
	
    public Integer getProductAvailableqty() {
		return productAvailableqty;
	}

	public void setProductAvailableqty(Integer productAvailableqty) {
		this.productAvailableqty = productAvailableqty;
	}


    public Integer getBalanceFlag() {
        return balanceFlag;
    }

    public void setBalanceFlag(Integer balanceFlag) {
        this.balanceFlag = balanceFlag;
    }

    public String getBlackProductText() {
        return blackProductText;
    }

    public void setBlackProductText(String blackProductText) {
        this.blackProductText = blackProductText;
    }

	public BigDecimal getUseBalanceAmount() {
		return useBalanceAmount;
	}

	public void setUseBalanceAmount(BigDecimal useBalanceAmount) {
		this.useBalanceAmount = useBalanceAmount;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public BigDecimal getBalanceAmount() {
		return balanceAmount;
	}

	public void setBalanceAmount(BigDecimal balanceAmount) {
		this.balanceAmount = balanceAmount;
	}

	public BigDecimal getRealPayAmount() {
		return realPayAmount;
	}

	public void setRealPayAmount(BigDecimal realPayAmount) {
		this.realPayAmount = realPayAmount;
	}

	public BigDecimal getProductPrice() {
		return productPrice;
	}

	public void setProductPrice(BigDecimal productPrice) {
		this.productPrice = productPrice;
	}

	public Integer getProductAmount() {
		return productAmount;
	}

	public void setProductAmount(Integer productAmount) {
		this.productAmount = productAmount;
	}
    
    
}
