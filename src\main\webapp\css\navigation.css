
div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td{margin:0;padding:0;}
object,iframe,abbr,acronym,address,code,del,dfn,em,form,label,legend,article,aside,details,dialog,figcaption,figure,footer,header,hgroup,menu,nav,section,summary{margin:0;padding:0;border:0; }
table{border-collapse:collapse;border-spacing:0;}  fieldset,img{border-width:0;}
body{margin:0;padding:0;font:12px/20px Helvetica,Roboto,Arial,sans-serif,Sim<PERSON><PERSON>,"Microsoft Yahei";background:#6699ff;}
#box{text-align:left;width:1180px;margin:0 auto; color:#333333;width:100%;max-width:1220px }
ul,li,dl,dd,ol{ list-style:none;}
a:focus{outline:0;}
.main{width:100%;height:100%;}

.logo{ margin-left:20px; margin-bottom:40px; font-size:16px;}
.logo img{ width:180px;}
.logo span{ color:#ce7d22; font-weight:bold; margin-left:20px; }

.middleContent {padding-top:30px;overflow:hidden; }	/*    width:100%; margin-left:auto; margin-right:auto; */
.middleContent dl{float:left;margin-left:1.63%;width:47.377%; border:1px solid #fff; margin-bottom:50px; height:226px; padding-top:10px;color:#fff;}	/* width:578px;  ;margin-left:20px; width:47.377%;  float:left;margin-left:20px;width:588px;*/

.middleContent dt{text-align:center; margin:-20px 0 16px 40px;position:relative;background:#6699ff; width:126px; font-size:14px;}
.middleContent  dd{overflow:hidden;zoom:1;}
.middleContent dd a{text-decoration:none; text-align:center; float:left; width:19.98%; height:98px;color:#fff;border:none; }
.middleContent  dd p{ height:20px; overflow:hidden;}
.middleContent  img{height:auto;max-width:100%; text-align:center;border:none;}

.down{ background:#184962; padding-right:20px; height:50px;}
.down img{ margin-left:30px; display:block; float:right; vertical-align:middle;  cursor:pointer;}

.addbox_bg{ position:absolute; left:0px; top:0px; width:100%; height:100%; z-index:50; display:none;}
.addbox_border{ position:absolute; width:600px; height:300px; z-index:51; left:50%; margin-left:-300px; top:50%; margin-top:-150px;}
.addbox_main{ width:590px; height:290px; margin-left:5px; margin-top:5px; background:#FFF;}
.addbox_title{ width:100%; height:40px; background:#eee; line-height:40px; text-indent:1em; font-weight:bold; color:#333;}
.addbox_name{ margin-left:100px; font-size:12px; margin-top:20px; height:30px; line-height:30px;}
.addbox_name input{ width:200px; margin-left:10px;height:25px; line-height:25px;}
.addbox_upimg{margin-left:100px; font-size:12px; margin-top:20px; height:30px; line-height:30px;}
.addbox_upimg div{ float:left;}
.uptext{ margin-left:10px; width:200px;height:25px; line-height:25px;}
.upBox_btn{ position:relative;}
.fileInput{width:80px;height:33px;overflow:hidden;position:relative; margin-top:-1px; margin-left:10px;}
.upfile{position:absolute;}
.upFileBtn{width:80px;height:33px;cursor:pointer;position:absolute; z-index:100;}
.yesORno{ clear:both; margin-top:20px; margin-left:150px;}
.yesORno input{ width:80px; height:35px; font-size:14px; font-weight:bold;  margin-right:20px;}

