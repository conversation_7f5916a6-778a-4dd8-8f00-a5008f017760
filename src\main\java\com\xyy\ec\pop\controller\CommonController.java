/**
 * Copyright (C), 2015-2019, XXX有限公司
 * FileName: CommonController
 * Author:   danshiyu
 * Date:     2019/11/21 11:49
 * Description: 公共工具
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.xyy.ec.pop.controller;

import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.server.api.Enum.ShareStatusEnum;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.ShareStatusVo;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 〈一句话功能简述〉<br>
 * 〈公共工具〉
 *
 * <AUTHOR>
 * @create 2019/11/21
 * @since 1.0.0
 */
@Api(tags = "公共")
@Slf4j
@Controller
@RequestMapping("util")
public class CommonController extends BaseController{
    @GetMapping("/shareStatus")
    @ResponseBody
    public ResponseVo shareStatus() {
        return ResponseVo.successResult(Arrays.stream(ShareStatusEnum.values()).map(CommonController::shareStatusEnumToVo).collect(Collectors.toList()));
    }

    private static ShareStatusVo shareStatusEnumToVo(ShareStatusEnum anEnum){
        ShareStatusVo shareStatusVo = new ShareStatusVo();
        shareStatusVo.setCode(anEnum.getCode());
        shareStatusVo.setName(anEnum.getName());
        return shareStatusVo;
    }

}

