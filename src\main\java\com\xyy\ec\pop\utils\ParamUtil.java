package com.xyy.ec.pop.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;

/**
 * <AUTHOR>
public class ParamUtil {

    /**
     * 转换一个json格式的字符串到java对象
     *
     * @param json
     * @return 成功返回 Object对象; 失败返回 null
     */
    public static <T> T jsonToObject(String json, Class<T> t) throws Exception {

        if (StringUtil.isEmpty(json)) {
            return null;
        }

        json = EscapeUtil.escapeJavaScript(json);

        T object = JSON.parseObject(json, t);
        return object;
    }

    /**
     * 转换一个java对象到json格式的字符串
     *
     * @param object
     * @return 成功返回 Object对象; 失败返回 null
     */
    public static String objectToJson(Object object) {

        HttpServletRequest request = RequestUtil.getRequest();

        String callback = request.getParameter("jsonp_callback");

        System.out.println("callback--->" + callback);

        return objectToJson(object, callback);

    }

    /**
     * 转换一个java对象到json格式的字符串
     *
     * @param object
     * @return 成功返回 Object对象; 失败返回 null
     */
    public static String objectToJson(Object object, String callback) {

        String json = "";
        try {
            json = JSON.toJSONString(object);
            if (!StringUtil.isEmpty(callback)) {
                json = callback + "(" + json + ")";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return json;
    }
    static int count =0;
    public static void main(String[] args) {

//        String str ="122345";//待排序字符串
//        ArrayList<Character> l=new ArrayList<Character>();//把待排序的字符串分成单个字符并保存进List里面
//        for(int i=0;i<str.length();i++){
//            l.add(str.charAt(i));
//        }
//        StringBuffer sb=new StringBuffer();//用于记录组合的字符串
//        deep(sb,l); //递归，组合字符串
//        System.out.println("共有："+count);
    }

    public static void deep(StringBuffer sb,ArrayList<Character> list){

        //判断是否组合完成，当已经组合好后l.size()=0，不理解可以跳过先
        if(list.size()>0){
            //备份字符串，用于一轮组合完成后，恢复list的原貌，进行下一轮的组合.不理解可以跳过先
            ArrayList<Character> list2=new ArrayList<Character>(list);
            String ostr=sb.toString();

            for(int i=0;i<list.size();i++){//
                sb=new StringBuffer(ostr);//sb作为参数传递，当一轮组合完成后，需要恢复原状，你可以注释掉看看效果
                sb.append(list.get(i));//组合当前这个字符
                list.remove(list.get(i));//移除刚刚组合的这个字符，这样list剩下的字符都是需要排列组合，不会重复，去到最后list。size()=0
                deep(sb,list);//递归，进入下一个字符的组合
                list=new ArrayList(list2);//来到这里表示已经完成以某一个字符在特定的位置的排序已经完成，接着是下一个字符在这个特定的位置的组合了，需要把list还原成原状
            }
        }
        else{
            System.out.println(sb.toString());//sb中的是已经组合好的其中一种字符串
            count+=1;//计算总共有多少种组合
        }
    }
}
