package com.xyy.ec.pop.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.excel.entity.OnlinePayPopBillPaymentExportVo;
import com.xyy.ec.pop.excel.style.SettleExcelExportStyler;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.helper.PopBillPaymentHelper;
import com.xyy.ec.pop.po.PopBillPaymentDetailPo;
import com.xyy.ec.pop.po.PopBillPaymentPo;
import com.xyy.ec.pop.server.api.order.enums.OrderPayTypeEnums;
import com.xyy.ec.pop.server.api.seller.dto.PopShareProfitDto;
import com.xyy.ec.pop.service.settle.PopBillPaymentService;
import com.xyy.ec.pop.service.settle.impl.domain.PopBillPaymentDomainService;
import com.xyy.ec.pop.utils.EncodeUtil;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.settle.PopBillPayStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillPayVo;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 在线支付入账单
* <AUTHOR>
* @date  2020/12/1 10:27
* @table
*/
@Slf4j
@RequestMapping("/onlinePayPopBillPayment")
@Controller
public class OnlinePayPopBillPayController extends BaseController {

    @Autowired
    private PopBillPaymentService popBillPaymentService;
    @Autowired
    private PopBillPaymentDomainService popBillPaymentDomainService;



    /**
     * 列表查询
     * @param popBillPayVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Object list(PopBillPayVo popBillPayVo, PageInfo pageInfo) {
        try {
            if (validateProv(popBillPayVo)){
                return new PageInfo<>();
            }
            popBillPayVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            PageInfo<PopBillPaymentPo> pageInfoPop = popBillPaymentDomainService.queryPopBillList(popBillPayVo, pageInfo);
            return ResponseUtils.returnObjectSuccess(pageInfoPop);
        } catch (ServiceRuntimeException e) {
            log.error("查询在线支付入账单列表异常", e);
            return ResponseUtils.returnException(e);
        }
    }

    private boolean validateProv(PopBillPayVo popBillPayVo) {
        List<Long> provIds = getProvIds(popBillPayVo.getProvId());
        if(CollectionUtils.isEmpty(provIds)){
            return true;
        }
        popBillPayVo.setProvIds(provIds);
        return false;
    }

    /**
     * 列表根据搜索条件统计佣金金额，应结算金额
     * @param popBillPayVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryPopBillPayStatis", method = RequestMethod.GET)
    public Object queryPopBillPayStatis(PopBillPayVo popBillPayVo) {
        try {
            if (validateProv(popBillPayVo)){
                PopBillPayStatisVo popBillPayStatisVo = new PopBillPayStatisVo();
                return ResponseUtils.returnObjectSuccess(popBillPayStatisVo);
            }
            popBillPayVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            PopBillPayStatisVo popBillPaymentPo = popBillPaymentDomainService.queryPopBillPayStatis(popBillPayVo);
            return ResponseUtils.returnObjectSuccess(popBillPaymentPo);
        } catch (ServiceRuntimeException e) {
            log.error("查询统计在线支付入账单异常", e);
            return ResponseUtils.returnException(e);
        }
    }


    /**
     * 查询列表入账单导出的条数  超过5000条数据，前端给出提示
     * @param popBillPayVo
     * @return
     */
    @RequestMapping(value = "/queryExprotBillListCount")
    @ResponseBody
    public Object queryExprotBillListCount(PopBillPayVo popBillPayVo){
        try{
            popBillPayVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            Long count = popBillPaymentDomainService.queryPopBillListCount(popBillPayVo);
            return ResponseUtils.returnObjectSuccess(count);
        }catch (Exception e){
            log.error("查询列表入账单导出的条数异常",e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 导出在线支付入账单
     * @param popBillPayVo
     * @param request
     * @param res
     */
    @RequestMapping(value = "/exportBillPaymemtList", method = RequestMethod.GET)
    public void exportBillPaymemtList(PopBillPayVo popBillPayVo, HttpServletRequest request, HttpServletResponse res){
        try{
            popBillPayVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            Workbook workbook = null;
            ExportParams params = new ExportParams();
            params.setSheetName("入账单");
            params.setType(ExcelType.XSSF);
            params.setColor(IndexedColors.BLUE_GREY.index);
            params.setFreezeCol(2);
            params.setStyle(SettleExcelExportStyler.class);
            int pageNum = 1;
            int limit = 500;
            while(true){
                PageInfo pageInfo = new PageInfo();
                pageInfo.setPageNum(pageNum);
                pageInfo.setPageSize(limit);
                PageInfo<PopBillPaymentPo> resultPage = popBillPaymentDomainService.queryPopBillList(popBillPayVo, pageInfo);
                List<OnlinePayPopBillPaymentExportVo> onlinePayPopBillPaymentExportVos = PopBillPaymentHelper.convertOnlinePayPopBillPaymentExport(resultPage.getList());
                int pageCount = resultPage.getPages();
                workbook = ExcelExportUtil.exportBigExcel(params, OnlinePayPopBillPaymentExportVo.class,onlinePayPopBillPaymentExportVos);
                if(pageNum >= pageCount || pageNum >= 2000){
                    break;
                }
                pageNum++;
            }
            ExcelExportUtil.closeExportBigExcel();
            ServletOutputStream out = res.getOutputStream();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fileName = URLEncoder.encode("入账单-", EncodeUtil.DEFAULT_URL_ENCODING) + sdf.format(new Date());	//对中文进行URL编码，防止乱码
            res.setHeader("Content-type", "application/x-xls; charset=" + EncodeUtil.DEFAULT_URL_ENCODING);
            res.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xlsx");
            res.setContentType("application/msexcel");
            if(null != workbook){
                workbook.write(out);
            }
        }catch (Exception e) {
            log.error("运营后台导出在线支付入账单列表导出异常",e);
        }
    }

    /**
     * 根据入帐单号查询入账单
     * @param flowNo
     * @return
     */
    @RequestMapping(value = "/billPayment",method = RequestMethod.GET)
    @ResponseBody
    public Object billPayment(String flowNo){
        try{
            if(StringUtils.isEmpty(flowNo)){
                return ResponseUtils.returnCommonException("入账单号不能为空");
            }
            PopBillPaymentPo popBillPaymentPo = popBillPaymentService.selectByFlowNo(flowNo);
            //佣金字段合并
            if (popBillPaymentPo != null) {
                popBillPaymentPo.setHireMoney(popBillPaymentPo.getPayableCommission().add(popBillPaymentPo.getHireMoney()));
            }
            return ResponseUtils.returnObjectSuccess(popBillPaymentPo);
        }catch (Exception e){
            log.error("根据入帐单好查询入账单异常",e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 查询入账单明细，带分页
     * @param flowNo
     * @return
     */
    @RequestMapping(value = "/billPaymentDetail",method = RequestMethod.GET)
    @ResponseBody
    public Object billPaymentDetail(String flowNo, PageInfo pageInfo) {
        try{
            if(StringUtils.isEmpty(flowNo)){
                return ResponseUtils.returnCommonException("入账单号不能为空");
            }
            PageInfo<PopBillPaymentDetailPo> popBillPaymentDetailPoPageInfo = popBillPaymentDomainService.queryPopBillPaymentDetail(flowNo, pageInfo);
            return ResponseUtils.returnObjectSuccess(popBillPaymentDetailPoPageInfo);
        }catch (Exception e){
            log.error("查询入账单明细异常");
            return ResponseUtils.returnException(e);
        }

    }

    /**
     * 查询列表入账单明细导出的条数  超过5000条数据，前端给出提示
     * @param popBillPayVo
     * @return
     */
    @RequestMapping(value = "/queryExprotBillPaymentDetailCount")
    @ResponseBody
    public Object queryExprotBillPaymentDetailCount(PopBillPayVo popBillPayVo){
        try{
            popBillPayVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            Long billPaymentDetailCount = popBillPaymentDomainService.queryExprotBillPaymentDetailCount(popBillPayVo);
            return ResponseUtils.returnObjectSuccess(billPaymentDetailCount);
        }catch (Exception e){
            log.error("查询列表入账单导出的条数异常",e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 导出在线支付入账单明细
     * @param popBillPayVo
     * @param request
     * @param res
     */
    @RequestMapping(value = "/exportBillPaymemtDetailList", method = RequestMethod.GET)
    public void exportBillPaymemtDetailList(PopBillPayVo popBillPayVo, HttpServletRequest request, HttpServletResponse res){
        try{
            popBillPayVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            popBillPaymentDomainService.exportOnlineBillPaymemtDetailList(popBillPayVo,res);
        }catch (Exception e) {
            log.error("运营后台导出在线支付入账单列表导出异常",e);
        }
    }

    /**
     * 入帐单列表查询分润失败入帐单数接口
     */
    @RequestMapping(value = "/queryBillShareFailCount", method = RequestMethod.GET)
    @ResponseBody
    public ResponseVo<Integer> queryBillShareFailCount(){
        try{
            return ResponseVo.successResult(popBillPaymentDomainService.queryBillPaymentCount());
        }catch (Exception e) {
            log.error("OnlinePayPopBillPayController.queryBillShareFailCount异常",e);
            return ResponseVo.errRest("查询分润失败的入账单数异常");
        }
    }

    /**
     * 分润失败的入账单重新分润
     */
    @PostMapping(value = "/againShareProfit")
    @ResponseBody
    public ResponseVo againShareProfit(@RequestBody List<PopShareProfitDto> shareProfitDtos) {
        try {
            log.info("OnlinePayPopBillPayController.againShareProfit#shareProfitDtos:{}", JSON.toJSONString(shareProfitDtos));
            if(CollectionUtils.isEmpty(shareProfitDtos)){
                return ResponseVo.errRest("重新分润参数不能为空");
            }
            popBillPaymentDomainService.againShareProfit(shareProfitDtos);
            return ResponseVo.successResultNotData("重新分润推送成功");
        } catch (ServiceRuntimeException e) {
            log.error("OnlinePayPopBillPayController.againShareProfit 自定义异常#shareProfitDtos:{}", JSON.toJSONString(shareProfitDtos), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("OnlinePayPopBillPayController.againShareProfit 异常#shareProfitDtos:{}", JSON.toJSONString(shareProfitDtos), e);
            return ResponseVo.errRest("重新分润推送失败，请稍后重试");
        }
    }

}
