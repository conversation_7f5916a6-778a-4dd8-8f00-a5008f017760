package com.xyy.ec.pop.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pop.remote.MerchantRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class MerchantRpcServiceImpl implements MerchantRpcService {

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    public List<MerchantBussinessDto> selectMerchantByIdList(List<Long> merchantIdList) {
        if (CollectionUtils.isEmpty(merchantIdList)) {
            return null;
        }
        List<MerchantBussinessDto> retList = Lists.newArrayListWithExpectedSize(merchantIdList.size());
        List<List<Long>> partition = Lists.partition(merchantIdList, 200);
        for (List<Long> subList : partition) {
            int merchantIdSize = subList.size();
            Long[] ids = subList.toArray(new Long[merchantIdSize]);
            MerchantBussinessDto merchantQueryParam = new MerchantBussinessDto();
            merchantQueryParam.setIds(ids);
            List<MerchantBussinessDto> merchantInfoListPageInfo = merchantBussinessApi.findMerchantInfoList(merchantQueryParam);
            if (CollectionUtils.isNotEmpty(merchantInfoListPageInfo)) {
                retList.addAll(merchantInfoListPageInfo);
            }
        }
        return retList;
    }

    @Override
    public List<MerchantBussinessDto> findMerchantInfo(MerchantBussinessDto merchantDto) {
        try {
            return merchantBussinessApi.findMerchantInfo(merchantDto);
        } catch (Exception e) {
            log.error("查询药店列表信息异常，e=", e);
        }
        return Collections.emptyList();
    }
}
