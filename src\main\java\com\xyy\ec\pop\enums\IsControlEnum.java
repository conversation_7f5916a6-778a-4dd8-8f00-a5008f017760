package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;


/**
 * 
 * <AUTHOR>
 *
 */
public enum IsControlEnum {


    YES(1,"是"),
    NO(0,"否");

    private int id;
    private  String value;

    IsControlEnum(int id, String value){
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, IsControlEnum> controlMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(IsControlEnum control : IsControlEnum.values()) {
        	controlMaps.put(control.getId(), control);
            maps.put(control.getId(),control.getValue());
        }
    }

    public static String get(int id) {
        return controlMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
