package com.xyy.ec.pop.utils.autoconfigure;

import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.*;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.springframework.beans.factory.InitializingBean;

import java.io.IOException;

/**
 * FtpClient连接池工厂类
 *
 * <AUTHOR>
 */
@Slf4j
public class XyyFtpClientFactory extends BasePooledObjectFactory<FTPClient> implements InitializingBean {

    private XyyFtpClientProperties config;

    public XyyFtpClientFactory(XyyFtpClientProperties config) {
        this.config = config;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        /* 初始化默认值 */
        if (config.getPort() < 0) {
            config.setPort(0);
        }
        //[0, 无穷大)
        if (config.getConnectTimeout() < 0) {
            config.setConnectTimeout(0);
        }
        //[0, 无穷大)
        if (config.getDataTimeout() < 0) {
            config.setDataTimeout(0);
        }
        // 小于等于0表示禁用超时
        if (config.getKeepAliveTimeout() < 0) {
            config.setKeepAliveTimeout(0L);
        }
        // 小于等于0表示禁用超时
        if (config.getControlKeepAliveReplyTimeout() < 0) {
            config.setControlKeepAliveReplyTimeout(0);
        }
        if (StringUtils.isEmpty(config.getEncoding())) {
            config.setEncoding("UTF-8");
        }
        if (config.getTransferFileType() == null || config.getTransferFileType() < 0) {
            config.setTransferFileType(FTP.BINARY_FILE_TYPE);
        }
    }

    @Override
    public FTPClient create() throws Exception {
        FTPClient ftpClient;
        XyyFtpClientProperties.Ftps ftps = config.getFtps();
        if (ftps.isEnable()) {
            FTPSClient ftpsClient;
            if (StringUtils.isNotEmpty(ftps.getProtocol())) {
                ftpsClient = new FTPSClient(ftps.getProtocol(), ftps.isImplicit());
            } else {
                ftpsClient = new FTPSClient(ftps.isImplicit());
            }
            ftpClient = ftpsClient;
        } else {
            XyyFtpClientProperties.Proxy proxy = config.getProxy();
            if (proxy.getHost() != null) {
                ftpClient = new FTPHTTPClient(proxy.getHost(), proxy.getPort(), proxy.getUser(), proxy.getPassword());
            } else {
                ftpClient = new FTPClient();
            }
        }
        // 配置
        /* 相关超时设置 */
        // 连接超时时长，单位毫秒
        if (config.getConnectTimeout() >= 0) {
            ftpClient.setConnectTimeout(config.getConnectTimeout());
        }
        // 获取响应数据超时时长，单位毫秒
        if (config.getDataTimeout() >= 0) {
            ftpClient.setDataTimeout(config.getDataTimeout());
        }
        // 保持连接存活时长，单位秒
        if (config.getKeepAliveTimeout() >= 0) {
            ftpClient.setControlKeepAliveTimeout(config.getKeepAliveTimeout());
        }
        // 保持消息回复存活时长，单位毫秒
        if (config.getControlKeepAliveReplyTimeout() >= 0) {
            ftpClient.setControlKeepAliveReplyTimeout(config.getControlKeepAliveReplyTimeout());
        }
        /* 数据传输的字符编码 */
        if (config.getEncoding() != null) {
            ftpClient.setControlEncoding(config.getEncoding());
        }
        try {
            /* 建立ftp连接 */
            int reply;
            if (config.getPort() > 0) {
                ftpClient.connect(config.getServer(), config.getPort());
            } else {
                ftpClient.connect(config.getServer());
            }
            if (log.isDebugEnabled()) {
                log.debug("FTP Connected to {} on {}", config.getServer(), (config.getPort() > 0 ? config.getPort() : ftpClient.getDefaultPort()));
            }
            reply = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                ftpClient.disconnect();
                throw new PopAdminException("FTP服务器拒绝连接", XyyJsonResultCodeEnum.FTP_CONNECT_FAILURE);
            }
            /* 登录 */
            if (!ftpClient.login(config.getUsername(), config.getPassword())) {
                ftpClient.logout();
                throw new PopAdminException("用户名或密码错误，无法连接到服务器。", XyyJsonResultCodeEnum.FTP_CONNECT_FAILURE);
            }
            if (log.isDebugEnabled()) {
                log.debug("FTP Remote system is {}", ftpClient.getSystemType());
            }
            ftpClient.setFileType(config.getTransferFileType());
            if (BooleanUtils.isTrue(config.isPassiveMode())) {
                ftpClient.enterLocalPassiveMode();
            } else {
                ftpClient.enterLocalActiveMode();
            }
            /*
            设置是否与IPv4一起使用EPSV。在某些情况下是值得启用的。例如，当使用带有NAT的IPv4时，它可能使用一些罕见的配置。
            例如，如果FTP服务器有一个静态的PASV地址(外部网络)，而客户端来自另一个内部网络。
            在这种情况下，PASV命令后的数据连接将失败，而EPSV将通过只获取端口使客户机成功。
             */
            ftpClient.setUseEPSVwithIPv4(BooleanUtils.isTrue(config.isUseEpsvWithIPv4()));
            return ftpClient;
        } catch (Exception e) {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException e1) {
                    log.error("关闭FTP连接失败", e1);
                }
            }
            throw new PopAdminException(e, XyyJsonResultCodeEnum.FTP_CONNECT_FAILURE);
        }
    }

    @Override
    public PooledObject<FTPClient> wrap(FTPClient ftpClient) {
        return new DefaultPooledObject<>(ftpClient);
    }

    @Override
    public void destroyObject(PooledObject<FTPClient> ftpPooled) throws Exception {
        if (ftpPooled == null) {
            return;
        }
        FTPClient ftpClient = ftpPooled.getObject();
        try {
            ftpClient.logout();
        } catch (Exception e) {
        } finally {
            try {
                if (ftpClient.isConnected()) {
                    ftpClient.disconnect();
                }
            } catch (IOException e) {
                log.error("关闭FTP连接失败", e);
            }
        }
    }

    @Override
    public boolean validateObject(PooledObject<FTPClient> ftpPooled) {
        try {
            FTPClient ftpClient = ftpPooled.getObject();
            return ftpClient.sendNoOp();
        } catch (Exception e) {
            log.error("验证FTP连接是否存活失败", e);
        }
        return false;
    }


}
