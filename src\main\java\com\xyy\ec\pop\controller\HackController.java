package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.ResultVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Hack工具类接口</p>
 *
 * <AUTHOR>
 * @Date: 2021/1/12
 */

@Slf4j
@Controller
@RequestMapping("/hack/tool")
public class HackController extends BaseController {



    @GetMapping(value = "/index")
    @ApiOperation("跳转hack生成密钥页面")
    public String index() {
        return "hack/index";
    }

    /**
     * @param orgId
     * @return
     */
    @GetMapping("/makeSecret")
    @ResponseBody
    public ResultVO makeSecret(String orgId) {
        log.info("HackController.makeSecret orgId:{}", orgId);
        return ResultVO.createError("功能已废弃");
    }

    @GetMapping(value = "/update")
    @ApiOperation("跳转hack更新客户数据页面")
    public String update() {
        return "hack/update";
    }

    /**
     * 完善配置信息
     *
     * @param orgId
     * @param htoken
     * @param databaseName
     * @return
     */
    @PostMapping("/updateConfig")
    @ResponseBody
    public ResultVO updateConfig(String orgId, String htoken, String databaseName) {
        log.info("HackController.updateConfig orgId:{},htoken:{},databaseName:{}", orgId, htoken, databaseName);
        return ResultVO.createError("功能已废弃");
    }

}
