package com.xyy.ec.pop.marketing.helpers;

import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.marketing.param.CustomerGroupQueryParam;

import java.util.Objects;

/**
 * {@link CustomerGroupQueryParam} 帮助类
 *
 * <AUTHOR>
 */
public class CustomerGroupQueryParamHelper {

    /**
     * 校验
     *
     * @param customerGroupQueryParam
     * @return
     */
    public static Boolean validate(CustomerGroupQueryParam customerGroupQueryParam) {
        if (Objects.isNull(customerGroupQueryParam)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        Integer pageNum = customerGroupQueryParam.getPageNum();
        Integer pageSize = customerGroupQueryParam.getPageSize();
        if (Objects.isNull(pageNum) || Objects.isNull(pageSize) || pageNum <= 0 | pageSize <= 0) {
            String msg = "分页参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        return true;
    }

}
