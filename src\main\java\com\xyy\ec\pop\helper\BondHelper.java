package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.server.api.merchant.dto.BondDto;
import com.xyy.ec.pop.vo.BondVo;

/**
 * @version v1
 * <AUTHOR>
 */
public class BondHelper {
    public static BondVo convertToBondVo(BondDto bond) {
        BondVo vo = new BondVo();
        vo.setId(bond.getId());
        vo.setCId(bond.getCId());
        vo.setOrgId(bond.getOrgId());
        vo.setBondMoney(bond.getBondMoney());
        vo.setState(bond.getState());

        return vo;
    }

    public static BondDto convertToBondDto(BondVo bond) {
        BondDto bondDto = new BondDto();
        bondDto.setId(bond.getId());
        bondDto.setCId(bond.getCId());
        bondDto.setOrgId(bond.getOrgId());
        bondDto.setBondMoney(bond.getBondMoney());

        return bondDto;
    }
}
