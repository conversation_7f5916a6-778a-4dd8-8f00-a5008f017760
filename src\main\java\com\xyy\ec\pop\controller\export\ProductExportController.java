package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.constants.Constants;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.helper.ProductSkuConvertHelper;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.product.dto.SkuAdminPageQuery;
import com.xyy.ec.pop.vo.ProductVo;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RequestMapping("/productExport/async")
@RestController
public class ProductExportController extends BaseController {

    @Autowired
    private DownloadRemote downloadRemote;

    @RequestMapping(value = "/downloadExcel", method = RequestMethod.GET)
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> downloadExcel(ProductVo productVo) {
        try {
            SkuAdminPageQuery query = ProductSkuConvertHelper.convertToQuery(productVo);
            query.setIsThirdCompany(Constants.IS1);
            List<Long> provIds = getProvIds(query.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            query.setProvIds(provIds);
            log.info("ProductExportController.downloadExcel#query:{}", JSON.toJSONString(query));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .businessType(DownloadTaskBusinessTypeEnum.PRODUCT_EXPORT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("ProductExportController.downloadExcel#query:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        }catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("ProductExportController.downloadExcel#query:{} 异常", JSON.toJSONString(productVo), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }


}
