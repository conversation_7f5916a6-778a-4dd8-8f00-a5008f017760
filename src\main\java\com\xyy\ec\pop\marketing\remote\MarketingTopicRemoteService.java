package com.xyy.ec.pop.marketing.remote;

import com.xyy.ms.promotion.business.common.constants.MarketingTopicEnum;
import com.xyy.ms.promotion.business.dto.marketingactivity.MarketingTopicDTO;

import java.util.List;

public interface MarketingTopicRemoteService {

    /**
     * 根据类型查询主题信息
     *
     * @param type
     * @return
     * @see MarketingTopicEnum
     */
    List<MarketingTopicDTO> listTopicsByType(Integer type);

    /**
     * 查询所有主题信息
     *
     * @return
     */
    List<MarketingTopicDTO> listAllTopic();

}
