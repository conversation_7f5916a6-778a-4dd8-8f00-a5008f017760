-- 商户金额统计表
CREATE TABLE `tb_xyy_pop_account_summary`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '汇总id',
  `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户编号',
  `can_cash_advance_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '可提现金额--在线',
  `amount_in_cash_advance` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '提现中金额--在线',
  `already_cash_advance_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '已提算金额--在线',
  `stay_settle_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '待结算金额--线下',
  `amount_in_settlement` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '结算中金额--线下',
  `already_settle_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '已结算金额--线下',
  `create_time` datetime(0) NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '创建时间',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '修改人',
  `meta_data1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备用字段1',
  `meta_data2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备用字段2',
  `meta_data3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备用字段3',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_org_id`(`org_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户汇总表' ROW_FORMAT = Dynamic;

-- 商户金额日汇总表
CREATE TABLE `tb_xyy_pop_daliy_account_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '汇总id',
  `org_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商户编号',
  `can_cash_advance_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '可提现金额',
  `already_cash_advance_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '以提算金额',
  `amount_in_settlement` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '结算中金额',
  `stay_settle_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '待结算金额',
  `daliy` date NOT NULL DEFAULT '1970-01-01' COMMENT '日汇总表日期',
  `create_time` datetime NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '创建时间',
  `create_by` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人',
  `meta_data1` varchar(255) NOT NULL DEFAULT '' COMMENT '备用字段1',
  `meta_data2` varchar(255) NOT NULL DEFAULT '' COMMENT '备用字段2',
  `meta_data3` varchar(255) NOT NULL DEFAULT '' COMMENT '备用字段3',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_org_id` (`org_id`)
) ENGINE=InnoDB COMMENT='商户日汇总表';

-- 结算任务执行流水表
CREATE TABLE `tb_xyy_pop_settlement_money_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `org_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商户编号',
  `order_no` varchar(64) NOT NULL DEFAULT '' COMMENT '订单编号',
  `handler_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '结算处理类型 0-结算 1-退款更新',
  `settlement_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '结算处理状态 0-处理成功 1-处理失败',
  `settlement_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结算时间',
  `settlement_money` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '结算金额',
  `create_by` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) NOT NULL DEFAULT '' COMMENT '更新人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_org_id` (`order_no`)
) ENGINE=InnoDB COMMENT='结算金额日志表';

-- 收款报表
CREATE TABLE `tb_xyy_pop_receiving_report`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `actually_received_date` datetime(0) NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '实际收款日期',
  `receivables_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '类型(1,收款,2,退款)',
  `org_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商户编号',
  `business_name` varchar(64)  NOT NULL DEFAULT '' COMMENT '商户名称',
  `order_no` varchar(64) NOT NULL DEFAULT '' COMMENT '订单号',
  `pay_way` tinyint(2) NOT NULL DEFAULT 0 COMMENT '支付方式(0,线下转账1,支付宝,2,微信,3,银联)',
  `commodity_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品金额',
  `freight_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '运费金额',
  `order_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '订单金额',
  `stores_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '店铺优惠',
  `platform_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '平台优惠',
  `actually_paid_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实付金额',
  `commission` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付手续费',
  `actually_received_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实收金额',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` datetime(0) NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '创建时间',
  `create_by` varchar(32)  NOT NULL DEFAULT '' COMMENT '创建人',
  `update_time` datetime(0) NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '更新时间',
  `update_by` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人',
  `meta_data1` varchar(255) NOT NULL DEFAULT '' COMMENT '备用字段1',
  `meta_data2` varchar(255) NOT NULL DEFAULT '' COMMENT '备用字段2',
  `meta_data3` varchar(255) NOT NULL DEFAULT '' COMMENT '备用字段3',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_org_id`(`org_id`) USING BTREE,
  INDEX `idx_order_no`(`order_no`) USING BTREE
) ENGINE = InnoDB COMMENT = '收款报表' ROW_FORMAT = Dynamic;

/*
 chencan
 Date: 09/12/2019 16:05:00
*/
CREATE TABLE `tb_xyy_pop_sku_extend`  (
  `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sku_id` bigint(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `source` tinyint(4) NOT NULL DEFAULT 1 COMMENT '来源 1.商家自建 2.商品库',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_id` bigint(11) NOT NULL DEFAULT 0 COMMENT '创建人id',
  `create_name` varchar(200) NOT NULL DEFAULT '' COMMENT '创建人姓名',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_id` bigint(11) NOT NULL DEFAULT 0 COMMENT '修改人id',
  `update_name` varchar(200) NOT NULL DEFAULT '' COMMENT '修改人姓名',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_sku_id`(`sku_id`) USING BTREE
) ENGINE = InnoDB COMMENT='pop商品表';



/* 修改付款证明审核表的付款时间为datetime类型 */
ALTER TABLE `tb_xyy_pop_check_payment_prove`
  MODIFY `pay_date` DATETIME   NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '付款时间';

/* 修改付款证明表的付款时间为datetime类型 */
ALTER TABLE `tb_xyy_pop_payment_prove`
  MODIFY `pay_date` DATETIME   NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '付款时间';

/* 企业资质审核表增加qualifications_detail_id */
ALTER TABLE `tb_xyy_pop_check_corporation_qualification`
	ADD COLUMN `qualifications_detail_id` bigint(20)  NOT NULL DEFAULT 0 COMMENT '资质明细Id';

/* 企业资质表增加qualifications_detail_id */
ALTER TABLE `tb_xyy_pop_corporation_qualification`
	ADD COLUMN `qualifications_detail_id` bigint(20)  NOT NULL DEFAULT 0 COMMENT '资质明细Id';

/* 创建保证金配置表 */
CREATE TABLE `tb_xyy_pop_bond`(
	`id` bigint(20) NOT NULL  auto_increment COMMENT '主键' ,
	`c_id` bigint(20) NOT NULL  DEFAULT 0 COMMENT '商户企业id' ,
	`org_id` varchar(64) NOT NULL  DEFAULT '' COMMENT '机构Id' ,
	`bond_money` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '保证金金额' ,
	`create_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
	`creator` varchar(200) NOT NULL  DEFAULT '' COMMENT '创建人' ,
	`update_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' ,
	`updator` varchar(200) NOT NULL  DEFAULT '' COMMENT '修改人' ,
	PRIMARY KEY (`id`) ,
	UNIQUE KEY `uniq_idx_c_id`(`c_id`) ,
	UNIQUE KEY `uniq_idx_org_id`(`org_id`)
) ENGINE=InnoDB COMMENT='商户保证金配置';

/* 创建佣金配置表 */
CREATE TABLE `tb_xyy_pop_commission`(
	`id` bigint(20) NOT NULL  auto_increment COMMENT '主键',
	`c_id` bigint(20) NOT NULL  DEFAULT 0 COMMENT '商户企业id' ,
	`org_id` varchar(64) NOT NULL  DEFAULT '' COMMENT '机构Id' ,
	`supplier_name` varchar(200) NOT NULL  DEFAULT '' COMMENT '商户名称' ,
	`commission_ratio` decimal(10,2) NOT NULL  DEFAULT 0.00 COMMENT '佣金比率' ,
	`create_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
	`creator` varchar(200) NOT NULL DEFAULT ''  COMMENT '创建人' ,
	`update_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' ,
	`updator` varchar(200) NOT NULL DEFAULT ''  COMMENT '修改人' ,
	PRIMARY KEY (`id`) ,
	UNIQUE KEY `uniq_idx_c_id`(`c_id`) ,
	UNIQUE KEY `uniq_idx_org_id`(`org_id`) ,
	KEY `idx_supplier_name`(`supplier_name`)
) ENGINE=InnoDB COMMENT='商户佣金配置';

/* 创建资质类别表 */
CREATE TABLE `tb_xyy_pop_qualifications_category`(
	`id` bigint(20) NOT NULL  auto_increment COMMENT '主键',
	`name` varchar(200) NOT NULL  DEFAULT '' COMMENT '资质分类名称' ,
	`creator` varchar(200) NOT NULL  DEFAULT '' COMMENT '创建者' ,
	`create_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
	`updator` varchar(200) NOT NULL  DEFAULT '' COMMENT '修改者' ,
	`update_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' ,
	`type` tinyint(4) NOT NULL  DEFAULT 0 COMMENT '0-企业经营类资质,1-企业统一资质' ,
	PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='资质类别表';


/* 创建资质类别与资质明细的关系表 */
CREATE TABLE `tb_xyy_pop_qualifications_category_relation`(
	`id` bigint(20) NOT NULL  auto_increment COMMENT '主键',
	`category_id` bigint(20) NOT NULL  DEFAULT 0 COMMENT '资质类别id' ,
	`detail_id` bigint(20) NOT NULL  DEFAULT 0 COMMENT '资质明细Id' ,
	`creator` varchar(200) NOT NULL  DEFAULT '' COMMENT '创建者' ,
	`create_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
	`updator` varchar(200) NOT NULL  DEFAULT '' COMMENT '修改者' ,
	`update_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' ,
	PRIMARY KEY (`id`) ,
	KEY `idx_category_id`(`category_id`)
) ENGINE=InnoDB COMMENT='资质类别与资质明细的关系表';


/* 创建资质明细表 */
CREATE TABLE `tb_xyy_pop_qualifications_detail`(
	`id` bigint(20) NOT NULL  auto_increment COMMENT '主键',
	`name` varchar(200) NOT NULL  DEFAULT '' COMMENT '资质名称' ,
	`remark` varchar(200) NOT NULL  DEFAULT '' COMMENT '资质内容描述' ,
	`is_need` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '是否必填:0-不填,1-必填' ,
	`is_need_code` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '证件号是否必填:0-不填,1-必填' ,
	`is_need_time` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '证件有效期是否必填:0-不填,1-必填' ,
	`creator` varchar(200) NOT NULL  DEFAULT '' COMMENT '创建者' ,
	`create_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
	`updator` varchar(200) NOT NULL  DEFAULT '' COMMENT '修改者' ,
	`update_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' ,
	PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='资质明细表';


/* 创建经营范围与资质类别的关系表 */
CREATE TABLE `tb_xyy_pop_business_category_relation`(
	`id` bigint(20) NOT NULL  auto_increment COMMENT '主键',
	`business_category_id` bigint(20) NOT NULL  DEFAULT 0 COMMENT '经营范围Id' ,
	`qualifications_category_id` bigint(20) NOT NULL  DEFAULT 0 COMMENT '资质类别id' ,
	`creator` varchar(200) NOT NULL  DEFAULT '' COMMENT '创建者' ,
	`create_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
	`updator` varchar(200) NOT NULL  DEFAULT '' COMMENT '修改者' ,
	`update_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' ,
	PRIMARY KEY (`id`) ,
	KEY `idx_business_category_id`(`business_category_id`) ,
	KEY `idx_qualifications_catory_id`(`qualifications_category_id`)
) ENGINE=InnoDB COMMENT='经营范围与资质类别的关系表';

/* 初始化资质明细数据 */
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('1','质量保证协议','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('2','GSP证书/GMP证书','','1','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('3','食品生产许可证/食品经营许可证','','1','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('4','商标注册证/商品注册申请书','','0','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('5','药品经营许可证/药品生产许可证','','1','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('6','营业执照','企业《营业执照》正本或副本复印件及上一年度企业信息公示表','1','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('7','开户许可证','银行开户行许可证、开票资料','1','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('8','法定代表人身份证复印件','','1','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('9','随货同行单据票样','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('10','印章印模备案','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('11','增值税普通/专用发票税票样板','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('12','医疗器械生产许可证','','1','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('13','消毒产品生产企业卫生许可证','','1','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('17','国产保健食品批准证书及其附件','','0','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('18','奶粉配方注册证明','','0','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('19','检验报告书','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('20','供货企业质量保证体系调查表','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('21','药品注册证或注册批件,再注册批件','','1','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('22','药品检验报告书','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('23','药品最小销售单元的包装、标签和说明书实样','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('24','药品质量标准','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('25','第二类医疗器械经营备案凭证','','0','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('26','医疗器械经营许可证','','0','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('27','医疗器械网络经营备案证明','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('28','质量保证协议','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('29','医疗器械备案凭证或产品注册证','','1','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('30','医疗器械合格证明文件或检测报告','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('31','《化妆品生产企业卫生许可证》','','0','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('32','特殊用途化妆品备案凭证/非特殊用途化妆品备案凭证','','0','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('33','检验报告书','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('34','商标注册证/商品注册申请书','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('35','消毒产品生产企业卫生许可证','','0','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('36','消毒产品卫生安全评价报告备案凭证','','0','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('37','检验报告书','','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('38','商标注册证/商品注册申请书','','0','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('39','法人授权书','法人授权书,授权书应载明委托人及其身份号码、委托事项等，并加盖企业公章和企业法人印章','1','0','0','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_detail` (`id`, `name`, `remark`, `is_need`, `is_need_code`, `is_need_time`, `creator`, `create_time`, `updator`, `update_time`) values('40','其他','','0','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);

/* 初始化资质类别数据 */
insert into `tb_xyy_pop_qualifications_category` (`id`, `name`, `creator`, `create_time`, `updator`, `update_time`, `type`) values('1','药品','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP,'0');
insert into `tb_xyy_pop_qualifications_category` (`id`, `name`, `creator`, `create_time`, `updator`, `update_time`, `type`) values('2','食品类','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP,'0');
insert into `tb_xyy_pop_qualifications_category` (`id`, `name`, `creator`, `create_time`, `updator`, `update_time`, `type`) values('3','统一资质','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP,'1');
insert into `tb_xyy_pop_qualifications_category` (`id`, `name`, `creator`, `create_time`, `updator`, `update_time`, `type`) values('4','医疗器械类','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP,'0');
insert into `tb_xyy_pop_qualifications_category` (`id`, `name`, `creator`, `create_time`, `updator`, `update_time`, `type`) values('5','消毒产品类','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP,'0');
insert into `tb_xyy_pop_qualifications_category` (`id`, `name`, `creator`, `create_time`, `updator`, `update_time`, `type`) values('6','美妆个护类','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP,'0');
insert into `tb_xyy_pop_qualifications_category` (`id`, `name`, `creator`, `create_time`, `updator`, `update_time`, `type`) values('7','其他','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP,'0');

/* 初始化资质类别与资质明细的关系数据 */
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('1','1','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('2','1','2','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('3','2','3','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('4','2','4','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('5','1','5','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('6','3','6','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('7','3','7','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('8','3','8','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('9','3','9','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('10','3','10','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('11','3','11','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('12','4','12','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('13','2','17','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('14','2','18','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('15','2','19','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('16','1','20','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('17','1','21','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('18','1','22','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('19','1','23','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('20','1','24','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('21','4','25','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('22','4','26','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('23','4','27','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('24','4','28','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('25','4','29','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('26','4','30','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('27','6','31','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('28','6','32','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('29','6','33','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('30','6','34','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('31','5','35','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('32','5','36','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('33','5','37','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('34','5','38','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('35','3','39','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_qualifications_category_relation` (`id`, `category_id`, `detail_id`, `creator`, `create_time`, `updator`, `update_time`) values('36','7','40','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);

/* 初始化经营范围与资质类别的关系数据 */
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('1','10001','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('3','10094','4','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('5','10325','7','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('6','10086','5','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('7','10219','2','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('8','10262','2','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('9','10372','2','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('10','10024','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('11','10397','1','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('12','10049','6','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('13','10058','6','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('14','10063','6','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('15','10075','6','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('16','10080','6','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);
insert into `tb_xyy_pop_business_category_relation` (`id`, `business_category_id`, `qualifications_category_id`, `creator`, `create_time`, `updator`, `update_time`) values('17','10092','6','system',CURRENT_TIMESTAMP,'system',CURRENT_TIMESTAMP);


/* 处理线上资质表已上传资质与当前资质配置的关系(仅需处理营业执照) */
UPDATE tb_xyy_pop_corporation_qualification a,tb_xyy_pop_qualifications_detail b,tb_xyy_pop_qualifications_category_relation c,tb_xyy_pop_qualifications_category d
SET a.qualifications_detail_id = b.`id`
WHERE a.qualifications_detail_id=0 AND b.id=c.`detail_id` AND c.`category_id`=d.`id`
     AND (CASE WHEN a.`name`='营业执照' THEN b.`name`='营业执照' AND d.`type`=1 AND d.name='统一资质'
		            WHEN a.`name`='药品经营许可证' THEN b.`name`='药品经营许可证/药品生产许可证' AND d.`type`=0 AND d.name='药品'
                WHEN a.`name`='GSP证书' THEN b.`name`='GSP证书/GMP证书' AND d.`type`=0 AND d.name='药品'
                WHEN a.`name`='开户许可证' THEN b.`name`='开户许可证' AND d.`type`=1 AND d.name='统一资质'
                WHEN a.`name`='药品经营质量保证协议' THEN b.`name`='质量保证协议' AND d.`type`=0 AND d.name='药品'
                WHEN a.`name`='税票样本' THEN b.`name`='增值税普通/专用发票税票样板' AND d.`type`=1 AND d.name='统一资质'
                WHEN a.`name`='供货企业质量保证体系调查表' THEN b.`name`='供货企业质量保证体系调查表' AND d.`type`=0 AND d.name='药品'
                WHEN a.`name`='印章印模（鲜章）' THEN b.`name`='印章印模备案' AND d.`type`=1 AND d.name='统一资质'
                WHEN a.`name`='法人身份证' THEN b.`name`='法定代表人身份证复印件' AND d.`type`=1 AND d.name='统一资质'
                WHEN a.`name`='医疗器械经营许可证' THEN b.`name`='医疗器械经营许可证' AND d.`type`=0 AND d.name='医疗器械类'
                WHEN a.`name`='法人授权委托书' THEN b.`name`='法人授权书' AND d.`type`=1 AND d.name='统一资质'
	      END);
/* 处理线上资质审核表已上传资质与当前资质配置的关系(仅需处理营业执照) */
UPDATE tb_xyy_pop_check_corporation_qualification a,tb_xyy_pop_qualifications_detail b,tb_xyy_pop_qualifications_category_relation c,tb_xyy_pop_qualifications_category d
SET a.qualifications_detail_id = b.`id`
WHERE a.qualifications_detail_id=0 AND b.id=c.`detail_id` AND c.`category_id`=d.`id`
     AND (CASE WHEN a.`name`='营业执照' THEN b.`name`='营业执照' AND d.`type`=1 AND d.name='统一资质'
                WHEN a.`name`='药品经营许可证' THEN b.`name`='药品经营许可证/药品生产许可证' AND d.`type`=0 AND d.name='药品'
                WHEN a.`name`='GSP证书' THEN b.`name`='GSP证书/GMP证书' AND d.`type`=0 AND d.name='药品'
                WHEN a.`name`='开户许可证' THEN b.`name`='开户许可证' AND d.`type`=1 AND d.name='统一资质'
                WHEN a.`name`='药品经营质量保证协议' THEN b.`name`='质量保证协议' AND d.`type`=0 AND d.name='药品'
                WHEN a.`name`='税票样本' THEN b.`name`='增值税普通/专用发票税票样板' AND d.`type`=1 AND d.name='统一资质'
                WHEN a.`name`='供货企业质量保证体系调查表' THEN b.`name`='供货企业质量保证体系调查表' AND d.`type`=0 AND d.name='药品'
                WHEN a.`name`='印章印模（鲜章）' THEN b.`name`='印章印模备案' AND d.`type`=1 AND d.name='统一资质'
                WHEN a.`name`='法人身份证' THEN b.`name`='法定代表人身份证复印件' AND d.`type`=1 AND d.name='统一资质'
                WHEN a.`name`='医疗器械经营许可证' THEN b.`name`='医疗器械经营许可证' AND d.`type`=0 AND d.name='医疗器械类'
                WHEN a.`name`='法人授权委托书' THEN b.`name`='法人授权书' AND d.`type`=1 AND d.name='统一资质'
          END);

/* 提现记录表*/
CREATE TABLE `tb_xyy_pop_cash_advance`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现id',
  `org_id` varchar(64) NOT NULL DEFAULT '' COMMENT '商户编号',
  `business_name` varchar(64) NOT NULL DEFAULT '' COMMENT '商户名称',
  `apply_amount` decimal(12, 4) NOT NULL DEFAULT 0.0000 COMMENT '申请提现金额',
  `fee` decimal(12, 4) NOT NULL DEFAULT 0.0000 COMMENT '提现手续费',
  `reality_amount` decimal(12, 4) NOT NULL DEFAULT 0.0000 COMMENT '实际到账金额',
  `cash_advance_amount` decimal(12, 4) NOT NULL DEFAULT 0.0000 COMMENT '提现后剩余可提金额',
  `apply_time` datetime(0) NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '申请时间',
  `account_name` varchar(64) NOT NULL DEFAULT '' COMMENT '提现账户名',
  `account_num` varchar(64)  NOT NULL DEFAULT '' COMMENT '账号',
  `account_bank` varchar(64) NOT NULL DEFAULT '' COMMENT '开户行',
  `payment_way` tinyint(2) NOT NULL DEFAULT 2 COMMENT '打款方式（1.线上打款，2线下打款）',
  `payment_status` tinyint(2) NOT NULL DEFAULT 1 COMMENT '打款状态（1.未打款，2.已打款）',
  `payment_time` datetime(0) NULL DEFAULT NULL COMMENT '打款时间',
  `reason` varchar(255) NOT NULL DEFAULT '' COMMENT '备注/原因',
  `audit_status` tinyint(2) NOT NULL DEFAULT 1 COMMENT '审核状态（1.待审核，2.审核不通过，3.审核通过）',
  `audit_time` datetime(0) NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '审核时间',
  `auditor` varchar(32) NOT NULL DEFAULT '' COMMENT '审核人',
  `create_time` datetime(0) NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '创建时间',
  `create_by` varchar(32)  NOT NULL DEFAULT '' COMMENT '创建人',
  `update_time` datetime(0) NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '更新时间',
  `update_by` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人',
  `cash_advance_num` varchar(255)  NOT NULL DEFAULT '' COMMENT '提现单号',
  `meta_data1` varchar(255) NOT NULL DEFAULT '' COMMENT '备用字段1',
  `meta_data2` varchar(255) NOT NULL DEFAULT '' COMMENT '备用字段2',
  `meta_data3` varchar(255) NOT NULL DEFAULT '' COMMENT '备用字段3',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_org_id`(`org_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '提现表' ROW_FORMAT = Dynamic;

-- 资质地址替换
update tb_xyy_pop_check_corporation_qualification set url='https://upload.ybm100.com/ybm/brand/a9f9c5d8-2452-43f9-9490-887259dbd02b.jpg' where id =149;
update tb_xyy_pop_check_corporation_qualification set url='https://upload.ybm100.com/ybm/brand/2922173c-d8d0-4c90-9974-f05e95c26084.jpg' where id =150;
update tb_xyy_pop_check_corporation_qualification set url='https://upload.ybm100.com/ybm/brand/e13fa15e-5337-4da1-a611-8f494eda1643.jpg' where id =151;

update tb_xyy_pop_corporation_qualification set url='https://upload.ybm100.com/ybm/brand/a9f9c5d8-2452-43f9-9490-887259dbd02b.jpg' where id =195;
update tb_xyy_pop_corporation_qualification set url='https://upload.ybm100.com/ybm/brand/2922173c-d8d0-4c90-9974-f05e95c26084.jpg' where id =196;
update tb_xyy_pop_corporation_qualification set url='https://upload.ybm100.com/ybm/brand/e13fa15e-5337-4da1-a611-8f494eda1643.jpg' where id =197;