package com.xyy.ec.pop.service;

import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.login.SystemMenu;
import com.xyy.ec.pop.vo.login.UserInfo;
import com.xyy.ec.pop.vo.zhongtai.PopProvinceVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: SystemService
 * @Package com.xyy.xpop.admin.service
 * @Description: 系统权限业务
 * @date 2020/5/28 17:25
 */
public interface SystemService {
    /**
     * 用户登录
     * @param username 用户账号
     * @param password 密码
     * @return
     */
    ResponseVo login(String username, String password);

    /**
     * 初始化菜单并放入redis缓存
     * @param loginUser 登录用户
     * @return
     */
    List<SystemMenu> initNavigationBar(SysUser loginUser);

    /**
     * 获取菜单权限
     * @return
     */
    List<SystemMenu> getNavigationBar(SysUser sysUser);

    /**
     * 退出登录
     */
    void logout();

    /**
     * 设置本地 session 缓存
     * @param userInfo 用户信息
     */
    boolean mockLocalLogin(UserInfo userInfo);

    /**
     * 获取用户省份权限
     * @param sysUser
     * @return
     */
    List<PopProvinceVo> getProvincialAuthority(SysUser sysUser);

    /**
     * 初始化省份
     *
     * @param sysUser
     * @return
     */
    List<PopProvinceVo> initProvinces(SysUser sysUser);

    /**
     * 获取菜单按钮权限
     * @param sysUser
     * @return
     */
    List<String> getButtons(SysUser sysUser, String menuUrl);

    /**
     * 初始化按钮
     */
    void initButtons(SysUser sysUser);

}
