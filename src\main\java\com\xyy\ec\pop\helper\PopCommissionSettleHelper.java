package com.xyy.ec.pop.helper;

import com.google.common.collect.Lists;
import com.xyy.ec.pop.excel.entity.PopCommissionSettleExportDetailVo;
import com.xyy.ec.pop.excel.entity.PopCommissionSettleExportVo;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleBillDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleDto;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleOverdueEnum;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleStatusEnum;
import com.xyy.ec.pop.vo.CommissionSettleVo;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class PopCommissionSettleHelper {

    public static List<PopCommissionSettleExportVo> convertSettleExportVo(List<PopCommissionSettleDto> settleDtoList ) {
        if(CollectionUtils.isEmpty(settleDtoList)){
            return Lists.newArrayList();
        }
        List<PopCommissionSettleExportVo> popCommissionSettleExportVoList = Lists.newArrayList();

        settleDtoList.forEach(settleDto->{
            PopCommissionSettleExportVo popCommissionSettleExportVo = new PopCommissionSettleExportVo();
            popCommissionSettleExportVo.setOrgId(settleDto.getOrgId());
            popCommissionSettleExportVo.setOrgName(settleDto.getOrgName());
            popCommissionSettleExportVo.setName(settleDto.getName());
            popCommissionSettleExportVo.setHireMonths(settleDto.getHireMonths());
            popCommissionSettleExportVo.setHireMoney(settleDto.getHireMoney());
            popCommissionSettleExportVo.setCreateTime(settleDto.getCreateTime());
            popCommissionSettleExportVo.setPaymentTerm(settleDto.getPaymentTerm());
            popCommissionSettleExportVo.setOverdue(CommissionSettleOverdueEnum.getTextByStatus(settleDto.getOverdue()));
            popCommissionSettleExportVo.setPaymentTime(settleDto.getPaymentTime());
            popCommissionSettleExportVo.setState(CommissionSettleStatusEnum.getTextByStatus(settleDto.getState()));
            popCommissionSettleExportVo.setRemarks(settleDto.getRemarks());
            popCommissionSettleExportVoList.add(popCommissionSettleExportVo);
        });
        return popCommissionSettleExportVoList;
    }

    public static List<PopCommissionSettleExportDetailVo> convertSettleDetailExportVo(List<PopCommissionSettleBillDto> billList,  Map<String,PopCommissionSettleDto> settleDtoMap ) {
        if(CollectionUtils.isEmpty(billList)){
            return Lists.newArrayList();
        }
        List<PopCommissionSettleExportDetailVo> popCommissionSettleExportDetailVoList = Lists.newArrayList();
        billList.forEach(o->{
            PopCommissionSettleDto popCommissionSettleDto = settleDtoMap.get(o.getHireNo());
            PopCommissionSettleExportDetailVo popCommissionSettleExportDetailVo = new PopCommissionSettleExportDetailVo();
            if(Objects.nonNull(popCommissionSettleDto)){
                popCommissionSettleExportDetailVo.setOrgId(popCommissionSettleDto.getOrgId());
                popCommissionSettleExportDetailVo.setOrgName(popCommissionSettleDto.getOrgName());
                popCommissionSettleExportDetailVo.setName(popCommissionSettleDto.getName());
                popCommissionSettleExportDetailVo.setHireMonths(popCommissionSettleDto.getHireMonths());
                popCommissionSettleExportDetailVo.setHireMoney(popCommissionSettleDto.getHireMoney());
                popCommissionSettleExportDetailVo.setCreateTime(popCommissionSettleDto.getCreateTime());
                popCommissionSettleExportDetailVo.setPaymentTerm(popCommissionSettleDto.getPaymentTerm());
                popCommissionSettleExportDetailVo.setOverdue(CommissionSettleOverdueEnum.getTextByStatus(popCommissionSettleDto.getOverdue()));
                popCommissionSettleExportDetailVo.setPaymentTime(popCommissionSettleDto.getPaymentTime());
                popCommissionSettleExportDetailVo.setState(CommissionSettleStatusEnum.getTextByStatus(popCommissionSettleDto.getState()));
                popCommissionSettleExportDetailVo.setRemarks(popCommissionSettleDto.getRemarks());
            }
            popCommissionSettleExportDetailVo.setBillNo(o.getBillNo());
            popCommissionSettleExportDetailVo.setProductMoney(o.getProductMoney());
            popCommissionSettleExportDetailVo.setFreightAmount(o.getFreightAmount());
            popCommissionSettleExportDetailVo.setTotalMoney(o.getTotalMoney());
            popCommissionSettleExportDetailVo.setShopTotalDiscount(o.getShopTotalDiscount());
            popCommissionSettleExportDetailVo.setPlatformTotalDiscount(o.getPlatformTotalDiscount());
            popCommissionSettleExportDetailVo.setMoney(o.getMoney());
            popCommissionSettleExportDetailVo.setBillHireMoney(o.getBillHireMoney());
            popCommissionSettleExportDetailVo.setStatementTotalMoney(o.getStatementTotalMoney());
            popCommissionSettleExportDetailVo.setPayableCommission(o.getPayableCommission());
            popCommissionSettleExportDetailVo.setBillPaymentTime(o.getBillPaymentTime());
            popCommissionSettleExportDetailVo.setBillCreateTime(o.getBillCreateTime());
            popCommissionSettleExportDetailVoList.add(popCommissionSettleExportDetailVo);
        });
        return popCommissionSettleExportDetailVoList;
    }

    public static CommissionSettleVo commissionSettleDto2Vo(PopCommissionSettleDto dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        CommissionSettleVo vo = new CommissionSettleVo();
        vo.setId(dto.getId());
        vo.setHireSettleMonth(dto.getHireSettleMonth());
        vo.setSettlementType(dto.getSettlementType());
        vo.setHireNo(dto.getHireNo());
        vo.setOrgId(dto.getOrgId());
        vo.setOrgName(dto.getOrgName());
        vo.setName(dto.getName());
        vo.setHireMoney(dto.getHireMoney());
        vo.setDeductedCommission(dto.getDeductedCommission());
        vo.setCreateTime(dto.getCreateTime());
        vo.setHireMonths(dto.getHireMonths());
        vo.setPaymentTerm(dto.getPaymentTerm());
        vo.setPaymentTime(dto.getPaymentTime());
        vo.setPaymentCertificate(dto.getPaymentCertificate());
        vo.setUploadTime(dto.getUploadTime());
        vo.setRemarks(dto.getRemarks());
        vo.setState(dto.getState());
        vo.setOverdue(dto.getOverdue());
        vo.setDiscountHireMoney(dto.getDiscountHireMoney());
        vo.setCommissionDiscount(dto.getCommissionDiscount());
        vo.setActualHireMoney(dto.getActualHireMoney());
        vo.setProvId(dto.getProvId());
        vo.setProv(dto.getProv());
        vo.setInvoiceFilename(dto.getInvoiceFilename());
        return vo;
    }

    public static List<CommissionSettleVo> commissionSettleDto2VoList(List<PopCommissionSettleDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Lists.newArrayList();
        }
        return dtoList.stream().map(dto -> commissionSettleDto2Vo(dto)).collect(Collectors.toList());
    }
}
