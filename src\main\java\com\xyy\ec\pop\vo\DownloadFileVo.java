package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 下载文件Vo
 *
 * <AUTHOR>
@Data
public class DownloadFileVo implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 下载文件名称
     */
    private String downloadFileName;

    /**
     * 导出进度
     */
    private Long downloadSpeed;

    /**
     * 任务状态 1:新建 2:生成中 3:已完成 4:失败
     */
    private Byte status;

    /**
     * 任务状态描述
     */
    private String statusDesc;

    /**
     * 下载文件路径
     */
    private String fileUrl;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    private String orgId;

    private static final long serialVersionUID = 1L;
}