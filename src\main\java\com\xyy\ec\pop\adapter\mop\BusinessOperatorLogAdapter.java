package com.xyy.ec.pop.adapter.mop;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.BusinessOperatorLogRemote;
import com.xyy.pop.mop.api.remote.parameter.query.BusinessOperatorQueryParame;
import com.xyy.pop.mop.api.remote.result.BusinessOperatorLogBasicDTO;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BusinessOperatorLogAdapter implements MopBaseAdapter {

    @Reference(version = "1.0.0")
    private BusinessOperatorLogRemote businessOperatorLogRemote;

    public ResponseVo<List<BusinessOperatorLogBasicDTO>> listBusinessOperatorLog(BusinessOperatorQueryParame param) {
        return to(() -> businessOperatorLogRemote.listBusinessOperatorLog(param));
    }

}
