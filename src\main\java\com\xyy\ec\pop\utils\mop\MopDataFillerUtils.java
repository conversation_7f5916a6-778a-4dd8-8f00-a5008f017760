package com.xyy.ec.pop.utils.mop;

import com.xyy.ec.pop.model.SysUser;
import com.xyy.scm.constant.enumerate.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;

@Slf4j
public class MopDataFillerUtils {
    /**
     * 填充数据到参数对象中
     *
     * @param parame 参数对象
     * @param isUpdate 是否为更新操作
     */
    public static void fillData(SysUser user, Object parame, boolean isUpdate) {
        if (user == null) {
            return;
        }

        try {
            if (isUpdate) {
                setFieldIfExists(parame, "updateBy", user.getRealName());
            } else {
                setFieldIfExists(parame, "createBy", user.getRealName());
            }
            setFieldIfExists(parame, "dataSource", DataSource.POP_ADMIN);
        } catch (Exception e) {
            log.error("MopDataFillerUtils -填充数据到参数对象中异常", e);
        }
    }

    /**
     * 设置字段值，如果字段存在
     *
     * @param obj 对象
     * @param fieldName 字段名
     * @param value 字段值
     * @throws IllegalAccessException 如果无法访问字段
     */
    private static void setFieldIfExists(Object obj, String fieldName, Object value) throws IllegalAccessException {
        try {
//            Field field = obj.getClass().getDeclaredField(fieldName);
            Field field = ReflectionUtils.findField(obj.getClass(), fieldName);
            field.setAccessible(true);
            field.set(obj, value);
        } catch (Exception e) {
            log.error("MopDataFillerUtils -设置字段值，如果字段存在异常", e);
        }
    }

}
