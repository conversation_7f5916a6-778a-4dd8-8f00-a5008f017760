package com.xyy.ec.pop.config;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.ButtonAuth;
import com.xyy.ec.pop.filter.InterfaceAuthenticationInterceptor;
import com.xyy.ec.pop.filter.LocalSessionInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by z<PERSON><PERSON> on 2018/8/30.
 */
@Configuration
public class WebConfig extends WebMvcConfigurerAdapter {

    //    @Autowired
//    private AccessInterceptor accessInterceptor;
    @Autowired
    private LocalSessionInterceptor localSessionInterceptor;

    @Autowired
    private InterfaceAuthenticationInterceptor interfaceAuthenticationInterceptor;

    @Value("${pop.authentication.interfaces:}")
    private String btnUriStr;

    private final String[] excludePathPatterns = {"/logout/success",
            "/static/**", "/v2/api-docs", "/swagger-resources", "/swagger-resources/**", "/css/**", "/fonts/**",
            "/images/**", "/img/**", "/js/**", "/login/**", "/uploadFile/**", "/download/**",
            "/ok.htm", "/favicon.ico", "/main.html", "/codeManagement/initCodeCache", "/branchManagement/initCache", "/user/modifyPassword", "/passwordManager/forgetPasswordStep2",
            "/passwordManager/forgetPasswordStep3", "/passwordManager/getValitionCode", "/passwordManager/checkValidate", "/passwordManager/updateUserPassword",
            "/dataInterfaceApi/queryOrderList", "/dataInterfaceApi/order/details", "/api/pop/order/orderInfo", "/dataInterfaceApi/updateOrderStatus",
            "/dataInterfaceApi/updateOrderTrackingNo", "/dataInterfaceApi/queryRefundOrderList", "/dataInterfaceApi/refundOrderInfo/details", "/dataInterfaceApi/refundOrderInfo",
            "/dataInterfaceApi/updateRefundOrderState", "/dataInterfaceApi/searchProductInfo", "/dataInterfaceApi/initProductEdit", "/dataInterfaceApi/saveSkuInfo",
            "/dataInterfaceApi/updateSkuInfo", "/dataInterfaceApi/searchProductCategoryRelation", "/dataInterfaceApi/searchSkuCategory", "/api/pop/category/getCategoryListByParentId",
            "/dataInterfaceApi/editSkuStatus", "/dataInterfaceApi/uploadImage", "/dataInterfaceApi/order/details", "/dataInterfaceApi/soldOutSku", "/dataInterfaceApi/putAwaySku",
            "/dataInterfaceApi/getDataCount", "/ok", "/login/userData", "/webService/**", "/popApi/listCorporationsByOrgIds", "/popApi/listCorporationsByNameAndBranchs", "/util/syncSkuCategory", "/util/syncSkuCategoryForStantard",
            "/isDubboActive","/tool/product/moveDeletedSku","/tool/product/moveDeletedSkuOtherInfo,/tool/product/deleteErpSkusByOrgId,/tool/product/maintain,/tool/downPopSkuPage,/fdd/fddSignUtil","/inner/query/queryOriginBarcodes"
    };

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        List<String> localSessionInterceptorExclude = new ArrayList<>();
        localSessionInterceptorExclude.addAll(Arrays.asList(excludePathPatterns));
        localSessionInterceptorExclude.add("/logout/**");
        localSessionInterceptorExclude.add("/logoutCustom");
        String[] newExclude = localSessionInterceptorExclude.toArray(new String[0]);
        registry.addInterceptor(localSessionInterceptor)
                .excludePathPatterns(newExclude).addPathPatterns("/**");
        // 登录拦截器
//        registry.addInterceptor(accessInterceptor).excludePathPatterns(excludePathPatterns).addPathPatterns("/**");
//        registry.addInterceptor(webButtonInterceptor).excludePathPatterns(newExclude).addPathPatterns("/**");
        List<ButtonAuth> buttonAuths = JSON.parseArray(btnUriStr, ButtonAuth.class);
        String[] btnUrls = buttonAuths.stream().map(ButtonAuth::getBtnUrl).toArray(String[]::new);
        registry.addInterceptor(interfaceAuthenticationInterceptor).addPathPatterns(btnUrls);
    }

    @Override
    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
        configurer.enable();
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
    }
}
