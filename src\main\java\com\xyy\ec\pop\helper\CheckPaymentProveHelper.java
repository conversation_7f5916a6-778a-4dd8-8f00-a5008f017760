package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.server.api.merchant.dto.CheckPaymentProveModelDto;
import com.xyy.ec.pop.vo.CheckPaymentProveVo;

public class CheckPaymentProveHelper {

    public static CheckPaymentProveVo convertDtoToVo(CheckPaymentProveModelDto dto) {
        CheckPaymentProveVo checkPaymentProveVo = new CheckPaymentProveVo();
        checkPaymentProveVo.setId(dto.getId());
        checkPaymentProveVo.setCId(dto.getCId());
        checkPaymentProveVo.setState(dto.getState());
        checkPaymentProveVo.setPhone(dto.getPhone());
        checkPaymentProveVo.setName(dto.getName());
        checkPaymentProveVo.setMerchantName(dto.getMerchantName());
        checkPaymentProveVo.setCorporat(dto.getCorporat());
        checkPaymentProveVo.setMerchantNo(dto.getMerchantNo());
        checkPaymentProveVo.setMoney(dto.getMoney());
        checkPaymentProveVo.setTransacIype(dto.getTransacIype());
        checkPaymentProveVo.setPayWay(dto.getPayWay());
        checkPaymentProveVo.setUrl(dto.getUrl());
        checkPaymentProveVo.setCreateTime(dto.getCreateTime());
        checkPaymentProveVo.setPayDate(dto.getPayDate());
        checkPaymentProveVo.setReceiptUrl(dto.getReceiptUrl());
        checkPaymentProveVo.setProvId(dto.getProvId());
        checkPaymentProveVo.setProv(dto.getProv());
        return checkPaymentProveVo;
    }
}
