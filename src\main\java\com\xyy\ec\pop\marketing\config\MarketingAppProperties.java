package com.xyy.ec.pop.marketing.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * 营销App属性配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties("xyy.marketing")
@Validated
@Getter
@Setter
public class MarketingAppProperties {

    /**
     * 批量下线拼团活动，导入Excel最大数量
     */
    @Value("${marketing.batchOfflineGroupBuyingImportExcelMaxRowNum:1000}")
    private Integer batchOfflineGroupBuyingImportExcelMaxRowNum;

}
