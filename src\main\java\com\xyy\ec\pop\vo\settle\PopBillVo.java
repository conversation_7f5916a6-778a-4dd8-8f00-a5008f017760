package com.xyy.ec.pop.vo.settle;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/1 11:29
* @table
*/
@Data
public class PopBillVo implements Serializable {
    /**
     * 主键
     */
    private Long id;
    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 商家名称
     */
    private String orgName;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 账单号
     */
    private String billNo;

    /**
     * 商品总金额
     */
    private BigDecimal productMoney;

    /**
     * 单据总金额
     */
    private BigDecimal totalMoney;

    /**
     * 订单实际付款金额
     */
    private BigDecimal money;

    /**
     * 运费
     */
    private BigDecimal freightAmount;

    /**
     * 店铺优惠券金额
     */
    private BigDecimal couponShopAmount;

    /**
     * 店铺营销活动优惠金额
     */
    private BigDecimal marketingShopAmount;

    /**
     * 店铺总优惠金额
     */
    private BigDecimal shopTotalDiscount;

    /**
     * 平台优惠券金额
     */
    private BigDecimal couponPlatformAmount;

    /**
     * 平台营销活动优惠金额
     */
    private BigDecimal marketingPlatformAmount;

    /**
     * 平台总优惠金额
     */
    private BigDecimal platformTotalDiscount;

    /**
     * 佣金
     */
    private BigDecimal hireMoney;
    /**
     * 应收佣金
     */
    private BigDecimal payableCommission;
    /**
     * 佣金结算方式，0:全部；1：非月结；2：月结
     */
    private Byte settlementType;
    /**
     * 处罚金额
     */
    private BigDecimal penaltyAmount;

    /**
     * 应结算金额
     */
    private BigDecimal statementTotalMoney;

    /**
     * 支付类型(1:在线支付 2:货到付款 3:线下转账（电汇平台） 4:线下转账（电汇商业））
     */
    private Byte payType;
    /**
     * 多个支付类型
     */
    private List<Byte> payTypes;

    /**
     * 账单生成时间
     */
    private Date billCreateTime;

    /**
     * 账单入账状态 0-未入账 1-已入账
     */
    private Byte billPaymentStatus;

    /**
     * 账单入账时间
     */
    private Date billPaymentTime;

    /**
     * 打款状态 0：未打款，1:已打款
     */
    private Byte remitStatus;

    /**
     * 确认打款日期
     */
    private Date remitTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 开票状态 0：未开，1:已开 2：无需开票
     */
    private Byte invoiceStatus;

    /**
     * 开票日期
     */
    private Date invoiceTime;
    /**
     * 开票开始时间
     */
    private Date startInvoiceTime;
    /**
     * 开票结束时间
     */
    private Date endInvoiceTime;
    /**
     * 账单生成开始时间
     */
    private Date startCreateTime;
    /**
     * 账单生成结束时间
     */
    private Date endCreateTime;
    /**
     * 账单入账开始时间
     */
    private Date startBillPaymentTime;
    /**
     * 账单入账结束时间
     */
    private Date endBillPaymentTime;
    /**
     * 账单号list 开票用
     */
    private List<String> billNoList;
    /**
     * 打款开始时间
     */
    private Date startRemitTime;
    /**
     * 打款结束时间
     */
    private Date endRemitTime;

    private List<String> orgIds;

    /**
     * 分润状态 1-未分润 2-分润成功 3-分润失败
     */
    private Byte billShareStatus;

    /**
     * 支付通道 1-直连支付 2-富民支付
     */
    private Integer paymentChannel;

    /**
     * 单据编号
     */
    private String businessNo;

    private Long provId;

    private List<Long> provIds;

    /**
     * 补贴冲抵佣金 0-否 1-是
     */
    private Byte deducted;

    /**
     * 分润时间-开始
     */
    private Date startBillShareTime;

    /**
     * 分润时间-结束
     */
    private Date endBillShareTime;
}
