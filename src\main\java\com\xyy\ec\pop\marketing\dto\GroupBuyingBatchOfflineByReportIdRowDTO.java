package com.xyy.ec.pop.marketing.dto;

import com.xyy.ec.pop.marketing.easyexcel.rows.GroupBuyingBatchOfflineByReportIdExcelRow;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 拼团批量下线行DTO。
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class GroupBuyingBatchOfflineByReportIdRowDTO implements Serializable {

    /**
     * 报名ID
     */
    private Long reportId;

    /**
     * 失败原因条项列表
     */
    private List<String> failureReasonItems;

    /**
     * excel行数据
     */
    private GroupBuyingBatchOfflineByReportIdExcelRow excelRow;

    /**
     * excel行索引
     */
    private Integer excelRowIndex;

    /* 字段校验结果 */
    private Boolean reportIdCheckResult;

    /**
     * 此行的校验结果
     */
    private Boolean checkResult;

    /**
     * checkResult 禁止修改，此字段实时根据结果获取
     *
     * @return
     */
    public Boolean getCheckResult() {
        return BooleanUtils.isTrue(reportIdCheckResult);
    }

    /**
     * checkResult 禁止修改，此字段实时根据结果获取，这里提供set方法，为了覆盖Lombok的Setter方法
     *
     * @param checkResult
     */
    public void setCheckResult(Boolean checkResult) {
    }

    /**
     * 操作结果
     */
    private Boolean optResult;

    /**
     * 程序上下文变量；
     */
}
