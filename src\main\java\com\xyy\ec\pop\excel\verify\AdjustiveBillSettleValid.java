package com.xyy.ec.pop.excel.verify;

import com.xyy.ec.pop.server.api.admin.dto.CommissionSettleSetDto;
import com.xyy.ec.pop.vo.AdjustiveBillSettleVo;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @version v1
 * @Description 调账单校验
 * <AUTHOR>
 */
public class AdjustiveBillSettleValid {
    /**
     * 小数
     */
    public final static String NUM_DOUBLE_WITH_ZERO = "^(0|[-]?[1-9][0-9]*)(\\.\\d{1,2})?$";


    public static void trim(List<AdjustiveBillSettleVo> vos) {
        vos.forEach(vo -> {
            vo.setOrgId(StringUtils.trimToNull(vo.getOrgId()));
            vo.setPlatformTotalDiscount(StringUtils.trimToNull(vo.getPlatformTotalDiscount()));
            vo.setHireMoney(StringUtils.trimToNull(vo.getHireMoney()));
            vo.setStatementTotalMoney(StringUtils.trimToNull(vo.getStatementTotalMoney()));
            vo.setDeductedCommission(StringUtils.trimToNull(vo.getDeductedCommission()));
            vo.setRemark(StringUtils.trimToNull(vo.getRemark()));
        });
    }

    public static void valid(List<AdjustiveBillSettleVo> vos, List<String> orgIdsInDB, Map<String, CommissionSettleSetDto> orgIdSettleSetMap) {
        //有佣金结算方式的商家
        Set<String> hasSettleSetOrgIds = orgIdSettleSetMap.keySet();
        vos.forEach(vo -> {
            StringBuilder errMsg = new StringBuilder();
            if (!orgIdsInDB.contains(vo.getOrgId())) {
                errMsg.append("商业编号不存在,");
            }
            if (!hasSettleSetOrgIds.contains(vo.getOrgId()) || orgIdSettleSetMap.get(vo.getOrgId()) == null || orgIdSettleSetMap.get(vo.getOrgId()).getSettlementType() == null) {
                errMsg.append("商业未设置佣金结算方式,");
            }
            if (StringUtils.isBlank(vo.getStatementTotalMoney())) {
                errMsg.append("应结算金额不允许为空,");
            }
            if (StringUtils.isBlank(vo.getDeductedCommission())) {
                errMsg.append("应缴纳佣金不允许为空,");
            }
            if (vo.getPlatformTotalDiscount() != null && !vo.getPlatformTotalDiscount().matches(NUM_DOUBLE_WITH_ZERO)) {
                errMsg.append("平台总优惠不规范,");
            }
            if (vo.getHireMoney() != null && !vo.getHireMoney().matches(NUM_DOUBLE_WITH_ZERO)) {
                errMsg.append("佣金不规范,");
            }
            if (vo.getStatementTotalMoney() != null && !vo.getStatementTotalMoney().matches(NUM_DOUBLE_WITH_ZERO)) {
                errMsg.append("应结算金额不规范,");
            }
            if (vo.getDeductedCommission() != null && !vo.getDeductedCommission().matches(NUM_DOUBLE_WITH_ZERO)) {
                errMsg.append("应缴纳佣金不规范,");
            }
            if (StringUtils.isNotBlank(vo.getRemark()) && vo.getRemark().length() > 100) {
                errMsg.append("备注字段限100字,");
            }

            if (errMsg.length() > 0) {
                vo.setErrorMsg(errMsg.substring(0, errMsg.length() - 1));
                vo.setFailed(true);
            }
        });
    }
}
