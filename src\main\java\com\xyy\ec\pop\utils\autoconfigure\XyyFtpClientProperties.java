package com.xyy.ec.pop.utils.autoconfigure;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * FtpClient属性
 *
 * <AUTHOR>
 */
@ConfigurationProperties("xyy.ftp_client")
@Validated
@Getter
@Setter
public class XyyFtpClientProperties {

    private GenericObjectPoolConfig pool = new GenericObjectPoolConfig();

    /**
     * 地址
     */
    private String server;
    /**
     * 端口号
     */
    private int port = 0;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;

    /**
     * 连接超时时长，单位毫秒
     */
    private int connectTimeout = 3000;
    /**
     * 获取响应数据的超时时长，单位毫秒
     */
    private int dataTimeout = 10000;
    /**
     * 保持连接存活时长，单位秒
     */
    private long keepAliveTimeout = 30L;
    /**
     * 保持消息回复存活时长，单位毫秒
     */
    private int controlKeepAliveReplyTimeout = 30000;

    /**
     * 数据传输的字符编码
     */
    private String encoding = "UTF-8";
    /**
     * 是否使用被动模式
     */
    private boolean passiveMode = true;
    /**
     * 设置是否与IPv4一起使用EPSV。在某些情况下是值得启用的。例如，当使用带有NAT的IPv4时，它可能使用一些罕见的配置。
     * 例如，如果FTP服务器有一个静态的PASV地址(外部网络)，而客户端来自另一个内部网络。
     * 在这种情况下，PASV命令后的数据连接将失败，而EPSV将通过只获取端口使客户机成功。
     */
    private boolean useEpsvWithIPv4 = false;
    /**
     * 文件传输模式
     */
    private Integer transferFileType;

    /**
     * ftps
     */
    private Proxy proxy = new Proxy();

    @Setter
    @Getter
    public static class Proxy {
        private String host;
        int port = 80;
        String user;
        String password;
    }

    /**
     * ftps
     */
    private Ftps ftps = new Ftps();

    @Setter
    @Getter
    public static class Ftps {
        private boolean enable = false;
        private String protocol;
        private boolean implicit = false;
    }

}
