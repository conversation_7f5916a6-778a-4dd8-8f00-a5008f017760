package com.xyy.ec.pop.vo.afterSales;

import lombok.Data;

import java.io.Serializable;
@Data
public class AfterSalesVo implements Serializable {
    private static final long serialVersionUID = -4688977824037736933L;


    private String orgId;
    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 商户名称
     */
    private String companyName;
    private Long orderId;
    private Long popOrderId;
    private String orderNo;
    private String afterSalesNo;
    private String createTime;
    private Integer orderStatus;
    private Integer auditProcessState;
    private String operateTime;
    private String operator;
    private Integer afterSalesType;
    private String afterSalesTypeName;
    //同订单售后单据数量
    private Integer afterSalesCount;
    //售后信息
    private String afterSalesInfo;

    private String customerErpCode;
    private String merchantName;
    private String mobile;
    private String address;

    //最新处理
    private String lastDealInfo;

    private String sellerRemark;

}
