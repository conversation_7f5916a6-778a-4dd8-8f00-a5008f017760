package com.xyy.ec.pop.service.impl;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.helper.CheckPaymentProveHelper;
import com.xyy.ec.pop.remote.CheckPaymentProveRemoteAdapter;
import com.xyy.ec.pop.server.api.export.param.CheckPaymentProveParam;
import com.xyy.ec.pop.server.api.merchant.dto.CheckPaymentProveModelDto;
import com.xyy.ec.pop.service.MerchantMarginService;
import com.xyy.ec.pop.vo.CheckPaymentProveVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class MerchantMarginServiceImpl implements MerchantMarginService {

    @Autowired
    private CheckPaymentProveRemoteAdapter adapter;

    @Override
    public Page<CheckPaymentProveVo> selectPageList(CheckPaymentProveParam proveParam) {
        Integer offset = Optional.ofNullable(proveParam.getOffset()).filter(off -> off != null && off >= 0).orElse(0);
        Integer limit = Optional.ofNullable(proveParam.getLimit()).filter(lim -> lim != null && lim > 0).orElse(10);
        proveParam.setOffset(offset);
        proveParam.setLimit(limit);
        PageInfo<CheckPaymentProveModelDto> info = adapter.selectPageList(proveParam);
        List<CheckPaymentProveModelDto> list = info.getList();
        Page<CheckPaymentProveVo> page = new Page<CheckPaymentProveVo>();
        page.setCurrentPage(info.getPageNum());
        page.setOffset(offset);
        page.setLimit(limit);
        long total = info.getTotal();
        page.setTotal(total);
        int pageCount = (int) (total % limit == 0 ? total / limit : total / limit +1);
        page.setPageCount(pageCount);
        List<CheckPaymentProveVo> proveVos = list.stream().map(CheckPaymentProveHelper::convertDtoToVo).collect(Collectors.toList());
        page.setRows(proveVos);
        return page;
    }
}
