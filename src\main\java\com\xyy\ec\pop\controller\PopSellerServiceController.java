package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.gmv.api.PopSellerServiceQualityApi;
import com.xyy.ec.pop.server.api.gmv.dto.PopReportsSellerServiceAdminDto;
import com.xyy.ec.pop.server.api.gmv.dto.PopReportsSellerServiceAdminQueryParamDto;
import com.xyy.ec.pop.server.api.merchant.api.admin.PopBillSettleaApi;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@Controller
@RequestMapping("/serviceQuality")
@Slf4j
public class PopSellerServiceController extends BaseController {

    @Reference(version = "1.0.0")
    private PopSellerServiceQualityApi popSellerServiceQualityApi;

    @Reference(version = "1.0.0")
    private PopBillSettleaApi popBillSettleaApi;

    @Autowired
    private DownloadRemote downloadRemote;

    @PostMapping("/selectPageList")
    @ResponseBody
    public ResponseVo<Object> selectPageList(@RequestBody PopReportsSellerServiceAdminQueryParamDto queryParamDto){
        try {
            log.info("PopSellerServiceController selectPageList request ,queryParamDto :{}", JSONObject.toJSONString(queryParamDto));
            if (Objects.isNull(queryParamDto) || Objects.isNull(queryParamDto.getPageSize()) || Objects.isNull(queryParamDto.getPageNum()) ){
                return ResponseVo.errRest("分页不能为空");
            }
            ApiRPCResult<PageInfo<PopReportsSellerServiceAdminDto>> apiRPCResult = popSellerServiceQualityApi.selectPageList(queryParamDto);
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail()){
                return ResponseVo.errRest("查询失败");
            }
            return ResponseVo.successResult(apiRPCResult.getData());
        } catch (Exception e) {
            log.error("PopSellerServiceController selectPageList error,queryParamDto :{}", JSONObject.toJSONString(queryParamDto),e);
            return ResponseVo.errRest("系统错误");
        }
    }

    @PostMapping("/export")
    @ResponseBody
    public ResponseVo<Object> export(@RequestBody PopReportsSellerServiceAdminQueryParamDto queryParamDto){
        try {
            log.info("PopSellerServiceController export request ,queryParamDto :{}", JSONObject.toJSONString(queryParamDto));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryParamDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.SELLER_SERVICE_QUALITY_ADMIN)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            return ResponseVo.successResult(Boolean.TRUE);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("PopSellerServiceController export error,queryParamDto :{}", JSONObject.toJSONString(queryParamDto),e);
            return ResponseVo.errRest("系统错误");
        }
    }

    @GetMapping("/export")
    @ResponseBody
    public ResponseVo<Object> initMerchantErpCode(){
        try {
            popBillSettleaApi.initMerchantErpCode();
            return ResponseVo.successResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("initMerchantErpCode export error",e);
            return ResponseVo.errRest("系统错误");
        }
    }
}
