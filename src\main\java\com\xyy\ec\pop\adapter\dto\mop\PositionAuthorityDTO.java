package com.xyy.ec.pop.adapter.dto.mop;

import com.xyy.pop.mop.api.common.enumerate.*;
import com.xyy.pop.mop.api.remote.parameter.PositionAuthorityParame;
import com.xyy.pop.mop.api.remote.parameter.validation.Create;
import com.xyy.pop.mop.api.remote.parameter.validation.Update;
import com.xyy.scm.constant.annotation.Explain;
import com.xyy.scm.constant.enumerate.DataSource;
import com.xyy.scm.constant.foundation.GeneralEnum;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
public class PositionAuthorityDTO {
    @Explain("自增ID")
    private @NotNull(
            message = "岗位ID不能为空",
            groups = {Update.class}
    ) Long id;
    @Explain("岗位名称")
    private @NotNull(
            message = "岗位名称不能为空",
            groups = {Create.class, Update.class}
    ) String positionName;
    @Explain("岗位类型：关联mop_position_type字典类型")
    private @NotNull(
            message = "岗位类型不能为空",
            groups = {Create.class, Update.class}
    ) MopPositionTypeEnum positionType;
    @Explain("岗位级别:关联mop_position_level字典类型")
    private @NotNull(
            message = "岗位级别不能为空",
            groups = {Create.class, Update.class}
    ) MopPositionLevelEnum positionLevel;
    @Explain("总业绩权限：关联mop_permission_type字典类型")
    private @NotNull(
            message = "总业绩/总新签权限不能为空",
            groups = {Create.class, Update.class}
    ) MopPermissionTypeEnum achievementPermission;
    @Explain("店铺动销权限：关联mop_permission_type字典类型")
    private @NotNull(
            message = "店铺动销/预警信息权限不能为空",
            groups = {Create.class, Update.class}
    ) MopPermissionTypeEnum storeSalesPermission;
    @Explain("运营拜访权限：关联mop_permission_type字典类型")
    private @NotNull(
            message = "运营拜访/招商权限不能为空",
            groups = {Create.class, Update.class}
    ) MopVisitPermissionTypeEnum operationVisitPermission;
    @Explain("店铺列表权限：关联mop_permission_type字典类型")
    private @NotNull(
            message = "店铺列表权限不能为空",
            groups = {Create.class, Update.class}
    ) MopPermissionTypeEnum storeListPermission;
    @Explain("其他权限：关联mop_other_permission_type字典类型")
    private List<String> otherPermission;
    @Explain("数据来源")
    private DataSource dataSource;
    @Explain("是否可见-可选-不可空创建时默认1,修改显示声明为0")
    private Integer yn;
    @Explain("备注-可空")
    private String remark;
    @Explain("创建人ID-不可空创建时显示声明")
    private String createBy;
    @Explain("最后一次修改人ID-不可空创建默认0;修改显示声明")
    private String updateBy;

    public PositionAuthorityParame toParame() {
        PositionAuthorityParame parame = new PositionAuthorityParame();
        BeanUtils.copyProperties(this, parame,"otherPermission");
        List<MopOtherPermissionTypeEnum> collect = Optional.ofNullable(otherPermission).orElse(new ArrayList<>()).stream().map(c -> GeneralEnum.of(c,MopOtherPermissionTypeEnum.class)).collect(Collectors.toList());
        parame.setOtherPermission(collect);
        return parame;
    }
}
