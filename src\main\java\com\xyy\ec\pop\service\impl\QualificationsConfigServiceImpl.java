package com.xyy.ec.pop.service.impl;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.QualificationsConfigHelper;
import com.xyy.ec.pop.remote.QualificationsCategoryRemoteAdapter;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.QualificationsCategoryDto;
import com.xyy.ec.pop.service.QualificationsConfigService;
import com.xyy.ec.pop.vo.QualificationsCategoryRelationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @version v1
 * @Description 资质配置接口
 * <AUTHOR>
 */
@Service
public class QualificationsConfigServiceImpl implements QualificationsConfigService {
    @Autowired
    private QualificationsCategoryRemoteAdapter qualificationsCategoryRemoteAdapter;

    @Override
    public QualificationsCategoryRelationVo commonQualificationConfigs(CorporationTypeEnum corporationType) {
        QualificationsCategoryDto categoryDto = qualificationsCategoryRemoteAdapter.commonQualificationConfigs(corporationType);
        return QualificationsConfigHelper.convertToCategoryRelationVo(categoryDto);
    }

    @Override
    public List<QualificationsCategoryRelationVo> getBusinessQualificationConfigs(CorporationTypeEnum corporationType) {
        List<QualificationsCategoryDto> categories = qualificationsCategoryRemoteAdapter.getBusinessQualificationConfigs(corporationType);
        return QualificationsConfigHelper.convertToCategoryRelationVos(categories);
    }

    @Override
    public void savePopQualifications(List<QualificationsCategoryRelationVo> relationVoList, byte corporationType, String username) throws ServiceException {
        List<QualificationsCategoryDto> dtos = QualificationsConfigHelper.convertToCategoryDto(relationVoList);
        qualificationsCategoryRemoteAdapter.saveQualifications(dtos,corporationType,username);
    }
}
