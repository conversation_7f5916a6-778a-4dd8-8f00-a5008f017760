package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.merchant.bussiness.utils.JsonUtil;
import com.xyy.ec.pop.service.ProductCategoryService;
import com.xyy.me.product.common.beans.ResponseInfo;
import com.xyy.me.product.general.api.facade.dictionary.DictionaryFacade;
import com.xyy.me.product.service.read.dto.TotalDictionaryReadDto;
import com.xyy.me.product.service.read.vo.DictionaryVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品四级分类服务
 *
 * <AUTHOR>
 * @createDate 2020/07/08 10:51
 * @see com.xyy.ec.pop.service.impl
 */
@Service
public class ProductCategoryServiceImpl implements ProductCategoryService {

    private static final Logger LOG = LoggerFactory.getLogger(ProductCategoryServiceImpl.class);

    @Reference(version = "1.0.0",registry = "me")
    private DictionaryFacade dictionaryFacade;

    @Override
    public List<TotalDictionaryReadDto> getAllProductCategory() {
         return getProductCategory(null,null);
    }

    @Override
    public List<TotalDictionaryReadDto> getFourLevelProductCategory() {
        return getFourLevelProductCategoryByIds(null);
    }

    @Override
    public Map<String, TotalDictionaryReadDto> getFirstLevelProductCategoryMap() {
        List<TotalDictionaryReadDto> dtoList = getProductCategory(Collections.singletonList((byte) 1), null);
        return dtoList.stream().collect(Collectors.toMap(dto->dto.getId().toString(), Function.identity(), (o, n) -> o));
    }

    @Override
    public List<TotalDictionaryReadDto> getTwoLevelProductCategory() {
        String ids= "1,2";
        List<Byte> levelNodeList = Arrays.asList(ids.split(",")).stream().map(s -> Byte.valueOf(s.trim())).collect(Collectors.toList());
        return getProductCategory(levelNodeList,null);
    }

    @Override
    public List<TotalDictionaryReadDto> getFourLevelProductCategoryByIds(List<Integer> idList) {
        String ids= "1,2,3,4";
        List<Byte> levelNodeList = Arrays.asList(ids.split(",")).stream().map(s -> Byte.valueOf(s.trim())).collect(Collectors.toList());
        return getProductCategory(levelNodeList,idList);
    }

    public List<TotalDictionaryReadDto> getProductCategory(List<Byte> levelNodeList, List<Integer> idList) {
        List<TotalDictionaryReadDto> list = Lists.newArrayList();
        try {
            int pageSize = 1000;  //中台接口一次最大支持1000条
            int dataSize = pageSize;
            DictionaryVo dictionaryVo = new DictionaryVo();
            dictionaryVo.setLimit(pageSize);
            dictionaryVo.setPageNo(1);
            dictionaryVo.setTraceId("YBM_XYY_XPOP_MERCHANT");
            dictionaryVo.setType("-1");
            dictionaryVo.setIdList(idList);
            if(!CollectionUtils.isEmpty(levelNodeList)){
                dictionaryVo.setLevelNodeList(levelNodeList);
            }
            LOG.info("ProductCategoryServiceImpl pageQueryDictionaryIndistinct param:{}", JsonUtil.toJson(dictionaryVo));
            while (dataSize==pageSize){
                dataSize=0;
                ResponseInfo<PageInfo<TotalDictionaryReadDto>> responseInfo = dictionaryFacade.pageQueryDictionaryIndistinct(dictionaryVo);
                if (null != responseInfo && null != responseInfo.getData()) {
                    PageInfo<TotalDictionaryReadDto> pageInfo = responseInfo.getData();
                    if (pageInfo!=null && !CollectionUtils.isEmpty(pageInfo.getList())) {
                        dataSize = pageInfo.getList().size();
                        list.addAll(pageInfo.getList());
                        dictionaryVo.setPageNo(dictionaryVo.getPageNo()+1);
                    }

                }
            }
            LOG.info("ProductCategoryServiceImpl pageQueryDictionaryIndistinct list result:{}", JsonUtil.toJson(list));
            list=list.stream().filter(s -> s.getIsValid()==1).collect(Collectors.toList());
            return list;
        } catch (Exception e) {
            LOG.error("获取中台四级分类全部信息错误，error是{}", e);
            return Lists.newArrayList();
        }
    }
}
