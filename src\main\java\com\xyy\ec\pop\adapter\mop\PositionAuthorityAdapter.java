package com.xyy.ec.pop.adapter.mop;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.PositionAuthorityRemote;
import com.xyy.pop.mop.api.remote.parameter.PositionAuthorityParame;
import com.xyy.pop.mop.api.remote.parameter.query.PositionAuthorityQueryParame;
import com.xyy.pop.mop.api.remote.result.PositionAllDTO;
import com.xyy.pop.mop.api.remote.result.PositionAuthorityBasicDTO;
import com.xyy.scm.constant.entity.pagination.Paging;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PositionAuthorityAdapter implements MopBaseAdapter {

    @Reference(version = "1.0.0")
    private PositionAuthorityRemote positionAuthorityRemote;

    public ResponseVo<Boolean> createPositionAuthority(PositionAuthorityParame parame) {
        return to(()->positionAuthorityRemote.createPositionAuthority(parame));
    }

    public ResponseVo<Boolean> updatePositionAuthority(PositionAuthorityParame parame) {
        return to(()->positionAuthorityRemote.updatePositionAuthority(parame));
    }

    public ResponseVo<Boolean> deletePositionAuthority(Long id) {
        return to(()->positionAuthorityRemote.deletePositionAuthority(id));
    }

    public ResponseVo<PositionAuthorityBasicDTO> findPositionAuthority(Long id) {
        return to(()->positionAuthorityRemote.findPositionAuthority(id));
    }

    public ResponseVo<Paging<PositionAuthorityBasicDTO>> pagePositionAuthoritiesByPaging(PositionAuthorityQueryParame parame) {
        return to(()->positionAuthorityRemote.findPositionAuthoritiesByPaging(parame));
    }

    public ResponseVo<List<PositionAllDTO>> findAllPosition() {
        return to(()->positionAuthorityRemote.findAllPosition());
    }

}
