package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.excel.entity.OfflinePopBillPaymentDetailExportVo;
import com.xyy.ec.pop.excel.entity.OfflinePopBillPaymentExportVo;
import com.xyy.ec.pop.excel.entity.OnlinePayPopBillPaymentDetailExportVo;
import com.xyy.ec.pop.excel.entity.OnlinePayPopBillPaymentExportVo;
import com.xyy.ec.pop.po.PopBillPaymentDetailPo;
import com.xyy.ec.pop.po.PopBillPaymentPo;
import com.xyy.ec.pop.po.PopBillPo;
import com.xyy.ec.pop.server.api.seller.dto.PopBillPaymentDto;
import com.xyy.ec.pop.vo.settle.PopBillPayVo;
import com.xyy.ec.pop.vo.settle.PopBillVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class PopBillPaymentHelper {
    public static PopBillPo convertPopBill(PopBillVo popBillVo) {
        if(null == popBillVo){
            return null;
        }
        PopBillPo popBillPo = new PopBillPo();
        popBillPo.setId(popBillVo.getId());
        popBillPo.setOrgId(popBillVo.getOrgId());
        popBillPo.setOrgName(popBillVo.getOrgName());
        popBillPo.setBillNo(popBillVo.getBillNo());
        popBillPo.setProductMoney(popBillVo.getProductMoney());
        popBillPo.setTotalMoney(popBillVo.getTotalMoney());
        popBillPo.setMoney(popBillVo.getMoney());
        popBillPo.setFreightAmount(popBillVo.getFreightAmount());
        popBillPo.setCouponShopAmount(popBillVo.getCouponShopAmount());
        popBillPo.setMarketingShopAmount(popBillVo.getMarketingShopAmount());
        popBillPo.setShopTotalDiscount(popBillVo.getShopTotalDiscount());
        popBillPo.setCouponPlatformAmount(popBillVo.getCouponPlatformAmount());
        popBillPo.setMarketingPlatformAmount(popBillVo.getMarketingPlatformAmount());
        popBillPo.setPlatformTotalDiscount(popBillVo.getPlatformTotalDiscount());
        popBillPo.setHireMoney(popBillVo.getHireMoney());
        popBillPo.setPayableCommission(popBillVo.getPayableCommission());
        popBillPo.setSettlementType(popBillVo.getSettlementType());
        popBillPo.setPenaltyAmount(popBillVo.getPenaltyAmount());
        popBillPo.setStatementTotalMoney(popBillVo.getStatementTotalMoney());
        popBillPo.setPayType(popBillVo.getPayType());
        popBillPo.setBillCreateTime(popBillVo.getBillCreateTime());
        popBillPo.setBillPaymentStatus(popBillVo.getBillPaymentStatus());
        popBillPo.setBillPaymentTime(popBillVo.getBillPaymentTime());
        popBillPo.setRemitStatus(popBillVo.getRemitStatus());
        popBillPo.setRemitTime(popBillVo.getRemitTime());
        popBillPo.setCreateTime(popBillVo.getCreateTime());
        popBillPo.setUpdateTime(popBillVo.getUpdateTime());
        popBillPo.setStartCreateTime(popBillVo.getCreateTime());
        popBillPo.setEndCreateTime(popBillVo.getCreateTime());
        return popBillPo;
    }

    public static PopBillPaymentPo convertPopBillPay(PopBillPayVo popBillPayVo) {
        if(null == popBillPayVo){
            return null;
        }
        PopBillPaymentPo popBillPaymentPo = new PopBillPaymentPo();
        popBillPaymentPo.setId(popBillPayVo.getId());
        popBillPaymentPo.setOrgId(popBillPayVo.getOrgId());
        popBillPaymentPo.setName(popBillPayVo.getName());
        popBillPaymentPo.setOrgName(popBillPayVo.getOrgName());
        popBillPaymentPo.setFlowNo(popBillPayVo.getFlowNo());
        popBillPaymentPo.setProductMoney(popBillPayVo.getProductMoney());
        popBillPaymentPo.setTotalMoney(popBillPayVo.getTotalMoney());
        popBillPaymentPo.setMoney(popBillPayVo.getMoney());
        popBillPaymentPo.setFreightAmount(popBillPayVo.getFreightAmount());
        popBillPaymentPo.setCouponShopAmount(popBillPayVo.getCouponShopAmount());
        popBillPaymentPo.setMarketingShopAmount(popBillPayVo.getMarketingShopAmount());
        popBillPaymentPo.setShopTotalDiscount(popBillPayVo.getShopTotalDiscount());
        popBillPaymentPo.setCouponPlatformAmount(popBillPayVo.getCouponPlatformAmount());
        popBillPaymentPo.setMarketingPlatformAmount(popBillPayVo.getMarketingPlatformAmount());
        popBillPaymentPo.setPlatformTotalDiscount(popBillPayVo.getPlatformTotalDiscount());
        popBillPaymentPo.setHireMoney(popBillPayVo.getHireMoney());
        popBillPaymentPo.setPenaltyAmount(popBillPayVo.getPenaltyAmount());
        popBillPaymentPo.setStatementTotalMoney(popBillPayVo.getStatementTotalMoney());
        popBillPaymentPo.setPayType(popBillPayVo.getPayType());
        popBillPaymentPo.setBillPaymentStatus(popBillPayVo.getBillPaymentStatus());
        popBillPaymentPo.setBillPaymentTime(popBillPayVo.getBillPaymentTime());
        popBillPaymentPo.setRemitStatus(popBillPayVo.getRemitStatus());
        popBillPaymentPo.setRemitTime(popBillPayVo.getRemitTime());
        popBillPaymentPo.setCreateTime(popBillPayVo.getCreateTime());
        popBillPaymentPo.setUpdateTime(popBillPayVo.getUpdateTime());
        popBillPaymentPo.setStartCreateTime(popBillPayVo.getStartCreateTime());
        popBillPaymentPo.setEndCreateTime(popBillPayVo.getEndCreateTime());
        popBillPaymentPo.setStartRemitTime(popBillPayVo.getStartRemitTime());
        popBillPaymentPo.setEndRemitTime(popBillPayVo.getEndRemitTime());
        popBillPaymentPo.setOrgIds(popBillPayVo.getOrgIds());
        popBillPaymentPo.setBillPaymentStatus(popBillPayVo.getBillPaymentStatus());
        popBillPaymentPo.setBillPaymentTime(popBillPayVo.getBillPaymentTime());
        popBillPaymentPo.setDeducted(popBillPayVo.getDeducted());
        return popBillPaymentPo;
    }

    public static List<OfflinePopBillPaymentExportVo> convertOfflinePopBillPaymentExportVo(List<PopBillPaymentPo> list) {

        List<OfflinePopBillPaymentExportVo> offlinePopBillPaymentExportVoList = new ArrayList<>();
        list.forEach(popBillPaymentPo -> {
            OfflinePopBillPaymentExportVo offlinePopBillPaymentExportVo = new OfflinePopBillPaymentExportVo();
            offlinePopBillPaymentExportVo.setOrgId(popBillPaymentPo.getOrgId());
            offlinePopBillPaymentExportVo.setOrgName(popBillPaymentPo.getOrgName());
            offlinePopBillPaymentExportVo.setName(popBillPaymentPo.getName());
            offlinePopBillPaymentExportVo.setFlowNo(popBillPaymentPo.getFlowNo());
            offlinePopBillPaymentExportVo.setProductMoney(popBillPaymentPo.getProductMoney());
            offlinePopBillPaymentExportVo.setFreightAmount(popBillPaymentPo.getFreightAmount());
            offlinePopBillPaymentExportVo.setTotalMoney(popBillPaymentPo.getTotalMoney());
            offlinePopBillPaymentExportVo.setShopTotalDiscount(popBillPaymentPo.getShopTotalDiscount());
            offlinePopBillPaymentExportVo.setPlatformTotalDiscount(popBillPaymentPo.getPlatformTotalDiscount());
            offlinePopBillPaymentExportVo.setMoney(popBillPaymentPo.getMoney());
            offlinePopBillPaymentExportVo.setHireMoney(popBillPaymentPo.getHireMoney());
            offlinePopBillPaymentExportVo.setPayableCommission(popBillPaymentPo.getPayableCommission());
            offlinePopBillPaymentExportVo.setStatementTotalMoney(popBillPaymentPo.getStatementTotalMoney());
            offlinePopBillPaymentExportVo.setCreateTime(popBillPaymentPo.getCreateTime());
            offlinePopBillPaymentExportVo.setRemitStatus(popBillPaymentPo.getRemitStatus());
            offlinePopBillPaymentExportVo.setRemitTime(popBillPaymentPo.getRemitTime());
            offlinePopBillPaymentExportVoList.add(offlinePopBillPaymentExportVo);
        });
        return offlinePopBillPaymentExportVoList;
    }

    public static List<OfflinePopBillPaymentDetailExportVo> convertOfflinePopBillPaymentDetailExportVo(Map<String, PopBillPaymentPo> popBillPaymentPoMap, List<PopBillPaymentDetailPo> popBillPaymentDetailPos) {
        List<OfflinePopBillPaymentDetailExportVo> offlinePopBillPaymentDetailExportVos = new ArrayList<>();
        popBillPaymentDetailPos.forEach(popBillPaymentDetailPo -> {
            PopBillPaymentPo popBillPaymentPo = popBillPaymentPoMap.get(popBillPaymentDetailPo.getFlowNo());
            OfflinePopBillPaymentDetailExportVo offlinePopBillPaymentDetailExportVo = new OfflinePopBillPaymentDetailExportVo();
            offlinePopBillPaymentDetailExportVo.setOrgId(popBillPaymentDetailPo.getOrgId());
            offlinePopBillPaymentDetailExportVo.setOrgName(popBillPaymentDetailPo.getOrgName());
            offlinePopBillPaymentDetailExportVo.setName(popBillPaymentDetailPo.getName());
            offlinePopBillPaymentDetailExportVo.setFlowNo(popBillPaymentDetailPo.getFlowNo());
            offlinePopBillPaymentDetailExportVo.setCreateTime(popBillPaymentDetailPo.getCreateTime());
            offlinePopBillPaymentDetailExportVo.setRemitStatus(null!=popBillPaymentPo?popBillPaymentPo.getRemitStatus():null);
            offlinePopBillPaymentDetailExportVo.setRemitTime(popBillPaymentDetailPo.getCreateTime());
            offlinePopBillPaymentDetailExportVo.setBillNo(popBillPaymentDetailPo.getBillNo());
            offlinePopBillPaymentDetailExportVo.setProductMoney(popBillPaymentDetailPo.getProductMoney());
            offlinePopBillPaymentDetailExportVo.setFreightAmount(popBillPaymentDetailPo.getFreightAmount());
            offlinePopBillPaymentDetailExportVo.setTotalMoney(popBillPaymentDetailPo.getTotalMoney());
            offlinePopBillPaymentDetailExportVo.setShopTotalDiscount(popBillPaymentDetailPo.getShopTotalDiscount());
            offlinePopBillPaymentDetailExportVo.setPlatformTotalDiscount(popBillPaymentDetailPo.getPlatformTotalDiscount());
            offlinePopBillPaymentDetailExportVo.setMoney(popBillPaymentDetailPo.getMoney());
            offlinePopBillPaymentDetailExportVo.setHireMoney(popBillPaymentDetailPo.getHireMoney());
            offlinePopBillPaymentDetailExportVo.setStatementTotalMoney(popBillPaymentDetailPo.getStatementTotalMoney());
            offlinePopBillPaymentDetailExportVo.setBillCreateTime(popBillPaymentDetailPo.getBillCreateTime());
            offlinePopBillPaymentDetailExportVo.setBillPaymentTime(null!=popBillPaymentPo?popBillPaymentPo.getBillPaymentTime():null);
            offlinePopBillPaymentDetailExportVo.setPayableCommission(popBillPaymentDetailPo.getPayableCommission());
            offlinePopBillPaymentDetailExportVo.setSettlementType(popBillPaymentDetailPo.getSettlementType());
            offlinePopBillPaymentDetailExportVos.add(offlinePopBillPaymentDetailExportVo);
        });
        return offlinePopBillPaymentDetailExportVos;
    }

    public static List<OnlinePayPopBillPaymentExportVo> convertOnlinePayPopBillPaymentExport(List<PopBillPaymentPo> list) {
        List<OnlinePayPopBillPaymentExportVo> onlinePayPopBillPaymentExportVos = new ArrayList<>();
        list.forEach(popBillPaymentPo -> {
            OnlinePayPopBillPaymentExportVo onlinePayPopBillPaymentExportVo = new OnlinePayPopBillPaymentExportVo();
            onlinePayPopBillPaymentExportVo.setOrgId(popBillPaymentPo.getOrgId());
            onlinePayPopBillPaymentExportVo.setOrgName(popBillPaymentPo.getOrgName());
            onlinePayPopBillPaymentExportVo.setName(popBillPaymentPo.getName());
            onlinePayPopBillPaymentExportVo.setFlowNo(popBillPaymentPo.getFlowNo());
            onlinePayPopBillPaymentExportVo.setProductMoney(popBillPaymentPo.getProductMoney());
            onlinePayPopBillPaymentExportVo.setFreightAmount(popBillPaymentPo.getFreightAmount());
            onlinePayPopBillPaymentExportVo.setTotalMoney(popBillPaymentPo.getTotalMoney());
            onlinePayPopBillPaymentExportVo.setShopTotalDiscount(popBillPaymentPo.getShopTotalDiscount());
            onlinePayPopBillPaymentExportVo.setPlatformTotalDiscount(popBillPaymentPo.getPlatformTotalDiscount());
            onlinePayPopBillPaymentExportVo.setMoney(popBillPaymentPo.getMoney());
            onlinePayPopBillPaymentExportVo.setHireMoney(popBillPaymentPo.getHireMoney());
            onlinePayPopBillPaymentExportVo.setPayableCommission(popBillPaymentPo.getPayableCommission());
            onlinePayPopBillPaymentExportVo.setStatementTotalMoney(popBillPaymentPo.getStatementTotalMoney());
            onlinePayPopBillPaymentExportVo.setCreateTime(popBillPaymentPo.getCreateTime());
            onlinePayPopBillPaymentExportVos.add(onlinePayPopBillPaymentExportVo);
        });
        return onlinePayPopBillPaymentExportVos;
    }

    public static List<OnlinePayPopBillPaymentDetailExportVo> convertOnlinePayPopBillPaymentDetailExportVo(Map<String, PopBillPaymentPo> popBillPaymentPoMap, List<PopBillPaymentDetailPo> popBillPaymentDetailPos) {
        List<OnlinePayPopBillPaymentDetailExportVo> onlinePayPopBillPaymentDetailExportVos = new ArrayList<>();
        popBillPaymentDetailPos.forEach(popBillPaymentDetailPo -> {
            PopBillPaymentPo popBillPaymentPo = popBillPaymentPoMap.get(popBillPaymentDetailPo.getFlowNo());
            OnlinePayPopBillPaymentDetailExportVo onlinePayPopBillPaymentDetailExportVo = new OnlinePayPopBillPaymentDetailExportVo();
            onlinePayPopBillPaymentDetailExportVo.setOrgId(popBillPaymentDetailPo.getOrgId());
            onlinePayPopBillPaymentDetailExportVo.setOrgName(popBillPaymentDetailPo.getOrgName());
            onlinePayPopBillPaymentDetailExportVo.setName(popBillPaymentDetailPo.getName());
            onlinePayPopBillPaymentDetailExportVo.setFlowNo(popBillPaymentDetailPo.getFlowNo());
            onlinePayPopBillPaymentDetailExportVo.setCreateTime(popBillPaymentDetailPo.getCreateTime());
            onlinePayPopBillPaymentDetailExportVo.setBillNo(popBillPaymentDetailPo.getBillNo());
            onlinePayPopBillPaymentDetailExportVo.setProductMoney(popBillPaymentDetailPo.getProductMoney());
            onlinePayPopBillPaymentDetailExportVo.setFreightAmount(popBillPaymentDetailPo.getFreightAmount());
            onlinePayPopBillPaymentDetailExportVo.setTotalMoney(popBillPaymentDetailPo.getTotalMoney());
            onlinePayPopBillPaymentDetailExportVo.setShopTotalDiscount(popBillPaymentDetailPo.getShopTotalDiscount());
            onlinePayPopBillPaymentDetailExportVo.setPlatformTotalDiscount(popBillPaymentDetailPo.getPlatformTotalDiscount());
            onlinePayPopBillPaymentDetailExportVo.setMoney(popBillPaymentDetailPo.getMoney());
            onlinePayPopBillPaymentDetailExportVo.setHireMoney(popBillPaymentDetailPo.getHireMoney());
            onlinePayPopBillPaymentDetailExportVo.setStatementTotalMoney(popBillPaymentDetailPo.getStatementTotalMoney());
            onlinePayPopBillPaymentDetailExportVo.setBillCreateTime(popBillPaymentDetailPo.getBillCreateTime());
            onlinePayPopBillPaymentDetailExportVo.setBillPaymentTime(null!=popBillPaymentPo?popBillPaymentPo.getBillPaymentTime():null);
            onlinePayPopBillPaymentDetailExportVo.setPayableCommission(popBillPaymentDetailPo.getPayableCommission());
            onlinePayPopBillPaymentDetailExportVo.setSettlementType(popBillPaymentDetailPo.getSettlementType());
            onlinePayPopBillPaymentDetailExportVos.add(onlinePayPopBillPaymentDetailExportVo);
        });
        return onlinePayPopBillPaymentDetailExportVos;
    }

    public static List<PopBillPaymentDto> convertPopBillPaymentDto(List<PopBillPaymentPo> popBillPaymentPoList) {
        List<PopBillPaymentDto> popBillPaymentDtoList = new ArrayList<>();
        popBillPaymentPoList.forEach(popBillPaymentPo -> {
            PopBillPaymentDto popBillPaymentDto = new PopBillPaymentDto();
            popBillPaymentDto.setId(popBillPaymentPo.getId());
            popBillPaymentDto.setOrgId(popBillPaymentPo.getOrgId());
            popBillPaymentDto.setOrgName(popBillPaymentPo.getOrgName());
            popBillPaymentDto.setFlowNo(popBillPaymentPo.getFlowNo());
            popBillPaymentDto.setProductMoney(popBillPaymentPo.getProductMoney());
            popBillPaymentDto.setTotalMoney(popBillPaymentPo.getTotalMoney());
            popBillPaymentDto.setMoney(popBillPaymentPo.getMoney());
            popBillPaymentDto.setFreightAmount(popBillPaymentPo.getFreightAmount());
            popBillPaymentDto.setCouponShopAmount(popBillPaymentPo.getCouponShopAmount());
            popBillPaymentDto.setMarketingShopAmount(popBillPaymentPo.getMarketingShopAmount());
            popBillPaymentDto.setShopTotalDiscount(popBillPaymentPo.getShopTotalDiscount());
            popBillPaymentDto.setCouponPlatformAmount(popBillPaymentPo.getCouponPlatformAmount());
            popBillPaymentDto.setMarketingPlatformAmount(popBillPaymentPo.getMarketingPlatformAmount());
            popBillPaymentDto.setPlatformTotalDiscount(popBillPaymentPo.getPlatformTotalDiscount());
            popBillPaymentDto.setHireMoney(popBillPaymentPo.getHireMoney());
            popBillPaymentDto.setPenaltyAmount(popBillPaymentPo.getPenaltyAmount());
            popBillPaymentDto.setStatementTotalMoney(popBillPaymentPo.getStatementTotalMoney());
            popBillPaymentDto.setPayType(popBillPaymentPo.getPayType());
            popBillPaymentDto.setBillPaymentStatus(popBillPaymentPo.getBillPaymentStatus());
            popBillPaymentDto.setBillPaymentTime(popBillPaymentPo.getBillPaymentTime());
            popBillPaymentDto.setRemitStatus(popBillPaymentPo.getRemitStatus());
            popBillPaymentDto.setRemitTime(popBillPaymentPo.getRemitTime());
            popBillPaymentDto.setCreateTime(popBillPaymentPo.getCreateTime());
            popBillPaymentDto.setUpdateTime(popBillPaymentPo.getUpdateTime());
            popBillPaymentDto.setUpdaterCode(popBillPaymentPo.getUpdaterCode());
            popBillPaymentDto.setUpdaterName(popBillPaymentPo.getUpdaterName());
            popBillPaymentDtoList.add(popBillPaymentDto);
        });
        return popBillPaymentDtoList;
    }
}
