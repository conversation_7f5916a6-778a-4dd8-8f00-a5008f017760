package com.xyy.ec.pop.helper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.ec.marketing.client.dto.discount.OrderPromoDetail;
import com.xyy.ec.merchant.bussiness.enums.BusinessTypeEnum;
import com.xyy.ec.order.backend.framework.enums.OrderPayChannelEnum;
import com.xyy.ec.order.backend.framework.enums.OrderStatusEnum;
import com.xyy.ec.order.backend.model.OrderOperateLogDto;
import com.xyy.ec.order.backend.order.dto.OrderRemarksDto;
import com.xyy.ec.order.backend.order.query.dto.OrderDto;
import com.xyy.ec.order.backend.pop.dto.PopOrderDto;
import com.xyy.ec.order.backend.pop.dto.PopOrderRefundDetailDto;
import com.xyy.ec.order.backend.pop.dto.PopOrderRefundDto;
import com.xyy.ec.order.backend.query.Page;
import com.xyy.ec.order.business.dto.OrderDetailBusinessDto;
import com.xyy.ec.order.business.enums.promo.OrderPromoTypeEnum;
import com.xyy.ec.pop.adapter.dto.order.OrderDeliveryAdapterDto;
import com.xyy.ec.pop.adapter.dto.order.OrderLogisticsDetailAdapterDto;
import com.xyy.ec.pop.vo.*;
import com.xyy.ec.pop.vo.order.OrderAdminVo;
import com.xyy.ec.pop.vo.order.OrderDeliveryVo;
import com.xyy.ec.pop.vo.order.OrderDetailDiscountVo;
import com.xyy.ec.pop.vo.order.OrderLogisticsDetailVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date  2020/12/22 14:51
* @table
*/
public class OrderHelper {

    public static List<OrderDeliveryVo> convertOrderDeliveryVo(List<OrderDeliveryAdapterDto> orderDeliveryMessageList, String logiCompany) {
        List<OrderDeliveryVo> orderDeliveryVoList = new ArrayList<>();

        orderDeliveryMessageList.forEach(orderDeliveryAdapterDto -> {
            OrderDeliveryVo orderDeliveryVo = new OrderDeliveryVo();
            orderDeliveryVo.setLogisticsCompany(logiCompany);
            orderDeliveryVo.setOrderNo(orderDeliveryAdapterDto.getOrderNo());
            orderDeliveryVo.setWaybillNo(orderDeliveryAdapterDto.getWaybillNo());
            orderDeliveryVo.setCurrentLogisticsStateName(orderDeliveryAdapterDto.getCurrentLogisticsStateName());

            List<OrderLogisticsDetailAdapterDto> orderLogisticsDetailAdapterDtos = orderDeliveryAdapterDto.getOrderLogisticsDetailAdapterDtos();

            List<OrderLogisticsDetailVo> orderLogisticsDetailVoList = getOrderLogisticsDetailVos(orderLogisticsDetailAdapterDtos);
            orderDeliveryVo.setOrderLogisticsDetailVoList(orderLogisticsDetailVoList);
            orderDeliveryVoList.add(orderDeliveryVo);
        });
        return orderDeliveryVoList;
    }

    private static List<OrderLogisticsDetailVo> getOrderLogisticsDetailVos(List<OrderLogisticsDetailAdapterDto> orderLogisticsDetailAdapterDtos) {
        List<OrderLogisticsDetailVo> orderLogisticsDetailVoList = new ArrayList<>();
        orderLogisticsDetailAdapterDtos.forEach(orderLogisticsDetailAdapterDto -> {
            OrderLogisticsDetailVo orderLogisticsDetailVo = new OrderLogisticsDetailVo();
            orderLogisticsDetailVo.setWaybillNo(orderLogisticsDetailAdapterDto.getWaybillNo());
            orderLogisticsDetailVo.setDescription(orderLogisticsDetailAdapterDto.getDescription());
            orderLogisticsDetailVo.setDeliveryTime(orderLogisticsDetailAdapterDto.getDeliveryTime());
            orderLogisticsDetailVo.setCreateDate(orderLogisticsDetailAdapterDto.getCreateDate());
            orderLogisticsDetailVoList.add(orderLogisticsDetailVo);
        });
        return orderLogisticsDetailVoList;
    }

    public static PopOrderDto convertOrderToPopOrderDto(OrderAdminVo order) {
        if (order == null) {
            return null;
        }
        PopOrderDto popOrderDto = new PopOrderDto();
        BeanUtils.copyProperties(order, popOrderDto);
        String statusStr = order.getStatusList();
        List<Integer> statusList = JSON.parseArray(statusStr, Integer.class);
        popOrderDto.setStatusList(statusList);
        popOrderDto.setUnDeliveredHour(order.getTimeoutHours());
        popOrderDto.setFirstOrderFlag(order.getFirstOrderFlag());
        popOrderDto.setOrderChannel(order.getOrderType());
        if(StringUtils.isNotEmpty(order.getBranchCode())){
            popOrderDto.setIsThirdCompany(-1);
        }
        List<Long> provIds = order.getProvIds();
        if(CollectionUtils.isNotEmpty(provIds)){
            popOrderDto.setCompanyProvinceCodeList(provIds.stream().map(Long::intValue).collect(Collectors.toList()));
        }
        return popOrderDto;
    }

    public static com.xyy.ec.pop.base.Page<OrderAdminVo> convertOrderDtoToOrder(Page<PopOrderDto> orderDtoPage, Map<Integer, String> provinceCodeNameMap) {
        if (orderDtoPage == null) {
            return null;
        }

        com.xyy.ec.pop.base.Page<OrderAdminVo> orderVoPage = new com.xyy.ec.pop.base.Page<>();
        orderVoPage.setTotal(orderDtoPage.getTotal() == null ? 0L : orderDtoPage.getTotal());
        orderVoPage.setOffset(orderDtoPage.getOffset());
        orderVoPage.setLimit(orderDtoPage.getLimit());
        orderVoPage.setPageCount(orderDtoPage.getPageCount());
        orderVoPage.setRequestParameters(orderDtoPage.getRequestParameters());
        orderVoPage.setRequestUrl(orderDtoPage.getRequestUrl());
        orderVoPage.setCurrentPage(orderDtoPage.getCurrentPage());
        List<PopOrderDto> popOrderUseDtos = orderDtoPage.getRows();
        List<OrderAdminVo> orderVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(popOrderUseDtos)) {
//            Map<String, String> mapBranchs = mapBranchs();
            for (PopOrderDto dto : popOrderUseDtos) {
                OrderAdminVo orderVo = convertPopOrderDtoToOrder(dto);
                orderVo.setExceptionFlag(dto.getHasException());
//                orderVo.setBranchName(mapBranchs == null ? "" : mapBranchs.get(dto.getBranchCode()));
                orderVo.setBranchName(provinceCodeNameMap.get(dto.getProvinceCode()));
                orderVos.add(orderVo);
            }
        }
        orderVoPage.setRows(orderVos);
        return orderVoPage;
    }

    private static OrderAdminVo convertPopOrderDtoToOrder(PopOrderDto dto) {
        if (dto == null) {
            return null;
        }
        OrderAdminVo order = new OrderAdminVo();
        BeanUtils.copyProperties(dto, order);
        OrderPayChannelEnum orderPayChannelEnum = dto.getPayChannel()==null?null:OrderPayChannelEnum.get(dto.getPayChannel());
        order.setPayChannelName(orderPayChannelEnum==null?null:orderPayChannelEnum.getName());
        order.setFreightAmount(dto.getFreightAmount().doubleValue());
        order.setMerchantRemark(dto.getSellerRemark());
        order.setFirstOrderFlag(dto.getFirstOrderFlag());
        order.setOrderType(dto.getOrderChannel());
        order.setArrivalTime(dto.getArrivalTime());
        String businessTypeName = "";
        if (order.getBusinessType() != null && BusinessTypeEnum.maps.get(order.getBusinessType()) != null){
            businessTypeName = BusinessTypeEnum.get(order.getBusinessType());
        }
        order.setBusinessTypeName(businessTypeName);
        if(dto.getCompanyProvinceCode() != null){
            order.setProvId(dto.getCompanyProvinceCode().longValue());
        }
        order.setEvidenceImages(StringUtils.isEmpty(dto.getEvidenceImages())?new ArrayList<>(0):JSON.parseArray(dto.getEvidenceImages(),String.class));
        return order;
    }

    public static PopOrderRefundDto convertRefundOrderDTOToPopOrderRefundDto(RefundOrderParamVo refundOrder) {
        if (null == refundOrder) {
            return null;
        }
        PopOrderRefundDto popOrderRefundDto = new PopOrderRefundDto();
//        popOrderRefundDto.setIsThirdCompany(1);
        popOrderRefundDto.setIsThirdCompany(refundOrder.getIsThirdCompany());
        popOrderRefundDto.setOrgIdList(refundOrder.getOrgIdList());
        popOrderRefundDto.setOrgId(refundOrder.getOrgId());
        popOrderRefundDto.setBranchCode(refundOrder.getBranchCode());
        popOrderRefundDto.setOrderNo(refundOrder.getOrderNo());
        popOrderRefundDto.setRefundOrderNo(refundOrder.getRefundOrderNo());
        popOrderRefundDto.setAuditState(refundOrder.getAuditState());
        popOrderRefundDto.setMerchantName(refundOrder.getMerchantName());
        popOrderRefundDto.setStartCreateTime(refundOrder.getStartCreateTime());
        popOrderRefundDto.setEndCreateTime(refundOrder.getEndCreateTime());
        popOrderRefundDto.setAdminAuditStatusName(refundOrder.getAdminAuditStatusName());
        popOrderRefundDto.setStartTime(refundOrder.getStartTime());
        popOrderRefundDto.setEndTime(refundOrder.getEndTime());
        popOrderRefundDto.setPayType(refundOrder.getPayType());
        popOrderRefundDto.setProvinceCode(refundOrder.getProvinceCode());
        popOrderRefundDto.setRefundChannel(refundOrder.getRefundChannel());
        popOrderRefundDto.setRefundReason(refundOrder.getRefundReason());
        popOrderRefundDto.setPayChannel(refundOrder.getPayChannel());
        popOrderRefundDto.setRefundOrderNos(refundOrder.getRefundOrderNos());
        if(CollectionUtils.isNotEmpty(refundOrder.getProvIds())){
            popOrderRefundDto.setCompanyProvinceCodeList(refundOrder.getProvIds().stream().map(Long::intValue).collect(Collectors.toList()));
        }
        return popOrderRefundDto;
    }

    public static RefundOrderDetailAdminVo convetPopOrderRefundDtoToVo(PopOrderRefundDto dto) {
        RefundOrderDetailAdminVo refundOrderVo = new RefundOrderDetailAdminVo();
        refundOrderVo.setId(dto.getId());
        refundOrderVo.setMerchantName(dto.getMerchantName());
        refundOrderVo.setMerchantId(dto.getMerchantId());
        refundOrderVo.setAuditProcessState(dto.getAuditProcessState());
//        refundOrderVo.setBusinessName();
        if (null != dto.getPayChannel()) {
            refundOrderVo.setPayChannel(Integer.valueOf(dto.getPayChannel()));
        }

        refundOrderVo.setOrderNo(dto.getOrderNo());
        refundOrderVo.setAuditState(dto.getAuditState());
        refundOrderVo.setRefundOrderNo(dto.getRefundOrderNo());
        refundOrderVo.setRefundType(dto.getRefundType());
        refundOrderVo.setCustomerAuditTime(dto.getCustomerAuditTime());
        refundOrderVo.setRefundAuditTime(dto.getRefundAuditTime());
        refundOrderVo.setRefundCreateTime(dto.getRefundCreateTime());
        refundOrderVo.setRefundFee(dto.getRefundFee());
        refundOrderVo.setRefundActualFee(dto.getRefundActualFee());
        refundOrderVo.setAuditStatusName(dto.getAuditStatusName());
        refundOrderVo.setPayType(dto.getPayType());
        refundOrderVo.setOwner(dto.getOwner());
        refundOrderVo.setBankCard(dto.getBankCard());
        refundOrderVo.setBankName(dto.getBankName());
        refundOrderVo.setOrgId(dto.getOrgId());
        refundOrderVo.setRefundChannel(dto.getRefundChannel());
        refundOrderVo.setRefundReason(dto.getRefundReason());
        refundOrderVo.setRefundExplain(dto.getRefundExplain());
        refundOrderVo.setCreateTime(dto.getCreateTime());
        refundOrderVo.setEvidence1(dto.getEvidence1());
        refundOrderVo.setEvidence2(dto.getEvidence2());
        refundOrderVo.setFreightAmount(dto.getFreightAmount());
        refundOrderVo.setEvidence3(dto.getEvidence3());
        refundOrderVo.setEvidence4(dto.getEvidence4());
        refundOrderVo.setEvidence5(dto.getEvidence5());
        refundOrderVo.setRefundVarietyNum(dto.getRefundVarietyNum());
        refundOrderVo.setIsFbp(dto.getIsFbp());
        refundOrderVo.setIsThirdCompany(dto.getIsThirdCompany());
        refundOrderVo.setUrgencyinfo(dto.getUrgencyinfo());
        refundOrderVo.setIndemnityMoney(dto.getIndemnityMoney());
        refundOrderVo.setIndemnitySource(dto.getIndemnitySource());
        if(dto.getCompanyProvinceCode() != null){
            refundOrderVo.setProvId(dto.getCompanyProvinceCode().longValue());
        }
        refundOrderVo.setEvidenceImages(StringUtils.isEmpty(dto.getEvidenceImages())?new ArrayList<>(0):
                JSON.parseArray(dto.getEvidenceImages(),String.class).stream().map(item-> StringUtils.trimToNull(item)).filter(item->item!=null).collect(Collectors.toList()));
        if (StringUtils.isNotBlank(dto.getRefundPayEvidence())) {
            if (dto.getRefundPayEvidence().startsWith("[")){
                refundOrderVo.setRefundPayEvidence(JSON.parseArray(dto.getRefundPayEvidence(), String.class).stream().map(item -> StringUtils.trimToNull(item)).filter(item -> item != null).collect(Collectors.toList()));
            }else{
                refundOrderVo.setRefundPayEvidence(Lists.newArrayList(dto.getRefundPayEvidence()));
            }
        }else{
            refundOrderVo.setRefundPayEvidence(new ArrayList<>());
        }
        refundOrderVo.setCashPayAmount(dto.getCashPayAmount());
        refundOrderVo.setVirtualGold(dto.getVirtualGold());
        if (StringUtils.isNotBlank(dto.getImgList())) {
            List<String> list = JSONObject.parseArray(dto.getImgList(), String.class);
            refundOrderVo.setImgList(list);
        }
        refundOrderVo.setPayStatus(dto.getPayStatus());
        return refundOrderVo;
    }

    public static RefundOrderDetailDto convetPopOrderRefundDetailDtoToVo(PopOrderRefundDetailDto dto) {
        RefundOrderDetailDto refundOrderDetailVo = new RefundOrderDetailDto();
        refundOrderDetailVo.setBarcode(dto.getBarcode());
        refundOrderDetailVo.setImageUrl(dto.getImageUrl());
        refundOrderDetailVo.setManufacturer(dto.getManufacturer());
        refundOrderDetailVo.setProductAmount(dto.getProductAmount());
        refundOrderDetailVo.setOrderDetailId(dto.getOrderDetailId());
        refundOrderDetailVo.setRefundOrderNo(dto.getRefundOrderNo());
        refundOrderDetailVo.setProductPrice(dto.getProductPrice());
        refundOrderDetailVo.setProductName(dto.getProductName());
        refundOrderDetailVo.setSpec(dto.getSpec());
        refundOrderDetailVo.setRefundFee(dto.getRefundFee());
        refundOrderDetailVo.setSkuId(dto.getSkuId());
        refundOrderDetailVo.setCashPayAmount(dto.getCashPayAmount());
        refundOrderDetailVo.setVirtualGold(dto.getVirtualGold());
        return refundOrderDetailVo;
    }

    public static OrderDetail convetOrderDetailDtoToVo(OrderDetailBusinessDto dto) {
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setId(dto.getId());
        orderDetail.setProductName(dto.getProductName());
        orderDetail.setBarcode(dto.getBarcode());
        orderDetail.setPackageId(String.valueOf(dto.getPackageId()));
        orderDetail.setPackageCount(String.valueOf(dto.getPackageCount()));
        orderDetail.setManufacturer(dto.getManufacturer());
        orderDetail.setSpec(dto.getSpec());
        orderDetail.setProductPrice(dto.getProductPrice() == null ? null : dto.getProductPrice().doubleValue());
        orderDetail.setProductAmount(dto.getProductAmount());
        orderDetail.setRefundProductAmount(String.valueOf(dto.getRefundProductAmount()));
        orderDetail.setType(dto.getType());
        orderDetail.setMediumPackageNum(dto.getMediumPackageNum());
        orderDetail.setIsSplit(dto.getIsSplit());
        orderDetail.setStatus(dto.getStatus());
        orderDetail.setDiscountAmount(dto.getDiscountAmount());
        orderDetail.setSubTotal(dto.getSubTotal());
        orderDetail.setRealPayAmount(dto.getRealPayAmount());
        orderDetail.setSkuId(dto.getSkuId());
//        orderDetail.setOrderConsignmentDetails();
        orderDetail.setCommissionRatio(dto.getCommissionRatio());
//        orderDetail.setBatchConsNumTotal();
//        orderDetail.setBatchPriceTotal();
//        orderDetail.setCanSendSkuCount();
//        orderDetail.setCanSendSkuAmount();
//        orderDetail.setErpCode();
        orderDetail.setShopVoucherAmount(dto.getShopVoucherAmount());
        orderDetail.setCrossPlatformVoucherAmount(dto.getCrossPlatformVoucherAmount());
//        orderDetail.setShopPromoDiscount();
//        orderDetail.setPlatformPromoDiscount();
        orderDetail.setOriginalTotalDiscount(dto.getOriginalTotalDiscount());
        orderDetail.setOriginalTotalAmount(dto.getOriginalTotalAmount());
        orderDetail.setAllPromoId(dto.getAllPromoId());
        orderDetail.setPromotionId(dto.getPromotionId());
        orderDetail.setOrigPrice(dto.getOrigPrice() == null ? null : BigDecimal.valueOf(dto.getOrigPrice()));
        orderDetail.setCashPayAmount(dto.getCashPayAmount());
        orderDetail.setVirtualGold(dto.getVirtualGold());
        orderDetail.setExtraGiftId(dto.getExtraGiftId());
        orderDetail.setExtraGift(dto.getExtraGift());
        //处理活动显示格式
        manageAllPromoId(orderDetail);
        return orderDetail;
    }

    /**
     * T开头代表特价，没有使用-表示
     */
    public static void manageAllPromoId(OrderDetail detail) {
        String allPromoId = detail.getAllPromoId();
        //参与活动字段为空
        if (org.springframework.util.StringUtils.isEmpty(allPromoId)) {
            detail.setAllPromoId("—");
            return;
        }
        //只能参与一个特价
        String[] split = allPromoId.split(",");
        List<String> tips = new ArrayList<>(2);
        for (String promoId : split) {
            if (promoId.startsWith("T")) {
                tips.add(promoId.replaceFirst("T","特价"));
            } else if (promoId.startsWith("P")) {
                tips.add(promoId.replaceFirst("P","拼团"));
            } else if (promoId.startsWith("H")) {
                tips.add(promoId.replaceFirst("H","红包"));
            }
        }
        if (Objects.nonNull(detail.getExtraGiftId())){
            tips.add("满赠"+detail.getExtraGiftId());
        }
        if(tips.isEmpty()){
            detail.setAllPromoId("—");
        }else {
            detail.setAllPromoId(StringUtils.join(tips,"<br>"));
        }
    }

    public static OrderDetailDiscountVo getPromoDetial(List<OrderPromoDetail> orderPromoDetails) {
        if (CollectionUtils.isEmpty(orderPromoDetails)) {
            return null;
        }

        //店铺优惠提示信息集合
        List<String> shopDiscountMsgList = com.beust.jcommander.internal.Lists.newArrayList();
        //平台优惠提示信息集合
        List<String> platformDiscountMsgList = com.beust.jcommander.internal.Lists.newArrayList();
        //拼团商家优惠：8(拼团)&&292(店铺)
        BigDecimal shopGroupDiscount = orderPromoDetails.stream().filter(t -> t.getPromoType() != null && t.getPromoType() == OrderPromoTypeEnum.GROUP_PURCHASE.getValue() && t.getInitiatorId() != null && t.getInitiatorId().equals(292L)).map(m -> m.getDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (shopGroupDiscount.compareTo(BigDecimal.ZERO) > 0) {
            shopDiscountMsgList.add("拼团优惠：" + shopGroupDiscount);
        }
        //特价优惠：2(特价)
        BigDecimal specialPriceDiscount = orderPromoDetails.stream().filter(t -> t.getPromoType() != null && t.getPromoType() == OrderPromoTypeEnum.PROMOTION.getValue() && t.getInitiatorId() != null && t.getInitiatorId().equals(292L)).map(m -> m.getDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (specialPriceDiscount.compareTo(BigDecimal.ZERO) > 0) {
            shopDiscountMsgList.add("特价优惠：" + specialPriceDiscount);
        }
        //店铺套餐-店铺：22(POP店铺券)&&292(店铺)
        BigDecimal shopPackageForShopCoupon = orderPromoDetails.stream().filter(t -> t.getPromoType() != null && t.getPromoType() == OrderPromoTypeEnum.SHOP_PACKAGE.getValue() && t.getInitiatorId() != null && t.getInitiatorId().equals(292L)).map(m -> m.getDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (shopPackageForShopCoupon.compareTo(BigDecimal.ZERO) > 0) {
            shopDiscountMsgList.add("店铺套餐：" + shopPackageForShopCoupon);
        }
        //店铺券商家优惠：33(POP店铺券)&&292(店铺)
        BigDecimal shopDiscountForShopCoupon = orderPromoDetails.stream().filter(t -> t.getPromoType() != null && t.getPromoType() == OrderPromoTypeEnum.POP_COUPON.getValue() && t.getInitiatorId() != null && t.getInitiatorId().equals(292L)).map(m -> m.getDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (shopDiscountForShopCoupon.compareTo(BigDecimal.ZERO) > 0) {
            shopDiscountMsgList.add("店铺券优惠：" + shopDiscountForShopCoupon);
        }
        //平台券商家优惠：34(平台券)&&292(店铺)
        BigDecimal shopDiscountForPlatformCoupon = orderPromoDetails.stream().filter(t -> t.getPromoType() != null && t.getPromoType() == OrderPromoTypeEnum.CROSS_SHOP_COUPON.getValue() && t.getInitiatorId() != null && t.getInitiatorId().equals(292L)).map(m -> m.getDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (shopDiscountForPlatformCoupon.compareTo(BigDecimal.ZERO) > 0) {
            shopDiscountMsgList.add("平台券优惠：" + shopDiscountForPlatformCoupon);
        }

        //拼团平台优惠：8(拼团)&&!292(平台)
        BigDecimal platformGroupDiscount = orderPromoDetails.stream().filter(t -> t.getPromoType() != null && t.getPromoType() == OrderPromoTypeEnum.GROUP_PURCHASE.getValue() && (t.getInitiatorId() == null || !t.getInitiatorId().equals(292L))).map(m -> m.getDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (platformGroupDiscount.compareTo(BigDecimal.ZERO) > 0) {
            platformDiscountMsgList.add("拼团优惠：" + platformGroupDiscount);
        }
        //店铺套餐-平台优惠：22(POP店铺券)&&!292(平台)
        BigDecimal platformPackageForShopCoupon = orderPromoDetails.stream().filter(t -> t.getPromoType() != null && t.getPromoType() == OrderPromoTypeEnum.SHOP_PACKAGE.getValue() && (t.getInitiatorId() == null || !t.getInitiatorId().equals(292L))).map(m -> m.getDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (platformPackageForShopCoupon.compareTo(BigDecimal.ZERO) > 0) {
            platformDiscountMsgList.add("店铺套餐：" + platformPackageForShopCoupon);
        }
        //店铺券平台优惠：33(POP店铺券)&&!292(平台)
        BigDecimal platformDiscountForShopCoupon = orderPromoDetails.stream().filter(t -> t.getPromoType() != null && t.getPromoType() == OrderPromoTypeEnum.POP_COUPON.getValue() && (t.getInitiatorId() == null || !t.getInitiatorId().equals(292L))).map(m -> m.getDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (platformDiscountForShopCoupon.compareTo(BigDecimal.ZERO) > 0) {
            platformDiscountMsgList.add("店铺券优惠：" + platformDiscountForShopCoupon);
        }
        //平台券平台优惠：34(平台券)&&!292(平台)
        BigDecimal platformDiscountForPlatformCoupon = orderPromoDetails.stream().filter(t -> t.getPromoType() != null && t.getPromoType() == OrderPromoTypeEnum.CROSS_SHOP_COUPON.getValue() && (t.getInitiatorId() == null || !t.getInitiatorId().equals(292L))).map(m -> m.getDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (platformDiscountForPlatformCoupon.compareTo(BigDecimal.ZERO) > 0) {
            platformDiscountMsgList.add("平台券优惠：" + platformDiscountForPlatformCoupon);
        }
        //红包平台优惠：35(红包)&&!292(平台)
        BigDecimal redPacketForPlatformCoupon = orderPromoDetails.stream().filter(t -> t.getPromoType() != null && t.getPromoType() == OrderPromoTypeEnum.RED_PACKET.getValue() && (t.getInitiatorId() == null || !t.getInitiatorId().equals(292L))).map(m -> m.getDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (redPacketForPlatformCoupon.compareTo(BigDecimal.ZERO) > 0) {
            platformDiscountMsgList.add("红包优惠：" + redPacketForPlatformCoupon);
        }
        BigDecimal payDiscount = orderPromoDetails.stream().filter(t -> t.getPromoType() != null && t.getPromoType() == OrderPromoTypeEnum.PAYMENT_DISCOUNT.getValue() && (t.getInitiatorId() == null || !t.getInitiatorId().equals(292L))).map(m -> m.getDiscountAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (payDiscount.compareTo(BigDecimal.ZERO) > 0) {
            platformDiscountMsgList.add("支付优惠：" + payDiscount);
        }
        OrderDetailDiscountVo orderDetailDiscountVo = new OrderDetailDiscountVo();
        orderDetailDiscountVo.setShopDiscountMsgList(shopDiscountMsgList);
        orderDetailDiscountVo.setPlatformDiscountMsgList(platformDiscountMsgList);
        return orderDetailDiscountVo;
    }


    public static List<OrderStatusRecordsVo> convertRpcRestToOrderStatusRecordsVo(List<OrderOperateLogDto> businessDtos, List<PopOrderDto> orderDtos) {
        List<OrderStatusRecordsVo> orderStatusRecordsVolist = Lists.newArrayList();
        PopOrderDto ecOrderDto = orderDtos.stream().findFirst().get();
        for (OrderOperateLogDto businessDto : businessDtos) {
            if (checkStatus(businessDto.getStatus())) {
                continue;
            }
            orderStatusRecordsVolist.add(convertRpcRestToOrderStatusRecordsVo(businessDto, ecOrderDto));

        }
        return orderStatusRecordsVolist;


    }

    private static boolean checkStatus(Integer status) {
        if (OrderStatusEnum.DELETED.getValue() == status || OrderStatusEnum.SEPARATED.getValue() == status) {
            return true;
        }
        return false;
    }

    public static OrderStatusRecordsVo convertRpcRestToOrderStatusRecordsVo(OrderOperateLogDto businessDto, PopOrderDto ecOrderDto) {
        OrderStatusRecordsVo orderStatusRecordsVo = new OrderStatusRecordsVo();
        orderStatusRecordsVo.setPackagesNum(ecOrderDto.getBagNums());
        if (OrderStatusEnum.OUTBOUND.getValue() == businessDto.getStatus()) {
            //0审核通过
            orderStatusRecordsVo.setAuditResult(0);
        }else if(OrderStatusEnum.AUDITING.getValue() == businessDto.getStatus()){
            orderStatusRecordsVo.setEvidenceUrlList(StringUtils.isEmpty(ecOrderDto.getEvidenceImages())?new ArrayList<>():JSON.parseArray(ecOrderDto.getEvidenceImages(),String.class));
        }
        orderStatusRecordsVo.setPayType(ecOrderDto.getPayType());
        OrderPayChannelEnum payChannelEnum = ecOrderDto.getPayChannel() == null?null:OrderPayChannelEnum.get(ecOrderDto.getPayChannel());
        if(payChannelEnum!=null){
            orderStatusRecordsVo.setPayTypeName(ecOrderDto.getPayTypeName()+"("+payChannelEnum.getName()+")");
        }else {
            orderStatusRecordsVo.setPayTypeName(ecOrderDto.getPayTypeName());
        }
        orderStatusRecordsVo.setPayMoney(ecOrderDto.getMoney());
        orderStatusRecordsVo.setCancelReason(ecOrderDto.getCancelReason());
        orderStatusRecordsVo.setOperationTime(businessDto.getUpdateTime());
        //orderStatusRecordsVo.setOperationContent();
        orderStatusRecordsVo.setOrderStatus(businessDto.getStatus());
        orderStatusRecordsVo.setPayChannel(ecOrderDto.getPayChannel());
        orderStatusRecordsVo.setOperator(businessDto.getUpdator());
        orderStatusRecordsVo.setOperationContent(businessDto.getRemark());
        orderStatusRecordsVo.setLogType(OrderStatusRecordsVo.ORDER_LOG_TYPE.intValue());
        orderStatusRecordsVo.setIsVisible(0);
        return orderStatusRecordsVo;
    }

    public static List<OrderStatusRecordsVo> convertOrderRemarkToOrderStatusRecord(List<OrderRemarksDto> orderRemarksDtos, List<PopOrderDto> rows) {
        if (CollectionUtils.isEmpty(orderRemarksDtos) || CollectionUtils.isEmpty(rows)){
            return Lists.newArrayList();
        }
        PopOrderDto ecOrderDto = rows.get(0);
        List<OrderStatusRecordsVo> orderStatusRecordsVos = Lists.newArrayList();
        for (OrderRemarksDto orderRemarksDto :  orderRemarksDtos){
            OrderStatusRecordsVo orderStatusRecordsVo = new OrderStatusRecordsVo();
            orderStatusRecordsVo.setPackagesNum(ecOrderDto.getBagNums());
            orderStatusRecordsVo.setPayType(ecOrderDto.getPayType());
            OrderPayChannelEnum payChannelEnum = ecOrderDto.getPayChannel() == null?null:OrderPayChannelEnum.get(ecOrderDto.getPayChannel());
            if(payChannelEnum!=null){
                orderStatusRecordsVo.setPayTypeName(ecOrderDto.getPayTypeName()+"("+payChannelEnum.getName()+")");
            }else {
                orderStatusRecordsVo.setPayTypeName(ecOrderDto.getPayTypeName());
            }
            orderStatusRecordsVo.setPayMoney(ecOrderDto.getMoney());
            orderStatusRecordsVo.setCancelReason(ecOrderDto.getCancelReason());
            orderStatusRecordsVo.setOperationTime(orderRemarksDto.getCreateTime());
            orderStatusRecordsVo.setOperationContent(orderRemarksDto.getRemark());
            orderStatusRecordsVo.setPayChannel(ecOrderDto.getPayChannel());
            orderStatusRecordsVo.setOperator(orderRemarksDto.getOperator());
            orderStatusRecordsVo.setLogType(OrderStatusRecordsVo.REMARK_TYPE.intValue());
            if (StringUtils.isNotEmpty(orderRemarksDto.getRemark()) && orderRemarksDto.getRemark().startsWith("部分发货")){
                orderStatusRecordsVo.setLogType(OrderStatusRecordsVo.ORDER_LOG_TYPE.intValue());//部分发货不展示发送  bd按钮
            }
            orderStatusRecordsVo.setIsVisible(orderRemarksDto.getIsVisible());
            orderStatusRecordsVos.add(orderStatusRecordsVo);
        }

        return orderStatusRecordsVos;
    }
}
