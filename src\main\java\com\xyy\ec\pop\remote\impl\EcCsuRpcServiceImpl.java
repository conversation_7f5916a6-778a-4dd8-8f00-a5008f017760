package com.xyy.ec.pop.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.exception.promo.XyyEcPromotionBizNoneCheckRTException;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.remote.EcCsuRpcService;
import com.xyy.ec.product.back.end.ecp.csu.api.CsuBackEndApi;
import com.xyy.ec.product.back.end.ecp.csu.api.EcCsuBackEndApi;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.param.EcSkuExamineParamDTO;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.query.EcSkuSellerAdminPageQuery;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.result.EcSkuDetailDto;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.result.SkuExamineLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class EcCsuRpcServiceImpl implements EcCsuRpcService {

    @Reference(version = "1.0.0")
    EcCsuBackEndApi ecCsuBackEndApi;
    @Reference(version = "1.0.0", timeout = 60000)
    private CsuBackEndApi csuBackEndApi;

    @Override
    public PageInfo<EcSkuDetailDto> adminQuery(EcSkuSellerAdminPageQuery pageQuery) {
        if(log.isDebugEnabled()){
            log.debug("EcCsuRpcServiceImpl.adminQuery pageQuery:{}", JSONObject.toJSONString(pageQuery));
        }
        try {
            ApiRPCResult<PageInfo<EcSkuDetailDto>> pageInfoApiRPCResult = ecCsuBackEndApi.adminQuery(pageQuery);
            if(!pageInfoApiRPCResult.isSuccess()){
                log.warn("ecCsuBackEndApi.adminQuery error, pageQuery:{}, result:{}", JSONObject.toJSONString(pageQuery), JSONObject.toJSONString(pageInfoApiRPCResult));
                throw new XyyEcPromotionBizNoneCheckRTException(ApiResultCodeEum.QUERY_ERROR.getCode(),"根据条件获取商品列表异常",null);
            }
            PageInfo<EcSkuDetailDto> result = pageInfoApiRPCResult.getData();
            if(log.isDebugEnabled()){
                log.debug("EcCsuRpcServiceImpl.adminQuery pageQuery:{}, result:{}", JSONObject.toJSONString(pageQuery), JSONObject.toJSONString(result));
            }
            return result;
        }catch (Exception e){
            log.warn("ecCsuBackEndApi.adminQuery error, pageQuery:{}, msg:{}", JSONObject.toJSONString(pageQuery), e);
            throw new XyyEcPromotionBizNoneCheckRTException(ApiResultCodeEum.QUERY_ERROR.getCode(),"根据条件获取商品列表异常",null);
        }
    }

    @Override
    public EcSkuDetailDto getSkuDetailByIdAndShopCode(Long id, String shopCode) {
        if(log.isDebugEnabled()){
            log.debug("EcCsuRpcServiceImpl.getSkuDetailByIdAndShopCode id:{}, shopCode:{}", id, shopCode);
        }
        try {
            ApiRPCResult<EcSkuDetailDto> detailResult = ecCsuBackEndApi.getSkuDetailByIdAndShopCode(id, shopCode);
            if(!detailResult.isSuccess()){
                log.warn("ecCsuBackEndApi.getSkuDetailByIdAndShopCode error, id:{}, shopCode:{}, result:{}", id, shopCode, JSONObject.toJSONString(detailResult));
                throw new XyyEcPromotionBizNoneCheckRTException(ApiResultCodeEum.QUERY_ERROR.getCode(),"根据条件获取商品列表异常",null);
            }
            EcSkuDetailDto result = detailResult.getData();
            if(log.isDebugEnabled()){
                log.debug("EcCsuRpcServiceImpl.getSkuDetailByIdAndShopCode id:{}, shopCode:{}, result:{}", id, shopCode, JSONObject.toJSONString(result));
            }
            return result;
        }catch (Exception e){
            log.warn("ecCsuBackEndApi.getSkuDetailByIdAndShopCode error, id:{}, shopCode:{}, msg:{}", id, shopCode, e);
            throw new XyyEcPromotionBizNoneCheckRTException(ApiResultCodeEum.QUERY_ERROR.getCode(),"根据id获取商品信息异常",null);
        }
    }

    @Override
    public Boolean audit(EcSkuExamineParamDTO param) {
        if(log.isDebugEnabled()){
            log.debug("EcCsuRpcServiceImpl.audit param:{}", JSONObject.toJSONString(param));
        }
        try {
            ApiRPCResult<Boolean> auditResult = ecCsuBackEndApi.audit(param);
            if(!auditResult.isSuccess()){
                log.warn("ecCsuBackEndApi.audit error, param:{}, result:{}", JSONObject.toJSONString(param), JSONObject.toJSONString(auditResult));
                throw new XyyEcPromotionBizNoneCheckRTException(ApiResultCodeEum.SYSTEM_ERROR.getCode(),"审核异常",null);
            }
            Boolean result = auditResult.getData();
            if(log.isDebugEnabled()){
                log.debug("EcCsuRpcServiceImpl.audit param:{}, result:{}", JSONObject.toJSONString(param), result);
            }
            return result;
        }catch (Exception e){
            log.warn("ecCsuBackEndApi.audit error, param:{}, msg:{}", JSONObject.toJSONString(param), e);
            throw new XyyEcPromotionBizNoneCheckRTException(ApiResultCodeEum.SYSTEM_ERROR.getCode(),"审核异常",null);
        }
    }

    @Override
    public List<SkuExamineLogDTO> getExamineLogsBySkuId(Long skuId) {
        if (log.isDebugEnabled()) {
            log.debug("EcCsuRpcServiceImpl.getExamineLogsBySkuId skuId:{}", skuId);
        }
        try {
            ApiRPCResult<List<SkuExamineLogDTO>> auditLogResult = ecCsuBackEndApi.getExamineLogsBySkuId(skuId);
            if (!auditLogResult.isSuccess()) {
                log.warn("ecCsuBackEndApi.getExamineLogsBySkuId error, skuId:{}, result:{}", skuId, JSONObject.toJSONString(auditLogResult));
                throw new XyyEcPromotionBizNoneCheckRTException(ApiResultCodeEum.SYSTEM_ERROR.getCode(), "查询审核日志异常", null);
            }
            List<SkuExamineLogDTO> auditLogList = auditLogResult.getData();
            if (log.isDebugEnabled()) {
                log.debug("EcCsuRpcServiceImpl.getExamineLogsBySkuId skuId:{}, result:{}", skuId, JSONObject.toJSONString(auditLogList));
            }
            return auditLogList;
        } catch (Exception e) {
            log.warn("ecCsuBackEndApi.getExamineLogsBySkuId error, skuId:{}, msg:{}", skuId, e);
            throw new XyyEcPromotionBizNoneCheckRTException(ApiResultCodeEum.SYSTEM_ERROR.getCode(), "查询审核日志异常", null);
        }
    }
}
