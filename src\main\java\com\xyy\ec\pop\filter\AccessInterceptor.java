/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.xyy.ec.pop.filter;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.redis.XyyJedisCluster;
import com.xyy.ec.pop.utils.Constants;
import com.xyy.ec.pop.utils.cookie.CookieTool;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.concurrent.TimeUnit;

/**
 * 资源访问拦截器
 *
 * <AUTHOR>
 */
//@Component
public class AccessInterceptor extends HandlerInterceptorAdapter{

    private static final Logger LOGGER = LoggerFactory.getLogger(AccessInterceptor.class);

    @Autowired
    private XyyJedisCluster xyyJedisCluster;
    @Value("${system.session.timeout:24}")
    private Integer systemSessionTime;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        boolean doFilter = true;
        String origin =request.getHeader("Origin");
        if(StringUtils.isNotBlank(origin)){
            response.setHeader("Access-Control-Allow-Origin", origin);
        } else {
            response.setHeader("Access-Control-Allow-Origin", "*");
        }
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "GET, HEAD, POST, PUT, PATCH, DELETE, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "86400");
        response.setHeader("Access-Control-Allow-Headers", "Accept,Origin,X-Requested-With,Content-Type,Last-Modified,device,token");
        //判断session是否过期或用户是否登录
        SysUser sysUser = getUser();
        if (sysUser==null) {
            doFilter = false;
        }else{
            String sid = request.getSession().getId();
            xyyJedisCluster.expireKey(sid, systemSessionTime, TimeUnit.HOURS);/** 刷新指定key的时间 **/
        }
        if (!doFilter) {
            response.setContentType("text/html; charset=UTF-8");
            //如果是ajax请求响应头会有，x-requested-with；
            if (request.getHeader("x-requested-with") != null && request.getHeader("x-requested-with") .equalsIgnoreCase("XMLHttpRequest")) {
                response.setHeader("sessionstatus", "timeout");//在响应头设置session状态
            } else {
                PrintWriter out = response.getWriter();
                StringBuilder builder = new StringBuilder();
                builder.append("<script type=\"text/javascript\" charset=\"UTF-8\">");
                builder.append("alert(\"会话过期，请重新登录\");");
                builder.append("window.top.location.href=\"");
                String path=request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+request.getContextPath();
                builder.append(path+"/login/index");
                builder.append("\";</script>");
                out.print(builder.toString());
                out.close();
            }
            return false;
        }
        return true;
    }
    public SysUser getUser(){
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) ra).getRequest();
        Cookie cookie = CookieTool.getCookie(request, Constants.SESSION_ID);
        if (cookie != null) {
            try {
                String json = xyyJedisCluster.get(cookie.getValue());
                SysUser user = (SysUser) JSON.parseObject(json, SysUser.class);
                return user;
            } catch (Exception e) {
                LOGGER.error(
                        "分布式缓存中没有sid=" + cookie.getValue() + "的用户"
                                + e.getMessage(), e);
            }

        }
        return null;
    }
}
