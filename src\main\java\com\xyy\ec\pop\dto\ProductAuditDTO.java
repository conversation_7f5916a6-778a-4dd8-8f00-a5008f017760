/**
 * Copyright (C), 2015-2019, XXX有限公司
 * FileName: BranchOrgQueryDTO
 * Author:   danshiyu
 * Date:     2019/11/21 10:48
 * Description: 商户查询入参
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.xyy.ec.pop.dto;

import com.xyy.ec.pop.server.api.product.dto.PopSkuQualificationDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 〈一句话功能简述〉<br>
 * 〈商户查询入参〉
 *
 * <AUTHOR>
 * @create 2019/11/21
 * @since 1.0.0
 */
@Data
@ToString
@NoArgsConstructor
public class ProductAuditDTO implements Serializable {
    private static final long serialVersionUID = -9068004270866045712L;
    /**
     * 商品id
     */
    private Long id;
    /**
     * 商品编号
     */
    private String barcode;
    /**
     * 关联分类
     */
    private String productCategoryIds;
    /**
     * 推荐分类
     */
    private String productCategoryId;

    /**
     * 经营分类
     */
    private String skuCategory;
    /**
     * 经营分类id
     */
    private Long skuCategoryId;
    /**
     * 审核状态
     */
    private int status;
    /**
     * 原因
     */
    private String remark;
    /**
     * 一级发布分类id
     */
    private Long erpFirstCategoryId;
    /**
     * 二级发布分类id
     */
    private Long erpSecondCategoryId;
    /**
     * 三级发布分类id
     */
    private Long erpThirdCategoryId;
    /**
     * 四级发布分类id
     */
    private Long erpFourthCategoryId;

    /**
     * 商品首营资质
     */
    private List<PopSkuQualificationDto> skuQualificationList;
    private String skuQualificationVoListStr;
    /**
     * 包装单位
     */
    private String productUnit;
    /**
     * 剂型
     */
    private String dosageForm;
    /**
     * 存储条件
     */
    private String storageCondition;
    /**
     * 处方类型
     */
    private Integer drugClassification;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 展示名称
     */
    private String showName;
    /**
     * 通用名称
     */
    private String commonName;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 69码
     */
    private String code;
    /**
     * 批文
     */
    private String approvalNumber;
    /**
     * 规格
     */
    private String spec;
    /**
     * 上市许可持有人
     */
    private String marketAuthor;
    /**
     * 有效期/保质期
     */
    private String term;
    /**
     * 产地
     */
    private String producer;
    /**
     * 生产许可证或备案凭证编号
     */
    private String manufacturingLicenseNo;

}
