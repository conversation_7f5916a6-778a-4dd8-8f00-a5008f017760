 input{
   background: -webkit-gradient(linear,0 0,0 100%,from(#fff),to(#fff));
   border:1px solid #ccc;
 }
 input:focus {
     outline:none;
 }

 .clear:after {
     visibility:hidden;
     display:block;
     font-size:0;
     content:" ";
     clear:both;
     height:0;
 }
 .clear {
     zoom:1;
 }
 .hd-tips{
     padding: 10px 0;
 }
 .hd-setp{
     height: 55px;
     line-height: 55px;
     background: rgba(242, 242, 242, 1);
 }
 .hd-setp-item {
     display: inline-block;
     width: 30%;
     height: 55px;
     float: left;
     text-align: center;
     font-size: 18px;
     cursor: pointer;
 }
 .hd-setp-item-cur{
     color: #169BD5;
 }
 .hd-setp-cont{
     padding: 40px 0;
 }
 .hd-setp-cont .form-control-feedback{
     font-size: 22px;
     right: 12px;
 }
 .table-list thead{
     background: rgba(242, 242, 242, 1);
 }
 .hds-tab{
     height: 30px;
 }
 .hds-tab-cont{
     border:1px solid rgba(0, 153, 255, 1);
     padding: 20px 0;
 }
 .hds-tab-item{
     display: inline-block;
     width: 100px;
     height: 30px;
     line-height: 30px;
     text-align: center;
     float: left;
     margin-right: 10px;
     background: #ccc;
     position: relative;
     cursor: pointer;
 }
 .hds-tab-item-inp{
     display: inline-block;
     width: 98px;
     height: 30px;
     border: none;
     text-align: center;
     background: #ccc;
     color: #fff;
     cursor: pointer;
 }
 .hds-tab-item-cur{
     height: 31px;
     background: #fff;
     margin-bottom: -1px;
     border-left: 1px solid rgba(0, 153, 255, 1);
     border-right: 1px solid rgba(0, 153, 255, 1);
     border-top: 1px solid rgba(0, 153, 255, 1);
     cursor: pointer;
 }
 .hds-tab-item-cur .hds-tab-item-inp{
     background: #fff;
     color: #333;
     cursor: pointer;
 }
 .del-tab-btn{
     display: inline-block;
     width: 17px;
     height: 17px;
     background: #999;
     color: #fff;
     border-radius: 50%;
     text-align: center;
     line-height: 16px;
     position: absolute;
     top: -5px;
     right: -5px;
     font-size: 16px;
     z-index: 99;
 }
 .del-tab-btn-id{
     display: inline-block;
     width: 17px;
     height: 17px;
     background: #999;
     color: #fff;
     border-radius: 50%;
     text-align: center;
     line-height: 16px;
     position: absolute;
     top: -5px;
     right: -5px;
     font-size: 16px;
     z-index: 99;
 }
 .add-tab-btn{
     display: inline-block;
     width: 100px;
     height: 30px;
     line-height: 30px;
     text-align: center;
     background: rgba(0, 153, 255, 1);
     text-decoration: none;
     color: #fff;
 }
 .add-tab-btn:hover,.add-tab-btn:visited,.add-tab-btn:link{
     color: #fff;
     text-decoration: none; 
 }
 .set-area-btn:hover,.set-area-btn:visited,.set-area-btn:link{
     
     text-decoration: none; 
 }
 .add-rule-btn:hover,.add-rule-btn:visited,.add-rule-btn:link{
     color: #fff;
     text-decoration: none; 
 }
 .del-rule-btn:hover,.del-rule-btn:visited,.del-rule-btn:link{
     color: #fff;
     text-decoration: none; 
 }
 
 .set-area-btn{
     display: none;
     width: 100px;
     height: 28px;
     text-align: center;
     line-height: 28px;
     border: 1px solid rgba(0, 153, 255, 1);
     color: rgba(0, 153, 255, 1);
     position: relative;
     top: 5px;
 }
 .radio-block{
     margin-bottom: 10px;
 }
 .alert-tips{
     color: #f00;
     padding: 5px 0;
 }
 .remarkeimg{
     margin-top: 8px;
 }
 .hd-setp-box {
     display: none;
 }
 .wancheng-box{
     padding: 30px 0;
     text-align: center;
     font-size: 20px;
 }
 .next-btn4{
     margin: 20px auto;
     width: 20%;
     display:block;
 }
 .form-horizontal-box{
     display: none;
 }
 .hds-ruel-list .radio-block{
     position: relative;
 }
 .hds-ruel-list .radio-block .blockrow{
     display: block;
     width: 730px;
     padding: 10px;
     clear: both;
 }
 .hds-ruel-list .radio-block input[type="text"]{
     width: 80px;
 }
 .hds-ruel-list .radio-block .blockrow-left{
     width: 300px;
     float: left;
 }
 .hds-ruel-list .radio-block .blockrow-right{
     width: 400px;
     float: left;
 }
 .hds-ruel-list .radio-block .blockrow-left .blockrow-item{
     margin-bottom: 10px;
 }
 .hds-ruel-list .radio-block .blockrow-right .blockrow-item{
     margin-bottom: 10px;
 }
 .blockrow-item-redbao-left{
     float: left;
     display: inline-block;
     width: 120px;
 }
 .blockrow-item-redbao-left-no{
     float: left;
     display: inline-block;
     width: 120px;
     color: #fff;
 }
 .hds-ruel-list .radio-block .blockrow-right .redbao-item{
     display: inline-block;
     width: 150px;
     height: 22px;
     line-height: 22px;
     text-align: center;
     border: 1px solid rgba(0, 153, 255, 1);
     color: rgba(0, 153, 255, 1);
     margin-left: 10px;
 }
 .hds-ruel-list .radio-block .blockrow-right .add-redbao-btn{
     background: rgba(0, 153, 255, 1);
     color: #fff;
     display: inline-block;
     width: 100px;
     height: 22px;
     text-align: center;
     line-height: 22px;
     margin-left: 10px;
     text-decoration: none;
 }
 .hds-ruel-list .radio-block .blockrow-right .del-redbao-btn{
     background: #777;
     color: #fff;
     display: inline-block;
     width: 100px;
     height: 22px;
     text-align: center;
     line-height: 22px;
     margin-left: 10px;
     text-decoration: none;
 }
 .blockrow-item-redbao-item{
     margin-bottom: 5px;
 }
 .add-rule-btn{
     background: rgba(0, 153, 255, 1);
     color: #fff;
     display: inline-block;
     width: 100px;
     height: 22px;
     text-align: center;
     line-height: 22px;
     margin-left: 10px;
     text-decoration: none;
     position: absolute;
     top: 0;
     left: 735px;
     text-decoration: none;
 }
 .del-rule-btn{
     background: #999;
     color: #fff;
     display: inline-block;
     width: 100px;
     height: 22px;
     text-align: center;
     line-height: 22px;
     margin-left: 10px;
     text-decoration: none;
     position: absolute;
     top: 0;
     left: 735px;
     text-decoration: none;
 }

 .del-rule-btn-id{
     background: #999;
     color: #fff;
     display: inline-block;
     width: 100px;
     height: 22px;
     text-align: center;
     line-height: 22px;
     margin-left: 10px;
     text-decoration: none;
     position: absolute;
     top: 0;
     left: 735px;
     text-decoration: none;
 }
 .image_input{
    opacity:0;
    filter:alpha(opacity=0);
    height: 95px;
    width: 100px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
}
.image_div{
    position: relative;
    min-height: 40px;
}
.table-list{
	width:100%;
	padding:20px;
}
.button-box{
     height: 54px;
     padding: 10px ;
}
.noactiv{
	background-color: #e0e0e0;
    border-color: #dbdbdb;
}

 .hd-setp-box .button-box{
     height: 54px;
     padding: 10px ;
     background: rgba(242, 242, 242, 1);
 }
 .hd-setp-box .button-box-tit{
     font-size: 20px;
     color: #000; 
     padding-right: 30px;
 }