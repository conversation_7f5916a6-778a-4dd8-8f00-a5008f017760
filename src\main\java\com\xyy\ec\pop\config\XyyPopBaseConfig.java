/**
 * Copyright (C), 2015-2019, XXX有限公司
 * FileName: XyyPopBaseConfig
 * Author:   danshiyu
 * Date:     2019/11/14 10:04
 * Description: pop基础配置
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.xyy.ec.pop.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 〈一句话功能简述〉<br>
 * 〈pop基础配置〉
 *
 * <AUTHOR>
 * @create 2019/11/14
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pop-base")
public class XyyPopBaseConfig {
    private String ecHost;

    public String smallImgUrlPrefix;

    private String smallDescImgUrlPrefix;

    private String bigImgUrlPrefix;

    private String smallMarginImgUrl;

    private String bigMarginImgUrl;

    public String basePathUrl;

}
