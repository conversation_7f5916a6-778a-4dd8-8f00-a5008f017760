package com.xyy.ec.pop.base;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.Page;

/**
 * 实体接口映射器
 * @ClassName: Mapper 
 * <AUTHOR>
 * @date 2016-4-10 下午2:16:09 
 * @param <T,P>
 */
public interface Mapper<T, P> {
	/**
	 * 动态添加实体信息
	 * @Title: insertSelective
	 * @param entity
	 * @return
	 * int
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:16:42
	 */
	public abstract int insertSelective(T entity);

	/**
	 * 根据主键ID删除实体
	 * @Title: deleteByPrimaryKey
	 * @param id
	 * @return
	 * int
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:17:31
	 */
	public abstract int deleteByPrimaryKey(P id);
	/**
	 * 根据ID列表批量删除entity
	 * @Title: batchDeleteByIds
	 * @param ids
	 * @return
	 * int
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:18:04
	 */
	public abstract int batchDeleteByIds(List<P> ids);
	
	/**
	 * 动态更新实体信息
	 * @Title: updateByPrimaryKeySelective
	 * @param entity
	 * @return
	 * int
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:18:16
	 */
	public abstract int updateByPrimaryKeySelective(T entity);
	
	/**
	 * 根据主键id查询实体
	 * @Title: selectById
	 * @param id
	 * @return
	 * T
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:18:42
	 */
	public abstract T selectByPrimaryKey(P id);
	
	/**
	 * 查询列表,不分页
	 * @Title: selectList
	 * @param entity
	 * @return
	 * List<T>
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:20:16
	 */
	public abstract Page<T> selectList(T entity);
	/**
	 * 多条件查询总数量
	 * @Title: selectCount
	 * @param entity
	 * @return
	 * int
	 * <AUTHOR> 
	 * @date 2016-9-6 上午11:13:43
	 */
	public abstract int selectCount(T entity);

	/**
	 * 统计结果集
	 * @param params {@link Object}：查询参数，Bean或Map类型；
	 * @return {@link Page}<{@link Map}<{@link String}, {@link Object}>>：结果集。
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 上午11:25:28
	 */
	Page<Map<String, Object>> selectCounts(Object params);
	/**
	 * 查询Beans
	 * @param pm {@link Object}：查询条件，Bean或Map类型；
	 * @return {@link Page}<{@link T}>：结果集。
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 上午10:41:57
	 */
	Page<T> select(Object pm);
	/**
	 * 更新Bean
	 * @param po {@link Object}：入参，Bean或Map类型；
	 * @return {@link Integer}：条数。
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 上午10:43:02
	 */
	int update(Object pm);
	/**
	 * 删除Bean
	 * @param po {@link Object}：入参，Bean或Map类型；
	 * @return {@link Integer}：条数。
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 上午10:43:50
	 */
	int del(Object pm);
	/**
	 * 新增Bean
	 * @param po {@link Object}：入参，Bean或Map类型；
	 * @return {@link Integer}：条数。
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 上午10:45:22
	 */
	int save(Object pm);
}
