package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class RefundOrderDetailDto implements Serializable {

    private String barcode;
    private String refundOrderNo;
    private Long orderDetailId;
    private BigDecimal productPrice;
    private String erpCode;
    private Integer productAmount;
    private String productName;
    private String spec;
    private String imageUrl;
    private String manufacturer;
    private BigDecimal refundFee;
    private Long skuId;
    /**
     * 现金实付
     */
    private BigDecimal cashPayAmount;

    /**
     * 购物金
     */
    private BigDecimal virtualGold;

    /**
     * 生产许可证号或备案凭证编号
     */
    private String manufacturingLicenseNo;

    /**
     * 医疗器械注册证或备案凭证编号
     */
    private String approvalNumber;


}
