package com.xyy.ec.pop.adapter.mop;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.pop.mop.api.remote.MeSysUserRemote;
import com.xyy.pop.mop.api.remote.parameter.MeSysUserParame;
import com.xyy.pop.mop.api.remote.result.MeSysUserDto;
import com.xyy.scm.constant.entity.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class MeSysUserAdapter {
    @Reference(version = "1.0.0")
    MeSysUserRemote meSysUserRemote;
    public MeSysUserDto getUser(String accountId){//输入工号
        MeSysUserParame meSysUserParame = new MeSysUserParame();
        meSysUserParame.setAccount(accountId);
        Result<List<MeSysUserDto>> userList = meSysUserRemote.getUserList(meSysUserParame);
        if (userList.isFailure()) {
            return null;
        }
        return userList.getResult().get(0);
    }
}
