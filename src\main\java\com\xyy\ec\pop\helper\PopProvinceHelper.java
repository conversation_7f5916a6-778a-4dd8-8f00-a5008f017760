package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.vo.zhongtai.PopProvinceVo;
import com.xyy.ec.pop.vo.zhongtai.ZhongTaiProvinceVo;

public class PopProvinceHelper {

    public static PopProvinceVo convertToVo(ZhongTaiProvinceVo vo) {
        if (vo == null) {
            return null;
        }
        PopProvinceVo popProvinceVo = new PopProvinceVo();
        popProvinceVo.setProvId(Long.parseLong(vo.getValueCode()));
        popProvinceVo.setProv(vo.getValueName());
        return popProvinceVo;
    }


}
