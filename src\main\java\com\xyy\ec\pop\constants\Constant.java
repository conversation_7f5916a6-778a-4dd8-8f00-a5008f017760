package com.xyy.ec.pop.constants;

public class Constant {

	public static final String BUSINESS_PREFIX="xyy_pop:";
	public static final String CATEGORY_RELATION="category_relation:";
	public static final long HOUR=60;
	public static final long DAY=24*HOUR;

	public final static byte DEL_FLAG_EXIST = 0;
	public final static byte DEL_FLAG_DELETED = 1;

	/**
	 * 商品来源 商家自建
	 */
	public final static byte SHOP_SELF_BUILD = 1;
	/**
	 * 商品来源 商品库
	 */
	public final static byte GOODS_LIBRARY = 2;
	
	public final static long SMS_SendBySystem = 0;
	
	public final static byte SendState_Sending = 2;
	public final static byte SendState_Success = 1;
	public final static byte SendState_Failed = 0;

	public final static byte SendUserListType_AllUser = 1;
	public final static byte SendUserListType_SingleUser = 2;
	public final static byte SendUserListType_Blacklist = 3;
	public final static byte SendUserListType_Whitelist = 4;
	
	public final static byte SendSmsType_Register = 1;
	public final static byte SendSmsType_FindPasswrod = 2;
	public final static byte SendSmsType_Login = 3;
	public final static byte SendSmsType_CreateAccount = 4;
	public final static byte SendSmsType_AuditPass = 5;
	public final static byte SendSmsType_AuditRefuse = 6;
	public final static byte SendSmsType_Other = 7;
    public static final String DICTIONARY = "dictionary:";
	public static final String CATEGORY_ID ="category_id:" ;

	public static final String RESULT_STATUS = "status";
	public static final String RESULT_ERRORMSG = "errorMsg";
	public static final String RESULT_ERRORCODE = "errorCode";
	public static final String CODE = "code";

	public static final String RESULT_MSG = "msg";
	public static final String RESULT_SUCCESS = "success";
	public static final String RESULT_FAILURE = "failure";
	public static final Integer TWO = 2;
    public static final Byte THREE = 3;
	public static final Integer ONE = 1;

	public static String redisKeyOrgDetail = "ORG_DETAIL_";//商户信息缓存key
	public static final String POP_FLOOR_GOODS = "POP_FLOOR_GOODS_"; //店铺首页楼层key
	public static final String POP_ADV_IMG = "POP_ADV_IMG_"; //店铺首页轮播图key
	public static String redisKeyOrgFirstFloorGoods ="ORG_FIRST_FLOOR_GOODS_";//商户第一个楼层商品信息
	public static String redisKeyOrgQualification = "ORG_QUALIFICATION_";//商户资质
	public static String redisKeyOrgShippingTemplate = "ORG_SHIPPING_TEMPLATE_";//运费模板
	public static String redisKeyOrgClassify = "ORG_CLASSIFY_";//分类

    /** 提现报表导出excel sheet名称 */
    public static final String CAHS_ADVIENCE_INFO_EXPORT = "提现报表";
	/** 商户信息列表导出excel sheet名称 */
	public static final String MERCHANT_INFO_EXPORT = "商户信息列表";

	/** 商家提现信息导出excel sheet名称 */
	public static final String CAHS_ADVIENCE_INFO_MERCHANT_EXPORT = "商家提现信息";
	/** 全国区域编码 */
	public static final String BRANCH_CODE_ALL_OVER ="XS000000";
    public static final String MODIFICATION_APPLICATION_LIST_EXPORT = "企业信息修改申请列表";
}
