/**
 * Copyright (C), 2015-2019,  武汉小药药医药科技有限公司
 * FileName: FileDownLoadUtils
 * Author:   dell
 * Date:     2019/3/20 15:13
 * Description: 文件下载工具
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.xyy.ec.pop.utils;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * 〈一句话功能简述〉<br>
 * 〈文件下载工具〉
 *
 * <AUTHOR>
 * @create 2019/3/20
 * @since 1.0.0
 */
@Slf4j
public class FileDownLoadUtils {

    /**
     * 下载ftp文件
     * @param restTemplate
     * @param url 要下载的文件地址
     */
    public static InputStream download(RestTemplate restTemplate, String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Lists.newArrayList(MediaType.APPLICATION_OCTET_STREAM));
        InputStream inputStream = null;
        try {
            ResponseEntity<byte[]> response = restTemplate.exchange(
                    UriComponentsBuilder.fromHttpUrl(url).build().encode().toUri(),
                    HttpMethod.GET,
                    new HttpEntity<byte[]>(headers),
                    byte[].class);
            byte[] result = response.getBody();
            inputStream = new ByteArrayInputStream(result);
        }
        catch (Exception e) {
            log.error("文件下载失败 文件地址:"+url, e);
            return null;
        }
        return inputStream;
    }

}