package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.api.admin.BondAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.BondDto;
import com.xyy.ec.pop.server.api.merchant.dto.BusinessScopeDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @version v1
 * @Description  商户保证金
 * <AUTHOR>
 */
@Component
@Slf4j
public class BondRemoteAdapter {
    @Reference
    private BondAdminApi bondAdminApi;
    public BondDto queryByOrgId(String orgId) {
        try {
            log.info("bondAdminApi.queryByOrgId(orgId:{})",orgId);
            ApiRPCResult<BondDto> result = bondAdminApi.queryByOrgId(orgId);
            log.info("bondAdminApi.queryByOrgId(orgId:{}) return {}",orgId, JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("bondAdminApi.queryByOrgId(orgId:{}) 异常",orgId, e);
            return null;
        }
    }

    public void saveBond(BondDto bondDto, String username) throws ServiceException {
        ApiRPCResult<Boolean> result = null;
        try {
            log.info("bondAdminApi.saveBond(bondDto:{},username:{})",JSON.toJSONString(bondDto),username);
            result = bondAdminApi.saveBond(bondDto,username);
            log.info("bondAdminApi.saveBond(bondDto:{},username:{}) return {}",JSON.toJSONString(bondDto),username, JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("bondAdminApi.saveBond(bondDto:{},username:{}) 异常",JSON.toJSONString(bondDto),username, e);
            throw new ServiceException("保存保证金异常");
        }
        if(result.isFail()){
            throw new ServiceException(result.getErrMsg());
        }
    }
}
