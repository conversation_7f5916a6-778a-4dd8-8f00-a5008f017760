package com.xyy.ec.pop.marketing.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 活动销量数据概要信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarketingActivitySaleDataSummaryInfoVO implements Serializable {

    /**
     * 采购店数
     */
    private Long purchaseMerchantNum;

    /**
     * 采购订单数
     */
    private Long purchaseOrderNum;

    /**
     * 采购数量
     */
    private Long purchaseProductNum;

    /**
     * 采购金额
     */
    private BigDecimal purchaseAmount;

}
