package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.enumcode.ApiResultExceptionTypeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.merchant.api.admin.BusinessCategoryAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.BusinessCategoryDictDto;
import com.xyy.ec.pop.server.api.merchant.dto.BusinessScopeDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @version v1
 * @Description 查询经营类目
 * <AUTHOR>
 */
@Component
@Slf4j
public class BusinessCategoryRemoteAdapter {
    @Reference
    private BusinessCategoryAdminApi businessCategoryAdminApi;

    public List<BusinessScopeDto> getBusinessScope() {
        try {
            log.info("businessScopeService.getBusinessScope() ");
            ApiRPCResult<List<BusinessScopeDto>> result = businessCategoryAdminApi.getBusinessScope();
            log.info("businessScopeService.getBusinessScope() return {}", JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : new ArrayList<>(0);
        } catch (Exception e) {
            log.error("businessScopeService.getBusinessScope() 异常", e);
            return new ArrayList<>(0);
        }
    }

    public List<BusinessCategoryDictDto> getBusinessTree(int corporationType) {
        try {
            log.info("businessScopeService.getBusinessTree() ");
            ApiRPCResult<List<BusinessCategoryDictDto>> result = businessCategoryAdminApi.getBusinessTree(corporationType);
            log.info("businessScopeService.getBusinessTree() return {}", JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : new ArrayList<>(0);
        } catch (Exception e) {
            log.error("businessScopeService.getBusinessTree() 异常", e);
            return new ArrayList<>(0);
        }
    }
}
