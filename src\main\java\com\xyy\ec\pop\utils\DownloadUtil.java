package com.xyy.ec.pop.utils;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * @version v1
 * @Description 下载工具
 */
@Slf4j
public class DownloadUtil {
    public static void downloadExcel(Workbook workbook, HttpServletResponse response, String fileName) {
        if(workbook==null){
            return;
        }
        try(OutputStream out =response.getOutputStream()) {
            response.reset();
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(),"iso-8859-1"));
            workbook.write(out);
        } catch (IOException e) {
            log.error("DownloadUtil.downloadExcel()出错",e);
        }
    }

    public static <T> void downloadExcel(HttpServletResponse response, String fileName, ExportParams params, Class<?> clazz, List<T> list) {
        Workbook workbook = null;
        ServletOutputStream out = null;
        try {
            workbook = ExcelExportUtil.exportBigExcel(params, clazz, list);
            ExcelExportUtil.closeExportBigExcel();
            out = response.getOutputStream();
            response.setHeader("Content-type", "application/x-xls; charset=" + "UTF-8");
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("application/msexcel");
            workbook.write(out);
        } catch (IOException e) {
            log.error("文件下载出错", e);
        } finally {
            if (null != workbook) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error("文件下载出错", e);
                }
            }
            if (null != out) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("文件下载出错", e);
                }
            }
        }
    }
}
