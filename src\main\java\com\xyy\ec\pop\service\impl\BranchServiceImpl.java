package com.xyy.ec.pop.service.impl;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.constants.RedisConstants;
import com.xyy.ec.pop.dto.RegionDto;
import com.xyy.ec.pop.redis.IRedisService;
import com.xyy.ec.pop.remote.EcSystemApiRemote;
import com.xyy.ec.pop.remote.RegionAdapter;
import com.xyy.ec.pop.service.BranchService;
import com.xyy.ec.pop.vo.BranchVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BranchServiceImpl
 * @Package com.xyy.ec.pop.service.impl
 * @Description: 区域业务
 * @date 2020/7/15 14:10
 */
@Slf4j
@Service
public class BranchServiceImpl implements BranchService {

    @Autowired
    private EcSystemApiRemote adminEcSystemApiRemote;
    @Autowired
    protected IRedisService adminRedisService;
    @Autowired
    protected RegionAdapter adminRegionAdapter;

    @Override
    public List<BranchVo> getAllBranchs() {
        //从redis缓存获取
        String branchKey =  RedisConstants.ALL_BRANCH_LIST_KEY;
        String branchList = adminRedisService.getValue(branchKey);
        List<BranchVo> branchVos = Collections.emptyList();
            if(StringUtils.isNotBlank(branchList)){
                branchVos = JSON.parseArray(branchList, BranchVo.class);
              return  branchVos;
            }

        branchVos = adminEcSystemApiRemote.getAllBranchs();
        log.info("#BranchServiceImpl#getAllBranchs,下拉框获取EC的机构信息:"+ JSON.toJSONString(branchVos));
        if (CollectionUtils.isNotEmpty(branchVos)) {
            branchVos.forEach(q->q.setBranchName(q.getBranchName().replace("子公司","")));
            adminRedisService.setValue(branchKey, JSON.toJSONString(branchVos), TimeUnit.DAYS,1);
        }
        return branchVos;
    }

    @Override
    public List<BranchVo> getAllProvinces() {
        //从redis缓存获取
        String provinceKey = RedisConstants.ALL_PROVINCE_LIST_KEY;
        String branchList = adminRedisService.getValue(provinceKey);
        List<BranchVo> branchVos = Collections.emptyList();
        if (StringUtils.isNotBlank(branchList)) {
            branchVos = JSON.parseArray(branchList, BranchVo.class);
            return branchVos;
        }

        List<RegionDto> allProvinces = adminRegionAdapter.getAllProvinces();
        branchVos = convertRegionDtos2BranchVos(allProvinces);
        log.info("#BranchServiceImpl.getAllProvinces,下拉框获取EC的机构信息:" + JSON.toJSONString(branchVos));
        if (CollectionUtils.isNotEmpty(branchVos)) {
            adminRedisService.setValue(provinceKey, JSON.toJSONString(branchVos), TimeUnit.DAYS, 1);
        }
        return branchVos;
    }

    private List<BranchVo> convertRegionDtos2BranchVos(List<RegionDto> regionDtos) {
        if (CollectionUtils.isEmpty(regionDtos)) {
            return Collections.emptyList();
        }
        List<BranchVo> branchVos = regionDtos.stream().map(regionDto -> {
            BranchVo branchVo = new BranchVo();
            branchVo.setBranchCode(String.valueOf(regionDto.getAreaCode()));
            branchVo.setBranchName(regionDto.getAreaName());
            return branchVo;
        }).collect(Collectors.toList());
        return branchVos;
    }


}
