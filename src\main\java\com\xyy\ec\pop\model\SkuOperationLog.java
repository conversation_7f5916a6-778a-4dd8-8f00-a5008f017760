package com.xyy.ec.pop.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel("sku操作日志实体")
public class SkuOperationLog implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("日志id")
    private Integer id;
    @ApiModelProperty("商品id")
    private Integer skuId;
    @ApiModelProperty("操作类型1")
    private Integer type;
    /**
     * 变更类型
     */
    private String typeName;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("操作时间")
    private Date createTime;
    @ApiModelProperty("操作人id")
    private Integer createId;
    @ApiModelProperty("操作人")
    private String createName;

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateId() {
        return createId;
    }

    public void setCreateId(Integer createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName == null ? null : createName.trim();
    }
}