package com.xyy.ec.pop.report.param;

import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.ec.merchant.bussiness.enums.AuditStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class MerchantReportBatchAuditImportParam implements IExcelModel, IExcelDataModel, Serializable {
    @ExcelProperty(value = "药店编码")
    private String merchantIdStr;
    @ExcelProperty(value = "审核结果")
    private String auditResult;
    @ExcelProperty(value = "原因")
    private String resultReason;
    private Long merchantId;
    private Integer auditStatus;
    private String errorMsg;
    private int rowNum;
    /**
     * 表明否已经导入错误了
     */
    public boolean failed;

    @Override
    public int getRowNum() {
        return this.rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String s) {
        this.errorMsg = s;
    }

    public Long getMerchantId() {
        if (StringUtils.isNotEmpty(merchantIdStr)) {
            return Long.valueOf(merchantIdStr);
        }
        return null;
    }

    public Integer getAuditStatus() {
        if (StringUtils.isNotEmpty(auditResult)) {
            return AuditStatusEnum.getCodeByDesc(auditResult);
        }
        return null;
    }
}
