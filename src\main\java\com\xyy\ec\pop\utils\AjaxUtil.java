package com.xyy.ec.pop.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.WebRequest;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * Created by canal.wen on 2014/10/22.
 */
public abstract class AjaxUtil {

    public static boolean isAjaxRequest(WebRequest webRequest) {
        return "XMLHttpRequest".equalsIgnoreCase(webRequest.getHeader("X-Requested-With"));
    }

    public static boolean isAjaxRequest(HttpServletRequest request) {
        return "XMLHttpRequest".equalsIgnoreCase(request.getHeader("X-Requested-With"));
    }

    public static boolean isAjaxUploadRequest(WebRequest webRequest) {
        return StringUtils.isNotBlank(webRequest.getParameter("ajaxUpload"));
    }
}
