package com.xyy.ec.pop.helper;


import com.xyy.ec.pop.server.api.merchant.dto.PopCorporationPriceTypeDto;
import com.xyy.ec.pop.vo.CorporationPriceTypeVo;

public class CorporationSaleTypeHelper {
    public static CorporationPriceTypeVo convertToSaleVo(PopCorporationPriceTypeDto dto, String orgId) {
        CorporationPriceTypeVo corporationSaleTypeVo = new CorporationPriceTypeVo();
        corporationSaleTypeVo.setOrgId(orgId);
        if (dto != null) {
            corporationSaleTypeVo.setPriceType(dto.getPriceType());
        }
        return corporationSaleTypeVo;

    }

    public static PopCorporationPriceTypeDto convertToPriceDto(CorporationPriceTypeVo vo, String username) {
        PopCorporationPriceTypeDto dto = new PopCorporationPriceTypeDto();
        dto.setOrgId(vo.getOrgId());
        dto.setPriceType(vo.getPriceType());
        dto.setCreateName(username);
        dto.setUpdateName(username);
        return dto;

    }
}
