/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.xyy.ec.pop.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.ResultMessage;
import com.xyy.ec.pop.config.XyyConfig;
import com.xyy.ec.pop.constants.Constants;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.remote.UploadRemote;
import com.xyy.ec.pop.utils.FileNameUtils;
import com.xyy.ec.pop.utils.FileUploadUtil;
import com.xyy.ec.pop.utils.cos.CosUploadPopConfig;
import com.xyy.ec.pop.utils.cos.CosUploadPopUtils;
import com.xyy.ec.pop.utils.fastdfs.FDFSClient;
import com.xyy.ec.pop.utils.oss.OssUtil;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.UploadResultVo;
import com.xyy.ec.pop.vo.UploadVo;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import com.xyy.util.UUIDUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 上传图片控制器
 *
 * <AUTHOR>
 */
@Api(tags = "文件上传")
@Controller
@RequestMapping("/uploadFile")
@Slf4j
public class FileUploadController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(FileUploadController.class);

    @Autowired
    private XyyConfig.CdnConfig cdnConfig;
    @Autowired
    private FDFSClient fdfsClient;
    private static final String UPLOAD_ERROR = "文件上传异常";
    private static final String XYY_WEBSERVICE = "xyy-webservice";
    @Autowired
    private XyyConfig xyyConfig;
    @Value("${fastdfs.fileType}")
    private String permissibleFileType;
    @Autowired
    private UploadRemote uploadRemote;
    @Autowired
    private CosUploadPopConfig cosUploadPopConfig;


    /**
     * 上传文件到FTP服务器
     *
     * @param request
     * @param response
     * @param uploadPath
     * @param targetFileName
     * @return
     */
    @ApiOperation(value = "上传文件到FTP服务器", notes = "上传文件到FTP服务器", response = Object.class)
    @ApiImplicitParams({
            @ApiImplicitParam(value = "文件上传路径", name = "uploadPath", dataTypeClass = String.class, required = true, paramType = "form"),
            @ApiImplicitParam(value = "上传目标文件名", name = "targetFileName", dataTypeClass = String.class, required = true, paramType = "form")
    })
    @ResponseBody
    @RequestMapping(value = "/invoke", method = RequestMethod.GET)
    public Object uploadFile(HttpServletRequest request, HttpServletResponse response,
                             @RequestParam("uploadPath") String uploadPath,
                             @RequestParam(value = "targetFileName", required = false) String targetFileName) {
        //上传文件所属目录
        log.error("uploadFile 文件上传异常,请联系重楼！");
        return this.addError("文件上传异常,请联系重楼！");
//        try {
//            String localTempPath = System.getProperty(Constants.XYYWESERVICE);
//            return FileUploadUtil.fileUpload(request, uploadPath, cdnConfig, targetFileName, localTempPath);
//        } catch (IllegalArgumentException e) {
//            LOGGER.error("文件上传异常 !!!");
//            return this.addError(" 文件上传异常 ");
//        }
    }

    /**
     * 上传文件到FTP服务器
     *
     * @param request
     * @param response
     * @param uploadPath
     * @param targetFileName
     * @return
     */
    @ApiOperation(value = "上传商家图片到FTP服务器", notes = "上传商家图片到FTP服务器", response = Object.class)
    @ApiImplicitParams({
            @ApiImplicitParam(value = "文件上传路径", name = "uploadPath", dataTypeClass = String.class, required = true, paramType = "form"),
            @ApiImplicitParam(value = "上传目标文件名", name = "targetFileName", dataTypeClass = String.class, required = true, paramType = "form"),
            @ApiImplicitParam(value = "", name = "flag", dataTypeClass = Integer.class, paramType = "form")
    })
    @ResponseBody
    @RequestMapping(value = "/uploadShopImg", method = RequestMethod.GET)
    public Object uploadShopImg(HttpServletRequest request, HttpServletResponse response,
                                @RequestParam("uploadPath") String uploadPath, String imgType,
                                @RequestParam(value = "targetFileName", required = false) String targetFileName,
                                @RequestParam(value = "flag", required = false) Integer flag) {
        //上传文件所属目录
        log.error("uploadShopImg 文件上传异常,请联系重楼！");
        return this.addError("文件上传异常,请联系重楼！");
//        try {
//            String localTempPath = System.getProperty(Constants.XYYWESERVICE);
//            return FileUploadUtil.imageUpload(imgType, request, uploadPath, cdnConfig, targetFileName, localTempPath);
//        } catch (IllegalArgumentException e) {
//            LOGGER.error(" 文件上传异常: {}", e.getMessage());
//            return this.addError(" 文件上传异常");
//        } catch (IOException e) {
//            LOGGER.error("文件上传异常:{} ", e.getMessage());
//            return this.addError("文件上传异常!");
//        }
    }

    @ApiOperation(value = "上传三方图片到FTP服务器", notes = "上传三方图片到FTP服务器", response = Object.class)
    @ApiImplicitParams({
            @ApiImplicitParam(value = "待上传的文件", name = "file", dataTypeClass = MultipartFile.class, required = true, paramType = "form"),
            @ApiImplicitParam(value = "上传目标实体", name = "vo", dataTypeClass = UploadVo.class, required = true, paramType = "query")
    })
    @RequestMapping(value = "/uploadThirdImg", method = RequestMethod.GET)
    @ResponseBody
    public Object uploadThirdImg(HttpServletRequest request, @RequestParam("file") MultipartFile file, UploadVo vo) {
        //上传文件所属目录
        log.error("uploadThirdImg 文件上传异常,请联系重楼！");
        return this.addError("文件上传异常,请联系重楼！");
//        try {
//            String localTempPath = System.getProperty(Constants.XYYWESERVICE);
//            String uploadPath = "/ybm/product/";
//            Map<String, Object> stringObjectMap = FileUploadUtil.uploadThirdImg(vo.getImgType().toString(), request, uploadPath, cdnConfig, file.getName(), localTempPath);
//            UploadResultVo uploadResultVo = JSON.parseObject(JSON.toJSONString(stringObjectMap), UploadResultVo.class);
//            return ResultMessage.createSuccess(uploadResultVo);
//        } catch (IllegalArgumentException e) {
//            LOGGER.error("文件上传异常: {}", e.getMessage());
//            return this.addError("文件上传异常!!");
//        } catch (IOException e) {
//            LOGGER.error("文件上传异常:{}", e.getMessage());
//            return this.addError("文件上传异常!!!");
//        }
    }

    /**
     * 上传视频到FTP服务器
     *
     * @param request
     * @param response
     * @param uploadPath
     * @param targetFileName
     * @return
     */
    @ApiOperation(value = "上传视频到FTP服务器", notes = "上传视频到FTP服务器", response = Object.class)
    @ApiImplicitParams({
            @ApiImplicitParam(value = "上传路径", name = "uploadPath", dataTypeClass = String.class, required = true, paramType = "form"),
            @ApiImplicitParam(value = "上传目标文件名", name = "targetFileName", dataTypeClass = String.class, required = true, paramType = "form")
    })
    @ResponseBody
    @RequestMapping(value = "/invokeVideo", method = RequestMethod.GET)
    public Object uploadVideo(HttpServletRequest request, HttpServletResponse response,
                              @RequestParam("uploadPath") String uploadPath,
                              @RequestParam(value = "targetFileName", required = false) String targetFileName) {
        //上传文件所属目录
        log.error("uploadVideo 文件上传异常,请联系重楼！");
        return this.addError("文件上传异常,请联系重楼！");
//        try {
//            String localTempPath = System.getProperty(Constants.XYYWESERVICE);
//            return FileUploadUtil.videoUpload(request, uploadPath, cdnConfig, targetFileName, localTempPath);
//        } catch (IllegalArgumentException e) {
//            LOGGER.error("文件上传异常 ");
//            return this.addError(" 文件上传异常");
//        }
    }

    /**
     * 上传文件到FTP服务器
     *
     * @param request
     * @param response
     * @param uploadPath
     * @param targetFileName
     * @return
     */
    @ApiOperation(value = "上传文件到FTP服务器", notes = "上传文件到FTP服务器", response = Object.class)
    @ApiImplicitParams({
            @ApiImplicitParam(value = "上传路径", name = "uploadPath", dataTypeClass = String.class, required = true, paramType = "form"),
            @ApiImplicitParam(value = "上传目标文件名", name = "targetFileName", dataTypeClass = String.class, required = true, paramType = "form")
    })
    @ResponseBody
    @RequestMapping(value = "/invokeFile", method = RequestMethod.GET)
    public Object invokeFile(HttpServletRequest request, HttpServletResponse response,
                             @RequestParam("uploadPath") String uploadPath,
                             @RequestParam(value = "targetFileName", required = false) String targetFileName) {
        //上传文件所属目录
        log.error("invokeFile 文件上传异常,请联系重楼！");
        return this.addError("文件上传异常,请联系重楼！");
//        try {
//            String localTempPath = System.getProperty(Constants.XYYWESERVICE);
//            /*
//             *  获取文件名
//             */
//            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
//            Iterator<String> iter = multiRequest.getFileNames();
//            List<MultipartFile> files = multiRequest.getFiles(iter.next());
//            String myFileName = "";
//            String trueFileName = "";
//            if (!CollectionUtils.isEmpty(files)) {
//                for (MultipartFile file : files) {
//                    myFileName = file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf('.'));
//                    trueFileName = file.getOriginalFilename();
//                }
//            }
//            if (StringUtils.isNotEmpty(myFileName)) {
//                targetFileName = myFileName;
//            }
//            Map<String, Object> objectMap
//                    = FileUploadUtil.videoUpload(request, uploadPath, cdnConfig, targetFileName, localTempPath);
//            String path = cdnConfig.getShowUrl() + uploadPath;
//            /*
//             *  将文件名字更新成全路径返回给前端
//             */
//            if (objectMap.get("fileName") != null) {
//                List<String> fileNameList = Lists.newArrayList();
//                fileNameList.add(path + trueFileName);
//                objectMap.put("fileName", fileNameList);
//            }
//            return objectMap;
//        } catch (IllegalArgumentException e) {
//            LOGGER.error("文件上传异常 ");
//            return this.addError(" 文件上传异常 ");
//        }
    }


    /**
     * @param request
     * @param response
     * @throws IOException
     */
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/excelOut", method = RequestMethod.GET)
    public void excelStandardTemplateOut(HttpServletRequest request,
                                         HttpServletResponse response) throws IOException, ServiceException {
        // 第一步，创建一个webbook，对应一个Excel文件
        HSSFWorkbook wb = new HSSFWorkbook();
        // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet("订单产品导入模板");
        sheet.setColumnWidth(0, 6000);
        sheet.setColumnWidth(1, 5000);
        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);

        HSSFFont font = wb.createFont();
        font.setFontName("黑体");
        font.setFontHeightInPoints((short) 12);// 字体大小
        font.setBold(true);// 加粗
        // 第四步，创建单元格，并设置值表头 设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);// 左右居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 上下居中
        style.setWrapText(true);
        style.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN); // 设置单元格的边框为粗体
        style.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex()); // 设置单元格的边框颜色．
        style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.PALE_BLUE.getIndex());// 设置单元格的背景颜色．

        HSSFCell cell = row.createCell((short) 0);
        cell.setCellValue("时空编码");
        cell.setCellStyle(style);

        cell = row.createCell((short) 1);
        cell.setCellValue("产品数量");
        cell.setCellStyle(style);


        // 设置response参数，可以打开下载页面
        response.reset();
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(("importOrderProduct" + ".xls").getBytes(), "iso-8859-1"));//下载文件的名称
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("excel 导出异常", e);
        } finally {
            wb.close();
        }
        try {
            OutputStream ouputStream = response.getOutputStream();
            wb.write(ouputStream);
            ouputStream.flush();
            ouputStream.close();
        } catch (final IOException e) {
            throw e;
        } finally {
            wb.close();
        }
    }

    /**
     * 上传文件到FTP服务器
     *
     * @param request
     * @param response
     * @param uploadPath
     * @param targetFileName
     * @param fileSize       指定文件大小，用于控制文件大小
     * @return
     */
    @ApiOperation(value = "上传文件到FTP服务器(限制大小)", notes = "上传文件到FTP服务器(限制大小)", response = Object.class)
    @ApiImplicitParams({
            @ApiImplicitParam(value = "上传路径", name = "uploadPath", dataTypeClass = String.class, required = true, paramType = "form"),
            @ApiImplicitParam(value = "文件大小", name = "fileSize", dataTypeClass = Long.class, required = true, paramType = "form"),
            @ApiImplicitParam(value = "上传目标文件名", name = "targetFileName", dataTypeClass = String.class, required = true, paramType = "form")
    })
    @ResponseBody
    @RequestMapping(value = "/invokeMax", method = RequestMethod.GET)
    public Object uploadFileMax(HttpServletRequest request, HttpServletResponse response,
                                @RequestParam("uploadPath") String uploadPath,
                                @RequestParam("fileSize") long fileSize,
                                @RequestParam(value = "targetFileName", required = false) String targetFileName) {
        //上传文件所属目录
        log.error("uploadFileMax 文件上传异常,请联系重楼！");
        return this.addError("文件上传异常,请联系重楼！");
//        try {
//            String localTempPath = System.getProperty("xyy-webservice");
//            return FileUploadUtil.fileUploadMax(request, uploadPath, cdnConfig, targetFileName, localTempPath, fileSize);
//        } catch (IllegalArgumentException e) {
//            LOGGER.error(" 文件上传异常 !");
//            return this.addError(" 文件上传异常 !!");
//        }
    }

    @RequestMapping(value = "/uploadImage", method = RequestMethod.GET)
    @ResponseBody
    public Object uploadShopImg(HttpServletRequest request, @RequestParam("file") MultipartFile file, UploadVo vo) {
        //上传文件所属目录
        log.error("uploadShopImg 文件上传异常,请联系重楼！");
        return this.addError("文件上传异常,请联系重楼！");
//        try {
//            String localTempPath = System.getProperty("xyy-webservice");
//            String uploadPath = "";
//            if (vo.getType() != null && vo.getType() == 1) {
//                uploadPath = "/ybm/product/";
//            } else if (vo.getType() != null && vo.getType() == 2) {
//                uploadPath = "/ybm/product/desc/";
//            } else if (vo.getType() != null && vo.getType() == 3) {
//                uploadPath = "/ybm/product/pic/";
//            }
//            Map<String, Object> stringObjectMap = FileUploadUtil.imageUpload(vo.getImgType().toString(), request, uploadPath, cdnConfig, file.getName(), localTempPath);
//            UploadResultVo uploadResultVo = JSON.parseObject(JSON.toJSONString(stringObjectMap), UploadResultVo.class);
//            return ResultMessage.createSuccess(uploadResultVo);
//        } catch (IllegalArgumentException e) {
//            LOGGER.error("文件上传异常:{} ", e.getMessage());
//            return this.addError("文件上传异常");
//        } catch (IOException e) {
//            LOGGER.error("文件上传异常: {}", e.getMessage());
//            return this.addError("文件上传异常");
//        }
    }

    @ResponseBody
    @PostMapping(value = "/uploadFDFS")
    public Object uploadFDFS(@RequestParam("file") MultipartFile file, String targetFileName) {
        //上传文件所属目录
        try {
            String fileType = FileNameUtils.getFileType(file.getOriginalFilename());
            validFileType(fileType, file.getOriginalFilename());
            String fileName = String.format("%s-%s%s", targetFileName, UUIDUtil.newUUID(), file.getOriginalFilename());
            String path;
            if (Objects.equals(cosUploadPopConfig.getSwitchFlag(), true)) {
                //将文件上传fastdfs
                path = fdfsClient.upload(file.getInputStream(), fileName);
            } else {
                path = CosUploadPopUtils.uploadAndGetRelativePath(file.getInputStream(), fileName);
            }
            return ResponseUtils.returnObjectSuccess(path);
        } catch (XyyEcPopException e) {
            log.warn("上传文件失败：原始文件名：{}，targetFileName:{},errorMessage:{}", file.getOriginalFilename(), targetFileName, e.getMessage());
            return ResponseUtils.returnException(e);
        } catch (IllegalArgumentException e) {
            log.warn("上传文件失败：原始文件名：{}，targetFileName:{},errorMessage:{}", file.getOriginalFilename(), targetFileName, e.getMessage());
            return ResponseUtils.returnException(e);
        } catch (IOException e) {
            log.warn("上传文件失败：原始文件名：{}，targetFileName:{},errorMessage:{}", file.getOriginalFilename(), targetFileName, e.getMessage());
            return ResponseUtils.returnException(e);
        }
    }

    @ResponseBody
    @PostMapping(value = "/uploadFDFSForVideo")
    public Object uploadFDFSForVideo(@RequestParam("file") MultipartFile file, String targetFileName,Integer uploadType) {
        //上传文件所属目录
        try {
            String fileType = FileNameUtils.getFileType(file.getOriginalFilename());
            validFileTypeForVideo(fileType, file,uploadType);
            String fileName = String.format("%s-%s%s", targetFileName, UUIDUtil.newUUID(), file.getOriginalFilename());
            String path;
            if (Objects.equals(cosUploadPopConfig.getSwitchFlag(), true)) {
                //将文件上传fastdfs
                path = fdfsClient.upload(file.getInputStream(), fileName);
            } else {
                path = CosUploadPopUtils.uploadAndGetRelativePath(file.getInputStream(), fileName);
            }
            return ResponseUtils.returnObjectSuccess(path);
        } catch (XyyEcPopException e) {
            log.warn("上传文件失败：原始文件名：{}，targetFileName:{},errorMessage:{}", file.getOriginalFilename(), targetFileName, e.getMessage());
            return ResponseUtils.returnException(e);
        } catch (IllegalArgumentException e) {
            log.warn("上传文件失败：原始文件名：{}，targetFileName:{},errorMessage:{}", file.getOriginalFilename(), targetFileName, e.getMessage());
            return ResponseUtils.returnException(e);
        } catch (IOException e) {
            log.warn("上传文件失败：原始文件名：{}，targetFileName:{},errorMessage:{}", file.getOriginalFilename(), targetFileName, e.getMessage());
            return ResponseUtils.returnException(e);
        }
    }



    private void validFileType(String fileType, String originalFilename) {
        if (StringUtils.isEmpty(permissibleFileType)) {
            return;
        }
        if (StringUtils.isEmpty(fileType)) {
            log.error("fastdfs上传文件，错误的文件类型,原始文件名：{}", originalFilename);
            throw new IllegalArgumentException("文件类型不能为空");
        }
        String[] types = permissibleFileType.split(",");
        for (String type : types) {
            if (fileType.equalsIgnoreCase(type)) {
                return;
            }
        }
        log.error("fastdfs上传文件，错误的文件类型,原始文件名：{}", originalFilename);
        throw new IllegalArgumentException("错误的文件类型");
    }

    private void validFileTypeForVideo(String fileType, MultipartFile file,Integer uploadType) {
        String typeList;
        if (uploadType == 1){
            typeList = "mp4";
            if (file.getSize() > 1024*1024*500){
                throw new XyyEcPopException("视频大小不能超过500M");
            }
        } else {
            typeList = "jpg,png,jpeg,pdf,gif,ppt,pptx";
            if (file.getSize() > 1024*1024*100){
                throw new XyyEcPopException("文档大小不能超过100M");
            }
        }

        if (StringUtils.isEmpty(fileType)) {
            log.error("fastdfs上传文件，错误的文件类型,原始文件名：{}", file.getOriginalFilename());
            throw new IllegalArgumentException("文件类型不能为空");
        }



        String[] types = typeList.split(",");
        for (String type : types) {
            if (fileType.equalsIgnoreCase(type)) {
                return;
            }
        }
        log.error("fastdfs上传文件，错误的文件类型,原始文件名：{}", file.getOriginalFilename());
        throw new IllegalArgumentException("错误的文件类型");
    }

    @GetMapping(value = "/downloadFile")
    @ResponseBody
    public ResponseVo downloadFile(HttpServletRequest request, HttpServletResponse response, String targetFileName) {
        //上传文件所属目录
        try {
            if (StringUtils.isEmpty(targetFileName)) {
                return ResponseVo.errRest("文件路径为空");
            }
            String showUrl = xyyConfig.getCdnConfig().getShowUrl();
            String replace = targetFileName.replace(showUrl, "");
            String groupName = replace.substring(1, 3);
            String dfsFilePath = replace.substring(4, replace.length());
            String fileName = dfsFilePath.substring(dfsFilePath.lastIndexOf("/") + 1, dfsFilePath.length());
            response.setContentType("application/x-download;charset=utf-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            //http://localhost:8091/uploadFile/downloadFile?targetFileName=G1/M00/0C/BB/Cgoz0135kQyACR8JAADI4xhYZSA704.jpg
            log.info("#uploadFile/downloadFile#groupName:{},dfsFilePath:{}", groupName, dfsFilePath);
            byte[] bytes = fdfsClient.downInputStream(groupName, dfsFilePath);

            ServletOutputStream outputStream = response.getOutputStream();
            IOUtils.write(bytes, outputStream);
        } catch (XyyEcPopException | IllegalArgumentException | IOException e) {
            log.error("文件下载失败", e);
            return ResponseVo.errRest("下载文件异常");
        }
        return ResponseVo.successResultNotData("下载成功");
    }

    /**
     * 获取上传主机域名
     *
     * @return
     * @date 2018年10月22日 下午3:50:39
     */
    @ApiOperation("获取上传主机域名")
    @ResponseBody
    @PostMapping(value = "/cdn/hostName")
    public Map<String, Object> cdnHostName() {
        Map<String, Object> r = new HashMap<>(1);
        r.put("hostName", cdnConfig.getShowUrl());
        return r;
    }

    /**
     * 获取上传cos主机域名
     *
     * @return
     * @date 2022年04月28日11:37:54
     */
    @ApiOperation("获取上传主机域名")
    @ResponseBody
    @PostMapping(value = "/cdn/cosName")
    public Map<String, Object> cdnCosName() {
        Map<String, Object> r = new HashMap<>(1);
        r.put("hostName", cdnConfig.getCosUrl());
        return r;
    }


    @GetMapping(value = "/downloadFromFastDfs")
    public void downloadFromFastDfs(HttpServletResponse response, String path, String fileName) {
        try {
            log.info("FileUploadController.downloadFromFastDfs#path:{},fileName:{}", path,fileName);
            if (StringUtils.isEmpty(path) || StringUtils.isEmpty(fileName)) {
                return;
            }

            if (Objects.equals(cosUploadPopConfig.getSwitchFlag(), true)) {
                fdfsClient.download(response, path, fileName);
            } else {
                try (OutputStream out = response.getOutputStream()) {
                    response.reset();
                    response.setContentType("application/octet-stream");
                    response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), "iso-8859-1"));
                    out.write(CosUploadPopUtils.downloadFile(path));
                } catch (IOException e) {
                    log.error("FileUploadController.downloadFromFastDfs#IO异常. path:{},fileName:{}", path, fileName, e);
                }
            }
            log.info("FileUploadController.downloadFromFastDfs#path:{},fileName:{} 文件正常返回",  path, fileName);
        } catch (Exception e) {
            log.error("FileUploadController.downloadFromFastDfs#path:{},fileName:{} 异常", path,fileName, e);
        }
    }

    /**
     * 从文件服务器下载文件
     *
     * @param response
     * @param path     文件路径
     * @param fileName 文件名
     */
    @GetMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response,
                                 @RequestParam(defaultValue = "/pop/temp") String path,
                                 String fileName) {
        if(StringUtils.isEmpty(fileName)){
            log.info("FileUploadController.downloadTemplate 下载文件名为空");
            return;
        }
        try(OutputStream out =response.getOutputStream()) {
            log.info("FileUploadController.downloadTemplate#path:{},fileName:{}",  path, fileName);
            response.reset();
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(),"iso-8859-1"));

            OssUtil.downloadFile(FileNameUtils.margeName(path, "/", fileName),out);
            log.info("FileUploadController.downloadTemplate#path:{},fileName:{} return 文件成功",  path, fileName);
        } catch (Exception e) {
            log.error("FileUploadController.downloadTemplate#path:{},fileName:{} 异常",  path, fileName, e);
        }
    }
}
