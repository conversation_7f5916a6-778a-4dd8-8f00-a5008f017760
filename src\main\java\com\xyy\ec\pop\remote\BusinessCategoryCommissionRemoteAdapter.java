package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.api.admin.BusinessCategoryCommissionApi;
import com.xyy.ec.pop.server.api.merchant.dto.BusinessCategoryCommissionDto;
import com.xyy.ec.pop.server.api.merchant.dto.BusinessCategoryDictDto;
import com.xyy.ec.pop.server.api.merchant.dto.BusinessScopeDto;
import com.xyy.ec.pop.server.api.merchant.dto.PopCommissionConfigDto;
import com.xyy.ec.pop.vo.BusinessCategoryCommissionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @version v1
 * @Description 企业佣金比例
 * <AUTHOR>
 */
@Component
@Slf4j
public class BusinessCategoryCommissionRemoteAdapter {
    @Reference
    private BusinessCategoryCommissionApi commissionApi;
    public List<BusinessCategoryCommissionDto> getCommissions(String orgId) throws ServiceException {
        ApiRPCResult<List<BusinessCategoryCommissionDto>> result;
        try {
            log.info("commissionApi.getCommissions(orgId:{})",orgId);
            result = commissionApi.getCommissions(orgId);
            log.info("commissionApi.getCommissions(orgId:{}) return {}",orgId, JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("commissionApi.getCommissions(orgId:{}) 异常",orgId,  e);
            throw new ServiceException("查询佣金比例异常");
        }
        if(result.isSuccess()){
            return result.getData();
        }
        throw new ServiceException(result.getErrMsg());
    }

    public void updateCommissions(List<BusinessCategoryCommissionDto> commissions, String orgId, String username) throws ServiceException {
        ApiRPCResult result;
        try {
            log.info("commissionApi.updateCommissions(orgId:{},username:{},commissions:{})",orgId,username,JSON.toJSONString(commissions));
            result = commissionApi.updateCommissions(commissions,orgId,username);
            log.info("commissionApi.updateCommissions(orgId:{},username:{},commissions:{}) return {}",orgId,username,JSON.toJSONString(commissions), JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("commissionApi.updateCommissions(orgId:{},username:{},commissions:{}) 异常",orgId,username,JSON.toJSONString(commissions),  e);
            throw new ServiceException("更新佣金比例异常");
        }
        if(result.isFail()){
            throw new ServiceException(result.getErrMsg());
        }
    }

    public List<BusinessCategoryCommissionDto> getCommissionsByorgIds(List<String> orgIds) throws ServiceException {
        ApiRPCResult<List<BusinessCategoryCommissionDto>> result;
        try {
            log.info("commissionApi.getCommissionsByorgIds(orgIds:{})",orgIds);
            result = commissionApi.getCommissionsByorgIds(orgIds);
            log.info("commissionApi.getCommissionsByorgIds(orgIds:{}) return {}",orgIds, JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("commissionApi.getCommissionsByorgIds(orgIds:{}) 异常",orgIds,  e);
            throw new ServiceException("查询佣金比例异常");
        }
        if(result.isSuccess()){
            return result.getData();
        }
        throw new ServiceException(result.getErrMsg());
    }

    public PopCommissionConfigDto getPopCommissionConfig() {
        try {
            log.info("businessScopeService.getBusinessTree() ");
            ApiRPCResult<PopCommissionConfigDto> apiRPCResult = commissionApi.getPopCommissionConfig();
            log.info("businessScopeService.getBusinessTree() return {}", JSON.toJSONString(apiRPCResult));
            return apiRPCResult.isSuccess() ? apiRPCResult.getData() : null;
        } catch (Exception e) {
            log.error("businessScopeService.getBusinessTree() 异常", e);
            return null;
        }
    }
}
