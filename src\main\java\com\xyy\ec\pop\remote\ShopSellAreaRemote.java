package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.shop.server.business.api.ShopSellAreaAdminApi;
import com.xyy.ec.shop.server.business.results.ShopSellAreaDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 店铺销售区域
 */
@Service
@Slf4j
public class ShopSellAreaRemote {
    @Reference
    private ShopSellAreaAdminApi shopSellAreaAdminApi;

    public List<ShopSellAreaDTO> queryShopSellAreaByShopCode(String shopCode) {
        try {
            log.info("ShopSellAreaRemote.queryShopSellAreaByShopCode#shopCode:{}", shopCode);
            ApiRPCResult<List<ShopSellAreaDTO>> result = shopSellAreaAdminApi.queryShopSellAreaByShopCode(shopCode);
            log.info("ShopSellAreaRemote.queryShopSellAreaByShopCode#shopCode:{} return {}", shopCode, JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("ShopSellAreaRemote.queryShopSellAreaByShopCode#shopCode:{} 异常", shopCode, e);
        }
        throw new ServiceRuntimeException("查询店铺销售区域失败");
    }

}
