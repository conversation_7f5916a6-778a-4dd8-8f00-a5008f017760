package com.xyy.ec.pop.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/12/20
 */
@Component
@ConfigurationProperties(value = "product.commission.update.batch")
@Data
public class ProductCommissionUpdateConfig {
    private int maxFileSize = 1;
    private int maxRows = 1000;
    private String commissionPattern = "[1-9][0-9]?";
    List<String> updateTitles = Arrays.asList("CSUID","佣金比例");

}
