package com.xyy.ec.pop.utils;

import com.alibaba.fastjson.JSONObject;

public class ResultData {


    public static String base(Integer code, String msg) {
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("code", code);
        jsonObject.put("msg", msg);
        return jsonObject.toString();
    }

    public static String failure() {
        return base(Data.FAILURE.code, Data.FAILURE.msg);
    }

    public static String addError(String msg) {
        return base(Data.FAILURE.code, msg);
    }

    public static String success() {
        return base(Data.SUCCESS.code, Data.SUCCESS.msg);
    }

    enum Data {

        SUCCESS(1, "操作成功"),
        FAILURE(0, "操作失败");

        Data(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        private Integer code;
        private String msg;

    }

}
