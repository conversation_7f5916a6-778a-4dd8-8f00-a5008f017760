package com.xyy.ec.pop.report.Vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

@Data
public class PopCashAdvanceBatchConfirmErrorVo implements Serializable {

    private static final long serialVersionUID = 697342984306016010L;

    @Excel(name = "提现单号（必填项）", width = 30)
    private String cashAdvanceNum;

    @Excel(name = "原因", width = 30)
    private String errorMsg;
}
