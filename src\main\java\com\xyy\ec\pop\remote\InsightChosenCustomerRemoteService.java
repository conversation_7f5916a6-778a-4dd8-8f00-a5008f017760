package com.xyy.ec.pop.remote;

import com.xyy.ec.marketing.insight.results.MarketCustomerGroupBaseInfoDTO;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupDTO;

import java.util.List;

/**
 * 选人服务RPC
 *
 * <AUTHOR>
 */
public interface InsightChosenCustomerRemoteService {

    /**
     * 批量查询人群基本信息
     *
     * @param customerGroupIds
     * @return
     */
    List<MarketCustomerGroupBaseInfoDTO> mgetChoseCustomerBaseInfo(List<Long> customerGroupIds);

    /**
     * 批量查询人群信息
     * @param customerGroupIds
     * @return
     */
    List<MarketCustomerGroupDTO> mgetChoseCustomer(List<Long> customerGroupIds);
}
