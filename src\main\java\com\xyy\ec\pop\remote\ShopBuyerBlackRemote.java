package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.shop.server.business.api.ShopBuyerBlackAdminApi;
import com.xyy.ec.shop.server.business.results.ShopBuyerBlackDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 店铺黑名单
 * <AUTHOR>
 * @Date 2022/3/22
 */
@Service
@Slf4j
public class ShopBuyerBlackRemote {
    @Reference
    private ShopBuyerBlackAdminApi shopBuyerBlackAdminApi;

    public List<ShopBuyerBlackDTO> queryByShopCode(String shopCode) {
        try {
            log.info("ShopBuyerBlackRemote.queryByShopCode#shopCode:{}", shopCode);
            ApiRPCResult<List<ShopBuyerBlackDTO>> result = shopBuyerBlackAdminApi.queryByShopCode(shopCode);
            log.info("ShopBuyerBlackRemote.queryByShopCode#shopCode:{} return {}", shopCode, JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("ShopBuyerBlackRemote.queryByShopCode#shopCode:{} 异常", shopCode, e);
        }
        throw new ServiceRuntimeException("查询店铺黑名单失败");
    }

}
