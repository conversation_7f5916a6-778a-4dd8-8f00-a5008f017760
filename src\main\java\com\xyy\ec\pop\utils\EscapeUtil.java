package com.xyy.ec.pop.utils;

/**
 * <AUTHOR>
public class EscapeUtil {

    /**
     * 对js进行处理 避免XSS攻击
     *
     * @param text
     * @return String
     */
    public static String escapeJavaScript(String text) {

        if (StringUtil.isEmpty(text)) {
            return text;
        }

        //对数据进行处理 避免XSS攻击
        text = text.replace("[\\s%09]*(j|[0]*106[\\s%09]*;|0x[0]*6a[\\s%09]*;|J|[0]*74[\\s%09]*;|0x[0]*4a[\\s%09]*;)[\\s%09]*(a|[0]*97[\\s%09]*;|0x[0]*61[\\s%09]*;|A|[0]*65[\\s%09]*;|0x[0]*41[\\s%09]*;)[\\s%09]*(v|[0]*118[\\s%09]*;|0x[0]*76[\\s%09]*;|V|[0]*86[\\s%09]*;|0x[0]*56[\\s%09]*;)[\\s%09]*(a|[0]*97[\\s%09]*;|0x[0]*61[\\s%09]*;|A|[0]*65[\\s%09]*;|0x[0]*41[\\s%09]*;)[\\s%09]*(s|[0]*115[\\s%09]*;|0x[0]*73[\\s%09]*;|S|[0]*83[\\s%09]*;|0x[0]*53[\\s%09]*;)[\\s%09]*(c|[0]*99[\\s%09]*;|0x[0]*63[\\s%09]*;|C|[0]*67[\\s%09]*;|0x[0]*43[\\s%09]*;)[\\s%09]*(r|[0]*114[\\s%09]*;|0x[0]*72[\\s%09]*;|R|[0]*82[\\s%09]*;|0x[0]*52[\\s%09]*;)[\\s%09]*(i|[0]*105[\\s%09]*;|0x[0]*69[\\s%09]*;|I|[0]*73[\\s%09]*;|0x[0]*49[\\s%09]*;)[\\s%09]*(p|[0]*112[\\s%09]*;|0x[0]*70[\\s%09]*;|P|[0]*80[\\s%09]*;|0x[0]*50[\\s%09]*;)[\\s%09]*(t|[0]*116[\\s%09]*;|0x[0]*74[\\s%09]*;|T|[0]*84[\\s%09]*;|0x[0]*54[\\s%09]*;)[\\s%09]*(\\:|%3A|[0]*58[\\s%09]*;|0x[0]*3a[\\s%09]*;)[\\s%09]*", "")
                .replace("<(s|S)(c|C)(r|R)(i|I)(p|P)(t|T)>", "&lt;script&gt;")
                .replace("</(s|S)(c|C)(r|R)(i|I)(p|P)(t|T)>", "&lt;/script&gt;")
                .replace("<", "&lt;")
                .replace(">", "&gt;");
//                .replaceAll("\"", "&quot;");

        return text;
    }

}
