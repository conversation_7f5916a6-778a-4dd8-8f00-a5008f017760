package com.xyy.ec.pop.vo.corporation;

import lombok.Data;

import java.io.Serializable;

@Data
public class ErpApprovalModelVo implements Serializable {
    private int id;
    /**
     * 单据号
     */
    private String number;
    /**
     * 流程实例ID
     */
    private String processInstanceId;
    /**
     * 业务模块主键
     */
    private String businessKey;
    /**
     * 发起人ID
     */
    private String initiatorId;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 处理意见
     */
    private String handStatus;
    /**
     * 节点ID
     */
    private String nodeId;
    /**
     * 当前节点任务ID
     */
    private String taskId;
}
