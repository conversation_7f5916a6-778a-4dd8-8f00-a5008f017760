package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/10/7 9:57
 */
public enum ReceiptVoucherStatusEnum {
    DISPLAY(1,"可显示"),
    NODISPLAY(2,"不可显示");

    private  int id;
    private  String value;

    ReceiptVoucherStatusEnum(int id, String value){
        this.id = id;
        this.value = value;
    }
    private static Map<Integer, ReceiptVoucherStatusEnum> receiptVoucherStatusMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(ReceiptVoucherStatusEnum control : ReceiptVoucherStatusEnum.values()) {
            receiptVoucherStatusMaps.put(control.getId(), control);
            maps.put(control.getId(),control.getValue());
        }
    }
    public static String get(int id) {
        return receiptVoucherStatusMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
