package com.xyy.ec.pop.marketing.vo;

import lombok.Data;

import java.util.List;

@Data
public class GroupBuyingSaleScopeVO {

    /**
     * 是否复制销售范围
     */
    private Integer isCopySaleArea;
    /**
     * 人群id
     */
    private Long customerGroupId;

    /**供货信息配置方式 1:人群 2:商圈、供货对象、黑白名单**/
    private Integer scopeType;
    /**是否复制原品商圈 1:是 2:否**/
    private Integer isCopyBusArea;
    /**商圈ID**/
    private Long busAreaId;
    /**
     * 商圈名称
     */
    private String busAreaName;

    /**是否复制原品供货对象 1:是 2:否**/
    private Integer isCopyControlUser;
    /**供货对象内容(控销用户类型)**/
    private String controlUserTypes;
    /**供货对象内容(控销用户类型)列表**/
    private List<String> controlUserTypeList;
    /**是否复制原品黑白名单 1:是 2:否**/
    private Integer isCopyControlRoster;
    /**黑白名单类型 0:无 1:黑名单 2:白名单**/
    private Integer controlRosterType;
    /**黑白名单ID**/
    private Long controlGroupId;

    /**黑白名单名称**/
    private String controlGroupName;
}
