package com.xyy.ec.pop.vo.corporation;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CorporationBusinessVo implements Serializable {
    private Long id;

    /**
     * 企业id
     */
    private Long cId;

    /**
     * 主营业务ID（关联'tb_category.id'）
     */
    private Long categoryId;

    /**
     * 业务父级ID
     */
    private Long pId;

    /**
     * 业务等级
     */
    private Integer level;

    /**
     * 业务名称
     */
    private String name;

    /**
     * 机构名称
     */
    private String companyName;

    /**
     * 业务简介
     */
    private String brief;

    /**
     * 账户状态
     */
    private Byte state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人姓名
     */
    private String createName;
}
