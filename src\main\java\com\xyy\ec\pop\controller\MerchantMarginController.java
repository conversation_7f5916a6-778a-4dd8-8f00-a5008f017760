package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.util.PoitlIOUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.XyyPopBaseConfig;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.model.*;
import com.xyy.ec.pop.server.api.export.param.CheckPaymentProveParam;
import com.xyy.ec.pop.server.api.merchant.api.admin.CheckCorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.CheckPaymentProveApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.OperateMarginAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.CheckPaymentProveDto;
import com.xyy.ec.pop.server.api.merchant.dto.CheckPaymentProveModelDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.merchant.dto.OperateMarginLogDto;
import com.xyy.ec.pop.server.api.seller.api.PaymentProveApi;
import com.xyy.ec.pop.server.api.seller.dto.PopPaymentProveDto;
import com.xyy.ec.pop.service.*;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.utils.NumberUtils;
import com.xyy.ec.pop.utils.ResultData;
import com.xyy.ec.pop.utils.WordDownloadUtils;
import com.xyy.ec.pop.vo.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;

/**
 * 商户保证金确认控制器
 */
@Slf4j
@Controller
@Api(tags = "商户保证金确认控制类")
public class MerchantMarginController extends BaseController {
    /**
     * 待付款和付款未通过
     */
    private static final List<Byte> checkList = Lists.newArrayList();

    static {
        checkList.add(new Byte("3"));
        checkList.add(new Byte("4"));
    }

    @Reference
    private OperateMarginAdminApi operateMarginAdminApi;
    @Reference
    private CorporationAdminApi corporationAdminApi;
    @Reference
    private CheckCorporationAdminApi checkCorporationAdminApi;
    @Autowired
    private XyyPopBaseConfig popBaseConfig;
    @Autowired
    private OperateMarginService operateMarginService;

    @Autowired
    private MerchantMarginService merchantMarginService;

    @Reference
    private PaymentProveApi paymentProveApi;

    @Reference
    private CheckPaymentProveApi checkPaymentProveApi;

    @Value("${sso.clientHostUrl}")
    private String hostUrl;
    @Value("${xyy.company.name}")
    private String xyyCompanyName;

    /**
     * 商户保证金确认列表页
     */
    @GetMapping(value = "/merchant/margin/acknowledge/index")
    @ApiOperation("商户保证金确认列表页")
    public String merchantMarginPage(Model model) {

        model.addAttribute("smallImgUrlPrefix", popBaseConfig.getSmallMarginImgUrl());
        model.addAttribute("bigImgUrlPrefix", popBaseConfig.getBigMarginImgUrl());

        return "merchantmargin/index";
    }


    /**
     * 加载商户保证金确认审核数据
     */
    @PostMapping(value = "/merchant/margin/acknowledge/listV2")
    @ResponseBody
    public String queryCertificateCheckData(CheckPaymentProveParam param) {
        try {
            List<Long> provIds = getProvIds(param.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return JSON.toJSONString(new PageInfo<>());
            }
            param.setProvIds(provIds);
            log.info("MerchantMarginController.queryCertificateCheckData # param:{} ", JSON.toJSONString(param));
            param.setStartTime(DateUtil.modifyStartTime(param.getStartTime()));
            param.setEndTime(DateUtil.modifyEndTime(param.getEndTime()));

            return JSON.toJSONString(merchantMarginService.selectPageList(param));
        } catch (Exception e) {
            log.error("MerchantMarginController.queryCertificateCheckData # param:{} ", JSON.toJSONString(param), e);
            return JSON.toJSONString(new PageInfo<>());
        }
    }

    /**
     * 保证金确认填写页面
     */
    @GetMapping(value = "/merchant/margin/acknowledge")
    @ApiOperation("保证金确认填写页面")
    public String productCheckPage(@ApiParam(name = "checkExt", value = "商户保证金扩展实体") CheckPaymentProveDto checkExt, @ApiIgnore Model model) {

        model.addAttribute("id", checkExt.getId());
        model.addAttribute("money", checkExt.getMoney());
        return "merchantmargin/acknowledge";
    }

    /**
     * 证书审核记录
     */
    @GetMapping(value = "/merchant/margin/acknowledge/log")
    @ApiOperation("证书审核记录")
    public String checkAcknowledgeLog(@ApiParam(name = "operateMarginLog", value = "商户保证金日志实体") OperateMarginLogDto operateMarginLog, @ApiIgnore Model model) {

        if (operateMarginLog != null ||operateMarginLog.getMerchantMarginId() != null ){
            ApiRPCResult<List<OperateMarginLogDto>> listApiRPCResult = operateMarginAdminApi.selectByMerchantMarginId(operateMarginLog.getMerchantMarginId());
            model.addAttribute("operateList", listApiRPCResult.getData());
        }

        return "merchantmargin/acknowledgeLog";
    }

    /**
     * 保存审核结果
     */
    @PostMapping(value = "/merchant/margin/acknowledge/result")
    @ResponseBody
    @ApiOperation("保存审核结果")
    public String saveCheckResult(@ApiParam(name = "log", value = "商户保证金日志实体") OperateMarginLogDto log, @ApiParam(name = "payDate", value = "支付时间") Date payDate) {
        try {
            LOGGER.info("MerchantMarginController.saveCheckResult(log:{},payDate:{})", JSON.toJSONString(log), JSON.toJSONString(payDate));
            // 查看企业资质审核总状态
            ApiRPCResult<CheckPaymentProveModelDto> apiRPCResult = checkPaymentProveApi.selectById(log.getMerchantMarginId());
            if (apiRPCResult.isFail() || apiRPCResult.getData() == null){
                return ResultData.failure();
            }

            SysUser sysUser = getUser();
            operateMarginService.saveCheckResult(log, apiRPCResult.getData().getCId(), payDate, sysUser);

        }catch (ServiceException e){
            LOGGER.info("MerchantMarginController.saveCheckResult(log:{},payDate:{}) 异常", JSON.toJSONString(log), JSON.toJSONString(payDate), e);
            return ResultData.addError(e.getMessage());
        }catch (Exception e) {
            LOGGER.info("MerchantMarginController.saveCheckResult(log:{},payDate:{}) 异常", JSON.toJSONString(log), JSON.toJSONString(payDate), e);
            return ResultData.failure();
        }
        return ResultData.success();
    }


    @PostMapping("/merchant/saveReceiptUrl")
    @ResponseBody
    public ResponseVo saveReceiptUrl(Long id,Long cId, String receiptUrl) {
        try {
            SysUser user = getUser();
            checkPaymentProveApi.updataReceiptUrl(id,receiptUrl);
            PopPaymentProveDto prove = new PopPaymentProveDto();
            prove.setCId(cId);
            prove.setReceiptUrl(receiptUrl);
            prove.setUpdateTime(new Date());
            prove.setUpdateName(user.getRealName());
            paymentProveApi.updateByCid(prove);
            return ResponseVo.successResultNotData("保存成功");
        } catch (Exception e) {
            LOGGER.error("出现异常:", e);
        }
        return ResponseVo.errRest("保存失败");
    }

    @GetMapping(value = "/merchant/margin/toUpdateMoneyAndUrl")
    @ApiOperation("商户保证金确认列表页")
    public String toUpdateMoneyAndUrl(Model model, @RequestParam("id")Long id,@RequestParam("companyName")String companyName) {
        model.addAttribute("id", id);
        model.addAttribute("companyName", companyName);

        return "merchantmargin/updateMoneyAndUrl";
    }



    @PostMapping("/merchant/updateUrlAndMoney")
    @ResponseBody
    public ResponseVo updateUrlAndMoney(Long id, String url,BigDecimal money){
        try {
            if (Objects.isNull(id)){
                return ResponseVo.errRest("id不能为空");
            }
            if (StringUtils.isEmpty(url) || Objects.isNull(money)){
                return ResponseVo.errRest("收款图片和收款金额不能为空");
            }
            ApiRPCResult<CheckPaymentProveModelDto> apiRPCResult = checkPaymentProveApi.selectById(id);
            if (apiRPCResult == null || apiRPCResult.isFail() || apiRPCResult.getData() == null){
                return ResponseVo.errRest("收款记录能为空");
            }
            ApiRPCResult<Boolean> rpcResult = checkPaymentProveApi.updatePaymentInfo(id,money,url);
            if(!rpcResult.isSuccess()||!rpcResult.getData()){
                return ResponseVo.errRest("保存失败");
            }
            PopPaymentProveDto popPaymentProveDto = new PopPaymentProveDto();
            popPaymentProveDto.setCId(apiRPCResult.getData().getCId());
            popPaymentProveDto.setMoney(money);
            popPaymentProveDto.setUrl(url);
            paymentProveApi.updateByCid(popPaymentProveDto);
            return ResponseVo.successResultNotData("保存成功");
        } catch (Exception e) {
            LOGGER.error("出现异常:", e);
        }
        return ResponseVo.errRest("保存失败");
    }

    /**
     * 下载凭证
     */
    @GetMapping("/merchant/margin/downloadCertificate")
    public void downloadCertificate(@RequestParam(value = "cId")Long cId,HttpServletResponse response){
        if (cId == null){
            return;
        }
        ApiRPCResult<PopPaymentProveDto> apiRPCResult = paymentProveApi.queryByCid(cId);
        if (apiRPCResult == null || apiRPCResult.isFail()  || Objects.isNull(apiRPCResult.getData())){
            return;
        }
        ApiRPCResult<CorporationDto> rpcResult = corporationAdminApi.queryCorporation(cId);
        if (rpcResult == null || rpcResult.isFail() || rpcResult.getData() == null){
            return;
        }
        CorporationDto corporationDto = rpcResult.getData();
        PopPaymentProveDto popPaymentProveDto = apiRPCResult.getData();
        try {
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition","attachment;filename=\""+"收据凭证.docx"+"\"");
            Map<String,Object> map = new HashMap<>();
            map.put("payDate",DateUtil.date2String(popPaymentProveDto.getUpdateTime(),DateUtil.PATTERN_DATE));
            //有样式的文本
            map.put("bonyMoneyStr", NumberUtils.number2CNMontrayUnit(popPaymentProveDto.getMoney()));
            //超链接和锚点文本
            map.put("bonyMoney", popPaymentProveDto.getMoney() != null ?popPaymentProveDto.getMoney().toString(): 0);
            map.put("remarks", "POP保证金");
            map.put("companyName", corporationDto.getCompanyName());
            map.put("xyyName", xyyCompanyName);
            DecimalFormat decimalFormat = new DecimalFormat("##.##");
            map.put("bonyMoney", popPaymentProveDto.getMoney() != null ?decimalFormat.format(popPaymentProveDto.getMoney()): 0);
            map.put("date",DateUtil.date2String(popPaymentProveDto.getUpdateTime(),DateUtil.PATTERN_DATE));
            XWPFTemplate template = WordDownloadUtils.downloadWord("https://oss-ec.ybm100.com/pop/shouju.docx",map);
            OutputStream out = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(out);
            try {
                template.write(bos);
                bos.flush();
                out.flush();
                PoitlIOUtils.closeQuietlyMulti(template, bos, out);
            } catch (IOException e) {
                log.error("MerchantMarginController.downloadCertificate error cId :{}",cId,e);
            }finally {
                if (bos != null) {
                    bos.close();
                }
                if (out != null) {
                    out.close();
                }
            }
        } catch (Exception e) {
            log.error("MerchantMarginController.downloadCertificate error cId :{}",cId,e);
        }
    }

    /**
     * 获取服务名
     * @return
     */
    @GetMapping("/merchant/margin/getHostUrl")
    @ResponseBody
    public ResponseVo getHostUrl(){
        return ResponseVo.successResult(hostUrl);
    }
}
