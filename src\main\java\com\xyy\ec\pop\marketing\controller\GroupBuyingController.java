package com.xyy.ec.pop.marketing.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.marketing.dto.GroupBuyingBatchOfflineResultDTO;
import com.xyy.ec.pop.marketing.easyexcel.rows.GroupBuyingBatchOfflineByActIdExcelRow;
import com.xyy.ec.pop.marketing.easyexcel.rows.GroupBuyingBatchOfflineByReportIdExcelRow;
import com.xyy.ec.pop.marketing.enums.GroupBuyingBatchOfflineTypeEnum;
import com.xyy.ec.pop.marketing.param.GroupBuyingListExportParam;
import com.xyy.ec.pop.marketing.param.GroupBuyingListQueryParam;
import com.xyy.ec.pop.marketing.param.GroupBuyingStatusCountQueryParam;
import com.xyy.ec.pop.marketing.service.GroupBuyingService;
import com.xyy.ec.pop.marketing.service.MarketingTopicService;
import com.xyy.ec.pop.marketing.vo.GroupBuyingInfoVO;
import com.xyy.ec.pop.marketing.vo.GroupBuyingStatisticsVO;
import com.xyy.ec.pop.marketing.vo.GroupBuyingStatusCountVO;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.model.XyyJsonResult;
import com.xyy.ec.pop.vo.KeyValueVO;
import com.xyy.ms.promotion.business.common.constants.MarketingTopicEnum;
import com.xyy.ms.promotion.business.enums.MarketingActivityCreateSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;

/**
 * 拼团活动
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(value = "/groupBuying")
public class GroupBuyingController extends BaseController {

    @Autowired
    private GroupBuyingService groupBuyingService;

    @Autowired
    private MarketingTopicService marketingTopicService;

    /**
     * 活动主题列表
     *
     * @return
     * @see MarketingTopicEnum.TopicTypeEnum
     */
    @ResponseBody
    @RequestMapping("/topic/list")
    public XyyJsonResult listTopics() {
        try {
            List<KeyValueVO> keyValueVOS = marketingTopicService.listTopicVOSByType(MarketingTopicEnum.TopicTypeEnum.GROUP_BUYING.getType());
            return XyyJsonResult.createSuccess().addResult("list", keyValueVOS);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("listTopics 查询异常", e);
            }
            return XyyJsonResult.createFailure().code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("listTopics 查询异常", e);
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        }
    }

    /**
     * 活动来源列表
     *
     * @return
     * @see MarketingActivityCreateSourceEnum
     */
    @ResponseBody
    @RequestMapping("/sourceType/list")
    public XyyJsonResult listSourceTypes() {
        try {
            List<KeyValueVO> keyValueVOS = Lists.newArrayList();
            keyValueVOS.add(KeyValueVO.builder().key(String.valueOf(MarketingActivityCreateSourceEnum.EC.getType()))
                    .value(MarketingActivityCreateSourceEnum.EC.getName()).build());
            keyValueVOS.add(KeyValueVO.builder().key(String.valueOf(MarketingActivityCreateSourceEnum.TI_BAO.getType()))
                    .value(MarketingActivityCreateSourceEnum.TI_BAO.getName()).build());
            return XyyJsonResult.createSuccess().addResult("list", keyValueVOS);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("listSourceTypes 查询异常", e);
            }
            return XyyJsonResult.createFailure().code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("listSourceTypes 查询异常", e);
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        }
    }

    /**
     * 获取拼团活动的统计信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getStatistics")
    public XyyJsonResult getStatistics() {
        try {
            SysUser user = super.getUser();
            String oaId = user.getOaId();
            GroupBuyingStatisticsVO groupBuyingStatisticsVO = groupBuyingService.getStatistics(oaId);
            return XyyJsonResult.createSuccess().addResult("statisticsInfo", groupBuyingStatisticsVO);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("getStatistics 查询异常", e);
            }
            return XyyJsonResult.createFailure().code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("getStatistics 查询异常", e);
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        }
    }

    /**
     * 分页查询拼团活动信息
     *
     * @param groupBuyingListQueryParam
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public XyyJsonResult list(@RequestBody GroupBuyingListQueryParam groupBuyingListQueryParam) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("list 查询，参数：{}", JSONObject.toJSONString(groupBuyingListQueryParam));
            }
            SysUser user = super.getUser();
            String oaId = user.getOaId();
            PageInfo<GroupBuyingInfoVO> pageInfo = groupBuyingService.paging(oaId, groupBuyingListQueryParam);
            return XyyJsonResult.createSuccess()
                    .addResult("pageNo", pageInfo.getPageNum())
                    .addResult("pageSize", pageInfo.getPageSize())
                    .addResult("totalPage", pageInfo.getPages())
                    .addResult("totalCount", pageInfo.getTotal())
                    .addResult("list", pageInfo.getList());
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("list 查询异常，参数：{}", JSONObject.toJSONString(groupBuyingListQueryParam), e);
            }
            return XyyJsonResult.createFailure().code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("list 查询异常，参数：{}", JSONObject.toJSONString(groupBuyingListQueryParam), e);
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        }
    }

    /**
     * 获取拼团活动各个状态的数量
     *
     * @param groupBuyingStatusCountQueryParam
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getStatusCount", method = {RequestMethod.POST})
    public XyyJsonResult getStatusCount(@RequestBody GroupBuyingStatusCountQueryParam groupBuyingStatusCountQueryParam) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("getStatusCount 查询，参数：{}", JSONObject.toJSONString(groupBuyingStatusCountQueryParam));
            }
            SysUser user = super.getUser();
            String oaId = user.getOaId();
            GroupBuyingStatusCountVO groupBuyingStatusCountVO = groupBuyingService.getStatusCount(oaId, groupBuyingStatusCountQueryParam);
            return XyyJsonResult.createSuccess().addResult("statusCountInfo", groupBuyingStatusCountVO);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("getStatusCount 查询异常，参数：{}", JSONObject.toJSONString(groupBuyingStatusCountQueryParam), e);
            }
            return XyyJsonResult.createFailure().code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("getStatusCount 查询异常，参数：{}", JSONObject.toJSONString(groupBuyingStatusCountQueryParam), e);
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        }
    }

    /**
     * 导出
     *
     * @param groupBuyingListExportParam
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/export", method = {RequestMethod.POST})
    public XyyJsonResult export(@RequestBody GroupBuyingListExportParam groupBuyingListExportParam) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("export 查询，参数：{}", JSONObject.toJSONString(groupBuyingListExportParam));
            }
            SysUser user = super.getUser();
            groupBuyingService.asyncExport(user, groupBuyingListExportParam);
            return XyyJsonResult.createSuccess().msg("文件生成中，请到 文件下载中心 页面进行查看和下载");
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("导出异常，参数：{}", JSONObject.toJSONString(groupBuyingListExportParam), e);
            }
            return XyyJsonResult.createFailure().code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("导出异常，参数：{}", JSONObject.toJSONString(groupBuyingListExportParam), e);
            return XyyJsonResult.createFailure().msg("导出失败，请稍后重试！");
        }
    }

    /**
     * 批量下线
     *
     * @param type      下线方式。1：通过报名ID下线；2：通过活动ID下线
     * @param excelFile excel文件
     * @return
     * @see GroupBuyingBatchOfflineTypeEnum
     */
    @ResponseBody
    @RequestMapping(value = "/batchOffline", method = {RequestMethod.POST})
    public XyyJsonResult batchOffline(@RequestParam(value = "type", required = false) Integer type,
                                      @RequestParam(value = "excelFile", required = false) MultipartFile excelFile) {
        try {
            SysUser user = super.getUser();
            GroupBuyingBatchOfflineResultDTO resultDTO = groupBuyingService.batchOffline(user, type, excelFile);
            return XyyJsonResult.createSuccess().addResult("resultInfo", resultDTO);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("批量下线拼团活动异常，type：{}", type, e);
            }
            return XyyJsonResult.createFailure().code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("批量下线拼团活动异常，type：{}", type, e);
            return XyyJsonResult.createFailure().msg("批量下线失败，请稍后重试！");
        }
    }

    /**
     * 下载批量下线模板
     *
     * @param type     下线方式。1：通过报名ID下线；2：通过活动ID下线
     * @param response
     * @see GroupBuyingBatchOfflineTypeEnum
     */
    @RequestMapping("/downloadBatchOfflineTemplate")
    public void downloadBatchOfflineTemplate(@RequestParam(value = "type", required = false) Integer type,
                                             HttpServletResponse response) {
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        try {
            if (Objects.isNull(type) || Objects.isNull(GroupBuyingBatchOfflineTypeEnum.valueOfCustom(type))) {
                this.writeResponseHtmlDialogMsg(response, "请选择下线方式！");
                return;
            }
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("批量下线拼团活动模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            if (Objects.equals(type, GroupBuyingBatchOfflineTypeEnum.BY_REPORT_ID.getType())) {
                // 这里需要设置不关闭流
                EasyExcel.write(response.getOutputStream(), GroupBuyingBatchOfflineByReportIdExcelRow.class)
                        .autoCloseStream(Boolean.FALSE)
                        .sheet("模板")
                        .doWrite(Lists.newArrayList());
            } else if (Objects.equals(type, GroupBuyingBatchOfflineTypeEnum.BY_ACT_ID.getType())) {
                // 这里需要设置不关闭流
                EasyExcel.write(response.getOutputStream(), GroupBuyingBatchOfflineByActIdExcelRow.class)
                        .autoCloseStream(Boolean.FALSE)
                        .sheet("模板")
                        .doWrite(Lists.newArrayList());
            }
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("下载批量下线模板异常，type：{}", type, e);
            }
            this.writeResponseHtmlDialogMsg(response, e.getMsg());
        } catch (Exception e) {
            log.error("下载批量下线模板异常，type：{}", type, e);
            this.writeResponseHtmlDialogMsg(response, "下载模板失败，请稍后重试！");
        }
    }

    private void writeResponseHtmlDialogMsg(HttpServletResponse response, String msg) {
        try {
            // 重置response
            response.reset();
            response.setContentType("text/html");
            response.setCharacterEncoding("utf-8");
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("<script type=\"text/javascript\" charset=\"UTF-8\">");
            stringBuilder.append("alert(\"" + msg + "\");");
            stringBuilder.append("window.close()");
            stringBuilder.append("</script>");
            response.getWriter().print(stringBuilder);
        } catch (Exception e) {
            log.error("writeResponseHtmlDialogMsg异常，msg：{}", msg, e);
        }
    }

}
