
CREATE TABLE `tb_xyy_pop_business_category_commission`(
	`id` bigint(20) NOT NULL  auto_increment COMMENT '主键自增' ,
	`c_id` bigint(20) NOT NULL  DEFAULT 0 COMMENT '企业id' ,
	`org_id` varchar(64)  NOT NULL  DEFAULT '' COMMENT '机构id' ,
	`business_first_category_code` varchar(50)  NOT NULL  DEFAULT '' COMMENT '经营分类一级类目code' ,
	`business_second_category_code` varchar(50)  NOT NULL  DEFAULT '' COMMENT '经营分类二级类目code' ,
	`commission_ratio` decimal(10,2) NOT NULL  DEFAULT 0.00 COMMENT '佣金比例' ,
	`is_delete` tinyint(4) NOT NULL  DEFAULT 0 COMMENT '删除状态，0-未删除,1-已删除' ,
	`creator` varchar(200)  NOT NULL  DEFAULT '' COMMENT '创建人' ,
	`create_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
	`updator` varchar(200)  NOT NULL  DEFAULT '' COMMENT '修改人' ,
	`update_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' ,
	PRIMARY KEY (`id`) ,
	UNIQUE KEY `uniq_c_id_business_second_category_code`(`c_id`,`business_second_category_code`) ,
	KEY `idx_org_id_business_second_category_code`(`org_id`,`business_second_category_code`) ,
	KEY `idx_c_id`(`c_id`) ,
	KEY `idx_org_id`(`org_id`)
) COMMENT='企业经营类目与佣金比例配置表';

CREATE TABLE `tb_xyy_pop_business_category_commission_history`(
	`id` bigint(20) NOT NULL  auto_increment COMMENT '主键自增' ,
	`c_id` bigint(20) NOT NULL  DEFAULT 0 COMMENT '企业id' ,
	`org_id` varchar(64)  NOT NULL  DEFAULT '' COMMENT '机构id' ,
	`business_first_category_code` varchar(50)  NOT NULL  DEFAULT '' COMMENT '经营分类一级类目code' ,
	`business_second_category_code` varchar(50)  NOT NULL  DEFAULT '' COMMENT '经营分类二级类目code' ,
	`commission_ratio` decimal(10,2) NOT NULL  DEFAULT 0.00 COMMENT '佣金比例' ,
	`is_delete` tinyint(4) NOT NULL  DEFAULT 0 COMMENT '删除状态，0-未删除,1-已删除' ,
	`creator` varchar(200)  NOT NULL  DEFAULT '' COMMENT '创建人' ,
	`create_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
	`updator` varchar(200)  NOT NULL  DEFAULT '' COMMENT '修改人' ,
	`update_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' ,
	PRIMARY KEY (`id`) ,
	KEY `idx_c_id_business_second_category_code`(`c_id`,`business_second_category_code`) ,
	KEY `idx_org_id_business_second_category_code`(`org_id`,`business_second_category_code`) ,
	KEY `idx_c_id`(`c_id`) ,
	KEY `idx_org_id`(`org_id`)
) COMMENT='企业经营类目与佣金比例配置历史记录表';

CREATE TABLE `tb_xyy_pop_sku_commission_history`(
	`id` bigint(20) NOT NULL  auto_increment COMMENT '主键自增' ,
	`sku_id` bigint(11) NOT NULL  DEFAULT 0 COMMENT '商品id' ,
	`commission_ratio` decimal(10,2) NOT NULL  DEFAULT 0.00 COMMENT '佣金比例' ,
	`creator` varchar(200)  NOT NULL  DEFAULT '' COMMENT '创建人' ,
	`create_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
	`updator` varchar(200)  NOT NULL  DEFAULT '' COMMENT '修改人' ,
	`update_time` datetime NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' ,
	PRIMARY KEY (`id`) ,
	KEY `idx_sku_id`(`sku_id`)
) COMMENT='商品佣金比例历史记录表';

ALTER TABLE `tb_xyy_pop_sku_extend`
	ADD COLUMN `org_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '机构id' after `id` ,
	ADD COLUMN `business_first_category_code` varchar(50)  NOT NULL DEFAULT '' COMMENT '一级分类' after `source` ,
	ADD COLUMN `business_second_category_code` varchar(50)  NOT NULL DEFAULT '' COMMENT '二级分类' after `business_first_category_code` ,
	ADD COLUMN `commission_ratio` decimal(10,2)   NOT NULL DEFAULT 2.00 COMMENT '佣金比例' after `business_second_category_code` ;