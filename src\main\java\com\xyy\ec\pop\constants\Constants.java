/*
 * @(#)Constants.java 2014-11-21
 *
 * Copyright 2014 Right,Inc. All rights reserved.
 */
package com.xyy.ec.pop.constants;


import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR> 2014-11-21
 */
public class Constants {
	public static final String ADMIN = "admin";
	public static final String LIMIT = "limit";
	public static final String OFFSET = "offset";
	public static final String XYYWESERVICE = "xyy-webservice";
	public static final String RESOURCE = "resource";
	public static final String AREAORGID = "areaOrgId";
	public static final String STATUS = "status";
	public static final String ORDERSTR = "orderStr";
	public static final String CODERMAP = "codeMap";



	public static final String PRODUCT_CATEGORY_INFO = "PRODUCT_CATEGORY_INFO";
    public static final String PRODUCT_INFO = "PRODUCT_INFO";
    public static final String PRODUCT_CATEGORY_KEY = "PRODUCT_CATEGORY_KEY";
    public static final String PRODUCT_KEY = "PRODUCT_KEY";
    //正常商品个人限购数量
    public static final Integer PRODUCT_PERSON_PURCHASE_QTY = 50;

	public final static Integer IS0 =0;
	public final static Integer IS1 =1;
	public final static Integer IS2 =2;
	public final static Integer IS3 =3;
	public final static Integer IS4 =4;
	public final static Integer IS5 =5;
	public final static Integer IS6 =6;
	public final static Integer IS7 =7;
	public final static Integer IS8 =8;
	public final static Integer IS9 =9;
	public final static Integer IS10 =10;
	public final static Integer IS11 =11;
	
	public static Integer IS99 = 99;
	public final static Integer IS100 =100;

	// 商品状态标识
	public static final int PRODUCT_IS_NOT_SPLIT = 0; // 商品不可拆零
	public static final int STATUS_IN_THE_SALES = 1; // 销售中
	public static final int STATUS_SOLD_OUT = 2; // 已售罄
	public static final int STATUS_IN_THE_PREFERENTIAL = 3; // 特惠中
	public static final int STATUS_THE_SHELVES = 4; // 下架
	public static final int STATUS_THE_SECKILL = 5; // 秒杀
	public static final int STATUS_THE_TO_SALES = 6; // 待上架


	public static final Boolean IS_DEFAULTED = Boolean.TRUE;      //设置默认

	public static final Boolean IS_NOT_DEFAULTED = Boolean.FALSE;  //非默认

	public static final Integer GSP_REMARK_IS_PASS = 3;  //GSP备注地址审核通过


	public static final int IS_SELECT = 1;
	public static final int IS_UNSELECT = 0;

	public static final int IS_CONTROL_YES = 1;

	public static final int IS_CONTROL_NO = 0;

	/** 病单状态标识 */
	/** 待处理 */
	public static final int INTERVENTION_HANDLE_WAIT = 1;

	/** 已处理 */
	public static final int INTERVENTION_HANDLE_COMPLETE  = 2;

	/** 已关闭 */
	public static final int INTERVENTION_HANDLE_CLOSE  = 3;
	
	/**
	 * 默认编码
	 */
	public static final String DEFAULT_ENCODING = "UTF-8";


	public static final Map<Integer, String> promoTypeMap = new HashMap<>();
	
	
	public static final String LINE = "-";

	static{
		promoTypeMap.put(1, "满减促销");
		promoTypeMap.put(2, "满折促销");
		promoTypeMap.put(3, "满赠促销");
		promoTypeMap.put(4, "满减赠促销");
		promoTypeMap.put(5, "返点返券");
	}

	/**
	 * 布尔状态(通用)
	 * <AUTHOR> 2015-3-11
	 */
	public static class BoolStatus {
		public static final int  YES= 1;
		public static final int  NO= 0;

	}
	
	/**
	 * API或Web应用返回码
	 * <AUTHOR> 2014-12-8
	 */
	public static class ReturnCodes {
		public static final int CODE_1 = 1;
		public static final int CODE_0 = 0;
		public static final int CODE_400 = 400;				
	    public static final int CODE_404 = 404;
	    public static final int CODE_500 = 500;					
	    public static final int CODE_200 = 200;
	    public static final String RESULT_STATUS = "status";
	    public static final String RESULT_ERRORMSG = "errorMsg";
	    public static final String RESULT_ERRORCODE = "errorCode";
	    public static final String RESULT_MSG = "msg";
	    public static final String RESULT_SUCCESS = "success";
	    public static final String RESULT_FAILURE = "failure";
	}
	
	/**
	 * 上传规格限制
	 * <AUTHOR> 2015-3-11
	 */
	public static class UploadSpecs {
	    /**
	     * 上传单张图片允许的大小上限Byte
	     */
	    public static final int IMG_SIZE_LIMIT = 2 * 1024 * 1024;
	    
	    public static final int ATTACHMENT_SIZE_LIMIT = 10*1024*1024;
	    /**
	     * 上传图片允许的后缀类型
	     */
	    public static final String IMG_SUFFIX_LIST = ".gif.jpg.jpeg.bmp.png";
	    
	    public static final String ATTACHMENT_SUFFIX_LIST = ".gif.jpg.jpeg.bmp.png.pdf.ppt.pptx.doc.docx.xls.xlsx.txt";
	}
	
	/**
	 * 日期格式
	 * <AUTHOR> 2014-12-3
	 */
	public static class DateFormats {
		public static final String PATTERN_STANDARD = "yyyy-MM-dd HH:mm:ss";
		public static final String PATTERN_DATE = "yyyy-MM-dd";
	}
	
	/**
	 * 请求内容类型
	 * <AUTHOR> 2014-12-4
	 */
	public static class ContentType {
		public static final String TEXT_TYPE = "text/plain";
		public static final String JSON_TYPE = "application/json";
	}
	
	/**
	 * API数据类型
	 * <AUTHOR> 2014-12-4
	 */
	public static class DataType {
		public static final String JSON = "json";
		public static final String XML = "xml";
		public static final String BINARY = "binary";
	}
	
	/**
	 * 模板类型
	 * <AUTHOR> 2014-12-23
	 */
	public static class TemplateType {
		public static final String SMS = "sms";
		public static final String EMAIL = "email";
		public static final String NOTICE = "notice";
	}
	
	/**
	 * 移动操作系统类型
	 * <AUTHOR> 2014-12-27
	 */
	public static class OsType {
		public static final String ALL="all";
		public static final String ANDROID = "android";
		public static final String IOS = "ios";
		public static final String WINPHONE = "winphone";
		public static final String PC="pc";
	}
	/**
	 * 语言编码
	 * <AUTHOR> 2014-12-23
	 */
	public static class LanguageCodes {
		public static final String ZH = "zh";//中文
		public static final String EN = "en";//英文
		public static final String FR = "fr";//法文
	}
	
	/**
	 * Memcached对象过期列表(单位：秒)
	 * <AUTHOR> 2015-6-15
	 */
	public static class MemcachedExpireds{
		public static final int EXPIRED_TIME = 1*60*60;   //过期时间，1小时
		public static final int DEMO_EXPIRED = 1*60*60;   //Demo详情1小时过期
		public static final int DEMO_LIST_EXPIRED = 1*60*60;   //Demo详情1小时过期
		public static final int SUB_CATEGORY_LIST_EXPIRED = 1*24*60*60; //商品子类目列表缓存1天
		public static final int GOODES_EXPIRED = 10*60; //商品详情10分钟过期
		public static final int GOODES_LIST_EXPIRED = 10*60; //商品分页列表缓存10分钟过期
	}
	
	/**
	 * 后台菜单类型
	 * @ClassName: AdminMenuType 
	 * <AUTHOR>
	 * @date 2017-5-24 下午2:02:50
	 */
	public static class AdminMenuType{
		public static final String DRIVER_WEBSERVICE = "司机端后台菜单";
		public static final String WEBSERVICE = "运营后台菜单";
		public static final String MAGIC_WEBSERVICE = "爬虫后台菜单";
		public static final String REPORT = "报表后台菜单";
	}
	
	public static class BranchProperty{
		public static final String BRANCH_CODE = "branchCode";
		public static final String BRANCH_CODE_STARTWITH= "XS";
	}
	
   /** KA用户标识 */
   public static final String TYPE_IS_KA = "TYPE_IS_KA";	
   
   /** 同步标识前缀 */
   public static final String SYNC_STARTWITH = "SYNC";
   
   /** 订单号前缀 */
   public static final String ORDERNO_PREFIX = "YBM";
   
   public static final String CODE = "code";
   
}
