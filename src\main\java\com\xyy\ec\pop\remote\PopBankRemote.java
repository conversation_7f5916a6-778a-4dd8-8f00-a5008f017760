package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.bank.api.PopBankApi;
import com.xyy.ec.pop.server.api.bank.dto.BankAccountDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PopBankRemote {
    @Reference
    private PopBankApi popBankApi;
    public BankAccountDto queryOpenedBankAccountByOrgId(String orgId) {
        try {
            log.info("PopBankRemote.queryOpenedBankAccountByOrgId#orgId:{}", orgId);
            ApiRPCResult<BankAccountDto> res = popBankApi.queryOpenedBankAccountByOrgId(orgId);
            log.info("PopBankRemote.queryOpenedBankAccountByOrgId#orgId:{} return {}", orgId, JSON.toJSONString(res));
            if (res.isSuccess()) {
                return res.getData();
            }
        } catch (Exception e) {
            log.error("PopBankRemote.queryOpenedBankAccountByOrgId#orgId:{} 异常", orgId, e);
        }
        return null;
    }
}
