var btnArr = [];
function statusFormatter(val,order) {
    if (val == 0) {
        return "尚未进入审单流程";
    } else if (val == 1) {
        return "已支付";
    } else if (val == 2) {
        return "配送中";
    } else if (val == 3) {
        return "已完成";
    } else if (val == 4) {
        return "取消";
    } else if (val == 5) {
        return "删除";
    } else if (val == 6) {
        return "已拆单";
    } else if (val == 7) {
        return "正在开单";
    } else if (val == 32) {
        return "分拣中";
    } else if (val == 33) {
        return "待配送";
    } else if (val == 9) {
        return "审单流程结束";
    } else if (val == 10) {
        if(order.payChannel!=8){
            return "未支付";
        }
        var status ="待上传电汇凭证";
        if(order.evidenceVerifyStatus==1){
            status ="待审核电汇凭证";
        }else if(order.evidenceVerifyStatus==2){
            status ="审核通过";
        }
        return "未支付（"+status+"）";
    }else if (val == 11) {
        return "已支付";
    }else if (val == 90) {
        return "退款审核中";
    } else if (val == 91) {
        return "已退款";
    } else if (val == 20) {
        return "已送达";
    } else if (val == 21) {
        return "已拒签";
    } else if(val == 8) {
        return "草稿";
    }else {
        return "--";
    }
}


function payTypeFormatter(val) {
    if (val == 1) {
        return "在线支付"
    } else if (val == 2) {
        return "货到付款";
    } else if (val == 3) {
        return "线下转账";
    } else {
        return "";
    }
}

function paymentChannelFormatter(val) {
    if (val == 1) {
        return "支付宝";
    } else if (val == 2) {
        return "微信";
    } else if (val == 3) {
        return "银联";
    } else if(val==7){
        return "电汇平台"
    } else if(val==8){
        return "电汇商业"
    } else if (val == 10) {
        return "平安ePay"
    } else if (val == 11) {
        return "JD银行卡支付"
    }else if (val == 12) {
        return "京东采购融资";
    }else if (val == 13) {
        return "农行链e贷";
    }else if (val == 14) {
        return "小雨点白条";
    }else if (val == 15) {
      return "金蝶信用付";
  }
}

function datetimeFormatter(val) {
    if (val != null) {
        return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
    }
}

function statusPayTypeFormatter(val) {
    if (val == 1) {
        return "在线支付";
    } else if (val == 2) {
        return "货到付款";
    } else if (val == 3) {
        return "线下转账";
    }
}

function orderSourceFormatter(val) {
    if (val == 0) {
        return "后台订单";
    } else if (val == 4) {
        return "PC订单";
    } else {
        return "APP订单";
    }
}

function firstOrderFormatter(val) {
    if (val == 1) {
        return "是";
    } else {
        return "否";
    }
}

function orderFormatter(val, row) {
    if (row.orderType == 7) {
        return val + "(拼团)";
    } else if (row.isRandom == 1) {
        return val + "(随心拼)";
    } else if (row.orderType == 10) {
        return val + "(批购包邮)";
    }{
        return val;
    }
}

/**
 *查询超时未发货订单数
 */
function queryTimeoutOrderCount() {
    $.get('/order/queryTimeoutOrderCount', function (res) {
        if (res.code === 0) {
            if (res.data > 0) {
                var tipMsg = "*有" + res.data + "笔订单超48小时未发货，请及时处理";
                $("#deliver_timeout").show();
                $("#tip_msg").text(tipMsg);
            } else {
                $("#deliver_timeout").hide();
            }
        }
    })
}
function queryLogisticsTrackCount() {
    $.get('/order/queryLogisticsTrackCount', function (res) {
        if (res.code === 0) {
           if(res.data.logisticsTrackFailCount>0){
               $("#logisticsTrackFail").show();
               $("#trackFail_tip_msg").text("*有" + res.data.logisticsTrackFailCount + "笔订单物流轨迹获取失败，请及时关注");
           } else {
               $("#logisticsTrackFail").hide();
           }
            if(res.data.logisticsTrackIllegalCount>0){
                $("#logisticsTrackIllegal").show();
                $("#trackIllegal_tip_msg").text("*有" + res.data.logisticsTrackIllegalCount + "笔订单物流揽收或签收时间<支付时间，请及时关注");
            } else {
                $("#logisticsTrackIllegal").hide();
            }
        }
    })
}

window.remarkEvents = {
    'click .remark_btn': function (e, value, row, index) {
        var id = row.id;
        var orderNo = row.orderNo;
        var urgencyDegree= row.orderNo;
        var title="添加备注";
        if(!!urgencyDegree){
            title="修改备注";
        }
        console.info(id);
        console.info(orderNo);

        LayTool.open({
            title: title,
            area: ['600px', '400px'],
            id : "order_remark",
            content: [
                '/order/orderServiceUrgency?orderNo=' + orderNo, 'yes'
            ],
            end: function () {
                // $('#tb_pendingList').bootstrapTable('refresh');
            }
        });
    }

};
window.operateEvents = {
      'click .exception_btn': function(e, value, row, index) {
        var id = row.id;
        var orderNo = row.orderNo;
        var merchantName = row.merchantName;
        LayTool.open({
            title: "关联资质异常",
            area: ['600px', '400px'],
            id : "exception_log",
            content: [
                '/order/exceptionLog?orderNo=' + orderNo + '&merchantName=' + merchantName, 'yes'
            ],
            end: function () {
                // $('#tb_pendingList').bootstrapTable('refresh');
            }
        });
      },
      'click .log_btn': function (e, value, row, index) {
        var id = row.id;
        var orderNo = row.orderNo;

        console.info(id);
        console.info(orderNo);

        LayTool.open({
            title: "操作日志",
            area: ['600px', '400px'],
            id : "check_log",
            content: [
                '/order/operationLog?orderNo=' + orderNo, 'yes'
            ],
            end: function () {
                // $('#tb_pendingList').bootstrapTable('refresh');
            }
        });
    },
    'click .sale_btn': function (e, value, row, index) {
        var salesId =  row.salesId;
        if (salesId == null){
            LayTool.alert("业务员信息为空");
            return;
        }
        LayTool.open({
            title: "业务员信息",
            area: ['600px', '400px'],
            id : "sale_mes",
            content: [
                '/order/getSalesMessage?saleId=' + salesId, 'yes'
            ],
            end: function () {
                // $('#tb_pendingList').bootstrapTable('refresh');
            }
        });
    },
    'click .order_sync_error_reason_bin': function (e, value, row, index) {
        var orderSyncErrorReason = row.orderSyncErrorReason;
        layer.open({
            title: "下推失败原因 ",
            area: ['600px', '400px'],
            id : "order_sync_error_reason",
            btn:[],
            content: orderSyncErrorReason
        });
    }

};
var applyPendingList = $.applyPendingList = ({

    //项目大类列表初始化
    tableInit: function () {
        var applyPendingTableObj = new Object();
        //初始化Table
        applyPendingTableObj.Init = function () {
            var $table = $('#tb_pendingList');
            $table.bootstrapTable({
                url: basePath + 'order/list', //请求后台的URL（*）
                method: 'get', //请求方式（*）
                contentType: "application/x-www-form-urlencoded",
                dataType: "json", //传入的类型
                toolbar: '#toolbar', //工具按钮用哪个容器
                striped: true, //是否显示行间隔色
                cache: false, //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                pagination: true, //是否显示分页（*）
                sortable: true, //是否启用排序
                sortOrder: "asc", //排序方式
                queryParams: applyPendingTableObj.queryParams, //传递参数（*）
                sidePagination: "server", //分页方式：client客户端分页，server服务端分页（*）
                formatLoadingMessage: function () {
                    return '请稍后,正在加载中...';
                },
                formatNoMatches:function (){
                    return "没有找到匹配的数据";
                },
                responseHandler: function (res) {
                    return {                            //return bootstrap-table能处理的数据格式
                        "total": res.data?res.data.total:0,
                        "rows": res.data?res.data.rows:''
                    }
                },
                pageNumber: 1, //初始化加载第一页，默认第一页
                pageSize: 10, //每页的记录行数（*）
                pageList: [10, 20, 50, 100], //可供选择的每页的行数（*）
                strictSearch: true,
                clickToSelect: true, //是否启用点击选中行
                //height: 550, //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                onClickRow: function (row, $element) {
                    $('.success').removeClass('success');
                    $($element).addClass('success');
                    if(row.isThirdCompany != 0){
                        $("#btn_confirm_money").show();
                        $("#btn_batch_over").show();
                        $("#btn_export").show();
                    }
                    // 如果支付方式为'1:在线支付'则隐藏掉取消按钮 20161104104629 添加
                    if (row.payType == 1) {
                        if (row.status == 10) {
                            $("#btn_cancel").show();
                        } else {
                            $("#btn_cancel").hide();
                        }
                        $("#btn_checkgetMonery").parent().hide();
                        if (row.status == 1 || row.status == 2 || row.status == 3 || row.status == 6 || row.status == 90 || row.status == 91) {
                            $("#btn_customerServiceIntervention").parent().show();
                            $("#btn_separate").parent().hide();
                        } else {
                            $("#btn_customerServiceIntervention").parent().hide();
                        }
                    } else if (row.payType == 2) {
                        $("#btn_cancel").show();
                        $("#btn_checkgetMonery").parent().hide();
                        $("#btn_customerServiceIntervention").parent().hide();
                    } else {
                        $("#btn_cancel").hide();
                        $("#btn_checkgetMonery").parent().show();
                        if (row.status == 1 || row.status == 2 || row.status == 3 || row.status == 6 || row.status == 90 || row.status == 91) {
                            $("#btn_customerServiceIntervention").parent().show();
                            $("#btn_separate").parent().hide();
                        } else {
                            $("#btn_customerServiceIntervention").parent().hide();
                        }
                    }
                    if (row.payType == 3) {
                        if (row.status == 10) {
                            $("#btn_checkgetMonery").parent().show();
                            $("#btn_cancel").show();
                        } else {
                            $("#btn_checkgetMonery").parent().hide();
                            $("#btn_cancel").hide();
                        }
                    }

                    if (row.status == 1) {
                        $("#btn_ship").parent().show();
                        if (row.payType == 2) {
                            if (row.orderNo.indexOf("-") < 0) {
                                $("#btn_separate").parent().show();
                            } else {
                                $("#btn_separate").parent().hide();
                            }
                        }
                    } else {
                        $("#btn_ship").parent().hide();
                        $("#btn_separate").parent().hide();
                    }
                    if (row.status == 2) {
                        $("#btn_finish").parent().show();
                        $("#btn_is_delivered").parent().show();
                        $("#btn_rejection").parent().show();
                    } else {
                        $("#btn_finish").parent().hide();
                        $("#btn_is_delivered").parent().hide();
                        $("#btn_rejection").parent().hide();
                    }

                    if (row.status == 20) {
                        $("#btn_finish").parent().show();
                    }

                    if (row.status == 6) {
                        if (row.separateFlag == 1) {
                            $("#btn_separateReduction").parent().show();
                        } else {
                            $("#btn_separateReduction").parent().hide();
                        }
                    } else {
                        $("#btn_separateReduction").parent().hide();
                    }
                    if (row.status == 6 || row.status == 4 || row.status == 5 || row.status == 10 || row.status == 91) {
                        $("#btn_apply_refund").parent().hide();
                    } else {
                        $("#btn_apply_refund").parent().show();
                    }

                    if (row.gspAuditState == 2 && row.status == 1) {
                        $("#btn_gsp_remark_pass").parent().show();
                    } else {
                        $("#btn_gsp_remark_pass").parent().hide();
                    }
                    /* 是否展示 确认付款按钮 单状态：1-待配送，2-配送中，3-已配送，4-取消，5-已删除,6-已拆单,7-出库中,10-未支付,90-退款审核中,91-已退款,20-已送达,21-已拒签',
  `check_status` int(1) DEFAULT '0' COMMENT '审单流程状态：默认：0-尚未进入审单流程,1-审单中，2-开票中，3-发货中，9-审单流程结束',*/
                    // 支付类型(1:在线支付 2:货到付款 3:线下转账
                    //payChannel 7-电汇平台，8-电汇商业
                    if (row.status == 10 && row.payType == 3&&row.payChannel==7) {
                        $("#btn_confirm_money").show();
                    } else {
                        $("#btn_confirm_money").hide();
                    }
                    if(row.isThirdCompany == 0){
                        $("#btn_confirm_money").hide();
                        $("#btn_batch_over").hide();
                        $("#btn_export").hide();
                    }
                },
                uniqueId: "id", //每一行的唯一标识，一般为主键列
                columns: [
                    {
                        field: 'corporationNo',
                        title: '商户编号',
                        align: 'center',
                        sortable: true
                    }, {
                        field: 'companyName',
                        title: '商户名称',
                        align: 'center',
                        sortable: true
                    }, {
                        field: 'corporationName',
                        title: '店铺名称',
                        align: 'center',
                        sortable: true
                    }, {
                        field: 'prov',
                        title: '商户注册省份',
                        align: 'center',
                        sortable: true
                    },{
                        field: 'merchantName',
                        title: '客户名称',
                        align: 'center',
                        sortable: true,
                        formatter: function (val, arguments) {
                            var order = arguments;
                            var str = "";
                           if (order.accountStatus == 0){
                               str =  "（未开户）";
                           }
                           if (order.accountStatus == 1){
                               str =  "（已开户）";
                           }
                           if (order.accountStatus == 2){
                               str =  "（销户）";
                           }
                           return str + val;
                        }
                    }, {
                        field: 'businessTypeName',
                        title: '客户类型',
                        align: 'center',
                        sortable: true
                    },{
                        field: 'orderNo',
                        title: '销售单号',
                        align: 'center',
                        sortable: true,
                        formatter: orderFormatter
                    }, {
                        field: 'originalTotalAmount',
                        title: '订单金额',
                        align: 'center'
                    }, {
                        field: 'freightAmount',
                        title: '运费金额',
                        align: 'center'
                    }, {
                        field: 'money',
                        title: '总实付金额',
                        align: 'center'
                    }, {
                        field: 'virtualGold',
                        title: '购物金实付金额',
                        align: 'center'
                    }, {
                        field: 'cashPayAmount',
                        title: '现金实付金额',
                        align: 'center'
                    }, {
                        field: 'originalTotalDiscount',
                        title: '优惠金额',
                        align: 'center'
                    }, {
                        field: 'createTime',
                        title: '下单时间',
                        align: 'center',
                        formatter: datetimeFormatter
                    }, {
                        field: 'branchName',
                        title: '省份',
                        align: 'center',
                        sortable: true
                    },
                     {
                        field: 'logisticsWay',
                        title: '物流公司',
                        align: 'center',
                        sortable: true
                    }, {
                        field: 'payType',
                        title: '支付类型',
                        align: 'center',
                        sortable: true,
                        formatter: payTypeFormatter
                    }, {
                        field: 'payChannel',
                        title: '支付渠道',
                        align: 'center',
                        formatter: paymentChannelFormatter
                    }, {
                        field: 'payTime',
                        title: '支付时间',
                        align: 'center',
                        formatter: function (val, arguments) {
                            var order = arguments;
                            if (order.payType == 1) {
                                return datetimeFormatter(val);
                            } else if (order.payType == 2) {
                                return datetimeFormatter(order.createTime);
                            } else if (order.payType == 3) {
                                return datetimeFormatter(order.paymentTime);
                            }
                        }
                    },  {
                        field: 'shipTime',
                        title: '出库时间',
                        align: 'center',
                        formatter: datetimeFormatter
                    }, {
                        field: 'finishTime',
                        title: '完成时间',
                        align: 'center',
                        formatter: datetimeFormatter
                    },{
                        field: 'status',
                        title: '状态',
                        align: 'center',
                        formatter: statusFormatter
                    }, {
                        field: 'orderSyncStatus',
                        title: '下推ERP状态',
                        align: 'center',
                        sortable: true,
                        events: operateEvents,
                        formatter:function (val, arguments) {
                            var order  = arguments;
                            var msg =  "";
                            if (val  == 0){
                                msg =  "未下发";
                            } if (val  == 1){
                                msg =  "已下发";
                            } if (val  == 2){
                                msg =  "下发失败";
                                msg += '<input type="submit" value="查看原因" class="order_sync_error_reason_bin">';
                            } if (val  == 3){
                                msg =  "手动发货";
                            }
                            return msg;
                        }
                    }, {
                        field: 'contactor',
                        title: '收货人',
                        align: 'center',
                        sortable: true
                    }, {
                        field: 'mobile',
                        title: '手机号码',
                        align: 'center'
                    }, {
                        field: 'urgencyDegree',
                        title: '客服备注',
                        align: 'center',
                        sortable: true,
                        events: remarkEvents,
                        formatter: function (val, arguments) {
                            var orderNo=arguments.orderNo;
                            if (val == 0) {
                                return '<a href="javascript:void(0);" class="remark_btn" data-value="'+orderNo+'">备注</a>';
                                  } else if (val == 1) {
                                return '<a href="javascript:void(0);" class="remark_btn" data-value="'+orderNo+'">低</a>';
                            } else if (val == 5) {
                                return '<a href="javascript:void(0);" class="remark_btn" data-value="'+orderNo+'">中</a>';
                            }else if (val == 10) {
                                return '<a href="javascript:void(0);" class="remark_btn" data-value="'+orderNo+'">高</a>';
                            } else {
                                return '<a href="javascript:void(0);" class="remark_btn" data-value="'+orderNo+'">备注</a>';
                            }
                        }
                    }, {
                        field: 'firstOrderFlag',
                        title: '是否首单',
                        align: 'center',
                        formatter: firstOrderFormatter
                    }, {
                        field: 'isVirtualSupplier',
                        title: '虚拟供应商',
                        align: 'center',
                        formatter: function (val) {
                            if(val==1){
                                return "是";
                            }
                            return "否"
                        }
                    },{
                        field: 'isFbp',
                        title: '是否入仓订单',
                        align: 'center',
                        formatter: function (val) {
                            if(val==1){
                                return "是";
                            }
                            return "否"
                        }
                    }, {
                        field: 'nextDayDelivery',
                        title: '是否次日达订单',
                        align: 'center',
                        formatter: function (val) {
                            if(val==1){
                                return "是";
                            }
                            return "否"
                        }
                    },{
                        field: 'exceptionFlag',
                        title: '关联资质状态',
                        align: 'center',
                        events: operateEvents,
                        formatter: function (val, arguments) {
                            var orderNo = arguments.orderNo;
                            if (val) {
                                // '<input type="submit" value="查看原因" class="order_sync_error_reason_bin">
                                return "<div><p>关联资质异常</p><input type='submit' class='exception_btn btn btn-info btn-xs' value='查看' /><div>";
                            };
                            return "关联资质正常";
                        }
                    }, {
                        field: 'operator',
                        title: '操作',
                        align: 'center',
                        events: operateEvents,
                        formatter: function (value, row, index) {
                            var arr = ["查看日志","查看业务员"];
                            var html = "";
                            for (var i = 0; i < arr.length ; i++ ){
                                if(jQuery.inArray(arr[i], btnArr) != -1){
                                    html += '<input type="submit" value="' + arr[i] + '" class="log_btn btn btn-info btn-xs" data-toggle="modal"  style="display:inline">';
                                }
                            }
                            return html;
                        }
                    }
                    ]
            });
        };

        //查询的参数
        applyPendingTableObj.queryParams = function (params) {
            var startCreateTime = $("#txt_search_startCreateTime").val();
            if(!!startCreateTime){
                 startCreateTime= startCreateTime+":00";
            }
            var endCreateTime = $("#txt_search_endCreateTime").val();
            if(!!endCreateTime){
                endCreateTime= endCreateTime+":00";
            }
            var startPayTime = $("#txt_search_startPayTime").val();
            if(!!startPayTime){
                startPayTime= startPayTime+":00";
            }
            var endPayTime = $("#txt_search_endPayTime").val();
            if(!!endPayTime){
                endPayTime= endPayTime+":00";
            }
            var startFinishTime = $("#txt_search_startFinishTime").val();
            if(!!startFinishTime){
                startFinishTime= startFinishTime+":00";
            }
            var endFinishTime = $("#txt_search_endFinishTime").val();
            if(!!endFinishTime){
                endFinishTime= endFinishTime+":00";
            }
            var isHighGrossStr = "";
            $("#txt_search_isHighGross option:selected").each(function () {
                if (isHighGrossStr != "") {
                    isHighGrossStr += ",";
                }
                isHighGrossStr += ($(this).val());
            });
            return {
                //每页显示条数
                limit: params.limit,
                //起始页数
                offset: params.offset,
                //排序字段
                property: (params.sort && params.sort !== '') ? ToolUtil.underscoreName(params.sort) : '',
                //排序方式
                direction: params.order,
                //查询条件（将对象属性封装成查询实体对象）
                "orderNo": $("#txt_search_orderNo").val(),
                "corporationNo": $("#corporationNo").val(),
                "corporationName": $("#corporationName").val(),
                "companyName": $("#companyName").val(),
                // "contactor": $("#txt_search_contactor").val(),
                // "mobile": $("#txt_search_mobile").val(),
                "merchantName": $("#txt_search_merchantName").val(),
                "status": $("#txt_search_status").val(),
                "startCreateTime": startCreateTime,
                "endCreateTime": endCreateTime,
                "startPayTime":startPayTime,
                "endPayTime":endPayTime,
                "startFinishTime":startFinishTime,
                "endFinishTime":endFinishTime,
                // "payType": $("#txt_search_pay_type").val(),
                // "payChannel": $("#txt_search_pay_channel").val(),
                // "startPayTime": $("#txt_search_startPayTime").val(),
                // "endPayTime": $("#txt_search_endPayTime").val(),
                "provinceCode": $("#txt_search_branch_code").val(),
                "businessTypeSearch": $("#business_type").val(),
                "firstOrderFlag": $("#txt_search_first_order").val(),
                "orderType": $("#txt_search_order_type").val(),
                "statusList": $("#txt_search_status_list").val(),
                "logisticsTrackFail": $("#txt_search_trackFail_list").val(),
                "logisticsTrackIllegal": $("#txt_search_trackIllegal_list").val(),
                "provId": $("#provId").val(),
                "payType": $("#txt_search_payType").val(),
                "payChannel": $("#txt_search_payChannel").val(),
                "evidenceVerifyStatus": $("#txt_search_evidenceVerifyStatus").val(),
                "isVirtualSupplier": $("#txt_search_isVirtualSupplier").val(),
                "isFbp": $("#txt_search_isFbp").val(),
                "isRandom": $("#txt_search_isRandom").val(),
                "isThirdCompany": $("#txt_search_isThirdCompany").val(),
                "isHighGrossStr" : isHighGrossStr,
                "exceptionFlag": $("#txt_search_exception").val(),
                "nextDayDelivery": $("#txt_search_arriveNextDay").val() || null,
                // "urgencyDegree": $("#txt_search_urgency_degree").val()
            };
        };
        return applyPendingTableObj;
    },
    buttonInit: function () {
        var $table = $('#tb_pendingList');
        var oInit = new Object();
        oInit.Init = function () {

            function getSelectedRow() {
                var index = $table.find('tr.success').data('index');
                return $table.bootstrapTable('getData')[index];
            }

            //初始化页面上面的按钮事件
            //条件查询事件
            $("#btn_query").click(function () {
                if($table.bootstrapTable('getOptions').totalRows){
                    $table.bootstrapTable('selectPage',1);
                }else{
                    $table.bootstrapTable('refresh');
                }
            });
            //点击查看
            $("#btn_view_timeout").click(function () {
                var text = $("#btn_view_timeout").text();
                //订单状态集合：待审核、出库中
                var statusList = JSON.stringify([1,7])
                if (text == '点击查看') {
                    $("#btn_view_timeout").text("取消查看")
                    $("#txt_search_status_list").val(statusList)
                } else {
                    $("#btn_view_timeout").text("点击查看")
                    $("#txt_search_status_list").val('')
                }
                $("#btn_view_trackFail").text("点击查看")
                $("#txt_search_trackFail_list").val('false')
                $("#btn_view_trackIllegal").text("点击查看");
                $("#txt_search_trackIllegal_list").val('false');
                if ($table.bootstrapTable('getOptions').totalRows) {
                    $table.bootstrapTable('selectPage', 1);
                } else {
                    $table.bootstrapTable('refresh');
                }

            });
            //点击查看
            $("#btn_view_trackFail").click(function () {
                var text = $("#btn_view_trackFail").text();
                //订单状态集合：待审核、出库中
                if (text == '点击查看') {
                    $("#btn_view_trackFail").text("取消查看")
                    $("#txt_search_trackFail_list").val('true')
                } else {
                    $("#btn_view_trackFail").text("点击查看")
                    $("#txt_search_trackFail_list").val('false')
                }
                //其他按钮重置
                $("#btn_view_timeout").text("点击查看");
                $("#txt_search_status_list").val('');
                $("#btn_view_trackIllegal").text("点击查看");
                $("#txt_search_trackIllegal_list").val('false');
                if ($table.bootstrapTable('getOptions').totalRows) {
                    $table.bootstrapTable('selectPage', 1);
                } else {
                    $table.bootstrapTable('refresh');
                }

            });
            //点击查看
            $("#btn_view_trackIllegal").click(function () {
                var text = $("#btn_view_trackIllegal").text();
                //订单状态集合：待审核、出库中
                if (text == '点击查看') {
                    $("#btn_view_trackIllegal").text("取消查看")
                    $("#txt_search_trackIllegal_list").val('true')
                } else {
                    $("#btn_view_trackIllegal").text("点击查看")
                    $("#txt_search_trackIllegal_list").val('false')
                }
                //其他按钮重置
                $("#btn_view_timeout").text("点击查看");
                $("#txt_search_status_list").val('');
                $("#btn_view_trackFail").text("点击查看")
                $("#txt_search_trackFail_list").val('false')
                if ($table.bootstrapTable('getOptions').totalRows) {
                    $table.bootstrapTable('selectPage', 1);
                } else {
                    $table.bootstrapTable('refresh');
                }

            });
            //条件清空事件
            $("#btn_clear").click(function () {
                $('#txt_search_orderNo').val('');
                $('#corporationNo').val('');
                $('#corporationName').val('');
                $('#companyName').val('');
                $('#txt_search_merchantName').val('');
                $('#txt_search_status').val('');
                $('#txt_search_startCreateTime').val('');
                $('#txt_search_endCreateTime').val('');
                $('#txt_search_startPayTime').val('');
                $('#txt_search_endPayTime').val('');
                $('#txt_search_startFinishTime').val('');
                $('#txt_search_endFinishTime').val('');
                $('#txt_search_branch_code').val('');
                $('#business_type').val('');
                $('#txt_search_first_order').val('');
                $('#txt_search_order_type').val('');
                $('#provId').val('');
                $('#txt_search_payType').val('');
                $('#txt_search_payChannel').val('');
                $('#txt_search_evidenceVerifyStatus').val('');
                $('#txt_search_isVirtualSupplier').val('');
                $('#txt_search_isFbp').val('');
                $('#txt_search_isRandom').val('');
                $('#txt_search_arriveNextDay').val('');
                $("#txt_search_isHighGross").selectpicker('val', ['noneSelectedText']);
                $("#txt_search_exception").selectpicker('val', ['noneSelectedText']);
                // $table.bootstrapTable('refresh');
                $table.bootstrapTable('selectPage',1);
            });


            $("#btn_confirm_money").on("click", function () {
                var row = getSelectedRow();
                console.info(row);
                var orderNo = row.orderNo;
                if (row && orderNo) {

                    LayTool.open({
                        type: 1
                        , title: "温馨提示"
                        , area: '300px;'
                        , shade: 0.8
                        , id: 'LAY_layuipro' //设定一个id，防止重复弹出
                        , resize: false
                        , btn: ['确认', '取消']
                        , btnAlign: 'c'
                        , moveType: 1 //拖拽模式，0或者1
                        , content: '<div> 订单编号:  ' + orderNo + '</div> <div>确认收款后，订单状态将变更为“待审核”，确认之前请核对该订单是否已经收到用户打款，并确定金额是否正确</div>'
                        , success: function (layero) {
                            var btn = layero.find('.layui-layer-btn');
                            btn.find('.layui-layer-btn0').on("click", function () {
                                $.post("/order/orderManagement/order/status",{"orderNo": orderNo,"status": 1,"orgId":row.orgId,"payType":3 },function (result) {
                                    if(result.code == 0){
                                        LayTool.success("修改成功");
                                    } else {
                                        LayTool.alert(result.message);
                                    }
                                    $table.bootstrapTable('refresh');
                                })

                            });
                        }
                    });

                    /*   LayTool.open({
                           title: "温馨提示",
                           area: ['300px', '300px'],
                           content: [
                               '/order/orderManagement/detail/' + row.orderNo, 'yes'
                           ]
                       });*/
                } else {
                    LayTool.alert("请选择一条记录");
                }

            });


            $("#btn_view").click(function () {
                var row = getSelectedRow();
                if (row) {
                    LayTool.open({
                        title: "订单详情",
                        area: ['1200px', '600px'],
                        content: [
                            '/order/orderManagement/detail/' + row.orderNo, 'yes'
                        ]
                    });
                } else {
                    LayTool.alert("请选择一条记录");
                }
            });

            $("#btn_add").click(function () {
                LayTool.open({
                    title: "新增连锁订单",
                    area: ['1200px', '600px'],
                    content: [
                        basePath + 'plannedOrderManagement/order/add', 'yes'
                    ],
                    end: function () {
                        $table.bootstrapTable('refresh');
                    }
                });
            });

            $("#btn_update").click(function () {
                var row = getSelectedRow();
                if (row) {
                    LayTool.open({
                        title: "修改订单",
                        area: ['1200px', '600px'],
                        content: [
                            basePath + 'plannedOrderManagement/update?id=' + row.id, 'yes'
                        ],
                        end: function () {
                            $table.bootstrapTable('refresh');
                        }
                    });
                } else {
                    LayTool.alert("请选择一条记录");
                }
            });

            $("#btn_export").click(function () {
                /*var startCreateTime = $("#txt_search_startCreateTime").val();
                var endCreateTime = $("#txt_search_endCreateTime").val();
                if (startCreateTime) {
                    var dateDiff = ToolUtil.differentDays(startCreateTime, endCreateTime, 1);
                    if (dateDiff > 31) {
                        LayTool.alert("可能造成服务器资源紧张,筛选时间段受限31天");
                        return false;
                    }
                } else {
                    LayTool.alert("请选择开始导出时间");
                    return false;
                }*/
                var s = this;
                LayTool.confirm("确认是否进行订单数据导出:", {icon: 1, title: '提示'}
                    , function (index) {
                    $.ajax({
                        type: "GET",
                        data: {"orderNo":$("#txt_search_orderNo").val(),
                            "corporationNo":$("#corporationNo").val(),
                            "corporationName":$("#corporationName").val(),
                            "companyName": $("#companyName").val(),
                            "status":$("#txt_search_status").val(),
                            "startCreateTime":$("#txt_search_startCreateTime").val(),
                            "endCreateTime":$("#txt_search_endCreateTime").val(),
                            "startPayTime":$("#txt_search_startPayTime").val(),
                            "endPayTime":$("#txt_search_endPayTime").val(),
                            "startFinishTime":$("#txt_search_startFinishTime").val(),
                            "endFinishTime":$("#txt_search_endFinishTime").val(),
                            "merchantName":$("#txt_search_merchantName").val(),
                            "provinceCode":$("#txt_search_branch_code").val(),
                            "businessTypeSearch":$("#business_type").val(),
                            "statusList":$("#txt_search_status_list").val(),
                            "logisticsTrackFail":$("#txt_search_trackFail_list").val(),
                            "logisticsTrackIllegal":$("#txt_search_trackIllegal_list").val(),
                            "firstOrderFlag":$("#txt_search_first_order").val(),
                            "isVirtualSupplier": $("#txt_search_isVirtualSupplier").val(),
                            "isFbp": $("#txt_search_isFbp").val(),
                            "isRandom": $("#txt_search_isRandom").val(),
                            "isThirdCompany": $("#txt_search_isThirdCompany").val(),
                            "provId":$("#provId").val(),
                            "exceptionFlag": $("#txt_search_exception").val(),
                            "nextDayDelivery": $("#txt_search_arriveNextDay").val() || null,
                        },
                        url: '/orderExport/exportOrderDetails',
                        success:function (res){
                            if(res.code == 0){
                                var p = LayTool.confirm("文件生成中，请到文件下载中心页面进行查看和下载", {icon: 3, title: '提示',btn: ['文件下载中心', '取消']}
                                    , function (index) {
                                        $(s).addtab("文件下载中心", "/webPop/#/downloadList", 1);
                                        LayTool.close(p);
                                    });
                            } else {
                                layer.msg(data.errorMsg);
                            }
                        }
                    });
                        /*window.location.href = '/order/orderManagement/exportData?orderNo='
                            + $("#txt_search_orderNo").val()
                            // + "&contactor=" + $("#txt_search_contactor").val()
                            // + "&mobile=" + $("#txt_search_mobile").val()
                            + "&corporationNo=" + $("#corporationNo").val()
                            + "&corporationName=" + $("#corporationName").val()
                            + "&status=" + $("#txt_search_status").val()
                            + "&startCreateTime=" + $("#txt_search_startCreateTime").val()
                            + "&endCreateTime=" + $("#txt_search_endCreateTime").val()
                            + "&merchantName=" + $("#txt_search_merchantName").val()
                        // + "&startPayTime=" + $("#txt_search_startPayTime").val()
                        // + "&endPayTime=" + $("#txt_search_endPayTime").val()
                        // + "&payType=" + $("#txt_search_pay_type").val()
                            + "&provinceCode=" + $("#txt_search_branch_code").val()
                            + "&firstOrderFlag=" + $("#txt_search_first_order").val()
                            + "&orderType=" + $("#txt_search_order_type").val()
                            + "&statusList=" + $("#txt_search_status_list").val();
                        top.layer.close(index);
                        top.layer.msg("订单正在导出中,请耐心等待(*^__^*)");*/
                    });
            });

            $("#btn_updateRemark").click(function () {
                var row = getSelectedRow();
                if (row) {
                    LayTool.open({
                        area: ['520px', '280px'],
                        content: [
                            basePath + 'orderManagement/submitCustomerServiceDialog/' + row.id, 'yes'
                        ]
                    });
                } else {
                    LayTool.alert("请选择一条记录");
                }
            });

            $("#btn_cancel").click(function () {
                var row = getSelectedRow();
                if (row) {
                    LayTool.confirm("确认是否取消,订单编号:" + row.orderNo, {icon: 3, title: '提示'}
                        , function (index) {
                            $.ajax({
                                type: "POST",
                                url: basePath + "orderManagement/cancel",
                                data: {
                                    "orderId": row.id
                                },
                                dataType: "json",
                                async: false,
                                success: function (data) {
                                    if (data.status === "success") {
                                        layer.msg("订单编号:" + row.orderNo + "，取消成功");
                                        $table.bootstrapTable('refresh');
                                    } else if (data.status === "failure") {
                                        layer.msg(data.errorMsg);
                                    } else {
                                        layer.msg("网络异常！");
                                    }
                                }
                            });
                            top.layer.close(index);
                        });
                } else {
                    LayTool.alert("请选择一条记录");
                }
            });
            //批量更新商品信息
            $("#btn_batch_over").click(function () {
                LayTool.open({
                    title: "批量配送完成",
                    type: 2,
                    area: ['500px', '450px'],
                    content: [
                        '/order/batchOverOrderView', 'yes'
                    ],
                    end: function () {
                        location.reload();
                    }
                })
            });
            $.ajax({
                type: "GET",
                url: "/businessProvinceList",
                dataType : "json",
                success:function (res) {
                    var options = '<option value="" selected="selected">全部</option>';
                    if(res.code == 0 && res.data != null && res.data.length > 0){
                        var data = res.data;
                        for (var i = 0; i < data.length; i++) {
                            options += '<option value='+data[i].provId+'>' + data[i].prov +'</option>';
                        }
                    }
                    $("#provId").html(options);
                }
            });

            //----------------------------------------初始化按钮事件完毕------------------------------------------------
        };
        return oInit;
    }
});

var utils = new Utils();
//launch
$(document).ready(function () {
    $(function () {
        $(".tooltip-options a").tooltip({html: true});
    });
    // var defaultDate = utils.GetFirstDayOfCurrentMonth();
    var defaultDate = utils.GetFirstDayOfDefaultMonth();
    $('#txt_search_startCreateTime').datetimepicker({
        format: 'yyyy-mm-dd hh:ii',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        endDate: new Date(),
        todayBtn: true,
        minView: 0
    }).val(defaultDate.substring(0,defaultDate.length-3));

    $('#txt_search_endCreateTime').datetimepicker({
        format: 'yyyy-mm-dd hh:ii',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        endDate: new Date(),
        todayBtn: true,
        minView: 0
    });

    $('#txt_search_startPayTime').datetimepicker({
        format: 'yyyy-mm-dd hh:ii',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        endDate: new Date(),
        todayBtn: true,
        minView: 0
    });

    $('#txt_search_endPayTime').datetimepicker({
        format: 'yyyy-mm-dd hh:ii',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        endDate: new Date(),
        todayBtn: true,
        minView: 0
    });
    $('#txt_search_startFinishTime').datetimepicker({
        format: 'yyyy-mm-dd hh:ii',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        endDate: new Date(),
        todayBtn: true,
        minView: 0
    });

    $('#txt_search_endFinishTime').datetimepicker({
        format: 'yyyy-mm-dd hh:ii',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        endDate: new Date(),
        todayBtn: true,
        minView: 0
    });

    $.ajax({
        type: "GET",
        async: false,
        url: "/getButtons",
        data: "menuUrl=/order/index",
        success:function (res) {
            if(res.code == 0 && res.data != null){
                btnArr = res.data;
                if(jQuery.inArray("查看详情", btnArr) == -1){
                    $("#btn_view").remove();
                }
                if(jQuery.inArray("导出", btnArr) == -1){
                    $("#btn_export").remove();
                }
                if(jQuery.inArray("确认收款", btnArr) == -1){
                    $("#btn_confirm_money").remove();
                }
                if(jQuery.inArray("批量配送完成", btnArr) == -1){
                    $("#btn_batch_over").remove();
                }
            }
        }
    });

    //1.初始化项目大类列表
    new applyPendingList.tableInit().Init();

    //2.初始化操作按钮
    new applyPendingList.buttonInit().Init();

    queryTimeoutOrderCount();
    queryLogisticsTrackCount();
    setInterval("queryTimeoutOrderCount()",600000);
    setInterval("queryLogisticsTrackCount()",600000);

    $(".selectpicker").selectpicker({

        noneSelectedText : '请选择'

    });
});
