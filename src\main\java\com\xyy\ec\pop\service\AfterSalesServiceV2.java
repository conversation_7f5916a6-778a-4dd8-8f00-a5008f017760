package com.xyy.ec.pop.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesSellerParamDto;
import com.xyy.ec.pop.vo.afterSales.AfterSaleQueryParamVo;
import com.xyy.ec.pop.vo.afterSales.AfterSalesLogisticsVo;
import com.xyy.ec.pop.vo.afterSales.AfterSalesVo;

import java.util.Map;

/**
 * 售后管理Service
 */
public interface AfterSalesServiceV2 {

    PageInfo<AfterSalesVo> queryPage(AfterSaleQueryParamVo param);

    Map<String,Object> queryAfterSalesDetail(String afterSalesNo);

    Map<String,Integer> queryAfterSalesStatusCount(AfterSaleQueryParamVo param);

    void saveSellerRemark(AfterSalesSellerParamDto param);

    void saveSellerOperate(AfterSalesSellerParamDto param);

    AfterSalesLogisticsVo queryLogistics(String afterSalesNo);
}
