package com.xyy.ec.pop.marketing.service.impl;

import com.google.common.collect.Lists;
import com.xyy.ec.pop.marketing.remote.MarketingTopicRemoteService;
import com.xyy.ec.pop.marketing.service.MarketingTopicService;
import com.xyy.ec.pop.vo.KeyValueVO;
import com.xyy.ms.promotion.business.common.constants.MarketingTopicEnum;
import com.xyy.ms.promotion.business.dto.marketingactivity.MarketingTopicDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class MarketingTopicServiceImpl implements MarketingTopicService {

    @Autowired
    private MarketingTopicRemoteService marketingTopicRemoteService;

    @Override
    public List<KeyValueVO> listTopicVOSByType(Integer type) {
        List<MarketingTopicDTO> marketingTopicDTOS = marketingTopicRemoteService.listTopicsByType(
                MarketingTopicEnum.TopicTypeEnum.GROUP_BUYING.getType());
        return this.createsForTopic(marketingTopicDTOS);
    }

    private List<KeyValueVO> createsForTopic(List<MarketingTopicDTO> marketingTopicDTOS) {
        if (CollectionUtils.isEmpty(marketingTopicDTOS)) {
            return Lists.newArrayList();
        }
        return marketingTopicDTOS.stream().map(this::createForTopic).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private KeyValueVO createForTopic(MarketingTopicDTO marketingTopicDTO) {
        if (Objects.isNull(marketingTopicDTO) || StringUtils.isEmpty(marketingTopicDTO.getName())
                || StringUtils.isEmpty(marketingTopicDTO.getTitle())) {
            return null;
        }
        return KeyValueVO.builder().key(marketingTopicDTO.getName()).value(marketingTopicDTO.getTitle()).build();
    }

}
