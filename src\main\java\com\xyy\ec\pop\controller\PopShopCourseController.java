package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.server.api.shopcourse.api.PopShopCourseApi;
import com.xyy.ec.pop.server.api.shopcourse.dto.PopShopCourseDto;
import com.xyy.ec.pop.server.api.shopcourse.dto.PopShopCourseParam;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Objects;

@Controller
@RequestMapping("/shopCourse")
@Slf4j
public class PopShopCourseController extends BaseController {

    @Reference
    private PopShopCourseApi popShopCourseApi;



    @PostMapping("/query")
    @ResponseBody
    public ResponseVo<Object> query(@RequestBody PopShopCourseParam queryParamDto){
        try {
            if (Objects.isNull(queryParamDto) || Objects.isNull(queryParamDto.getPageSize()) || Objects.isNull(queryParamDto.getPageNum()) ){
                return ResponseVo.errRest("分页不能为空");
            }
            PageInfo<PopShopCourseDto> query = popShopCourseApi.query(queryParamDto);
            return ResponseVo.successResult(query);
        } catch (Exception e) {
            log.error("PopShopCourseController selectPageList error",e);
            return ResponseVo.errRest("系统错误");
        }
    }

    @PostMapping("/getDetail")
    @ResponseBody
    public ResponseVo<Object> getDetail(@RequestBody PopShopCourseParam queryParamDto){
        try {
            PopShopCourseDto detail = popShopCourseApi.getDetail(queryParamDto);
            return ResponseVo.successResult(detail);
        } catch (Exception e) {
            log.error("PopShopCourseController getDetail error",e);
            return ResponseVo.errRest("系统错误");
        }
    }


    @PostMapping("/save")
    @ResponseBody
    public ResponseVo<Object> save(@RequestBody PopShopCourseParam param){
        try {
            log.info("PopShopCourseController save request ,queryParamDto :{}", JSONObject.toJSONString(param));
            SysUser user = getUser();
            if (user == null){
                return ResponseVo.errRest("请登录");
            }
            param.setUser(user.getUsername());
            String save = popShopCourseApi.save(param);
            if (StringUtils.isNotBlank(save)){
                return ResponseVo.errRest(save);
            }
            return ResponseVo.successResult("操作成功");
        } catch (Exception e) {
            log.error("PopShopCourseController save error",e);
            return ResponseVo.errRest("系统错误");
        }
    }


    @PostMapping("/delete")
    @ResponseBody
    public ResponseVo<Object> delete(@RequestBody PopShopCourseParam param){
        try {
            log.info("PopShopCourseController delete request ,param :{}", JSONObject.toJSONString(param));
            SysUser user = getUser();
            if (user == null){
                return ResponseVo.errRest("请登录");
            }
            String save = popShopCourseApi.delete(param.getId(), user.getUsername());
            if (StringUtils.isNotBlank(save)){
                return ResponseVo.errRest(save);
            }
            return ResponseVo.successResult("操作成功");
        } catch (Exception e) {
            log.error("PopShopCourseController delete error",e);
            return ResponseVo.errRest("系统错误");
        }
    }

    @PostMapping("/updateStatus")
    @ResponseBody
    public ResponseVo<Object> updateStatus(@RequestBody PopShopCourseParam param){
        try {
            log.info("PopShopCourseController updateStatus request ,param :{}", JSONObject.toJSONString(param));
            SysUser user = getUser();
            if (user == null){
                return ResponseVo.errRest("请登录");
            }
            String save = popShopCourseApi.updateStatus(param.getId(),param.getStatus(),user.getUsername());
            if (StringUtils.isNotBlank(save)){
                return ResponseVo.errRest(save);
            }
            return ResponseVo.successResult("操作成功");
        } catch (Exception e) {
            log.error("PopShopCourseController updateStatus error",e);
            return ResponseVo.errRest("系统错误");
        }
    }
}
