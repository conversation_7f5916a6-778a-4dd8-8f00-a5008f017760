package com.xyy.ec.pop.marketing.helpers;

import com.xyy.ec.marketing.shop.activity.dto.CouponRecallRecordDTO;
import com.xyy.ec.marketing.shop.activity.dto.ShopCouponDTO;
import com.xyy.ec.marketing.shop.activity.enums.MarketingShopCouponTypeEnum;
import com.xyy.ec.pop.marketing.vo.CouponRecallRecordVO;
import com.xyy.ec.pop.marketing.vo.CouponTemplateVO;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.Objects;

public class CouponRecallRecordHelper {


    public static CouponRecallRecordVO createCouponRecallRecordVO(CouponRecallRecordDTO couponRecallRecordDTO){
        if (couponRecallRecordDTO == null){
            return null;
        }
        String typeStr = Objects.equals(couponRecallRecordDTO.getTemplateType(), MarketingShopCouponTypeEnum.FULL_REDUCTION.getType()) ? "减{1}元" : "打{1}折";
        String discountStr = MessageFormat.format("满{0}元," + typeStr, couponRecallRecordDTO.getMinMoneyToEnable(), couponRecallRecordDTO.getDiscount());

       return CouponRecallRecordVO.builder()
                .id(couponRecallRecordDTO.getId())
                .couponTemplateId(couponRecallRecordDTO.getCouponTemplateId())
                .couponTemplateName(couponRecallRecordDTO.getCouponTemplateName())
                .recallSuccessNum(couponRecallRecordDTO.getRecallSuccessNum())
                .shopCode(couponRecallRecordDTO.getShopCode())
                .shopName(couponRecallRecordDTO.getShopName())
                .creator(couponRecallRecordDTO.getCreator())
                .ctime(couponRecallRecordDTO.getCtime())
                .remark(couponRecallRecordDTO.getRemark())
                .realName(couponRecallRecordDTO.getRealName())
                .templateDiscountStr(discountStr)
               .remark(couponRecallRecordDTO.getRemark())
               .build();
    }

    public static CouponTemplateVO createCouponTemplateVO(ShopCouponDTO shopCouponDTO){
        if (shopCouponDTO == null){
            return null;
        }
        BigDecimal discount = Objects.equals(shopCouponDTO.getType(), MarketingShopCouponTypeEnum.FULL_REDUCTION.getType()) ? shopCouponDTO.getMoneyInVoucher() : shopCouponDTO.getDiscount();
        String typeStr = Objects.equals(shopCouponDTO.getType(), MarketingShopCouponTypeEnum.FULL_REDUCTION.getType()) ? "减{1}元" : "打{1}折";
        String discountStr = MessageFormat.format("满{0}元," + typeStr, shopCouponDTO.getMinMoneyToEnable(), discount);
        return CouponTemplateVO.builder()
                .couponTemplateId(shopCouponDTO.getId())
                .couponTemplateName(shopCouponDTO.getName())
                .shopCode(shopCouponDTO.getShopCode())
                .shopName(shopCouponDTO.getShopName())
                .templateDiscountStr(discountStr)
                .build();
    }
}
