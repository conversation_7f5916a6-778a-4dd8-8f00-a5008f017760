package com.xyy.ec.pop.adapter.helper;

import com.xyy.ec.order.business.dto.OrderDeliveryBusinessDto;
import com.xyy.ec.order.business.dto.OrderDeliveryLogisticsDetailBusinessDto;
import com.xyy.ec.pop.adapter.dto.order.OrderDeliveryAdapterDto;
import com.xyy.ec.pop.adapter.dto.order.OrderLogisticsDetailAdapterDto;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/22 14:05
* @table
*/
public class YbmOrderAdapterHelper {


    public static List<OrderDeliveryAdapterDto> convertOrderDeliveryAdapterDto(List<OrderDeliveryBusinessDto> orderDeliveryMessageList) {
        List<OrderDeliveryAdapterDto> orderDeliveryAdapterDtoList = new ArrayList<>();
        if(CollectionUtils.isEmpty(orderDeliveryMessageList)){
            return orderDeliveryAdapterDtoList;
        }
        orderDeliveryMessageList.forEach(orderDeliveryBusinessDto -> {
            OrderDeliveryAdapterDto orderDeliveryAdapterDto = new OrderDeliveryAdapterDto();
            orderDeliveryAdapterDto.setOrderNo(orderDeliveryBusinessDto.getOrderNo());
            orderDeliveryAdapterDto.setWaybillNo(orderDeliveryBusinessDto.getWaybillNo());
            orderDeliveryAdapterDto.setCurrentLogisticsStateName(orderDeliveryBusinessDto.getCurrentLogisticsStateName());

            List<OrderDeliveryLogisticsDetailBusinessDto> orderDeliveryLogisticsDetailList = orderDeliveryBusinessDto.getOrderDeliveryLogisticsDetailList();

            List<OrderLogisticsDetailAdapterDto> orderLogisticsDetailAdapterDtos = getOrderLogisticsDetailAdapterDtos(orderDeliveryLogisticsDetailList);
            orderDeliveryAdapterDto.setOrderLogisticsDetailAdapterDtos(orderLogisticsDetailAdapterDtos);
            orderDeliveryAdapterDtoList.add(orderDeliveryAdapterDto);
        });
        return orderDeliveryAdapterDtoList;
    }

    private static List<OrderLogisticsDetailAdapterDto> getOrderLogisticsDetailAdapterDtos(List<OrderDeliveryLogisticsDetailBusinessDto> orderDeliveryLogisticsDetailList) {
        List<OrderLogisticsDetailAdapterDto> orderLogisticsDetailAdapterDtos = new ArrayList<>();
        if(CollectionUtils.isEmpty(orderDeliveryLogisticsDetailList)){
            return orderLogisticsDetailAdapterDtos;
        }
        orderDeliveryLogisticsDetailList.forEach(orderDeliveryLogisticsDetailBusinessDto -> {
            OrderLogisticsDetailAdapterDto orderLogisticsDetailAdapterDto = new OrderLogisticsDetailAdapterDto();
            orderLogisticsDetailAdapterDto.setWaybillNo(orderDeliveryLogisticsDetailBusinessDto.getWaybillNo());
            orderLogisticsDetailAdapterDto.setDescription(orderDeliveryLogisticsDetailBusinessDto.getDescription());
            orderLogisticsDetailAdapterDto.setDeliveryTime(orderDeliveryLogisticsDetailBusinessDto.getDeliveryTime());
            orderLogisticsDetailAdapterDto.setCreateDate(orderDeliveryLogisticsDetailBusinessDto.getCreateDate());
            orderLogisticsDetailAdapterDtos.add(orderLogisticsDetailAdapterDto);
        });
        return orderLogisticsDetailAdapterDtos;
    }
}
