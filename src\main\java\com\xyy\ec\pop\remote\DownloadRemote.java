package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.server.api.export.api.DownloadApi;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileDto;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileQuery;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Objects;

/**
 * @Description 下载文件远程调用
 * <AUTHOR>
 * @Date 2021/7/23
 */
@Component
@Slf4j
public class DownloadRemote {
    @Reference
    private DownloadApi downloadApi;

    public PageInfo<DownloadFileDto> queryDownloadListForAdmin(DownloadFileQuery query) {
        try {
            log.info("DownloadRemote.queryDownloadListForAdmin#query:{}", JSON.toJSONString(query));
            ApiRPCResult<PageInfo<DownloadFileDto>> result = downloadApi.queryDownloadListForAdmin(query);

            log.info("DownloadRemote.queryDownloadListForAdmin#query:{} return {}", JSON.toJSONString(query), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("DownloadRemote.queryDownloadListForAdmin#query:{} 异常", JSON.toJSONString(query), e);
        }
        return PageInfo.of(new ArrayList<>(0));
    }


    public Boolean saveTask(DownloadFileContent content) throws ServiceRuntimeException {
        try {
            content.setSysType(DownloadTaskSysTypeEnum.ADMIN);
            content.setOrgId("");
            log.info("DownloadRemote.saveTask#content:{}", JSON.toJSONString(content));
            ApiRPCResult<Boolean> result = downloadApi.saveTask(content);
            if (result.isFail() && (Objects.equals(ApiResultCodeEum.PARAMETER_ERROR.getCode(),result.getCode())
                    || Objects.equals(ApiResultCodeEum.AUTHORITY_ERROR.getCode(),result.getCode()))){
                throw new ServiceRuntimeException(result.getErrMsg());
            }
            log.info("DownloadRemote.saveTask#content:{} return {}", JSON.toJSONString(content), JSON.toJSONString(result));
            return result.isSuccess() && result.getData();
        } catch (ServiceRuntimeException e){
            throw new ServiceRuntimeException(e.getMessage());
        }catch (Exception e) {
            log.error("DownloadRemote.saveTask#content:{} 异常", JSON.toJSONString(content), e);
            return false;
        }
    }

    public Boolean saveTask(Object query, String operator, DownloadTaskBusinessTypeEnum typeEnum) throws ServiceRuntimeException {
        DownloadFileContent content = DownloadFileContent.builder()
                .query(query)
                .operator(operator)
                .sysType(DownloadTaskSysTypeEnum.ADMIN)
                .businessType(typeEnum)
                .build();
        return saveTask(content);
    }
}
