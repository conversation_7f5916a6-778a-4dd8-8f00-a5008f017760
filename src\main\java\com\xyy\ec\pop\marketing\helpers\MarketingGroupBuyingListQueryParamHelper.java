package com.xyy.ec.pop.marketing.helpers;

import com.xyy.ec.marketing.elephant.params.MarketingGroupBuyingListQueryParam;
import com.xyy.ec.pop.marketing.param.GroupBuyingListQueryParam;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * {@link MarketingGroupBuyingListQueryParam} 帮助类
 *
 * <AUTHOR>
 */
public class MarketingGroupBuyingListQueryParamHelper {

    public static MarketingGroupBuyingListQueryParam create(GroupBuyingListQueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            return null;
        }
        MarketingGroupBuyingListQueryParam marketCustomerGroupAdminQueryParam = MarketingGroupBuyingListQueryParam.builder()
                .orgId(queryParam.getOrgId())
                .orgName(queryParam.getOrgName())
                .barcode(queryParam.getBarcode())
                .productCode(queryParam.getProductCode())
                .productName(queryParam.getProductName())
                .theme(queryParam.getTheme())
                .actStartTime(queryParam.getActStartTime())
                .actEndTime(queryParam.getActEndTime())
                .sourceType(queryParam.getSourceType())
                .status(queryParam.getStatus())
                .isPlatformSubsidy(queryParam.getIsPlatformSubsidy())
                .isVirtualShop(queryParam.getIsVirtualShop())
                .stepPriceStatus(queryParam.getStepPriceStatus())
                .isOrder(queryParam.getIsOrder())
                .pageNum(queryParam.getPageNum())
                .pageSize(queryParam.getPageSize())
                .build();
        if (StringUtils.isNotEmpty(queryParam.getActIdOrReportId())) {
            marketCustomerGroupAdminQueryParam.setActIdOrReportId(Long.parseLong(queryParam.getActIdOrReportId()));
        }
        if (StringUtils.isNotEmpty(queryParam.getCsuId())) {
            marketCustomerGroupAdminQueryParam.setCsuId(Long.parseLong(queryParam.getCsuId()));
        }
        if (StringUtils.isNotEmpty(queryParam.getTopics())) {
            List<String> topics = Arrays.stream(queryParam.getTopics().split(","))
                    .filter(StringUtils::isNotEmpty)
                    .map(String::trim)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            marketCustomerGroupAdminQueryParam.setTopics(topics);
        }
        return marketCustomerGroupAdminQueryParam;
    }

}
