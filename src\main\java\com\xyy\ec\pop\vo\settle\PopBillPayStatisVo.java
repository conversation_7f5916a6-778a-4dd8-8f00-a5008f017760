package com.xyy.ec.pop.vo.settle;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**入账单 统计佣金金额，应结算金额
* <AUTHOR>
* @date  2020/12/9 10:49
* @table
*/
@Data
public class PopBillPayStatisVo implements Serializable {

    /**
     * 非月结佣金合计
     */
    private BigDecimal hireMoneyTotal = BigDecimal.ZERO;
    /**
     * 应结算金额合计
     */
    private BigDecimal statementTotalMoneyTotal = BigDecimal.ZERO;

    /**
     * 月结佣金金额合计
     */
    private BigDecimal payableCommissionTotal = BigDecimal.ZERO;
    /**
     * 应缴纳佣金金额合计
     */
    private BigDecimal deductedCommissionTotal = BigDecimal.ZERO;
}
