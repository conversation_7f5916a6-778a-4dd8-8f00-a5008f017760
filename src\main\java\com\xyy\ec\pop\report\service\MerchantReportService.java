package com.xyy.ec.pop.report.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.report.Vo.*;
import com.xyy.ec.pop.report.param.MerchantReportBatchAuditImportParam;
import com.xyy.ec.pop.vo.BatchUpdateResultVo;
import com.xyy.ec.pop.vo.ResponseVo;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface MerchantReportService {
    ResponseVo<PageInfo<MerchantRecordsVo>> pageReportRecords(ReportRecordQueryVo reportRecordQueryVo);

    ResponseVo<PageInfo<MerchantDetailsVo>> pageReportDetails(ReportDetailQueryVo reportDetailQueryVo);

    ResponseVo<String> audit(AuditVo auditVo, String operator);

    ResponseVo<BatchUpdateResultVo> batchAudit(List<MerchantReportBatchAuditImportParam> params, String operator, MultipartFile file);

    void exportReportRecord(ReportRecordQueryVo reportRecordQueryVo, SysUser user);

    ResponseVo<PageInfo<ReportLogVo>> queryReportLogs(Long merchantId, int pageNum, int pageSize);
}
