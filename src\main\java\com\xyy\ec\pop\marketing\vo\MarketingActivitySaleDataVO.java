package com.xyy.ec.pop.marketing.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarketingActivitySaleDataVO implements Serializable {

    /**
     * SKU编码：取csuid。
     */
    private Long csuId;

    /**
     * 商品编码：取barcode，对应POP的是商品编码，对应自营的是商品原编码。
     */
    private String csuBarcode;

    /**
     * 商品ERP编码：POP取ERP编码，自营取神农编码；
     */
    private String csuErpCode;

    /**
     * 原商品编码：取复制品的spid对应的barcode，若非复制品则不展示该字段。
     */
    private String originalCsuBarcode;

    /**
     * 商品名称，取的展示名称
     */
    private String csuProductName;

    /**
     * 商品规格
     */
    private String csuSpec;

    /**
     * 商品厂家
     */
    private String csuManufacturer;

    /**
     * 商品原价（购买时的快照价格）
     */
    private BigDecimal csuOriginalPrice;

    /**
     * 购买单价
     */
    private BigDecimal buyUnitPrice;

    /**
     * 购买数量
     */
    private Long buyNum;

    /**
     * 已退数量
     */
    private Long refundNum;

    /**
     * 实收数量
     */
    private Long receiptNum;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 实付金额
     */
    private BigDecimal actualAmount;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 药店编号
     */
    private Long merchantId;

    /**
     * 药店名称
     */
    private String merchantName;

    /**
     * 省份
     */
    private Integer provinceCode;

    /**
     * 省份
     */
    private String provinceName;

    /**
     * 收货人
     */
    private String consignee;

    /**
     * 收货手机号
     */
    private String consigneeMobile;

    /**
     * 收货人详细地址
     */
    private String consigneeAddress;

    /**
     * 下单时间
     */
    private Date orderCreateTime;

    /**
     * 下单时间字符串，格式化字符串：yyyy-MM-DD HH:mm:ss
     */
    private String orderCreateTimeStr;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单状态名称
     */
    private String orderStatusName;

}
