package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.util.PoitlIOUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.dto.PopMerchantFundAuditQueryDto;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.redis.RedisConstants;
import com.xyy.ec.pop.redis.XyyJedisCluster;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.PopMerchantFundAuditApi;
import com.xyy.ec.pop.server.api.merchant.api.enums.FundPropertyEnum;
import com.xyy.ec.pop.server.api.merchant.api.seller.CorporationApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.merchant.dto.PopMerchantFundAuditDto;
import com.xyy.ec.pop.server.api.merchant.dto.PopMerchantFundFlowLogDto;
import com.xyy.ec.pop.server.api.merchant.param.MerchantFundAuditParam;
import com.xyy.ec.pop.server.api.merchant.param.MerchantFundAuditQueryParam;
import com.xyy.ec.pop.server.api.merchant.param.MerchantFundAuditReceiptParam;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.utils.NumberUtils;
import com.xyy.ec.pop.utils.WordDownloadUtils;
import com.xyy.ec.pop.vo.PopMerchantFundAuditVo;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.zhongtai.PopProvinceVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商户资金账户控制类
 */
@Slf4j
@Controller
@Api(tags = "商户资金账户控制类")
@RequestMapping("/merchant/fund")
public class MerchantFundController extends BaseController {

    @Reference
    private PopMerchantFundAuditApi popMerchantFundAuditApi;

    @Reference
    private CorporationAdminApi corporationAdminApi;

    @Reference
    private CorporationApi corporationApi;

    @Autowired
    private XyyJedisCluster xyyJedisCluster;

    @Value("${xyy.company.name}")
    private String xyyCompanyName;

    /**
     * 查询商户资金账户打款审核列表
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/queryPage")
    @ResponseBody
    public ResponseVo<PageInfo<PopMerchantFundAuditVo>> queryPage(@RequestBody PopMerchantFundAuditQueryDto queryDto) {
        try {
            log.info("MerchantFundController.queryPage#param:{}", JSON.toJSONString(queryDto));
            if (queryDto == null || queryDto.getPageNum() == null || queryDto.getPageNum() < 1 || queryDto.getPageSize() == null || queryDto.getPageSize() < 1) {
                return ResponseVo.errRest("请检查分页参数");
            }
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if(!CollectionUtils.isEmpty(provIds)){
                queryDto.setProvIds(provIds);
            }
            MerchantFundAuditQueryParam param = new MerchantFundAuditQueryParam();
            BeanUtils.copyProperties(queryDto, param);
            param.setOrgIdList(getOrgIdList(queryDto));
            param.setCreateStartTime(DateUtil.modifyStartTime(queryDto.getCreateStartTime()));
            param.setCreateEndTime(DateUtil.modifyEndTime(queryDto.getCreateEndTime()));
            param.setActualReceivedStartTime(DateUtil.modifyStartTime(queryDto.getActualReceivedStartTime()));
            param.setActualReceivedEndTime(DateUtil.modifyEndTime(queryDto.getActualReceivedEndTime()));
            log.info("popMerchantFundAuditApi.selectPageList # param:{} ", JSON.toJSONString(param));
            ApiRPCResult<PageInfo<PopMerchantFundAuditDto>> result = popMerchantFundAuditApi.selectPageList(param);
            if (result.isFail()) {
                return ResponseVo.errRest(result.getErrMsg());
            }
            PageInfo<PopMerchantFundAuditVo> pageInfoVo = new PageInfo<>();
            BeanUtils.copyProperties(result.getData(), pageInfoVo);
            pageInfoVo.setList(getFundAuditVoList(result.getData().getList()));
            return ResponseVo.successResult(pageInfoVo);
        } catch (Exception e) {
            log.error("MerchantFundController.queryPage#error. param:{}", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("查询商户资金账户打款审核列表失败");
        }
    }

    private List<PopMerchantFundAuditVo> getFundAuditVoList(List<PopMerchantFundAuditDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<String> orgIds = list.stream().map(PopMerchantFundAuditDto::getOrgId).collect(Collectors.toList());
        ApiRPCResult<List<CorporationDto>> result = corporationApi.queryByOrgIds(orgIds);
        Map<String, CorporationDto> corporationMap = result.getData().stream()
                .collect(Collectors.toMap(CorporationDto::getOrgId, Function.identity(), (o1, o2) -> o1));
        List<PopMerchantFundAuditVo> auditVoList = Lists.newArrayList();
        for (PopMerchantFundAuditDto auditDto : list) {
            PopMerchantFundAuditVo auditVo = new PopMerchantFundAuditVo();
            BeanUtils.copyProperties(auditDto, auditVo);
            CorporationDto corporationDto = corporationMap.get(auditDto.getOrgId());
            if (corporationDto != null) {
                auditVo.setMerchantName(corporationDto.getCompanyName());
                auditVo.setShopName(corporationDto.getName());
                auditVo.setProvName(corporationDto.getProv());
            }
            auditVoList.add(auditVo);
        }
        return auditVoList;
    }


    /**
     * 审核
     *
     * @param auditParam
     * @return
     */
    @PostMapping("/audit")
    @ResponseBody
    public ResponseVo<String> audit(@RequestBody MerchantFundAuditParam auditParam) {
        try {
            SysUser sysUser = getUser();
            auditParam.setAuditor(sysUser.getUsername());
            auditParam.setAuditorId(sysUser.getId());
            log.info("MerchantFundController.audit#auditParam:{}", JSON.toJSONString(auditParam));
            ApiRPCResult<String> result = popMerchantFundAuditApi.audit(auditParam);
            if (result.isSuccess() && StringUtils.isEmpty(result.getMsg())) {
                return ResponseVo.successResultNotData("审核成功");
            }
            return ResponseVo.errRest(result.getMsg());
        } catch (Exception e) {
            log.error("MerchantFundController.audit#error. param:{}", JSON.toJSONString(auditParam), e);
            return ResponseVo.errRest("商户资金账户打款审核失败");
        }
    }

    /**
     * 上传收据
     *
     * @param param
     * @return
     */
    @PostMapping("/uploadReceiptUrl")
    @ResponseBody
    public ResponseVo uploadReceiptUrl(@RequestBody MerchantFundAuditReceiptParam param) {
        try {
            log.info("MerchantFundController.uploadReceiptUrl#param:{}", JSON.toJSONString(param));
            ApiRPCResult<String> result = popMerchantFundAuditApi.uploadReceiptUrl(param.getId(), param.getReceiptUrl());
            if (result.isSuccess() && StringUtils.isEmpty(result.getMsg())) {
                return ResponseVo.successResultNotData("上传成功");
            }
            return ResponseVo.errRest(result.getMsg());
        } catch (Exception e) {
            log.error("MerchantFundController.uploadReceiptUrl#error. param:{}", JSON.toJSONString(param), e);
            return ResponseVo.errRest("上传收据失败");
        }
    }

    /**
     * 根据流水单号查看日志
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryLog")
    @ResponseBody
    public ResponseVo<List<PopMerchantFundFlowLogDto>> queryLog(@RequestBody PopMerchantFundFlowLogDto dto) {
        try {
            if (dto == null || StringUtils.isEmpty(dto.getFlowOrderNumber())) {
                return ResponseVo.errRest("请输入流水单号");
            }
            log.info("MerchantFundController.queryLog#flowOrderNumber:{}", dto.getFlowOrderNumber());
            ApiRPCResult<List<PopMerchantFundFlowLogDto>> result = popMerchantFundAuditApi.queryLog(dto.getFlowOrderNumber());
            return ResponseVo.successResult(result.getData());
        } catch (Exception e) {
            log.error("MerchantFundController.queryLog#error.flowOrderNumber:{}", dto.getFlowOrderNumber(), e);
            return ResponseVo.errRest("查询商户资金账户打款审核日志失败");
        }
    }

    /**
     * 根据商户信息获取orgId
     *
     * @param queryDto
     * @return
     */
    private List<String> getOrgIdList(PopMerchantFundAuditQueryDto queryDto) {
        if (StringUtils.isEmpty(queryDto.getMerchantName()) && StringUtils.isEmpty(queryDto.getMerchantNo())
                && StringUtils.isEmpty(queryDto.getName()) && StringUtils.isEmpty(queryDto.getPhone())) {
            return Lists.newArrayList();
        }
        CorporationDto corporationDto = new CorporationDto();
        BeanUtils.copyProperties(queryDto, corporationDto);
        corporationDto.setCompanyName(queryDto.getMerchantName());
        corporationDto.setOrgId(queryDto.getMerchantNo());
        corporationDto.setSearch(queryDto.getKeyword());
        ApiRPCResult<List<CorporationDto>> corporationResult = corporationApi.queryList(corporationDto);
        if (corporationResult.isSuccess() && !CollectionUtils.isEmpty(corporationResult.getData())) {
            return corporationResult.getData().stream().map(CorporationDto::getOrgId).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    protected List<Long> getProvIds(Long provId){
        if(Objects.nonNull(provId)){
            return Collections.singletonList(provId);
        }
        SysUser user = getUser();
        String redisKey = RedisConstants.XYY_POP_PROVINCE_KEYS + user.getOaId();
        String provinceStr = xyyJedisCluster.get(redisKey);
        if(org.springframework.util.StringUtils.isEmpty(provinceStr)){
            return Lists.newArrayList();
        }
        List<PopProvinceVo> list = JSON.parseArray(provinceStr, PopProvinceVo.class);
        return list.stream().map(PopProvinceVo::getProvId).collect(Collectors.toList());
    }

    /**
     * 下载空白收据
     */
    @GetMapping("/downloadCertificate")
    public void downloadCertificate(@RequestParam(value = "fundAuditId") Long fundAuditId, HttpServletResponse response) {
        if (fundAuditId == null) {
            return;
        }
        ApiRPCResult<PopMerchantFundAuditDto> apiRPCResult = popMerchantFundAuditApi.getById(fundAuditId);
        if (apiRPCResult == null || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())
                || !FundPropertyEnum.MARGIN.getCode().equals(apiRPCResult.getData().getFundPropertyStatus())
                || apiRPCResult.getData().getIsFirstCharge() != 1) {
            // 只有首次保证金充值才给收据
            return;
        }
        ApiRPCResult<CorporationDto> rpcResult = corporationApi.queryCorpBaseByOrgId(apiRPCResult.getData().getOrgId());
        if (rpcResult == null || rpcResult.isFail() || rpcResult.getData() == null) {
            return;
        }
        CorporationDto corporationDto = rpcResult.getData();
        PopMerchantFundAuditDto fundAuditDto = apiRPCResult.getData();
        try {
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + "收据凭证.docx" + "\"");
            Map<String, Object> map = new HashMap<>();
            map.put("payDate", DateUtil.date2String(fundAuditDto.getUpdateTime(), DateUtil.PATTERN_DATE));
            //有样式的文本
            map.put("bonyMoneyStr", NumberUtils.number2CNMontrayUnit(fundAuditDto.getAmount()));
            //超链接和锚点文本
            map.put("remarks", "POP保证金");
            map.put("companyName", corporationDto.getCompanyName());
            map.put("xyyName", xyyCompanyName);
            DecimalFormat decimalFormat = new DecimalFormat("##.##");
            map.put("bonyMoney", fundAuditDto.getAmount() != null ? decimalFormat.format(fundAuditDto.getAmount()) : 0);
            map.put("date", DateUtil.date2String(fundAuditDto.getUpdateTime(), DateUtil.PATTERN_DATE));
            XWPFTemplate template = WordDownloadUtils.downloadWord("https://oss-ec.ybm100.com/pop/shouju.docx", map);
            OutputStream out = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(out);
            try {
                template.write(bos);
                bos.flush();
                out.flush();
                PoitlIOUtils.closeQuietlyMulti(template, bos, out);
            } catch (IOException e) {
                log.error("MerchantFundController.downloadCertificate error fundAuditId :{}", fundAuditId, e);
            } finally {
                if (bos != null) {
                    bos.close();
                }
                if (out != null) {
                    out.close();
                }
            }
        } catch (Exception e) {
            log.error("MerchantFundController.downloadCertificate error fundAuditId :{}", fundAuditId, e);
        }
    }

}
