package com.xyy.ec.pop.utils;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * 取系统配置属性
 * 
 * 
 */
public final class ConfigUtils {
    private static String              DEFAULT_CONFIG_FILE = "/config.properties";
    private static final Logger        log                 = LoggerFactory.getLogger(ConfigUtils.class);
    private static Map<String, String> propertiesMap;

    static {
        try {
            initProperty();
        } catch (IOException e) {
            log.error("error at init properties", e);
        }
    }

    /**
     * 初始化配置文件(config.properties)
     * 
     * @throws IOException
     */
    private static void initProperty() throws IOException {
        if (propertiesMap != null) {
            return;
        }
        loadDefaultProperty();
    }

    private static void loadDefaultProperty() throws IOException {
        propertiesMap = new HashMap<String, String>();
        InputStream ins = null;
        Properties properties = new Properties();
        try {
            ins = ConfigUtils.class.getResourceAsStream(DEFAULT_CONFIG_FILE);
            if (ins == null) {
                return;
            }
            properties.load(ins);
        } finally {
            IOUtils.closeQuietly(ins);
        }
        Set<Entry<Object, Object>> entrySet = properties.entrySet();
        for (Entry<Object, Object> entry : entrySet) {
            propertiesMap.put((String) entry.getKey(), ((String) entry.getValue()).trim());
        }
    }

    private static final String getWebRoot() {

        URL url = ConfigUtils.class.getResource("/");
        String path = url.getPath();
        if (path.endsWith("/WEB-INF/classes/")) {
            int beginIndex = path.length() - "WEB-INF/classes/".length();
            return path.substring(0, beginIndex);
        }
        return path;
    }

    public static String getString(String proKey) {
        return propertiesMap.get(proKey);
    }

    public static String getString(String proKey, String defaultValue) {
        String value = propertiesMap.get(proKey);
        return StringUtil.isEmpty(value) ? defaultValue : value;
    }

    public static final int getInt(String key) {
        return Integer.parseInt(propertiesMap.get(key));
    }

    public static final int getInt(String key, int df) {
        if (propertiesMap.get(key) == null) {
            return df;
        }
        return Integer.parseInt(propertiesMap.get(key));
    }

    public static final long getLong(String key) {
        return Long.parseLong(propertiesMap.get(key));
    }

    private static final boolean getBoolean(String key, boolean defaultValue) {
        String str = getString(key, new Boolean(defaultValue).toString());
        if (str.equalsIgnoreCase("true") || str.equals("1") || str.equals("是") || str.equalsIgnoreCase("yes")) return Boolean.TRUE;
        return Boolean.FALSE;
    }
    
    /**
     * 得到网站地址
     */
    public static final String       ELASTIC_SEARCH_INDEX                    = getString("elastic_search_index", "");
    
    /**
     * 得到网站地址
     */
    public static final String       ELASTIC_SEARCH_TYPE                    = getString("elastic_search_type", "");

    /**
     * 得到网站地址
     */
    public static final String       WEBSITE                    = getString("web.site", "");

    /**
     * 得到网站域名
     */
    public static final String       DOMAIN                     = getString("web.domain");

    /**
     * 网站的绝对路径
     */
    public static final String       WEB_ROOT                   = getWebRoot();

    /**
     * 网站上传图片的路径
     */
    public static final String       UPLOAD_IMAGE_PATH          = getString("upload.image.path", "/upload_file/image");

    /**
     * 系统药品图片的路径
     */
    public static final String       MERCHANDISE_IMAGE_PATH     = getString("merchandise.image.path",
                                                                            "http://59.173.12.139:8010/MedicinedePository/manager/upload/Img/");

    /**
     * 定时任务启动运行开关
     */
    public static final boolean      QUARTZ_RUN_OPEN            = getBoolean("quartz.run.open", true);
    /**
     * 项目的基础包目录
     */
    public static final String       BASE_PACKAGE               = getString("project.base.package", "com");

    /**
     * xls模板的路径
     */
    public static final String       XLS_TEMPLATE_PATH          = getString("xls.template.path", "/xls_template/");

    /**
     * JSON VIEW 是否是开发模式
     */
    public static final boolean      JSON_VIEW_DEV_MODE         = getBoolean("json.view.dev.mode", false);

    /**
     * 图片上传的服务器路径
     */
    public static final String       UPLOAD_IMG_PATH            = getString("upload.img.path", "/");
    /**
     * 质检报告单的服务器路径
     */
    public static final String       ZHJD_PATH                  = getString("zhjd.path", "/");
    /**
     * 质检报告单相对路径
     */
    public static final String       ZHJD_PATH_RELATIVE         = getString("zhjd.path.relative", "/");

    /**
     * 图片上传后访问的相对路径
     */
    public static final String       UPLOAD_IMG_URL_RELATIVE    = getString("upload.img.url_relative", "/");

    /**
     * 文件上传的扩展名
     */
    public static final List<String> UPLOAD_IMG_FILE_EXTENSTION = Arrays.asList(getString("upload.img.file_extension",
                                                                                          "").split(" "));

    /**
     * 上传图片文件的最大限制
     */
    public static final int          UPLOAD_IMG_MAXSIZE         = getInt("upload.img.maxsize", 0);

    /**
     * 上传抽奖图片文件的最大限制
     */
    public static final int          UPLOAD_GIFT_IMG_MAXSIZE    = getInt("upload.gift.img.maxsize", 0);

    /**
     * 上传文件的最大限制
     */
    public static final int          UPLOAD_FILE_MAXSIZE        = getInt("upload.file.maxsize", 0);

    /**
     * 文件上传的服务器文件路径
     */
    public static final String       UPLOAD_FILE_PATH           = getString("upload.file.path", "/upload/file");

    /**
     * 文件上传后访问的相对路径
     */
    public static final String       UPLOAD_FILE_URL_RELATIVE   = getString("upload.file.url_relative", "/");

    /**
     * 药品优惠政策获取路径
     */
    public static final String       SHOW_HOTACTION_URL         = getString("show.hotaction.url", "/");

    /**
     * 药品说明书获取路径
     */
    public static final String       SHOW_DIRECTION_URL         = getString("show.direction.url", "/");

    /**
     * MANUFACTURER_TEMPLATE_URL 厂家专题导入模板路径
     */
    public static final String       MANUFACTURER_TEMPLATE_URL  = getString("manufacturerTemplate.template.url");

    /**
     * CHOOSE_BRANCH_URL 选择分公司跳转链接
     */
    public static final String       CHOOSE_BRANCH_URL          = getString("choose.branch.url");

    // //////////////////////////////////freemark模板参数////////////////////////////////////////////

    /**
     * TEMPLATE_BRANCHIDS FREEMARK所有分公司ID
     */
    public static final String       TEMPLATE_BRANCHIDS         = getString("template.branchIds");

    /**
     * TEMPLATE_HTML_ROOT FREEMARK静态文件跟目录
     */
    public static final String       TEMPLATE_HTML_ROOT         = getString("template.html.root");

    /**
     * TEMPLATE_INDEX_FTL 首页模板路径
     */
    public static final String       TEMPLATE_INDEX_FTL         = getString("template.index.ftl");

    /**
     * TEMPLATE_INDEX_HTML 首页HTML路径
     */
    public static final String       TEMPLATE_INDEX_HTML        = getString("template.index.html");

    // //////////////////////////////////freemark模板参数结束////////////////////////////////////////////

    // ///////////////////////////////////////任务调度系统参数开始///////////////////////////////////////////
    /**
     * JOB_FILE_URL 天梯同步任务配置文件的路径
     */
    public static final String       JOB_FILE_URL               = getString("job.file.url");

    /**
     * ZOOKEEPER_HOSTS ZOOKEEPER服务的地址
     */
    public static final String       ZOOKEEPER_HOSTS            = getString("zookeeper.hosts");

    /**
     * ZOOKEEPER1服务的地址
     */
    public static final String       ZOOKEEPER_HOST1            = getString("zookeeper.host1");
    /**
     * ZOOKEEPER2服务的地址
     */
    public static final String       ZOOKEEPER_HOST2            = getString("zookeeper.host2");

    /**
     * ZOOKEEPER3服务的地址
     */
    public static final String       ZOOKEEPER_HOST3            = getString("zookeeper.host3");

    /**
     * ZOOKEEPER_JOB_NODE 天梯任务在ZOOKEEPER上的存储路径
     */
    public static final String       ZOOKEEPER_JOB_NODE         = getString("zookeeper.job.node");

    /**
     * ZOOKEEPER_JOB_NODEBACKUP 天梯任务在ZOOKEEPER上的存储路径
     */
    public static final String       ZOOKEEPER_JOB_NODEBACKUP   = getString("zookeeper.job.nodebackup");

    /**
     * ZOOKEEPER_JOB_NODE 天梯线程在ZOOKEEPER上的存储路径
     */
    public static final String       ZOOKEEPER_QUEUE_NODE       = getString("zookeeper.queue.node");
    /**
     * ZOOKEEPER_JOB_NODE zookeeper连接超时时间
     */
    public static final int          ZOOKEEPER_TIMEOUT          = getInt("zookeeper.timeout", 10000);
    // ///////////////////////////////////////任务调度系统参数结束///////////////////////////////////////////

    public static final String       CHECK_BRANCH_RESOURCE      = getString("check.branch.resource", "0");

    /**
     * ZOOKEEPER_JOB_NODE threadCount 线程连接数
     */
    public static final int          THREAD_COUNT               = getInt("thread.count", 0);


    /**
     * 是否为分布式部署
     */
    public static final boolean      IS_ZOOKEEPER               = getBoolean("iszookeeper", false);

    /**
     * 质检报告单 参数
     */
    public static final String       ERP_BILL_URL               = getString("erp.bill.url");

    /**
     * 质检报告单 参数 质管系统
     */
    public static final String       ZG_REPORT_URL              = getString("zg.report.url");

    /**
     * 质检报告单 参数 临时打包文件夹
     */
    public static final String       DOWNLOAD_TEMP_FILE         = getString("download.temp.file");

    /**
     * 质检报告单首页地址
     */
    public static final String       REPORT_INDEX_URL           = getString("report.index.url");
    /**
     * 访问首页地址
     */
    public static final String       MAP_URL                    = getString("map.url");


    /**
     * 静态资源根目录
     */
    public static final String       STATIC_DOMAIN_NAME            = getString("static_domain_name", "/static");

 


}
