package com.xyy.ec.pop.vo;

import lombok.Data;

import java.util.Date;

/**
 * @version v1
 * @Description 资质分类扩展实体
 * <AUTHOR>
 */
@Data
public class QualificationsCategoryVo {
    private Long id;

    /**
     * 资质分类名称
     */
    private String name;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改者
     */
    private String updator;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 0-企业经营类资质,1-企业统一资质
     */
    private Byte type;
    /**
     *是否必填
     */
    private boolean isCheck;
    /**
     * 父级ID
     */
    private Long parentId;
    /**
     * 企业类型
     */
    private Byte corporationType;
}
