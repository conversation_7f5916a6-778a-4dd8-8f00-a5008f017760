package com.xyy.ec.pop.controller;

import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.config.ProductCommissionUpdateConfig;
import com.xyy.ec.pop.excel.verify.ProductBatchUpdateExcelVerifyHandler;
import com.xyy.ec.pop.excel.verify.ProductCommissionUpdateExcelVerifyHandler;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.helper.ProductCommissionHelper;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.service.PopSkuCategoryService;
import com.xyy.ec.pop.utils.ExceImportWarpUtil;
import com.xyy.ec.pop.utils.FileNameUtils;
import com.xyy.ec.pop.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 */
@RestController
@RequestMapping("/productCommission")
@Slf4j
public class ProductCommissionController extends BaseController {
    @Autowired
    private PopSkuCategoryService popSkuCategoryService;
    @Autowired
    private ProductCommissionUpdateConfig productCommissionUpdateConfig;
    @Autowired
    private DownloadRemote downloadRemote;

    @GetMapping(value = "/list")
    public ResponseVo<Page<ProductCommissionVo>> list(ProductCommissionQueryVo queryVo) {
        try {
            log.info("ProductCommissionController.list#queryVo:{}", JSON.toJSONString(queryVo));
            Page<ProductCommissionVo> page = popSkuCategoryService.list(queryVo);
            log.info("ProductCommissionController.list#queryVo:{} return {}", JSON.toJSONString(queryVo), JSON.toJSONString(page));
            return ResponseVo.successResult(page);
        } catch (Exception e) {
            log.error("ProductCommissionController.list#queryVo:{} 异常", JSON.toJSONString(queryVo), e);
            return ResponseVo.errRest("查询异常");
        }
    }

    @PostMapping(value = "/batchCommission")
    public ResponseVo<BatchUpdateResultVo> copyMeSkuFromExcel(@RequestParam("file") MultipartFile file) {
        String userName = getUser().getUsername();
        try {
            log.info("ProductCommissionController.copyMeSkuFromExcel#user:{}", userName);

            ImportParams importParams = new ImportParams();
            importParams.setVerifyHandler(new ProductCommissionUpdateExcelVerifyHandler());
            List<ProductCommissionBatchUpdateVo> vos = ExceImportWarpUtil.importExcel(file, ProductCommissionBatchUpdateVo.class, importParams, productCommissionUpdateConfig.getMaxFileSize(), productCommissionUpdateConfig.getMaxRows(), productCommissionUpdateConfig.getUpdateTitles());
            Long distinctCount = vos.stream().map(item -> item.getCsuid()).distinct().collect(Collectors.counting());
            if(vos.size()!=distinctCount){
                return ResponseVo.errRest("有重复的CSUID，导入失败");
            }
            BatchUpdateResultVo resultVo = popSkuCategoryService.batchUpdate(vos, userName);

            resultVo.setErrorFileName(FileNameUtils.getErrorFileName(file));
            if(resultVo.getError()>0){
                resultVo.setErrorFileUrl(FileNameUtils.getErrorFileDownUrl(resultVo.getErrorFileUrl(), resultVo.getErrorFileName()));
            }
            log.info("ProductCommissionController.copyMeSkuFromExcel#user:{} return {}", userName, JSON.toJSONString(resultVo));
            return ResponseVo.successResult(resultVo);
        } catch (ServiceException e) {
            log.error("ProductCommissionController.copyMeSkuFromExcel#user:{} 失败:{}", userName, e.getMessage());
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("ProductCommissionController.copyMeSkuFromExcel#user:{} 异常", userName, e);
            return ResponseVo.errRest("设置异常");
        }
    }

    @PostMapping(value = "/updateCommission")
    public ResponseVo<Boolean> updateCommission(@RequestBody ProductCommissionBatchUpdateVo updateVo) {
        String userName = getUser().getUsername();
        try {
            log.info("ProductCommissionController.updateCommission#updateVo:{}", JSON.toJSONString(updateVo));
            boolean result = popSkuCategoryService.updateCommission(updateVo, userName);
            log.info("ProductCommissionController.updateCommission#updateVo:{} user:{},return {}", JSON.toJSONString(updateVo), userName, result);
            return ResponseVo.successResult(result);
        } catch (ServiceException e) {
            log.error("ProductCommissionController.updateCommission#updateVo:{} user:{}, 失败:{}", JSON.toJSONString(updateVo), userName, e.getMessage());
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("ProductCommissionController.updateCommission#updateVo:{} user:{}, 异常", JSON.toJSONString(updateVo), userName, e);
            return ResponseVo.errRest("更新异常");
        }
    }


    @GetMapping(value = "/query/commission/log")
    public ResponseVo<CommissionRatioOperateLogVo> queryCommissionRatioLog(@RequestParam(required = true) String barcode) {
        try {
            log.info("ProductCommissionController.queryCommissionRatioLog#barcode:{}", JSON.toJSONString(barcode));
            CommissionRatioOperateLogVo commissionRatioOperateLogVo = popSkuCategoryService.queryCommissionRatioLog(barcode);
            log.info("ProductCommissionController.queryCommissionRatioLog#barcode:{},return {}", barcode, commissionRatioOperateLogVo);
            return ResponseVo.successResult(commissionRatioOperateLogVo);
        } catch (Exception e) {
            log.error("ProductCommissionController.queryCommissionRatioLog#barcode:{} , 异常", barcode, e);
            return ResponseVo.errRest("查询异常");
        }
    }

    @PostMapping(value = "/deleteCommission")
    public ResponseVo<Boolean> deleteCommission(@RequestBody List<String> barcodes) {
        String userName = getUser().getUsername();
        try {
            log.info("ProductCommissionController.deleteCommission#barcodes:{}", JSON.toJSONString(barcodes));
            boolean result = popSkuCategoryService.deleteCommission(barcodes, userName);
            log.info("ProductCommissionController.deleteCommission#barcodes:{} user:{},return {}", JSON.toJSONString(barcodes), userName, result);
            return result ? ResponseVo.successResult(result) : ResponseVo.errRest("删除失败");
        } catch (ServiceException e) {
            log.error("ProductCommissionController.deleteCommission#barcodes:{} user:{} 失败:{}", JSON.toJSONString(barcodes), userName, e.getMessage());
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("ProductCommissionController.deleteCommission#barcodes:{} user:{} 异常", JSON.toJSONString(barcodes), userName, e);
            return ResponseVo.errRest("删除异常");
        }
    }

    @GetMapping(value = "/export")
    public ResponseVo<Boolean> export(ProductCommissionQueryVo queryVo) {
        try {
            log.info("ProductCommissionController.export#queryVo:{}", JSON.toJSONString(queryVo));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(ProductCommissionHelper.convertToQuery(queryVo))
                    .operator(getUser().getEmail())
                    .businessType(DownloadTaskBusinessTypeEnum.SKU_COMMISSION_RATIO_SET)
                    .build();
            boolean result = downloadRemote.saveTask(content);
            log.info("ProductCommissionController.export#queryVo:{} return {}", JSON.toJSONString(queryVo), JSON.toJSONString(result));
            return ResponseVo.successResult(result);
        }catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("ProductCommissionController.export#queryVo:{} 异常", JSON.toJSONString(queryVo), e);
            return ResponseVo.errRest("导出异常");
        }
    }
}
