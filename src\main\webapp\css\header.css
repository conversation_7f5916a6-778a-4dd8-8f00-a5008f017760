html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, menu, nav, output, ruby, section, summary, time, mark, audio, video{
    margin: 0;
    padding: 0;
    border: 0;
    text-decoration: none;
    font: normal;
    font-size: 100%;
    font-family: "Microsoft YaHei", "微软雅黑";
}
.header{
    width: 100%;
    height: 110px;
    box-shadow:0px 0px 15px #666;
    font-size: 14px;
}
.header_nav{
    width: 100%;
    height: 60px;
    background-color:#0075a9 ;
}
.header_nav_logo{
    float: left;
    padding-top:18px;
}
.header_nav_logo img{
    width:200px;
}
.header_nav_main{
    height: 60px;
}
.header_nav_main ul{
    height: 60px;
}
.header_nav_main ul li{
    float: left;
    list-style: none;
    height: 60px;
    line-height: 60px;
    padding-left: 20px;
    padding-right: 20px;

}
.left_list_ul_li{
    color: #ffffff;;
    font-size:12px;
    cursor: pointer;
}
.header_nav_main ul li a{
    color: #cffdff;
}
.header_nav_main ul li:hover{
    background-color: #0098c3;

}
.list_active{
    background-color: #0098c3;
}
.list_active:hover{
    background-color: #0098c3;
}
.header_nav_right{
    float: right;
    position: absolute;
    top: 0;
    right: 0;

}
.header_nav_right div{
    float: left;
    height: 60px;
    line-height: 60px;
    padding: 0 15px;
    color: #fff;
    cursor: pointer;
}
.header_nav_right_name{
    margin-right: -10px;
}
.header_nav_right_img{
    margin-right: -20px;
}
.header_nav_right_img_tx{
    margin-top: 17px;
}
.header_nav_right_img div{
    width: 20px;
    height: 20px;
    line-height: 20px;
    background-image: url("../images/red.png");
    background-repeat:no-repeat ;
    margin-left: 20px;
    margin-top: -60px;
    position: absolute;
    color: #fff;
    padding: 0;

}
.header_nav_right_close img{
    margin-top: 17px;
}
.header_title{
    height: 50px;
    background-color: #fff;
}
.header_title div{
    line-height: 50px;
    font-size: 12px;
}
.header_title_left{
    float: left;
    color:#809FAC;
    padding-left: 20px;
}
.header_title_right{
    float: right;
    color:#B4B4B4;
    padding-right: 20px;
}
.header_span{
    height: 5px;
    width: 100%;
    background-color: transparent;
    background-color: #0098c3;
    opacity: 0.5;
    position: absolute;
    top: 0;
    left: 0;
}