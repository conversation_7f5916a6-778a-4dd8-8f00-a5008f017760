package com.xyy.ec.pop.dto.mop;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 商家运用-下载绑定招商/运营/poi 模版DTO
 *
 * @author: duHao
 * @since: 12:55 2024/12/4
 */
@Data
public class MopMerchantBatchBindTemplateDTO implements Serializable {

    /**
     * poiId
     */
    @ExcelProperty(value = "poiId")
    private String poiId;

    /**
     * 商户编号
     */
    @ExcelProperty("商户编号")
    private String orgId;

    /**
     * 招商
     */
    @ExcelProperty("招商工号")
    private String bindZsAccountId;

    /**
     * 运营
     */
    @ExcelProperty("运营工号")
    private String bindYyAccountId;


}
