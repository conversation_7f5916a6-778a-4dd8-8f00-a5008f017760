/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.xyy.ec.pop.utils;

import java.util.regex.Pattern;

/**
 * 正则工具类
 * <AUTHOR>
 */
public class RegexUtils {
	  /**
     * 验证整数（正整数和负整数）
     *
     * @param digit 一位或多位0-9之间的整数
     * @return 验证成功返回true，验证失败返回false
     */
    public static boolean checkDigit(String digit) {
        String regex = "^-?\\d+$";
        return Pattern.matches(regex, digit);
    }
    
    
    /**
     * 匹配是否是数字
     * @param digit
     * @return
     */
    public static boolean isNumber(String digit){
    	 String regex = "-?[0-9]+\\.?[0-9]*";
    	 return Pattern.matches(regex, digit);
    }

	/**
	 * 判断是否整型
	 * @param str
	 * @return
	 */
	public static boolean isInteger(String str) {
		Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
		return pattern.matcher(str).matches();
	}
    
}
