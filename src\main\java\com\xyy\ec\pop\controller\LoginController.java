package com.xyy.ec.pop.controller;


import com.xyy.ec.pop.redis.XyyJedisCluster;
import com.xyy.ec.pop.service.SystemService;
import com.xyy.ec.pop.utils.Base64Utils;
import com.xyy.ec.pop.utils.ShiroUtils;
import com.xyy.ec.pop.utils.rsa.RSAPublicKey;
import com.xyy.ec.pop.utils.rsa.RSASecurity;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.me.sso.client.pac4j.Pac4jConfigurationProperties;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录控制器 Created by caofei on 2016/3/28.
 */
@Controller
@Api(tags ="登录控制类")
public class LoginController {


    private static final Logger LOGGER = LoggerFactory.getLogger(LoginController.class);
    public static final String RESULT_STATUS = "status";
    public static final String RESULT_SUCCESS = "success";
    @Autowired
    private XyyJedisCluster xyyJedisCluster;

    @Autowired
    private SystemService systemService;
    @Autowired
    private Pac4jConfigurationProperties pac4jProperties;

    /**
     * 初始化RSA加密密钥
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/login/initPublicKey",method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("初始化RSA加密密钥")
    public Object initPublicKey(@ApiParam(name = "qt",value = "RSA加密密钥") String qt) throws Exception {
        KeyPair keyPair = RSASecurity.generateKey();
        PrivateKey privateKey = keyPair.getPrivate();
        byte[] keyBytes = privateKey.getEncoded();
        String privateKeyStr = Base64Utils.encode(keyBytes);
        xyyJedisCluster.setex("RSAPrivateKey_" + qt, 1800, privateKeyStr);
        RSAPublicKey rsaKey = new RSAPublicKey(((java.security.interfaces.RSAPublicKey) keyPair.getPublic()).getModulus().toString(16),
                ((java.security.interfaces.RSAPublicKey) keyPair.getPublic()).getPublicExponent().toString(16));
        return this.addResult("rsaKey", rsaKey);
    }

    /**
     * 添加返回结果
     *
     * @param name  返回对象的名称
     * @param value 需要返回的对象
     * @return
     * <AUTHOR>
     */
    protected Map<String, Object> addResult(String name, Object value) {
        Map<String, Object> responseData = new HashMap<>();
        responseData.put(RESULT_STATUS, RESULT_SUCCESS);
        responseData.put(name, value);
        return responseData;
    }

    /**
     * 退出.
     * @return 退出登录
     */
    @RequestMapping(value = "/logout/success",method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @ApiOperation("退出")
    public Map<String, Object> logoutUser() {
        try {
            systemService.logout();
            ShiroUtils.unBindSubject();
        } catch (Exception e) {
            LOGGER.error("退出登录异常",e);
        }
        LOGGER.info("注销登录");
        return this.addResult();
    }

    /**
     * 退出登录后，重定向到中台登录页
     * @param session session
     * @return 重定向页面
     */
    @RequestMapping(value="/logoutCustom")
    public String logoutCustomer(HttpSession session, HttpServletRequest request) {
        session.invalidate();
        ShiroUtils.unBindSubject();
        LOGGER.info("退出登录");
        // 退出登录后，跳转到退出成功的页面，不走默认页面
        return "redirect:"+ pac4jProperties.getCasServerUrl()
                + "/logout?service="+pac4jProperties.getClientHostUrl();
    }

    public HttpServletRequest getRequest() {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) ra).getRequest();
        return request;
    }

    protected Map<String, Object> addResult() {
        Map<String, Object> responseData = new HashMap<String, Object>();
        responseData.put(RESULT_STATUS, RESULT_SUCCESS);
        return responseData;
    }
}
