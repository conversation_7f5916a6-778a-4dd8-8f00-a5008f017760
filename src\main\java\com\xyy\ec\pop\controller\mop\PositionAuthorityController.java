package com.xyy.ec.pop.controller.mop;

import com.xyy.ec.pop.adapter.dto.mop.PositionAuthorityDTO;
import com.xyy.ec.pop.adapter.mop.PositionAuthorityAdapter;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.utils.mop.MopDataFillerUtils;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.parameter.PositionAuthorityParame;
import com.xyy.pop.mop.api.remote.parameter.query.PositionAuthorityQueryParame;
import com.xyy.pop.mop.api.remote.result.PositionAllDTO;
import com.xyy.pop.mop.api.remote.result.PositionAuthorityBasicDTO;
import com.xyy.scm.constant.entity.pagination.Paging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 岗位权限控制器
 * 本控制器提供了岗位权限的增删改查功能，通过调用适配器层的方法实现具体业务逻辑
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/mop/position")
@RestController
public class PositionAuthorityController extends BaseController {

    @Resource
    private PositionAuthorityAdapter positionAuthorityAdapter;

    /**
     * 添加岗位权限
     * 该方法用于新增岗位权限信息
     *
     * @param parame 包含需要创建的岗位权限详细信息的参数对象
     * @return 返回操作结果，包括是否成功创建岗位权限
     */
    @PostMapping("/add")
    public ResponseVo<Boolean> add(@RequestBody PositionAuthorityDTO dto) {
        PositionAuthorityParame parame = dto.toParame();
        MopDataFillerUtils.fillData(getUser(),parame,false);
        return positionAuthorityAdapter.createPositionAuthority(parame);
    }

    /**
     * 更新岗位权限
     * 该方法用于更新已有的岗位权限信息
     *
     * @param parame 包含需要更新的岗位权限详细信息的参数对象
     * @return 返回操作结果，包括是否成功更新岗位权限
     */
    @PostMapping("/update")
    public ResponseVo<Boolean> update(@RequestBody PositionAuthorityDTO dto) {
        PositionAuthorityParame parame = dto.toParame();
        MopDataFillerUtils.fillData(getUser(),parame,true);
        return positionAuthorityAdapter.updatePositionAuthority(parame);
    }

    /**
     * 删除岗位权限
     * 该方法用于删除指定的岗位权限信息
     *
     * @param id 需要删除的岗位权限的ID
     * @return 返回操作结果，包括是否成功删除岗位权限
     */
    @GetMapping("/delete/{id}")
    public ResponseVo<Boolean> deletePositionAuthority(@PathVariable Long id) {
        return positionAuthorityAdapter.deletePositionAuthority(id);
    }

    /**
     * 查询岗位权限
     * 该方法用于查询指定ID的岗位权限详细信息
     *
     * @param id 需要查询的岗位权限的ID
     * @return 返回查询结果，包括岗位权限的基本数据传输对象
     */
    @GetMapping("/{id}")
    public ResponseVo<PositionAuthorityBasicDTO> findPositionAuthority(@PathVariable Long id) {
        return positionAuthorityAdapter.findPositionAuthority(id);
    }

    /**
     * 分页查询岗位权限
     * 该方法用于分页查询岗位权限列表，以便处理大量数据时进行分页显示
     *
     * @param parame 包含分页查询条件的参数对象
     * @return 返回查询结果，包括分页的岗位权限基本数据传输对象列表
     */
    @GetMapping("/list")
    public ResponseVo<Paging<PositionAuthorityBasicDTO>> pagePositionAuthoritiesByPaging(PositionAuthorityQueryParame parame) {
        return positionAuthorityAdapter.pagePositionAuthoritiesByPaging(parame);
    }


    /**
     * 查询所有岗位权限信息
     * @return
     */
    @GetMapping("/all")
    public ResponseVo<List<PositionAllDTO>> findAllPosition() {
        return positionAuthorityAdapter.findAllPosition();
    }
}
