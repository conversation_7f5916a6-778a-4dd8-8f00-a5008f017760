package com.xyy.ec.pop.filter;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.redis.XyyJedisCluster;
import com.xyy.ec.pop.service.SsoService;
import com.xyy.ec.pop.service.SystemService;
import com.xyy.ec.pop.utils.Constants;
import com.xyy.ec.pop.utils.ShiroUtils;
import com.xyy.ec.pop.utils.cookie.CookieTool;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.login.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 模拟原来的登录逻辑，设置登录之后的必要信息
 */
@Component
@Slf4j
public class LocalSessionInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private SystemService systemService;
    @Autowired
    private SsoService ssoService;
    @Autowired
    private XyyJedisCluster xyyJedisCluster;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Cookie tgc = CookieTool.getCookie(request, "TGC");
        if (tgc == null) {
            return false;
        }
        String token;
        if (StringUtils.isNotEmpty(token = tgc.getValue()) && StringUtils.isNotEmpty(ShiroUtils.getUsername())) {
            Cookie localSessionCookie = CookieTool.getCookie(request, Constants.SESSION_ID);
            log.info("current user {}", ShiroUtils.getUsername());
            // 如果本地缓存有，就没有必要再次设置了
            if (localSessionCookie != null && StringUtils.isNotEmpty(localSessionCookie.getValue())
                    && StringUtils.isNotEmpty(xyyJedisCluster.get(localSessionCookie.getValue()))
                    && getUser() != null && ShiroUtils.getUsername().equals(getUser().getUsername())) {
                return true;
            }
            ResponseVo<UserInfo> responseVo = ssoService.getUser(ShiroUtils.getUsername(), token);
            if (responseVo.isSuccess()) {
                UserInfo userInfo = responseVo.getData();
                if (userInfo == null){
                    return false;
                }
                userInfo.setToken(token);
                log.info("设置本地 session 缓存");
                // 设置本地相关权限，如果无权限，则直接拦截
                return systemService.mockLocalLogin(userInfo);
            }
        }
        return true;
    }

    public SysUser getUser() {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) ra).getRequest();
        Cookie cookie = CookieTool.getCookie(request, Constants.SESSION_ID);
        if (cookie != null) {
            try {
                String json = xyyJedisCluster.get(cookie.getValue());
                SysUser user = (SysUser) JSON.parseObject(json, SysUser.class);
                return user;
            } catch (Exception e) {
                log.error(
                        "分布式缓存中没有sid=" + cookie.getValue() + "的用户"
                                + e.getMessage(), e);
            }

        }
        return null;
    }
}
