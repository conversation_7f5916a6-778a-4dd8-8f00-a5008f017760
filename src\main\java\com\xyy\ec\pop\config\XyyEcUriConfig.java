/**
 * Copyright (C), 2015-2019, XXX有限公司
 * FileName: XyyEcUriConfig
 * Author:   danshiyu
 * Date:     2019/11/14 10:08
 * Description: ec接口地址配置
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.xyy.ec.pop.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 〈一句话功能简述〉<br>
 * 〈ec接口地址配置〉
 *
 * <AUTHOR>
 * @create 2019/11/14
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ec.uri")
public class XyyEcUriConfig {

    /**
     * 获取所有域
     */
    private String allBranchsUrl;

    /**
     *
     */
    private String dicNewAreasUrl;

}
