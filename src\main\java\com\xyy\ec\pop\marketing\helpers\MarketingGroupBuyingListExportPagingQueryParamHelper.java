package com.xyy.ec.pop.marketing.helpers;

import com.xyy.ec.marketing.elephant.params.MarketingGroupBuyingListExportPagingQueryParam;
import com.xyy.ec.pop.marketing.param.GroupBuyingListExportParam;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * {@link MarketingGroupBuyingListExportPagingQueryParam} 帮助类
 *
 * <AUTHOR>
 */
public class MarketingGroupBuyingListExportPagingQueryParamHelper {

    public static MarketingGroupBuyingListExportPagingQueryParam create(GroupBuyingListExportParam queryParam) {
        if (Objects.isNull(queryParam)) {
            return null;
        }
        MarketingGroupBuyingListExportPagingQueryParam marketingGroupBuyingListExportPagingQueryParam = MarketingGroupBuyingListExportPagingQueryParam.builder()
                .orgId(queryParam.getOrgId())
                .orgName(queryParam.getOrgName())
                .barcode(queryParam.getBarcode())
                .productCode(queryParam.getProductCode())
                .productName(queryParam.getProductName())
                .theme(queryParam.getTheme())
                .actStartTime(queryParam.getActStartTime())
                .actEndTime(queryParam.getActEndTime())
                .sourceType(queryParam.getSourceType())
                .status(queryParam.getStatus())
                .isPlatformSubsidy(queryParam.getIsPlatformSubsidy())
                .isVirtualShop(queryParam.getIsVirtualShop())
                .stepPriceStatus(queryParam.getStepPriceStatus())
                .isOrder(queryParam.getIsOrder())
                .build();
        if (StringUtils.isNotEmpty(queryParam.getActIdOrReportId())) {
            marketingGroupBuyingListExportPagingQueryParam.setActIdOrReportId(Long.parseLong(queryParam.getActIdOrReportId()));
        }
        if (StringUtils.isNotEmpty(queryParam.getCsuId())) {
            marketingGroupBuyingListExportPagingQueryParam.setCsuId(Long.parseLong(queryParam.getCsuId()));
        }
        if (StringUtils.isNotEmpty(queryParam.getTopics())) {
            List<String> topics = Arrays.stream(queryParam.getTopics().split(","))
                    .filter(StringUtils::isNotEmpty)
                    .map(String::trim)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            marketingGroupBuyingListExportPagingQueryParam.setTopics(topics);
        }
        return marketingGroupBuyingListExportPagingQueryParam;
    }

}
