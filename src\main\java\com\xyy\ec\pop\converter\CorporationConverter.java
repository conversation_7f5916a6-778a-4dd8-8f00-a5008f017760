package com.xyy.ec.pop.converter;

import com.xyy.ec.pop.server.api.merchant.dto.CorporationSettleCycle;
import com.xyy.ec.pop.vo.CorporationSettleCycleVo;

/**
 * <AUTHOR>
 */
public class CorporationConverter {
    public static CorporationSettleCycleVo toSettleCycleVo(CorporationSettleCycle settleCycle) {
        if (settleCycle == null) {
             return null;
        }
        CorporationSettleCycleVo settleCycleVo = new CorporationSettleCycleVo();
        settleCycleVo.setSettleCycle(settleCycle.getSettleCycle());
        settleCycleVo.setSettleValue(settleCycle.getSettleValue());
        return settleCycleVo;
    }
}
