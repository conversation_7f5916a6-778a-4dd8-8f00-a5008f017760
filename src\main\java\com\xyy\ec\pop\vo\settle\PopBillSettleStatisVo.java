package com.xyy.ec.pop.vo.settle;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**结算单统计佣金金额，应结算金额
* <AUTHOR>
* @date  2020/12/9 10:49
* @table
*/
@Data
public class PopBillSettleStatisVo implements Serializable {

    /**
     * 佣金合计
     */
    private BigDecimal hireMoneyTotal = BigDecimal.ZERO;
    /**
     * 应结算金额合计
     */
    private BigDecimal statementTotalMoneyTotal = BigDecimal.ZERO;
    /**
     * 实际需缴纳佣金合计
     */
    private BigDecimal actualCommissionMoneyTotal = BigDecimal.ZERO;
    /**
     * 佣金优惠合计
     */
    private BigDecimal commissionDiscountMoneyTotal = BigDecimal.ZERO;
    /**
     * 商品实付金额合计
     */
    private BigDecimal productActualMoneyTotal = BigDecimal.ZERO;
    /**
     * 应缴纳佣金合计
     */
    private BigDecimal deductedCommissionTotal = BigDecimal.ZERO;
}
