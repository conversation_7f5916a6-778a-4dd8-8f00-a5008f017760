package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationBusinessAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationBusinessDto;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 经营范围查询
 * <AUTHOR>
 * @Date 2020/12/21
 */
@Service
@Slf4j
public class CorporationBusinessRemoteAdapter {
    @Reference
    private CorporationBusinessAdminApi corporationBusinessAdminApi;

    public List<CorporationBusinessDto> listCorporationBusiness(Integer cid) throws ServiceException {
        ApiRPCResult<List<CorporationBusinessDto>> result;
        try {
            log.info("CorporationBusinessRemoteAdapter.listCorporationBusiness#cid:{}",cid);
            result = corporationBusinessAdminApi.listCorporationBusiness(cid);
            log.info("CorporationBusinessRemoteAdapter.listCorporationBusiness#cid:{} return {}",cid, JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("CorporationBusinessRemoteAdapter.listCorporationBusiness#cid:{} 异常",cid, e);
            throw new ServiceException("查询经营范围异常");
        }
        if(result.isSuccess()){
            return result.getData();
        }
        throw new ServiceException("查询经营范围异常");
    }
}
