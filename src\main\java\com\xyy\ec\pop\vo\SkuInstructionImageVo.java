package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品说明书图片实体
 */
@Data
public class SkuInstructionImageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 商品id */
    private Long skuId;
    /**
     * 商品编码
     */
    private String barcode;
    /** 图片地址(相对地址） */
    private String instrutionImageUrl;

    /** 创建提交 */
    private Date createTime;

    /** 创建者 */
    private String creator;

    /** 更新时间 */
    private Date updateTime;

    /** 更新者 */
    private String updator;

}