<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.ec.pop.dao.PopBillPaymentMapper">
  <resultMap id="BaseResultMap" type="com.xyy.ec.pop.po.PopBillPaymentPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="product_money" jdbcType="DECIMAL" property="productMoney" />
    <result column="total_money" jdbcType="DECIMAL" property="totalMoney" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="freight_amount" jdbcType="DECIMAL" property="freightAmount" />
    <result column="coupon_shop_amount" jdbcType="DECIMAL" property="couponShopAmount" />
    <result column="marketing_shop_amount" jdbcType="DECIMAL" property="marketingShopAmount" />
    <result column="shop_total_discount" jdbcType="DECIMAL" property="shopTotalDiscount" />
    <result column="coupon_platform_amount" jdbcType="DECIMAL" property="couponPlatformAmount" />
    <result column="marketing_platform_amount" jdbcType="DECIMAL" property="marketingPlatformAmount" />
    <result column="platform_total_discount" jdbcType="DECIMAL" property="platformTotalDiscount" />
    <result column="hire_money" jdbcType="DECIMAL" property="hireMoney" />
    <result column="penalty_amount" jdbcType="DECIMAL" property="penaltyAmount" />
    <result column="statement_total_money" jdbcType="DECIMAL" property="statementTotalMoney" />
    <result column="pay_type" jdbcType="TINYINT" property="payType" />
    <result column="bill_payment_status" jdbcType="TINYINT" property="billPaymentStatus" />
    <result column="bill_payment_time" jdbcType="TIMESTAMP" property="billPaymentTime" />
    <result column="remit_status" jdbcType="TINYINT" property="remitStatus" />
    <result column="remit_time" jdbcType="TIMESTAMP" property="remitTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="payable_commission" jdbcType="DECIMAL" property="payableCommission" />
    <result column="deducted_commission" jdbcType="DECIMAL" property="deductedCommission" />
    <result column="deducted" jdbcType="TINYINT" property="deducted" />
    <result column="bill_share_status" property="billShareStatus" />
    <result column="bill_share_time" property="billShareTime" />
    <result column="bill_share_result" property="billShareResult" />
    <result column="payment_channel" property="paymentChannel" />
    <result column="trade_no"  jdbcType="VARCHAR" property="tradeNo" />
    <result column="direct_order_real_money" jdbcType="DECIMAL" property="directOrderRealMoney" />
    <result column="refund_order_real_money" jdbcType="DECIMAL" property="refundOrderRealMoney" />
    <result column="pa_order_real_money" jdbcType="DECIMAL" property="paOrderRealMoney" />
    <result column="fm_order_real_money" jdbcType="DECIMAL" property="fmOrderRealMoney" />
    <result column="virtual_gold_real_money" jdbcType="DECIMAL" property="virtualGoldRealMoney" />
    <result column="refund_cash_real_money" jdbcType="DECIMAL" property="refundCashRealMoney" />
    <result column="refund_virtual_gold_real_money" jdbcType="DECIMAL" property="refundVirtualGoldRealMoney" />
    <result column="prov_id" property="provId"/>
    <result column="prov" property="prov"/>
    <result column="updater_code" jdbcType="VARCHAR" property="updaterCode" />
    <result column="updater_name" jdbcType="VARCHAR" property="updaterName" />
    <result column="cash_pay_amount" jdbcType="DECIMAL" property="cashPayAmount" />
    <result column="virtual_gold" jdbcType="DECIMAL" property="virtualGold" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_id, org_name, flow_no, product_money, total_money, money, freight_amount, 
    coupon_shop_amount, marketing_shop_amount, shop_total_discount, coupon_platform_amount, 
    marketing_platform_amount, platform_total_discount, hire_money, payable_commission, deducted_commission,deducted,penalty_amount, statement_total_money,
    pay_type, bill_payment_status, bill_payment_time, remit_status, remit_time, bill_share_status,bill_share_time,bill_share_result,payment_channel,
    trade_no, direct_order_real_money, refund_order_real_money,
    pa_order_real_money, fm_order_real_money, virtual_gold_real_money, refund_cash_real_money, refund_virtual_gold_real_money,
    create_time, update_time, prov_id, prov,updater_code,updater_name, cash_pay_amount, virtual_gold
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_xyy_pop_bill_payment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tb_xyy_pop_bill_payment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xyy.ec.pop.po.PopBillPaymentPo" useGeneratedKeys="true">
    insert into tb_xyy_pop_bill_payment (org_id, org_name, flow_no, 
      product_money, total_money, money, 
      freight_amount, coupon_shop_amount, marketing_shop_amount, 
      shop_total_discount, coupon_platform_amount, 
      marketing_platform_amount, platform_total_discount, 
      hire_money,payable_commission, penalty_amount, statement_total_money,
      pay_type, bill_payment_status, bill_payment_time, 
      remit_status, remit_time,   create_time, update_time
      )
    values (#{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, #{flowNo,jdbcType=VARCHAR}, 
      #{productMoney,jdbcType=DECIMAL}, #{totalMoney,jdbcType=DECIMAL}, #{money,jdbcType=DECIMAL}, 
      #{freightAmount,jdbcType=DECIMAL}, #{couponShopAmount,jdbcType=DECIMAL}, #{marketingShopAmount,jdbcType=DECIMAL}, 
      #{shopTotalDiscount,jdbcType=DECIMAL}, #{couponPlatformAmount,jdbcType=DECIMAL}, 
      #{marketingPlatformAmount,jdbcType=DECIMAL}, #{platformTotalDiscount,jdbcType=DECIMAL}, 
      #{hireMoney,jdbcType=DECIMAL},#{payableCommission,jdbcType=DECIMAL}, #{penaltyAmount,jdbcType=DECIMAL}, #{statementTotalMoney,jdbcType=DECIMAL},
      #{payType,jdbcType=TINYINT}, #{billPaymentStatus,jdbcType=TINYINT}, #{billPaymentTime,jdbcType=TIMESTAMP}, 
      #{remitStatus,jdbcType=TINYINT}, #{remitTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xyy.ec.pop.po.PopBillPaymentPo" useGeneratedKeys="true">
    insert into tb_xyy_pop_bill_payment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="flowNo != null">
        flow_no,
      </if>
      <if test="productMoney != null">
        product_money,
      </if>
      <if test="totalMoney != null">
        total_money,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="freightAmount != null">
        freight_amount,
      </if>
      <if test="couponShopAmount != null">
        coupon_shop_amount,
      </if>
      <if test="marketingShopAmount != null">
        marketing_shop_amount,
      </if>
      <if test="shopTotalDiscount != null">
        shop_total_discount,
      </if>
      <if test="couponPlatformAmount != null">
        coupon_platform_amount,
      </if>
      <if test="marketingPlatformAmount != null">
        marketing_platform_amount,
      </if>
      <if test="platformTotalDiscount != null">
        platform_total_discount,
      </if>
      <if test="hireMoney != null">
        hire_money,
      </if>
      <if test="payableCommission != null">
        payable_commission,
      </if>
      <if test="penaltyAmount != null">
        penalty_amount,
      </if>
      <if test="statementTotalMoney != null">
        statement_total_money,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="billPaymentStatus != null">
        bill_payment_status,
      </if>
      <if test="billPaymentTime != null">
        bill_payment_time,
      </if>
      <if test="remitStatus != null">
        remit_status,
      </if>
      <if test="remitTime != null">
        remit_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="flowNo != null">
        #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="productMoney != null">
        #{productMoney,jdbcType=DECIMAL},
      </if>
      <if test="totalMoney != null">
        #{totalMoney,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="freightAmount != null">
        #{freightAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponShopAmount != null">
        #{couponShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingShopAmount != null">
        #{marketingShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="shopTotalDiscount != null">
        #{shopTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="couponPlatformAmount != null">
        #{couponPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingPlatformAmount != null">
        #{marketingPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="platformTotalDiscount != null">
        #{platformTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="hireMoney != null">
        #{hireMoney,jdbcType=DECIMAL},
      </if>
      <if test="payableCommission != null">
        #{payableCommission,jdbcType=DECIMAL},
      </if>
      <if test="penaltyAmount != null">
        #{penaltyAmount,jdbcType=DECIMAL},
      </if>
      <if test="statementTotalMoney != null">
        #{statementTotalMoney,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=TINYINT},
      </if>
      <if test="billPaymentStatus != null">
        #{billPaymentStatus,jdbcType=TINYINT},
      </if>
      <if test="billPaymentTime != null">
        #{billPaymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remitStatus != null">
        #{remitStatus,jdbcType=TINYINT},
      </if>
      <if test="remitTime != null">
        #{remitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xyy.ec.pop.po.PopBillPaymentPo">
    update tb_xyy_pop_bill_payment
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="flowNo != null">
        flow_no = #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="productMoney != null">
        product_money = #{productMoney,jdbcType=DECIMAL},
      </if>
      <if test="totalMoney != null">
        total_money = #{totalMoney,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="freightAmount != null">
        freight_amount = #{freightAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponShopAmount != null">
        coupon_shop_amount = #{couponShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingShopAmount != null">
        marketing_shop_amount = #{marketingShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="shopTotalDiscount != null">
        shop_total_discount = #{shopTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="couponPlatformAmount != null">
        coupon_platform_amount = #{couponPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingPlatformAmount != null">
        marketing_platform_amount = #{marketingPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="platformTotalDiscount != null">
        platform_total_discount = #{platformTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="hireMoney != null">
        hire_money = #{hireMoney,jdbcType=DECIMAL},
      </if>
      <if test="payableCommission != null">
        payable_commission =  #{payableCommission,jdbcType=DECIMAL},
      </if>
      <if test="penaltyAmount != null">
        penalty_amount = #{penaltyAmount,jdbcType=DECIMAL},
      </if>
      <if test="statementTotalMoney != null">
        statement_total_money = #{statementTotalMoney,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=TINYINT},
      </if>
      <if test="billPaymentStatus != null">
        bill_payment_status = #{billPaymentStatus,jdbcType=TINYINT},
      </if>
      <if test="billPaymentTime != null">
        bill_payment_time = #{billPaymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remitStatus != null">
        remit_status = #{remitStatus,jdbcType=TINYINT},
      </if>
      <if test="remitTime != null">
        remit_time = #{remitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xyy.ec.pop.po.PopBillPaymentPo">
    update tb_xyy_pop_bill_payment
    set org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      flow_no = #{flowNo,jdbcType=VARCHAR},
      product_money = #{productMoney,jdbcType=DECIMAL},
      total_money = #{totalMoney,jdbcType=DECIMAL},
      money = #{money,jdbcType=DECIMAL},
      freight_amount = #{freightAmount,jdbcType=DECIMAL},
      coupon_shop_amount = #{couponShopAmount,jdbcType=DECIMAL},
      marketing_shop_amount = #{marketingShopAmount,jdbcType=DECIMAL},
      shop_total_discount = #{shopTotalDiscount,jdbcType=DECIMAL},
      coupon_platform_amount = #{couponPlatformAmount,jdbcType=DECIMAL},
      marketing_platform_amount = #{marketingPlatformAmount,jdbcType=DECIMAL},
      platform_total_discount = #{platformTotalDiscount,jdbcType=DECIMAL},
      hire_money = #{hireMoney,jdbcType=DECIMAL},
      payable_commission =  #{payableCommission,jdbcType=DECIMAL},
      penalty_amount = #{penaltyAmount,jdbcType=DECIMAL},
      statement_total_money = #{statementTotalMoney,jdbcType=DECIMAL},
      pay_type = #{payType,jdbcType=TINYINT},
      bill_payment_status = #{billPaymentStatus,jdbcType=TINYINT},
      bill_payment_time = #{billPaymentTime,jdbcType=TIMESTAMP},
      remit_status = #{remitStatus,jdbcType=TINYINT},
      remit_time = #{remitTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="queryPopBillList" resultMap="BaseResultMap" parameterType="com.xyy.ec.pop.vo.settle.PopBillPayVo">
    SELECT
    <include refid="Base_Column_List"/>
    FROM tb_xyy_pop_bill_payment
    <where>
      <if test="popBillPay.payType != null">
        AND pay_type = #{popBillPay.payType,jdbcType=TINYINT}
      </if>
      <if test="popBillPay.payTypes != null and popBillPay.payTypes.size() >0 ">
        AND pay_type in
        <foreach collection="popBillPay.payTypes" item="payType" open="(" close=")" separator=",">
          #{payType}
        </foreach>
      </if>
      <if test="null != popBillPay.remitStatus">
        AND remit_status = #{popBillPay.remitStatus,jdbcType=TINYINT}
      </if>
      <if test="null != popBillPay.orgId and '' != popBillPay.orgId">
        AND org_id = #{popBillPay.orgId,jdbcType=VARCHAR}
      </if>
      <if test="null != popBillPay.orgName and '' != popBillPay.orgName">
        AND org_name like CONCAT('%',#{popBillPay.orgName},'%')
      </if>
      <if test="null != popBillPay.flowNo and '' != popBillPay.flowNo">
        AND flow_no = #{popBillPay.flowNo,jdbcType=VARCHAR}
      </if>
      <if test="popBillPay.startCreateTime != null">
        AND create_time >= #{popBillPay.startCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.endCreateTime != null">
        AND create_time &lt;= #{popBillPay.endCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.startRemitTime != null">
        AND remit_time >= #{popBillPay.startRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.endRemitTime != null">
        AND remit_time &lt;= #{popBillPay.endRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.orgIds != null and popBillPay.orgIds.size() &gt; 0">
        AND org_id in
        <foreach collection="popBillPay.orgIds" item="orgId" open="(" close=")" separator=",">
          #{orgId}
        </foreach>
      </if>
      <if test="popBillPay.provIds != null and popBillPay.provIds.size() != 0">
        AND prov_id in
        <foreach collection="popBillPay.provIds" item="provId" open="(" separator="," close=")">
          #{provId}
        </foreach>
      </if>
      <if test="popBillPay.billShareStatus != null">
        AND bill_share_status = #{popBillPay.billShareStatus}
      </if>
      <if test="popBillPay.startBillShareTime != null">
        AND bill_share_time &gt;= #{popBillPay.startBillShareTime}
      </if>
      <if test="popBillPay.endBillShareTime != null">
        AND bill_share_time &lt;= #{popBillPay.endBillShareTime}
      </if>
      <if test="popBillPay.paymentChannel != null">
        AND payment_channel = #{popBillPay.paymentChannel}
      </if>
      <if test="popBillPay.paymentChannels != null and popBillPay.paymentChannels.size() >0 ">
        AND payment_channel in
        <foreach collection="popBillPay.paymentChannels" item="paymentChannel" open="(" close=")" separator=",">
          #{paymentChannel}
        </foreach>
      </if>
      <if test="popBillPay.billNo != null and popBillPay.billNo != ''">
        AND flow_no in (select flow_no from tb_xyy_pop_bill_payment_detail where bill_no = #{popBillPay.billNo})
      </if>
      <if test="popBillPay.deducted != null">
        AND deducted = #{popBillPay.deducted,jdbcType=TINYINT}
      </if>
    </where>
      order by bill_payment_time desc,id desc
    <if test="pageNum != null and pageSize != null">
      limit #{pageNum, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
    </if>
  </select>
  <select id="queryPopBillListCount" resultType="java.lang.Long"  parameterType="com.xyy.ec.pop.vo.settle.PopBillPayVo">
    select count(id) from tb_xyy_pop_bill_payment
    <where>

      <if test="popBillPay.payType != null">
        AND pay_type = #{popBillPay.payType,jdbcType=TINYINT}
      </if>
      <if test="popBillPay.payTypes != null and popBillPay.payTypes.size() >0 ">
        AND pay_type in
        <foreach collection="popBillPay.payTypes" item="payType" open="(" close=")" separator=",">
          #{payType}
        </foreach>
      </if>
      <if test="null != popBillPay.remitStatus">
        AND remit_status = #{popBillPay.remitStatus,jdbcType=TINYINT}
      </if>
      <if test="null != popBillPay.orgId and '' != popBillPay.orgId">
        AND org_id = #{popBillPay.orgId,jdbcType=VARCHAR}
      </if>
      <if test="null != popBillPay.orgName and '' != popBillPay.orgName">
        AND org_name like CONCAT('%',#{popBillPay.orgName},'%')
      </if>
      <if test="null != popBillPay.flowNo and '' != popBillPay.flowNo">
        AND flow_no = #{popBillPay.flowNo,jdbcType=VARCHAR}
      </if>
      <if test="popBillPay.startCreateTime != null">
        AND create_time >= #{popBillPay.startCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.endCreateTime != null">
        AND create_time &lt;= #{popBillPay.endCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.startRemitTime != null">
        AND remit_time >= #{popBillPay.startRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.endRemitTime != null">
        AND remit_time &lt;= #{popBillPay.endRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.orgIds != null and popBillPay.orgIds.size() &gt; 0">
        AND org_id in
        <foreach collection="popBillPay.orgIds" item="orgId" open="(" close=")" separator=",">
          #{orgId}
        </foreach>
      </if>
      <if test="popBillPay.provIds != null and popBillPay.provIds.size() != 0">
        AND prov_id in
        <foreach collection="popBillPay.provIds" item="provId" open="(" separator="," close=")">
          #{provId}
        </foreach>
      </if>
      <if test="popBillPay.billShareStatus != null">
        AND bill_share_status = #{popBillPay.billShareStatus}
      </if>
      <if test="popBillPay.startBillShareTime != null">
        AND bill_share_time &gt;= #{popBillPay.startBillShareTime}
      </if>
      <if test="popBillPay.endBillShareTime != null">
        AND bill_share_time &lt;= #{popBillPay.endBillShareTime}
      </if>
      <if test="popBillPay.paymentChannel != null">
        AND payment_channel = #{popBillPay.paymentChannel}
      </if>
      <if test="popBillPay.paymentChannels != null and popBillPay.paymentChannels.size() >0 ">
        AND payment_channel in
        <foreach collection="popBillPay.paymentChannels" item="paymentChannel" open="(" close=")" separator=",">
          #{paymentChannel}
        </foreach>
      </if>
      <if test="popBillPay.billNo != null and popBillPay.billNo != ''">
        AND flow_no in (select flow_no from tb_xyy_pop_bill_payment_detail where bill_no = #{popBillPay.billNo})
      </if>
      <if test="popBillPay.deducted != null">
        AND deducted = #{popBillPay.deducted,jdbcType=TINYINT}
      </if>
    </where>
  </select>
  <select id="queryPopBillPayStatis" resultType="com.xyy.ec.pop.vo.settle.PopBillPayStatisVo"  parameterType="com.xyy.ec.pop.po.PopBillPaymentPo">
    select SUM(hire_money) hireMoneyTotal,SUM(statement_total_money) statementTotalMoneyTotal,
           SUM(payable_commission) payableCommissionTotal,SUM(deducted_commission) deductedCommissionTotal
    from tb_xyy_pop_bill_payment
    WHERE 1=1
    <if test="popBillPay.payType != null">
      AND pay_type = #{popBillPay.payType,jdbcType=TINYINT}
    </if>
    <if test="popBillPay.payTypes != null and popBillPay.payTypes.size() >0 ">
      AND pay_type in
      <foreach collection="popBillPay.payTypes" item="payType" open="(" close=")" separator=",">
        #{payType}
      </foreach>
    </if>
    <if test="null != popBillPay.remitStatus">
      AND remit_status = #{popBillPay.remitStatus,jdbcType=TINYINT}
    </if>
      <if test="null != popBillPay.orgId and '' != popBillPay.orgId">
        AND org_id = #{popBillPay.orgId,jdbcType=VARCHAR}
      </if>
      <if test="null != popBillPay.orgName and '' != popBillPay.orgName">
        AND org_name like CONCAT('%',#{popBillPay.orgName},'%')
      </if>
      <if test="null != popBillPay.flowNo and '' != popBillPay.flowNo">
        AND flow_no = #{popBillPay.flowNo,jdbcType=VARCHAR}
      </if>
      <if test="popBillPay.startCreateTime != null">
        AND create_time >= #{popBillPay.startCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.endCreateTime != null">
        AND create_time &lt;= #{popBillPay.endCreateTime, jdbcType=TIMESTAMP}
      </if>
    <if test="popBillPay.startRemitTime != null">
      AND remit_time >= #{popBillPay.startRemitTime, jdbcType=TIMESTAMP}
    </if>
    <if test="popBillPay.endRemitTime != null">
      AND remit_time &lt;= #{popBillPay.endRemitTime, jdbcType=TIMESTAMP}
    </if>
    <if test="popBillPay.orgIds != null and popBillPay.orgIds.size() &gt; 0">
      AND org_id in
      <foreach collection="popBillPay.orgIds" item="orgId" open="(" close=")" separator=",">
        #{orgId}
      </foreach>
    </if>
    <if test="popBillPay.provIds != null and popBillPay.provIds.size() != 0">
      AND prov_id in
      <foreach collection="popBillPay.provIds" item="provId" open="(" separator="," close=")">
        #{provId}
      </foreach>
    </if>
    <if test="popBillPay.billShareStatus != null">
      AND bill_share_status = #{popBillPay.billShareStatus}
    </if>
    <if test="popBillPay.startBillShareTime != null">
      AND bill_share_time &gt;= #{popBillPay.startBillShareTime}
    </if>
    <if test="popBillPay.endBillShareTime != null">
      AND bill_share_time &lt;= #{popBillPay.endBillShareTime}
    </if>
    <if test="popBillPay.paymentChannel != null">
      AND payment_channel = #{popBillPay.paymentChannel}
    </if>
    <if test="popBillPay.paymentChannels != null and popBillPay.paymentChannels.size() >0 ">
      AND payment_channel in
      <foreach collection="popBillPay.paymentChannels" item="paymentChannel" open="(" close=")" separator=",">
        #{paymentChannel}
      </foreach>
    </if>
    <if test="popBillPay.billNo != null and popBillPay.billNo != ''">
      AND flow_no in (select flow_no from tb_xyy_pop_bill_payment_detail where bill_no = #{popBillPay.billNo})
    </if>
    <if test="null != popBillPay.deducted">
      AND deducted = #{popBillPay.deducted,jdbcType=TINYINT}
    </if>
  </select>
  <select id="selectByFlowNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_xyy_pop_bill_payment
    where flow_no = #{flowNo}
  </select>
  <select id="queryByOrderNos" resultType="java.lang.String">
    SELECT flow_no as flowNo
    FROM tb_xyy_pop_bill_payment
    <where>
      <if test="null != popBillPay.payType">
        AND pay_type = #{popBillPay.payType,jdbcType=TINYINT}
      </if>
      <if test="null != popBillPay.remitStatus">
        AND remit_status = #{popBillPay.remitStatus,jdbcType=TINYINT}
      </if>
      <if test="null != popBillPay.orgId and '' != popBillPay.orgId">
        AND org_id = #{popBillPay.orgId,jdbcType=VARCHAR}
      </if>
      <if test="null != popBillPay.orgName and '' != popBillPay.orgName">
        AND org_name like CONCAT('%',#{popBillPay.orgName},'%')
      </if>
      <if test="null != popBillPay.flowNo and '' != popBillPay.flowNo">
        AND flow_no = #{popBillPay.flowNo,jdbcType=VARCHAR}
      </if>
      <if test="popBillPay.startCreateTime != null">
        AND create_time >= #{popBillPay.startCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.endCreateTime != null">
        AND create_time &lt;= #{popBillPay.endCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.startRemitTime != null">
        AND remit_time >= #{popBillPay.startRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.endRemitTime != null">
        AND remit_time &lt;= #{popBillPay.endRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBillPay.orgIds != null and popBillPay.orgIds.size() &gt; 0">
        AND org_id in
        <foreach collection="popBillPay.orgIds" item="orgId" open="(" close=")" separator=",">
          #{orgId}
        </foreach>
      </if>
      <if test="popBillPay.deducted != null">
        AND deducted = #{popBillPay.deducted,jdbcType=TINYINT}
      </if>
    </where>
  </select>
  <select id="queryPopBillPayByFlowNoList" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_xyy_pop_bill_payment
    where flow_no in
    <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
  <update id="batchUpdateById">
    <if test="list != null and list.size() &gt; 0">
      <foreach collection="list" item="billPay" separator=";">
        update tb_xyy_pop_bill_payment
        <set>
          <if test="billPay.remitStatus != null">
            remit_status = #{billPay.remitStatus,jdbcType=TINYINT},
          </if>
          <if test="billPay.remitTime != null">
            remit_time = #{billPay.remitTime,jdbcType=TIMESTAMP},
          </if>
        </set>
        where id = #{billPay.id,jdbcType=BIGINT}
      </foreach>
    </if>
  </update>
</mapper>