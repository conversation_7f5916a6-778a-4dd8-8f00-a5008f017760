package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.server.api.admin.api.CommissionActivityApi;
import com.xyy.ec.pop.server.api.admin.dto.*;
import com.xyy.ec.pop.vo.activity.ActivityQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class CommissionActivityRemote {

    @Reference
    private CommissionActivityApi commissionActivityApi;

    public PageInfo<CommissionActivityRespDto> listByPage(ActivityQueryVo activityQueryVo) {
        try {
            log.info("#CommissionActivityApi.listByPage#activityQueryVo:{}", activityQueryVo);
            CommissionActivityQueryDto commissionActivityQueryDto = new CommissionActivityQueryDto();
            BeanUtils.copyProperties(activityQueryVo, commissionActivityQueryDto);
            ApiRPCResult<PageInfo<CommissionActivityRespDto>> result = commissionActivityApi.listByPage(commissionActivityQueryDto);
            log.info("#CommissionActivityApi.listByPage#activityQueryVo:{} return:{}", JSON.toJSONString(activityQueryVo), JSON.toJSONString(result));

            return result.isSuccess() ? result.getData() : new PageInfo<>();
        } catch (Exception e) {
            log.error("#CommissionActivityApi.listByPage#activityQueryVo:{}", activityQueryVo, e);
            return new PageInfo<>();
        }
    }

    public Boolean save(CommissionActivityDto commissionActivityDto) {
        try {
            log.info("#CommissionActivityApi.save#commissionActivityDto:{}", commissionActivityDto);
            ApiRPCResult<Boolean> result = commissionActivityApi.save(commissionActivityDto);
            log.info("#CommissionActivityApi.save#commissionActivityDto:{} return:{}", JSON.toJSONString(commissionActivityDto), JSON.toJSONString(result));
            if (result.isFail()) {
                throw new ServiceRuntimeException(result.getErrMsg());
            }
            return result.getData();
        } catch (ServiceRuntimeException e) {
            throw new ServiceRuntimeException(e.getMessage());
        } catch (Exception e) {
            log.error("#CommissionActivityApi.save#commissionActivityDto:{}", commissionActivityDto, e);
            throw new ServiceRuntimeException("创建失败");
        }
    }

    public Boolean update(CommissionActivityDto commissionActivityDto) {
        try {
            log.info("#CommissionActivityApi.update#commissionActivityDto:{}", commissionActivityDto);
            ApiRPCResult<Boolean> result = commissionActivityApi.update(commissionActivityDto);
            log.info("#CommissionActivityApi.update#commissionActivityDto:{} return:{}", JSON.toJSONString(commissionActivityDto), JSON.toJSONString(result));
            if (result.isFail()) {
                throw new ServiceRuntimeException(result.getErrMsg());
            }
            return result.getData();
        } catch (ServiceRuntimeException e) {
            throw new ServiceRuntimeException(e.getMessage());
        } catch (Exception e) {
            log.error("#CommissionActivityApi.update#commissionActivityDto:{}", commissionActivityDto, e);
            throw new ServiceRuntimeException("创建失败");
        }
    }

    public Map<Integer, String> businessTypeList() {
        try {
            ApiRPCResult<Map<Integer, String>> result = commissionActivityApi.businessTypeList();
            log.info("#CommissionActivityApi.listByPage#return:{}", JSON.toJSONString(result));

            return result.isSuccess() ? result.getData() : new HashMap<>();
        } catch (Exception e) {
            log.error("#CommissionActivityApi.listByPage", e);
            return new HashMap<>();
        }
    }

    public PageInfo<CommissionActivityMonthDto> listActivityMonthByPage(Long activityId, Integer pageNum, Integer pageSize) {
        try {
            log.info("#CommissionActivityApi.listActivityMonthByPage#activityId:{}", activityId);
            ApiRPCResult<PageInfo<CommissionActivityMonthDto>> result = commissionActivityApi.listActivityMonthByPage(activityId, pageNum, pageSize);
            log.info("#CommissionActivityApi.listActivityMonthByPage#activityId:{} return:{}", JSON.toJSONString(activityId), JSON.toJSONString(result));

            return result.isSuccess() ? result.getData() : new PageInfo<>();
        } catch (Exception e) {
            log.error("#CommissionActivityApi.listActivityMonthByPage#activityId:{}", activityId, e);
            return new PageInfo<>();
        }
    }

    public Boolean updateOffline(Long activityId, String userName) {
        try {
            log.info("#CommissionActivityApi.updateOffline#activityId:{}", activityId);
            ApiRPCResult<Boolean> result = commissionActivityApi.updateOffline(activityId, userName);
            log.info("#CommissionActivityApi.updateOffline#activityId:{} return:{}", JSON.toJSONString(activityId), JSON.toJSONString(result));
            if (result.isFail()) {
                throw new ServiceRuntimeException(result.getErrMsg());
            }
            return result.getData();
        } catch (ServiceRuntimeException e) {
            throw new ServiceRuntimeException(e.getMessage());
        } catch (Exception e) {
            log.error("#CommissionActivityApi.updateOffline#activityId:{}", activityId, e);
            throw new ServiceRuntimeException("操作失败");
        }
    }

    public List<CommissionActivityLogDto> listActivityLog(Long activityId) {
        try {
            log.info("#CommissionActivityApi.listActivityLog#activityId:{}", activityId);
            ApiRPCResult<List<CommissionActivityLogDto>> result = commissionActivityApi.listActivityLog(activityId);
            log.info("#CommissionActivityApi.listActivityLog#activityId:{} return:{}", activityId, JSON.toJSONString(result));

            return result.isSuccess() ? result.getData() : Collections.emptyList();
        } catch (Exception e) {
            log.error("#CommissionActivityApi.listActivityLog#activityQueryVo:{}", activityId, e);
            return Collections.emptyList();
        }
    }

    public Object updateCommissionActivity(int notStarted, int inProgress) {
        try {
            ApiRPCResult rpcResult = commissionActivityApi.updateCommissionActivity(notStarted, inProgress);
            if(rpcResult.isFail()){
                log.error("#CommissionActivityRemote.updateCommissionActivity#error,Rpc请求失败，参数：notStarted:{},inProgress:{},返回：{}",notStarted, inProgress, JSON.toJSONString(rpcResult));
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("#CommissionActivityRemote.updateCommissionActivity#error,参数：notStarted:{},inProgress:{}",notStarted, inProgress,e);
            throw new ServiceRuntimeException("批量修改活动状态调度任务失败");
        }
    }
}
