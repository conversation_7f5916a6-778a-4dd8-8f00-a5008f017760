package com.xyy.ec.pop.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleBillDto;
import com.xyy.ec.pop.server.api.seller.dto.PopInvoiceApplyDto;
import com.xyy.ec.pop.server.api.seller.dto.PopInvoiceApplyForExportDto;
import com.xyy.ec.pop.server.api.seller.param.PopInvoiceApplyAdminParam;
import com.xyy.ec.pop.vo.AdjustiveBillSettleResultVo;
import com.xyy.ec.pop.vo.InvoiceApplyExportVo;

import java.util.List;

public interface InvoiceApplyService {
    PageInfo<PopInvoiceApplyDto> queryInvoiceListForPage(PopInvoiceApplyAdminParam popInvoiceApplyAdminParam);

    PopInvoiceApplyDto getInvoiceBaseInfo(String invoiceApplyNo);

    PageInfo<PopCommissionSettleBillDto> queryInvoiceDetailListForPage(String invoiceApplyNo, Integer pageNum, Integer pageSize);

    List<PopInvoiceApplyForExportDto> exportInvoiceList(PopInvoiceApplyAdminParam popInvoiceApplyAdminParam);

    int queryExportCount(PopInvoiceApplyAdminParam popInvoiceApplyAdminParam);

    List<String> selectEffectiveInvoiceApplyNo(List<String> invoiceApplyNos);

    void batchUpdateTrackingNo(List<PopInvoiceApplyDto> popInvoiceApplyDtos);

    AdjustiveBillSettleResultVo batchImport(List<InvoiceApplyExportVo> vos);
}
