package com.xyy.ec.pop.service.settle;

import com.xyy.ec.pop.po.PopBillPaymentDetailPo;
import com.xyy.ec.pop.po.PopBillPaymentPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/1 10:49
* @table
*/
public interface PopBillPaymentDetailService {
    int insert(PopBillPaymentDetailPo record);

    int insertSelective(PopBillPaymentDetailPo record);

    PopBillPaymentDetailPo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PopBillPaymentDetailPo record);

    int updateByPrimaryKey(PopBillPaymentDetailPo record);

    List<PopBillPaymentDetailPo> queryPopBillPaymentDetail(PopBillPaymentDetailPo popBillPaymentDetailPo,Integer pageNum,Integer pageSize);

    Long queryPopBillPaymentDetailCount( List<String> flowNoList);

    List<PopBillPaymentDetailPo> queryPopBillPaymentDetailByFlowNoList(List<String> flowNoList);

}
