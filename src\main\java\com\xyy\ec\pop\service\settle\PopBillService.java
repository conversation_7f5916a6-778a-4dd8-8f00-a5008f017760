package com.xyy.ec.pop.service.settle;

import com.xyy.ec.pop.po.PopBillPo;
import com.xyy.ec.pop.vo.settle.PopBillStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/1 10:38
* @table
*/
public interface PopBillService {
    int insert(PopBillPo record);

    int insertSelective(PopBillPo record);

    PopBillPo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PopBillPo record);

    int updateByPrimaryKey(PopBillPo record);

    List<PopBillPo> queryPopBillList(PopBillVo popBillVo, Integer pageNum, Integer pageSize);

    Long queryPopBillListCount(PopBillVo popBillVo);

    PopBillStatisVo queryPopBillStatis(PopBillVo popBillVo);

    PopBillPo selectByBillNo(String billNo);

    List<String> queryByOrderNos (PopBillPo popBillPo);

    List<PopBillPo> queryPopBillByBillNoList(List<String> billNoList);

    void batchUpdateById(List<PopBillPo> popBillPos);

}
