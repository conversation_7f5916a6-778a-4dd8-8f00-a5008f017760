package com.xyy.ec.pop.vo;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.server.api.seller.constant.ActivityConstant;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 返回结果实体类
 */
@Data
public class ResponseVo<T> implements Serializable {
    private static final long serialVersionUID = 5147285805394771156L;
    public int code;
    public String message;
    public T data;
    public static final String MSG_SUCCESS = "success";
    public static final int CODE_SUCCESS = 0;
    public static final int CODE_ERROR = 1;

    public ResponseVo() {
        this.code = CODE_SUCCESS;
        this.message = MSG_SUCCESS;
        this.data = null;
    }

    public ResponseVo(T data) {
        this.code = CODE_SUCCESS;
        this.message = MSG_SUCCESS;
        this.data = data;

    }

    public ResponseVo(String message) {
        this.code = CODE_ERROR;
        this.message = message;
        this.data = null;
    }

    public ResponseVo(int code, String message) {
        this.code = code;
        this.message = message;
        this.data = null;
    }

    public ResponseVo(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> ResponseVo<T> errRest(String message) {
        ResponseVo<T> responseVo = new ResponseVo<>(message);
        return responseVo;
    }

    public static <T> ResponseVo<T> errRest(int code,String message) {
        ResponseVo<T> responseVo = new ResponseVo<>(code,message);
        return responseVo;
    }

    public static <T> ResponseVo<T> errRest(String message, T data) {
        return new ResponseVo<>(CODE_ERROR, message, data);
    }

    public static <T> ResponseVo<T> errCodeRest(String message) {
        if (StringUtils.isEmpty(message) || !message.startsWith(ActivityConstant.PREFIX_RESPONSE_CODE)) {
            return errRest(message);
        }

        String[] split = message.split(":");
        ResponseVo<T> responseVo = new ResponseVo<>(Integer.parseInt(split[split.length - 1]), split[split.length - 2]);
        return responseVo;
    }

    public static <T> ResponseVo<T> successResult(T data) {
        return new ResponseVo<>(data);
    }

    public static <T> ResponseVo<PageInfo<T>> pack(List<T> list, long total, int pages) {
        PageInfo<T> page = new PageInfo<>(list);
        page.setTotal(total);
        page.setPages(pages);
        return successResult(page);
    }
    public static <T> ResponseVo<T> successResultNotData() {
        return new ResponseVo<>();
    }

    public static <T> ResponseVo<T> successResultNotData(String msg) {
        return new ResponseVo<>(CODE_SUCCESS, msg);
    }

    public  boolean isSuccess() {
        return this.code == CODE_SUCCESS;
    }

    public boolean isFail() {
        return this.code != CODE_SUCCESS;
    }
}
