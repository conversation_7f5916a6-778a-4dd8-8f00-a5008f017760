package com.xyy.ec.pop.marketing.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomerGroupQueryParam implements Serializable {

    private static final long serialVersionUID = -589886766210943634L;

    /**
     * 人群id
     */
    private Long id;

    /**
     * 人群名称
     */
    private String groupName;

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;

    /**
     * 是否是多组。null：不限制。true：只查询多组。false：只查询单组。
     */
    private Boolean isMultiBundle;

    /**
     * 当前页码，从1开始
     */
    private Integer pageNum;

    /**
     * 当前页显示的最大条数，最小值为1
     */
    private Integer pageSize;

}
