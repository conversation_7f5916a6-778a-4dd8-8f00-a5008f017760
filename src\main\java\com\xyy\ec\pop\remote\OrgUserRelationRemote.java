package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.merchant.api.OrgUserRelationApi;
import com.xyy.ec.pop.server.api.merchant.dto.OrgUserRelationDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class OrgUserRelationRemote {
    @Reference
    private OrgUserRelationApi orgUserRelationApi;

    public List<OrgUserRelationDto> queryUserRelations(List<String> orgIds, List<Long> merchantIds) {
        ApiRPCResult<List<OrgUserRelationDto>> apiRPCResult = orgUserRelationApi.queryUserRelations(orgIds, merchantIds);
        if (!apiRPCResult.isSuccess() || apiRPCResult.getData() == null) {
            return new ArrayList<>();
        }
        return apiRPCResult.getData();
    }
}
