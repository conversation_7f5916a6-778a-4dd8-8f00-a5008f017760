$(document).ready(function () {

    $(function () {
        $(".tooltip-options a").tooltip({html: true});
    });
    //1.初始化项目大类列表
    new applyPendingList.tableInit().Init();
    //2.初始化操作按钮
    new applyPendingList.buttonInit().Init();

    initSearchTime();

	/* 导出列表 */
	$('#btn_export').on('click', function (e) {
        var ps = [];
        var pms = queryParams();
        for (var k in pms) {	//组装参数
            var pm = pms[k]
            if (pm)
                ps.push(k + '=' + pm);
        }
        window.location.href = basePath + 'accountStatement/downloadExcel?' + encodeURI(ps.join('&'));
	});
});

var applyPendingList = $.applyPendingList = ({
    //项目大类列表初始化
    tableInit: function () {
        var applyPendingTableObj = new Object();
        //初始化Table
        applyPendingTableObj.Init = function () {
            var $table = $('#tb_pendingList');
            $table.bootstrapTable({
                url: '/accountStatement/list', //请求后台的URL（*）
                method: 'get', //请求方式（*）
                contentType: 'application/x-www-form-urlencoded',
                dataType: 'json', //传入的类型
                toolbar: '#toolbar', //工具按钮用哪个容器
                striped: true, //是否显示行间隔色
                cache: false, //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                pagination: true, //是否显示分页（*）
                sortable: false, //是否启用排序
                sortOrder: "asc", //排序方式
                queryParams: applyPendingTableObj.queryParams, //传递参数（*）
                sidePagination: "server", //分页方式：client客户端分页，server服务端分页（*）
                formatLoadingMessage: function () {
                    return '请稍后,正在加载中...';
                },
                showFooter: false,
                pageNumber: 1, //初始化加载第一页，默认第一页
                pageSize: 20, //每页的记录行数（*）
                pageList: [10, 20, 50, 100], //可供选择的每页的行数（*）
                strictSearch: true,
                onLoadSuccess: function (data) {
                },
                clickToSelect: true, //是否启用点击选中行
                //height: 550, //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                onClickRow: function (row, $element) {
                    $('.success').removeClass('success');
                    $($element).addClass('success');

                    var auditingState = row.auditingState;
                    var statementState = row.statementState;
                    if(auditingState == 1){
                        $("#btn_auditing").show();
                    }else{
                        $("#btn_auditing").hide();
                    }
                    if(statementState == 1 && auditingState ==3){
                        $("#btn_make_money").show();
                    }else{
                        $("#btn_make_money").hide();
                    }
                },
                // onDblClickRow: function (row) {
                //     var id = row.id;
                //     window.location.href= "/accountStatement/detail?id="+id+"&isLook=1";
                // },
                uniqueId: "id", //每一行的唯一标识，一般为主键列
                columns: [{
                    field: 'checked',
                    align: 'center',
                    checkbox: true
                    // formatter:function (value,row,index) {
                    //     if(row.auditingState == 2 && row.statementState==1){
                    //         return {
                    //             disabled:true,
                    //             checked:true
                    //         }
                    //     }
                    // }
                },{
                    field: 'orgId',
                    title: '商户编号',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'orgName',
                    title: '商户名称',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'orderNo',
                    title: '订单号',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'orderTotalMoney',
                    title: '订单总额',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'shopDiscountMoney',
                    title: '店铺优惠总额',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'platformDiscountMoney',
                    title: '平台优惠总额',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'refundCouponShopAmount',
                    title: '退款单店铺优惠券金额',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'freightAmount',
                    title: '运费金额',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'refuntTotalMoney',
                    title: '退款总额',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'hireMoney',
                    title: '佣金总额',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'statementTotalMoney',
                    title: '结算总合计',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'createTime',
                    title: '生成时间',
                    align: 'center',
                    sortable: true,
                    formatter:function (val, row) {
                        if (val != null && val != "") {
                            var time = ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
                            if (time == '1970-01-01 00:00:00') {
                                time = ''
                            }
                            return time;
                        } else {
                            return "";
                        }
                    }
                }, {
                    field: 'auditingState',
                    title: '审核状态',
                    align: 'center',
                    sortable: true,
                    formatter: function (val, row) {
                        switch (val) {
                            case 0:
                                return '<span style="color:#99CC00">未审核</span>';
                                break;
                            case 1:
                                return '<span style="color:#99CC33">待审核</span>';
                                break;
                            case 3:
                                return '<span style="color:#99CC33">审核完成</span>';
                                break;
                            default:
                                return '<span style="color:#99CC99">审核失败</span>';
                                break;
                        }
                    }
                }, {
                    field: 'statementState',
                    title: '结算状态',
                    align: 'center',
                    sortable: true,
                    formatter: function (val, row) {
                        switch (val) {
                            case 0:
                                return '<span style="color:#99CC00">未结算</span>';
                                break;
                            case 1:
                                return '<span style="color:#99CC33">待结算</span>';
                                break;
                            case 2:
                                return '<span style="color:#99CC33">结算完成</span>';
                                break;
                            default:
                                return '<span style="color:#99CC99">结算失败</span>';
                                break;
                        }
                    }
                }, {
                    field: 'billingStatus',
                    title: '开票状态',
                    align: 'center',
                    sortable: true,
                    formatter: function (val, row) {
                        switch (val) {
                            case 0:
                                return '<span style="color:#000000">未开票</span>';
                                break;
                            case 1:
                                return '<span style="color:#000000">已开票</span>';
                                break;
                            default:
                                return '<span style="color:#000000"></span>';
                                break;
                        }
                    }
                }, {
                    field: 'billingTime',
                    title: '开票日期',
                    align: 'center',
                    sortable: true,
                    formatter:function (val, row) {
                        if (val != null && val != "") {
                            var time = ToolUtil.dateFormat(val, 'yyyy-MM-dd');
                            if (time == '1970-01-01') {
                                time = ''
                            }
                            return time;
                        } else {
                            return "";
                        }
                    }
                }]
            });
        };
        //查询的参数
        applyPendingTableObj.queryParams = function (params) {
            // return {
            //     //每页显示条数
            //     limit: params.limit,
            //     //起始页数
            //     offset: params.offset,
            //     //排序字段
            //     property: (params.sort && params.sort !== '') ? ToolUtil.underscoreName(params.sort) : '',
            //     //排序方式
            //     direction: params.order,
            //     //查询条件（将对象属性封装成查询实体对象）
            //     keyword: $.trim($("#keyword").val()),
            //     merchantName: $.trim($("#orderNo").val()),
            //     statementState: $.trim($("#statementState").val()),
            //     auditingState: $.trim($("#auditingState").val()),
            //     createBeginDateStr: $.trim($("#createBeginDateStr").val()),
            //     createEndDateStr: $.trim($("#createEndDateStr").val())
            // };
            return queryParams(params);
        };
        return applyPendingTableObj;
    },
    buttonInit: function () {
        var $table = $('#tb_pendingList');
        var oInit = new Object();
        oInit.Init = function () {

            function getSelectedRow() {
                var index = $table.find('tr.success').data('index');
                return $table.bootstrapTable('getData')[index];
            }

            //初始化页面上面的按钮事件
            //条件查询事件
            $("#btn_query").click(function () {
                // $table.bootstrapTable('refresh');
                $table.bootstrapTable('selectPage',1);
            });

            //条件清空事件
            $("#btn_clear").click(function () {
                $("#orgId").val("");
                $("#orgName").val("");
                $("#orderNo").val("");
                $("#auditingState").val("");
                $("#statementState").val("");
                $("#billingStatus").val("");
                $("#startCreateTime").val("");
                $("#endCreateTime").val("");
                $("#startCreateTime1").val("");
                $("#endCreateTime1").val("");

                $('#startCreateTime').datetimepicker('remove');
                $('#endCreateTime').datetimepicker('remove');
                $('#startCreateTime1').datetimepicker('remove');
                $('#endCreateTime1').datetimepicker('remove');
                initSearchTime();
                // $table.bootstrapTable('refresh');
                $table.bootstrapTable('selectPage',1);
            });

            //审核
            $("#btn_auditing").click(function () {
                var row = getSelectedRow();
                if(row){
                    var id = row.id;
                    window.location.href= "/accountStatement/detail?id="+id;
                }else {
                    LayTool.alert("请选择一条记录");
                }
            });

            //确认打款
            $("#btn_make_money").click(function () {
                var row = getSelectedRow();
                if(!!!row){
                    LayTool.alert("请选择一条记录");
                    return;
                }

                layer.confirm('请确认当前对账单已线下打款？', {
                    btn : [ '确定', '取消' ]
                }, function(i) {
                    // var row = getSelectedRow();
                    if(row) {
                        var id = row.id;
                        $.ajax({
                            type: 'get',
                            data: {"id": id},
                            cache: false,
                            url: '/accountStatement/makeMoney',
                            dataType: 'json',
                            success: function (data) {
                                if (data != null) {
                                    layer.alert(data.msg);
                                    $table.bootstrapTable('refresh');
                                } else {
                                    layer.alert("确认打款失败");
                                }
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                layer.alert('网络异常');
                            }
                        });
                    }
                });
            });

            //查看日志
            $("#btn_log").click(function () {
                var row = getSelectedRow();
                if (row) {
                    var id = row.id;

                    LayTool.open({
                        title: "查看日志",
                        area: ['500px', '400px'],
                        content: [
                            basePath + 'accountStatement/accountLog?accountId=' + id, 'yes'
                        ],
                        end: function () {
                            $table.bootstrapTable('refresh');
                        }
                    });
                }else {
                    LayTool.alert("请选择一条记录");
                }
            });

            //确认开票
            $("#btn_confirm").click(function () {
                var isCheck = false,
                    isState = false,
                    isBill = false;
                var productList = new Array();
                var productRows = $('#tb_pendingList').bootstrapTable('getData');
                $.each(productRows, function (i, n) {
                    if (n.checked) {
                        isCheck = true;
                        return;
                    }
                });
                $.each(productRows, function (i, n) {
                    if (n.checked && n.billingStatus) {
                        isBill = true;
                        return;
                    }
                });
                $.each(productRows, function (i, n) {
                    if (n.checked && (n.statementState != 2)) {
                        isState = true;
                        return;
                    }
                });
                if(isCheck) {
                    if(!isState){
                        if(isBill){
                            layer.confirm('已开票订单不能重复确认，请检查已勾选的订单开票状态', {
                                offset: '20%',
                                btn: ['确定', '取消']
                            }, function (i) {
                                layer.close(i);
                            });
                        }else {
                            layer.confirm('开票日期：<input type="text" id="billInputValue" name="billInputValue"/>', {
                                offset: '20%',
                                btn: ['确定', '取消'],
                                success: function (layero, index) {
                                    $(layero).find("#billInputValue").datetimepicker({
                                        format: 'yyyy-mm-dd',
                                        language: 'zh-CN',
                                        bootcssVer: 3,
                                        autoclose: true,
                                        todayBtn: true,
                                        minView: "month"
                                    })
                                    if($("#billInputValue")){
                                        $(".datetimepicker").css("position","fixed")
                                    }
                                }
                            }, function (i) {
                                var billInputValue = $("#billInputValue").val();
                                $.each(productRows, function (i, n) {
                                    if (n.checked) {
                                        productList.push(n.id);
                                    }
                                });
                                $.ajax({
                                    type: 'post',
                                    data: JSON.stringify(productList),
                                    cache: false,
                                    url: '/accountStatement/confirmBillingTimeBatch?billingTime=' + billInputValue,
                                    dataType: 'json',
                                    contentType: 'application/json; charset=UTF-8',
                                    success: function (data) {
                                        if (data.status == "success") {
                                            top.layer.alert("确认开票成功");
                                            layer.close(i);
                                            $table.bootstrapTable('refresh');
                                        } else {
                                            top.layer.alert(data.errorMsg);
                                        }
                                    },
                                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                                        layer.alert('网络异常');
                                    }
                                });
                            });
                        }
                    }else{
                        layer.confirm('存在未结算的订单，不允许开票', {
                            offset: '20%',
                            btn: ['确定', '取消']
                        }, function (i) {
                            layer.close(i);
                        });
                    }
                }else{
                    LayTool.alert("请选择一条记录");
                }
            });

            //详情
            // $('.queryDetail').click(function () {
            //     var row = getSelectedRow();
            //     if (row) {
            // 	    window.location.href = '/product/detail?id=' + row.id;
            //     } else {
            //         LayTool.alert("请选择一条记录");
            //     }
            // });
        };
        return oInit;
    }
});


function datetimeFormatter(val) {
    if (val != null) {
        return ToolUtil.dateFormat(val, 'yyyy-MM-dd');
    }
}

var queryParams = function(params) {
    params = params || {};
    return {
        //每页显示条数
        limit: params.limit,
        //起始页数
        offset: params.offset,
        //排序字段
        property: (params.sort && params.sort !== '') ? ToolUtil.underscoreName(params.sort) : '',
        //排序方式
        direction: params.order,
        //查询条件（将对象属性封装成查询实体对象）

        orgId:$.trim($("#orgId").val()),
        orgName:$.trim($("#orgName").val()),
        orderNo: $.trim($("#orderNo").val()),
        statementState: $.trim($("#statementState").val()),
        auditingState: $.trim($("#auditingState").val()),
        startCreateTime: $.trim($("#startCreateTime").val()==""?"":$("#startCreateTime").val()+" 00:00:00"),
        endCreateTime: $.trim($("#endCreateTime").val()==""?"":$("#endCreateTime").val()+" 23:59:59"),
        startBillingTime: $.trim($("#startCreateTime1").val()==""?"":$("#startCreateTime1").val()+" 00:00:00"),
        endBillingTime: $.trim($("#endCreateTime1").val()==""?"":$("#endCreateTime1").val()+" 23:59:59"),
        billingStatus: $.trim($("#billingStatus").val())
    };
}

function getNowFormatDate() {
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    return currentdate;
}

function initSearchTime(){
    $('#startCreateTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var searchStartTime = $("#startCreateTime").val();
        $("#endCreateTime").datetimepicker('setStartDate', searchStartTime);
    });

    $('#endCreateTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var billEndTime = $("#endCreateTime").val();
        $("#startCreateTime").datetimepicker('setEndDate', billEndTime);
    });

    //开票时间
    $('#startCreateTime1').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var searchStartTime1 = $("#startCreateTime1").val();
        $("#endCreateTime1").datetimepicker('setStartDate', searchStartTime1);
    });

    $('#endCreateTime1').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var billEndTime1 = $("#endCreateTime1").val();
        $("#startCreateTime1").datetimepicker('setEndDate', billEndTime1);
    });
    $('#billInputValue').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var searchStartTime = $("#billInputValue").val();
        $("#billInputValue").datetimepicker('setStartDate', searchStartTime);
    });
}