package com.xyy.ec.pop.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.excel.entity.PopBillSettleExportVo;
import com.xyy.ec.pop.excel.style.SettleExcelExportStyler;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.helper.PopBillSettleHelper;
import com.xyy.ec.pop.po.PopBillSettlePo;
import com.xyy.ec.pop.server.api.order.enums.OrderPayTypeEnums;
import com.xyy.ec.pop.server.api.seller.enums.OrderSettleStatusEnum;
import com.xyy.ec.pop.service.settle.PopBillSettleService;
import com.xyy.ec.pop.service.settle.impl.domain.PopBillSettleDomainService;
import com.xyy.ec.pop.utils.EncodeUtil;
import com.xyy.ec.pop.vo.settle.PopBillSettleStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleVo;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**线下转账结算单
* <AUTHOR>
* @date  2020/12/8 11:56
* @table
*/
@Slf4j
@RequestMapping("/offlineAccountPopBillSettle")
@Controller
public class OfflineAccountPopBillSettleController extends BaseController {

    @Autowired
    private PopBillSettleService popBillSettleService;
    @Autowired
    private PopBillSettleDomainService popBillSettleDomainService;



    /**
     * 列表查询
     * @param popBillSettleVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Object list(PopBillSettleVo popBillSettleVo, PageInfo pageInfo) {
        try {
            if (validateProv(popBillSettleVo)) return ResponseUtils.returnObjectSuccess(new PageInfo<>());
            popBillSettleVo.setPayTypes(OrderPayTypeEnums.getOfflineTypes(popBillSettleVo.getPayType()));
            popBillSettleVo.setPayType(null);
//            popBillSettleVo.setOrderSettlementStatus((byte) OrderSettleStatusEnum.DONE.getStatus());  //只查询已结算
            PageInfo<PopBillSettlePo> pageInfoPop = popBillSettleDomainService.queryPopBillSettleList(popBillSettleVo, pageInfo);
            return ResponseUtils.returnObjectSuccess(pageInfoPop);
        } catch (ServiceRuntimeException e) {
            log.error("查询线下转账结算单列表异常", e);
            return ResponseUtils.returnException(e);
        }
    }

    private boolean validateProv(PopBillSettleVo popBillSettleVo) {
        List<Long> provIds = getProvIds(popBillSettleVo.getProvId());
        if(CollectionUtils.isEmpty(provIds)){
            return true;
        }
        popBillSettleVo.setProvIds(provIds);
        return false;
    }


    /**
     * 列表根据搜索条件统计佣金金额，应结算金额
     * @param popBillSettleVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryPopBillSettleStatis", method = RequestMethod.GET)
    public Object queryPopBillPayStatis(PopBillSettleVo popBillSettleVo) {
        try {
            if (validateProv(popBillSettleVo)) {
                PopBillSettleStatisVo popBillSettleStatisVo = new PopBillSettleStatisVo();
                popBillSettleStatisVo.setHireMoneyTotal(BigDecimal.ZERO);
                popBillSettleStatisVo.setStatementTotalMoneyTotal(BigDecimal.ZERO);
                popBillSettleStatisVo.setActualCommissionMoneyTotal(BigDecimal.ZERO);
                popBillSettleStatisVo.setCommissionDiscountMoneyTotal(BigDecimal.ZERO);
                popBillSettleStatisVo.setProductActualMoneyTotal(BigDecimal.ZERO);
                popBillSettleStatisVo.setDeductedCommissionTotal(BigDecimal.ZERO);
                return ResponseUtils.returnObjectSuccess(popBillSettleStatisVo);
            }
            popBillSettleVo.setPayTypes(OrderPayTypeEnums.getOfflineTypes(popBillSettleVo.getPayType()));
            popBillSettleVo.setPayType(null);
//            popBillSettleVo.setOrderSettlementStatus((byte)OrderSettleStatusEnum.DONE.getStatus());  //只查询已结算
            PopBillSettleStatisVo popBillPaymentPo = popBillSettleDomainService.queryPopBillSettleStatis(popBillSettleVo);
            return ResponseUtils.returnObjectSuccess(popBillPaymentPo);
        } catch (ServiceRuntimeException e) {
            log.error("查询统计线下转账结算单异常", e);
            return ResponseUtils.returnException(e);
        }
    }


    /**
     * 查询列表结算单导出的条数  超过5000条数据，前端给出提示
     * @param popBillSettleVo
     * @return
     */
    @RequestMapping(value = "/queryExprotBillSettleListCount")
    @ResponseBody
    public Object queryExprotBillListCount(PopBillSettleVo popBillSettleVo){
        try{
            popBillSettleVo.setPayTypes(OrderPayTypeEnums.getOfflineTypes(popBillSettleVo.getPayType()));
            popBillSettleVo.setPayType(null);
//            popBillSettleVo.setOrderSettlementStatus((byte)OrderSettleStatusEnum.DONE.getStatus());  //只查询已结算
            Long count = popBillSettleDomainService.queryPopBillListCount(popBillSettleVo);
            return ResponseUtils.returnObjectSuccess(count);
        }catch (Exception e){
            log.error("查询列表结算单导出的条数异常",e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 导出线下转账结算单
     * @param popBillSettleVo
     * @param request
     * @param res
     */
    @RequestMapping(value = "/exportBillSettleList", method = RequestMethod.GET)
    public void exportBillPaymemtList(PopBillSettleVo popBillSettleVo, HttpServletRequest request, HttpServletResponse res){
        try{
            popBillSettleVo.setPayType(Integer.valueOf(OrderPayTypeEnums.OFFLINE_PLATFORM.getType()).byteValue());
            popBillSettleVo.setOrderSettlementStatus((byte)OrderSettleStatusEnum.DONE.getStatus());  //只查询已结算
            Workbook workbook = null;
            ExportParams params = new ExportParams();
            params.setSheetName("结算单信息");
            params.setType(ExcelType.XSSF);
            params.setColor(IndexedColors.BLUE_GREY.index);
            params.setFreezeCol(2);
            params.setStyle(SettleExcelExportStyler.class);
            int pageNum = 1;
            int limit = 500;
            while(true){
                PageInfo pageInfo = new PageInfo();
                pageInfo.setPageNum(pageNum);
                pageInfo.setPageSize(limit);
                PageInfo<PopBillSettlePo> resultPage = popBillSettleDomainService.queryPopBillSettleList(popBillSettleVo, pageInfo);
                List<PopBillSettleExportVo> popBillSettleExportVoList = PopBillSettleHelper.convertPopBillSettleExportVo(resultPage.getList());
                int pageCount = resultPage.getPages();
                workbook = ExcelExportUtil.exportBigExcel(params, PopBillSettleExportVo.class,popBillSettleExportVoList);
                if(pageNum >= pageCount || pageNum >= 2000){
                    break;
                }
                pageNum++;
            }
            ExcelExportUtil.closeExportBigExcel();
            ServletOutputStream out = res.getOutputStream();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fileName = URLEncoder.encode("运营后台导出线下转账结算单-", EncodeUtil.DEFAULT_URL_ENCODING) + sdf.format(new Date());	//对中文进行URL编码，防止乱码
            res.setHeader("Content-type", "application/x-xls; charset=" + EncodeUtil.DEFAULT_URL_ENCODING);
            res.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xlsx");
            res.setContentType("application/msexcel");
            if(null != workbook){
                workbook.write(out);
            }
        }catch (Exception e) {
            log.error("运营后台导出线下转账结算单列表导出异常",e);
        }
    }


}
