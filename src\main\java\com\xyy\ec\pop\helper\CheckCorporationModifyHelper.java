package com.xyy.ec.pop.helper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.xyy.ec.pop.server.api.merchant.dto.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 资质申请变更项比较
 * <AUTHOR>
 */
public class CheckCorporationModifyHelper {
    //不需要比较的字段
    private static Set<String> ignoreKeys = Sets.newHashSet("id", "erpApprovalModelDto", "checkcorporationBusinessDtos",
            "checkCorporationQualificationDtos", "serialVersionUID","createTime","createId","createName",
            "updateTime","updateId","updateName","erpNumber");

    public static Map<String, Object> compare(CheckCorporationDto check, CorporationDto cor) {
        if (check == null || cor == null) {
            return new HashMap<>(0);
        }
        //比较直接字段
        Map<String, Object> map = compare(JSON.parseObject(JSON.toJSONString(check)), JSON.parseObject(JSON.toJSONString(cor)));
        //比较 主营业务
        boolean businessChanged = compare(check.getCheckCorporationBusiness(),cor.getCorporationBusiness());
        map.put("checkcorporationBusinessDtos",businessChanged);
        //证书
        Map<String, Object> qualificationMap = compareQualifications(check.getCheckCorporationQualifications(),cor.getCorporationQualifications());
        map.put("checkCorporationQualificationDtos",qualificationMap);
        return map;
    }

    private static Map<String, Object> compareQualifications(List<CheckCorporationQualificationDto> checks, List<CorporationQualificationDto> cors) {
        Map<String, Object> map = new HashMap<>(checks==null?0:checks.size());
        if(CollectionUtils.isEmpty(checks)){
            return map;
        }
        Map<Long,CorporationQualificationDto> corMap = cors==null?new HashMap<>(0):cors.stream().collect(Collectors.toMap(item->item.getQualificationsDetailId(),item->item));
        for(CheckCorporationQualificationDto check:checks){
            CorporationQualificationDto cor = corMap.get(check.getQualificationsDetailId());
            Map<String, Object> subMap = compare(JSON.parseObject(JSON.toJSONString(check)),cor==null?new HashMap<String,Object>():JSON.parseObject(JSON.toJSONString(cor)));
            map.put(check.getQualificationsDetailId().toString(),subMap);
        }
        return map;
    }

    private static boolean compare(List<CheckCorporationBusinessDto> checks, List<CorporationBusinessDto> cors){
        if(CollectionUtils.isEmpty(checks)){
            return !CollectionUtils.isEmpty(cors);
        }
        if(CollectionUtils.isEmpty(cors)){
            return true;
        }
        List<Long> checkIds = checks.stream().map(item->item.getCategoryId()).collect(Collectors.toList());
        List<Long> cords = cors.stream().map(item->item.getCategoryId()).collect(Collectors.toList());

        //过滤不同的id
        List<Long> ch = checkIds.stream().filter(item->!cords.contains(item)).collect(Collectors.toList());
        List<Long> co = cords.stream().filter(item->!checkIds.contains(item)).collect(Collectors.toList());
        return (CollectionUtils.isNotEmpty(ch)||CollectionUtils.isNotEmpty(co));
    }

    /**
     * 比较两个对象
     * @param check
     * @param cor
     * @return
     */
    private static Map<String, Object> compare(Map<String, Object> check, Map<String, Object> cor) {
        Map<String, Object> map = new HashMap<>(check.size());
        for (Map.Entry<String, Object> entry : check.entrySet()) {
            if (ignoreKeys.contains(entry.getKey())) {
                continue;
            }
            map.put(entry.getKey(), changed(entry.getValue(), cor.get(entry.getKey())));
        }
        return map;
    }

    /**
     * 比较两个值是否有变化，null和null为没有变动,空白认为 没有变化
     *
     * @param now
     * @param before
     * @return
     */
    private static Boolean changed(Object now, Object before) {
        now = trimToNull(now);
        before = trimToNull(before);
        if (now == null) {
            return before != null;
        }
        if(now instanceof BigDecimal && before instanceof BigDecimal){
            return ((BigDecimal)now).compareTo((BigDecimal)before)!=0;
        }
        return !now.equals(before);
    }

    private static Object trimToNull(Object value) {
        if (value instanceof String) {
            value = StringUtils.trimToNull((String) value);
        }
        return value;
    }
}
