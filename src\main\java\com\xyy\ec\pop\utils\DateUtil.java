package com.xyy.ec.pop.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * <AUTHOR> 2014-12-3
 */
public class DateUtil {
    public static final String PATTERN_STANDARD = "yyyy-MM-dd HH:mm:ss";

    public static final String PATTERN_TIMESTAMP = "yyyyMMddHHmmss";

    public static final String PATTERN_DATE = "yyyy-MM-dd";

    private static final String FORMAT = "yyyy-MM-dd HH:mm";


    private static Logger logger = LoggerFactory.getLogger(DateUtil.class);

    private static Date modifyTime(Date date, int hour, int min, int second) {

        if (date == null) {
            return date;
        }
        int year = date.getYear();
        int month = date.getMonth();
        int day = date.getDate();
        return new Date(year, month, day, hour, min, second);
    }

    public static Date modifyStartTime(Date date) {
        return modifyTime(date, 0, 0, 0);
    }

    public static Date modifyEndTime(Date date) {
        return modifyTime(date, 23, 59, 59);
    }


    /**
     * @param timestamp
     * @param pattern
     * @return
     * <AUTHOR> 2014-12-3
     */
    public static String timestamp2String(Timestamp timestamp, String pattern) {
        if (timestamp == null) {
            return "";
        }
        if (StringUtil.isEmpty(pattern)) {
            pattern = PATTERN_STANDARD;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(new Date(timestamp.getTime()));
    }



    /**
     * @param strDate
     * @param pattern
     * @return
     * <AUTHOR> 2014-12-3
     */
    public static Date string2Date(String strDate, String pattern) {
        if (StringUtil.isEmpty(strDate)) {
            return null;
        }
        if (pattern == null || pattern.equals("")) {
            pattern = PATTERN_DATE;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date date = null;

        try {
            date = sdf.parse(strDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return date;
    }

    public static Date string2Date(String strDate, String pattern, Locale locale) {
        if (StringUtil.isEmpty(strDate)) {
            return null;
        }
        if (pattern == null || pattern.equals("")) {
            pattern = PATTERN_DATE;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, locale);
        Date date = null;

        try {
            date = sdf.parse(strDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return date;
    }


    /**
     * 日期转换为字符串
     *
     * @param date
     * @param pattern
     * @return
     * <AUTHOR> 2014-12-3
     */
    public static String date2String(Date date, String pattern) {
        if (date == null) return "";
        if (StringUtil.isEmpty(pattern)) {
            pattern = PATTERN_STANDARD;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, Locale.CHINESE);
        return sdf.format(date);
    }




    /**
     *用当前时间推移指定时长
     * @param add
     * @param CalendarField 时长单位,参考 Calendar
     * @return
     */
    public static Date getDate(int add, int CalendarField) {
        return getDate(new Date(),add,CalendarField);
    }

    /**
     * 用date时间推移指定时长
     * @param date 时间基准
     * @param add 推移时长
     * @param CalendarField 时长单位,参考 Calendar
     * @return
     */
    public static Date getDate(Date date, int add, int CalendarField) {
        if(date==null){
            throw new IllegalArgumentException("参数不能为空");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(CalendarField,add);
        return c.getTime();
    }




    /**
     * 在时间上加天数
     *
     * @param currentDate
     * @param day
     * @return
     */
    public static Date getAddDate(Date currentDate, Integer day) {
        Calendar c = Calendar.getInstance();
        c.clear();
        c.setTime(currentDate);
        c.add(Calendar.DATE, day);
        return c.getTime();
    }


    /**
     * 判断是否是日期字符串
     *
     * @param dateStr
     * @return
     * <AUTHOR> 2015-5-5
     */
    public static boolean isDateString(String dateStr, String pattern) {
        if (StringUtil.isEmpty(dateStr)) {
            return false;
        }
        if (pattern == null || pattern.equals("")) {
            pattern = PATTERN_STANDARD;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            sdf.parse(dateStr);
        } catch (ParseException e) {
            return false;
        }
        return true;
    }










    //将星期转换为对应的系数  星期日：1，星期一：2，星期二：3，星期三：4，星期四：5，星期五：6，星期六：7  
    public static Integer getWeekNum(String strWeek) {
        Integer number = 1;//默认为星期日  
        if ("星期日".equals(strWeek)) {
            number = 1;
        } else if ("星期一".equals(strWeek)) {
            number = 2;
        } else if ("星期二".equals(strWeek)) {
            number = 3;
        } else if ("星期三".equals(strWeek)) {
            number = 4;
        } else if ("星期四".equals(strWeek)) {
            number = 5;
        } else if ("星期五".equals(strWeek)) {
            number = 6;
        } else if ("星期六".equals(strWeek)) {
            number = 7;
        }
        return number;
    }





    /**
     * 得到当前yyyy-MM-dd HH:mm:ss格式的时间
     */
    public static String now() {
        return DateUtil.date2String(new Date(), DateUtil.PATTERN_STANDARD);
    }






    /**
     * 获取年月日当前时间
     *
     * @return
     */
    public static Date getDate() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(PATTERN_DATE);
        String sDate = simpleDateFormat.format(new Date());
        Date date = null;
        try {
            date = simpleDateFormat.parse(sDate);
        } catch (ParseException e) {
            logger.error("时间解析异常：", e);
        }
        return date;
    }

    public static String date2Str(Date d) {//yyyy-MM-dd HH:mm:ss
        return date2Str(d, null);
    }

    public static String date2Str(Date d, String format) {
        if (d == null) {
            return null;
        }
        if (format == null || format.length() == 0) {
            format = FORMAT;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String s = sdf.format(d);
        return s;
    }

    public static Calendar string2Calendar(String str,String format){

        Date date = string2Date(str,format);
        if(date==null){
            return null;
        }
        Calendar c=Calendar.getInstance();
        c.setTime(date);

        return c;

    }

    /**
     * 给定时间所在月的最大时间
     * @param date
     */
    public static Date getEndDayOfMonth(Date date) {

        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate lastday = localDate.with(TemporalAdjusters.lastDayOfMonth());
        final Date endDay = localDateToDate(lastday);
        return getEndOfDay(endDay);
    }
    public static Date getEndOfDay(Date date) {

        if (date == null) date = new Date();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());;
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }
    public static Date localDateToDate(LocalDate localDate) {
        Instant instant = localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * localDateTime to str
     *
     * @param localDateTime
     * @param formatStr
     * @return
     */
    public static String localDateTimeToStr(LocalDateTime localDateTime, String formatStr) {
        // 如果设置为空,择获取当前时间
        LocalDateTime localDateTimeTmp = Optional.ofNullable(localDateTime)
                .orElseGet(() -> LocalDateTime.now());

        formatStr = Optional.ofNullable(formatStr)
                .orElse(PATTERN_STANDARD);

        return localDateTimeTmp.format(DateTimeFormatter.ofPattern(formatStr));
    }
    public static void main(String[] args) {
//        getMax();
        final Date date = DateUtil.string2Date("2020-01-01 10:00:21", PATTERN_STANDARD);
        final Date endDayOfMonth = getEndDayOfMonth(date);
        final String s = DateUtil.date2String(endDayOfMonth, PATTERN_STANDARD);
        System.out.println(s);
    }
}