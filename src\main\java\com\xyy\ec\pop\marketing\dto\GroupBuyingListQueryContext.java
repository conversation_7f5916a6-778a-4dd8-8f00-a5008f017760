package com.xyy.ec.pop.marketing.dto;

import com.xyy.ec.product.back.end.ecp.csu.dto.CsuDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 拼团活动列表查询上下文
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupBuyingListQueryContext implements Serializable {

    /**
     * 映射关系：商品ID - 信息
     */
    private Map<Long, CsuDTO> csuIdToInfoMap;

}
