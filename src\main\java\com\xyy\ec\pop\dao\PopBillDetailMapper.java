package com.xyy.ec.pop.dao;

import com.xyy.ec.pop.po.PopBillDetailPo;
import com.xyy.ec.pop.po.PopBillPaymentDetailPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface PopBillDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PopBillDetailPo record);

    int insertSelective(PopBillDetailPo record);

    PopBillDetailPo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PopBillDetailPo record);

    int updateByPrimaryKey(PopBillDetailPo record);

    List<PopBillDetailPo> queryPopBillDetail(@Param("popBillPayDetail") PopBillDetailPo popBillDetailPo, @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    Long queryPopBillDetailCount(@Param("list") List<String> billNoList);

    List<PopBillDetailPo> queryPopBillDetailByBillNoList(@Param("list") List<String> billNoList);

    String selectPopBillNoByBusinessNo(@Param("businessNo") String businessNo);
}