package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.address.api.BaseRegionBusinessApi;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.address.dto.XyyRegionParams;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantAndSalesBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.enums.BusinessTypeEnum;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.remote.*;
import com.xyy.ec.pop.server.api.product.dto.PopBusAreaDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDetailDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuSaleTimeDto;
import com.xyy.ec.pop.vo.ControlSaleAreaUserTypeVO;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.UserTypesVO;
import com.xyy.ec.product.back.end.ecp.csucontrol.api.ControlSaleBackEndApi;
import com.xyy.ec.product.back.end.ecp.csucontrol.api.ControlSaleMerchantBusinessApi;
import com.xyy.ec.product.back.end.ecp.csucontrol.dto.ControlSaleAreaUserTypeDTO;
import com.xyy.ec.product.back.end.ecp.csucontrol.dto.ControlSaleEntity;
import com.xyy.ec.product.back.end.ecp.csucontrol.dto.ControlSaleMerchantEntity;
import com.xyy.ec.product.back.end.ecp.pop.dto.MerchantGroupRelationDTO;
import com.xyy.ec.product.back.end.ecp.stock.dto.BPageDto;
import com.xyy.ec.shop.server.business.results.ShopBuyerBlackDTO;
import com.xyy.ec.shop.server.business.results.ShopSellAreaDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 查看商品控销信息
 */
@Controller
@RequestMapping("/salesControl")
@Slf4j
public class ControlSalesController {
    @Autowired
    private ProductSkuRemoteAdapter productSkuRemoteAdapter;
    @Reference
    private ControlSaleBackEndApi controlSaleBackEndApi;
    @Reference
    private ControlSaleMerchantBusinessApi controlSaleMerchantBusinessApi;
    @Reference
    private MerchantBussinessApi merchantBussinessApi;
    @Reference(version = "1.0.0")
    private BaseRegionBusinessApi baseRegionBusinessApi;
    @Autowired
    private PopBusAreaRemoteAdapter popBusAreaRemoteAdapter;
    @Autowired
    private MerchantCustomerTypeRemote merchantCustomerTypeRemote;
    @Autowired
    private ShopUserTypeRemote shopUserTypeRemote;
    @Autowired
    private SkuSaleTimeRemote skuSaleTimeRemote;
    @Autowired
    private ShopBuyerBlackRemote shopBuyerBlackRemote;
    @Autowired
    private BaseRegionRemote baseRegionRemote;
    @Autowired
    private ShopSellAreaRemote shopSellAreaRemote;
    @Autowired
    private MerchantGroupRemote merchantGroupRemote;
    /**
     * 可见可买名单
     */
    private int WHITE = 10001;
    /**
     * 可见不可买名单
     */
    private int BLACK = 10002;
    /**
     * 不可见不可买名单
     */
    private int HIDE = 10003;
    @Value("${saleControl.queryOld:true}")
    private boolean queryOldSaleControl;
    private int queryStep = 200;
    @RequestMapping(value = "/index")
    public String index() {
        return "salesControl/index";
    }

    /**
     * 只查看控销信息页面
     *
     * @return
     */
    @RequestMapping(value = "/controlInfoPage")
    public String controlInfo(Model model, String barcode) {
        model.addAttribute("barcode",barcode);
        return "salesControl/controlInfo";
    }

    /**
     * 只查看控销信息页面
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/controlInfo")
    public ResponseVo<Object> controlInfo(String barcode) {
        try {
            log.info("ControlSalesController.controlInfo#barcode:{}", barcode);
            if (StringUtils.isEmpty(barcode)) {
                return ResponseVo.errRest("商品编码不能为空");
            }
            PopSkuDetailDto sku = productSkuRemoteAdapter.getSkuDetailByBarcodeWithArchive(barcode);
            if (sku == null) {
                return ResponseVo.errRest("指定商品不存在");
            }
            Map<Integer, String> typeMap = merchantCustomerTypeRemote.getBusinessTypeMap();
            HashMap success = new HashMap<>();
            queryControl(success, sku, typeMap);
            log.info("ControlSalesController.controlInfo#barcode:{} return {}", JSON.toJSONString(barcode), JSON.toJSONString(success));
            return ResponseVo.successResult(success);
        } catch (Exception e) {
            log.error("ControlSalesController.controlInfo#barcode:{} 异常", JSON.toJSONString(barcode), e);
            return ResponseVo.errRest("控销信息查询异常");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/detail")
    public ResponseVo<Object> detail(String barcode) {
        try {
            log.info("ControlSalesController.detail#barcode:{}", barcode);
            if (StringUtils.isEmpty(barcode)) {
                return ResponseVo.errRest("商品编码不能为空");
            }
            PopSkuDetailDto sku = productSkuRemoteAdapter.getSkuDetailByBarcodeWithArchive(barcode);
            if (sku == null) {
                return ResponseVo.errRest("指定商品不存在");
            }
            Map<Integer, String> typeMap = merchantCustomerTypeRemote.getBusinessTypeMap();
            HashMap success = new HashMap<>();
            BeanUtils.copyProperties(sku, success);
            success.put("sku", sku.getPopSku());
            success.put("popSkuInstruction", sku.getPopSkuInstruction());
            success.put("limitDto", sku.getPopSkuPurchaseLimitDto());
            queryArea(success, barcode);
            queryControl(success, sku,typeMap);
            queryShopControl(success,sku.getPopSku().getShopCode(),typeMap);
            querySaleTime(success,barcode);
            queryBlackBuyer(success,sku.getPopSku().getShopCode());
            queryShopSaleArea(success,sku.getPopSku().getShopCode());
            log.info("ControlSalesController.detail#barcode:{} return {}", barcode, JSON.toJSONString(success));
            return ResponseVo.successResult(success);

        } catch (Exception e) {
            log.error("ControlSalesController.detail#barcode:{} 异常", barcode, e);
            return ResponseVo.errRest("控销信息查询异常");
        }
    }

    /**
     * 店铺销售区域
     * @param success
     * @param shopCode
     */
    private void queryShopSaleArea(HashMap success, String shopCode) {
        List<ShopSellAreaDTO> shopSellAreaDTOS = shopSellAreaRemote.queryShopSellAreaByShopCode(shopCode);
        if(CollectionUtils.isEmpty(shopSellAreaDTOS)){
            success.put("shopSaleArea",null);
            return;
        }
        List<Integer> areaCodes = shopSellAreaDTOS.stream().map(item -> item.getAreaCode()).collect(Collectors.toList());
        XyyRegionParams params = new XyyRegionParams();
        params.setAreaCodes(areaCodes);
        Map<Integer, XyyRegionBusinessDto> regMap = baseRegionRemote.queryRegion(params).stream().collect(Collectors.toMap(item -> item.getAreaCode(), item -> item));

        String areaNames = areaCodes.stream().sorted().map(item -> regMap.containsKey(item) ? regMap.get(item).getAreaName() : null).filter(item -> item != null).collect(Collectors.joining(","));
        success.put("shopSaleArea",areaNames);
    }

    /**
     * 店铺黑名单客户
     * @param success
     * @param shopCode
     */
    private void queryBlackBuyer(HashMap success, String shopCode) {
        List<ShopBuyerBlackDTO> shopBuyerBlackDTOS = shopBuyerBlackRemote.queryByShopCode(shopCode);
        if(CollectionUtils.isEmpty(shopBuyerBlackDTOS)){
            success.put("shopBlackBuyer",null);
            return;
        }
        success.put("shopBlackBuyer", shopBuyerBlackDTOS.stream().map(item->item.getBuyerCode()).collect(Collectors.joining(",")));
    }

    /**
     * 查询销售时间
     * @param success
     * @param barcode
     */
    private void querySaleTime(HashMap success, String barcode) {
        PopSkuSaleTimeDto timeDto = skuSaleTimeRemote.skuSaleTime(barcode);
        if(timeDto!=null){
            success.put("timeDto", timeDto);
        }
    }

    /**
     * 查询店铺控销
     * @param success
     * @param shopCode
     * @param typeMap
     */
    private void queryShopControl(HashMap success, String shopCode, Map<Integer, String> typeMap) {

        Map<String, String> userTypeMap = shopUserTypeRemote.getShopUserTypeByShopCode(Arrays.asList(shopCode));
        String userType = userTypeMap.get(shopCode);
        if(StringUtils.isEmpty(userType)){
            success.put("shopControl", null);
            return;
        }
        String supplyCustomerType = Arrays.stream(userType.split(",")).map(item-> NumberUtils.toInt(item))
                .map(item -> typeMap.get(item))
                .filter(item -> item != null).collect(Collectors.joining(","));
        success.put("shopControl", supplyCustomerType);
    }

    private void queryControl(HashMap success, PopSkuDetailDto skuDetail, Map<Integer, String> typeMap) throws ServiceException {
        //有设置新控销
        if(skuDetail.getPopSkuInstruction()!=null&&StringUtils.isNotEmpty(skuDetail.getPopSkuInstruction().getControlUserTypes())){
            String typeName = Arrays.stream(skuDetail.getPopSkuInstruction().getControlUserTypes().split(",")).
                    map(item -> typeMap.get(NumberUtils.toInt(item))).collect(Collectors.joining(","));
            skuDetail.getPopSkuInstruction().setControlUserTypes(typeName);
            success.put("saleControlMerchantIds",queryMerchantIds(skuDetail.getPopSkuInstruction().getControlGroupId()));
            return;
        }
        if(!queryOldSaleControl){
            return;
        }
        PopSkuDto sku = skuDetail.getPopSku();
        //查控销信息
        ControlSaleEntity param = new ControlSaleEntity();
        param.setShopCode(sku.getShopCode());
//        param.setBarcode(sku.getBarcode());
        param.setSkuId(sku.getCsuid());
        param.setCsuIds(Lists.newArrayList(sku.getCsuid()));
        PageInfo<ControlSaleEntity> pageInfo = controlSaleBackEndApi.queryControlSaleEntityPageList(1, 100, param);
        if (pageInfo == null || pageInfo.getTotal() == 0 || CollectionUtils.isEmpty(pageInfo.getList())) {
            log.info("商品{}没有控销信息", sku.getBarcode());
            return;
        }

        //过滤当前有效的控销
        ControlSaleEntity cs = filter(pageInfo.getList());
        if (cs == null) {
            log.info("商品{}当前没有控销信息", sku.getBarcode());
            return;
        }
        //查控销客户信息
        ControlSaleMerchantEntity controlSaleMerchantEntity = new ControlSaleMerchantEntity();
        controlSaleMerchantEntity.setControlId(cs.getId());
        List<ControlSaleMerchantEntity> custData = controlSaleMerchantBusinessApi.queryControlSaleMerchantEntity(controlSaleMerchantEntity);
        List<MerchantAndSalesBussinessDto> busdtos = setMerchatStatusUtils(custData);
        //控销方案
        List<ControlSaleAreaUserTypeDTO> plans = controlSaleMerchantBusinessApi.getControlSaleAreaUserTypeByControlId(cs.getId());

        if (CollectionUtils.isNotEmpty(plans)) {
            Map<Integer, List<ControlSaleAreaUserTypeVO>> planMap = plans.stream().map(this::convertPlanVO).collect(Collectors.groupingBy(ControlSaleAreaUserTypeVO::getRosterType));
            success.put("whitePlan", planMap.get(WHITE));
            success.put("blackPlan", planMap.get(BLACK));
            success.put("hidePlan", planMap.get(HIDE));
        }
        success.put("control", cs);
        if (CollectionUtils.isNotEmpty(busdtos)) {
            Map<Integer, List<ControlSaleMerchantEntity>> planMap = custData.stream().collect(Collectors.groupingBy(ControlSaleMerchantEntity::getRosterType));
            success.put("blackCustData", setMerchatStatusUtils(planMap.get(BLACK)));
            success.put("whiteCustData", setMerchatStatusUtils(planMap.get(WHITE)));
            success.put("hideCustData", setMerchatStatusUtils(planMap.get(HIDE)));
        }
    }

    private List<Long> queryMerchantIds(Long id) throws ServiceException {
        if(id==null){
            return new ArrayList<>(0);
        }
        MerchantGroupRelationDTO dto = new MerchantGroupRelationDTO();
        dto.setGroupId(id);
        dto.setPageSize(queryStep);
        dto.setOffset(0);
        boolean hasMove = true;
        List<Long> result = new ArrayList<>(256);
        while(hasMove){
            BPageDto<MerchantGroupRelationDTO> pageDto = merchantGroupRemote.selectMerchantGroupRelationPage(dto);
            result.addAll(pageDto.getRows().stream().map(item->item.getMerchantId()).collect(Collectors.toList()));
            if(pageDto.getRows().size()<dto.getPageSize()||result.size()>=pageDto.getTotalCount()){
                hasMove=false;
            }
            dto.setOffset(dto.getOffset()+dto.getPageSize());
        }
        return result;
    }

    private void queryArea(HashMap success, String barcode) {
        List<PopBusAreaDto> areaDtos = popBusAreaRemoteAdapter.queryAreasByBarcode(barcode);
        if (areaDtos != null) {
            success.put("areaDtos", areaDtos);
        }
    }

    private ControlSaleAreaUserTypeVO convertPlanVO(ControlSaleAreaUserTypeDTO plan) {
        ControlSaleAreaUserTypeVO controlSaleAreaUserTypeVO = new ControlSaleAreaUserTypeVO();
        BeanUtils.copyProperties(plan, controlSaleAreaUserTypeVO);
        String areaCodes = plan.getAreaCodes();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(areaCodes)) {
            String[] split = areaCodes.split(",");
            List<Integer> areaList = Lists.newArrayList(split).stream().map(Integer::valueOf).collect(Collectors.toList());
            XyyRegionParams xyyRegionParams = new XyyRegionParams();
            xyyRegionParams.setAreaCodes(areaList);
            List<XyyRegionBusinessDto> xyyRegionBusinessDtos = baseRegionBusinessApi.queryRegionByAreaCodeList(xyyRegionParams);
            xyyRegionBusinessDtos.sort(Comparator.comparing(XyyRegionBusinessDto::getAreaCode));
            controlSaleAreaUserTypeVO.setAreaCodes(xyyRegionBusinessDtos);
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(plan.getUserTypes())) {
            String[] types = plan.getUserTypes().split(",");
            List<Integer> typeList = Lists.newArrayList(types).stream().map(Integer::valueOf).collect(Collectors.toList());
            List<UserTypesVO> usertTypes = Lists.newArrayList();
            for (Integer type : typeList) {
                UserTypesVO userTypesVO = new UserTypesVO();
                userTypesVO.setKey(type);
                userTypesVO.setValue(BusinessTypeEnum.get(type));
                usertTypes.add(userTypesVO);
            }
            controlSaleAreaUserTypeVO.setUserTypes(usertTypes);
        }
        return controlSaleAreaUserTypeVO;
    }

    /**
     * 根据客户id查客户信息
     *
     * @param lists
     * @return
     */
    private List<MerchantAndSalesBussinessDto> setMerchatStatusUtils(List<ControlSaleMerchantEntity> lists) {
        List<MerchantAndSalesBussinessDto> res = new ArrayList<>();
        if (lists == null || lists.size() == 0) {
            return res;
        }
        MerchantBussinessDto dto = new MerchantBussinessDto();
        Long[] ids = new Long[lists.size()];
        for (int i = 0; i < lists.size(); i++) {
            ids[i] = lists.get(i).getMerchantId();
        }
        dto.setIds(ids);
        List<MerchantBussinessDto> merchantInfoList = merchantBussinessApi.findMerchantInfoList(dto);
        Map<Long, MerchantBussinessDto> collect = merchantInfoList.stream().collect(Collectors.toMap(MerchantBussinessDto::getId, Function.identity(), (k1, k2) -> k2));
        for (ControlSaleMerchantEntity entity : lists) {
            MerchantAndSalesBussinessDto merchantAndSalesBussinessDto = convertMerchantAndSales(entity);
            Long merchatId = entity.getMerchantId();
            MerchantBussinessDto merchantBussinessDto = collect.get(merchatId);
            if (merchantBussinessDto != null) {
                merchantAndSalesBussinessDto.setAddress(merchantAndSalesBussinessDto.getAddress());
                merchantAndSalesBussinessDto.setMobile(merchantAndSalesBussinessDto.getMobile());
                merchantAndSalesBussinessDto.setRealName(merchantAndSalesBussinessDto.getRealName());
                merchantAndSalesBussinessDto.setMerchantState(merchantBussinessDto.getStatus());
                merchantAndSalesBussinessDto.setCreateTime(merchantBussinessDto.getCreateTime());
            }
            res.add(merchantAndSalesBussinessDto);
        }
        return res;
    }

    private MerchantAndSalesBussinessDto convertMerchantAndSales(ControlSaleMerchantEntity controlSaleMerchantEntity) {
        MerchantAndSalesBussinessDto res = new MerchantAndSalesBussinessDto();
        res.setId(controlSaleMerchantEntity.getMerchantId());
        res.setAddress(controlSaleMerchantEntity.getAddress());
        res.setState(controlSaleMerchantEntity.getMerchantState());
        res.setBranchCode(controlSaleMerchantEntity.getBranchCode());
        res.setBusinessType(controlSaleMerchantEntity.getBusinessType());
        res.setBusinessTypeName(controlSaleMerchantEntity.getBusinessTypeName());
        res.setCreateTime(controlSaleMerchantEntity.getCreateTime());
        res.setMobile(controlSaleMerchantEntity.getMobile());
        res.setRealName(controlSaleMerchantEntity.getRealName());
        res.setMerchantId(controlSaleMerchantEntity.getMerchantId());
        return res;
    }

    private ControlSaleEntity filter(List<ControlSaleEntity> list) {
        Date now = new Date();
        List<ControlSaleEntity> ok = list.stream()
                .filter(item -> item.getStartTime() != null && item.getStartTime().before(now))
                .filter(item -> item.getEndTime() == null || item.getEndTime().after(now)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ok)) {
            return null;
        }
        ok.sort((item1, item2) -> item2.getId().compareTo(item1.getId()));
        return ok.get(0);
    }
}
