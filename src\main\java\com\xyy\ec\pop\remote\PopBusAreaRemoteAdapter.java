package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.product.api.admin.PopBusAreaApi;
import com.xyy.ec.pop.server.api.product.api.admin.PopBusAreaProductRelationApi;
import com.xyy.ec.pop.server.api.product.dto.PopBusAreaDto;
import com.xyy.ec.pop.server.api.product.dto.PopBusAreaProductRelationDto;
import com.xyy.ec.pop.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @Date: 2021/3/23
 */
@Slf4j
@Component
public class PopBusAreaRemoteAdapter {

    @Reference(version = "1.0.0")
    PopBusAreaProductRelationApi popBusAreaProductRelationApi;
    @Reference
    private PopBusAreaApi popBusAreaApi;

    public Map<String, PopBusAreaProductRelationDto> queryBusAreaProductByBarcodes(List<String> barCodes) {
        log.info("PopBusAreaServiceImpl.queryAreaByBarcodes barCodes:{}", StringUtils.join(barCodes));
        Map<String, PopBusAreaProductRelationDto> result = Maps.newHashMap();
        try {
            ApiRPCResult<Map<String, PopBusAreaProductRelationDto>> apiRPCResult = popBusAreaProductRelationApi.queryBusAreaProductByBarcodes(barCodes);
            log.info("PopBusAreaServiceImpl.queryAreaByBarcodes apiRPCResult: [{}]", JsonUtil.toJson(apiRPCResult));
            if (null != apiRPCResult && ApiResultCodeEum.SUCCESS.getCode() == apiRPCResult.getCode()) {
                result = apiRPCResult.getData();
            }
        } catch (Exception e) {
            log.info("PopBusAreaServiceImpl.queryAreaByBarcodes 系统异常!");
        }
        log.info("PopBusAreaServiceImpl.queryAreaByBarcodes result:[{}]", JsonUtil.toJson(result));
        return result;
    }

    public List<PopBusAreaDto> queryAreasByBarcode(String barcode) {
        try {
            log.info("PopBusAreaRemoteAdapter.queryAreasByBarcode#barcode:{}", JSON.toJSONString(barcode));
            ApiRPCResult<List<PopBusAreaDto>> result = popBusAreaApi.queryAreasByBarcode(barcode);

            log.info("PopBusAreaRemoteAdapter.queryAreasByBarcode#barcode:{} return {}", JSON.toJSONString(barcode), JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("PopBusAreaRemoteAdapter.queryAreasByBarcode#barcode:{} 异常", JSON.toJSONString(barcode), e);
            return null;
        }
    }
}
