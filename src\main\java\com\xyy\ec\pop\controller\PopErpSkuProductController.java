package com.xyy.ec.pop.controller;

import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.ProductBatchUpdateConfig;
import com.xyy.ec.pop.excel.verify.ProductErpSkuBatchUpdateExcelVerify;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.service.PopErpSkuBatchUpdateService;
import com.xyy.ec.pop.service.ProductService;
import com.xyy.ec.pop.utils.ExceImportWarpUtil;
import com.xyy.ec.pop.utils.FileNameUtils;
import com.xyy.ec.pop.vo.BatchUpdateResultVo;
import com.xyy.ec.pop.vo.ErpSkuBatchUpdateVo;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @project_name: xyy-ec-pop-service
 * @Auther: Jincheng.Li
 * @Date: 2021/04/14/14:57
 * @Description:
 */
@Slf4j
@Controller
@RequestMapping("/product")
public class PopErpSkuProductController extends BaseController {
    private static final Logger LOG = LoggerFactory.getLogger(PopErpSkuProductController.class);

    @Value("${product.erpSku.batchUpdate.users}")
    private String batchUpdateUsers;
    @Value("${fastdfs.host}")
    private String hostUrl;
    @Autowired
    private ProductBatchUpdateConfig productBatchUpdateConfig;
    @Autowired
    private PopErpSkuBatchUpdateService popErpSkuBatchUpdateService;

    @GetMapping(value = "/batchUpdateErpSkuView")
    public String batchUpdateSkuView(Model model) {
        model.addAttribute("fileHost", hostUrl);
        model.addAttribute("rowSize", productBatchUpdateConfig.getMaxRows());
        model.addAttribute("fileSize", productBatchUpdateConfig.getMaxFileSize());
        return "product/batchUpdatePopErpSku";
    }

    @PostMapping("/batchUpdateErpSkuInfoFromExcel")
    @ResponseBody
    public ResponseVo<BatchUpdateResultVo> batchUpdateErpSkuInfoFromExcel(@RequestParam("file") MultipartFile file) {
        String user = getUser().getUsername();
        try {
            validBatchUpdateAuthority();
            ImportParams importParams = new ImportParams();
            importParams.setVerifyHandler(new ProductErpSkuBatchUpdateExcelVerify());
            List<ErpSkuBatchUpdateVo> vos = ExceImportWarpUtil.importExcel(file, ErpSkuBatchUpdateVo.class, importParams, productBatchUpdateConfig.getMaxFileSize(), productBatchUpdateConfig.getMaxRows(), productBatchUpdateConfig.getUpdateErpSkuTitles());

            log.info("##PopErpSkuProductController.batchUpdateErpSkuInfoFromExcel##vos:{}", vos);

            ProductErpSkuBatchUpdateExcelVerify.trim(vos);
            ProductErpSkuBatchUpdateExcelVerify.valid(vos);

            BatchUpdateResultVo resultVo = popErpSkuBatchUpdateService.batchUpdateErpSku(user, vos);

            resultVo.setErrorFileName(FileNameUtils.getErrorFileName(file));
            String downLoadUrl = FileNameUtils.getErrorFileDownUrl(resultVo.getErrorFileUrl(), resultVo.getErrorFileName());
            resultVo.setErrorFileUrl(downLoadUrl);
            return ResponseVo.successResult(resultVo);
        } catch (ServiceException e) {
            LOG.warn("PopErpSkuProductController.batchUpdateErpSkuInfoFromExcel,user:{}, 失败", user, e);
            return ResponseVo.errRest(StringUtils.isEmpty(e.getMessage()) ? "系统异常" : e.getMessage());
        } catch (Exception e) {
            LOG.error("PopErpSkuProductController.batchUpdateErpSkuInfoFromExcel,user:{}, 出现异常", user, e);
            return ResponseVo.errRest("批量修改ERP商品信息导入异常");
        }
    }


    /**
     * 校验是否有批量修改ERP商品权限
     */
    private void validBatchUpdateAuthority() throws ServiceException {
        if (StringUtils.isEmpty(batchUpdateUsers)) {
            throw new ServiceException("没有操作权限");
        }
        String user = getUser().getUsername();
        String[] users = StringUtils.split(batchUpdateUsers, ",");
        for (String u : users) {
            if (user.equals(u)) {
                return;
            }
        }
        LOG.warn("ProductController.batchUpdateErpSkuInfoFromExcel没有操作权限,user:{}, users:{}", user, batchUpdateUsers);
        throw new ServiceException("没有操作权限");
    }

}
