package com.xyy.ec.pop.vip.controller;

import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.vip.service.VipActiveUsersService;
import com.xyy.ec.pop.vip.vo.VipActiveUserVo;
import com.xyy.ec.pop.vip.vo.VipActiveUsersQueryVo;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Slf4j
@Controller
@RequestMapping(value = "/vipActiveUsers")
public class VipActiveUsersController extends BaseController {
    @Resource
    private VipActiveUsersService vipActiveUsersService;

    /**
     * 分页查询会员活跃用户
     *
     * @param vipActiveUsersQuery
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/pageVipActiveUsers", method = {RequestMethod.GET})
    public ResponseVo<Page<VipActiveUserVo>> pageVipActiveUsers(VipActiveUsersQueryVo vipActiveUsersQuery) {
        return vipActiveUsersService.pageVipActiveUsers(vipActiveUsersQuery);
    }

    /**
     * 导出会员活跃用户
     *
     * @param vipActiveUsersQuery
     * @return
     */
    @RequestMapping(value = "/exportActiveUsers", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseVo<Boolean> exportActiveUsers(VipActiveUsersQueryVo vipActiveUsersQuery) {
        return vipActiveUsersService.exportActiveUsers(vipActiveUsersQuery, getUser());
    }
}
