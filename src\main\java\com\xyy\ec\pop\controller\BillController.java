package com.xyy.ec.pop.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.po.PopBillDetailPo;
import com.xyy.ec.pop.po.PopBillPo;
import com.xyy.ec.pop.server.api.order.enums.OrderPayTypeEnums;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.service.settle.PopBillService;
import com.xyy.ec.pop.service.settle.impl.domain.PopBillDomainService;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.settle.PopBillStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 账单
 *
 * <AUTHOR>
 * @date 2022/8/16
 * @table
 */
@Slf4j
@RequestMapping("/bill")
@Controller
public class BillController extends BaseController {

    @Autowired
    private PopBillService popBillService;
    @Autowired
    private PopBillDomainService popBillDomainService;


    /**
     * 列表查询
     *
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/list")
    public ResponseVo<PageInfo<PopBillPo>> list(PopBillVo popBillVo, PageInfo pageInfo) {
        log.info("BillController.list#popBillVo:{},pageNum:{},pageSize:{}", JSON.toJSONString(popBillVo), pageInfo.getPageNum(), pageInfo.getPageSize());
        try {
            if (validateProv(popBillVo)) {
                return ResponseVo.successResult(new PageInfo<>());
            }
            PageInfo<PopBillPo> popBillPoPageInfo = popBillDomainService.queryPopBillList(popBillVo, pageInfo);
            log.info("BillController.list#popBillVo:{},pageNum:{},pageSize:{},result:{}", JSON.toJSONString(popBillVo), pageInfo.getPageNum(), pageInfo.getPageSize(), JSON.toJSONString(popBillPoPageInfo));
            return ResponseVo.successResult(popBillPoPageInfo);
        } catch (ServiceRuntimeException e) {
            log.error("BillController.list#查询账单列表，自定义异常 popBillVo:{}", JSON.toJSONString(popBillVo), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillController.list#查询账单列表，未知异常 popBillVo:{}", JSON.toJSONString(popBillVo), e);
            return ResponseVo.errRest("查询账单列表异常");
        }
    }

    private boolean validateProv(PopBillVo popBillVo) {
        List<Long> provIds = getProvIds(popBillVo.getProvId());
        if (CollectionUtils.isEmpty(provIds)) {
            return true;
        }
        popBillVo.setProvIds(provIds);
        return false;
    }


    /**
     * 账单统计
     *
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/queryBillStatistic")
    public ResponseVo<PopBillStatisVo> queryBillStatistic(PopBillVo popBillVo) {
        log.info("BillController.queryBillStatistic#popBillVo:{}", JSON.toJSONString(popBillVo));
        try {
            if (validateProv(popBillVo)) {
                return ResponseVo.successResult(new PopBillStatisVo());
            }
            PopBillStatisVo popBillPo = popBillDomainService.queryPopBillPayStatis(popBillVo);
            log.info("BillController.queryBillStatistic#popBillVo:{},popBillPo:{}", JSON.toJSONString(popBillVo), JSON.toJSONString(popBillPo));
            if (popBillPo == null) {
                popBillPo = new PopBillStatisVo();
            }
            return ResponseVo.successResult(popBillPo);
        } catch (ServiceRuntimeException e) {
            log.error("BillController.queryBillStatistic#自定义异常 popBillVo:{}", JSON.toJSONString(popBillVo), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillController.queryBillStatistic#未知异常 popBillVo:{}", JSON.toJSONString(popBillVo), e);
            return ResponseVo.errRest("查询账单统计异常");
        }
    }

    /**
     * 根据帐单号查询账单
     *
     * @return
     */
    @GetMapping(value = "/queryBillByNo")
    @ResponseBody
    public ResponseVo<PopBillPo> queryBillByNo(String billNo) {
        log.info("BillController.queryBillByNo#billNo:{}", billNo);
        try {
            if (StringUtils.isBlank(billNo)) {
                return ResponseVo.errRest("账单号不能为空");
            }
            PopBillPo popBillPo = popBillDomainService.selectByBillNo(billNo);
            log.info("BillController.queryBillByNo#billNo:{}, result:{}", billNo, JSON.toJSONString(popBillPo));
            //合并佣金字段
            if (popBillPo != null) {
                if (popBillPo.getSettlementType() == CommissionSettleTypeEnum.EVERY_MONTH.getCode()) {
                    popBillPo.setHireMoney(popBillPo.getPayableCommission());
                }
            }
            return ResponseVo.successResult(popBillPo);
        } catch (Exception e) {
            log.error("根据帐单号查询账单异常", e);
            return ResponseVo.errRest("查询账单信息异常");
        }
    }

    /**
     * 查询账单明细，带分页
     *
     * @param billNo
     * @return
     */
    @GetMapping(value = "/billDetail")
    @ResponseBody
    public ResponseVo<PageInfo<PopBillDetailPo>> billDetail(String billNo, PageInfo pageInfo) {
        log.info("BillController.queryBillByNo#billNo:{},pageNum:{},pageSize:{}", billNo, pageInfo.getPageNum(), pageInfo.getPageSize());
        try {
            if (StringUtils.isBlank(billNo)) {
                return ResponseVo.errRest("账单号不能为空");
            }
            PageInfo<PopBillDetailPo> popBillDetailPoPageInfo = popBillDomainService.queryPopBillDetail(billNo, pageInfo);
            log.info("BillController.queryBillByNo#billNo:{},pageNum:{},pageSize:{},result:{}", billNo, pageInfo.getPageNum(), pageInfo.getPageSize(), JSON.toJSONString(popBillDetailPoPageInfo));
            return ResponseVo.successResult(popBillDetailPoPageInfo);
        } catch (Exception e) {
            log.error("查询账单明细异常 billNo:{},pageNum:{},pageSize:{}", billNo, pageInfo.getPageNum(), pageInfo.getPageSize(), e);
            return ResponseVo.errRest("查询账单明细异常");
        }
    }

    /**
     * 查询账单导出的条数  超过5000条数据，前端给出提示
     *
     * @param popBillVo
     * @return
     */
    @RequestMapping(value = "/queryBillExportCount")
    @ResponseBody
    public ResponseVo<Long> queryExprotBillListCount(PopBillVo popBillVo) {
        log.info("BillController.queryExprotBillListCount#popBillVo:{}", JSON.toJSONString(popBillVo));
        try {
            Long count = popBillDomainService.queryPopBillListCount(popBillVo);
            log.info("BillController.queryExprotBillListCount#popBillVo:{}, count:{}", JSON.toJSONString(popBillVo), count);
            return ResponseVo.successResult(count);
        } catch (Exception e) {
            log.error("查询账单导出的条数异常 popBillVo:{}", JSON.toJSONString(popBillVo), e);
            return ResponseVo.errRest("查询账单导出数量异常");
        }
    }

    /**
     * 查询账单明细导出的条数  超过5000条数据，前端给出提示
     *
     * @param popBillVo
     * @return
     */
    @RequestMapping(value = "/queryBillDetailExportCount")
    @ResponseBody
    public ResponseVo<Long> queryBillDetailExportCount(PopBillVo popBillVo) {
        log.info("BillController.queryBillDetailExportCount#popBillVo:{}", JSON.toJSONString(popBillVo));
        try {
            popBillVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            Long billDetailCount = popBillDomainService.queryExprotBillDetailCount(popBillVo);
            log.info("BillController.queryBillDetailExportCount#popBillVo:{},count:{}", JSON.toJSONString(popBillVo), billDetailCount);
            return ResponseVo.successResult(billDetailCount);
        } catch (Exception e) {
            log.error("查询账单明细导出的条数异常 popBillVo:{}", JSON.toJSONString(popBillVo), e);
            return ResponseVo.errRest("查询账单明细导出数量异常");
        }
    }

}
