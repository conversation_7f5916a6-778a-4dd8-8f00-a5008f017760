package com.xyy.ec.pop.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.vip.VipActiveUsersApi;
import com.xyy.ec.merchant.bussiness.dto.OrderNumMonthlyDto;
import com.xyy.ec.merchant.bussiness.dto.OrderNumMonthlyQueryDto;
import com.xyy.ec.merchant.bussiness.dto.vip.VipActivateUserQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class VipActiveUsersRemoteImpl {
    @Reference(version = "1.0.0", check = false)
    private VipActiveUsersApi vipActiveUsersApi;

    public PageInfo<OrderNumMonthlyDto> pageMerchantReportDetail(VipActivateUserQueryDto vipActivateUserQueryDto) {
        try {
            ApiRPCResult<PageInfo<OrderNumMonthlyDto>> apiRPCResult = vipActiveUsersApi.pageVipActiveUsers(vipActivateUserQueryDto);
            if (apiRPCResult.isSuccess()) {
                return apiRPCResult.getData();
            }
        } catch (Exception e) {
            log.error("分页查询会员列表异常，e=", e);
        }
        return null;
    }
}
