package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.xyy.ec.merchant.bussiness.api.MerchantCustomerTypeBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantCustomerTypeBusinessDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/8/20
 */
@Component
@Slf4j
public class MerchantCustomerTypeRemote {
    @Reference
    private MerchantCustomerTypeBusinessApi merchantCustomerTypeBusinessApi;

    public List<MerchantCustomerTypeBusinessDto> getBusinessType() {
        try {
            log.info("MerchantCustomerTypeRemote.getBusinessType#:{}");
            List<MerchantCustomerTypeBusinessDto> all = merchantCustomerTypeBusinessApi.getAll();
            log.info("MerchantCustomerTypeRemote.getBusinessType#:{} return {}", JSON.toJSONString(all));
            if (CollectionUtils.isEmpty(all)) {
                return Lists.newArrayList();
            }
            return all;
        } catch (Exception e) {
            log.error("MerchantCustomerTypeRemote.getBusinessType#:{} 异常", e);
            return Lists.newArrayList();
        }
    }

    public Map<Integer,String> getBusinessTypeMap() {
        List<MerchantCustomerTypeBusinessDto> all = merchantCustomerTypeBusinessApi.getAll();
        if(CollectionUtils.isEmpty(all)){
            return new HashMap<>(0);
        }
        return all.stream().collect(Collectors.toMap(item->item.getId(),item->item.getName()));
    }
}
