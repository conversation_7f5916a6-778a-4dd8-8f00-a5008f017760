package com.xyy.ec.pop.service.impl;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.BusinessCategoryCommissionHelper;
import com.xyy.ec.pop.remote.BusinessCategoryCommissionRemoteAdapter;
import com.xyy.ec.pop.server.api.merchant.dto.BusinessCategoryCommissionDto;
import com.xyy.ec.pop.server.api.merchant.dto.PopCommissionConfigDto;
import com.xyy.ec.pop.service.BusinessCategoryCommissionService;
import com.xyy.ec.pop.service.ProductCategoryService;
import com.xyy.ec.pop.utils.CollectionUtil;
import com.xyy.ec.pop.vo.BusinessCategoryCommissionVo;
import com.xyy.me.product.service.read.dto.TotalDictionaryReadDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 企业佣金比例
 * <AUTHOR>
 */
@Service
public class BusinessCategoryCommissionServiceImpl implements BusinessCategoryCommissionService {
    @Autowired
    private BusinessCategoryCommissionRemoteAdapter commissionRemoteAdapter;
    @Autowired
    private ProductCategoryService productCategoryService;
    @Autowired
    private BusinessCategoryCommissionRemoteAdapter commissionService;
    @Override
    public List<BusinessCategoryCommissionVo> getCommissions(String orgId) throws ServiceException {
        //查佣金比例
        List<BusinessCategoryCommissionDto> list = commissionRemoteAdapter.getCommissions(orgId);
        //查分类名
        List<TotalDictionaryReadDto> categoryList = productCategoryService.getTwoLevelProductCategory();
        //转换
        List<BusinessCategoryCommissionVo> vos = convertToVos(list,categoryList);
        return vos;
    }

    @Override
    public Map<String,List<BusinessCategoryCommissionVo>> getCommissionsByorgIds(List<String> orgIds) throws ServiceException {
        Map<String,List<BusinessCategoryCommissionVo>> resMap  = new HashMap<>();
        //查佣金比例
        List<BusinessCategoryCommissionDto> businessCategoryCommissionDtos = commissionRemoteAdapter.getCommissionsByorgIds(orgIds);
        //查分类名
        List<TotalDictionaryReadDto> categoryList = productCategoryService.getTwoLevelProductCategory();

        Map<String,List<BusinessCategoryCommissionDto>> commissionVoMap= new HashMap<>();

        if (CollectionUtil.isNotEmpty(businessCategoryCommissionDtos)) {
            commissionVoMap = businessCategoryCommissionDtos.stream().collect(Collectors.toMap(BusinessCategoryCommissionDto::getOrgId, businessCategoryCommissionVo -> {
                List<BusinessCategoryCommissionDto> list = new ArrayList<>();
                list.add(businessCategoryCommissionVo);
                return list;
            }, (List<BusinessCategoryCommissionDto> value1, List<BusinessCategoryCommissionDto> value2) -> {
                value1.addAll(value2);
                return value1;
            }));
        }

        for (String orgId:commissionVoMap.keySet()) {
            //转换
            List<BusinessCategoryCommissionDto> list = commissionVoMap.get(orgId);
            List<BusinessCategoryCommissionVo> vos = convertToVos(list,categoryList);
            resMap.put(orgId,vos);
        }
        return resMap;
    }

    @Override
    public void updateCommissions(String orgId, List<BusinessCategoryCommissionVo> commissions, String username) throws ServiceException {
        if(CollectionUtils.isEmpty(commissions)|| StringUtils.isEmpty(orgId)){
            return;
        }
        //只要一级分类的
        commissions = commissions.stream().filter(item->StringUtils.isEmpty(item.getBusinessSecondCategoryCode())).collect(Collectors.toList());
        List<BusinessCategoryCommissionDto> dtos = BusinessCategoryCommissionHelper.convertToDto(commissions,orgId);
        commissionRemoteAdapter.updateCommissions(dtos,orgId,username);
    }

    private List<BusinessCategoryCommissionVo> convertToVos(List<BusinessCategoryCommissionDto> list, List<TotalDictionaryReadDto> categoryList) {
        Map<String, BusinessCategoryCommissionDto> map = list.stream().collect(Collectors.toMap(BusinessCategoryCommissionDto::getBusinessFirstCategoryCode, Function.identity(),(o1,o2) -> o1));
        List<TotalDictionaryReadDto> one = categoryList.stream().filter(item->item.getLevelNode().intValue()==1).collect(Collectors.toList());
        PopCommissionConfigDto configVO = commissionService.getPopCommissionConfig();
        return one.stream().map(item->{
            BusinessCategoryCommissionVo vo = new BusinessCategoryCommissionVo();
            vo.setBusinessFirstCategoryCode(item.getId().toString());
            vo.setCategoryName(item.getDictName());
            BusinessCategoryCommissionDto dto = map.get(item.getId().toString());
            if(dto!=null){
                vo.setCommissionRatio(dto.getCommissionRatio());
                vo.setId(dto.getId());
            }else{
                vo.setCommissionRatio(configVO.getDefaultCommissionRatio());
            }
            return vo;
        }).collect(Collectors.toList());
    }


}
