package com.xyy.ec.pop.helper;


import com.xyy.ec.pop.server.api.product.dto.ProductCommissionDto;
import com.xyy.ec.pop.server.api.product.enums.PopSkuStatus;
import com.xyy.ec.pop.server.api.product.query.ProductCommissionQuery;
import com.xyy.ec.pop.vo.ProductCommissionBatchUpdateVo;
import com.xyy.ec.pop.vo.ProductCommissionQueryVo;
import com.xyy.ec.pop.vo.ProductCommissionVo;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ProductCommissionHelper {
    public static ProductCommissionQuery convertToQuery(ProductCommissionQueryVo queryVo) {
        ProductCommissionQuery productCommissionQuery = new ProductCommissionQuery();
        productCommissionQuery.setOrgName(queryVo.getOrgName());
        productCommissionQuery.setShopName(queryVo.getShopName());
        productCommissionQuery.setBarcode(queryVo.getBarcode());
        productCommissionQuery.setShowName(queryVo.getShowName());
        productCommissionQuery.setStandardProductId(queryVo.getStandardProductId());
        productCommissionQuery.setManufacturer(queryVo.getManufacturer());
        productCommissionQuery.setStatus(queryVo.getStatus());
        productCommissionQuery.setOrgIds(queryVo.getOrgIds());
        productCommissionQuery.setPageSize(queryVo.getPageSize());
        productCommissionQuery.setPageNum(queryVo.getPageNum());
        productCommissionQuery.setOrgId(queryVo.getOrgId());
        productCommissionQuery.setActivityType(queryVo.getActivityType());
        productCommissionQuery.setCsuid(queryVo.getCsuid());
        return productCommissionQuery;
    }

    public static List<ProductCommissionVo> convertToCommissionVos(List<ProductCommissionDto> list) {
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList();
        }
        return list.stream().map(item->convertToCommissionVo(item)).collect(Collectors.toList());
    }

    private static ProductCommissionVo convertToCommissionVo(ProductCommissionDto item) {
        ProductCommissionVo vo = new ProductCommissionVo();
        vo.setOrgId(item.getOrgId());
        vo.setOrgName(item.getOrgName());
        vo.setShopName(item.getShopName());
        vo.setBarcode(item.getBarcode());
        vo.setShowName(item.getShowName());
        vo.setStandardProductId(item.getStandardProductId());
        vo.setManufacturer(item.getManufacture());
        vo.setSpec(item.getSpec());
        vo.setStatus(item.getStatus());
        vo.setStatusName(PopSkuStatus.getNameByValue(item.getStatus()));
        vo.setCommissionRatio(item.getCommissionRatio());
        vo.setBusinessFirstCategoryCode(item.getBusinessFirstCategoryCode());
        vo.setActivityType(item.getActivityType());
        vo.setCsuid(item.getCsuid());
        return vo;
    }

    public static ProductCommissionDto convertToCommissionUpdateDto(ProductCommissionBatchUpdateVo updateVo) {
        ProductCommissionDto productCommissionDto = new ProductCommissionDto();
        productCommissionDto.setBarcode(updateVo.getBarcode());
        productCommissionDto.setCommissionRatio(new BigDecimal(updateVo.getCommissionRatio()));
        return productCommissionDto;

    }

    public static List<ProductCommissionDto> convertToCommissionUpdateDtos(List<ProductCommissionBatchUpdateVo> vos) {
        if(CollectionUtils.isEmpty(vos)){
            return new ArrayList();
        }
        return vos.stream().map(item->convertToCommissionUpdateDto(item)).collect(Collectors.toList());
    }
}
