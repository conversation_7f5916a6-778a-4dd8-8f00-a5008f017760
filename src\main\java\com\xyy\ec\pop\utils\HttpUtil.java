package com.xyy.ec.pop.utils;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class HttpUtil {

	private static Logger logger = LoggerFactory.getLogger(HttpUtil.class);
	private static SSLConnectionSocketFactory sslsf = null;


	public static String post(String url, Map<String, String> params, Map<String, String> heads)
			throws IOException, KeyManagementException, NoSuchAlgorithmException {
		CloseableHttpClient httpclient = HttpUtil.createHttpClient(url);
		HttpPost httpPost = new HttpPost(url);
		List<NameValuePair> nvps = new ArrayList<NameValuePair>();
		if (params != null) {
			for (Map.Entry<String, String> entry : params.entrySet()) {
				nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
			}
		}

		if (heads != null) {
			for (Map.Entry<String, String> entry : heads.entrySet()) {
				httpPost.setHeader(entry.getKey(), entry.getValue());
			}
		}
		RequestConfig requestConfig = RequestConfig.custom()
				.setConnectTimeout(100000).setConnectionRequestTimeout(100000)
				.setSocketTimeout(100000).build();
		httpPost.setConfig(requestConfig);
		httpPost.setEntity(new UrlEncodedFormEntity(nvps,"utf-8"));
		CloseableHttpResponse response = httpclient.execute(httpPost);
		return EntityUtils.toString(response.getEntity());
	}

	private static CloseableHttpClient createHttpClient(String url)
			throws KeyManagementException, NoSuchAlgorithmException, MalformedURLException {
		URL u = new URL(url);
		CloseableHttpClient httpclient = null;
		if ("https".equals(u.getProtocol())) {
			logger.trace("https");
			httpclient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
		} else {
			logger.trace("http");
			httpclient = HttpClients.createDefault();
		}

		return httpclient;
	}

	/**
	 * JavaBean对象转化成Map对象
	 *
	 * @param javaBean
	 * @return
	 * <AUTHOR>
	 */
	public static Map<String,String> javaToMap(Object javaBean) {
		Map<String,String> map = new HashMap<>();
		try {
			// 获取javaBean属性
			BeanInfo beanInfo = Introspector.getBeanInfo(javaBean.getClass());
			PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
			if (propertyDescriptors != null && propertyDescriptors.length > 0) {
				String propertyName = null; // javaBean属性名
				Object propertyValue = null; // javaBean属性值
				for (PropertyDescriptor pd : propertyDescriptors) {
					propertyName = pd.getName();
					if (!propertyName.equals("class")) {
						Method readMethod = pd.getReadMethod();
						propertyValue = readMethod.invoke(javaBean, new Object[0]);
						if(propertyValue != null  && !"".equals(propertyValue)){
							map.put(propertyName, String.valueOf(propertyValue));
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return map;
	}

	public static String post(String url, Object object, Map<String, String> heads)
			throws IOException, KeyManagementException, NoSuchAlgorithmException {
		CloseableHttpClient httpclient = HttpUtil.createHttpClient(url);
		HttpPost httpPost = new HttpPost(url);
		List<NameValuePair> nvps = new ArrayList<NameValuePair>();
		Map<String, String> params = javaToMap(object);
		if (params != null) {
			for (Map.Entry<String, String> entry : params.entrySet()) {
				nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
			}
		}

		if (heads != null) {
			for (Map.Entry<String, String> entry : heads.entrySet()) {
				httpPost.setHeader(entry.getKey(), entry.getValue());
			}
		}

		httpPost.setEntity(new UrlEncodedFormEntity(nvps,"utf-8"));
		CloseableHttpResponse response = httpclient.execute(httpPost);
		return EntityUtils.toString(response.getEntity());
	}

	public static String getResponse(Map<String, String> params, String url,Map<String, String> heads) {
		String response=null;
		logger.info("访问url:{},参数:{}",url,params);
		long start=System.currentTimeMillis();
		try {
			response= HttpUtil.post(url, params, heads);
		} catch (Exception e) {
			logger.error("出现异常：",e);
		}
		long end=System.currentTimeMillis();
		logger.info("接口调用耗时:{}ms,接口返回结果：{}",(end-start),response);
		return response;
	}

	public static String getResponse(Object object, String url,Map<String, String> heads) {
		String response=null;
		try {
			response= HttpUtil.post(url, object, heads);
		} catch (IOException e) {
			e.printStackTrace();
		} catch (KeyManagementException e) {
			e.printStackTrace();
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} finally {

		}
		return response;
	}

	private static final String UTF_FORMAT = "UTF-8";
	public static String  postForm(String url, String json, Map<String, String> headers) {
		URL u = null;
		HttpURLConnection con = null;
		OutputStreamWriter osw = null;
		// 构建请求参数
		StringBuffer sb = new StringBuffer();
		sb.append(json);
		// 尝试发送请求
		try {
			u = new URL(url);
			con = (HttpURLConnection) u.openConnection();
			// // POST 只能为大写，严格限制，post会不识别
			con.setRequestMethod("POST");
			con.setDoOutput(true);
			con.setDoInput(true);
			con.setUseCaches(false);
			con.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			con.setConnectTimeout(10000);
			if(headers != null){
				for (String header : headers.keySet()) {
					con.setRequestProperty(header, headers.get(header));
				}
			}
			osw = new OutputStreamWriter(con.getOutputStream(), UTF_FORMAT);
			osw.write(sb.toString());
			osw.flush();
			osw.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (con != null) {
				con.disconnect();
			}
			if (osw != null) {
				try {
					osw.close();
				} catch (Exception ex) {
					ex.printStackTrace();
				}
			}
		}
		// 读取返回内容
		StringBuffer buffer = new StringBuffer();
		try {
			// 一定要有返回值，否则无法把请求发送给server端。
			BufferedReader br = new BufferedReader(new InputStreamReader(con.getInputStream(), UTF_FORMAT));
			String temp;
			while ((temp = br.readLine()) != null) {
				buffer.append(temp);
				buffer.append("\n");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return buffer.toString();
	}



	/**
	 * v4.3
	 * 模拟POST方式提交(表单参数)
	 * @param url
	 * @param paramsMap
	 * @return
	 * @throws ClientProtocolException
	 * @throws IOException
	 * <AUTHOR> 2014-12-19
	 */
	public static String doPost(String url, Map<String,Object> paramsMap)throws ClientProtocolException, IOException {
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		//添加参数
		List<NameValuePair> list=new ArrayList<NameValuePair>();
		Set<Map.Entry<String, Object>> entrySet = paramsMap.entrySet();
		for (Map.Entry<String, Object> entry : entrySet) {
			list.add(new BasicNameValuePair(entry.getKey(), entry.getValue()+""));
		}
		httpPost.setEntity(new UrlEncodedFormEntity(list,UTF_FORMAT));
		String result = null;
		try {
			HttpResponse res = httpClient.execute(httpPost);
			if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				HttpEntity entity = res.getEntity();
				result = EntityUtils.toString(entity, UTF_FORMAT);
			}
		} catch (Exception e) {
			throw new RuntimeException(e);

		} finally {
			// 关闭连接，释放资源
			httpClient.close();
		}
		return result;
	}
}
