package com.xyy.ec.pop.utils;

import org.springframework.web.multipart.MultipartFile;

/**
 * @version v1
 * @Description 解析名工具
 * <AUTHOR>
 */
public class FileNameUtils {
    public static String getFileType(String file) {
        int splitIndex = file.lastIndexOf(".");
        if(splitIndex<=0||splitIndex==(file.length()-1)){
            return null;
        }
        return file.substring(splitIndex+1);
    }

    public static String getFileName(String item) {
        int splitIndex = item.lastIndexOf(".");
        if(splitIndex<=0){
            return item;
        }
        return item.substring(0,splitIndex).trim();
    }

    public static String getErrorFileName(MultipartFile file){
        String fileName = file.getOriginalFilename();
        String fileType =".xlsx";// fileName.substring(fileName.lastIndexOf("."));
        return fileName.substring(0,fileName.length()-fileType.length())+"-错误"+fileType;
    }

    public static String getErrorFileDownUrl(String errorFileUrl,String errorFileName){
       return  "/uploadFile/downloadFromFastDfs?path="+errorFileUrl+"&fileName="+errorFileName;
    }

    /**
     * 拼接路径 文件 并去除路径中重复的分割符
     *
     * @param path
     * @param pathSeparator
     * @param fileName
     * @return
     */
    public static String margeName(String path, String pathSeparator, String fileName) {
        fileName = path + pathSeparator + fileName;
        String befreName = null;
        do {
            befreName = fileName;
            fileName = fileName.replace(pathSeparator + pathSeparator, pathSeparator);
        } while (!befreName.equals(fileName));
        return fileName;
    }
}
