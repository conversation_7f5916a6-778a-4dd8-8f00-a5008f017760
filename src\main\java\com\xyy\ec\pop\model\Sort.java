/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.xyy.ec.pop.model;

import java.io.Serializable;

/**
 * bootstrap-table排序辅助类
 * ClassName: Sort <br/>
 * date: 2015-9-21 下午3:06:22 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.7
 */
public class Sort implements Serializable {
	private static final long serialVersionUID = 1884229556863409516L;
	public static final String ASC = "ASC";
    public static final String DESC = "DESC";

    private String property;
    private String direction;

    public Sort() {

    }

    public Sort(String prop, String direction) {
        this.property = prop;
        this.direction = direction;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

}
