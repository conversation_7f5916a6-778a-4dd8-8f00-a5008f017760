package com.xyy.ec.pop.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.label.server.business.api.LabelQueryApi;
import com.xyy.ec.label.server.business.dto.BatchCheckLabelParam;
import com.xyy.ec.label.server.business.dto.CheckLabelParam;
import com.xyy.ec.label.server.business.dto.EcpLabelDTO;
import com.xyy.ec.pop.helper.InsightLabelDTOHelper;
import com.xyy.ec.pop.marketing.dto.InsightLabelDTO;
import com.xyy.ec.pop.remote.LabelQueryRPCService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * {@link LabelQueryApi}
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class LabelQueryRPCServiceImpl implements LabelQueryRPCService {

    @Reference(version = "1.0.0")
    private LabelQueryApi labelQueryApi;

    @Override
    public List<InsightLabelDTO> listAllLabels() {
        try {
            ApiRPCResult<List<EcpLabelDTO>> apiRPCResult = labelQueryApi.queryAllEcpLabel();
            log.info("获取全部的标签列表，响应：{}", JSONObject.toJSONString(apiRPCResult));
            if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
                log.error("获取全部的标签列表失败，响应：{}", JSONObject.toJSONString(apiRPCResult));
                return Lists.newArrayList();
            }
            List<EcpLabelDTO> data = apiRPCResult.getData();
            if (CollectionUtils.isEmpty(data)) {
                return Lists.newArrayList();
            }
            return InsightLabelDTOHelper.creates(data);
        } catch (Exception e) {
            log.error("获取全部的标签列表失败", e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<InsightLabelDTO> listLabelByIds(List<Long> labelIds) {
        if (CollectionUtils.isEmpty(labelIds)) {
            return Lists.newArrayList();
        }
        Map<Long, InsightLabelDTO> labelIdToInfoMap = Maps.newHashMapWithExpectedSize(16);
        List<List<Long>> queryLabelIdsLists = Lists.partition(labelIds, 200);
        for (List<Long> queryLabelIdsList : queryLabelIdsLists) {
            try {
                ApiRPCResult<List<EcpLabelDTO>> apiRPCResult = labelQueryApi.queryEcpLabelByIdList(queryLabelIdsList);
                log.info("批量根据标签ID获取标签信息，参数：queryLabelIdsList：{}，响应：{}",
                        JSONArray.toJSONString(queryLabelIdsList), JSONObject.toJSONString(apiRPCResult));
                if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
                    log.error("批量根据标签ID获取标签信息失败，参数：queryLabelIdsList：{}，响应：{}",
                            JSONArray.toJSONString(queryLabelIdsList), JSONObject.toJSONString(apiRPCResult));
                    continue;
                }
                List<EcpLabelDTO> data = apiRPCResult.getData();
                if (CollectionUtils.isEmpty(data)) {
                    continue;
                }
                for (EcpLabelDTO ecpLabelDTO : data) {
                    if (Objects.isNull(ecpLabelDTO)) {
                        continue;
                    }
                    labelIdToInfoMap.put(ecpLabelDTO.getId(), InsightLabelDTOHelper.create(ecpLabelDTO));
                }
            } catch (Exception e) {
                log.error("批量根据标签ID获取标签信息失败，参数：queryLabelIdsList：{}", JSONArray.toJSONString(queryLabelIdsList), e);
            }
        }
        return labelIds.stream().map(item -> labelIdToInfoMap.get(item)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public Set<Long> checkLabelIds(Set<Long> labelIds) {
        if (CollectionUtils.isEmpty(labelIds)) {
            return Sets.newHashSet();
        }
        labelIds.remove(null);
        if (CollectionUtils.isEmpty(labelIds)) {
            return Sets.newHashSet();
        }
        Set<Long> result = Sets.newHashSetWithExpectedSize(labelIds.size());
        List<Long> queryLabelIds = Lists.newArrayList(labelIds);
        List<List<Long>> queryLabelIdsLists = Lists.partition(queryLabelIds, 200);
        for (List<Long> queryLabelIdsList : queryLabelIdsLists) {
            try {
                ApiRPCResult<List<Long>> apiRPCResult = labelQueryApi.checkLabel(queryLabelIdsList);
                log.info("校验标签是否有效存在，参数：queryLabelIdsList：{}，响应：{}",
                        JSONArray.toJSONString(queryLabelIdsList), JSONObject.toJSONString(apiRPCResult));
                if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
                    log.error("校验标签是否有效存在失败，参数：queryLabelIdsList：{}，响应：{}",
                            JSONArray.toJSONString(queryLabelIdsList), JSONObject.toJSONString(apiRPCResult));
                    continue;
                }
                List<Long> data = apiRPCResult.getData();
                if (CollectionUtils.isEmpty(data)) {
                    continue;
                }
                Set<Long> tempInLabelIdsSet = data.stream()
                        .filter(item -> Objects.nonNull(item))
                        .collect(Collectors.toSet());
                result.addAll(tempInLabelIdsSet);
            } catch (Exception e) {
                log.error("校验标签是否有效存在失败，参数：queryLabelIdsList：{}", JSONArray.toJSONString(queryLabelIdsList), e);
            }
        }
        return result;
    }

    @Override
    public Set<Long> checkUserIsInLabelIds(Long merchantId, Set<Long> labelIds) {
        if (Objects.isNull(merchantId) || CollectionUtils.isEmpty(labelIds)) {
            return Sets.newHashSet();
        }
        labelIds.remove(null);
        if (CollectionUtils.isEmpty(labelIds)) {
            return Sets.newHashSet();
        }
        Set<Long> inLabelIdsSet = Sets.newHashSetWithExpectedSize(labelIds.size());
        List<Long> queryLabelIds = Lists.newArrayList(labelIds);
        List<List<Long>> queryLabelIdsLists = Lists.partition(queryLabelIds, 50);
        CheckLabelParam checkLabelParam = new CheckLabelParam();
        for (List<Long> queryLabelIdsList : queryLabelIdsLists) {
            try {
                checkLabelParam.setMerchantId(merchantId);
                checkLabelParam.setLabelIdList(queryLabelIdsList);
                ApiRPCResult<List<Long>> apiRPCResult = labelQueryApi.checkUserLabel(checkLabelParam);
                log.info("校验用户是否在标签内，参数：checkLabelParam：{}，响应：{}",
                        JSONObject.toJSONString(checkLabelParam), JSONObject.toJSONString(apiRPCResult));
                if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
                    log.error("校验用户是否在标签内失败，参数：checkLabelParam：{}，响应：{}",
                            JSONObject.toJSONString(checkLabelParam), JSONObject.toJSONString(apiRPCResult));
                    continue;
                }
                List<Long> data = apiRPCResult.getData();
                if (CollectionUtils.isEmpty(data)) {
                    continue;
                }
                Set<Long> tempInLabelIdsSet = data.stream()
                        .filter(item -> Objects.nonNull(item))
                        .collect(Collectors.toSet());
                inLabelIdsSet.addAll(tempInLabelIdsSet);
            } catch (Exception e) {
                log.error("校验用户是否在标签内失败，参数：checkLabelParam：{}", JSONObject.toJSONString(checkLabelParam), e);
            }
        }
        return inLabelIdsSet;
    }

    @Override
    public Map<Long, Set<Long>> checkUsersIsInLabelIds(Set<Long> merchantIds, Set<Long> labelIds) {
        if (CollectionUtils.isEmpty(merchantIds) || CollectionUtils.isEmpty(labelIds)) {
            return Maps.newHashMap();
        }
        merchantIds.remove(null);
        if (CollectionUtils.isEmpty(merchantIds)) {
            return Maps.newHashMap();
        }
        labelIds.remove(null);
        if (CollectionUtils.isEmpty(labelIds)) {
            return Maps.newHashMap();
        }
        Map<Long, Set<Long>> result = Maps.newHashMapWithExpectedSize(16);
        List<Long> queryMerchantIds = Lists.newArrayList(merchantIds);
        List<List<Long>> queryMerchantIdsLists = Lists.partition(queryMerchantIds, 200);
        List<Long> queryLabelIds = Lists.newArrayList(labelIds);
        List<List<Long>> queryLabelIdsLists = Lists.partition(queryLabelIds, 50);
        BatchCheckLabelParam batchCheckLabelParam = new BatchCheckLabelParam();
        for (List<Long> queryMerchantIdsList : queryMerchantIdsLists) {
            for (List<Long> queryLabelIdsList : queryLabelIdsLists) {
                try {
                    batchCheckLabelParam.setMerchantIdList(queryMerchantIdsList);
                    batchCheckLabelParam.setLabelIdList(queryLabelIdsList);
                    ApiRPCResult<Map<Long, List<Long>>> apiRPCResult = labelQueryApi.batchCheckUserLabel(batchCheckLabelParam);
                    log.info("校验多个用户是否在标签内，参数：batchCheckLabelParam：{}，响应：{}",
                            JSONObject.toJSONString(batchCheckLabelParam), JSONObject.toJSONString(apiRPCResult));
                    if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
                        log.error("校验多个用户是否在标签内失败，参数：batchCheckLabelParam：{}，响应：{}",
                                JSONObject.toJSONString(batchCheckLabelParam), JSONObject.toJSONString(apiRPCResult));
                        continue;
                    }
                    Map<Long, List<Long>> data = apiRPCResult.getData();
                    if (MapUtils.isEmpty(data)) {
                        continue;
                    }
                    for (Map.Entry<Long, List<Long>> entry : data.entrySet()) {
                        Long merchantId = entry.getKey();
                        List<Long> ecpLabelDTOS = entry.getValue();
                        if (Objects.isNull(merchantId) || CollectionUtils.isEmpty(ecpLabelDTOS)) {
                            continue;
                        }
                        Set<Long> inLabelIdsSet = result.get(merchantId);
                        if (Objects.isNull(inLabelIdsSet)) {
                            inLabelIdsSet = Sets.newHashSetWithExpectedSize(16);
                            result.put(merchantId, inLabelIdsSet);
                        }
                        Set<Long> tempInLabelIdsSet = ecpLabelDTOS.stream()
                                .filter(item -> Objects.nonNull(item))
                                .collect(Collectors.toSet());
                        inLabelIdsSet.addAll(tempInLabelIdsSet);
                    }
                } catch (Exception e) {
                    log.error("校验多个用户是否在标签内失败，参数：batchCheckLabelParam：{}", JSONObject.toJSONString(batchCheckLabelParam), e);
                }
            }
        }
        return result;
    }
}
