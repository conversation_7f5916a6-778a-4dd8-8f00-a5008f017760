package com.xyy.ec.pop.service.impl;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.BondHelper;
import com.xyy.ec.pop.remote.BondRemoteAdapter;
import com.xyy.ec.pop.remote.PopCorporationRemoteAdapter;
import com.xyy.ec.pop.server.api.merchant.api.enums.BondStatusEnum;
import com.xyy.ec.pop.server.api.merchant.dto.BondDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.service.BondService;
import com.xyy.ec.pop.service.PopCorporationAreaService;
import com.xyy.ec.pop.vo.BondVo;
import com.xyy.ec.pop.vo.CorporationAreaInfoVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @version v1
 * @Description 商户保证金
 * <AUTHOR>
 */
@Service
public class BondServiceImpl implements BondService {
    @Autowired
    private BondRemoteAdapter bondRemoteAdapter;
    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;
    @Autowired
    private PopCorporationAreaService corporationAreaService;
    @Override
    public BondVo getBond(String orgId) {
        BondDto bond = bondRemoteAdapter.queryByOrgId(orgId);
        if(bond==null){
            return null;
        }
        return BondHelper.convertToBondVo(bond);
    }

    @Override
    public void saveBond(BondVo bond, String username) throws ServiceException {
//        BondDto his = bondRemoteAdapter.queryByOrgId(bond.getOrgId());
//        if(his!=null&&his.getState()== BondStatusEnum.APPROVED.getCode()){
//            throw new ServiceException("保证金已审核通过，不允许修改");
//        }
        CorporationDto corporationDto = popCorporationRemoteAdapter.queryByOrgId(bond.getOrgId());
        if(corporationDto==null){
            throw new ServiceException("机构不存在");
        }
        List<CorporationAreaInfoVo> areaVos = corporationAreaService.getCorporationArea(corporationDto.getId());
        if(CollectionUtils.isEmpty(areaVos)){
            throw new ServiceException("请先设置可售卖区域");
        }
        bondRemoteAdapter.saveBond(BondHelper.convertToBondDto(bond),username);
    }
}
