package com.xyy.ec.pop.marketing.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.marketing.elephant.params.MarketingActivitySaleDataExportPagingQueryParam;
import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataStatisticsInfoVO;
import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataStatusCountInfoVO;
import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataVO;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ms.promotion.business.params.*;
import com.xyy.ms.promotion.business.result.MarketingActivitySaleDataSearchInfoDTO;
import com.xyy.ms.promotion.business.result.MarketingActivitySaleDataSummaryInfoDTO;
import org.apache.commons.collections4.keyvalue.DefaultKeyValue;

import java.util.List;

public interface MarketingActivitySaleDataService {

    /**
     * 查询省份列表
     *
     * @return
     */
    List<DefaultKeyValue<Integer, String>> listProvinces();

    /**
     * 查询订单状态列表
     *
     * @return
     */
    List<DefaultKeyValue<Integer, String>> listOrderStatus();

    /**
     * 获取活动销售数据概要信息
     *
     * @param queryParam
     * @return
     */
    MarketingActivitySaleDataSummaryInfoDTO getSummaryInfo(MarketingActivitySaleDataSummaryInfoQueryParam queryParam);

    /**
     * 获取活动销售数据查询信息
     *
     * @param queryParam
     * @return
     */
    MarketingActivitySaleDataSearchInfoDTO getSearchInfo(MarketingActivitySaleDataSearchInfoQueryParam queryParam);

    /**
     * 根据条件分页查询销售数据
     *
     * @param queryParam
     * @return
     */
    PageInfo<MarketingActivitySaleDataVO> paging(MarketingActivitySaleDataListQueryParam queryParam);

    /**
     * 根据条件查询销售统计数据
     *
     * @param queryParam
     * @return
     */
    MarketingActivitySaleDataStatisticsInfoVO getStatisticsInfo(MarketingActivitySaleDataStatisticsQueryParam queryParam);

    /**
     * 根据条件查询状态及数量
     *
     * @param queryParam
     * @return
     */
    MarketingActivitySaleDataStatusCountInfoVO getStatusCountInfo(MarketingActivitySaleDataOrderStatusCountQueryParam queryParam);

    /**
     * 导出
     *
     * @param user
     * @param queryParam
     */
    void asyncExport(SysUser user, MarketingActivitySaleDataExportPagingQueryParam queryParam);
}
