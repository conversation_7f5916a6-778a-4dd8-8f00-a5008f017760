package com.xyy.ec.pop.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description 退款单列表查询参数
 */
@Data
public class RefundOrderParamVo {
    /**
     * 区域机构id集合
     */
    private List<String> orgIdList;

    private String orgId;

    /**
     * 区域编码
     */
    private String branchCode;

    /**
     * 省份编码
     */
    private Integer provinceCode;
    /**
     * 商户编号
     */
    private String corporationNo;

    /**
     * 店铺名称
     */
    private String corporationName;

    /**
     * 机构名称
     */
    private String companyName;
    /**
     *下单用户
     */
    private String merchantName;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 退款单编号
     */
    private String refundOrderNo;

    /**
     * 退款状态
     */
    private Integer auditState;
    /**
     * 申请开始日期
     */
    private Date startCreateTime;
    /**
     * 审请结束日期
     */
    private Date endCreateTime;

    /**
     * 申请开始日期
     */
    private Date startRefundFinishTime;
    /**
     * 审请结束日期
     */
    private Date endRefundFinishTime;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 退款状态名称
     */
    private String adminAuditStatusName;
    /**
     * 退款发起方
     */
    private Integer refundChannel;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 支付类型
     */
    public Integer payType;
    /**
     * 起始页
     */
    private Integer offset;
    /**
     * 每页显示的条数
     */
    private Integer limit;
    /**
     * 支付渠道
     */
    private Integer payChannel;

    private Long provId;

    private List<Long> provIds;

    private List<String> refundOrderNos;

    /* 是否入仓订单 精准搜索 0 否 1是 */
    private Integer isFbp;

    /**
     * 退款支付状态 1：已发起，2：退款成功，3：退款失败，4：处理中
     */
    private Integer payStatus;
    /** 是否第三方厂家（0：否；1：是） */
    private Integer isThirdCompany;
}
