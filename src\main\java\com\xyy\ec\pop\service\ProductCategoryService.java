package com.xyy.ec.pop.service;

import com.xyy.me.product.service.read.dto.TotalDictionaryReadDto;

import java.util.List;
import java.util.Map;

/**
 * 商品父类服务
 */
public interface ProductCategoryService {

    /**
     * 查询所有六级分类信息
     * @return
     */
    List<TotalDictionaryReadDto> getAllProductCategory();

    /**
     * 查询所有四级分类信息
     * @return
     */
    List<TotalDictionaryReadDto> getFourLevelProductCategory();

    Map<String, TotalDictionaryReadDto> getFirstLevelProductCategoryMap();
    /**
     * 查询两级分类信息
     * @return
     */
    List<TotalDictionaryReadDto> getTwoLevelProductCategory();

    List<TotalDictionaryReadDto> getFourLevelProductCategoryByIds(List<Integer> idList);

    List<TotalDictionaryReadDto> getProductCategory(List<Byte> levelNodeList, List<Integer> idList);
}
