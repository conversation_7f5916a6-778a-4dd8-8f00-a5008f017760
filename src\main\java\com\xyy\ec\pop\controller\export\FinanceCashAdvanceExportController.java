package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.export.param.CashAdvanceAdminParam;
import com.xyy.ec.pop.server.api.seller.enums.PopCashAdvanceStatusEnum;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/08/08/20:46
 * @Description:
 */
@Controller
@Slf4j
@RequestMapping("/cashAdvance/async")
public class FinanceCashAdvanceExportController extends BaseController {
    @Autowired
    private DownloadRemote downloadRemote;

    @ResponseBody
    @GetMapping(value = "/export")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> export(CashAdvanceAdminParam queryDto) {
        log.info("CommissionSettleExportController.export#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            queryDto.setPaymentStatus(Integer.valueOf(PopCashAdvanceStatusEnum.SUCCESS.getStatus()).byteValue());
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.FINANCE_CASH_ADVANCE_ADMIN)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("CommissionSettleExportController.export#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        }catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("CommissionSettleExportController.export#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }
}
