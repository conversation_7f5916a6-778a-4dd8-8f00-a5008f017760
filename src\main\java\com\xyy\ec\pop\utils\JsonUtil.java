package com.xyy.ec.pop.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;

/**
 * 版权：小药药
 * 作者：<EMAIL>
 * 生成日期：2017/3/15
 * 描述：json相关工具类
 */
public class JsonUtil {


    private static Logger logger = LoggerFactory.getLogger(JsonUtil.class);

    /**
     * java对象转json
     *
     * @param object
     * @return
     * @throws JsonProcessingException
     */
    public static String wapperObjToString(Object object) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        // Convert object to JSON string
        return mapper.writeValueAsString(object);
    }

    /**
     * json转java对象
     *
     * @param jsonString
     * @param clazz
     * @return
     * @throws IOException
     */
    public static <T> T wapperStringToObj(String jsonString, Class<T> clazz) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return StringUtil.isEmpty(jsonString) ? null : mapper.readValue(jsonString, clazz);
    }

    /**
     * 对象转换成JSON字符串
     *
     * @param obj
     * @return String
     * @Title: toJson
     * <AUTHOR>
     * @date 2016-5-11 下午11:38:45
     */
    public static String toJson(Object obj) {
        return JSON.toJSONString(obj);
    }

    /**
     * JSON字符串转换为对象
     *
     * @param jsonString
     * @param type
     * @return T
     * @Title: fromJson
     * <AUTHOR>
     * @date 2016-5-11 下午11:39:20
     */
    public static <T> T fromJson(String jsonString, Class<T> type) {
        return JSON.parseObject(jsonString, type);
    }

    /**
     * 将JSONArray对象转换成list集合
     *
     * @param jsonArr
     * @return
     */
    public static List<Object> jsonToList(JSONArray jsonArr) {
        List<Object> list = new ArrayList<Object>();
        for (Object obj : jsonArr) {
            if (obj instanceof JSONArray) {
                list.add(jsonToList((JSONArray) obj));
            } else if (obj instanceof JSONObject) {
                list.add(jsonToMap((JSONObject) obj));
            } else {
                list.add(obj);
            }
        }
        return list;
    }

    /**
     * 将json字符串转换成map对象
     *
     * @param json
     * @return
     */
    public static Map<String, Object> jsonToMap(String json) {
        JSONObject obj = JSON.parseObject(json);
        return jsonToMap(obj);
    }

    /**
     * JSON对象转换为Map
     *
     * @param obj
     * @return Map<String,Object>
     * @Title: jsonToMap
     * <AUTHOR>
     * @date 2016-5-11 下午11:40:01
     */
    public static Map<String, Object> jsonToMap(JSONObject obj) {
        Set<?> set = obj.keySet();
        Map<String, Object> map = new HashMap<String, Object>(set.size());
        for (Object key : obj.keySet()) {
            Object value = obj.get(key);
            if (value instanceof JSONArray) {
                map.put(key.toString(), jsonToList((JSONArray) value));
            } else if (value instanceof JSONObject) {
                map.put(key.toString(), jsonToMap((JSONObject) value));
            } else {
                map.put(key.toString(), obj.get(key));
            }

        }
        return map;
    }

    /**
     * json转java集合
     *
     * @param jsonString
     * @param clazz
     * @return
     * @throws IOException
     */
    public static <T> List<T> wapperStringToList(String jsonString, Class<T> clazz) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(jsonString, getCollectionType(mapper, List.class, clazz));
    }

    /**
     * json转java集合
     *
     * @param jsonString
     * @param clazz
     * @return
     * @throws IOException
     */
    public static <T> Set<T> wapperStringToSet(String jsonString, Class<T> clazz) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(jsonString, getCollectionType(mapper, Set.class, clazz));
    }


    public static JavaType getCollectionType(ObjectMapper mapper, Class<?> collectionClass, Class<?>... elementClasses) {
        return mapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
    }

    /**
     * 处理取出缓存中list的序列化和反序列化后不是list的问题
     *
     * @param objectList
     * @param clazz
     * @return
     * @throws Exception
     */
    public static <T> List<T> listToList(List<T> objectList, Class<T> clazz) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
//        List<Object> objList = mapper.readValue(JSON.toJSON(objectList).toString(), new TypeReference<List<Object>>() {});
        return mapper.readValue(JSON.toJSON(objectList).toString(), getCollectionType(mapper, List.class, clazz));
    }


    public static Integer extractInteger(JSONObject json, String key) {

        if (json != null && json.containsKey(key)) {
            try {
                return json.getInteger(key);
            } catch (Exception e) {
            }
        }
        return 0;
    }

    public static Integer extractInteger(JSONObject json, String key, Integer defaultValue) {

        if (json != null && json.containsKey(key)) {
            try {
                return json.getInteger(key);
            } catch (Exception e) {
            }
        }
        return defaultValue;
    }

    public static Long extractLong(JSONObject json, String key, Long defaultValue) {

        if (json != null && json.containsKey(key)) {
            try {
                return json.getLong(key);
            } catch (Exception e) {
            }
        }
        return defaultValue;
    }

    public static JSONArray extractJsonArray(JSONObject json, String key) {

        if (json != null && json.containsKey(key)) {
            try {
                return json.getJSONArray(key);
            } catch (Exception e) {
            }
        }
        return new JSONArray();
    }

    /** 判断是否为 jsonobject */
    public static boolean isJsonObject(String str) {

        try {
            JSONObject jr = JSON.parseObject(str);
            return true;
        } catch (Exception e) {
           logger.error("str is not json  format {} and e is {}",str,e);
           return false;
        }
    }

    /** 判断是否为 jsonArray */
    public static boolean isJsonArray(String str) {

        try {
            JSONArray jr = JSON.parseArray(str);
            return true;
        } catch (Exception e) {
            logger.error("str is not json  format {} and e is {}",str,e);
            return false;
        }
    }




}
