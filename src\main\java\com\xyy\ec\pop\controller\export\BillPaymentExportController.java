package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.export.param.OfflineBillExportAdminParam;
import com.xyy.ec.pop.server.api.seller.enums.BillPayTypeEnum;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description 入账单导出接口
 */
@Slf4j
@RequestMapping("/billPayment/async")
@RestController
public class BillPaymentExportController extends BaseController {
    @Autowired
    private DownloadRemote downloadRemote;

    /**
     * 导出入账单
     *
     * @param query
     * @return
     */
    @RequestMapping(value = "/exportBillPaymemtList", method = RequestMethod.GET)
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportBillPaymemtList(OfflineBillExportAdminParam query) {
        try {
            List<Long> provIds = getProvIds(query.getProvId());
            if (CollectionUtils.isEmpty(provIds)) {
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            query.setProvIds(provIds);
            log.info("BillPaymentExportController.exportBillPaymemtList#query:{}", JSON.toJSONString(query));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.BILL_PAYMENT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("BillPaymentExportController.exportBillPaymemtList#query:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e) {
            log.error("BillPaymentExportController.exportBillPaymemtList#query:{} 自定义异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillPaymentExportController.exportBillPaymemtList#query:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出入账单明细
     *
     * @param query
     * @return
     */
    @RequestMapping(value = "/exportBillPaymemtDetailList", method = RequestMethod.GET)
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportBillPaymemtDetailList(OfflineBillExportAdminParam query) {
        try {
            List<Long> provIds = getProvIds(query.getProvId());
            if (CollectionUtils.isEmpty(provIds)) {
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            query.setProvIds(provIds);
            log.info("BillPaymentExportController.exportBillPaymemtDetailList#query:{}", JSON.toJSONString(query));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.BILL_PAYMENT_DETAIL)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("OnlinePayPopBillPayExportController.exportBillPaymemtDetailList#popBillPayVo:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e) {
            log.error("OnlinePayPopBillPayExportController.exportBillPaymemtDetailList#popBillPayVo:{} 自定义异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("OnlinePayPopBillPayExportController.exportBillPaymemtDetailList#popBillPayVo:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }
}
