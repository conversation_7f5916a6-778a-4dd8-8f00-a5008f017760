package com.xyy.ec.pop.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.server.api.erpUtil.dto.PopCorporationDto;
import com.xyy.ec.pop.server.api.fdd.dto.TbXyyPopFddEnterpriseAgreementDTO;
import com.xyy.ec.pop.server.api.fdd.dto.TbXyyPopFddEnterpriseEmpowerDTO;
import com.xyy.ec.pop.server.api.fdd.param.FddSignaturesParam;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.fdd.FddEnterpriseEmpowerVo;

public interface PlatformServiceAgreementService {
    PageInfo<TbXyyPopFddEnterpriseAgreementDTO> platformAgreementList(FddSignaturesParam query);

    TbXyyPopFddEnterpriseAgreementDTO platformAgreementById(Long id);

    void platformAgreementTerminateTask(Long id, String reason,String username);

    ResponseVo platformAgreementAddAgreement(TbXyyPopFddEnterpriseAgreementDTO agreement, String userName) throws Exception;

    ResponseVo platformAgreementSubmitAgreement(TbXyyPopFddEnterpriseAgreementDTO agreement) throws Exception;

    String openedOrgChange(String sourceOrg, String targetOrg,String username,Integer type) throws Exception;

    String changeFddAdminAccount(FddEnterpriseEmpowerVo vo) throws Exception;
}
