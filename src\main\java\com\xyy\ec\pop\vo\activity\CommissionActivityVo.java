package com.xyy.ec.pop.vo.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @TableName tb_xyy_pop_commission_activity
 */
@Data
public class CommissionActivityVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 商户名称
     */
    private String orgName;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    @JsonFormat(pattern = "yyyy-MM")
    private Date startDate;

    /**
     * 活动结束时间
     */
    @JsonFormat(pattern = "yyyy-MM")
    private Date endDate;

    /**
     * 活动状态 1 未开始 2进行中 3已结束
     */
    private Integer status;

    /**
     * 生效客户类型，多个类型使用逗号拼接
     */
    private String businessTypes;

    /**
     * 高毛利折扣配置 佣金比例
     */
    private BigDecimal highDiscountRatio;

    /**
     * 高毛折扣配置 1不享受折扣 2商品一级分类佣金比例部分享受折扣
     */
    private Integer highDiscountStatus;

    /**
     * 活动附件url
     */
    private String annexUrl;

    /**
     * 活动编码 唯一标识
     */
    private Long activityId;

    /**
     * 活动门槛
     */
    List<CommissionActivityFenceVo> fenceList;

    /**
     * 再次确认标识 true为再次确认请求
     */
    private boolean confirmAgain;

}