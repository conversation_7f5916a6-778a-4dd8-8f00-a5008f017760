package com.xyy.ec.pop.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description 商品佣金比例设置
 */
@Data
public class ProductCommissionBatchUpdateVo {
    /**
     * 商品编码
     */
    @Excel(name="CSUID")
    private Long csuid;

    /**
     * 商品编码
     */
    private String barcode;
    /**
     * 商品佣金比例
     */
    @Excel(name="佣金比例")
    private String commissionRatio;
    /**
     * 错误原因
     */
    @Excel(name="错误原因")
    private String errorMessage;
}
