package com.xyy.ec.pop.controller;

import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.excel.verify.AdjustiveBillSettleExcelVerifyHandler;
import com.xyy.ec.pop.excel.verify.AdjustiveBillSettleValid;
import com.xyy.ec.pop.excel.verify.InvoiceApplyExcelVerifyHandle;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleBillDto;
import com.xyy.ec.pop.server.api.seller.dto.PopInvoiceApplyDto;
import com.xyy.ec.pop.server.api.seller.dto.PopInvoiceApplyForExportDto;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.server.api.seller.param.PopInvoiceApplyAdminParam;
import com.xyy.ec.pop.service.ExportService;
import com.xyy.ec.pop.service.InvoiceApplyService;
import com.xyy.ec.pop.utils.ExceImportWarpUtil;
import com.xyy.ec.pop.utils.FileNameUtils;
import com.xyy.ec.pop.vo.AdjustiveBillSettleResultVo;
import com.xyy.ec.pop.vo.AdjustiveBillSettleVo;
import com.xyy.ec.pop.vo.InvoiceApplyExportVo;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import com.xyy.pop.framework.core.vo.Response;
import io.swagger.annotations.Api;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发票申请单Controller
 *
 * <AUTHOR>
 * @date 2021-03-09
 * @Description:
 */
@Api(tags = "发票申请单")
@RestController
@RequestMapping(value = "/invoice")
public class InvoiceApplyController extends BaseController {
    private static final Logger LOG = LoggerFactory.getLogger(InvoiceApplyController.class);
    @Autowired
    private InvoiceApplyService invoiceApplyService;
    @Value("${invoice.apply.export.max.count}")
    private Integer exportMaxCount;
    @Autowired
    private ExportService exportService;
    @Autowired
    private DownloadRemote downloadRemote;

    private static final String EXCEL_NAME = "发票申请记录";


    /**
     * 发票申请单列表
     *
     * @param popInvoiceApplyAdminParam
     * @return
     */
    @GetMapping(value = "/queryInvoiceListForPage")
    public Response<PageInfo<PopInvoiceApplyDto>> queryInvoiceListForPage(PopInvoiceApplyAdminParam popInvoiceApplyAdminParam) {
        List<Long> provIds = getProvIds(popInvoiceApplyAdminParam.getProvId());
        if(CollectionUtils.isEmpty(provIds)){
            return ResponseUtils.returnObjectSuccess(new PageInfo<>());
        }
        popInvoiceApplyAdminParam.setProvIds(provIds);
        LOG.info("InvoiceApplyController.queryInvoiceListForPage#popInvoiceApplyAdminParam:{}", JSON.toJSONString(popInvoiceApplyAdminParam));
        try {
            PageInfo<PopInvoiceApplyDto> pageInfo = invoiceApplyService.queryInvoiceListForPage(popInvoiceApplyAdminParam);
            return ResponseUtils.returnObjectSuccess(pageInfo);
        } catch (Exception e) {
            LOG.error("InvoiceApplyController.queryInvoiceListForPage#查询发票申请单列表异常 popInvoiceApplyAdminParam:{}", JSON.toJSONString(popInvoiceApplyAdminParam), e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 查看发票申请单基本信息
     *
     * @param invoiceApplyNo
     * @return
     */
    @GetMapping(value = "/getInvoiceBaseInfo")
    public Response<PopInvoiceApplyDto> getInvoiceBaseInfo(String invoiceApplyNo) {
        LOG.info("InvoiceApplyController.getInvoiceBaseInfo#invoiceApplyNo:{}", invoiceApplyNo);
        try {
            PopInvoiceApplyDto popInvoiceApplyDto = invoiceApplyService.getInvoiceBaseInfo(invoiceApplyNo);
            return ResponseUtils.returnObjectSuccess(popInvoiceApplyDto);
        } catch (Exception e) {
            LOG.error("InvoiceApplyController.getInvoiceBaseInfo#查询发票申请单基本信息异常 invoiceApplyNo:{}", invoiceApplyNo, e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 查看发票明细列表
     *
     * @param invoiceApplyNo
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping(value = "/queryInvoiceDetailListForPage")
    public Response<PageInfo<PopCommissionSettleBillDto>> queryInvoiceDetailListForPage(String invoiceApplyNo, Integer pageNum, Integer pageSize) {
        LOG.info("InvoiceApplyController.queryInvoiceDetailListForPage#查看发票明细列表 invoiceApplyNo:{},pageNum:{},pageSize:{}", invoiceApplyNo, pageNum, pageSize);
        try {
            PageInfo<PopCommissionSettleBillDto> pageInfo = invoiceApplyService.queryInvoiceDetailListForPage(invoiceApplyNo, pageNum, pageSize);
            //合并佣金金额字段
            if (pageInfo != null) {
                pageInfo.getList().stream().forEach(item -> {
                    if (item.getSettlementType() == CommissionSettleTypeEnum.EVERY_MONTH.getCode()) {
                        item.setBillHireMoney(item.getPayableCommission());
                    }
                });
            }
            return ResponseUtils.returnObjectSuccess(pageInfo);
        } catch (Exception e) {
            LOG.error("InvoiceApplyController.queryInvoiceDetailListForPage#error invoiceApplyNo:{},pageNum:{},pageSize:{}", invoiceApplyNo, pageNum, pageSize, e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 导出发票申请列表
     *
     * @param popInvoiceApplyAdminParam
     * @param request
     * @param response
     * @param map
     */
    @GetMapping(value = "/exportInvoiceList")
    public Response exportInvoiceList(PopInvoiceApplyAdminParam popInvoiceApplyAdminParam,
                                      HttpServletRequest request,
                                      HttpServletResponse response,
                                      ModelMap map) {
        LOG.info("InvoiceApplyController.exportInvoiceList#导出发票申请单列表 popInvoiceApplyAdminParam:{}", JSON.toJSONString(popInvoiceApplyAdminParam));
        try {
            int exportCount = invoiceApplyService.queryExportCount(popInvoiceApplyAdminParam);
            if (exportCount > exportMaxCount) {
                String msg = String.format("导出上限为%s条，当前搜索结果导出数据为%s条，超出导出上限", exportMaxCount, exportCount);
                return ResponseUtils.returnCommonException(msg);
            }
            popInvoiceApplyAdminParam.setPageNum(1);
            popInvoiceApplyAdminParam.setPageSize(exportMaxCount);
            List<PopInvoiceApplyForExportDto> rows = invoiceApplyService.exportInvoiceList(popInvoiceApplyAdminParam);
            exportService.export(map, request, response, rows, PopInvoiceApplyForExportDto.class, EXCEL_NAME, EXCEL_NAME);
            return ResponseUtils.returnSuccess();
        } catch (Exception e) {
            LOG.error("InvoiceApplyController.exportInvoiceList#error popInvoiceApplyAdminParam:{}", JSON.toJSONString(popInvoiceApplyAdminParam), e);
            return ResponseUtils.returnException(e);
        }
    }

    @GetMapping(value = "/async/exportInvoiceList")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportInvoiceList(PopInvoiceApplyAdminParam popInvoiceApplyAdminParam){
        try {
            List<Long> provIds = getProvIds(popInvoiceApplyAdminParam.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            popInvoiceApplyAdminParam.setProvIds(provIds);
            LOG.info("InvoiceApplyController.exportInvoiceList#popInvoiceApplyAdminParam:{}", JSON.toJSONString(popInvoiceApplyAdminParam));
            int exportCount = invoiceApplyService.queryExportCount(popInvoiceApplyAdminParam);
            if (exportCount > exportMaxCount) {
                String msg = String.format("导出上限为%s条，当前搜索结果导出数据为%s条，超出导出上限", exportMaxCount, exportCount);
                return ResponseVo.errRest(msg);
            }
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(popInvoiceApplyAdminParam)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.INVOICE_APPLY)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            LOG.info("InvoiceApplyController.exportInvoiceList#popInvoiceApplyAdminParam:{} return {}", JSON.toJSONString(popInvoiceApplyAdminParam), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            LOG.error("InvoiceApplyController.exportInvoiceList#popInvoiceApplyAdminParam:{} 异常", JSON.toJSONString(popInvoiceApplyAdminParam), e);
            return ResponseVo.errRest("导出失败，请重试");
        }


    }

    /**
     * 查询导出数量
     *
     * @param popInvoiceApplyAdminParam
     */
    @GetMapping(value = "/queryExportCount")
    public Response queryExportCount(PopInvoiceApplyAdminParam popInvoiceApplyAdminParam) {
        LOG.info("InvoiceApplyController.queryExportCount#查询导出数量 popInvoiceApplyAdminParam:{}", JSON.toJSONString(popInvoiceApplyAdminParam));
        try {
            int exportCount = invoiceApplyService.queryExportCount(popInvoiceApplyAdminParam);
            if (exportCount > exportMaxCount) {
                String msg = String.format("导出上限为5000条，当前搜索结果导出数据为%s条，超出导出上限", exportCount);
                return ResponseUtils.returnCommonException(msg);
            } else {
                return ResponseUtils.returnObjectSuccess(exportCount);
            }
        } catch (Exception e) {
            LOG.error("InvoiceApplyController.queryExportCount#error popInvoiceApplyAdminParam:{}", JSON.toJSONString(popInvoiceApplyAdminParam), e);
            return ResponseUtils.returnException(e);
        }
    }

    @PostMapping(value = "/importExcel")
    @ResponseBody
    public ResponseVo importExcel(@RequestParam("file") MultipartFile file){

        String user = getUser().getId() + getUser().getUsername();
        try {
            if (StringUtils.isBlank(user)) {
                return ResponseVo.errRest("未知用户，请重新登录后重试");
            }
            ImportParams importParams = new ImportParams();
            importParams.setVerifyHandler(new InvoiceApplyExcelVerifyHandle());
            List<InvoiceApplyExportVo> vos = ExceImportWarpUtil.importExcel(file, InvoiceApplyExportVo.class, importParams, 3, 1000, Lists.newArrayList("*发票申请单号","*物流公司","*运单号"));
            InvoiceApplyExcelVerifyHandle.trim(vos);
            List<String> invoiceApplyNos = vos.stream().map(InvoiceApplyExportVo::getInvoiceApplyNo).distinct().collect(Collectors.toList());
            List<String> effectiveInvoiceApplyNos = invoiceApplyService.selectEffectiveInvoiceApplyNo(invoiceApplyNos);
            InvoiceApplyExcelVerifyHandle.valid(vos, effectiveInvoiceApplyNos);

            AdjustiveBillSettleResultVo resultVo = invoiceApplyService.batchImport(vos);
            if (resultVo != null && resultVo.getErrorCount() > 0) {
                resultVo.setErrorFileName(FileNameUtils.getErrorFileName(file));
                String downLoadUrl = FileNameUtils.getErrorFileDownUrl(resultVo.getErrorFileUrl(), resultVo.getErrorFileName());
                resultVo.setErrorFileUrl(downLoadUrl);
            }
            return ResponseVo.successResult(resultVo);
        } catch (ServiceException e) {
            LOG.error("InvoiceApplyController.importExcel,user:{}, 失败", user, e);
            return ResponseVo.errRest(StringUtils.isEmpty(e.getMessage()) ? "系统异常" : e.getMessage());
        } catch (Exception e) {
            LOG.error("InvoiceApplyController.importExcel,user:{}, 出现异常", user, e);
            return ResponseVo.errRest("发票导入异常");
        }
    }

}
