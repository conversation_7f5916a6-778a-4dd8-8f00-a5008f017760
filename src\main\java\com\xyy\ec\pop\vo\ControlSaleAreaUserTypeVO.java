package com.xyy.ec.pop.vo;

import com.xyy.address.dto.XyyRegionBusinessDto;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 */
@Data
public class ControlSaleAreaUserTypeVO {
    private Long id;

    private Long controlId;

    private Long csuId;

    private String branchCode;

    private String shopCode;

    private List<XyyRegionBusinessDto> areaCodes;

    private List<UserTypesVO> userTypes;

    private Date createTime;

    private Date updateTime;

    private String operator;

    private Integer rosterType;

}