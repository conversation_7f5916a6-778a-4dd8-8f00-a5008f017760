package com.xyy.ec.pop.report.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantReportDetailsDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantReportRecordsDto;
import com.xyy.ec.merchant.bussiness.dto.ReportOperationLogDto;
import com.xyy.ec.merchant.bussiness.dto.report.MerchantReportAuditDto;
import com.xyy.ec.merchant.bussiness.dto.report.MerchantReportDetailQueryDto;
import com.xyy.ec.merchant.bussiness.dto.report.MerchantReportRecordQueryDto;
import com.xyy.ec.merchant.bussiness.dto.report.ReportOperationLogQueryDto;
import com.xyy.ec.merchant.bussiness.enums.AuditStatusEnum;
import com.xyy.ec.pop.enums.BusinessTypeEnum;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.redis.impl.RedisService;
import com.xyy.ec.pop.remote.*;
import com.xyy.ec.pop.report.Vo.*;
import com.xyy.ec.pop.report.param.MerchantReportBatchAuditImportParam;
import com.xyy.ec.pop.report.service.MerchantReportService;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.merchant.api.enums.MerchantFundAuditStatusEnum;
import com.xyy.ec.pop.server.api.merchant.dto.OrgUserRelationDto;
import com.xyy.ec.pop.server.api.merchant.param.ReportRecordQueryParam;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.service.FastDfsUtilService;
import com.xyy.ec.pop.service.settle.PopBillSettleService;
import com.xyy.ec.pop.utils.FileNameUtils;
import com.xyy.ec.pop.vip.service.VipActiveUsersService;
import com.xyy.ec.pop.vo.BatchUpdateResultVo;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MerchantReportServiceImpl implements MerchantReportService {
    @Resource
    private RedisService redisService;
    @Autowired
    private DownloadRemote downloadRemote;
    @Resource
    private BaseRegionRemote baseRegionRemote;
    @Reference
    private MerchantRpcService merchantRpcService;
    @Resource
    private FastDfsUtilService fastDfsUtilService;
    @Autowired
    private PopBillSettleService popBillSettleService;
    @Resource
    private OrgUserRelationRemote orgUserRelationRemote;
    @Resource
    private MerchantReportRemote merchantReportRemote;
    @Resource
    private VipActiveUsersService vipActiveUsersService;
    @Resource
    private ProductSkuRemoteAdapter productSkuRemoteAdapter;
    @Resource
    private ProductCsuForOtherSystemRemoteService productCsuForOtherSystemRemoteService;

    @Override
    public ResponseVo<PageInfo<MerchantRecordsVo>> pageReportRecords(ReportRecordQueryVo reportRecordQueryVo) {
        MerchantReportRecordQueryDto queryDto = processReportRecordQuery(reportRecordQueryVo);

        //分页查询
        PageInfo<MerchantReportRecordsDto> pageInfo = merchantReportRemote.pageMerchantReportRecord(queryDto);

        //类型转换
        List<MerchantRecordsVo> merchantRecordsVoList = convertToMerchantRecordsVoList(pageInfo.getList());

        PageInfo<MerchantRecordsVo> returnPage = new PageInfo<>(merchantRecordsVoList);
        returnPage.setPageNum(reportRecordQueryVo.getPage());
        returnPage.setPageSize(reportRecordQueryVo.getLimit());
        returnPage.setTotal(pageInfo.getTotal());

        return ResponseVo.successResult(returnPage);
    }

    public MerchantReportRecordQueryDto processReportRecordQuery(ReportRecordQueryVo reportRecordQueryVo) {
        MerchantReportRecordQueryDto queryDto = new MerchantReportRecordQueryDto();
        BeanUtils.copyProperties(reportRecordQueryVo, queryDto);
        if (NumberUtil.isNumber(reportRecordQueryVo.getMerchantName())) {
            queryDto.setMerchantIds(Collections.singletonList(Long.parseLong(reportRecordQueryVo.getMerchantName())));
            queryDto.setMerchantName(null);
        } else {
            queryDto.setMerchantName(reportRecordQueryVo.getMerchantName());
        }

        // 调用独立的方法进行字符串转LocalDateTime
        queryDto.setStartFirstReportTime(parseLocalDateTime(reportRecordQueryVo.getStartFirstReportTime()));
        queryDto.setEndFirstReportTime(parseLocalDateTime(reportRecordQueryVo.getEndFirstReportTime()));
        queryDto.setEndLastReportTime(parseLocalDateTime(reportRecordQueryVo.getEndLastReportTime()));
        queryDto.setStartLastReportTime(parseLocalDateTime(reportRecordQueryVo.getStartLastReportTime()));

        queryDto.setPageNum(reportRecordQueryVo.getPage());
        queryDto.setPageSize(reportRecordQueryVo.getLimit());

        if (StringUtils.isNotEmpty(reportRecordQueryVo.getMerchantTypes())) {
            queryDto.setCustomerTypes(Arrays.stream(reportRecordQueryVo.getMerchantTypes().split(","))
                    .map(Integer::parseInt).collect(Collectors.toList()));
        }
        return queryDto;
    }

    public ReportRecordQueryParam processReportRecordParam(ReportRecordQueryVo reportRecordQueryVo) {
        ReportRecordQueryParam queryDto = new ReportRecordQueryParam();
        BeanUtils.copyProperties(reportRecordQueryVo, queryDto);
        if (NumberUtil.isNumber(reportRecordQueryVo.getMerchantName())) {
            queryDto.setMerchantIds(Collections.singletonList(Long.parseLong(reportRecordQueryVo.getMerchantName())));
            queryDto.setMerchantName(null);
        } else {
            queryDto.setMerchantName(reportRecordQueryVo.getMerchantName());
        }

        // 调用独立的方法进行字符串转LocalDateTime
        queryDto.setStartFirstReportTime(parseLocalDateTime(reportRecordQueryVo.getStartFirstReportTime()));
        queryDto.setEndFirstReportTime(parseLocalDateTime(reportRecordQueryVo.getEndFirstReportTime()));
        queryDto.setEndLastReportTime(parseLocalDateTime(reportRecordQueryVo.getEndLastReportTime()));
        queryDto.setStartLastReportTime(parseLocalDateTime(reportRecordQueryVo.getStartLastReportTime()));

        if (StringUtils.isNotEmpty(reportRecordQueryVo.getMerchantTypes())) {
            queryDto.setCustomerTypes(Arrays.stream(reportRecordQueryVo.getMerchantTypes().split(","))
                    .map(Integer::parseInt).collect(Collectors.toList()));
        }
        return queryDto;
    }

    private LocalDateTime parseLocalDateTime(String dateTimeString) {
        if (StringUtils.isEmpty(dateTimeString)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(dateTimeString, formatter);
    }

    @Override
    public ResponseVo<PageInfo<MerchantDetailsVo>> pageReportDetails(ReportDetailQueryVo reportDetailQueryVo) {

        MerchantReportDetailQueryDto queryDto = new MerchantReportDetailQueryDto();
        BeanUtils.copyProperties(reportDetailQueryVo, queryDto);
        queryDto.setPageNum(reportDetailQueryVo.getPage());
        queryDto.setPageSize(reportDetailQueryVo.getLimit());

        PageInfo<MerchantReportDetailsDto> pageInfo = merchantReportRemote.pageMerchantReportDetail(queryDto);
        List<MerchantDetailsVo> merchantDetailsVoList = convertToMerchantDetailsVoList(pageInfo.getList());

        if (CollectionUtils.isNotEmpty(merchantDetailsVoList)) {
            enrichMerchantDetailsVoList(merchantDetailsVoList);
        }

        PageInfo<MerchantDetailsVo> returnPage = new PageInfo<>(merchantDetailsVoList);
        returnPage.setPageNum(reportDetailQueryVo.getPage());
        returnPage.setPageSize(reportDetailQueryVo.getLimit());
        returnPage.setTotal(pageInfo.getTotal());

        return ResponseVo.successResult(returnPage);
    }

    @Override
    public ResponseVo<String> audit(AuditVo auditVo, String operator) {
        MerchantReportAuditDto auditDto = new MerchantReportAuditDto();
        BeanUtils.copyProperties(auditVo, auditDto);
        auditDto.setOperator(operator);
        boolean auditFlag = merchantReportRemote.audit(auditDto);
        if (auditFlag) {
            return ResponseVo.successResult("审核成功");
        }
        return ResponseVo.errCodeRest("审核失败");
    }

    @Override
    public ResponseVo<BatchUpdateResultVo> batchAudit(List<MerchantReportBatchAuditImportParam> params, String operator, MultipartFile file) {
        // 查询审核记录是否存在
        MerchantReportRecordQueryDto merchantReportRecordQueryDto = new MerchantReportRecordQueryDto();
        List<Long> merchantIds = params.stream()
                .map(MerchantReportBatchAuditImportParam::getMerchantIdStr)
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        merchantReportRecordQueryDto.setMerchantIds(merchantIds);
        Map<Long, MerchantReportRecordsDto> reportRecordsDtoMap = merchantReportRemote.queryReportRecordMap(merchantReportRecordQueryDto);

        List<MerchantReportAuditDto> auditDtos = new ArrayList<>();
        for (MerchantReportBatchAuditImportParam param : params) {
            if (!validateParam(param)) {
                continue;
            }

            MerchantReportRecordsDto merchantReportRecordsDto = reportRecordsDtoMap.get(Long.valueOf(param.getMerchantIdStr()));
            if (merchantReportRecordsDto == null) {
                setErrorMessage(param, "药店无待审核举报记录");
                continue;
            }
            if (!AuditStatusEnum.PENDING.getCode().equals(merchantReportRecordsDto.getAuditStatus())) {
                setErrorMessage(param, "药店举报记录不是待审核，不允许操作审核");
                continue;
            }

            MerchantReportAuditDto auditDto = createAuditDto(param, operator);
            auditDtos.add(auditDto);
        }

        List<Long> errorAuditMerchantIds = audit(auditDtos);

        // 返回失败的条数
        List<MerchantReportBatchAuditErrorVo> errorVos = params.stream()
                .filter(a -> errorAuditMerchantIds.contains(a.getMerchantId()) || a.failed)
                .map(a -> {
                    MerchantReportBatchAuditErrorVo errorVo = new MerchantReportBatchAuditErrorVo();
                    BeanUtils.copyProperties(a, errorVo);
                    if (errorAuditMerchantIds.contains(a.getMerchantId())) {
                        errorVo.setErrorMsg("持久化审核失败，请重试");
                    }
                    return errorVo;
                })
                .collect(Collectors.toList());

        BatchUpdateResultVo batchUpdateResultVo = batchUpdateResultVoBase(params.size(), errorVos.size(), errorVos, "举报记录批量审核_错误文件");

        setFileName(batchUpdateResultVo, file);
        return ResponseVo.successResult(batchUpdateResultVo);
    }

    private BatchUpdateResultVo setFileName(BatchUpdateResultVo resultVo, MultipartFile file) {
        resultVo.setErrorFileName(FileNameUtils.getErrorFileName(file));
        String downLoadUrl = FileNameUtils.getErrorFileDownUrl(resultVo.getErrorFileUrl(), resultVo.getErrorFileName());
        resultVo.setErrorFileUrl(downLoadUrl);
        return resultVo;
    }

    private List<Long> audit(List<MerchantReportAuditDto> auditDtos) {
        List<List<MerchantReportAuditDto>> partition = Lists.partition(auditDtos, 100);
        List<Long> errorMerchantIds = new ArrayList<>();
        partition.forEach(batchAuditDtos -> {
            boolean saveFlag = merchantReportRemote.batchAudit(auditDtos);
            if (!saveFlag) {
                List<Long> merchantIdList = batchAuditDtos.stream().map(MerchantReportAuditDto::getMerchantId).collect(Collectors.toList());
                errorMerchantIds.addAll(merchantIdList);
            }
        });
        return errorMerchantIds;
    }

    private boolean validateParam(MerchantReportBatchAuditImportParam param) {
        if (StringUtils.isEmpty(param.getMerchantIdStr())) {
            setErrorMessage(param, "药店编码不存在");
            return false;
        }
        if (StringUtils.isEmpty(param.getAuditResult())) {
            setErrorMessage(param, "审核结果不存在");
            return false;
        }
        if (StringUtils.isNotEmpty(param.getResultReason()) && param.getResultReason().length() > 50) {
            setErrorMessage(param, "最多可输入50个字");
            return false;
        }
        return true;
    }

    private MerchantReportAuditDto createAuditDto(MerchantReportBatchAuditImportParam param, String operator) {
        MerchantReportAuditDto auditDto = new MerchantReportAuditDto();
        auditDto.setMerchantId(param.getMerchantId());
        auditDto.setAuditStatus(param.getAuditStatus());
        auditDto.setAuditDesc(param.getResultReason());
        auditDto.setOperator(operator);
        return auditDto;
    }

    private BatchUpdateResultVo batchUpdateResultVoBase(int totalSize, int errors, List<MerchantReportBatchAuditErrorVo> errorVos, String sheetName) {
        BatchUpdateResultVo resultVo = new BatchUpdateResultVo();
        resultVo.setError(errors);
        resultVo.setSuccess(totalSize - resultVo.getError());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(errorVos)) {
            //将数据写入excel文件
            String fileUrl = fastDfsUtilService.writeDateToFastDfs(new ExportParams(null, sheetName, ExcelType.XSSF), errorVos.get(0).getClass(), errorVos);
            resultVo.setErrorFileUrl(fileUrl);
        }
        return resultVo;
    }

    private void setErrorMessage(MerchantReportBatchAuditImportParam vo, String errorMsg) {
        vo.setErrorMsg(errorMsg);
        vo.setFailed(true);
    }

    private List<MerchantDetailsVo> convertToMerchantDetailsVoList(List<MerchantReportDetailsDto> reportDetailsDtos) {
        if (CollectionUtils.isEmpty(reportDetailsDtos)) {
            return Collections.emptyList();
        }
        return reportDetailsDtos.stream()
                .map(dto -> {
                    MerchantDetailsVo vo = new MerchantDetailsVo();
                    BeanUtils.copyProperties(dto, vo);
                    if (StringUtils.isNotEmpty(dto.getOrgId())) {
                        vo.setOrgId(dto.getOrgId());
                    }
                    vo.setAuditTime(dto.getAuditTime());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    private void enrichMerchantDetailsVoList(List<MerchantDetailsVo> merchantDetailsVoList) {
        List<Long> csuids = merchantDetailsVoList.stream()
                .map(MerchantDetailsVo::getCsuid)
                .collect(Collectors.toList());

        List<CsuDTO> csuDTOList = productCsuForOtherSystemRemoteService.listCsuInfosByCsuIds(csuids);
        //转map
        Map<Long, CsuDTO> csuIdToInfoMap = csuDTOList.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getId()))
                .collect(Collectors.toMap(CsuDTO::getId, Function.identity(), (s, f) -> s));

        merchantDetailsVoList.forEach(detailVo -> {
            CsuDTO csuDTO = csuIdToInfoMap.get(detailVo.getCsuid());
            if (Objects.isNull(csuDTO)) {
                log.info("商品不存在，csuid={}", detailVo.getCsuid());
                return;
            }
            detailVo.setProductName(csuDTO.getProductName());
            detailVo.setManufacturer(csuDTO.getManufacturer());
            detailVo.setSpec(csuDTO.getSpec());
            detailVo.setApprovalNumber(csuDTO.getApprovalNumber());
            detailVo.setAuditDesc(detailVo.getAuditDesc());
            detailVo.setOrgName(detailVo.getCompanyName());
        });
    }

    private List<MerchantRecordsVo> convertToMerchantRecordsVoList(List<MerchantReportRecordsDto> reportRecordsDtoList) {
        if (CollectionUtils.isEmpty(reportRecordsDtoList)) {
            return Collections.emptyList();
        }
        return reportRecordsDtoList.stream()
                .map(dto -> {
                    MerchantRecordsVo vo = new MerchantRecordsVo();
                    BeanUtils.copyProperties(dto, vo);
                    return vo;
                })
                .collect(Collectors.toList());
    }

    private void enrichMerchantRecordsVoList(List<MerchantRecordsVo> merchantRecordsVoList) {
        // 查询店铺信息
        List<Long> merchantIdList = merchantRecordsVoList.stream()
                .map(MerchantRecordsVo::getMerchantId)
                .map(Long::valueOf)
                .collect(Collectors.toList());

        List<MerchantBussinessDto> merchantBussinessDtos = merchantRpcService.selectMerchantByIdList(merchantIdList);
        Map<Long, MerchantBussinessDto> merchantMap = merchantBussinessDtos.stream()
                .collect(Collectors.toMap(MerchantBussinessDto::getId, Function.identity(), (existing, replacement) -> existing));

        // 更新店铺地址
        merchantRecordsVoList.forEach(recordVo -> {
            MerchantBussinessDto merchant = merchantMap.get(Long.valueOf(recordVo.getMerchantId()));
            if (merchant != null) {
                recordVo.setAddress(merchant.getAddress());
            }
        });

        // 查询省份信息
        List<Integer> provinceCodeList = merchantRecordsVoList.stream()
                .map(MerchantRecordsVo::getProvinceCode)
                .collect(Collectors.toList());

        Map<Integer, XyyRegionBusinessDto> regionMap = baseRegionRemote.getRegionMap(provinceCodeList);

        // 更新省份名称
        merchantRecordsVoList.forEach(recordVo -> {
            XyyRegionBusinessDto region = regionMap.get(recordVo.getProvinceCode());
            if (region != null) {
                recordVo.setProvinceName(region.getAreaName());
            }
        });

        // 设置客户类型字符串
        merchantRecordsVoList.forEach(recordVo -> {
            recordVo.setCustomerTypeStr(BusinessTypeEnum.get(recordVo.getCustomerType()));
        });
    }

    public void exportReportRecord(ReportRecordQueryVo reportRecordQueryVo, SysUser user) {
        String redisCacheKey = "m_p_e_r_" + user.getId();
        boolean isHaveLock;

        // 最多锁60秒
        isHaveLock = redisService.setNx(redisCacheKey, StringUtils.EMPTY, 60);
        if (BooleanUtils.isNotTrue(isHaveLock)) {
            log.info("举报记录导出，分布式锁，标记={},isHaveLock={}", redisCacheKey, isHaveLock);
            throw new PopAdminException(XyyJsonResultCodeEnum.EXPORT_FREQUENCY_LIMIT, "您目前有正在导出的任务，请等待其完成后再进行导出！");
        }
        try {
            ReportRecordQueryParam exportQueryParam = processReportRecordParam(reportRecordQueryVo);

            DownloadFileContent content = DownloadFileContent.builder()
                    .query(exportQueryParam)
                    .operator(user.getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.ADMIN_MERCHANT_REPORT_LIST)
                    .build();


            Boolean asyncResult = downloadRemote.saveTask(content);
            if (BooleanUtils.isNotTrue(asyncResult)) {
                log.error("导出失败，DownloadRemote.saveTask，content：{}", JSONObject.toJSONString(content));
                throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "导出失败，请稍后重试！");
            }
        } finally {
            if (StringUtils.isNotEmpty(redisCacheKey) && BooleanUtils.isTrue(isHaveLock)) {
                // 释放锁
                try {
                    redisService.deleteKey(redisCacheKey);
                } catch (Exception e) {
                    log.error("释放导出频次锁失败，redisCacheKey：{}", redisCacheKey, e);
                }
            }
        }
    }

    @Override
    public ResponseVo<PageInfo<ReportLogVo>> queryReportLogs(Long merchantId, int pageNum, int pageSize) {
        PageInfo<ReportLogVo> pageInfo = new PageInfo<>();
        pageInfo.setTotal(0);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);

        ReportOperationLogQueryDto queryDto = new ReportOperationLogQueryDto();
        queryDto.setMerchantId(merchantId);
        PageInfo<ReportOperationLogDto> reportLogVos = merchantReportRemote.pageLogs(queryDto);
        if (reportLogVos == null) {
            return ResponseVo.successResult(pageInfo);
        }
        List<ReportLogVo> list = reportLogVos.getList().stream().map(dto -> {
            ReportLogVo vo = new ReportLogVo();
            vo.setAuditDesc(dto.getOperationContent());
            vo.setAuditStatus(dto.getAuditStatus());
            vo.setAuditTime(dto.getCtime());
            vo.setOperator(dto.getOperator());
            if (dto.getAuditStatus() != null) {
                vo.setAuditStatusStr(AuditStatusEnum.getDescByCode(dto.getAuditStatus()));
            }
            return vo;
        }).collect(Collectors.toList());
        pageInfo.setList(list);
        pageInfo.setTotal(reportLogVos.getTotal());
        return ResponseVo.successResult(pageInfo);
    }
}
