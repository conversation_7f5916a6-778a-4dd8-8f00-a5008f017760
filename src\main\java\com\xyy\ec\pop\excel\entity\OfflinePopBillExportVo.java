package com.xyy.ec.pop.excel.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**线下转账导出账单信息
* <AUTHOR>
* @date  2020/12/11 17:30
* @table
*/
@NoArgsConstructor
@Data
public class OfflinePopBillExportVo implements Serializable {

    @Excel(name = "商户编号",width = 15)
    private String orgId;

    @Excel(name = "商户名称",width = 15)
    private String orgName;

    @Excel(name = "店铺名称",width = 15)
    private String name;
    @Excel(name = "账单号",width = 15)
    private String billNo;
    /**
     * 佣金结算方式，1：非月结；2：月结
     */
    @Excel(name = "佣金结算方式",width = 15,replace={"非月结_1","月结_2","_null"})
    private Byte settlementType;

    @Excel(name = "商品金额",width = 15)
    private BigDecimal productMoney;

    @Excel(name = "运费金额",width = 15)
    private BigDecimal freightAmount;

    @Excel(name = "单据总金额(含运费)",width = 15)
    private BigDecimal totalMoney;

    @Excel(name = "店铺总优惠",width = 15)
    private BigDecimal shopTotalDiscount;

    @Excel(name = "平台总优惠",width = 15)
    private BigDecimal platformTotalDiscount;

    @Excel(name = "实付金额",width = 15)
    private BigDecimal money;

    @Excel(name = "佣金金额",width = 15)
    private BigDecimal hireMoney;

    @Excel(name = "应结算金额",width = 15)
    private BigDecimal statementTotalMoney;
    /**
     * 应收佣金
     */
    @Excel(name = "应收佣金",width = 15)
    private BigDecimal payableCommission;

    @Excel(name = "账单状态",width = 15,replace={"未入账_0","已入账_1","_null"})
    private Byte billPaymentStatus;

    @Excel(name = "生成时间",width = 18,format = "yyyy-MM-dd HH:mm:ss")
    private Date billCreateTime;

    @Excel(name = "入账时间",width = 18,format = "yyyy-MM-dd HH:mm:ss")
    private Date billPaymentTime;

    @Excel(name = "打款状态",width = 15,replace={"未打款_0","已打款_1","_null"})
    private Byte remitStatus;

    @Excel(name = "打款时间",width = 18,format = "yyyy-MM-dd HH:mm:ss")
    private Date remitTime;

    @Excel(name = "开票状态",width = 15,replace={"未开_0","已开_1","无需开票_2","_null"})
    private Byte invoiceStatus;

    @Excel(name = "开票日期",width = 18,format = "yyyy-MM-dd")
    private Date invoiceTime;
}
