package com.xyy.ec.pop.constants;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.pop.server.api.merchant.dto.BusinessScopeDto;
import com.xyy.ec.pop.vo.ZTree;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 2020/05/06
 */
public class ZTreeByCategoryRelation {
    public static List<ZTree> getZTreeByCategoryRelation(List<BusinessScopeDto> businessScopeDtos, List<Long> categoryIdList) {
        if (CollectionUtils.isEmpty(businessScopeDtos)) {
            return new ArrayList<>(0);
        }
        List<ZTree> tree = Lists.newArrayListWithCapacity(businessScopeDtos.size());
        businessScopeDtos.forEach(businessScopeDto -> {
            ZTree node = new ZTree();
            //树节点id
            node.setId(businessScopeDto.getId());
            //节点父类id
            Integer parentId = businessScopeDto.getParentId();
            node.setpId(parentId.longValue());
            //节点名
            node.setName(businessScopeDto.getDictName());
            node.setLevel(businessScopeDto.getLevelNode());
            //是否是父类
            node.setIsParent(CollectionUtils.isNotEmpty(businessScopeDto.getChildDictionaryList()));

            Map<String, Object> attributes = Maps.newHashMapWithExpectedSize(3);
            //分类编码
            attributes.put("code", businessScopeDto.getLevelNode());
            //分类排序
            //attributes.put("sortno", erpCategory.getPriority());
            //分类父id
            attributes.put("parentId", businessScopeDto.getParentId());
            //node.setChecked(false);
            node.setAttributes(attributes);
            if (!org.springframework.util.CollectionUtils.isEmpty(categoryIdList)) {
                //商户资质发布分类id存在则选中
                boolean isChoose = categoryIdList.stream().anyMatch(categoryId -> categoryId.equals(node.getId()));
                if (isChoose) {
                    node.setChecked(true);
                }
            }
            //处理子节点
            if (CollectionUtils.isNotEmpty(businessScopeDto.getChildDictionaryList())) {
                List<ZTree> children = getZTreeByCategoryRelation(businessScopeDto.getChildDictionaryList(), categoryIdList);
                //如果子节点有选中，就设置父节点选中
                boolean childrenIsChecked = children.stream().anyMatch(ZTree::isChecked);
                if (childrenIsChecked) {
                    node.setChecked(true);
                }
                //树形是否展开
                //Boolean childDisplay = businessScopeDto.getChildDisplay();
                //node.setOpen(childDisplay == null ? false : true);
                node.setChildren(children);
            }
            tree.add(node);
        });
        return tree;
    }
}
