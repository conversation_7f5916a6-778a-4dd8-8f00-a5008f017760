package com.xyy.ec.pop.helper;

import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ms.promotion.business.params.MarketingActivitySaleDataSearchInfoQueryParam;

import java.util.Objects;

public class MarketingActivitySaleDataSearchInfoQueryParamHelper {

    /**
     * 校验
     *
     * @param queryParam
     * @return
     */
    public static Boolean validate(MarketingActivitySaleDataSearchInfoQueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (Objects.isNull(queryParam.getActivityType()) || Objects.isNull(MarketingEnum.getEnum(queryParam.getActivityType()))) {
            String msg = "请填写活动类型";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        return true;
    }
    
}
