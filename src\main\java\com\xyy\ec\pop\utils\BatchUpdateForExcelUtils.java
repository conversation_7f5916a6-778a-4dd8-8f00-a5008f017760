package com.xyy.ec.pop.utils;

import com.xyy.ec.pop.vo.BatchUpdateResultVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Description 批量更新工具
 */
public class BatchUpdateForExcelUtils {
    /**
     * 修改错误文件名为固定名称（原文件名—错误.xlsx）
     * @param resultVo
     * @param originalFile
     */
    public static void setErrorName(BatchUpdateResultVo resultVo, MultipartFile originalFile) {
        if(resultVo.getErrorFileUrl()!=null){
            resultVo.setErrorFileName(FileNameUtils.getErrorFileName(originalFile));
            String downLoadUrl = FileNameUtils.getErrorFileDownUrl(resultVo.getErrorFileUrl(),resultVo.getErrorFileName());
            resultVo.setErrorFileUrl(downLoadUrl);
        }
    }
}
