package com.xyy.ec.pop.dao;

import com.xyy.ec.pop.po.PopBillPo;
import com.xyy.ec.pop.vo.settle.PopBillStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface PopBillMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PopBillPo record);

    int insertSelective(PopBillPo record);

    PopBillPo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PopBillPo record);

    int updateByPrimaryKey(PopBillPo record);

    List<PopBillPo> queryPopBillList(@Param("popBill") PopBillVo popBillVo, @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    Long queryPopBillListCount(@Param("popBill") PopBillVo popBillVo);

    PopBillStatisVo queryPopBillStatis(@Param("popBill") PopBillVo popBillVo);

    PopBillPo selectByBillNo(@Param("billNo") String billNo);

    List<String> queryByOrderNos (@Param("popBill") PopBillPo popBillPo);

    List<PopBillPo> queryPopBillByBillNoList(@Param("list") List<String> billNoList);

    void batchUpdateById(@Param("list") List<PopBillPo> popBillPos);
}