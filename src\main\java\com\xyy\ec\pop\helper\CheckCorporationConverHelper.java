package com.xyy.ec.pop.helper;

import com.google.common.collect.Lists;
import com.xyy.ec.pop.server.api.merchant.api.enums.CheckCorporationRecordTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.*;
import com.xyy.ec.pop.vo.CheckCorporationExportVO;
import com.xyy.ec.pop.vo.CheckCorporationVo;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 2020/12/01
 */
public class CheckCorporationConverHelper {

    public static List<CheckCorporationVo> convertToCheckVo(List<CheckCorporationDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>(0);
        }
        return list.stream().map(item -> convertToCheckVo(item)).collect(Collectors.toList());
    }

    public static CheckCorporationVo convertToCheckVo(CheckCorporationDto dto) {
        CheckCorporationVo vo = new CheckCorporationVo();
        vo.setId(dto.getId());
        vo.setCId(dto.getCId());
        vo.setRecordType(dto.getRecordType());
        vo.setBatch(dto.getBatch());
        vo.setOrgId(dto.getOrgId());
        vo.setName(dto.getName());
        vo.setRegCode(dto.getRegCode());
        vo.setCorporat(dto.getCorporat());
        vo.setPhone(dto.getPhone());
        vo.setFixedPhone(dto.getFixedPhone());
        vo.setEmail(dto.getEmail());
        vo.setWeb(dto.getWeb());
        vo.setProvId(dto.getProvId());
        vo.setProv(dto.getProv());
        vo.setCityId(dto.getCityId());
        vo.setCity(dto.getCity());
        vo.setCorporationType(dto.getCorporationType());
        vo.setCustomerServicePhone(dto.getCustomerServicePhone());
        vo.setErpProcessId(dto.getErpProcessId());
        vo.setAreaId(dto.getAreaId());
        vo.setArea(dto.getArea());
        vo.setAddr(dto.getAddr());
        vo.setLogoUrl(dto.getLogoUrl());
        vo.setBrief(dto.getBrief());
        vo.setRemarks(dto.getRemarks());
        vo.setState(dto.getState());
        vo.setDel(dto.getDel());
        vo.setCreateTime(dto.getCreateTime());
        vo.setCreateId(dto.getCreateId());
        vo.setCreateName(dto.getCreateName());
        vo.setUpdateTime(dto.getUpdateTime());
        vo.setUpdateId(dto.getUpdateId());
        vo.setUpdateName(dto.getUpdateName());
        vo.setErpNumber(dto.getErpNumber());
        vo.setStreetId(dto.getStreetId());
        vo.setStreetName(dto.getStreetName());
        vo.setSearch(dto.getSearch());
        vo.setStatus(dto.getStatus());
        vo.setAction(dto.getRecordType()==null?"":
                CheckCorporationRecordTypeEnum.AUTHENTICATION_RECORD.getCode()==dto.getRecordType()
                        ||CheckCorporationRecordTypeEnum.AUDIT_RECORD.getCode()==dto.getRecordType()?"首营资质认证":CheckCorporationRecordTypeEnum.SYSTEM_RECORD.getCode()==dto.getRecordType()?"系统自动发起，需进行人工资质核验":"修改");
        vo.setRejectReason(dto.getRejectReason());
        vo.setAuditTime(dto.getAuditTime());
        return vo;
    }

    public static List<CheckCorporationExportVO> convertCheckCorporationAndUserExtDtoToDTO(List<CheckCorporationAndUserExtDto> userExtDtos) {
        List<CheckCorporationExportVO> arrayList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(userExtDtos)) {
            return arrayList;
        }
        for (CheckCorporationAndUserExtDto userExtDto : userExtDtos) {
            CheckCorporationExportVO exportVO = convertCheckCorporationAndUserExtDtoToDTO(userExtDto);
            arrayList.add(exportVO);
        }
        return arrayList;
    }

    public static CheckCorporationExportVO convertCheckCorporationAndUserExtDtoToDTO(CheckCorporationAndUserExtDto userExtDto) {
        CheckCorporationExportVO checkCorporationExportVO = new CheckCorporationExportVO();
        checkCorporationExportVO.setBatch(userExtDto.getBatch());
        checkCorporationExportVO.setOrgId(userExtDto.getOrgId());
        checkCorporationExportVO.setName(userExtDto.getName());
        checkCorporationExportVO.setCompanyName(userExtDto.getCompanyName());
        checkCorporationExportVO.setSuUserName(userExtDto.getUserName());
        checkCorporationExportVO.setCorporat(userExtDto.getCorporat());
        checkCorporationExportVO.setPhone(userExtDto.getPhone());
        checkCorporationExportVO.setSuCreateTime(userExtDto.getCreateTime());
        checkCorporationExportVO.setState(userExtDto.getState());
        return checkCorporationExportVO;
    }
}
