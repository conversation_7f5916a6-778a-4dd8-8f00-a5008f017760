package com.xyy.ec.pop.controller;


import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.fdd.dto.TbXyyPopFddEnterpriseAgreementDTO;
import com.xyy.ec.pop.server.api.fdd.param.FddSignaturesParam;
import com.xyy.ec.pop.service.PlatformServiceAgreementService;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.fdd.FddEnterpriseEmpowerVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@Slf4j
@RequestMapping("/platformServiceAgreement")
@Controller
public class PlatformServiceAgreementController  extends BaseController {


    @Autowired
    private PlatformServiceAgreementService platformServiceAgreementService;

    @Autowired
    private DownloadRemote downloadRemote;


    @RequestMapping(value = "/list")
    @ResponseBody
    public ResponseVo platformAgreementList(@RequestBody FddSignaturesParam query) {
        try{
            SysUser user = getUser();
            if (Objects.isNull(query)
                    || Objects.isNull(query.getPageIndex())
                    || Objects.isNull(query.getPageSize()) || Objects.isNull(user)){
                return ResponseVo.errRest("必填参数不能为空");
            }
            PageInfo<TbXyyPopFddEnterpriseAgreementDTO> res = platformServiceAgreementService.platformAgreementList(query);
            return ResponseVo.successResult(res);
        } catch (Exception e) {
            log.error("PlatformServiceAgreementController.list#query:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("查询异常");
        }
    }


    @PostMapping(value = "/export")
    @ResponseBody
    public ResponseVo<Boolean> exportPlatformAgreement(@RequestBody FddSignaturesParam query) {
        try {
            log.info("exportPlatformAgreement:{}", JSON.toJSONString(query));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.PLATFORM_SERVICE_PROTOCOL)
                    .build();
            boolean res = downloadRemote.saveTask(content);
            log.info("exportPlatformAgreement result:{} return {}", JSON.toJSONString(query), res);
            return ResponseVo.successResult(res);
        } catch (Exception e) {
            log.error("exportPlatformAgreement error:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public ResponseVo platformAgreementEdit(Long id) {
        try{

            if (Objects.isNull(id)){
                return ResponseVo.errRest("数据传参为空");
            }
            TbXyyPopFddEnterpriseAgreementDTO res = platformServiceAgreementService.platformAgreementById(id);
            return ResponseVo.successResult(res);
        } catch (Exception e) {
            log.error("PlatformServiceAgreementController.platformAgreementEdit#query:{} 异常", JSON.toJSONString(id), e);
            return ResponseVo.errRest("查询异常");
        }
    }

    @RequestMapping(value = "/terminateTask", method = RequestMethod.GET)
    @ResponseBody
    public ResponseVo platformAgreementTerminateTask(Long id, String reason) {
        try{
            SysUser user = getUser();
            if (Objects.isNull(id) || Objects.isNull(reason) || reason.length() > 100 || Objects.isNull(user)){
                return ResponseVo.errRest("数据传参异常");
            }
            platformServiceAgreementService.platformAgreementTerminateTask(id,reason, user.getUsername());
        } catch (Exception e) {
            log.error("PlatformServiceAgreementController.platformAgreementTerminateTask{} 异常", JSON.toJSONString(id), e);
            return ResponseVo.errRest("终止任务异常");
        }
        return ResponseVo.successResult("终止任务成功");
    }


    @RequestMapping(value = "/addAgreement")
    @ResponseBody
    public ResponseVo platformAgreementAddAgreement(@RequestBody TbXyyPopFddEnterpriseAgreementDTO agreement) {
        try{
            SysUser user = getUser();
            if (Objects.isNull(agreement) || Objects.isNull(user)){
                return ResponseVo.errRest("数据传参异常");
            }
            platformServiceAgreementService.platformAgreementAddAgreement(agreement, user.getUsername());
        } catch (Exception e) {
            log.error("PlatformServiceAgreementController.platformAgreementAddAgreement{} 异常", JSON.toJSONString(agreement), e);
            return ResponseVo.errRest(e.getMessage());
        }
        return ResponseVo.successResult("新增成功");
    }

    @RequestMapping(value = "/submit")
    @ResponseBody
    public ResponseVo platformAgreementSubmitAgreement(@RequestBody TbXyyPopFddEnterpriseAgreementDTO agreement) {
        try{
            SysUser user = getUser();
            if (Objects.isNull(agreement) || Objects.isNull(user)){
                return ResponseVo.errRest("数据传参异常");
            }
            platformServiceAgreementService.platformAgreementSubmitAgreement(agreement);
        } catch (Exception e) {
            log.error("PlatformServiceAgreementController.platformAgreementAddAgreement{} 异常", JSON.toJSONString(agreement), e);
            return ResponseVo.errRest(e.getMessage());
        }
        return ResponseVo.successResult("修改成功");
    }


    @RequestMapping(value = "/openedOrgChange", method = RequestMethod.GET)
    @ResponseBody
    public ResponseVo openedOrgChange(@RequestParam("sourceOrg") String sourceOrg, @RequestParam("targetOrg") String targetOrg, @RequestParam("operationType") Integer operationType) {
        try{
            SysUser user = getUser();
            if (Objects.isNull(sourceOrg) || Objects.isNull(user) || Objects.isNull(targetOrg)){
                return ResponseVo.errRest("数据传参异常");
            }
            String result = platformServiceAgreementService.openedOrgChange(sourceOrg, targetOrg, user.getUsername(), operationType);
            if (StringUtils.isEmpty(result))return ResponseVo.successResult("修改成功");

            return ResponseVo.errRest(result);
        } catch (Exception e) {
            log.error("PlatformServiceAgreementController.sourceOrg{}，targetOrg{} 异常", sourceOrg,targetOrg, e);
            return ResponseVo.errRest(e.getMessage());
        }
    }


    @RequestMapping(value = "/changeFddAdminAccount", method = RequestMethod.POST)
    @ResponseBody
    public ResponseVo changeFddAdminAccount(@RequestBody FddEnterpriseEmpowerVo vo) {
        try{
            if (Objects.isNull(vo) || Objects.isNull(vo.getAccount()) || Objects.isNull(vo.getOrgId())){
                return ResponseVo.errRest("数据传参异常");
            }
            String result = platformServiceAgreementService.changeFddAdminAccount(vo);
            if (StringUtils.isEmpty(result))return ResponseVo.successResult("修改成功");

            return ResponseVo.errRest(result);
        } catch (Exception e) {
            log.error("PlatformServiceAgreementController.changeFddAdminAccount 异常", e);
            return ResponseVo.errRest(e.getMessage());
        }
    }
}
