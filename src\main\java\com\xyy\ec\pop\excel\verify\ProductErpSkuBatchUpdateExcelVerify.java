package com.xyy.ec.pop.excel.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.xyy.ec.pop.config.ProductBatchUpdateConfig;
import com.xyy.ec.pop.vo.ErpSkuBatchUpdateVo;
import com.xyy.ec.pop.vo.SkuBatchUpdateVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @project_name: xyy-ec-pop-service
 * @Auther: Jincheng.Li
 * @Date: 2021/04/14/15:37
 * @Description: ERP商品过滤
 */
@Slf4j
public class ProductErpSkuBatchUpdateExcelVerify implements IExcelVerifyHandler<ErpSkuBatchUpdateVo> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(ErpSkuBatchUpdateVo vo) {
        if (StringUtils.isNotBlank(vo.getOrgId()) || StringUtils.isNotBlank(vo.getErpCode())) {
            return new ExcelVerifyHandlerResult(true);
        }
        return new ExcelVerifyHandlerResult(false);
    }

    public static void trim(List<ErpSkuBatchUpdateVo> vos) {
        vos.forEach(vo -> {
            vo.setOrgId(org.apache.commons.lang.StringUtils.trimToNull(vo.getOrgId()));
            vo.setErpCode(org.apache.commons.lang.StringUtils.trimToNull(vo.getErpCode()));
            vo.setCode(org.apache.commons.lang.StringUtils.trimToNull(vo.getCode()));
            vo.setProductName(org.apache.commons.lang.StringUtils.trimToNull(vo.getProductName()));
            vo.setCommonName(org.apache.commons.lang.StringUtils.trimToNull(vo.getCommonName()));
            vo.setApprovalNumber(org.apache.commons.lang.StringUtils.trimToNull(vo.getApprovalNumber()));
            vo.setManufacturer(org.apache.commons.lang.StringUtils.trimToNull(vo.getManufacturer()));
            vo.setSpec(org.apache.commons.lang.StringUtils.trimToNull(vo.getSpec()));
        });
    }

    public static void valid(List<ErpSkuBatchUpdateVo> vos) {
        Map<String, Long> countMap = vos.stream().collect(Collectors.groupingBy(item -> (item.getOrgId() +"-"+ item.getErpCode()), Collectors.counting()));
        log.info("##vaild##countMap:{}",countMap);
        vos.forEach(vo -> {
            StringBuilder errMsg = new StringBuilder();
            if (vo.getCode() == null && vo.getApprovalNumber() == null
                    && vo.getManufacturer() == null && vo.getProductName() == null
                    && vo.getCommonName() == null && vo.getSpec() == null) {
                errMsg.append("没有可更新项,");
            }

            if (vo.getOrgId() == null){
                vo.setFailed(true);
                errMsg.append(" 机构编码不能为空,");
            }

            if (vo.getErpCode() == null){
                vo.setFailed(true);
                errMsg.append(" 商品ERP编码不能为空,");
            }

            if (errMsg.length() > 0) {
                vo.setErrorMsg(errMsg.toString().substring(0, errMsg.length() - 1));
                vo.setFailed(true);
            }
        });
    }
}