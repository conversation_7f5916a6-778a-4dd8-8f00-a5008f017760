package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.export.param.CheckPaymentProveParam;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RequestMapping("/merchantMarginExport/async")
@RestController
public class ExportMerchantMarginController extends BaseController {

    @Autowired
    private DownloadRemote downloadRemote;

    @ResponseBody
    @GetMapping(value = "/export")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> export(CheckPaymentProveParam param) {
        log.info("ExportMerchantMarginController.export#queryDto:{}", JSON.toJSONString(param));
        try {
            List<Long> provIds = getProvIds(param.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            param.setProvIds(provIds);
            param.setStartTime(DateUtil.modifyStartTime(param.getStartTime()));
            param.setEndTime(DateUtil.modifyEndTime(param.getEndTime()));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(param)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.MERCHANT_MARGIN)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("ExportMerchantMarginController.export#queryDto:{} return {}", JSON.toJSONString(param), b);
            return ResponseVo.successResult(b);
        }catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("ExportMerchantMarginController.export#queryDto:{} 异常", JSON.toJSONString(param), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

}
