package com.xyy.ec.pop.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShopNextdayConfDTO implements Serializable {

    private static final long serialVersionUID = -6192248286194307268L;

    private String shopCode;

    private Integer nextdayEnabled;

}
