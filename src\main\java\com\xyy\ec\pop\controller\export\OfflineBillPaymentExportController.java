package com.xyy.ec.pop.controller.export;

import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.param.OfflineBillExportAdminParam;
import com.xyy.ec.pop.server.api.order.enums.OrderPayTypeEnums;
import com.xyy.ec.pop.server.api.seller.enums.BillPayTypeEnum;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description 线下转账入账单
 */
@Slf4j
@RequestMapping("/offlineAccountPopBillPayment/async")
@RestController
public class OfflineBillPaymentExportController extends BaseController {
    @Autowired
    private DownloadRemote downloadRemote;
    /**
     * 列表根据搜索条件统计佣金金额，应结算金额
     * @param query
     * @return
     */
    @RequestMapping(value = "/exportBillPaymemtList", method = RequestMethod.GET)
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportBillPaymemtList(OfflineBillExportAdminParam query) {
        try {
            List<Long> provIds = getProvIds(query.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            query.setProvIds(provIds);
            query.setPayTypes(OrderPayTypeEnums.getOfflineTypes(query.getPayType()));
            query.setPayType(null);
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .businessType(DownloadTaskBusinessTypeEnum.BILL_PAYMENT_OFFLINE)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("查询统计线下转账入账单异常！", e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出线下转账入账单明细
     * @param query
     */
    @RequestMapping(value = "/exportBillPaymemtDetailList", method = RequestMethod.GET)
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportBillPaymemtDetailList(OfflineBillExportAdminParam query){
        try {
            List<Long> provIds = getProvIds(query.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            query.setProvIds(provIds);
            query.setPayTypes(OrderPayTypeEnums.getOfflineTypes(query.getPayType()));
            query.setPayType(null);
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .businessType(DownloadTaskBusinessTypeEnum.BILL_PAYMENT_OFFLINE_DETAIL)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("查询统计线下转账入账单异常！", e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }
}
