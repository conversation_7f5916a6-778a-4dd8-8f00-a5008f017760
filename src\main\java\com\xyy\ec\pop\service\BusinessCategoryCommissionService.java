package com.xyy.ec.pop.service;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.vo.BusinessCategoryCommissionVo;
import com.xyy.ec.pop.vo.BusinessCategoryVo;

import java.util.List;
import java.util.Map;

/**
 * @version v1
 * @Description 企业佣金比例
 * <AUTHOR>
 */
public interface BusinessCategoryCommissionService {
    List<BusinessCategoryCommissionVo> getCommissions(String orgId) throws ServiceException;

    Map<String,List<BusinessCategoryCommissionVo>> getCommissionsByorgIds(List<String> orgIds) throws ServiceException;

    void updateCommissions(String orgId, List<BusinessCategoryCommissionVo> commissions, String username) throws ServiceException;
}
