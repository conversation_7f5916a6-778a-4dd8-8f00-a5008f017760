package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.helper.PopSkuQualificationHelper;
import com.xyy.ec.pop.server.api.product.api.PopSkuQualificationApi;
import com.xyy.ec.pop.server.api.product.dto.PopSkuQualificationDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuQualificationsDetailDto;
import com.xyy.ec.pop.service.SkuQualificationService;
import com.xyy.ec.pop.vo.PopSkuQualificationVo;
import com.xyy.ec.pop.vo.PopSkuQualificationsDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SkuQualificationServiceImpl implements SkuQualificationService {
    @Reference
    private PopSkuQualificationApi skuQualificationApi;

    @Override
    public List<PopSkuQualificationVo> selectByBarcode(String barcode) {
        log.info("#SkuQualificationServiceImpl.selectByBarcode#info,参数：barcode:{}", barcode);
        try {
            ApiRPCResult<List<PopSkuQualificationDto>> rpcResult = skuQualificationApi.selectByBarcode(barcode);
            if (rpcResult.isFail()) {
                log.error("#SkuQualificationServiceImpl.selectByBarcode#error,RPC请求失败，参数：barcode:{}，返回：{}", barcode, JSON.toJSONString(rpcResult));
                return Lists.newArrayList();
            }
            return PopSkuQualificationHelper.popSkuQualificationDto2VoList(rpcResult.getData());
        } catch (Exception e) {
            log.error("#SkuQualificationServiceImpl.selectByBarcode#error,参数：barcode:{}", barcode, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<PopSkuQualificationsDetailVo> selectQualificationDetailByCategoryId(Long firstCategoryId, Long secondCategoryId) {
       log.info("#SkuQualificationServiceImpl.selectQualificationDetailByCategoryId#info,参数：firstCategoryId:{},secondCategoryId:{}",firstCategoryId,secondCategoryId);
       try {
           ApiRPCResult<List<PopSkuQualificationsDetailDto>> rpcResult = skuQualificationApi.selectQualificationDetailByCategoryId(firstCategoryId,secondCategoryId);
          if (rpcResult.isFail()){
              log.error("#SkuQualificationServiceImpl.selectQualificationDetailByCategoryId#error,RPC请求失败，参数：firstCategoryId:{},secondCategoryId:{},返回",firstCategoryId,secondCategoryId, JSON.toJSONString(rpcResult));
              return Lists.newArrayList();
          }
           return PopSkuQualificationHelper.popSkuQualificationsDetailDto2VoList(rpcResult.getData());
       } catch (Exception e) {
           log.error("#SkuQualificationServiceImpl.selectQualificationDetailByCategoryId#error,参数：firstCategoryId:{},secondCategoryId:{}",firstCategoryId,secondCategoryId,e);
           return Lists.newArrayList();
       }
    }
}
