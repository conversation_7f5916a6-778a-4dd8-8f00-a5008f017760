package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.ResultVO;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.CorporationRemote;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.merchant.api.admin.PopMerchantTotalFundAccountApi;
import com.xyy.ec.pop.server.api.merchant.api.enums.MarketingServiceFundChangeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.PopMerchantFundAccountDto;
import com.xyy.ec.pop.server.api.merchant.dto.PopMerchantFundFlowDto;
import com.xyy.ec.pop.server.api.merchant.param.MerchantFundAccountQueryParam;
import com.xyy.ec.pop.server.api.merchant.param.MerchantFundFlowQueryParam;
import com.xyy.ec.pop.vo.ResponseVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/9/5 19:22
 */
@Slf4j
@Controller
@Api(tags = "商户总资金账号")
@RequestMapping("/merchant/totalFundAccount")
public class MerchantTotalFundAccountController extends BaseController {
    @Reference(version = "1.0.0")
    private PopMerchantTotalFundAccountApi popMerchantTotalFundAccountApi;
    @Autowired
    private DownloadRemote downloadRemote;

    @Autowired
    private CorporationRemote corporationRemote;

    /**
     * 资金账户变动类型
     *
     * @return
     */
    @GetMapping("/getFundChangeType")
    @ResponseBody
    public ResultVO getFundChangeType() {
        try {
            List<Map<String, Object>> fundChangeTypeMap = new ArrayList<>();
            fundChangeTypeMap.add(getMap(MarketingServiceFundChangeEnum.RECHARGE_MARGIN.getCode(),MarketingServiceFundChangeEnum.RECHARGE_MARGIN.getName()));
            fundChangeTypeMap.add(getMap(MarketingServiceFundChangeEnum.PURCHASE_QUOTA.getCode(),MarketingServiceFundChangeEnum.PURCHASE_QUOTA.getName()));
            fundChangeTypeMap.add(getMap(99, "其他"));
            return ResultVO.createSuccess(fundChangeTypeMap);
        } catch (Exception e) {
            log.error("getFundChangeType ", e);
            return ResultVO.createError(e.getMessage());
        }
    }
    private Map<String, Object> getMap(Integer code,String name) {
        Map<String, Object> map = new HashMap<>();
        map.put("code", code);
        map.put("name", name);
        return map;
    }

    /**
     * 分页查询商家资金账户流水
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/pageFundFlow")
    @ResponseBody
    public ResponseVo<PageInfo<PopMerchantFundFlowDto>> pageFundFlow(@RequestBody MerchantFundFlowQueryParam param) {
        try {
            log.info("MerchantTotalFundAccountController.pageFundFlow#param:{}", JSON.toJSONString(param));
            if (param == null || param.getPageNum() == null || param.getPageNum() < 1 || param.getPageSize() == null || param.getPageSize() < 1) {
                return ResponseVo.errRest("请检查分页参数");
            }
            if (param.getFundChangeType() != null && param.getFundChangeType().equals(99)) {
                List<Integer> fundChangeTypeList = MarketingServiceFundChangeEnum.toMap(null)
                        .stream().map(item -> (Integer)item.get("code")).collect(Collectors.toList());
                fundChangeTypeList.remove(MarketingServiceFundChangeEnum.RECHARGE_MARGIN.getCode());
                fundChangeTypeList.remove(MarketingServiceFundChangeEnum.PURCHASE_QUOTA.getCode());
                param.setFundChangeTypeList(fundChangeTypeList);
                param.setFundChangeType(null);
            }
            ApiRPCResult<PageInfo<PopMerchantFundFlowDto>> result = popMerchantTotalFundAccountApi.pageFundFlow(param);
            return ResponseVo.successResult(result.getData());
        } catch (Exception e) {
            log.error("MerchantTotalFundAccountController.pageFundFlow#error. param:{}", JSON.toJSONString(param), e);
            return ResponseVo.errRest("分页查询商家资金账户流水失败");
        }
    }
    /**
     * 分页查询商家资金账户余额
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/pageFundAccount")
    @ResponseBody
    public ResponseVo<PageInfo<PopMerchantFundAccountDto>> list(@RequestBody MerchantFundAccountQueryParam param) {
        try {
            log.info("MerchantTotalFundAccountController.pageFundFlow#param:{}", JSON.toJSONString(param));
            if (param == null || param.getPageNum() == null || param.getPageNum() < 1 || param.getPageSize() == null || param.getPageSize() < 1) {
                return ResponseVo.errRest("请检查分页参数");
            }
            ApiRPCResult<PageInfo<PopMerchantFundAccountDto>> result = popMerchantTotalFundAccountApi.selectFundAccountPageList(param);
            if (result.isFail()) {
                return ResponseVo.errRest(result.getErrMsg());
            }
            return ResponseVo.successResult(result.getData());
        } catch (Exception e) {
            log.error("MerchantTotalFundAccountController.pageFundFlow#error. param:{}", JSON.toJSONString(param), e);
            return ResponseVo.errRest("分页查询商家资金账户流水失败");
        }
    }

    /**
     * 导出资金流水
     * @return
     */
    @PostMapping(value = "/exportFundFlow")
    @ResponseBody
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportFundFlow(@RequestBody MerchantFundFlowQueryParam param) {
        log.info("MerchantTotalFundAccountController.exportFundFlow#queryDto:{}", JSON.toJSONString(param));
        try {
            if (param.getFundChangeType() != null && param.getFundChangeType().equals(99)) {
                List<Integer> fundChangeTypeList = MarketingServiceFundChangeEnum.toMap(null)
                        .stream().map(item -> (Integer)item.get("code")).collect(Collectors.toList());
                fundChangeTypeList.remove(MarketingServiceFundChangeEnum.RECHARGE_MARGIN.getCode());
                fundChangeTypeList.remove(MarketingServiceFundChangeEnum.PURCHASE_QUOTA.getCode());
                param.setFundChangeTypeList(fundChangeTypeList);
                param.setFundChangeType(null);
            }
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(param)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.ADMIN_MERCHANT_TOTAL_FUND_ACCOUNT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("MerchantTotalFundAccountController.exportFundFlow#queryDto:{} return {}", JSON.toJSONString(param), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("MerchantTotalFundAccountController.exportFundFlow#queryDto:{} 异常", JSON.toJSONString(param), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }
    /**
     * 导出资金流水
     * @return
     */
    @PostMapping(value = "/exportFundAccount")
    @ResponseBody
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportFundAccount(@RequestBody MerchantFundAccountQueryParam param) {
        log.info("MerchantTotalFundAccountController.exportFundAccount#queryDto:{}", JSON.toJSONString(param));
        try {
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(param)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.MERCHANT_TOTAL_BALANCE)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("MerchantTotalFundAccountController.exportFundAccount#queryDto:{} return {}", JSON.toJSONString(param), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("MerchantTotalFundAccountController.exportFundAccount#queryDto:{} 异常", JSON.toJSONString(param), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }
}
