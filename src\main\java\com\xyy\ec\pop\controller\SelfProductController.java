package com.xyy.ec.pop.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.config.ProductConfig;
import com.xyy.ec.pop.config.XyyConfig;
import com.xyy.ec.pop.config.XyyPopBaseConfig;
import com.xyy.ec.pop.dto.SelfProductDTO;
import com.xyy.ec.pop.helper.ProductSkuConvertHelper;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.*;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationPriceTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.product.enums.ActivityTypeEnum;
import com.xyy.ec.pop.server.api.product.enums.PopSkuStatus;
import com.xyy.ec.pop.service.ProductCategoryService;
import com.xyy.ec.pop.service.ProductService;
import com.xyy.ec.pop.vo.*;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.param.EcSkuExamineParamDTO;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.query.EcSkuSellerAdminPageQuery;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.result.EcSkuDetailDto;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.result.SkuExamineLogDTO;
import com.xyy.ec.product.back.end.ecp.skucategory.dto.CategoryBackEndDTO;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import com.xyy.ec.system.business.utils.BranchEnum;
import com.xyy.me.product.service.read.dto.TotalDictionaryReadDto;
import com.xyy.ms.promotion.business.result.MarketingGroupBuyingBaseInfoDTO;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/self/product/v2")
@Api(tags = "EC商品控制类")
public class SelfProductController extends BaseController {
    private static final Logger LOG = LoggerFactory.getLogger(ProductController.class);

    @Autowired
    private XyyConfig xyyConfig;
    @Autowired
    private XyyPopBaseConfig popBaseConfig;
    @Value("${pop-base.bigDescImgUrlPrefix}")
    private String bigDescImgUrlPrefix;
    @Autowired
    private ShopAdminRemoteService shopAdminRemoteService;
    @Autowired
    private EcCsuRpcService ecCsuRpcService;
    @Autowired
    private CorporationRemote corporationRemote;
    @Autowired
    private ProductSkuRemoteAdapter productSkuRemoteAdapter;
    @Autowired
    private EcSkuRemoteAdapter ecSkuRemoteAdapter;
    @Autowired
    private PromotionRemote promotionRemote;
    @Autowired
    private ProductService productService;
    @Autowired
    private CorporationPriceTypeRemote corporationPriceTypeRemote;
    @Autowired
    private PopErpTaskRemote popErpTaskRemote;
    @Autowired
    private BusinessCategoryCommissionRemoteAdapter businessCategoryCommissionRemoteAdapter;
    @Autowired
    private ProductCategoryService productCategoryService;

    @Value("#{'${ec.product.branchCode}'.split(',')}")
    private List<String> branchCodeList;



    @ResponseBody
    @GetMapping(value = "/list")
    @ApiOperation("商品列表")
    public ResponseVo<Page<ProductListVo>> list(@ApiParam(name = "page", value = "分页") Page<ProductVo> page, @ApiParam(name = "po", value = "商品入参实体") ProductVo po) {
        po.setPageNum(page.getPageNum());
        po.setPageSize(page.getPageSize());
        po.setProperty(page.getProperty());
        po.setDirection(page.getDirection());
//        po.setIsThirdCompany(com.xyy.ec.pop.constants.Constants.IS1);
//        List<String> branchCodes = getProvIds(po.getBranchCode());
//
//        if (CollectionUtils.isEmpty(branchCodes)) {
//            return ResponseVo.successResult(getEmptyPageList());
//        }
        po.setBranchCodes(StringUtils.isEmpty(po.getBranchCode()) ? branchCodeList : Lists.newArrayList(po.getBranchCode()));
        return ResponseVo.successResult(productListVo(po));
    }


    @GetMapping(value = "/detail")
    @ApiOperation("商品明细")
    public String detail(@ApiParam(name = "id", value = "商品id") Long id, @ApiIgnore Model model, @ApiParam(name = "isAuditing", value = "是否审核标识") Integer isAuditing,
                         @ApiParam(name = "shopCode", value = "商铺code") String shopCode) {
        try {
            ProductSkuVo productVo = productService.getEcProductInfo(shopCode, id);
            long erpId = NumberUtils.toLong(productVo.getErpThirdCategoryId());
            CategoryBackEndDTO catDto = ecSkuRemoteAdapter.getCategoryInfoByErpCategoryId(erpId == 0 ? null : erpId);
            Byte priceType = corporationPriceTypeRemote.queryPriceTypeByOrgId(productVo.getOrgId());

            if (catDto != null) {
                productVo.setSkuRelationCategoryName(catDto.getName());
                productVo.setSkuRelationCategory(catDto.getId());
            }
            model.addAttribute("id", productVo.getId());
            //有设置同步erp
            if (Objects.equals(productVo.getStockSyncErp(), 1) || Objects.equals(productVo.getPriceSyncErp(), 1)) {
                boolean hasConfig = popErpTaskRemote.hasConfig(productVo.getOrgId());
                if (!hasConfig) {
                    //配置为空表示没有对接erp
                    productVo.setStockSyncErp(0);
                    productVo.setPriceSyncErp(0);
                }
            }

            model.addAttribute("host", xyyConfig.getEcHost());
            model.addAttribute("bigImgUrlPrefix", popBaseConfig.getBigImgUrlPrefix());
            model.addAttribute("smallImgUrlPrefix", popBaseConfig.getSmallImgUrlPrefix());
            model.addAttribute("smallDescImgUrlPrefix", popBaseConfig.getSmallDescImgUrlPrefix());
            model.addAttribute("bigDescImgUrlPrefix", bigDescImgUrlPrefix);
            String certicatePrefix = xyyConfig.getCdnConfig().getShowUrl();
            model.addAttribute("certicatePrefix", certicatePrefix);

            BigDecimal commissionRatio = productVo.getCommissionRatio();

            model.addAttribute("productVo", productVo);
            model.addAttribute("isAuditing", isAuditing);
            model.addAttribute("isInstrumentCategory", ProductConfig.config.instrumentCategory.equals(productVo.getErpFirstCategoryId()));
            model.addAttribute("isDrug", ProductConfig.config.drugCategory.contains(productVo.getErpFirstCategoryId()));
            model.addAttribute("isChi", ProductConfig.config.chiMedCategory.contains(productVo.getErpFirstCategoryId()));
            model.addAttribute("corOrgId", productVo.getOrgId());
            model.addAttribute("priceType", priceType == null ? CorporationPriceTypeEnum.defaultType.getCode() : priceType);
            model.addAttribute("commissionRatio", commissionRatio);
            model.addAttribute("commissionConfig", businessCategoryCommissionRemoteAdapter.getPopCommissionConfig());
            //获取ec全部发布分类
            List<TotalDictionaryReadDto> fourLevelProductCategory = productCategoryService.getFourLevelProductCategory();
            LOG.info("中台四级分类" + JSON.toJSONString(fourLevelProductCategory));
            List<EcDeployCategoryVO> fourCategoryVOS = convertCategoryVO(fourLevelProductCategory);
            Map<String, List<EcDeployCategoryVO>> fourLevelProductCategoryMap = fourCategoryVOS.stream().collect(Collectors.groupingBy(data -> String.valueOf(data.getParentId())));
            model.addAttribute("categoryMap", JSON.toJSON(fourLevelProductCategoryMap));
            return "/product/detail1";
        } catch (Exception e) {
            LOG.error("查询商品信息异常. id:{},shopCode:{}", id, shopCode, e);
            return "/product/detail1";
        }
    }


    @GetMapping(value = "/auditingPage")
    @ApiOperation("跳转审核页面")
    public String auditingPage(@ApiParam(name = "id",value = "商品id") Integer id,@ApiParam(name = "shopCode", value = "商铺code") String shopCode,
                               @ApiIgnore Model model){
        model.addAttribute("id",id);
        model.addAttribute("shopCode",shopCode);
        return "product/auditing1";
    }


    @PostMapping(value = "/auditing")
    @ResponseBody
    @ApiOperation("商品审核")
    public Object auditing(SelfProductDTO selfProductDTO) {
        if (Objects.isNull(selfProductDTO) || Objects.isNull(selfProductDTO.getId()) || StringUtils.isEmpty(selfProductDTO.getShopCode())) {
            return this.addError("缺少必要参数！");
        }
        try {
            ProductSkuVo productVo = productService.getEcProductInfo(selfProductDTO.getShopCode(), selfProductDTO.getId());
            if (StringUtils.isBlank(productVo.getSkuCategory()) || productVo.getSkuCategory().equals("0")) {
                return this.addError("缺少经营分类！");
            }
            if (Objects.equals(selfProductDTO.getStatus(), PopSkuStatus.STAY_ON_SHELF.getValue()) && StringUtils.isEmpty(productVo.getErpFirstCategoryId())) {
                return this.addError("缺少一级分类参数！");
            }
            if (Objects.equals(selfProductDTO.getStatus(), PopSkuStatus.STAY_ON_SHELF.getValue()) && StringUtils.isEmpty(productVo.getErpSecondCategoryId())) {
                return this.addError("缺少二级分类参数！");
            }
            if (Objects.isNull(productVo) || productVo.getStatus() != PopSkuStatus.TO_AUDITED.getValue()) {
                return this.addError("当前商品不能审核！请刷新页面查看商品最新状态");
            }
            //待上架
            if (Objects.equals(selfProductDTO.getStatus(), PopSkuStatus.STAY_ON_SHELF.getValue())) {
                String errorTip = productService.ecValidAudit(productVo);
                if (StringUtils.isNotEmpty(errorTip)) {
                    return this.addError(errorTip);
                }
            }
            SysUser user = getUser();
            ecCsuRpcService.audit(EcSkuExamineParamDTO.builder().status(selfProductDTO.getStatus()).skuId(selfProductDTO.getId()).operator(user.getUsername())
                    .remark(selfProductDTO.getRemark()).build());
        } catch (Exception e) {
            LOG.error("SelfProductController.auditing 审核失败 id:{} ", selfProductDTO.getId(), e);
            return this.addError("操作失败！");
        }
        return this.addResult("操作成功！");
    }

    @ResponseBody
    @GetMapping(value = "/skuAuditLog/{skuId}")
    public ResponseVo<List<SkuExamineLogDTO>> skuAuditLog(@PathVariable("skuId") Long skuId) {
        List<SkuExamineLogDTO> skuAuditLogList = ecCsuRpcService.getExamineLogsBySkuId(skuId);
        if (CollectionUtils.isEmpty(skuAuditLogList)) {
            return ResponseVo.successResult(Lists.newArrayList());
        }
        return ResponseVo.successResult(skuAuditLogList);
    }

    /**
     * 查询ec数据并适配类型
     * @param productVo
     * @return
     */
    private Page<ProductListVo> productListVo(ProductVo productVo) {
        EcSkuSellerAdminPageQuery query = ProductSkuConvertHelper.convertToEcQuery(productVo);
        PageInfo<EcSkuDetailDto> detailPage = ecCsuRpcService.adminQuery(query);
        Page<ProductListVo> ecSkuList = ProductSkuConvertHelper.convertToEcPage(detailPage);

        if (null == ecSkuList || CollectionUtils.isEmpty(ecSkuList.getRows())) {
            return getEmptyPageList();
        } else {
            remarkActivity(ecSkuList.getRows());
            //调用库存接口查询商品库存
            //调用营销接口查询拼团价格和总限购库存
            fillGroupPriceAndTotalLimitQty(ecSkuList.getRows());
            //填充ec库存
            fillEcQty(ecSkuList.getRows());
            return ecSkuList;
        }
    }

    private List<EcDeployCategoryVO> convertCategoryVO(List<TotalDictionaryReadDto> list) {
        List<EcDeployCategoryVO> categoryList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        list.forEach(val -> {
            EcDeployCategoryVO vo = new EcDeployCategoryVO();
            vo.setId(val.getId().longValue());
            vo.setName(val.getDictName());
            vo.setParentId(val.getParentId().longValue());
            vo.setLevel(val.getLevelNode().intValue());
            categoryList.add(vo);
        });
        return categoryList;
    }

    /**
     * 标记商品活动
     * @param rows
     */
    private void remarkActivity(List<ProductListVo> rows) {
        String orgId = rows.get(0).getOrgId();
        CorporationDto corporationDto = corporationRemote.queryCorpBaseByOrgId(orgId);
        if (corporationDto == null) {
            return;
        }
        List<Long> csuIds = rows.stream().map(item -> item.getCsuid()).collect(Collectors.toList());
        List<EcpSkuActiveVo> acts = productSkuRemoteAdapter.getActivityCsuIds(csuIds);
        rows.stream().forEach(item->{
            for (EcpSkuActiveVo vo : acts) {
                if (item.getCsuid().equals(vo.getCsuId())) {
                    item.setActiveName(vo.getActive().getTitle());
                    break;
                }
            }
        });
    }

    private void fillEcQty(List<ProductListVo> ecSkuList) {
        List<Long> csuIds = ecSkuList.stream().map(item -> item.getCsuid()).filter(item->item!=null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(csuIds)){
            return;
        }
        Map<Long, Integer> ecStock = ecSkuRemoteAdapter.getStockBySkuIdList(csuIds).stream().collect(Collectors.toMap(item -> item.getCsuId(), item -> item.getAvailableQty()));
        ecSkuList.forEach(item->{
            Integer stock = ecStock.get(item.getCsuid());
            if(stock!=null){
                item.setAvailableQty(stock.toString());
            }
        });
    }

    private void fillGroupPriceAndTotalLimitQty(List<ProductListVo> rows) {
        //活动品的活动id集合
        List<Long> activityIdList = rows.stream().filter(f -> Objects.equals(f.getActivityType(), ActivityTypeEnum.GROUP.getCode())).map(item -> item.getActivityId()).collect(Collectors.toList());
        //查询商品活动信息
        List<MarketingGroupBuyingBaseInfoDTO> skuActivities = promotionRemote.listMarketingGroupBuyingBaseInfo(activityIdList);
        Map<Long, MarketingGroupBuyingBaseInfoDTO> csuIdEntityMap = skuActivities.stream().collect(Collectors.toMap(item -> item.getCsuId(), Function.identity()));
        rows.stream().forEach(item -> {
            MarketingGroupBuyingBaseInfoDTO groupSkuInfo = csuIdEntityMap.get(item.getCsuid());
            if (groupSkuInfo != null) {
                item.setGroupPrice(groupSkuInfo.getDiscountPrice());
                item.setTotalLimitQty(groupSkuInfo.getTotalQty());
            }
        });
    }


    private Page<ProductListVo> getEmptyPageList() {
        Page<ProductListVo> pageInfo = new Page<>();
        pageInfo.setTotal(0L);
        pageInfo.setRows(Lists.newArrayList());
        pageInfo.setPageCount(0);
        return pageInfo;
    }

    @ResponseBody
    @GetMapping(value = "/branch/list")
    @ApiOperation("区域列表")
    public ResponseVo branchList() {
        try {
            //查询区域信息
            Map<String, ShopInfoDTO> branchCodeShopInfoMap = shopAdminRemoteService.queryECBranchShopCode(branchCodeList);
            if (CollectionUtils.isEmpty(branchCodeShopInfoMap)) {
                return ResponseVo.errRest("查询异常");
            }
            //组装dto
            List<ECBranchShopInfoVo> ecBranchShopInfoVoList = branchCodeShopInfoMap.entrySet().stream()
                    .map(entry -> ECBranchShopInfoVo.builder().branchCode(entry.getKey()).shopCode(entry.getValue().getShopCode())
                            .shopName(entry.getValue().getShowName()).branchName(BranchEnum.get(entry.getKey())).build())
                    .collect(Collectors.toList());
            return ResponseVo.successResult(ecBranchShopInfoVoList);
        } catch (Exception e) {
            LOG.error("SelfProductController.branchList异常", e);
            return ResponseVo.errRest("查询异常");
        }
    }
}
