package com.xyy.ec.pop.utils.autoconfigure;

import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPFileFilter;
import org.apache.commons.pool2.impl.GenericObjectPool;

import java.io.InputStream;
import java.io.OutputStream;
import java.text.MessageFormat;

/**
 * FtpClient连接池的操作模板类。<br/>
 * 特别注意：由于ftp操作某个连接用户在服务器上的操作，在使用连接池的时候，要特别注意当前所处的目录。<br/>
 * 若有使用者，请完善下去。<br/>
 *
 * <AUTHOR>
 */
@Slf4j
public class XyyFtpClientTemplate {

    private XyyFtpClientProperties config;
    private GenericObjectPool<FTPClient> ftpClientPool;

    public XyyFtpClientTemplate(XyyFtpClientProperties config, XyyFtpClientFactory xyyFtpClientFactory) {
        this.config = config;
        this.ftpClientPool = new GenericObjectPool<>(xyyFtpClientFactory, config.getPool());
    }

    private static String separator = "/";
    private static String basePath = "/";

    /**
     * 上传文件
     *
     * @param inputStream  文件输入流。不可为null。
     * @param absolutePath ftp服务器的绝对目录。不可为null。
     * @param newFilename  文件名。不可为null，需自行保证在服务器上唯一。
     * @return 文件路径
     */
    public String uploadFile(InputStream inputStream, String absolutePath, String newFilename) throws Exception {
        if (inputStream == null) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "文件");
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (StringUtils.isBlank(absolutePath)) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "目录");
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (!absolutePath.startsWith(basePath)) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "目录");
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (StringUtils.isBlank(newFilename)) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "文件名");
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            //切换到根目录下
            // 判定传的文件目录是否存在，若不存在则创建
            boolean dirExists = ftpClient.changeWorkingDirectory(absolutePath);
            if (!dirExists) {
                absolutePath = makeDirectories(ftpClient, absolutePath);
            }
            dirExists = ftpClient.changeWorkingDirectory(absolutePath);
            if (!dirExists) {
                throw new PopAdminException("目录不存在，上传文件失败。", XyyJsonResultCodeEnum.FTP_UPLOAD_FILE_FAILURE);
            }
            // 上传文件
            boolean isSuccess = ftpClient.storeFile(newFilename, inputStream);
            if (!isSuccess) {
                throw new PopAdminException("上传文件失败。", XyyJsonResultCodeEnum.FTP_UPLOAD_FILE_FAILURE);
            } else {
                return absolutePath + separator + newFilename;
            }
        } catch (PopAdminException e) {
            throw e;
        } catch (Exception e) {
            throw new PopAdminException("上传文件失败。", e, XyyJsonResultCodeEnum.FTP_UPLOAD_FILE_FAILURE);
        } finally {
            IOUtils.closeQuietly(inputStream);
            returnObjectQuietly(ftpClient);
        }
    }

    private void returnObjectQuietly(FTPClient ftpClient) {
        try {
            if (ftpClient != null) {
                //将对象放回池中
                ftpClientPool.returnObject(ftpClient);
            }
        } catch (Exception e) {
            log.warn("上传文件归还FtpClient失败", e);
        }
    }

    /* 暴露FtpClient高频率方法 */

    public boolean remoteRetrieve(String filename) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.remoteRetrieve(filename);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean remoteStore(String filename) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.remoteStore(filename);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean remoteStoreUnique(String filename) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.remoteStoreUnique(filename);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean remoteStoreUnique() throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.remoteStoreUnique();
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean remoteAppend(String filename) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.remoteAppend(filename);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean retrieveFile(String remote, OutputStream local) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.retrieveFile(remote, local);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public InputStream retrieveFileStream(String remote) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.retrieveFileStream(remote);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean storeFile(String remote, InputStream local) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.storeFile(remote, local);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public OutputStream storeFileStream(String remote) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.storeFileStream(remote);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean appendFile(String remote, InputStream local) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.appendFile(remote, local);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public OutputStream appendFileStream(String remote) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.appendFileStream(remote);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean storeUniqueFile(String remote, InputStream local) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.storeUniqueFile(remote, local);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public OutputStream storeUniqueFileStream(String remote) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.storeUniqueFileStream(remote);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean storeUniqueFile(InputStream local) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.storeUniqueFile(local);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public OutputStream storeUniqueFileStream() throws Exception {

        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.storeUniqueFileStream();
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean allocate(int bytes) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.allocate(bytes);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean allocate(int bytes, int recordSize) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.allocate(bytes, recordSize);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public FTPFile mlistFile(String pathname) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.mlistFile(pathname);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public FTPFile[] mlistDir() throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.mlistDir();
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public FTPFile[] mlistDir(String pathname) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.mlistDir(pathname);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public FTPFile[] mlistDir(String pathname, FTPFileFilter filter) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.mlistDir(pathname, filter);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean rename(String from, String to) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.rename(from, to);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean deleteFile(String pathname) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.deleteFile(pathname);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public boolean removeDirectory(String pathname) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.removeDirectory(pathname);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    /**
     * 递归创建目录
     *
     * @param pathname 目录路径，若为相对路径，则在当前所在的工作目录中创建。不可为null。
     * @return 创建目录的绝对路径
     * @throws Exception
     */
    public String makeDirectory(String pathname) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return makeDirectories(ftpClient, pathname);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    /**
     * 递归创建目录
     *
     * @param ftpClient
     * @param pathname  目录路径，若为相对路径，则在当前所在的工作目录中创建。不可为null。
     * @return 创建目录的绝对路径
     * @throws Exception
     */
    private String makeDirectories(FTPClient ftpClient, String pathname) throws Exception {
        if (StringUtils.isBlank(pathname)) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "目录路径");
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        /* 特别注意：ftp的接口不支持创建多级目录。 */
        String pwd = ftpClient.printWorkingDirectory();
        // 判定常见的目录是否是绝对路径，若不是，则在当前目录下递归创建。
        String realAbsolutePath = basePath;
        if (!pathname.startsWith(separator)) {
            realAbsolutePath = pwd;
        }
        if (!realAbsolutePath.endsWith(separator)) {
            realAbsolutePath += separator;
        }
        // 判定传的文件目录是否存在，若不存在则创建
        StringBuffer realAbsolutePathStrBuffer = new StringBuffer();
        boolean dirExists;
        boolean isMakeDirSuccess;
        String tempAbsolutePath;
        String[] dirArray = pathname.split(separator);
        for (String dir : dirArray) {
            if (StringUtils.isNotBlank(dir)) {
                realAbsolutePathStrBuffer.append(dir);
                tempAbsolutePath = realAbsolutePath + realAbsolutePathStrBuffer.toString();
                dirExists = ftpClient.changeWorkingDirectory(tempAbsolutePath);
                if (!dirExists) {
                    isMakeDirSuccess = ftpClient.makeDirectory(tempAbsolutePath);
                    log.info("【FTP】目录不存在，创建目录：{}，创建结果：{}", tempAbsolutePath, isMakeDirSuccess);
                }
                realAbsolutePathStrBuffer.append(separator);
            }
        }
        if (realAbsolutePathStrBuffer.length() > 0) {
            realAbsolutePathStrBuffer.deleteCharAt(realAbsolutePathStrBuffer.length() - 1);
        }
        realAbsolutePath = realAbsolutePath + realAbsolutePathStrBuffer.toString();
        log.info("创建目录的绝对路径：{}", realAbsolutePath);
        boolean b = ftpClient.changeWorkingDirectory(realAbsolutePath);
        if (!b) {
            throw new PopAdminException(XyyJsonResultCodeEnum.FTP_MAKE_DIRECTORY_FAILURE);
        }
        return realAbsolutePath;
    }

    public boolean sendNoOp() throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.sendNoOp();
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public String[] listNames(String pathname) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.listNames(pathname);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public String[] listNames() throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.listNames();
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public FTPFile[] listFiles(String pathname) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.listFiles(pathname);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public FTPFile[] listFiles() throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.listFiles();
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public FTPFile[] listFiles(String pathname, FTPFileFilter filter) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.listFiles(pathname, filter);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public FTPFile[] listDirectories() throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.listDirectories();
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }

    public FTPFile[] listDirectories(String parent) throws Exception {
        FTPClient ftpClient = null;
        try {
            //从池中获取对象
            ftpClient = ftpClientPool.borrowObject();
            return ftpClient.listDirectories(parent);
        } finally {
            returnObjectQuietly(ftpClient);
        }
    }
}
