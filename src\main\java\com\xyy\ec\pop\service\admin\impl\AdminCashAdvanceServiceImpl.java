package com.xyy.ec.pop.service.admin.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.dto.PopCashAdvanceBatchConfirmImportDTO;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.report.Vo.PopCashAdvanceBatchConfirmErrorVo;
import com.xyy.ec.pop.server.api.Enum.CashAdvancePaymentStatusEnum;
import com.xyy.ec.pop.server.api.Enum.PaymentWayEnum;
import com.xyy.ec.pop.server.api.merchant.api.OrgUserRelationApi;
import com.xyy.ec.pop.server.api.seller.api.PopCashAdvanceApi;
import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceDto;
import com.xyy.ec.pop.service.FastDfsUtilService;
import com.xyy.ec.pop.service.admin.AdminCashAdvanceService;
import com.xyy.ec.pop.utils.FileNameUtils;
import com.xyy.ec.pop.vo.BatchUpdateResultVo;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lizhiwei
 * @description: 商户提现
 * create at:  2020/12/7  19:41
 */
@Slf4j
@Service
public class AdminCashAdvanceServiceImpl implements AdminCashAdvanceService {

    @Reference
    private PopCashAdvanceApi popCashAdvanceApi;
    @Reference
    private OrgUserRelationApi orgUserRelationApi;
    @Resource
    private FastDfsUtilService fastDfsUtilService;

    @Override
    public boolean confirmPayment(List<Integer> ids, String updateBy, Date paymentTime) throws ServiceException {
        log.info("#CashAdvanceServiceImpl.confirmPayment#info,参数：ids:{},updateBy:{},paymentTime:{}", ids, updateBy, paymentTime);
        ApiRPCResult rpcResult = popCashAdvanceApi.confirmPayment(ids, updateBy, paymentTime);
        if(rpcResult.isSuccess()){
            return true;
        }
        throw new ServiceException(rpcResult.getErrMsg());
    }

    @Override
    public PopCashAdvanceDto queryById(Integer id) {
        log.info("#CashAdvanceServiceImpl.queryById#id:{}", id);
        ApiRPCResult<PopCashAdvanceDto> rpcResult = popCashAdvanceApi.queryById(id);
        log.info("#CashAdvanceServiceImpl.queryById#id:{},result:{}", id, JSON.toJSONString(rpcResult));
        if (rpcResult == null || rpcResult.isFail()) {
            return null;
        }
        return rpcResult.getData();
    }

    @Override
    public ResponseVo<BatchUpdateResultVo> batchConfirm(List<PopCashAdvanceBatchConfirmImportDTO> dataList, String userName, MultipartFile file) {
        log.info("#CashAdvanceServiceImpl.batchConfirm#dataList:{},userName:{}", JSON.toJSONString(dataList), userName);
        if (CollectionUtils.isEmpty(dataList)) {
            return ResponseVo.errRest("批量确认数据为空");
        }
        List<String> cashDdvanceNumList = dataList.stream()
                .filter(t -> StringUtils.isNotEmpty(t.getCashAdvanceNum()))
                .map(PopCashAdvanceBatchConfirmImportDTO::getCashAdvanceNum)
                .distinct().collect(Collectors.toList());
        ApiRPCResult<List<PopCashAdvanceDto>> cashAdvanceDtoApiRPCResult = popCashAdvanceApi.listByCashAdvanceNums(cashDdvanceNumList);
        if (cashAdvanceDtoApiRPCResult.isFail()) {
            return ResponseVo.errRest("查询提现信息失败");
        }

        Map<String, PopCashAdvanceDto> cashAdvanceDtoMap = Optional.ofNullable(cashAdvanceDtoApiRPCResult.getData()).orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(PopCashAdvanceDto::getCashAdvanceNum, Function.identity(), (v1, v2) -> v1));
        List<Integer> cashAdvanceIds = new ArrayList<>();
        for (PopCashAdvanceBatchConfirmImportDTO importDTO : dataList) {
            PopCashAdvanceDto popCashAdvanceDto = cashAdvanceDtoMap.get(importDTO.getCashAdvanceNum());
            if (popCashAdvanceDto == null) {
                importDTO.setErrorMsg("提现单号不存在");
                continue;
            }
            if (CashAdvancePaymentStatusEnum.PENDING_VERIFICATION.getCode() != popCashAdvanceDto.getPaymentStatus()) {
                importDTO.setErrorMsg("提现单状态必须是“待核款”");
                continue;
            }
            if (PaymentWayEnum.OFFLINE.getCode() != popCashAdvanceDto.getPaymentWay()) {
                importDTO.setErrorMsg("提现单打款方式必须为“线下打款”");
                continue;
            }
            cashAdvanceIds.add(popCashAdvanceDto.getId());
        }
        log.info("#CashAdvanceServiceImpl.batchConfirm#cashAdvanceIds:{},userName:{}", JSON.toJSONString(cashAdvanceIds), userName);
        ApiRPCResult batchConfirmResult = popCashAdvanceApi.batchConfirm(cashAdvanceIds, userName);
        if (batchConfirmResult.isFail()) {
            return ResponseVo.errRest("批量确认执行失败");
        }

        BatchUpdateResultVo resultVo = new BatchUpdateResultVo();
        List<PopCashAdvanceBatchConfirmErrorVo> errorData = new ArrayList<>();
        dataList.stream().filter(t -> StringUtils.isNotEmpty(t.getErrorMsg()))
                .forEach(t -> {
                    PopCashAdvanceBatchConfirmErrorVo errorVo = new PopCashAdvanceBatchConfirmErrorVo();
                    errorVo.setCashAdvanceNum(t.getCashAdvanceNum());
                    errorVo.setErrorMsg(t.getErrorMsg());
                    errorData.add(errorVo);
                });
        resultVo.setError(errorData.size());
        resultVo.setSuccess(cashAdvanceIds.size());
        if (CollectionUtils.isNotEmpty(errorData)) {
            log.info("#CashAdvanceServiceImpl.batchConfirm#errorData:{},userName:{}", JSON.toJSONString(errorData), userName);
            //将数据写入excel文件
            String fileUrl = fastDfsUtilService.writeDateToFastDfs(new ExportParams(null, "商户提现批量确认_错误文件", ExcelType.XSSF), errorData.get(0).getClass(), errorData);
            resultVo.setErrorFileName(FileNameUtils.getErrorFileName(file));
            String downLoadUrl = FileNameUtils.getErrorFileDownUrl(fileUrl, resultVo.getErrorFileName());
            resultVo.setErrorFileUrl(downLoadUrl);
        }
        log.info("#CashAdvanceServiceImpl.batchConfirm#resultVo:{},userName:{}", JSON.toJSONString(resultVo), userName);
        return ResponseVo.successResult(resultVo);
    }

    @Override
    public boolean confirmPayment(List<Integer> ids, String updateBy) throws ServiceException {
        log.info("#CashAdvanceServiceImpl.confirmPayment#info,参数：ids:{},updateBy:{}", ids, updateBy);
        ApiRPCResult rpcResult = popCashAdvanceApi.confirmPayment(ids, updateBy);
        if(rpcResult.isSuccess()){
            return true;
        }
        throw new ServiceException(rpcResult.getErrMsg());
    }
}
