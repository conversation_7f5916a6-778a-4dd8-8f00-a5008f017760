package com.xyy.ec.pop.adapter.mop;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.DictRemote;
import com.xyy.pop.mop.api.remote.parameter.DictTypeParame;
import com.xyy.pop.mop.api.remote.result.DictDataDTO;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 适配器类，用于与MOP字典远程服务进行交互。
 */
@Slf4j
@Component
public class MopDictAdapter implements MopBaseAdapter {

    /**
     * 引用MOP字典远程服务。
     */
    @Reference(version = "1.0.0")
    private DictRemote dictRemote;

    /**
     * 根据字典类型获取字典数据列表。
     *
     * @param dictType 字典类型
     * @return 包含字典数据的列表
     */
    public ResponseVo<List<DictDataDTO>> listDictDataByType(String dictType) {
        log.info("MopDictAdapter.listDictDataByType dictType:{}", dictType);
        if (StringUtils.isEmpty(dictType)) {
            return ResponseVo.errRest("字典类型不能为空");
        }
        DictTypeParame parame = new DictTypeParame();
        parame.setDictType(dictType);
        return to(() -> dictRemote.listDictDataByType(parame));
    }
    /**
     * 根据字典类型获取字典数据列表。
     *
     * @param dictTypes 字典类型
     * @return 包含字典数据的列表
     */
    public ResponseVo<Map<String,List<DictDataDTO>>> listDictDataByType(List<String> dictTypes) {
        Map<String,List<DictDataDTO>> map = new HashMap<>();
        for (String dictType : dictTypes) {
            ResponseVo<List<DictDataDTO>> listResponseVo = listDictDataByType(dictType);
            if (listResponseVo.isFail()) {
                return ResponseVo.errRest(listResponseVo.getMessage());
            }
            map.put(dictType,listResponseVo.getData());
        }
        return ResponseVo.successResult(map);
    }
}
