package com.xyy.ec.pop.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.param.PopReceivingReportAdminParam;
import com.xyy.ec.pop.server.api.seller.param.PopReceivingReportParam;
import com.xyy.ec.pop.service.BranchService;
import com.xyy.ec.pop.service.ReceivingReportService;
import com.xyy.ec.pop.vo.*;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import com.xyy.pop.framework.core.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;
import java.util.List;

/**
 * 〈一句话功能简述〉<br>
 * 〈收款报表〉
 *
 * <AUTHOR>
 * @create 2019/11/18
 * @since 1.0.0
 */
@Controller
@RequestMapping("receivablesReportform")
@Slf4j
@Api(tags = "收款报表")
public class ReceivablesReportFormController extends BaseController {
    @Autowired
    private BranchService branchService;
    @Autowired
    private DownloadRemote downloadRemote;

    @Autowired
    private ReceivingReportService reportService;

    @RequestMapping(value = "/index",method = RequestMethod.GET)
    @ApiOperation("收款报表跳转页面")
    public ModelAndView index() {
//        return "/receivablesReportform/index";
        ModelAndView orderModel = new ModelAndView("receivablesReportform/index");
        //获取区域列表
        List<BranchVo> branchVos = branchService.getAllProvinces();
        orderModel.addObject("branchList", branchVos);

        return orderModel;
    }

//    /**
//     * @param receivingReportParam
//     * @return 列表数据
//     */
//    @RequestMapping(value = "/list",method = RequestMethod.GET)
//    @ResponseBody
//    @ApiOperation("收款报表列表数据")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "popReceivingReportDto",value = "收款报表实体"),
//            @ApiImplicitParam(name = "page",value = "分页")
//    })
//    public Object list(PopReceivingReportParam receivingReportParam, Page page) {
//        try {
//            receivingReportParam.setLimit(page.getLimit());
//            receivingReportParam.setOffset(page.getOffset());
//            ApiRPCResult<PageInfo<PopReceivingReportDto>> apiRPCResult = popReceivingReportApi.popReceivingReportList(receivingReportParam);
//            if (apiRPCResult == null || apiRPCResult.isFail()){
//                return "";
//            }
//
//            return apiRPCResult.getData();
//        } catch (Exception e) {
//            log.error("获取列表失败",e);
//        }
//        return "";
//    }

    /**
     * @return 列表数据
     */
    @RequestMapping(value = "/v2/list",method = RequestMethod.GET)
    @ResponseBody
    public Response<PageInfo> list(PopReceivingReportParam receivingReportParam) {
        try {
            return ResponseUtils.returnObjectSuccess(reportService.popReceivingReportList(receivingReportParam));
        } catch (Exception e) {
            log.error("ReceivablesReportFormController.list获取列表失败 # receivingReportParam:{}", JSON.toJSONString(receivingReportParam),e);
        }
        return ResponseUtils.returnSuccess();
    }

    /**
     * 同步商户信息到入账单表
     * @return
     */
    @RequestMapping(value = "/syncMerchant", method = RequestMethod.GET)
    @ResponseBody
    public Object syncMerchant(){

        try {
            reportService.syncMerchant();

        } catch (Exception e) {
            log.error("同步商户信息到入账单表错误",e);
        }
        return ResponseUtils.returnObjectSuccess(true);
    }

    /**
     * @param receivingReportParam
     * @return 合计
     */
    @RequestMapping(value = "/summation",method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("收款报表实体合计")
    @ApiImplicitParam(name = "popReceivingReportDto",value = "收款报表实体")
    public Object summation(PopReceivingReportParam receivingReportParam) {
        BigDecimal summation = reportService.popReceivingReportSummation(receivingReportParam);
        return ResponseUtils.returnObjectSuccess(summation);
    }

    @GetMapping(value = "/async/downloadExcel")
    @ApiOperation("收款报表导出")
    @ResponseBody
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> downloadExcel(PopReceivingReportAdminParam query){
        try {
            log.info("ReceivablesReportFormController.downloadExcel#query:{}", JSON.toJSONString(query));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .businessType(DownloadTaskBusinessTypeEnum.RECEIVING_REPORT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("ReceivablesReportFormController.downloadExcel#query:{} return {}", JSON.toJSONString(query), JSON.toJSONString(b));
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("ReceivablesReportFormController.downloadExcel#query:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

}
