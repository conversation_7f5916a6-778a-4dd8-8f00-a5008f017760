package com.xyy.ec.pop.service.impl;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.CorporationAreaRemoteAdapter;
import com.xyy.ec.pop.remote.OperateMarginRemoteAdapter;
import com.xyy.ec.pop.remote.PopCommissionSettlementRemoteAdapter;
import com.xyy.ec.pop.remote.PopCorporationRemoteAdapter;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationStateEnum;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationAreaDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.merchant.dto.OperateMarginLogDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleSetDto;
import com.xyy.ec.pop.service.OperateMarginService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @version v1
 * @Description 保证金审核通过，修改集合
 * <AUTHOR>
 */
@Service
public class OperateMarginServiceImpl implements OperateMarginService {
    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;
    @Autowired
    private OperateMarginRemoteAdapter operateMarginRemoteAdapter;
    @Autowired
    private CorporationAreaRemoteAdapter corporationAreaRemoteAdapter;
    @Autowired
    private PopCommissionSettlementRemoteAdapter popCommissionSettlementRemoteAdapter;

    @Override
    public void saveCheckResult(OperateMarginLogDto log, Long cId, Date payDate, SysUser sysUser) throws ServiceException {
        CorporationDto corporationDto = popCorporationRemoteAdapter.queryById(cId);
        //状态校验
//        validStatus(corporationDto);
        validPreconditions(corporationDto);
        OperateMarginLogDto  logDto = corporationDto(log,cId,payDate,sysUser);
        operateMarginRemoteAdapter.updateCheckResult(logDto);
    }

    /**
     * 校验前置条件
     * @param corporationDto
     */
    private void validPreconditions(CorporationDto corporationDto) throws ServiceException {
        //校验可售卖区域是否有值
        List<CorporationAreaDto> areaDtos = corporationAreaRemoteAdapter.getCorporationArea(corporationDto.getId());
        if(CollectionUtils.isEmpty(areaDtos)){
            throw new ServiceException("请先设置企业可售卖区域");
        }
        //校验佣金结算方式是否有值
        List<PopCommissionSettleSetDto> settleSetDtos = popCommissionSettlementRemoteAdapter.queryCooperationCommissionsSettlementType(corporationDto.getOrgId());
        if(CollectionUtils.isEmpty(settleSetDtos)){
            throw new ServiceException("请先设置佣金结算方式");
        }
    }

    private OperateMarginLogDto corporationDto(OperateMarginLogDto log, Long cId, Date payDate, SysUser sysUser) {
        OperateMarginLogDto dto = new OperateMarginLogDto();
        dto.setCreateId(sysUser.getId().intValue());
        dto.setMerchantMarginId(log.getMerchantMarginId());
        dto.setComment(log.getComment());
        dto.setStatus(log.getStatus());
        dto.setCreateBy(sysUser.getUsername());
        dto.setPayDate(payDate);
        dto.setCid(cId);
        return dto;
    }

    private void validStatus(CorporationDto corporationDto) throws ServiceException {
        if(CorporationStateEnum.TO_BE_PAID.getCode()!=corporationDto.getState().intValue()&&
                CorporationStateEnum.PAYMENT_AUDIT_FAILED.getCode()!=corporationDto.getState().intValue()){
            throw new ServiceException("只有企业资质为待付款状态时才能进行保证金缴纳审核");
        }
    }

}
