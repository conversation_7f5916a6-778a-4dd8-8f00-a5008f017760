package com.xyy.ec.pop.controller.mop;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.exception.XyyEcCommonBizCheckRTException;
import com.xyy.ec.pop.adapter.mop.MeSysUserAdapter;
import com.xyy.ec.pop.adapter.mop.MerchantManagerAdapter;
import com.xyy.ec.pop.adapter.mop.TargetAdapter;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.dto.mop.TargetManagerExcelDTO;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.utils.easyexcel.EasyExcelUtil;
import com.xyy.ec.pop.utils.mop.MopDataFillerUtils;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.common.condition.TargetSelectCondition;
import com.xyy.pop.mop.api.common.enumerate.TargetCheckObjectEnum;
import com.xyy.pop.mop.api.remote.parameter.TargetBatchImportParameter;
import com.xyy.pop.mop.api.remote.result.TargetBasicDTO;
import com.xyy.scm.constant.entity.pagination.Paging;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商家运营-目标管理
 */
@Slf4j
@RequestMapping("/mop/target")
@RestController
public class TargetManagementController extends BaseController{
    @Autowired
    TargetAdapter targetAdapter;
    @Autowired
    private DownloadRemote downloadRemote;
    @Autowired
    private MerchantManagerAdapter merchantManagerAdapter;
    @Autowired
    private MeSysUserAdapter meSysUserAdapter;
    /**
     * 查询
     * @param targetSelectCondition
     * @return
     */
    @PostMapping("/queryData")
    public ResponseVo<Paging<TargetBasicDTO>> queryData(@RequestBody TargetSelectCondition targetSelectCondition) {
        if (targetSelectCondition.getTargetMonth()!=null && targetSelectCondition.getTargetMonth()==0) {
            //前端传0，查全部月份的
            targetSelectCondition.setTargetMonth(null);
        }
        return  targetAdapter.queryPageTargetByParam(targetSelectCondition);
    }

    /**
     * 导入数据
     *
     * @return
     */
    @PostMapping("/importData")
    public ResponseVo importData(@RequestParam("file") MultipartFile file) {
        try {

            List<TargetManagerExcelDTO> excelDTOList = EasyExcelUtil.readExcel(file.getInputStream(), TargetManagerExcelDTO.class);
            TargetBatchImportParameter param = new TargetBatchImportParameter();
            MopDataFillerUtils.fillData(getUser(), param, false);
            return targetAdapter.batchImportTarget(param, excelDTOList);
        } catch (IOException e) {
            return ResponseVo.errRest("文件解析失败");
        }

//        try {
//            List<TargetBasicDTO> targetBasicDTOS = EasyExcelUtil.readExcel(file.getInputStream(), TargetBasicDTO.class);
//            //补上操作人 人员名店铺名 排个序 再转换枚举类
//            targetBasicDTOS = this.convertToList(targetBasicDTOS);
//            //校验
//            this.checkRule(targetBasicDTOS);
//            return targetAdapter.importExcel(targetBasicDTOS);
//        } catch (Exception e) {
//            return ResponseVo.errRest(e.getMessage());
//        }
    }
    /**
     * 修改数据
     * @return
     */
    @PostMapping("/updateData")
    public ResponseVo updateData(@RequestParam("id") Long id,@RequestParam("targetValue") BigDecimal targetValue) {
        TargetBasicDTO targetBasicDTO = new TargetBasicDTO();
        targetBasicDTO.setId(id);
        targetBasicDTO.setTargetValue(targetValue);
        targetBasicDTO.setUpdateBy(getUser().getRealName());
        return targetAdapter.update(targetBasicDTO);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/delete")
    public ResponseVo deleteTarget(@RequestParam("id") Long id) {
        TargetBasicDTO targetBasicDTO = new TargetBasicDTO();
        targetBasicDTO.setId(id);
        targetBasicDTO.setUpdateBy(getUser().getRealName());
        return targetAdapter.deleteTarget(targetBasicDTO);
    }

    /**
     * 下载导入模板
     * @return
     */
    @GetMapping("/downloadModule")
    public void exportModule(HttpServletResponse response) {
//    public ResponseEntity<byte[]> exportModule() {
        EasyExcelUtil.writeExcel(response, "目标管理导入模板", new ArrayList<>(), TargetManagerExcelDTO.class,"目标管理");

//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
//        headers.setContentDispositionFormData("attachment", "目标管理导入模板.xlsx");
//        return new ResponseEntity<>(generateTemplate(), headers, HttpStatus.OK);
    }

    /**
     * 导出列表
     *
     * @param query
     */
    @PostMapping(value = "/export")
    @ResponseBody
    public ResponseVo<Boolean> exportShippingReminders(@RequestBody TargetSelectCondition query) {
        try {
            if (Objects.nonNull(query.getTargetMonth()) && Objects.equals(0, query.getTargetMonth())) {
                // 前端传0，查全部月份的
                query.setTargetMonth(null);
            }
            log.info("exportShippingReminders:{}", JSON.toJSONString(query));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.TARGET_MANAGEMENT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("exportShippingReminders result:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        } catch (Exception e) {
            log.error("exportShippingReminders error:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }
    private List<TargetBasicDTO> convertToList(List<TargetBasicDTO> targetBasicDTOS) {
        String username = getUser().getUsername();
        for (int i = 0; i < targetBasicDTOS.size(); i++) {
            TargetBasicDTO targetBasicDTO = targetBasicDTOS.get(i);
            targetBasicDTO.setCreateBy(username);
            targetBasicDTO.setUpdateBy(username);
            targetBasicDTO.setSort(i+1);
//            if (targetBasicDTO.getObjType().equals(TargetCheckObjectEnum.SHOP.getMsg())) {
//                targetBasicDTO.setObjName(merchantManagerAdapter.queryMerchantName(targetBasicDTO.getObjCode()));
//                targetBasicDTO.setObjType(TargetCheckObjectEnum.SHOP.getCode());
//            }
//            if (targetBasicDTO.getObjType().equals(TargetCheckObjectEnum.PERSONNEL.getMsg())){
//                targetBasicDTO.setObjName(meSysUserAdapter.getUser(targetBasicDTO.getObjCode()).getRealname());
//                targetBasicDTO.setObjType(TargetCheckObjectEnum.PERSONNEL.getCode());
//            }
//            if (targetBasicDTO.getObjType().equals(TargetCheckObjectEnum.HEADQUARTER.getMsg())){
//                targetBasicDTO.setObjType(TargetCheckObjectEnum.HEADQUARTER.getCode());
//            }
        }
        return targetBasicDTOS;
    }


    private void checkRule(List<TargetBasicDTO> targetBasicDTOS) throws Exception {
        Map<Integer, List<TargetBasicDTO>> monthMap = targetBasicDTOS.stream().collect(Collectors.groupingBy(TargetBasicDTO::getTargetMonth));
        String exception="";
        Map<String, String> hashMap = new HashMap<>();
        //当月维度下作这些计算
        for (Map.Entry<Integer, List<TargetBasicDTO>> integerListEntry : monthMap.entrySet()) {
            List<TargetBasicDTO> oneMonth = integerListEntry.getValue();
            Map<String, Long> peopleMap = oneMonth.stream().filter(item -> !TargetCheckObjectEnum.PERSONNEL.getCode().equals(item.getObjType())).collect(Collectors.groupingBy(t -> t.getObjCode(), Collectors.counting()));
            Map<String, Long> storeMap = oneMonth.stream().filter(item -> !TargetCheckObjectEnum.SHOP.getCode().equals(item.getObjType())).collect(Collectors.groupingBy(t -> t.getObjCode(), Collectors.counting()));
            //总部\店铺
            // TODO 待调整
            Map<String, Long> headMap = new HashMap<>();
//            Map<String, Long> headMap = oneMonth.stream().filter(item -> !TargetCheckObjectEnum.HEADQUARTER.getCode().equals(item.getObjType().getCode())).collect(Collectors.groupingBy(t -> t.getObjType(), Collectors.counting()));

            for (TargetBasicDTO targetBasicDTO : oneMonth) {
                if (!TargetCheckObjectEnum.HEADQUARTER.getCode().equals(targetBasicDTO.getObjType())) {
                    //不是总部的全部字段不能为空 目标月份、考核对象、工号/商户编码、目标值
                    if (targetBasicDTO.getTargetMonth() == null ||
                            targetBasicDTO.getObjCode() == null ||
                            targetBasicDTO.getObjType() == null ||
                            targetBasicDTO.getTargetValue() == null) {
//                    throw new XyyEcCommonBizCheckRTException("第" +(i+1) + "行存在空数据,请检查修正后再导入");
                        convertMap(hashMap, targetBasicDTO.getSort(), "空值");
                        continue;
                    }
                    if (peopleMap.get(targetBasicDTO.getObjCode()) != null && headMap.get(targetBasicDTO.getObjCode()).intValue() > 2) {
                        convertObjCodeMap(hashMap, targetBasicDTO.getObjCode(), "人员店铺");
                    }
                    if (storeMap.get(targetBasicDTO.getObjCode()) != null && headMap.get(targetBasicDTO.getObjCode()).intValue() > 1) {
                        convertObjCodeMap(hashMap, targetBasicDTO.getObjCode(), "人员店铺");
                    }
                    if (StringUtils.isBlank(targetBasicDTO.getObjName())) {
                        convertObjCodeMap(hashMap, targetBasicDTO.getObjCode(), "名称");
                    }
                } else {//是总部
                    if (targetBasicDTO.getTargetMonth() == null ||
                            targetBasicDTO.getObjType() == null ||
                            targetBasicDTO.getTargetValue() == null) {
                        convertMap(hashMap, targetBasicDTO.getSort(), "空值");
                        continue;
                    }
                    if (headMap.get(TargetCheckObjectEnum.HEADQUARTER.getCode()) != null && headMap.get(TargetCheckObjectEnum.HEADQUARTER.getCode()).intValue() > 1) {
                        convertObjCodeMap(hashMap, TargetCheckObjectEnum.HEADQUARTER.getCode(), "人员店铺");
                    }
                }
                if (targetBasicDTO.getTargetValue().compareTo(BigDecimal.ZERO) <= 0) {
                    convertMap(hashMap, targetBasicDTO.getSort(), "负数");
//                throw new XyyEcCommonBizCheckRTException("第" +(i+1) + "行存在负数数据,请检查修正后再导入");
                }
            }
            if (StringUtils.isNotBlank(hashMap.get("空值"))){
                exception=exception+"第"+hashMap.get("空值")+"行存在空数据,请检查修正后再导入;";
            }if (StringUtils.isNotBlank(hashMap.get("负数"))){
                exception=exception+"第"+hashMap.get("负数")+"行,目标格式错误,请检查修正后再导入;";
            }if (StringUtils.isNotBlank(hashMap.get("人员店铺"))){
                exception=exception+hashMap.get("人员店铺")+"同一个月份存在多条目标,请检查修正后再导入;";
            }if (StringUtils.isNotBlank(hashMap.get("名称"))){
                exception=exception+hashMap.get("名称")+"不存在,请检查修正后再导入;";
            }
            if (exception != "") {
                throw new XyyEcCommonBizCheckRTException(exception);
            }
        }
    }

    private void convertObjCodeMap(Map<String, String> hashMap, String objCode, String key) {
        if (hashMap.get(key)!=null){
            hashMap.put(key,hashMap.get(key)+","+objCode);
        }else {
            hashMap.put(key,objCode);
        }
    }

    public void convertMap(Map hashMap,Integer i,String key){
        if (hashMap.get(key)!=null){
            hashMap.put(key,hashMap.get(key)+","+i);
        }else {
            hashMap.put(key,""+i);
        }
    }


   /* private <T> List<T> convertToList(MultipartFile file,Class<T> clazz) throws Exception {
        Workbook workbook = null;
        List<List<String>> data = new ArrayList<>();
        String createBy = getUser().getUsername();
        String updateBy = getUser().getUsername();
        try {
            workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                List<String> rowData = new ArrayList<>();
                for (int j = 0; j < headerRow.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    if (cell == null) {
                        rowData.add(null);
                    } else {
                        rowData.add(cell.getStringCellValue());
                    }
                }
                data.add(rowData);
            }
            } catch (Exception e) {
                log.info("convertToList导入失败:" + JSON.toJSONString(e));
                throw e;
            }
        List<T> returnList = new ArrayList();
        if (CollectionUtils.isEmpty(data)){
            throw new XyyEcCommonBizCheckRTException("未输入数据");
        }
        // 获取类的所有字段
        Field[] fields = clazz.getDeclaredFields();
        Integer num=1;
        for (List<String> datum : data) {
            T instance = clazz.newInstance();
            // 检查字段数量是否与 datum 一致
            if (fields.length != datum.size()) {
                throw new XyyEcCommonBizCheckRTException("输入数据超出范围,请检查!");
            }
            String tagetMonthStr = datum.get(0);//目标月份
            String objTypeStr = datum.get(1);//考核对象
            String objCodeStr = datum.get(2);//工号/商户编码
            String targetValueStr = datum.get(3);//目标值

            // 遍历字段并设置值
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                field.setAccessible(true); // 如果字段是私有的，允许访问
                // 获取字段的类型
                if (field.getName().equals("sort")) {
                    Object value = num;
                    field.set(instance, value);
                }
                if (field.getName().equals("createBy")) {
                    Class<?> fieldType = field.getType();
                    Object value = convertValue(createBy, fieldType);
                    field.set(instance, value);
                }
                if (field.getName().equals("updateBy")) {
                    Class<?> fieldType = field.getType();
                    Object value = convertValue(updateBy, fieldType);
                    field.set(instance, value);
                }
                if (field.getName().equals("targetType")) {
                    Class<?> fieldType = field.getType();
                    Object value = convertValue(TargetTypeEnum.ACTUALLY_PAID.getCode(), fieldType);
                    field.set(instance, value);
                }
                if (field.getName().equals("targetMonth")) {
                    Class<?> fieldType = field.getType();
                    Object value = convertValue(tagetMonthStr, fieldType);
                    field.set(instance, value);
                }
                if (field.getName().equals("objType")) {
                    Class<?> fieldType = field.getType();
                    Object value = convertValue(objTypeStr, fieldType);
                    field.set(instance, value);
                }
                if (field.getName().equals("objCode")) {
                    Class<?> fieldType = field.getType();
                    Object value = convertValue(objCodeStr, fieldType);
                    field.set(instance, value);
                }
                if (field.getName().equals("targetValue")) {
                    Class<?> fieldType = field.getType();
                    Object value = convertValue(targetValueStr, fieldType);
                    field.set(instance, value);
                }
                returnList.add(instance);
            }
            num++;
        }
        return returnList;
    }*/

    // 转换字符串值为字段的实际类型
    private static Object convertValue(String value, Class<?> fieldType) {
        if (fieldType == String.class) {
            return value;
        } else if (fieldType == int.class || fieldType == Integer.class) {
            return Integer.valueOf(value);
        } else if (fieldType == long.class || fieldType == Long.class) {
            return Long.valueOf(value);
        } else if (fieldType == double.class || fieldType == Double.class) {
            return Double.valueOf(value);
        } else if (fieldType == float.class || fieldType == Float.class) {
            return Float.valueOf(value);
        } else if (fieldType == boolean.class || fieldType == Boolean.class) {
            return Boolean.valueOf(value);
        } else {
            throw new IllegalArgumentException("Unsupported field type: " + fieldType);
        }
    }


    public byte[] generateTemplate() {
        // 创建 Excel 工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("模板");

        // 设置表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("目标月份");
        headerRow.createCell(1).setCellValue("考核对象");
        headerRow.createCell(2).setCellValue("工号/商户编码");
        headerRow.createCell(3).setCellValue("目标值");
        // 设置考核对象列的下拉框
        DataValidationHelper validationHelper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(new String[]{"人员", "店铺", "总部"});
        CellRangeAddressList addressList = new CellRangeAddressList(1, 1048575, 1, 1); // 设置下拉框的范围
        DataValidation validation = validationHelper.createValidation(constraint, addressList);
        validation.setSuppressDropDownArrow(false); // 显示下拉箭头
        sheet.addValidationData(validation);
        // 设置列宽
        sheet.setColumnWidth(0, 256 * 15); // 目标月份
        sheet.setColumnWidth(1, 256 * 20); // 考核对象
        sheet.setColumnWidth(2, 256 * 25); // 工号/商户编码
        sheet.setColumnWidth(3, 256 * 15); // 目标值

        // 将 Excel 工作簿转换为字节码
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            workbook.write(byteArrayOutputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return byteArrayOutputStream.toByteArray();
    }
}
