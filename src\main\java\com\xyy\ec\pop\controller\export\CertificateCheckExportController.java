package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.export.param.CertificateCheckExportParam;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/product/certificateReview")
public class CertificateCheckExportController extends BaseController {

    @Autowired
    private DownloadRemote downloadRemote;

    /**
     * 导出数据
     */
    @RequestMapping(value = "/export")
    public ResponseVo<Boolean> exportExcel(CertificateCheckExportParam queryDto) {
        try {
            log.info("CertificateCheckExportController.exportExcel#queryDto:{}", JSON.toJSONString(queryDto));
            queryDto.setEndTime(DateUtil.modifyEndTime(queryDto.getEndTime()));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.PRODUCT_CERTIFICATE_REVIEW)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("CertificateCheckExportController.exportExcel#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("CertificateCheckExportController.exportExcel#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }
}
