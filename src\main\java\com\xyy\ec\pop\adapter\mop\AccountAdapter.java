package com.xyy.ec.pop.adapter.mop;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pop.dto.mop.AccountImportDTO;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.AccountRemote;
import com.xyy.pop.mop.api.remote.MeSysUserRemote;
import com.xyy.pop.mop.api.remote.parameter.AccountAddOrUpdateParame;
import com.xyy.pop.mop.api.remote.parameter.AccountBatchImportParame;
import com.xyy.pop.mop.api.remote.parameter.AccountImportParame;
import com.xyy.pop.mop.api.remote.parameter.MeSysUserParame;
import com.xyy.pop.mop.api.remote.parameter.query.AccountQueryParame;
import com.xyy.pop.mop.api.remote.result.AccountBasicDTO;
import com.xyy.pop.mop.api.remote.result.MeSysUserDto;
import com.xyy.scm.constant.entity.pagination.Paging;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class AccountAdapter implements MopBaseAdapter {

    @Reference(version = "1.0.0")
    private AccountRemote accountRemote;

    @Reference(version = "1.0.0")
    private MeSysUserRemote meSysUserRemote;

    /**
     * 查询中台用户信息
     */
    public ResponseVo<List<MeSysUserDto>> getUserList(MeSysUserParame parame) {
        return to(() -> meSysUserRemote.getUserList(parame));
    }

    /**
     * 添加账号
     * 添加新的账号信息，包括姓名、岗位、所属部门、区域权限等
     *
     * @param accountParame 新增账号的参数对象
     * @return 返回操作结果，包括是否成功添加账号
     */
    public ResponseVo<Boolean> addAccount(AccountAddOrUpdateParame accountParame) {
        return to(() -> accountRemote.addAccount(accountParame));
    }

    /**
     * 修改账号
     * 修改已存在的账号信息，包括岗位、所属部门、区域权限等
     *
     * @param accountParame 修改账号的参数对象
     * @return 返回操作结果，包括是否成功修改账号
     */
    public ResponseVo<Boolean> updateAccount(AccountAddOrUpdateParame accountParame) {
        return to(() -> accountRemote.updateAccount(accountParame));
    }

    /**
     * 删除账号
     * 删除指定的账号信息
     *
     * @param accountId 需要删除的账号ID
     * @return 返回操作结果，包括是否成功删除账号
     */
    public ResponseVo<Boolean> deleteAccount(Long accountId) {
        return to(() -> accountRemote.deleteAccount(accountId));
    }

    /**
     * 查询账号详情
     * 根据账号ID查询账号详细信息
     *
     * @param accountId 查询的账号ID
     * @return 返回查询结果，包括账号的基本数据传输对象
     */
    public ResponseVo<AccountBasicDTO> findAccount(Long accountId) {
        return to(() -> accountRemote.findAccount(accountId));
    }

    /**
     * 分页查询账号列表
     * 根据查询条件分页查询账号信息
     *
     * @param accountQueryParame 查询条件参数对象
     * @return 返回分页查询结果，包括账号的基本数据传输对象列表
     */
    public ResponseVo<Paging<AccountBasicDTO>> pageAccountsByPaging(AccountQueryParame accountQueryParame) {
        return to(() -> accountRemote.pageAccountsByPaging(accountQueryParame));
    }

    /**
     * 批量导入账号
     * 批量导入账号信息，包括工号、岗位ID、部门ID、省份、城市等
     *
     * @param accountList 需要导入的账号信息列表
     * @return 返回操作结果，包括是否成功导入账号
     */
    public ResponseVo<Boolean> batchImportAccounts(AccountBatchImportParame importParame,List<AccountImportDTO> accountList) {
        List<AccountImportParame> accountParameList = accountList.stream().map(account -> {
            AccountImportParame accountParame = new AccountImportParame();
            accountParame.setAccountId(account.getAccountId());
            accountParame.setPositionId(account.getPositionId());
            accountParame.setDepartmentName(account.getDepartmentName());
            accountParame.setRegionPermission(account.getRegionPermission());
            accountParame.setAccountName(account.getAccountName());
            return accountParame;
        }).collect(Collectors.toList());
        importParame.setImportParameList(accountParameList);
        return to(() -> accountRemote.batchImportAccounts(importParame));
    }
}
