package com.xyy.ec.pop.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Author:xinghu.zhang
 * @Description:
 * @Date:Created in 16:21 2018/5/12
 * @Modified By:
 **/
@ApiModel("上传实体")
public class UploadVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(hidden = true)
    private Integer imgType;
    @ApiModelProperty(hidden = true)
    private String targetFileName;
    @ApiModelProperty(hidden = true)
    private Integer type;



    public Integer getImgType() {
        return imgType;
    }

    public void setImgType(Integer imgType) {
        this.imgType = imgType;
    }

    public String getTargetFileName() {
        return targetFileName;
    }

    public void setTargetFileName(String targetFileName) {
        this.targetFileName = targetFileName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
