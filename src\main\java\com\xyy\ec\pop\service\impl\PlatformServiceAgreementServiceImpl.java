package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.server.api.erpUtil.dto.PopCorporationDto;
import com.xyy.ec.pop.server.api.fdd.api.PlatformServiceAgreementApi;
import com.xyy.ec.pop.server.api.fdd.dto.TbXyyPopFddEnterpriseAgreementDTO;
import com.xyy.ec.pop.server.api.fdd.dto.TbXyyPopFddEnterpriseEmpowerDTO;
import com.xyy.ec.pop.server.api.fdd.enums.FddSingTypeEnum;
import com.xyy.ec.pop.server.api.fdd.param.FddSignaturesParam;
import com.xyy.ec.pop.service.PlatformServiceAgreementService;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.fdd.FddEnterpriseEmpowerVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;


@Service
@Slf4j
public class PlatformServiceAgreementServiceImpl implements PlatformServiceAgreementService {


    @Reference(version = "1.0.0")
    private PlatformServiceAgreementApi platformServiceAgreementApi;

    @Override
    public PageInfo<TbXyyPopFddEnterpriseAgreementDTO> platformAgreementList(FddSignaturesParam query) {
        return platformServiceAgreementApi.platformAgreementList(query);
    }

    @Override
    public TbXyyPopFddEnterpriseAgreementDTO platformAgreementById(Long id) {
        return platformServiceAgreementApi.platformAgreementById(id);
    }

    @Override
    public void platformAgreementTerminateTask(Long id,String reason, String username) {
        platformServiceAgreementApi.platformAgreementTerminateTask(id,reason, username);
    }

    @Override
    public ResponseVo platformAgreementAddAgreement(TbXyyPopFddEnterpriseAgreementDTO agreement, String userName) throws Exception {

        ResponseVo responseVo = checkAgreement(agreement);
        if(responseVo.getCode() == 1){
            throw new Exception(responseVo.getMessage());
        }

        platformServiceAgreementApi.platformAgreementAddAgreement(agreement, userName);

        return ResponseVo.successResult("新增成功");
    }

    @Override
    public ResponseVo platformAgreementSubmitAgreement(TbXyyPopFddEnterpriseAgreementDTO agreement) throws Exception {

        ResponseVo responseVo = checkAgreement(agreement);
        if(responseVo.getCode() == 1){
            throw  new Exception(responseVo.getMessage());
        }
        platformServiceAgreementApi.platformAgreementSubmitAgreement(agreement);
        return ResponseVo.successResult("修改成功");
    }

    @Override
    public String openedOrgChange(String sourceOrg, String targetOrg,String username,Integer type) throws Exception {
        //type = 1表示是开通电子签，type = 2标识是关闭电子签
        if (type == 1){
            return platformServiceAgreementApi.openedOrgChange(sourceOrg,targetOrg,username);
        }else if (type == 2){
            return platformServiceAgreementApi.closeElectricSign(sourceOrg,targetOrg,username);
        }
        return "";
    }

    @Override
    public String changeFddAdminAccount(FddEnterpriseEmpowerVo vo) throws Exception {
        TbXyyPopFddEnterpriseEmpowerDTO dto = new TbXyyPopFddEnterpriseEmpowerDTO();
        dto.setAdminPhone(vo.getAccount());
        dto.setOrgId(vo.getOrgId());
        return platformServiceAgreementApi.changeFddAdminAccount(dto);
    }

    private ResponseVo checkAgreement(TbXyyPopFddEnterpriseAgreementDTO agreement) {
        String taskId = agreement.getTaskId();
        String taskTopic = agreement.getTaskTopic();
        Date effectTime = agreement.getEffectTime();
        Date failureTime = agreement.getFailureTime();
        String agreementUrl = agreement.getAgreementUrl();
        Integer signType = agreement.getSignType();

        if(signType.equals(FddSingTypeEnum.PAPER_CONTRACTS.getCode()) && (StringUtils.isEmpty(taskId) || taskId.length() > 30)){
            return ResponseVo.errRest(ResponseVo.CODE_ERROR, "任务编号不能为空且长度不能超过30个字");
        }

        if(StringUtils.isEmpty(taskTopic) || taskTopic.length() > 200){
            return ResponseVo.errRest(ResponseVo.CODE_ERROR,"签署任务主题不能为空且长度不能超过100个字");
        }

        if(signType.equals(FddSingTypeEnum.PAPER_CONTRACTS.getCode())){
            if(effectTime == null){
                return ResponseVo.errRest(ResponseVo.CODE_ERROR,"生效日期不能为空");
            }else {
                agreement.setEffectTime(DateUtil.modifyStartTime(effectTime));
            }

            if(failureTime == null){
                return ResponseVo.errRest(ResponseVo.CODE_ERROR,"失效日期不能为空");
            }else {
                agreement.setFailureTime(DateUtil.modifyEndTime(failureTime));
            }

            if(failureTime.compareTo(effectTime) < 0){
                return ResponseVo.errRest(ResponseVo.CODE_ERROR,"生效日期不能大于失效日期");
            }

            if(StringUtils.isEmpty(agreementUrl)){
                return ResponseVo.errRest(ResponseVo.CODE_ERROR,"合同附件地址不能为空");
            }
            agreement.setSignTime(new Date());
        }

        return ResponseVo.successResult(ResponseVo.CODE_SUCCESS);
    }
}
