package com.xyy.ec.pop.utils;

import org.apache.commons.lang.math.RandomUtils;

import java.util.Random;

public class RandomUtil {

    private RandomUtil() {
    }


    public static int getNum(int start, int end) {
        return (int)(Math.random() * (double)(end - start + 1) + (double)start);
    }


    public static String getAuthCode(int length) {
        Random random = RandomUtils.JVM_RANDOM;
        StringBuffer sb = new StringBuffer();

        for(int i = 0; i < length; ++i) {
            int number = random.nextInt(54) - 1;
            if (number <= 0) {
                number = getNum(1, 53);
            }

            sb.append("abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ23456789".charAt(number));
        }

        return sb.toString();
    }


}
