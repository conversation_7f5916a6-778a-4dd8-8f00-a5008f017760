package com.xyy.ec.pop.report.Vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

@Data
public class MerchantReportBatchAuditErrorVo implements Serializable {
    private static final long serialVersionUID = 415193742121531858L;
    @Excel(name = "药店编码")
    private String merchantIdStr;
    @Excel(name = "审核结果")
    private String auditResult;
    @Excel(name = "原因")
    private String resultReason;
    @Excel(name = "失败原因", width = 30)
    protected String errorMsg;
}
