package com.xyy.ec.pop.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author:xinghu.zhang
 * @Description:
 * @Date:Created in 11:50 2018/5/10
 * @Modified By:
 **/
@ApiModel("商品信息实体")
@Data
public class ProductListVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;
    /**
     * 区域编码
     */
    @ApiModelProperty("主键区域编码id")
    private String branchCode;
    /**
     * 条码
     */
    @ApiModelProperty("条码")
    private String code;
    /**
     * 商品编号
     */
    @ApiModelProperty("商品编号")
    private String barcode;
    /**
     * ec商品id
     */
    private Long csuid;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productName;
    /**
     * 通用名称
     */
    @ApiModelProperty("通用名称")
    private String commonName;
    /**
     *展示名称
     */
    @ApiModelProperty("展示名称")
    private String showName;
    /**
     *图片
     */
    @ApiModelProperty("图片")
    private String imageUrl;
    /**
     * 规格
     */
    @ApiModelProperty("规格")
    private String spec;
    /**
     * 生产厂家
     */
    @ApiModelProperty("生产厂家")
    private String manufacturer;
    /**
     * 单体采购价
     */
    @ApiModelProperty("单体采购价")
    private String fob;
    /**
     * 连锁采购价
     */
    private String chainPrice;
    /**
     * 商家
     */
    @ApiModelProperty("商家")
    private String companyName;

    /**
     * 商家
     */
    @ApiModelProperty("店铺")
    private String name;
    /**
     *状态
     */
    @ApiModelProperty("状态")
    private String status;
    /**
     * 商品逻辑状态（商品列表）：1-销售中，2-缺货下架 3-下架，6-待上架，8-待审核，9-审核未通过，20-删除
     */
    private Integer logicStatus;
    /**
     * 药品类型
     */
    @ApiModelProperty("药品类型")
    private String drugClassification;
    /**
     * 总库存
     */
    @ApiModelProperty("总库存")
    private String totalAvailableQty;
    /**
     * 库存
     */
    @ApiModelProperty("库存")
    private String availableQty;
    /**
     *库存类型
     */
    @ApiModelProperty("库存类型")
    private String availableQtyType;
    /**
     * 是否限购
     */
    @ApiModelProperty("是否限购")
    private String limitedQty;
    /**
     * 是否促销
     */
    @ApiModelProperty("是否促销")
    private String isPromotion;
    /**
     * 是否底价
     */
    @ApiModelProperty("是否底价")
    private String isBasePrice;
    /**
     *商品等级分类
     */
    @ApiModelProperty("商品等级分类")
    private String grade;
    @ApiModelProperty("一级分类id")
    private String categoryFirstId;
    @ApiModelProperty("二级分类id")
    private String categorySecondId;
    @ApiModelProperty("三级分类id")
    private String categoryThirdId;
    @ApiModelProperty("四级分类id")
    private String categoryFourthId;
    @ApiModelProperty("市场价")
    private String marketPrice;
    @ApiModelProperty("建议价")
    private String suggestPrice;
    @ApiModelProperty("特殊字符串")
    private String characteristicStr;
    @ApiModelProperty("助记码")
    private String zjm;

    /**
     * 来源 1.商家自建 2.商品库
     */
    @ApiModelProperty("来源")
    private Byte source;

    @ApiModelProperty("pop商品扩展")
    private SkuPopExtendOld skuPopExtend;
    @ApiModelProperty("pop商品扩展集合")
    private List<SkuPopExtend> skuPopExtendNewList;

    /** 是否限购（0：否；1：是） */
    @JsonIgnore
    private Integer isLimited;


    /** 商品id集合 */
    @ApiModelProperty("商品id集合")
    private List<Long> skuIdList;

    /** 商品barcode集合 */
    @ApiModelProperty("商品barcode集合")
    private List<Long> barcodeList;


    /**注意事项 */
    @ApiModelProperty("注意事项")
    private String considerations;

    /** 药品相互作用 */
    @ApiModelProperty("药品相互作用")
    private String interaction;

    /** 成分 */
    @ApiModelProperty("成分")
    private String component;

    /** 存储条件 */
    @ApiModelProperty("存储条件")
    private String storageCondition;

    /** 适应症/功能主治 */
    @ApiModelProperty("适应症/功能主治")
    private String indication;

    /** 不良反应 */
    @ApiModelProperty("不良反应")
    private String untowardEffect;

    /** 用法与用量 */
    @ApiModelProperty("用法与用量")
    private String usageAndDosage;

    /** 禁忌 */
    @ApiModelProperty("禁忌")
    private String abstain;

    /** 商品分类名 */
    @ApiModelProperty("商品分类名")
    private String categoryName;

    /** 商品关联分类集合 */
    @ApiModelProperty("商品关联分类集合")
    private List<SkuCategoryRelation> skuCategoryRelations;

    /** 查询商品过滤条件 若为1:过滤掉下架商品 */
    @ApiModelProperty("查询商品过滤条件")
    private Integer searchType;

    /** 商品排序字段 */
    @ApiModelProperty("商品排序字段")
    private Integer sort;

    // 是否首字母查询
    @JsonIgnore
    private Boolean isLettersQuery;

    //一级分类名称
    @ApiModelProperty("一级分类名称")
    private String categoryFirstName;

    //二级分类名称
    @ApiModelProperty("二级分类名称")
    private String categorySecondName;

    //三级分类名称
    @ApiModelProperty("三级分类名称")
    private String categoryThirdName;
    //三级分类名称
    @ApiModelProperty("三级分类名称")
    private String categoryFourthName;

    //零售价(1:建议零售价；2:控销零售价)
    @ApiModelProperty("零售价")
    private Integer price;


    /*****************添加商品扩展表属性 begin************************/
    /** 展示价 */
    @ApiModelProperty("展示价")
    private BigDecimal displayPrice;
    /** 排序 */
    @ApiModelProperty("排序")
    private Integer sortLevel;
    /**是否控价**/
    @ApiModelProperty("是否控价")
    private Integer isPriceControl;

    /**安全库存**/
    @ApiModelProperty("安全库存")
    private Integer safeQty;

    /** 商品状态名称 */
    @ApiModelProperty("商品状态名称")
    private String statusName;

    /** 处方类型名字 */
    @ApiModelProperty("处方类型名字")
    private String drugClassificationName;

    /** 库存类型名字 */
    @ApiModelProperty("库存类型名字")
    private String availableQtyTypeName;

    /** 是否拆零名称 */
    @ApiModelProperty("是否拆零名称")
    private String splitName;

    /** 是否代理名称标识 */
    @ApiModelProperty("是否代理名称标识")
    private String agentName;


    // 促销Tag
    @ApiModelProperty("促销Tag")
    private String promotionTag;

    // 黑色背景文本描述(类似于:不参与返点)
    @ApiModelProperty("黑色背景文本描述")
    private String blackBGTextDes;

    //域名称
    @ApiModelProperty("域名称")
    private String branchName;

    //仅看有货(1:有货)（用于APP端筛选有货商品）
    @JsonIgnore
    private Integer hasStock;

    //价格区间-最低价（用于APP端筛选价格）
    @ApiModelProperty("价格区间-最低价")
    private Double minPrice;

    //价格区间-最高价（用于APP端筛选价格）
    @ApiModelProperty("价格区间-最高价")
    private Double maxPrice;

    //存储所有商品图片集合（包括主图和附图）
    @ApiModelProperty("存储所有商品图片集合")
    private List<String> imagesList;
    @ApiModelProperty("图片列表")
    private String imagesListStr;
    @ApiModelProperty("商品适应症/功能主治图片")
    private String skuInstructionImageListStr;

    //秒杀结束时间字符串
    @ApiModelProperty("秒杀结束时间字符串")
    private String seckillEndTimeStr;
    @ApiModelProperty("有展示价格")
    private Integer havingDisplayPrice;
    @ApiModelProperty("是否价格控销")
    private String isPriceControlValue;

    /** 是否底价标识字符串 */
    @ApiModelProperty("是否底价标识字符串")
    private String isBasePriceStr;

    /** 控销区域字符串 以逗号隔开 */
    @ApiModelProperty("控销区域字符串")
    private String controlAreaStr;

    /** 商品等级分类名称 */
    @ApiModelProperty("商品等级分类名称")
    private String gradeName;

    //是否活动中黑名单商品文案
    @ApiModelProperty("是否活动中黑名单商品文案")
    private String blackProductText;

    // 购物车商品数量
    @ApiModelProperty("购物车商品数量")
    private Integer cartProductNum = 0;

    // 是否收藏
    @ApiModelProperty("是否收藏")
    private Integer isCollected;

    /** 收藏：失效时间*/
    @ApiModelProperty("收藏：失效时间")
    private Date expireDate;

    /** 收藏：收藏类型*/
    @ApiModelProperty("收藏：收藏类型")
    private Integer businessType;

    /** 是否可收藏 */
    @ApiModelProperty("是否可收藏")
    private Integer favoriteFlag;

    /** 收藏订阅是否过期 */
    @ApiModelProperty("收藏订阅是否过期")
    private Integer isExpire;

    /** 收藏：期望价*/
    @ApiModelProperty("收藏：期望价")
    private Double expectPrice;

    /** 收藏：关注价*/
    @ApiModelProperty("收藏：关注价")
    private Double currentPrice;

    /** 关注差价 */
    @ApiModelProperty("关注差价")
    private Double favoriterDiffPrice;

    /** 关注差价文案 */
    @ApiModelProperty("关注差价文案")
    private String favoriterDiffPriceTag;

    //对我显示价格区间还是控销价
    @ApiModelProperty("对我显示价格区间还是控销价")
    private Integer isControlPriceToMe;

    //厂家数组
    @ApiModelProperty("厂家数组")
    private String [] manufacturers;

    //药品类型数字字符串 如：1，2，3
    @ApiModelProperty("药品类型数字字符串")
    private String drugClassificationStr;

    //药品类型数字数组
    @ApiModelProperty("药品类型数字数组")
    private int[] drugClassifications;

    /** '药品分类图标 */
    @ApiModelProperty("药品分类图标")
    private String drugClassificationImage;

    /** 商品详情分享URL */
    @ApiModelProperty("商品详情分享URL")
    private String detailUrl;

    // 总销量
    @ApiModelProperty("总销量")
    private Integer payAmount;

    /** 最近售价 */
    @ApiModelProperty("最近售价")
    private BigDecimal latePrice;

    //是否控销商品（0-否，1-是）
    @ApiModelProperty("是否控销商品")
    private Integer isControl;

    /** 秒杀商品id */
    @ApiModelProperty("秒杀商品id")
    private Long seckillGroupId;

    /** 是否促销状态名称 */
    @ApiModelProperty("是否促销状态名称")
    private String promotionStatusName;

    /** 需要根据连锁订单序号进行排序 0:不按  1:按 */
    @ApiModelProperty("需要根据连锁订单序号进行排序")
    private Integer needExtendSortLevelDirection;

    /** 展示名称数组 */
    @ApiModelProperty("展示名称数组")
    private String[] showNameArr;

    /** 查询不包含此字段的commonName*/
    @ApiModelProperty("查询不包含此字段的commonName")
    private String notLikeCommonName;

    /** 商品是默认图片 */
    @ApiModelProperty("商品是默认图片")
    private Integer isDefaultPhoto;

    //商品关联分类名称
    @ApiModelProperty("商品关联分类名称")
    private String skuCategoryName;

    //是否易碎品
    @ApiModelProperty("是否易碎品")
    private String isFragileGoodsName;

    //提交时间
    @ApiModelProperty("提交时间")
    private Date createTime;

    /**
     * 商家id
     */
    private Integer supplierId;
    private String standardProductId;
    /**
     * 机构id
     */
    private String orgId;
    /**
     * 商品erp编码
     */
    private String erpCode;
    /**
     * 商品类别(II类.8520)
     */
    private String skuCategory;
    /**
     * 批准文号
     */
    private String approvalNumber;
    /**
     * 有效期限
     */
    private String term;

    /**
     * 商圈名称
     */
    private String busAreaName;

    /**
     * 中台审核原因:0，历史记录， 1已上报等待结果、 2审核通过，3、审核不通过，强制下架 4.其他审核不通过原因
     */
    private Integer authReason;

    /**
     *中台审核原因-描述
     */
    private String authReasonName;
    /**
     * 是否已上报中台 0 否 1 是
     */
    private Boolean reportToStandard;

    /**
     * 省ID
     */
    private Long provId;

    /**
     * 省名称
     */
    private String prov;
    /**
     * 活动名
     */
    private String activeName;

    /**
     * 商品来源 0：普通，1：拼团品
     */
    private Integer activityType;
    /**
     * 商品原来编码
     */
    private String originalBarcode;
    /**
     * 原商品csuid
     */
    private Long originalCsuid;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 活动状态：1-待生效 2-生效中 3-已结束
     */
    private Integer activityStatus;
    /**
     * 活动预热时间
     */
    private Date activityPreheatTime;
    /**
     * 活动开始时间
     */
    private Date activityStartTime;
    /**
     * 活动结束时间
     */
    private Date activityEndTime;
    /**
     * 中台图片
     */
    private String standardImageUrl;
    /**
     * 使用标品图片
     */
    private Boolean useStandardImage;
    /**
     * 标品库图片版本
     */
    private String standardImageVersion;
    /**
     * 拼团价格
     */
    private BigDecimal groupPrice;
    /**
     * 活动总限购库存
     */
    private Integer totalLimitQty;
    /**
     * 停用类型：0-默认；1、公司规定停用；2、药监要求停用；3、数据错误；4、商品退市；5、其它；停用说明(自定义文本)
     */
    @ApiModelProperty("停用类型")
    private Integer disableType;

    private List<PopSkuAreaPriceVo> skuAreaPriceVos;
    private Integer haveAreaPrice;



    public Long getProvId() {
        return provId;
    }

    public void setProvId(Long provId) {
        this.provId = provId;
    }


    /**
     * 停用类型：0-默认值；1、公司规定停用；2、药监要求停用；3、数据错误；4、商品退市；5、其它；停用说明(自定义文本)
     */
    @ApiModelProperty("停用类型名称")
    private String disableTypeName;

    /**
     * 停用说明
     */
    @ApiModelProperty("停用说明")
    private String disableNote;

    /**
     * shopCode
     */
    private String shopCode;

    /**
     * 是否有追溯码: 0-否,1-是
     */
    private Integer tracingCode;
}
