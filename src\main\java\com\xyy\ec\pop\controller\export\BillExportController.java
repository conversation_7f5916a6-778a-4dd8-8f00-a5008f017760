package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.export.param.BillExportAdminParam;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/07/28
 */
@Slf4j
@RequestMapping("/billExport/async")
@RestController
public class BillExportController extends BaseController {
    @Autowired
    private DownloadRemote downloadRemote;

    /**
     * 导出账单数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportBill")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportBill(BillExportAdminParam queryDto) {
        log.info("BillExportController.exportBill#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if (CollectionUtils.isEmpty(provIds)) {
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(provIds);
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.BILL)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("BillExportController.exportBill#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillExportController.exportBill#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出账单明细数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportBillDetail")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportBillDetail(BillExportAdminParam queryDto) {
        log.info("BillExportController.exportBillDetail#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if (CollectionUtils.isEmpty(provIds)) {
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(provIds);
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.BILL_DETAIL)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("BillExportController.exportBillDetail#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillExportController.exportBillDetail#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出账单商品明细数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportBillProductDetail")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportBillProductDetail(BillExportAdminParam queryDto) {
        log.info("BillExportController.exportBillProductDetail#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if (CollectionUtils.isEmpty(provIds)) {
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(provIds);
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.BILL_PRODUCT_DETAIL)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("BillExportController.exportBillProductDetail#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e) {
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillExportController.exportBillProductDetail#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }


}
