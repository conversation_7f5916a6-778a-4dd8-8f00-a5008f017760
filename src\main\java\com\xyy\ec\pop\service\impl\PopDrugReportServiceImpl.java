package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.server.api.product.api.PopDrugReportApi;
import com.xyy.ec.pop.server.api.product.dto.*;
import com.xyy.ec.pop.service.PopDrugReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class PopDrugReportServiceImpl implements PopDrugReportService {


    @Reference(version = "1.0.0")
    private PopDrugReportApi popDrugReportApi;

    @Override
    public PageInfo<PopDrugReportResDto> drugReportQuery(PopDrugReportDto dto) {
        return popDrugReportApi.drugReportQuery(dto);
    }

    @Override
    public String downZipUrl(String barCode, String batchCode,String sealStatus) throws Exception {
        return popDrugReportApi.downZipUrl(barCode, batchCode,sealStatus);
    }
}
