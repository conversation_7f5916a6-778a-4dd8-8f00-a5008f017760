package com.xyy.ec.pop.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version v1
 * @Description 商品批量更新配置
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(value = "product.update.batch")
@Data
public class ProductBatchUpdateConfig {
    private int maxFileSize = 1;
    private int maxRows = 1000;
    public final static Map<String, Integer> statusMap = new HashMap<>();
    public final static Map<String, Integer> drugsMap = new HashMap<>();

    static {
        statusMap.put("不修改", null);
        //暂时不让质管直接上架商品，上架要校验信息比较多
//        statusMap.put("销售中", 1);
        statusMap.put("下架", 4);
        statusMap.put("待上架", 6);
        statusMap.put("待审核", 8);
        statusMap.put("审核未通过", 9);
        drugsMap.put("不修改", null);
        drugsMap.put("无", 1);
        drugsMap.put("甲类OTC", 1);
        drugsMap.put("乙类OTC", 2);
        drugsMap.put("处方药Rx", 3);
    }
    List<String> updateTitles = Arrays.asList("*商品编码","通用名称","商品名称","展示名称","erp编码","状态");

    List<String> updateErpSkuTitles = Arrays.asList("*机构编码","*商品ERP编码","商品条码","通用名称","商品名称","批准文号","生产厂家","规格");
    public final static List<String> DOSAGE_FORM_VALUE = Arrays.asList("不修改","无","露剂","栓剂","散剂","软膏剂","溶液剂","气雾剂","片剂","糖浆剂"
            ,"贴剂","丸剂","合剂","干混悬剂","注射剂","硬膏剂","眼膏剂","橡胶膏剂","洗剂","吸入剂","喷雾剂","凝胶剂","滴眼剂","滴丸剂","滴剂"
            ,"滴耳剂","滴鼻剂","大容量注射剂","搽剂","酊剂","锭剂","混悬剂","膜剂","混悬剂","膜剂","流浸膏剂","口服液","颗粒剂","酒剂"
            ,"胶囊剂","胶剂","煎膏剂","膏药","其他");
}
