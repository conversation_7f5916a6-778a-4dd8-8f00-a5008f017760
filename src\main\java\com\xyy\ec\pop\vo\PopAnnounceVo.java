package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: Jincheng.Li
 * @Date: 2021/07/14/14:37
 * @Description:
 */
@Data
public class PopAnnounceVo implements Serializable {
    private Integer id;

    /**
     * 公告标题
     */
    private String title;

    /**
     * 公告图片
     */
    private String url;

    /**
     * 0:停用，1:启用
     */
    private Byte status;

    private String statusName;

    /**
     * 图片排序
     */
    private Integer sort;

    /**
     * 公告内容
     */
    private String content;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否在首页展示 0否 1是
     */
    private Integer isShowBanner;

    /**
     * 店铺公告类型
     *  AnnounceTypeEnum
     */
    private Integer announceType;

    /**
     * 标题是否红色战士 0否 1是
     */
    private Integer redTitle;

    private String announceTypeDesc;

    /**
     * 适用店铺类型
     */
    private Integer applicableType;

    private String applicableTypeName;
    /**
     * 适用店铺具体值
     */
    private String applicableCode;

    /**
     * 是否弹框展示 0否 1是
     */
    private Integer showDialog;


    /**
     * 是否弹框展示 0否 1是
     */
    private String showDialogStr;

    /**
     * 弹框展示时间
     */
    private Integer showTime;
}
