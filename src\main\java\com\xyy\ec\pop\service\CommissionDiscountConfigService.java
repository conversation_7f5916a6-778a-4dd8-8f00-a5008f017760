//package com.xyy.ec.pop.service;
//
//import com.xyy.ec.pop.vo.CommissionDiscountSetVo;
//
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @Auther: lijincheng
// * @Date: 2021/09/13/15:10
// * @Description:
// */
//public interface CommissionDiscountConfigService {
//    /**
//     * 保存佣金折扣配置
//     * @param orgId
//     * @param userName
//     * @param vos
//     * @return
//     */
//    Boolean saveCommissionDiscountConfig(String orgId, String userName, List<CommissionDiscountSetVo> vos, Integer flag);
//
//    /**
//     * 查看佣金折扣配置
//     * @param orgId
//     * @return
//     */
//    List<CommissionDiscountSetVo> getCommissionDiscountConfigByOrgId(String orgId);
//}
