$(document).ready(function () {

    $(function () {
        $(".tooltip-options a").tooltip({html: true});
    });
    //1.初始化项目大类列表
    new applyPendingList.tableInit().Init();
    //2.初始化操作按钮
    new applyPendingList.buttonInit().Init();

    $('#createBeginTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var searchStartTime = $("#createBeginTime").val();
        $("#createEndTime").datetimepicker('setStartDate', searchStartTime);
    });

    $('#createEndTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var billEndTime = $("#createEndTime").val();
        $("#createBeginTime").datetimepicker('setEndDate', billEndTime);
    });

	/* 导出列表 */
	$('#btn_export').on('click', function (e) {
		window.location.href = basePath + 'accountPeriod/downloadExcel?offset=1';
	});
});

var applyPendingList = $.applyPendingList = ({
    //项目大类列表初始化
    tableInit: function () {
        var applyPendingTableObj = new Object();
        //初始化Table
        applyPendingTableObj.Init = function () {
            var $table = $('#tb_pendingList');
            $table.bootstrapTable({
                // url: '/accountStatement/detailList', //请求后台的URL（*）
                method: 'get', //请求方式（*）
                contentType: 'application/x-www-form-urlencoded',
                dataType: 'json', //传入的类型
                toolbar: '#toolbar', //工具按钮用哪个容器
                striped: true, //是否显示行间隔色
                cache: false, //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                pagination: true, //是否显示分页（*）
                sortable: false, //是否启用排序
                sortOrder: "asc", //排序方式
                queryParams: applyPendingTableObj.queryParams, //传递参数（*）
                sidePagination: "server", //分页方式：client客户端分页，server服务端分页（*）
                formatLoadingMessage: function () {
                    return '请稍后,正在加载中...';
                },
                showFooter: false,
                pageNumber: 1, //初始化加载第一页，默认第一页
                pageSize: 20, //每页的记录行数（*）
                pageList: [10, 20, 50, 100], //可供选择的每页的行数（*）
                strictSearch: true,
                onLoadSuccess: function (data) {
                },
                clickToSelect: true, //是否启用点击选中行
                //height: 550, //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                onClickRow: function (row, $element) {
                    $('.success').removeClass('success');
                    $($element).addClass('success');

                   /* var status = row.auditingState;``
                    if(status == 1){
                        $("#btn_auditing").show();
                    }else{
                        $("#btn_auditing").hide();
                    }*/
                },
                uniqueId: "id", //每一行的唯一标识，一般为主键列
                columns: [{
                    field: 'orderNo',
                    title: '订单号',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'totalAmount',
                    title: '订单总额',
                    align: 'center',
                    sortable: true,
                    formatter: function (value,row,index) {
                        var totalAmount = row.totalAmount;
                        var freightAmount = row.freightAmount;
                        if(!freightAmount){
                            freightAmount = 0;
                        }
                        return  totalAmount + freightAmount;
                    }
                }, {
                    field: 'money',
                    title: '实际支付',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'discount',
                    title: '店铺券优惠',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'ptDiscount',
                    title: '跨店券优惠',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'freightAmount',
                    title: '运费',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'refuntTotalMoney',
                    title: '退款金额',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'hireMoney',
                    title: '佣金总额',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'fTotalMoney',
                    title: '处罚金额',
                    align: 'center',
                    sortable: true
                },{
                    field: 'yTotalMoney',
                    title: '应结算',
                    align: 'center',
                    sortable: true,
                    formatter: function (value,row,index) {
                        var totalAmount = row.totalAmount;
                        var refuntTotalMoney = row.refuntTotalMoney;
                        if(!refuntTotalMoney){
                            refuntTotalMoney = 0;
                        }
                        var freightAmount = row.freightAmount;
                        if(!freightAmount){
                            freightAmount = 0;
                        }
                        return  totalAmount - refuntTotalMoney+freightAmount;
                    }
                },{
                    field: 'sTotalMoney',
                    title: '实结算',
                    align: 'center',
                    sortable: true,
                    formatter: function (value,row,index) {
                        var totalAmount = row.totalAmount;
                        var refuntTotalMoney = row.refuntTotalMoney;
                        if(!refuntTotalMoney){
                            refuntTotalMoney = 0;
                        }
                        var freightAmount = row.freightAmount;
                        if(!freightAmount){
                            freightAmount = 0;
                        }
                        return  totalAmount - refuntTotalMoney + freightAmount;
                    }
                },{
                    field: 'createTime',
                    title: '下单时间',
                    align: 'center',
                    sortable: true,
                    formatter:function (val, row) {
                        if (val != null && val != "") {
                            var time = ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
                            if (time == '1970-01-01 00:00:00') {
                                time = ''
                            }
                            return time;
                        } else {
                            return "";
                        }
                    }
                },{
                    field: 'finishTime',
                    title: '收货时间',
                    align: 'center',
                    sortable: true,
                    formatter:function (val, row) {
                        if (val != null && val != "") {
                            var time = ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
                            if (time == '1970-01-01 00:00:00') {
                                time = ''
                            }
                            return time;
                        } else {
                            return "";
                        }
                    }
                }/*, {
                    field: 'auditingState',
                    title: '备注',
                    align: 'center',
                    sortable: true
                }*/]
            });
        };
        //查询的参数
        applyPendingTableObj.queryParams = function (params) {
            return {
                //每页显示条数
                limit: params.limit,
                //起始页数
                offset: params.offset,
                //排序字段
                property: (params.sort && params.sort !== '') ? ToolUtil.underscoreName(params.sort) : '',
                //排序方式
                direction: params.order,
                //查询条件（将对象属性封装成查询实体对象）
                id:$("#accountId").val()
            };
        };
        return applyPendingTableObj;
    },
    buttonInit: function () {
        var $table = $('#tb_pendingList');
        var oInit = new Object();
        oInit.Init = function () {

            function getSelectedRow() {
                var index = $table.find('tr.success').data('index');
                return $table.bootstrapTable('getData')[index];
            }

            //初始化页面上面的按钮事件

            //审核
            $("#btn_auditing").click(function () {
                var id = $("#accountId").val();
                LayTool.open({
                    title: "审核",
                    area: ['500px', '400px'],
                    content: [
                        basePath + 'accountStatement/auditingPage?id='+id, 'yes'
                    ],
                    end: function () {
                        location.reload();
                    }
                });
                // if(row){
                //     var id = row.id;
                //     window.location.href= "/accountStatement/detail?id="+id;
                // }else {
                //     LayTool.alert("请选择一条记录");
                // }
            });

            //查看日志
            $("#btn_log").click(function () {
                var row = getSelectedRow();
                if (row) {
                    var id = row.id;

                    LayTool.open({
                        title: "查看日志",
                        area: ['500px', '400px'],
                        content: [
                            basePath + 'product/skuUpdateLog?skuId=' + id, 'yes'
                        ],
                        end: function () {
                            location.reload();
                        }
                    });
                }else {
                    LayTool.alert("请选择一条记录");
                }
            });

        };
        return oInit;
    }
});


function datetimeFormatter(val) {
    if (val != null) {
        return ToolUtil.dateFormat(val, 'yyyy-MM-dd');
    }
}

function getNowFormatDate() {
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    return currentdate;
}