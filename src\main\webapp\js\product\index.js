var isInited=false;
$(document).ready(function () {

    $(function () {
        $(".tooltip-options a").tooltip({html: true});
    });
    //1.初始化项目大类列表
    getQuery()

    //2.初始化操作按钮
    new applyPendingList.buttonInit().Init();

    initSearchTime();

    /* 导出列表 */
    $('#btn_export').on('click', function (e) {
        var ps = [];
        var pms = queryParams();
        for (var k in pms) {	//组装参数
            var pm = pms[k]
            if (pm)
                ps.push(k + '=' + pm);
        }
        var s = this;
        $.ajax({
            type: "GET",
            url: "/productExport/async/downloadExcel",
            data: queryParams(),
            success:function (res) {
                if(res.code == 0){
                    var p = LayTool.confirm("文件生成中，请到文件下载中心页面进行查看和下载", {icon: 3, title: '提示',btn: ['文件下载中心', '取消']}
                        , function (index) {
                            $(s).addtab("文件下载中心", "/webPop/#/downloadList", 1);
                            LayTool.close(p);
                        });
                } else {
                    layer.msg(data.errorMsg);
                }
            }
        })
        //window.location.href = basePath + 'product/downloadExcel?' + encodeURI(ps.join('&'));
    });

    isShowBtn();

});

var btnArr = [];
$.ajax({
    type: "GET",
    async: false,
    url: "/getButtons",
    data: "menuUrl=/product/index",
    success:function (res) {
        if(res.code == 0 && res.data != null){
            btnArr = res.data;
        }
    }
});

var _local = localStorage;
var applyPendingList = $.applyPendingList = ({
    //项目大类列表初始化
    tableInit: function () {
        var applyPendingTableObj = new Object();
        //初始化Table
        applyPendingTableObj.Init = function () {
            var $table = $('#tb_pendingList');
            $table.bootstrapTable({
                url: '/product/list', //请求后台的URL（*）
                method: 'get', //请求方式（*）
                contentType: 'application/x-www-form-urlencoded',
                dataType: 'json', //传入的类型
                toolbar: '#toolbar', //工具按钮用哪个容器
                striped: true, //是否显示行间隔色
                cache: false, //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                pagination: true, //是否显示分页（*）
                sortable: true, //是否启用排序
                sortOrder: "asc", //排序方式
                queryParams: applyPendingTableObj.queryParams, //传递参数（*）
                sidePagination: "server", //分页方式：client客户端分页，server服务端分页（*）
                formatLoadingMessage: function () {
                    return '请稍后,正在加载中...';
                },
                showFooter: false,
                pageNumber: parseInt(localStorage.getItem('pageLocalNum') ? localStorage.getItem('pageLocalNum') : 1), //初始化加载第一页，默认第一页
                onPageChange: function (page){
                    setPageLocal(page)
                },
                pageSize: 20, //每页的记录行数（*）
                pageList: [10, 20, 50, 100], //可供选择的每页的行数（*）
                strictSearch: true,
                onLoadSuccess: function (data) {
                },
                onDblClickRow:function(row, $element){
                    if (row.activityType == 0) {
                        window.location.href= "/product/detail?barcode="+row.barcode+"&orgId="+row.orgId+"&isAuditing="+0;
                    }
                },
                clickToSelect: true, //是否启用点击选中行
                //height: 550, //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                onClickRow: function (row, $element) {
                    $('.success').removeClass('success');
                    $($element).addClass('success');

                    var status = row.status;
                    var activityType = row.activityType;
                    if ((status == 7 || status == 8) && activityType == 0) {
                        $("#btn_auditing").show();
                    }else{
                        $("#btn_auditing").hide();
                    }
                    //拼团品隐藏审核、日志和预览按钮
                    console.log(activityType, "activityType")
                    if (activityType == 0) {
                        $("#btn_log").show();
                        $("#btn_yuLan").show();
                        $("#btn_batchUpdateErp").show();
                    } else if (activityType == 3) {
                        $("#btn_log").show();
                        $("#btn_batchUpdateErp").hide();
                        $("#btn_yuLan").hide();
                    } else {
                        $("#btn_log").hide();
                        $("#btn_yuLan").hide();
                        $("#btn_batchUpdateErp").show();
                    }
                },
                uniqueId: "id", //每一行的唯一标识，一般为主键列
                columns: [{
                    field: 'companyName',
                    title: '商户名称',
                    align: 'center',
                },{
                    field: 'name',
                    title: '店铺名称',
                    align: 'center',
                },{
                    field: 'prov',
                    title: '商户注册省份',
                    align: 'center',
                }, {
                    field: 'barcode',
                    title: '商品编码',
                    align: 'center',
                    formatter:function(val,row){
                        if(row.activityType == 0){
                            return val;
                        }
                        return "<div>"+val+"</div><div style='color: #00ff00;'>原商品："+row.originalBarcode+"</div>"
                    }
                }, {
                    field: 'csuid',
                    title: 'sku编码',
                    align: 'center',
                }, {
                    field: 'showName',
                    title: '商品名称',
                    align: 'center',
                    formatter:function(val,row){
                        if(!row.activeName){
                            return val;
                        }
                        return "<div>"+val+"</div><div style='color: #00ff00;'>"+row.activeName+"</div>"
                    }
                }, {
                    field: 'tracingCode',
                    title: '是否有追溯码',
                    align: 'center',
                    formatter: function (val) {
                        switch (val) {
                            case 0:
                                return '否';
                                break;
                            case 1:
                                return '是';
                                break;
                            default:
                                return '-';
                                break;
                        }
                    }
                }, {
                    field: 'source',
                    title: '商品来源',
                    align: 'center',
                    formatter: function (val) {
                        switch (val) {
                            case 1:
                                return '商家自建';
                                break;
                            case 2:
                                return '商品库';
                                break;
                            default:
                                return '--';
                                break;
                        }
                    }
                },{
                    field: 'standardProductId',
                    title: '标准库id',
                    align: 'center',
                    formatter:function (value,row) {
                        if(value){
                            if(row.disableTypeName != null && row.disableTypeName != ''){
                                return "<div>"+value+"</div>" + "<div title='点击查看详情' style='color: red; font-weight: bold;' onclick='alert(\"" + row.disableTypeName + ":" + row.disableNote + "\")'>已停用</div>"
                            }
                            return value
                        }
                        return "/"
                    }
                },{
                    field: 'spec',
                    title: '规格',
                    align: 'center',
                }, {
                    field: 'manufacturer',
                    title: '生产厂家',
                    align: 'center',
                }, {
                    field: 'drugClassification',
                    title: '处方类型',
                    align: 'center',
                    formatter: function (val) {
                        switch (val) {
                            case "1":
                                return '<span style="color:#99CC00">甲类OTC</span>';
                                break;
                            case "2":
                                return '<span style="color:#99CC33">乙类OTC</span>';
                                break;
                            case "3":
                                return '<span style="color:#99CC66">RX处方药</span>';
                                break;
                            default:
                                return '<span style="color:#99CC99">无</span>';
                                break;
                        }
                    }
                }, {
                    field: 'activityType',
                    title: '单体采购价',
                    align: 'center',
                    formatter: function (val, row,index) {
                        switch (val) {
                            case 0:
                            case 2:
                                // return row.fob;
                                return `<div>${row.fob}</div>
                                        ${row.haveAreaPrice == 1?`<div style="color:#0075A9;cursor: pointer;" onclick="openRegionalPrice(${index},${row.fob},${row.chainPrice})">查看区域价
                                        </div>`:''}
                                        `;
                                break;
                            case 1:
                                // return row.groupPrice;
                                return `<div>${row.groupPrice}</div>
                                        ${row.haveAreaPrice == 1?`<div style="color:#0075A9;cursor: pointer;" onclick="openRegionalPrice(${index},${row.groupPrice},${row.groupPrice})">查看区域价
                                        </div>`:''}
                                        `;
                                break;
                            case 3:
                                // return row.fob;
                                return `<div>${row.fob}</div>
                                        ${row.haveAreaPrice == 1?`<div style="color:#0075A9;cursor: pointer;" onclick="openRegionalPrice(${index},${row.fob},${row.chainPrice})">查看区域价
                                        </div>`:''}
                                        `;
                                break;
                            default:
                                // return '-';
                                return `<div>-</div>
                                        ${row.haveAreaPrice == 1?`<div style="color:#0075A9;cursor: pointer;" onclick="openRegionalPrice(${index},'','')">查看区域价
                                        </div>`:''}
                                        `;;
                                break;
                        }
                    }
                }, {
                    field: 'activityType',
                    title: '连锁采购价',
                    align: 'center',
                    formatter: function (val, row) {
                        switch (val) {
                            case 0:
                            case 2:
                                return row.chainPrice;
                                break;
                            case 1:
                                return row.groupPrice;
                                break;
                            case 3:
                                return row.chainPrice;
                                break;
                            default:
                                return '-';
                                break;
                        }
                    }
                },
                    /*{
                    field: 'skuPopExtend',
                    title: '市场价',
                    align: 'center',
                    sortable: true,
                    formatter: function (val, row) {
                        if(val){
                            return val.marketPrice;
                        }
                    }
                },*/ {
                    field: 'suggestPrice',
                    title: '零售价',
                    align: 'center',
                }, {
                    field: 'availableQty',
                    title: '库存',
                    align: 'center'
                }, /*{
                    field: 'limitedQty',
                    title: '限购数量',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'skuPopExtend',
                    title: '可购客户类型',
                    align: 'center',
                    sortable: true,
                    formatter: function (val, row) {
                        if(val){
                            switch (val.purchaseMerchantType) {
                                case '1':
                                    return '<span style="color:#99CC00">药店</span>';
                                    break;
                                case '2':
                                    return '<span style="color:#99CC33">诊所</span>';
                                    break;
                                default:
                                    return '<span style="color:#99CC99">无</span>';
                                    break;
                            }
                        }

                    }
                },*/ /*{
                    field: 'salesArea',
                    title: '销售区域',
                    align: 'center',
                    sortable: true
                },*/ {
                    field: 'createTime',
                    title: '提交时间',
                    align: 'center',
                    formatter:datetimeFormatter
                },
                    /*{
                    field: 'reportToStandard',
                    title: '上报中台',
                    align: 'center',
                    formatter:function (v) {
                        return v===true?"是":"否"
                    }
                },*/
                    {
                    field: 'logicStatus',
                    title: '商品状态',
                    align: 'center',
                    formatter: function (val) {
                        switch (val) {
                            case 1:
                                return '<span style="color:#99CC00">销售中</span>';
                                break;
                            case 2:
                                return '<span style="color:#99CC00">缺货下架</span>';
                                break;
                            case 3:
                                return '<span style="color:#99CC00">下架</span>';
                                break;
                            case 4:
                                return '<span style="color:#99CC00">下架</span>';
                                break;
                            case 6:
                                return '<span style="color:#99CC00">待上架</span>';
                                break;
                            case 8:
                                return '<span style="color:#99CC00">待审核</span>';
                                break;
                            case 9:
                                return '<span style="color:#99CC00">审核未通过</span>';
                                break;
                            default:
                                return '<span style="color:#99CC99">删除</span>';
                                break;
                        }
                    },
                }]
            });
        };
        //查询的参数
        applyPendingTableObj.queryParams = function (params) {
            return queryParams(params);
        };
        return applyPendingTableObj;
    },
    buttonInit: function () {
        var $table = $('#tb_pendingList');
        var oInit = new Object();
        oInit.Init = function () {

            function getSelectedRow() {
                var index = $table.find('tr.success').data('index');
                return $table.bootstrapTable('getData')[index];
            }

            //初始化页面上面的按钮事件
            //条件查询事件
            $("#btn_query").click(function () {
                localStorage.setItem('pageLocalNum',1);
                setQuery()
                if(!isInited){
                    new applyPendingList.tableInit().Init();
                    $("#notDataTips").css("display","none");
                    isInited=true;
                }else {
                    $table.bootstrapTable('refreshOptions',{pageNumber:1});  // pageNumber:1, 指定页码为第1页
                    $table.bootstrapTable('refresh');
                }
            });

            //条件清空事件
            $("#btn_clear").click(function () {
                $("#statusPre0").prop("checked",true);
                $("#barcode").val("");
                $("#originalBarcode").val("");
                $("#csuid").val("");
                $("#showName").val("");
                $("#tracingCode").val("");
                $("#manufacturer").val("");
                $("#companyName").val("");
                $("#name").val("");
                $("#approvalNumber").val("");
                $("#standardProductId").val("");
                $("#status").val("");
                $("#createBeginTime").val("");
                $("#createEndTime").val("");

                $('#createBeginTime').datetimepicker('remove');
                $('#createEndTime').datetimepicker('remove');
                $("#source").val("");
                $("#authReason").val("");
                $("#stockStatus").val("");
                resetCheckBoxValue("highGross");
                $("#activityType").val("");
                $("#provId").val("");
                initSearchTime();
                // $table.bootstrapTable('refresh');
                localStorage.setItem('pageLocalNum',1)
                $table.bootstrapTable('selectPage',1);
            });

            //审核
            $("#btn_auditing").click(function () {
                var row = getSelectedRow();
                debugger;
                setQuery()
                if(row){
					$.ajax({
						url: '/product/resentCallback/' + row.orgId,
						method: 'get',
						success: function(res) {
							if (res.data) {
								//是灰度
								LayTool.alert("该商业为上品流程灰度店铺，已上报中台不允许审核");
							} else {
								var id = row.id;
                    			window.location.href= "/product/detail?barcode="+row.barcode+"&orgId="+row.orgId+"&isAuditing="+1;
							}
						}
					})
                }else {
                    LayTool.alert("请选择一条记录");
                }
            });
            //预览
            $("#btn_yuLan").click(function () {
                var row = getSelectedRow();
                if(row){
                    var width = window.document.body.clientWidth - 40;
                    var height = window.document.body.clientHeight -40;
                    debugger;
                    LayTool.open({
                        title: "商品预览",
                        area:[width+'px',height+'px'],
                        type: 2,
                        content: "/webPop/#/product/preview?barcode="+row.barcode+"&orgId="+row.orgId
                    });
                }else {
                    LayTool.alert("请选择一条记录");
                }
            });
            //查看日志
            $("#btn_log").click(function () {
                var row = getSelectedRow();
                if (row) {
                    var barcode = row.barcode;

                    LayTool.open({
                        title: "查看日志",
                        area: ['500px', '400px'],
                        content: [
                            basePath + 'product/skuUpdateLog?barcode=' + barcode, 'yes'
                        ]
                    });
                }else {
                    LayTool.alert("请选择一条记录");
                }
            });
            //查看日志
            $("#btn_stockLog").click(function () {
                var row = getSelectedRow();
                if (row) {
                    var barcode = row.barcode;
                    var showName = row.showName;
                    var erpCode = row.erpCode;

                    LayTool.open({
                        title: "商品库存日志",
                        area: ['800px', '600px'],
                        content: [
                            basePath + 'product/stockLogView?barcode=' + barcode, 'yes'
                        ]
                    });
                }else {
                    LayTool.alert("请选择一条记录");
                }
            });
            //详情
            $('.queryDetail').click(function () {
                var row = getSelectedRow();
                setQuery()
                if (row) {
                    window.location.href = "/product/detail?barcode="+row.barcode+"&orgId="+row.orgId
                } else {
                    LayTool.alert("请选择一条记录");
                }
            });
            //批量更新商品信息
            $("#btn_batchUpdate").click(function () {
                LayTool.open({
                    title: "批量修改商品信息",
                    type: 2,
                    area: ['500px', '450px'],
                    content: [
                        '/product/batchUpdateSkuView', 'yes'
                    ],
                    end: function () {
                        location.reload();
                    }
                })
            });
            //批量更新ERP商品信息
            $("#btn_batchUpdateErp").click(function () {
                LayTool.open({
                    title: "批量修改ERP商品信息",
                    type: 2,
                    area: ['500px', '450px'],
                    content: [
                        '/product/batchUpdateErpSkuView', 'yes'
                    ],
                    end: function () {
                        location.reload();
                    }
                })
            });

            $.ajax({
                type: "GET",
                url: "/businessProvinceList",
                dataType : "json",
                success:function (res) {
                    var options = '<option value="" selected="selected">全部</option>';
                    if(res.code == 0 && res.data != null && res.data.length > 0){
                        var data = res.data;
                        for (var i = 0; i < data.length; i++) {
                            options += '<option value='+data[i].provId+'>' + data[i].prov +'</option>';
                        }
                    }
                    $("#provId").html(options);
                }
            });
        };
        return oInit;
    }
});
/**区域设置
 * 
 */
function regionalFiltering(flag,code){
    $.ajax({
        type: 'get',
        url: '/common/areas?parentCode='+code,
        success: function (res) {
            console.log(res);
            if (res.code == 0) {
                var dat = res.data;
                var result = '<option value="" selected="selected">全部</option>';
                for (let i = 0; i < dat.length; i++) {
                    var d = dat[i];
                    result += '<option value="' + d.areaCode + '">' + d.areaName + '</option>';
                }
                if(flag == 1){
                    $("#province").html(result);
                }else if(flag == 2){
                    $("#city").html(result);
                }else if(flag == 3){
                    $("#district").html(result);
                }
            }
        }
    });
};
/**选择省 */
function provinceChange(){
    let val = $('#province').val();
    regionalFiltering(2,val);
    $("#city").html('<option value="" selected="selected">全部</option>');
    $("#district").html('<option value="" selected="selected">全部</option>');
};
/**选择市 */
function cityChange(){
    let val = $('#city').val();
    regionalFiltering(3,val);
    $("#district").html('<option value="" selected="selected">全部</option>');
};
/**查看区域价 */
function openRegionalPrice(index,MPurchasePrice,CPurchasePrice){
    var $table = $('#tb_pendingList');
    const rowData = $table.bootstrapTable('getData')[index];
    var width = window.document.body.clientWidth - 40;
    var height = window.document.body.clientHeight -40;
    LayTool.open({
        title: "查看区域价",
        area:[width+'px',height+'px'],
        type: 2,
        content: "/webPop/#/openRegionalPrice?barcode="+rowData.barcode+'&showName='+rowData.commonName+'&MPurchasePrice='+MPurchasePrice+'&CPurchasePrice='+CPurchasePrice,
    });
    // $('#province').val('');
    // $("#district").val('');
    // $("#city").val('');
    // $("#customerType").val('');
    // regionalFiltering(1,null);
    // $.ajax({
    //     type: 'get',
    //     url: '/common/customerTypes',
    //     success: function (res) {
    //         console.log(res);
    //         if (res.code == 0) {
    //             var dat = res.data;
    //             var result = '<option value="" selected="selected">全部</option>';
    //             for (let i = 0; i < dat.length; i++) {
    //                 var d = dat[i];
    //                 result += '<option value="' + d.code + '">' + d.text + '</option>';
    //             }
    //             $("#customerType").html(result);
    //         }
    //     }
    // });
    // $('#barcodeC').text(rowData.barcode);
    // $('#MPurchasePrice').text(MPurchasePrice);
    // $('#CPurchasePrice').text(CPurchasePrice);
    // console.log(rowData,'qiyu');
    // $('#openRegionalPriceDialog').modal("show");
};
function datetimeFormatter(val) {
    if (val != null) {
        return ToolUtil.dateFormat(val, 'yyyy-MM-dd');
    }
}


function toDecimal2(x) {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return false;
    }
    var fb = Math.round(x * 100) / 100;
    var s = fb.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
}

function getNowFormatDate() {
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    return currentdate;
}

var queryParams = function (params) {
    params = params || {};
    //var local = localStorage.getItem('localAdminQuery')?JSON.parse(localStorage.getItem('localAdminQuery')):{}
    var returnObj = {
        //每页显示条数
        limit: params.limit,
        //起始页数
        offset: params.offset,
        //排序字段
        property: (params.sort && params.sort !== '') ? ToolUtil.underscoreName(params.sort) : '',
        //排序方式
        direction: params.order,
        //查询条件（将对象属性封装成查询实体对象）
        barcode: $.trim($("#barcode").val()),
        originalBarcode: $.trim($("#originalBarcode").val()),
        csuid: $.trim($("#csuid").val()),
        showName: $.trim($("#showName").val()),
        tracingCode: $.trim($("#tracingCode").val()),
        manufacturer: $.trim($("#manufacturer").val()),
        companyName: $.trim($("#companyName").val()),
        name: $.trim($("#name").val()),
        approvalNumber: $.trim($("#approvalNumber").val()),
        standardProductId: $.trim($("#standardProductId").val()),
        status: $.trim($("#status").val()),
        createBeginTime: $.trim($("#createBeginTime").val()),
        createEndTime: $.trim($("#createEndTime").val()),
        source: $.trim($("#source").val()),
        authReason: $.trim($("#authReason").val()),
        stockStatus: $.trim($("#stockStatus").val()),
        highGross: getCheckBoxValue("highGross"),
        activityType: $.trim($("#activityType").val()),
        disable: $.trim($("#disable").val()),
        provId: $.trim($("#provId").val())
    };
    return returnObj
};


function getCheckBoxValue(name){
    var chk_value =[];
    $('input[name='+name+']:checked').each(function(){
        chk_value.push($(this).val());
    });
    return chk_value.join(",");
}
function resetCheckBoxValue(name,value){
    var checkeds = value?value.split(','):[];
    $('input[name='+name+']').each(function(){
        $(this).prop('checked', $.inArray($(this).val(), checkeds)>=0);
    });
}


function initSearchTime(){
    $('#createBeginTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var searchStartTime = $("#createBeginTime").val();
        $("#createEndTime").datetimepicker('setStartDate', searchStartTime);
    });
    $('#createEndTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var billEndTime = $("#createEndTime").val();
        $("#createBeginTime").datetimepicker('setEndDate', billEndTime);
    });
}
function getTime(time) {
    if(time<10){
        return "0"+time;
    }
    return time;
}

function setPageLocal(num){
    localStorage.setItem('pageLocalNum',num);
}
function setQuery() {
    // console.log(new applyPendingList.tableInit().queryParams,'new applyPendingList.tableInit().queryParams')
    var queryObj = queryParams();
    localStorage.setItem('localAdminQuery',JSON.stringify(queryObj))
}
function getQuery() {
    var queryS = JSON.parse(localStorage.getItem('localAdminQuery'));
    if(queryS){
        $("#barcode").val(queryS.barcode);
        $("#originalBarcode").val(queryS.originalBarcode);
        $("#csuid").val(queryS.csuid);
        $("#showName").val(queryS.showName);
        $("#tracingCode").val(queryS.tracingCode);
        $("#manufacturer").val(queryS.manufacturer);
        $("#companyName").val(queryS.companyName);
        $("#approvalNumber").val(queryS.approvalNumber);
        $("#standardProductId").val(queryS.standardProductId);
        $("#status").val(queryS.status);
        $("#createBeginTime").val(queryS.createBeginTime);
        $("#createEndTime").val(queryS.createEndTime);
        $("#source").val(queryS.source);
        $("#authReason").val(queryS.authReason);
        $("#stockStatus").val(queryS.stockStatus);
        resetCheckBoxValue("highGross",queryS.highGross);
        $("#activityType").val(queryS.activityType);
        $("#provId").val(queryS.provId);
    }
    if(!isInited&&!queryS){
    }else {
        new applyPendingList.tableInit().Init();
        $("#notDataTips").css("display","none");
        isInited=true;
    }
}

function isShowBtn() {
    if(jQuery.inArray("审核", btnArr) == -1){
        $("#btn_auditing").remove();
    }
    if(jQuery.inArray("查看日志", btnArr) == -1){
        $("#btn_log").remove();
    }
    if(jQuery.inArray("导出", btnArr) == -1){
        $("#btn_export").remove();
    }
    if(jQuery.inArray("预览", btnArr) == -1){
        $("#btn_yuLan").remove();
    }
    if(jQuery.inArray("批量更新商品信息", btnArr) == -1){
        $("#btn_batchUpdate").remove();
    }
    if(jQuery.inArray("批量更新ERP商品信息", btnArr) == -1){
        $("#btn_batchUpdateErp").remove();
    }
}

function reShowStatus(value) {
    if(value==1){
        $("#status").val("20");
        $("#status").css("display","none");
    }else {
        $("#status").val("");
        $("#status").css("display","");
    }
}