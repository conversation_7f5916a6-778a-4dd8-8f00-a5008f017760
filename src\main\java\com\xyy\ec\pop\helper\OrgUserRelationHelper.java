package com.xyy.ec.pop.helper;

import com.google.common.collect.Maps;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pop.server.api.merchant.dto.OrgUserRelationDto;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * POP用户关系helper
 */
public class OrgUserRelationHelper {

    public static Map<Long, OrgUserRelationDto> convertMerchantToOrgUserMap(Map<Long, MerchantBussinessDto> merchantMap) {
        if(MapUtils.isEmpty(merchantMap)){
            return Maps.newHashMap();
        }
        Map<Long, OrgUserRelationDto> retMap = Maps.newHashMap();
        for(Long merchantId : merchantMap.keySet()){
            MerchantBussinessDto merchantDto = merchantMap.get(merchantId);
            OrgUserRelationDto orgUserRelationDto = convertMerchantToOrgUserRelation(merchantDto);
            if(null != orgUserRelationDto){
                retMap.put(merchantId, orgUserRelationDto);
            }
        }
        return retMap;
    }

    public static OrgUserRelationDto convertMerchantToOrgUserRelation(MerchantBussinessDto merchant) {
        if (merchant == null) {
            return null;
        }
        OrgUserRelationDto vo = new OrgUserRelationDto();
        vo.setMerchantId(merchant.getId());
        vo.setMerchantName(merchant.getNickname());
        vo.setAccountStatus(merchant.getStatus() == null ? null : merchant.getStatus().byteValue());
        return vo;
    }
}
