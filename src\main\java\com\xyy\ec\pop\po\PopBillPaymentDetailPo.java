package com.xyy.ec.pop.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * tb_xyy_pop_bill_payment_detail
 * <AUTHOR>
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PopBillPaymentDetailPo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 商家名称
     */
    private String orgName;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 账单号
     */
    private String billNo;

    /**
     * 商品总金额
     */
    private BigDecimal productMoney;

    /**
     * 单据总金额
     */
    private BigDecimal totalMoney;

    /**
     * 订单实际付款金额
     */
    private BigDecimal money;

    /**
     * 运费
     */
    private BigDecimal freightAmount;

    /**
     * 店铺优惠券金额
     */
    private BigDecimal couponShopAmount;

    /**
     * 店铺营销活动优惠金额
     */
    private BigDecimal marketingShopAmount;

    /**
     * 店铺总优惠金额
     */
    private BigDecimal shopTotalDiscount;

    /**
     * 平台优惠券金额
     */
    private BigDecimal couponPlatformAmount;

    /**
     * 平台营销活动优惠金额
     */
    private BigDecimal marketingPlatformAmount;

    /**
     * 平台总优惠金额
     */
    private BigDecimal platformTotalDiscount;

    /**
     * 佣金
     */
    private BigDecimal hireMoney;
    /**
     * 应收佣金
     */
    private BigDecimal payableCommission;
    /**
     * 冲抵平台优惠后的佣金
     */
    private BigDecimal deductedCommission;

    /**
     * 是否冲抵优惠 0-未冲抵 1-已冲抵
     */
    private Byte deducted;
    /**
     * 佣金结算方式，1：非月结；2：月结
     */
    private Byte settlementType;
    /**
     * 处罚金额
     */
    private BigDecimal penaltyAmount;

    /**
     * 应结算金额
     */
    private BigDecimal statementTotalMoney;

    /**
     * 支付类型(1:在线支付 2:货到付款 3:线下转账）
     */
    private Byte payType;

    /**
     * 入账单号
     */
    private String flowNo;

    /**
     * 账单生成时间
     */
    private Date billCreateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 账单入账时间
     */
    private Date billPaymentTime;

    /**
     * 现金实付金额
     */
    private BigDecimal cashPayAmount;

    /**
     * 购物金实付金额
     */
    private BigDecimal virtualGold;

    private static final long serialVersionUID = 1L;
}