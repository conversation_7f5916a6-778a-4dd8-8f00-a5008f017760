package com.xyy.ec.pop.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class AssertUtil {
    public static void equals(String expected, String actual, String message) {
        if (expected == null && actual == null) {
            return;
        }
        if (expected != null && expected.equals(actual)) {
            return;
        }
        if (actual != null && actual.equals(expected)) {
            return;
        }
        throw new IllegalArgumentException(message);
    }

    public static void notNull(Object o, String message) {
        if (o == null) {
            throw new IllegalArgumentException(message);
        }
    }

    public static void notBlank(String str, String message) {
        if (StringUtils.isBlank(str)) {
            throw new IllegalArgumentException(message);
        }
    }

    public static void isFalse(boolean bool, String message) {
        if (bool) {
            throw new IllegalArgumentException(message);
        }
    }
}
