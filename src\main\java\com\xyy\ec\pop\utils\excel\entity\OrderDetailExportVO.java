package com.xyy.ec.pop.utils.excel.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.xyy.ec.pop.enums.OrderEnum;
import com.xyy.ec.pop.utils.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单记录导出
 * Created by danshiyu on 2019/11/4.
 */
@Data
@NoArgsConstructor
public class OrderDetailExportVO implements Serializable{

    /** 订单明细主键ID */
    private Long id;

    /**
     * 机构（公司）ID
     */
    private String orgId;

    /* 用户ID */
    private Long merchantId;
    /**
     * 商户编号
     */
    @Excel(name = "商户编号",width = 10)
    private String corporationNo;

    /**
     * 商户名称
     */
    @Excel(name = "商户名称",width = 10)
    private String companyName;

    /**
     * 商户名称
     */
    @Excel(name = "店铺名称",width = 10)
    private String corporationName;

    /** 药店名称 */
    @Excel(name = "客户名称",width = 10)
    private String merchantName;

    /** 客户erp编码  */
    @Excel(name = "客户erp编码",width = 10)
    private String merchantErpCode;

    /** 订单编号 */
    @Excel(name = "销售单号",width = 10)
    private String orderNo;

    /** 总金额=实付金额+优惠金额 */
    @Excel(name = "订单金额",width = 10)
    private BigDecimal orderOriginalTotalAmount;

    //  运费金额
    @Excel(name = "运费金额",width = 10)
    private BigDecimal freightAmount;

    /** 订单实付金额 */
    @Excel(name = "实付金额",width = 10)
    private BigDecimal money;

    /** 优惠金额 */
    @Excel(name = "优惠金额",width = 10)
    private BigDecimal orderOriginalTotalDiscount;

    /** 创建时间 */
    @Excel(name = "下单时间",width = 20,format = DateUtil.PATTERN_STANDARD)
    private Date createTime;

    @Excel(name = "省份",width = 10)
    private String provinceStr;
    /**
     * 物流公司
     */
    @Excel(name = "物流公司",width = 10)
    private String logisticsWay;

    @Excel(name = "支付类型",width = 10)
    private String payTypeStr;

    @Excel(name = "支付渠道",width = 10)
    private String payChannelStr;

    @Excel(name = "支付时间",width = 20,format = DateUtil.PATTERN_STANDARD)
    private Date payTime;

    @Excel(name = "出库时间",width = 20,format = DateUtil.PATTERN_STANDARD)
    private Date shipTime;

    @Excel(name = "完成时间",width = 20,format = DateUtil.PATTERN_STANDARD)
    private Date finishTime;

    @Excel(name = "状态",width = 10)
    private String statusStr;

    @Excel(name = "是否首单", width = 10)
    private String firstOrderFlagDesc;

    @Excel(name = "订单类型", width = 10)
    private String orderTypeDesc;

    @Excel(name = "商品编码",width = 10)
    private String barcode;

    @Excel(name = "商品名称",width = 10)
    private String productName;

    @Excel(name = "商品erp编码",width = 10)
    private String productErpCode;

    @Excel(name = "规格",width = 10)
    private String spec;

    @Excel(name = "商品原价",width = 10)
    private BigDecimal origPrice;

    @Excel(name = "采购数量",width = 10)
    private Integer productAmount;

    @Excel(name = "采购金额",width = 10)
    private BigDecimal originalTotalAmount;

    @Excel(name = "店铺券优惠",width = 10)
    private BigDecimal shopVoucherAmount;

    @Excel(name = "平台券优惠",width = 10)
    private BigDecimal crossPlatformVoucherAmount;

    @Excel(name = "店铺活动优惠",width = 10)
    private BigDecimal shopPromoDiscount;

    @Excel(name = "平台活动优惠",width = 10)
    private BigDecimal platformPromoDiscount;

    @Excel(name = "优惠金额",width = 10)
    //private BigDecimal discountAmount;
    private BigDecimal originalTotalDiscount;

    @Excel(name = "实付金额",width = 10)
    private BigDecimal realPayAmount;

    @Excel(name = "参与活动",width = 10)
    private String allPromoId;
}
