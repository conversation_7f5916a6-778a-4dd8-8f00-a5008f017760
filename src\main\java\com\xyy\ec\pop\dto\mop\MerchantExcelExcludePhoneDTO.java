package com.xyy.ec.pop.dto.mop;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.scm.constant.annotation.Explain;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @author: duHao
 * @since: 22:21 2024/12/9
 */
@Data
public class MerchantExcelExcludePhoneDTO implements Serializable {

    @Explain("主键id")
    private Long id;

    @Explain("poiId")
    @ExcelProperty(value = "poiId")
    private Long poiId;

    @Explain("(poiId状态)门店生命周期状态([营业状态])")
    @ExcelProperty(value = "poiId状态")
    private String poiLifeStatus;

    @Explain("poi省份id")
    private Long poiProvinceId;

    @Explain("poi城市id")
    private Long poiCityId;

    @Explain("企业省份id")
    private Long provId;

    @Explain("企业城市id")
    private Long cityId;

    @Explain("线索省份")
    @ExcelProperty(value = "线索省份")
    private String provName;

    @Explain("线索城市")
    @ExcelProperty(value = "线索城市")
    private String cityName;

    @Explain("(商户编码)机构id")
    @ExcelProperty(value = "商户编码")
    private String orgId;

    // TODO 店铺编码

    @Explain("(商户名称)机构名称")
    @ExcelProperty(value = "商户名称")
    private String companyName;

    @Explain("(店铺名称)公司名称")
    @ExcelProperty(value = "店铺名称")
    private String storeName;

    @Explain("(店铺分类)店铺经营属性")
    @ExcelProperty(value = "店铺分类")
    private String businessAttribute;

    @Explain("店铺状态")
    @ExcelProperty(value = "店铺状态")
    private String shopStatus;

    @Explain("(经营状态)店铺经营状态")
    @ExcelProperty(value = "经营状态")
    private String shopManageStatus;

    @Explain("商圈分类")
    @ExcelProperty(value = "商圈分类")
    private String shopCategory;

    @Explain("(供货对象)用户类型")
    @ExcelProperty(value = "供货对象")
    private String supplyUserType;

    @Explain("(店铺供货省份)区域编码")
    @ExcelProperty(value = "店铺供货省份")
    private Integer supplyAreaCode;

    @Explain("(在售品规)在售商品数")
    @ExcelProperty(value = "在售品规")
    private Long saleGoodsQty;

    @Explain("(注册时间)企业创建时间")
    @ExcelProperty(value = "注册时间")
    private LocalDateTime corCreateTime;

    @Explain("认证时间")
    @ExcelProperty(value = "认证时间")
    private LocalDateTime authTime;

    @Explain("资质状态")
    @ExcelProperty(value = "资质状态")
    private String qualificationStatus;

    @Explain("保证金金额")
    @ExcelProperty(value = "保证金金额")
    private BigDecimal bondMoney;

    @Explain("协议签署状态")
    @ExcelProperty(value = "协议签署状态")
    private String fddEnterpriseAgreementStatus;

    @Explain("erp对接状态")
    @ExcelProperty(value = "erp对接状态")
    private String erpJointStatus;

    @Explain("erp对接完成时间")
    @ExcelProperty(value = "erp对接完成时间")
    private LocalDateTime erpJointFinishTime;

    @Explain("(成功入驻时间)店铺成功入驻时间")
    @ExcelProperty(value = "成功入驻时间")
    private LocalDateTime shopSuccessEnterTime;

    @Explain("(有效入驻时间)店铺有效入驻时间")
    @ExcelProperty(value = "有效入驻时间")
    private LocalDateTime shopEffectEnterTime;

    @Explain("佣金比例")
    @ExcelProperty(value = "佣金比例")
    private BigDecimal commissionRatio;

    @Explain("(支付通道)结算通道")
    @ExcelProperty(value = "支付通道")
    private String paymentChannel;

    @Explain("(电子签开通状态)法大大开通状态")
    @ExcelProperty(value = "电子签开通状态")
    private String fddStatus;

    @Explain("(法大大超管账号)法大大管理员手机号")
    @ExcelProperty(value = "法大大超管账号")
    private String fddAdminPhone;

    @Explain("经度")
    @ExcelProperty(value = "经度")
    private Integer longitude;

    @Explain("纬度")
    @ExcelProperty(value = "纬度")
    private Integer latitude;

}
