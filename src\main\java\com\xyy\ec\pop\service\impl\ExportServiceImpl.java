package com.xyy.ec.pop.service.impl;

import cn.afterturn.easypoi.entity.vo.NormalExcelConstants;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.exception.excel.ExcelExportException;
import cn.afterturn.easypoi.view.PoiBaseView;
import com.xyy.ec.pop.service.ExportService;
import com.xyy.ec.pop.utils.DateUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;
import org.springframework.ui.ModelMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 2020/05/04
 */
@Service
public class ExportServiceImpl implements ExportService {
    @Override
    public <T> void export(ModelMap modelMap, HttpServletRequest request, HttpServletResponse response, List<T> parseArray, Class<T> clazz, String fileName, String sheetName) {
        try {
            ExportParams params = new ExportParams();
            params.setSheetName(sheetName);
            params.setType(ExcelType.XSSF);
            params.setColor(IndexedColors.BLUE_GREY.index);
            params.setFreezeCol(2);
            // 数据集合
            modelMap.put(NormalExcelConstants.DATA_LIST, parseArray);
            //导出实体
            modelMap.put(NormalExcelConstants.CLASS, clazz);
            //参数
            modelMap.put(NormalExcelConstants.PARAMS, params);
            //文件名称
            modelMap.put(NormalExcelConstants.FILE_NAME, String.format("%s%s", fileName, DateFormatUtils.format(System.currentTimeMillis(), DateUtil.PATTERN_DATE)));
            PoiBaseView.render(modelMap, request, response, NormalExcelConstants.EASYPOI_EXCEL_VIEW);
        } catch (Exception e) {
            throw new ExcelExportException(e.getMessage());
        }
    }


}
