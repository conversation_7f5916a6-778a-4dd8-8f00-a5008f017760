package com.xyy.ec.pop.excel.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.xyy.ec.pop.vo.BatchOverOrderVo;
import com.xyy.ec.pop.vo.SkuBatchUpdateVo;
import org.apache.commons.lang3.StringUtils;

/**
 * @version v1
 * @Description 批量完成订单过滤空行
 * <AUTHOR>
 */
public class OrderOverOrderExcelVerifyHandler implements IExcelVerifyHandler<BatchOverOrderVo> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(BatchOverOrderVo vo) {
        if(StringUtils.isNotBlank(vo.getOrderNo())){
            return new ExcelVerifyHandlerResult(true);
        }
        return new ExcelVerifyHandlerResult(false);
    }
}
