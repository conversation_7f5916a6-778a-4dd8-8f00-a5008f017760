package com.xyy.ec.pop.marketing.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.marketing.dto.GroupBuyingBatchOfflineResultDTO;
import com.xyy.ec.pop.marketing.param.GroupBuyingListExportParam;
import com.xyy.ec.pop.marketing.param.GroupBuyingListQueryParam;
import com.xyy.ec.pop.marketing.param.GroupBuyingStatusCountQueryParam;
import com.xyy.ec.pop.marketing.vo.GroupBuyingInfoVO;
import com.xyy.ec.pop.marketing.vo.GroupBuyingStatisticsVO;
import com.xyy.ec.pop.marketing.vo.GroupBuyingStatusCountVO;
import com.xyy.ec.pop.model.SysUser;
import org.springframework.web.multipart.MultipartFile;

public interface GroupBuyingService {

    /**
     * 获取拼团活动的统计信息
     *
     * @param userOaId
     * @return
     */
    GroupBuyingStatisticsVO getStatistics(String userOaId);

    /**
     * 分页查询拼团活动信息
     *
     * @param userOaId
     * @param queryParam
     * @return
     */
    PageInfo<GroupBuyingInfoVO> paging(String userOaId, GroupBuyingListQueryParam queryParam);

    /**
     * 获取拼团活动各个状态的数量
     *
     * @param userOaId
     * @param queryParam
     * @return
     */
    GroupBuyingStatusCountVO getStatusCount(String userOaId, GroupBuyingStatusCountQueryParam queryParam);

    /**
     * 导出
     *
     * @param user
     * @param queryParam
     */
    void asyncExport(SysUser user, GroupBuyingListExportParam queryParam);

    /**
     * 批量下线
     *
     * @param user
     * @param type
     * @param excelFile
     * @return
     */
    GroupBuyingBatchOfflineResultDTO batchOffline(SysUser user, Integer type, MultipartFile excelFile);
}
