package com.xyy.ec.pop.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class SkuPopExtend implements Serializable {
    private static final long serialVersionUID = 8367376249534253216L;
    private Long id;

    private String orgId;

    private String branchCode;

    private Long skuId;

    private BigDecimal marketPrice;

    private Integer certificateId;

    private String url;

    private Integer type;

    private Integer isChoosed;

    private String salesArea;

    private Integer isSalesOn;

    private String erpCode;

    private String subtitle;

    private Date onlineTime;

    private String purchaseMerchantType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode == null ? null : branchCode.trim();
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public Integer getCertificateId() {
        return certificateId;
    }

    public void setCertificateId(Integer certificateId) {
        this.certificateId = certificateId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSalesArea() {
        return salesArea;
    }

    public void setSalesArea(String salesArea) {
        this.salesArea = salesArea == null ? null : salesArea.trim();
    }

    public Integer getIsSalesOn() {
        return isSalesOn==null?0:isSalesOn;
    }

    public void setIsSalesOn(Integer isSalesOn) {
        this.isSalesOn = isSalesOn;
    }

    public String getErpCode() {
        return erpCode==null?"":erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle == null ? null : subtitle.trim();
    }

    public Date getOnlineTime() {
        return onlineTime;
    }

    public void setOnlineTime(Date onlineTime) {
        this.onlineTime = onlineTime;
    }

    public String getPurchaseMerchantType() {
        return purchaseMerchantType==null?"":purchaseMerchantType;
    }

    public void setPurchaseMerchantType(String purchaseMerchantType) {
        this.purchaseMerchantType = purchaseMerchantType;
    }

    public Integer getIsChoosed() {
        return isChoosed;
    }

    public void setIsChoosed(Integer isChoosed) {
        this.isChoosed = isChoosed;
    }
}