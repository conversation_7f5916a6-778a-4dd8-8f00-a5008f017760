package com.xyy.ec.pop.vo.settle;

import com.xyy.ec.pop.po.PopBillSettleDetailPo;
import com.xyy.ec.pop.server.api.admin.dto.PopBillSettleDetailDto;
import com.xyy.ec.pop.utils.NumberUtils;
import com.xyy.me.product.service.read.dto.TotalDictionaryReadDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class PopBillSettleDetailVo {

    /**
     * 商品编码
     */
    private String barcode;

    /**
     * skuid
     */
    private Long csuid;

    /**
     * 商品erp编码
     */
    private String erpCode;

    /**
     * 商品一级分类
     */
    private String businessFirstCategoryName;

    /**
     * 商品原价
     */
    private BigDecimal productOriginPrice;

    /**
     * 数量
     */
    private BigDecimal productAmount;

    /**
     * 商品金额
     */
    private BigDecimal productMoney;

    /**
     * 店铺总优惠
     */
    private BigDecimal shopTotalDiscount;

    /**
     * 平台总优惠金额
     */
    private BigDecimal platformTotalDiscount;

    /**
     * 商品实付金额
     */
    private BigDecimal payAmount;

    /**
     * 商品佣金比例
     */
    private String commissionRatio;

    /**
     * 一级分类佣金比例
     */
    private String firstCommissionRatio;

    /**
     * 一级分类佣金金额
     */
    private BigDecimal firstHireMoney;

    /**
     * 佣金金额
     */
    private BigDecimal hireMoney;
    /**
     * 佣金折扣
     */
    private BigDecimal commissionDiscount;

    /**
     * 折扣原因
     */
    private String discountReason;
    /**
     * 应缴纳佣金
     */
    private BigDecimal payableCommission;

    /**
     * 实际需缴纳佣金
     */
    private BigDecimal actualCommissionMoney;

    /**
     * 佣金优惠
     */
    private BigDecimal commissionDiscountMoney;

    public static PopBillSettleDetailVo from(PopBillSettleDetailDto detailDto, Map<String, TotalDictionaryReadDto> firstCategoryMap) {
        PopBillSettleDetailVo detailVo = new PopBillSettleDetailVo();
        detailVo.setBarcode(detailDto.getBarcode());
        detailVo.setCsuid(detailDto.getCsuid());
        detailVo.setErpCode(detailDto.getErpCode());
        TotalDictionaryReadDto readDto = firstCategoryMap.get(detailDto.getBusinessFirstCategoryCode());
        if (Objects.nonNull(readDto)) {
            detailVo.setBusinessFirstCategoryName(readDto.getDictName());
        }
        detailVo.setProductOriginPrice(detailDto.getProductOrginPrice());
        detailVo.setProductAmount(detailDto.getProductQuantity());
        detailVo.setProductMoney(detailDto.getProductMoney());
        detailVo.setShopTotalDiscount(detailDto.getShopTotalDiscount());
        detailVo.setPlatformTotalDiscount(detailDto.getPlatformTotalDiscount());
        detailVo.setPayAmount(detailDto.getPayAmount());
        detailVo.setCommissionRatio(NumberUtils.percentage(detailDto.getCommissionRatio()));
        detailVo.setFirstCommissionRatio(NumberUtils.percentage(detailDto.getFirstCommissionRatio()));
        detailVo.setHireMoney(detailDto.getHireMoney());
        detailVo.setFirstHireMoney(detailDto.getFirstHireMoney());
        detailVo.setCommissionDiscount(detailDto.getCommissionDiscount());
        detailVo.setDiscountReason(detailDto.getDiscountReason());
        detailVo.setPayableCommission(detailDto.getPayableCommission());
        detailVo.setActualCommissionMoney(detailDto.getActualCommissionMoney());
        detailVo.setCommissionDiscountMoney(detailDto.getCommissionDiscountMoney());
        return detailVo;
    }
}
