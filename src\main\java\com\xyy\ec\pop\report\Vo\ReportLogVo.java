package com.xyy.ec.pop.report.Vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ReportLogVo implements Serializable {
    private static final long serialVersionUID = 4100819558293366436L;
    private Integer auditStatus;
    private String auditStatusStr;
    private String auditDesc;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;
    private String operator;
}
