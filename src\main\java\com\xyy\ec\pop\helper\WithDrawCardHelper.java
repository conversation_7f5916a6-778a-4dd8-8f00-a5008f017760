package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.server.api.merchant.dto.WithDrawCardDto;
import com.xyy.ec.pop.vo.WithDrawCardVo;

import java.util.Date;


/**
 * @ClassName CashAccountHelper
 * <AUTHOR>
 * @Date 2024/5/13 19:07
 * @Version 1.0
 */
public class WithDrawCardHelper {
    public static WithDrawCardDto convertToDto(WithDrawCardVo withDrawCardVo) {
        WithDrawCardDto dto = new WithDrawCardDto();
        dto.setOrgId(withDrawCardVo.getOrgId());
        dto.setAccountName(withDrawCardVo.getAccountName());
        dto.setBankCode(withDrawCardVo.getBankCode());
        dto.setBankName(withDrawCardVo.getBankName());
        dto.setSubBankName(withDrawCardVo.getSubBankName());
        dto.setCardType(withDrawCardVo.getCardType());
        dto.setApplicationUrl(withDrawCardVo.getApplicationUrl());
        dto.setCreateTime(new Date());
        dto.setUpdateTime(new Date());
        return dto;
    }
}