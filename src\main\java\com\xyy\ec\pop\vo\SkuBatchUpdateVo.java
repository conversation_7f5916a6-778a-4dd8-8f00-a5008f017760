package com.xyy.ec.pop.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * @Description 批量更新商品信息
 * <AUTHOR>
 * @Date 2021/3/11
 */
@Data
public class SkuBatchUpdateVo {
    @Excel(name="*商品编码",width=20)
    private String barcode;
    @Excel(name="通用名称",width=20)
    private String commonName;
    @Excel(name="商品名称",width=20)
    private String productName;
    @Excel(name="展示名称",width=20)
    private String showName;
    @Excel(name="erp编码",width=20)
    private String erpCode;
    /**
     * 状态:1-销售中，4-下架，6-待上架(新增商品属于待上架)，8-待审核，9-审核未通过
     */
    @Excel(name="状态",width=20)
    private String status;
    @Excel(name="经营分类",width=20)
    private String skuCategory;
    @Excel(name="一级分类",width=20)
    private String businessFirstCategoryName;
    //一级分类编码
    private String businessFirstCategoryCode;
    @Excel(name="二级分类",width=20)
    private String businessSecondCategoryName;
    //二级分类编码
    private String businessSecondCategoryCode;
    @Excel(name="三级分类",width=20)
    private String businessThirdCategoryName;
    //三级分类编码
    private String businessThirdCategoryCode;
    @Excel(name="四级分类",width=20)
    private String businessFourthCategoryName;
    //四级分类编码
    private String businessFourthCategoryCode;
    @Excel(name="商品条码",width=20)
    private String code;
    @Excel(name="批准文号",width=20)
    private String approvalNumber;
    @Excel(name="生产厂家",width=20)
    private String manufacturer;
    @Excel(name="处方类型",width=20)
    private String drugClassification;
    @Excel(name="剂型",width=20)
    private String dosageForm;
    @Excel(name="包装单位",width=20)
    private String productUnit;
    @Excel(name="规格",width=20)
    private String spec;
    @Excel(name="建议零售价",width=20)
    private String suggestPrice;
    @Excel(name="推荐卖点1",width=20)
    private String sellingProposition1;
    @Excel(name="推荐卖点2",width=20)
    private String sellingProposition2;
    @Excel(name="推荐卖点3",width=20)
    private String sellingProposition3;
    @Excel(name="备注",width=20)
    private String remark;

    //经营范围Id
    private Long skuCategoryId;
    @Excel(name="失败原因",width=15)
    protected String errorMsg;
    /**
     * 表明否已经导入错误了
     */
    private boolean failed;
    private String orgId;
}
