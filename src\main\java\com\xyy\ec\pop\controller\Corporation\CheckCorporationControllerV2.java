package com.xyy.ec.pop.controller.Corporation;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.constants.ZTreeByCategoryRelation;
import com.xyy.ec.pop.helper.CheckCorporationConverHelper;
import com.xyy.ec.pop.helper.CheckCorporationModifyHelper;
import com.xyy.ec.pop.remote.CheckCorporationRemoteAdapter;
import com.xyy.ec.pop.server.api.merchant.api.admin.BusinessCategoryAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.CheckCorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.CheckCorporationBusinessAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.enums.CheckCorporationStateEnum;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationStateEnum;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.*;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 2020/12/08
 */
@RequestMapping(value = "/checkCorporation/v2")
@Slf4j
@RestController
public class CheckCorporationControllerV2 extends BaseController {
    @Reference
    private CheckCorporationAdminApi checkCorporationAdminApi;
    @Reference
    private CheckCorporationBusinessAdminApi checkCorporationBusinessAdminApi;
    @Autowired
    private CheckCorporationRemoteAdapter checkCorporationRemoteAdapter;
    @Reference
    private CorporationAdminApi corporationAdminApi;
    @Reference
    private BusinessCategoryAdminApi businessCategoryAdminApi;
    @Value("${zhiguan.users}")
    private String zhiguanUser;
    @Value("${select.items.flag:false}")
    private Boolean selectItemsFlag;
    @Value("${instrument.categoryIds:21443,21444,37659,37867}")
    private String instrumentCategoryIds;
    @Value("${corp.drug.production.license.regular}")
    private String drugProductionLicenseRegular;
    @Value("${corp.drug.business.license.regular}")
    private String drugBusinessLicenseRegular;

    @PostMapping("authCorporation")
    @ResponseBody
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo authCorporation(@RequestBody CheckCorporationDto bean) {
//        String oaId = getUser().getOaId();
//        //校验当前登录人是否可以审核
//        ApiRPCResult<ErpApprovalProcessRestDto> erpRpcResult = corporationAdminApi.queryAuditNodeInfoNow(bean.getErpProcessId());
//
//        ErpApprovalProcessRestDto erpRpcResultData = erpRpcResult.getData();
//        if (!erpRpcResultData.isStatus()) {
//            return ResponseVo.errRest("流程已结束");
//        }
//        if (!erpRpcResultData.getHandlerId().contains(oaId)) {
//            return ResponseVo.errRest("您不可审核该记录");
//        }
//        if (erpRpcResultData.getHandlerId().contains(",")) {
//            //ID中有逗号说明还未领取需要先领取
//            ApiRPCResult<Boolean> rpcResult = corporationAdminApi.claim(oaId, erpRpcResultData.getTaskId());
//            if (rpcResult.isFail() || !rpcResult.getData()) {
//                return ResponseVo.errRest("认领审核失败");
//            }
//        }
        String userName = this.getUser().getRealName();

        if (!zhiguanUser.contains(userName)){
            return ResponseVo.errRest("审核失败，暂无审核权限！");
        }

        //是否有药品生产或经营许可证
        boolean hasDrugLicense = false;
        List<CheckCorporationQualificationDto> qualificationDtos = bean.getCheckCorporationQualifications();


        bean.setSearch(bean.getName());
        bean.setCreateId(this.getUser().getId());
        bean.setCreateName(this.getUser().getRealName());
        bean.setUpdateId(this.getUser().getId());
        bean.setUpdateName(this.getUser().getRealName());
        bean.setAuditTime(new Date());
        ApiRPCResult<Boolean> rpcResult = checkCorporationAdminApi.checkCorporation(bean);
        if (rpcResult.isFail() || (rpcResult.isSuccess() && !rpcResult.getData())) {
            return ResponseVo.errRest("审核信息更新失败");
        }
        //判断状态,调用审核
        boolean approval = true;
        StringBuffer stringBuffer = new StringBuffer();
        Integer status = bean.getStatus();
        if (status == null) {
            return ResponseVo.errRest("企业基本信息审核未勾选");
        }
        if (CorporationStateEnum.NOT_CERTIFIED.getCode() == status) {
            approval = false;
            stringBuffer.append("企业基本信息:审核未通过(");
            stringBuffer.append(bean.getRemarks() + ")。");
        }

//        //校验是否勾选二类、三类医疗器械细项
//        boolean flag = verifyInstrument(bean.getCheckCorporationBusiness());
//        if (!flag) {
//            return ResponseVo.errRest("请勾选医疗器械细项");
//        }

//        List<CheckCorporationQualificationDto> qualificationDtos = bean.getCheckCorporationQualifications();
        for (CheckCorporationQualificationDto checkCorporationQualification : qualificationDtos) {
            Byte state = checkCorporationQualification.getState();
            if (state == null) {
                return ResponseVo.errRest("资质审核未勾选:" + checkCorporationQualification.getName());
            }
            if (CheckCorporationStateEnum.AUDIT_NO_ADOPT.getCode() == state) {
                approval = false;
                stringBuffer.append(checkCorporationQualification.getName() + ":" + "审核未通过:(" + checkCorporationQualification.getRemarks() + "),");
            }
        }
        if (stringBuffer.length() > 0) {
            stringBuffer.deleteCharAt(stringBuffer.length() - 1);
        }
        if (approval) {
            for (CheckCorporationQualificationDto qualificationDto : qualificationDtos) {
                //校验药品生产或经营许可证号格式
                if (bean.getCorporationType() == CorporationTypeEnum.PRODUCTION.getCode() && qualificationDto.getName().contains("药品生产许可证")) {
                    if (StringUtils.isNotBlank(qualificationDto.getCode()) && !Pattern.matches(drugProductionLicenseRegular, qualificationDto.getCode())) {
                        return ResponseVo.errRest("药品生产许可证证照号格式不规范，请检查");
                    } else if (StringUtils.isNotBlank(qualificationDto.getLegalPersonName()) && !Pattern.matches(drugProductionLicenseRegular, qualificationDto.getLegalPersonName())) {
                        return ResponseVo.errRest("药品生产许可证证照号格式不规范，请检查");
                    }
                    hasDrugLicense = true;
                } else if (bean.getCorporationType() == CorporationTypeEnum.MANAGEMENT.getCode() && qualificationDto.getName().contains("药品经营许可证")) {
                    if (StringUtils.isNotBlank(qualificationDto.getCode()) && !Pattern.matches(drugBusinessLicenseRegular, qualificationDto.getCode())) {
                        return ResponseVo.errRest("药品经营许可证证照号格式不规范，请检查");
                    } else if (StringUtils.isNotBlank(qualificationDto.getLegalPersonName()) && !Pattern.matches(drugBusinessLicenseRegular, qualificationDto.getLegalPersonName())) {
                        return ResponseVo.errRest("药品经营许可证证照号格式不规范，请检查");
                    }
                    hasDrugLicense = true;
                }
            }

            //如果上传了药品生产或经营许可证，则需要校验法人授权委托书有效期不可以超过一年
            if (hasDrugLicense) {
                List<CheckCorporationQualificationDto> corporationQualificationDtos = qualificationDtos.stream().filter(item -> Objects.equals(item.getName(), "法人授权委托书")).collect(Collectors.toList());
                CheckCorporationQualificationDto corporationQualificationDto = CollectionUtils.isEmpty(corporationQualificationDtos) ? null : corporationQualificationDtos.get(0);
                if (corporationQualificationDto != null) {
                    Date startDate = corporationQualificationDto.getStartDate();
                    Date endDate = corporationQualificationDto.getEndDate();
                    Date finalDate = DateUtil.getDate(startDate, 1, Calendar.YEAR);
                    //如果法人授权委托书有效期超过一年，校验不通过
                    if (endDate != null && finalDate != null && endDate.compareTo(finalDate) >= 0) {
                        return ResponseVo.errRest("法人授权委托书有效期不可以超过一年");
                    }
                }
            }



            //各项都审核通过,调用审核通过接口
            ApiRPCResult<Boolean> result = checkCorporationAdminApi.completeTask(bean.getCId(), bean.getBatch(),userName );
            if (result.isFail() || (result.isSuccess() && !result.getData())) {
                return ResponseVo.errRest("审批失败");
            }
            return ResponseVo.successResultNotData("审批成功");
        }
        //调用审批驳回接口
        ApiRPCResult<Boolean> processnewResult = checkCorporationAdminApi.commitProcessnew(bean.getCId(), bean.getBatch(),userName, stringBuffer.toString());
        if (processnewResult.isFail() || (processnewResult.isSuccess() && !processnewResult.getData())) {
            return ResponseVo.errRest("驳回失败");
        }
        return ResponseVo.successResultNotData("驳回成功");
    }

    /**
     * 校验是否勾选二类、三类医疗器械细项
     *
     * @param checkCorporationBusiness
     * @return
     */
    private boolean verifyInstrument(List<CheckCorporationBusinessDto> checkCorporationBusiness) {
        if (CollectionUtils.isEmpty(checkCorporationBusiness)) {
            return true;
        }
        //如果有II类医疗器械(新版)、III类医疗器械(新版)、II类医疗器械、III类医疗器械，则对应的细项必须至少勾选一项(必须勾选到最细粒度)
        if (StringUtils.isBlank(instrumentCategoryIds)) {
            return true;
        }
        List<Long> instrumentCategoryIdList = Splitter.on(",").splitToList(instrumentCategoryIds).stream().map(Long::parseLong).collect(Collectors.toList());
        //是否存在II类医疗器械(新版)、III类医疗器械(新版)、II类医疗器械、III类医疗器械的数据 一级数据
        List<Long> firstCategoryIds = checkCorporationBusiness.stream().filter(f -> instrumentCategoryIdList.contains(f.getCategoryId())).map(m -> m.getCategoryId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(firstCategoryIds)) {
            return true;
        }

        //<firstCategoryId, 对应的二级分类数量> 如果有一级分类，看是否有二级分类数据，没有的返回false
        Map<Long, Long> firstCategoryIdCountMap = checkCorporationBusiness.stream().filter(f -> firstCategoryIds.contains(f.getPId())).collect(Collectors.groupingBy(item -> item.getPId(), Collectors.counting()));
        if (MapUtils.isEmpty(firstCategoryIdCountMap)) {
            return false;
        }
        //某个一级分类下，没有二级分类
        List<Long> noSecondCategoryList = firstCategoryIdCountMap.values().stream().filter(item -> item <= 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noSecondCategoryList)) {
            return false;
        }

        //二级分类id
        List<Long> secondCategoryIds = checkCorporationBusiness.stream().filter(f -> firstCategoryIds.contains(f.getPId())).map(m -> m.getCategoryId()).distinct().collect(Collectors.toList());
        //<secondCategoryId, 对应的三级分类数量> 如果有二级分类，看是否有三级分类数据，没有的返回false
        Map<Long, Long> secondCategoryIdCountMap = checkCorporationBusiness.stream().filter(f -> secondCategoryIds.contains(f.getPId())).collect(Collectors.groupingBy(item -> item.getPId(), Collectors.counting()));
        if (MapUtils.isEmpty(secondCategoryIdCountMap)) {
            return false;
        }
        //某个二级分类下，没有三级分类
        List<Long> noThirdCategoryList = secondCategoryIdCountMap.values().stream().filter(item -> item <= 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noThirdCategoryList)) {
            return false;
        }

        return true;
    }

    @GetMapping("queryCorporation")
    public ResponseVo queryCorporation(Long id, String batch) {
        if (null == id || StringUtils.isBlank(batch)) {
            return ResponseVo.errRest("缺少重要参数");
        }
        ApiRPCResult<CheckCorporationDto> apiRPCResult = checkCorporationAdminApi.queryCheckCorporation(id, batch);
        if (apiRPCResult.isFail()) {
            return ResponseVo.errRest("查询失败");
        }
        //医疗器械二类和三类若勾选，对应的细项是否默认勾选开关：true-不勾选 false-勾选
        apiRPCResult.getData().setSelectItemsFlag(selectItemsFlag);
        return ResponseVo.successResult(apiRPCResult.getData());
    }

    /**
     * 查询申请和当前资质的不同
     *
     * @param id    机构id
     * @param batch
     * @return
     */
    @GetMapping("queryModified")
    public ResponseVo queryModified(@RequestParam("cId") Long id, String batch) {
        try {
            log.info("CheckCorporationControllerV2.queryModified#id:{},batch:{}", id, batch);
            ApiRPCResult<CheckCorporationDto> apiRPCResult = checkCorporationAdminApi.queryCheckCorporation(id, batch);
            ApiRPCResult<CorporationDto> corResult = corporationAdminApi.queryCorporation(id);
            if (apiRPCResult.isFail() || corResult.isFail()) {
                log.warn("checkCorporationAdminApi.queryCheckCorporation#id:{},batch:{},return :{}", id, batch, JSON.toJSONString(apiRPCResult));
                log.warn("corporationAdminApi.queryCorporation#id:{},return :{}", id, JSON.toJSONString(corResult));
                return ResponseVo.errRest("查询变更项失败");
            }
            return ResponseVo.successResult(CheckCorporationModifyHelper.compare(apiRPCResult.getData(), corResult.getData()));
        } catch (Exception e) {
            log.error("CheckCorporationControllerV2.queryModified#id:{},batch:{}", id, batch);
            return ResponseVo.errRest("查询变更项异常");
        }
    }

    /**
     * 经营范围类目树
     *
     * @param corporationType
     * @return
     */
    @ResponseBody
    @RequestMapping("/getBusinessTree")
    public ResponseVo getBusinessTree(Integer cId, Integer corporationType, Long category,String batch) {
        try {
            if (null == cId || corporationType == null || null == category||StringUtils.isEmpty(batch)) {
                return ResponseVo.errRest("缺少参数");
            }
            ApiRPCResult<List<BusinessCategoryDictDto>> scopeTree = businessCategoryAdminApi.getBusinessTree(corporationType);
            if (scopeTree.isFail()) {
                log.warn("获取经营类目字典信息失败,error:{}", scopeTree.getErrMsg());
                return ResponseVo.errRest("获取经营类目字典信息失败");
            }
            List<BusinessCategoryDictDto> businessScopeTree = scopeTree.getData();
            ApiRPCResult<List<CheckCorporationBusinessDto>> apiRPCResult = checkCorporationBusinessAdminApi.listCorporationBusiness(cId,batch);
            if (apiRPCResult.isFail()) {
                log.warn("获取经营类目信息失败,error:{}", apiRPCResult.getErrMsg());
                return ResponseVo.errRest("获取经营类目信息失败");
            }
            List<CheckCorporationBusinessDto> businessDtos = apiRPCResult.getData();
            List<ZTree> arrayList = new ArrayList<>();
            List<Long> categoryIdList = businessDtos.stream().map(sellerCorporationBusinessDto1 -> sellerCorporationBusinessDto1.getCategoryId()).collect(Collectors.toList());
            for (BusinessCategoryDictDto businessCategoryDictDto : businessScopeTree) {
                if (!category.equals(businessCategoryDictDto.getCategoryId())) {
                    continue;
                }
                Long categoryId = businessCategoryDictDto.getCategoryId();
                String categoryName = businessCategoryDictDto.getCategoryName();
                ZTree zTree = new ZTree();
                zTree.setId(categoryId);
                zTree.setName(categoryName);
                if (categoryIdList.contains(categoryId)) {
                    zTree.setChecked(true);
                }
                zTree.setCheckDisable(true);
                List<BusinessScopeDto> businessScopeDtoList = businessCategoryDictDto.getBusinessScopeDtoList();
                //设定树型结构中只允许修改同种类型下类目,"其他" 类目除外  specialCategoryList
                for (BusinessScopeDto businessScopeDto : businessScopeDtoList) {
                    Long businessScopeDtoId = businessScopeDto.getId();
                    if (categoryIdList.contains(businessScopeDtoId)) {
                        zTree.setCheckDisable(false);
                    }
                    if (categoryIdList.contains(businessScopeDtoId)) {
                        zTree.setChecked(true);
                    }
                }
                //如果树结构下有选中数据那么相应类目也选中
                if (!zTree.isChecked()) {
                    zTree.setChecked(true);
                }
                List<ZTree> relation = ZTreeByCategoryRelation.getZTreeByCategoryRelation(businessScopeDtoList, categoryIdList);
                zTree.setChildren(relation);
                arrayList.add(zTree);
            }
            return ResponseVo.successResult(arrayList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ResponseVo.errRest("获取失败");
    }

    @GetMapping("/listCorporation")
    @ResponseBody
    public ResponseVo listCorporation(QueryCheckCorporationParam queryCheckCorporation) {
        List<Long> provIds = getProvIds(queryCheckCorporation.getProvId());
        if(CollectionUtils.isEmpty(provIds)){
            PageInfo<CheckCorporationAndUserExtDto> pageInfo = new PageInfo<>();
            pageInfo.setPageNum(queryCheckCorporation.getPageNum());
            pageInfo.setPageSize(queryCheckCorporation.getPageSize());
            pageInfo.setTotal(0);
            pageInfo.setList(Lists.newArrayList());
            return ResponseVo.successResult(pageInfo);
        }
        queryCheckCorporation.setProvIds(provIds);
        ApiRPCResult<PageInfo<CheckCorporationAndUserExtDto>> rpcResult = checkCorporationAdminApi.listCorporations(queryCheckCorporation);
        if (rpcResult.isFail()) {
            return ResponseVo.errRest("查询数据异常");
        }
        return ResponseVo.successResult(rpcResult.getData());
    }

    @ResponseBody
    @RequestMapping(value = "/checkHis", method = {RequestMethod.POST}, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResponseVo<List<CheckCorporationVo>> checkHis(@RequestParam String orgId) {
        try {
            List<CheckCorporationDto> list = checkCorporationRemoteAdapter.listCorporations(orgId);
            List<CheckCorporationVo> result = CheckCorporationConverHelper.convertToCheckVo(list);
            if (CollectionUtils.isNotEmpty(result)){
                result.stream().forEach(o ->{
                    if (o.getState() == (byte) CheckCorporationStateEnum.WAIT_AUDIT.getCode()){
                        o.setUpdateTime(null);
                        o.setUpdateName(null);
                    }
                });
            }
            return ResponseVo.successResult(result);
        } catch (Exception e) {
            log.error("企业信息申请记录查询失败：{}", orgId, e);
            return ResponseVo.errRest("查询失败");
        }

    }
}
