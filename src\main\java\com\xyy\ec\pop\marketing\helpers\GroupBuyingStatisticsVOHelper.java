package com.xyy.ec.pop.marketing.helpers;

import com.xyy.ec.marketing.elephant.results.MarketingGroupBuyingStatisticsDTO;
import com.xyy.ec.pop.marketing.vo.GroupBuyingStatisticsVO;

import java.util.Objects;

public class GroupBuyingStatisticsVOHelper {

    public static GroupBuyingStatisticsVO create(MarketingGroupBuyingStatisticsDTO marketingGroupBuyingStatisticsDTO) {
        if (Objects.isNull(marketingGroupBuyingStatisticsDTO)) {
            return null;
        }
        return GroupBuyingStatisticsVO.builder()
                .platformSubsidy(marketingGroupBuyingStatisticsDTO.getPlatformSubsidy())
                .build();
    }
}
