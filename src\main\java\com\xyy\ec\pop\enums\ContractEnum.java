package com.xyy.ec.pop.enums;

/**
 * @Description:
 * @Auther: WanKp
 * @Date: 2018/8/26 22:06
 **/
public enum ContractEnum {

    STATUS_NO(0,"无效"),
    STATUS_YES(1,"有效"),
    TYPE_APPENDDICES(1,"合同附件"),
    TYPE_AGREEMENT(2,"补充协议");
    private Integer key ;
    private  String value;

    ContractEnum(Integer key, String value){
        this.key = key;
        this.value = value;
    }
    public String getValue() {
        return value;
    }

    public Integer getKey() {
        return key;
    }


}
