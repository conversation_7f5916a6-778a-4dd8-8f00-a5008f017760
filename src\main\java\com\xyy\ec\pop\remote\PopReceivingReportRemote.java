package com.xyy.ec.pop.remote;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.dto.OperateMarginLogDto;
import com.xyy.ec.pop.server.api.seller.api.PopReceivingReportApi;
import com.xyy.ec.pop.server.api.seller.dto.PopReceivingReportDto;
import com.xyy.ec.pop.server.api.seller.param.PopReceivingReportParam;
import com.xyy.ec.pop.utils.PageInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

@Slf4j
@Component
public class PopReceivingReportRemote {

    @Reference
    private PopReceivingReportApi receivingReportApi;

    public PageInfo<PopReceivingReportDto> popReceivingReportList(PopReceivingReportParam reportParam) {
        ApiRPCResult<Boolean> result;
        try {
            log.info("PopReceivingReportRemote.popReceivingReportList(reportParam:{})", JSON.toJSONString(reportParam));
            ApiRPCResult<PageInfo<PopReceivingReportDto>> rpcResult = receivingReportApi.popReceivingReportList(reportParam);
            log.info("PopReceivingReportRemote.popReceivingReportList(reportParam:{}) return {}",JSON.toJSONString(reportParam), JSON.toJSONString(rpcResult));
            return rpcResult.isSuccess() ? rpcResult.getData() : PageInfoUtils.pageInfo(new ArrayList<>(),0,1,reportParam.getLimit());
        } catch (Exception e) {
            log.error("PopReceivingReportRemote.popReceivingReportList(reportParam:{}) 异常",JSON.toJSONString(reportParam), e);
            return PageInfoUtils.pageInfo(new ArrayList<>(), 0, 1, reportParam.getLimit());
        }
    }

    public void syncMerchant() {
        receivingReportApi.syncMerchant();
    }

    public void updateByPrimaryKeySelective(PopReceivingReportDto popReceivingReportDto){
        try {
            receivingReportApi.updateByPrimaryKeySelective(popReceivingReportDto);
        } catch (Exception e) {
            log.error("PopReceivingReportRemote.updateByPrimaryKeySelective(popReceivingReportDto:{}) 异常",JSON.toJSONString(popReceivingReportDto), e);
        }
    }

    public BigDecimal popReceivingReportSummation(PopReceivingReportParam receivingReportParam) {
        try {
            ApiRPCResult<BigDecimal> apiRPCResult = receivingReportApi.popReceivingReportSummation(receivingReportParam);
            if (apiRPCResult != null && apiRPCResult.isSuccess()){
                return apiRPCResult.getData();
            }
        } catch (Exception e) {
            log.error("PopReceivingReportRemote.popReceivingReportSummation(popReceivingReportDto:{}) 异常",JSON.toJSONString(receivingReportParam), e);
        }
        return BigDecimal.ZERO;
    }
}
