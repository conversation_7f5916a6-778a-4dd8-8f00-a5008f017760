package com.xyy.ec.pop.excel.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@NoArgsConstructor
@Data
public class RefundExportVo implements Serializable {
    private static final long serialVersionUID = 8435616855797077323L;

    @Excel(name = "商户编号",width = 15)
    private String orgId;

    @Excel(name = "商户名称",width = 15)
    private String companyName;

    @Excel(name = "店铺名称",width = 15)
    private String businessName;

    @Excel(name = "客户名称",width = 15)
    private String merchantName;

    @Excel(name = "客户erp编码",width = 15)
    private String merchantErpCode;
    @Excel(name = "省份",width = 15)
    private String branchName;

    @Excel(name = "销售单号",width = 15)
    private String orderNo;

    @Excel(name = "订单支付类型",width = 18)
    public String payType;

    @Excel(name = "支付渠道",width = 18,replace={"支付宝_1","微信_2","银联_3","_null"})
    public Integer payChannel;

    @Excel(name = "退款单号",width = 15)
    private String refundOrderNo;

    @Excel(name = "退款单金额",width = 15)
    private BigDecimal refundFee;

    @Excel(name = "退款单状态",width = 15)
    private String auditStatusName;
    @Excel(name = "发起方",width = 15,replace={"用户发起_1","客服介入_2","司机发起_3","商家发起_4","系统发起_5","_null"})
    private Integer refundChannel;

    @Excel(name = "退款原因",width = 15)
    private String refundReason;

    @Excel(name = "退款说明",width = 15)
    private String refundExplain;


    @Excel(name = "下单时间",width = 15,format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "申请退款时间",width = 18,format = "yyyy-MM-dd HH:mm:ss")
    private Date refundCreateTime;

    @Excel(name = "退款账户名",width = 15)
    private String owner;

    @Excel(name = "账号",width = 15)
    private String bankCard;

    @Excel(name = "开户行",width = 15)
    private String bankName;

    @Excel(name = "退款时间",width = 15,format = "yyyy-MM-dd HH:mm:ss")
    private Date refundAuditTime;
}
