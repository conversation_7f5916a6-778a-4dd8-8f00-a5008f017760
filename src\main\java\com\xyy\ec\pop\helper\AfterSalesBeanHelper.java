package com.xyy.ec.pop.helper;

import com.xyy.ec.order.business.dto.afterSales.AfterSalesDetailVo;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesProductCredentialVo;
import com.xyy.ec.order.business.enums.afterSales.AfterSalesSubTypeEnum;
import com.xyy.ec.order.business.enums.afterSales.AfterSalesTypeEnum;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.afterSales.AfterSalesDetailInfo;
import com.xyy.ec.pop.vo.afterSales.AfterSalesInfoVo;
import com.xyy.ec.pop.vo.afterSales.SpecialInvoiceInfoVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

public class AfterSalesBeanHelper {

    public static AfterSalesInfoVo convert(AfterSalesDetailVo vo) {
        AfterSalesInfoVo afterSalesInfoVo = new AfterSalesInfoVo();
        afterSalesInfoVo.setOrderNo(vo.getOrderNo());
        afterSalesInfoVo.setAfterSalesNo(vo.getAfterSalesNo());
        afterSalesInfoVo.setMerchantName(vo.getMerchantName());
        afterSalesInfoVo.setSellerName(vo.getCompanyName());
        afterSalesInfoVo.setSubType(vo.getSubType());
        afterSalesInfoVo.setProvinceCode(vo.getProvinceCode());
        afterSalesInfoVo.setSellerCountDownTime(vo.getSellerCountDownTime());
        if (vo.getSubType() != null && AfterSalesSubTypeEnum.INCORRECT_INVOICE.getKey().equals(vo.getSubType())) {
            afterSalesInfoVo.setReason("发票有误信息-"+vo.getIncorrectInvoiceInfo());
        } else {
            afterSalesInfoVo.setReason(vo.getIncorrectInvoiceInfo());
        }

//        afterSalesInfoVo.setAmount();
        afterSalesInfoVo.setRemark(vo.getMerchantRemark());
        afterSalesInfoVo.setAfterSalesType(vo.getAfterSalesType());
        afterSalesInfoVo.setAfterSalesTypeName(vo.getAfterSalesTypeName());
        afterSalesInfoVo.setAuditProcessState(vo.getAuditProcessState());
        afterSalesInfoVo.setCreateTime(DateUtil.date2Str(vo.getCreateTime(),DateUtil.PATTERN_STANDARD));
//        afterSalesInfoVo.setCountDownTime();
//        afterSalesInfoVo.setTips();


        AfterSalesDetailInfo afterSalesDetailInfo = new AfterSalesDetailInfo();
        afterSalesDetailInfo.setAfterSalesTypeName(vo.getAfterSalesTypeName());
        //专票
        if (AfterSalesTypeEnum.INVOICE.getKey().equals(vo.getAfterSalesType()) && AfterSalesSubTypeEnum.SPECIAL_INVOICE.getKey().equals(vo.getSubType())) {

            afterSalesDetailInfo.setSpecialInvoiceTitle("");
            SpecialInvoiceInfoVo specialInvoiceInfoVo = new SpecialInvoiceInfoVo();
            BeanUtils.copyProperties(vo.getSpecialInvoiceInfo(),specialInvoiceInfoVo);
            afterSalesDetailInfo.setSpecialInvoiceInfo(specialInvoiceInfoVo);
        }
        if (AfterSalesTypeEnum.CREDENTIAL.getKey().equals(vo.getAfterSalesType())) {
            final String corpCredential = vo.getCorpCredential();
            List list = new ArrayList();
            if (StringUtils.isNotEmpty(corpCredential)) {
                list.add("企业资质："+corpCredential);
            }
            final List<AfterSalesProductCredentialVo> productCredentialList = vo.getProductCredentialList();
            if (CollectionUtils.isNotEmpty(productCredentialList)) {
                StringBuilder drugSupervisionReport = new StringBuilder();
                StringBuilder productCredentialSb = new StringBuilder();

                for (AfterSalesProductCredentialVo afterSalesProductCredentialVo : productCredentialList) {

                    if (afterSalesProductCredentialVo.getCredentialType().contains("1")) {
                        drugSupervisionReport.append(afterSalesProductCredentialVo.getProductName()+"("+afterSalesProductCredentialVo.getSpec()+"),");
                    }
                    if (afterSalesProductCredentialVo.getCredentialType().contains("2")) {
                        productCredentialSb.append(afterSalesProductCredentialVo.getProductName()+"("+afterSalesProductCredentialVo.getSpec()+"),");
                    }

                }
                list.add("商品首营资质："+productCredentialSb.toString());
                list.add("商品药监报告："+drugSupervisionReport.toString());
            }
            afterSalesDetailInfo.setItemList(list);
        }
        afterSalesInfoVo.setAfterSalesDetailInfo(afterSalesDetailInfo);
        return afterSalesInfoVo;

    }
}
