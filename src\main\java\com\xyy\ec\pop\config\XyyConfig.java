package com.xyy.ec.pop.config;

import com.xyy.ec.pop.vo.LogisticsWayVo;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.*;

/**
 * 供admin端读取的配置文件
 */
@Configuration
@Getter
@Setter
public class XyyConfig {
    @Value("${fastdfs.host}")
    private String fastHostUrl;
    @Value("${base_path_url}")
    public static String basePathUrl;

    public static Long t_v = new Date().getTime();

    @Value("${seckill_activity_category_id}")
    public static String seckillActivityCategoryId;

    @Value("${erp_url}")
    public static String erpUrl;

    @Value("${es_url}")
    public static String esUrl;

    @Value("${environment}")
    private static String environment;

    @Value("${remote_order_extend_url}")
    public String remoteOrderExtendUrl;

    @Value("${export_warning_count}")
    public int exportWarningCount;

    @Value("${export_warning_msg}")
    public String exportWarningMsg;

    @Value("${ecHost}")
    public String ecHost;

    @Value("${ecAppHost}")
    public String ecAppHost;

    @Value("${max_export_data_num}")
    private Integer maxExportDataNum;

    @Value("${orderRefundExportTempPath}")
    private String orderRefundExportTempPath;

    @Value("${logistics}")
    private String logistics;

    @Value("${cos.upload.pop.domain}")
    private String cosUrl;

    public List<LogisticsWayVo> getLogisticsList() {
        List<LogisticsWayVo> logisticsList = new ArrayList<>();
        List<String> logisticSplitList = Arrays.asList(logistics.split(","));
        for (String item : logisticSplitList) {
            String[] split = item.split("-");
            logisticsList.add(LogisticsWayVo.builder().code(split[0]).name(split[1]).build());
        }
        return logisticsList;
    }

    public String getLogisticsNameByCode(String code) {
        List<LogisticsWayVo> logisticsList = getLogisticsList();
        Optional<String> first = logisticsList.stream().filter(f -> f.getCode().equals(code)).map(m -> m.getName()).findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        return StringUtils.EMPTY;
    }


    public String getOrderRefundExportTempPath() {
        return orderRefundExportTempPath;
    }

    public void setOrderRefundExportTempPath(String orderRefundExportTempPath) {
        this.orderRefundExportTempPath = orderRefundExportTempPath;
    }

    public String getEcAppHost() {
        return ecAppHost;
    }

    public void setEcAppHost(String ecAppHost) {
        this.ecAppHost = ecAppHost;
    }

    public Integer getMaxExportDataNum() {
        return maxExportDataNum;
    }

    public void setMaxExportDataNum(Integer maxExportDataNum) {
        this.maxExportDataNum = maxExportDataNum;
    }

    public int getExportWarningCount() {
        return exportWarningCount;
    }

    public void setExportWarningCount(int exportWarningCount) {
        this.exportWarningCount = exportWarningCount;
    }

    public String getExportWarningMsg() {
        return exportWarningMsg;
    }

    public void setExportWarningMsg(String exportWarningMsg) {
        this.exportWarningMsg = exportWarningMsg;
    }

    public void setRemoteOrderExtendUrl(String remoteOrderExtendUrl) {
        this.remoteOrderExtendUrl = remoteOrderExtendUrl;
    }

    public String getRemoteOrderExtendUrl() {
        return remoteOrderExtendUrl;
    }

    public String getEcHost() {
        return ecHost;
    }

    public void setEcHost(String ecHost) {
        this.ecHost = ecHost;
    }

    @Bean
    public CdnConfig getCdnConfig() {
        return new CdnConfig();
    }

    @Bean
    public JpushConfig getJpushConfig() {
        return new JpushConfig();
    }

    @Bean
    public AliyunSmsConfig getAliyunSmsConfig() {
        return new AliyunSmsConfig();
    }

    @Bean
    public SunNetClientConfig getSunNetClientConfig() {
        return new SunNetClientConfig();
    }

    public static Long getT_v() {
        return t_v;
    }

    public static String getBasePathUrl() {
        return basePathUrl;
    }

    public static String getSeckillActivityCategoryId() {
        return seckillActivityCategoryId;
    }

    public static String getErpUrl() {
        return erpUrl;
    }

    public static String getEsUrl() {
        return esUrl;
    }

    public class CdnConfig {
        @Value("${cdn_uploadPath}")
        private String cdnUploadPath;
        @Value("${cdn_hostname}")
        private String cdnHostname;
        @Value("${cdn_port}")
        private String cdnPort;
        @Value("${cdn_username}")
        private String cdnUsername;
        @Value("${cdn_password}")
        private String cdnPassword;
        @Value("${cdn_hostname_show}")
        private String showUrl;

        @Value("${orderRefundExcelCdnPath}")
        private String orderRefundExcelCdnPath;
        private CdnConfig() {
        }

        public String getCdnUploadPath() {
            return cdnUploadPath;
        }

        public String getCdnHostname() {
            return cdnHostname;
        }

        public String getCdnPort() {
            return cdnPort;
        }

        public String getCdnUsername() {
            return cdnUsername;
        }

        public String getCdnPassword() {
            return cdnPassword;
        }

        public String getShowUrl() {
            return showUrl;
        }
        public String getCosUrl() {
            return cosUrl;
        }


        public String getOrderRefundExcelCdnPath() {
            return orderRefundExcelCdnPath;
        }

        public void setOrderRefundExcelCdnPath(String orderRefundExcelCdnPath) {
            this.orderRefundExcelCdnPath = orderRefundExcelCdnPath;
        }
    }

    public class JpushConfig {
        @Value("${jpush.appKey.dev}")
        private String jpushAppKeyDev;

        @Value("${jpush.masterSecret.dev}")
        private String jpushMasterSecretDev;

        @Value("${jpush.appKey.prd}")
        private String jpushAppKeyPrd;

        @Value("${jpush.apnsProduction}")
        private boolean jpushApnsProduction;


        private JpushConfig() {
        }

        public String getJpushAppKeyDev() {
            return jpushAppKeyDev;
        }

        public String getJpushMasterSecretDev() {
            return jpushMasterSecretDev;
        }

        public String getJpushAppKeyPrd() {
            return jpushAppKeyPrd;
        }

        public boolean isJpushApnsProduction() {
            return jpushApnsProduction;
        }
    }

    public class AliyunSmsConfig {
        @Value("${aliyun_sms_access_key_id}")
        private String aliYunSmsAccessKeyId;
        @Value("${aliyun_sms_access_key_secret}")
        private String aliyunSmsAccessKeySecret;
        @Value("${aliyun_sms_sign_name}")
        private String aliyunSmsSignName;

        private AliyunSmsConfig() {
        }

        public String getAliYunSmsAccessKeyId() {
            return aliYunSmsAccessKeyId;
        }

        public String getAliyunSmsSignName() {
            return aliyunSmsSignName;
        }
    }

    public class SunNetClientConfig {
        @Value("${sun.net.client.defaultConnectTimeout}")
        private int defaultConnectTimeout;
        @Value("${sun.net.client.defaultReadTimeout}")
        private int defaultReadTimeout;

        private SunNetClientConfig() {
        }

        public int getDefaultConnectTimeout() {
            return defaultConnectTimeout;
        }

        public int getDefaultReadTimeout() {
            return defaultReadTimeout;
        }
    }
}
