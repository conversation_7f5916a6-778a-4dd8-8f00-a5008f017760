package com.xyy.ec.pop.utils.fastdfs;

import com.xyy.ec.pop.config.FastDfsConfig;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import org.csource.common.NameValuePair;
import org.csource.fastdfs.StorageClient1;
import org.csource.fastdfs.TrackerServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 封装FDFS java api
 * 
 * <AUTHOR>
 * @date 20191113
 */
@Component
public class FDFSClient {

	/* 日志 */
	private static final Logger logger = LoggerFactory.getLogger(FDFSClient.class);
	/* 路径分隔符 */
	public static final String SEPARATOR = "/";
	/* point */
	public static final String POINT = ".";
	/* ContentType */
	public static final Map<String, String> EXT_MAPS = new HashMap<>();
	/* 文件名称Key */
	private static final String FILENAME = "filename";
	/* 文件名称Key */
	private static final String FILE_EXT_NAME = "fileextname";
	/**
	 * 文件后缀key
	 */
	private static final String SUFFIX = "suffix";

	@Autowired
	private FastDfsConfig fastDfsConfig;

	public FDFSClient() {
		initExt();
	}

	/* init 文件名 */
	private void initExt() {
		// image
		EXT_MAPS.put("png", "image/png");
		EXT_MAPS.put("gif", "image/gif");
		EXT_MAPS.put("bmp", "image/bmp");
		EXT_MAPS.put("ico", "image/x-ico");
		EXT_MAPS.put("jpeg", "image/jpeg");
		EXT_MAPS.put("jpg", "image/jpeg");
		// 压缩文件
		EXT_MAPS.put("zip", "application/zip");
		EXT_MAPS.put("rar", "application/x-rar");
		// doc
		EXT_MAPS.put("pdf", "application/pdf");
		EXT_MAPS.put("ppt", "application/vnd.ms-powerpoint");
		EXT_MAPS.put("xls", "application/vnd.ms-excel");
		EXT_MAPS.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		EXT_MAPS.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
		EXT_MAPS.put("doc", "application/msword");
		EXT_MAPS.put("doc", "application/wps-office.doc");
		EXT_MAPS.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
		EXT_MAPS.put("txt", "text/plain");
		// 音频
		EXT_MAPS.put("mp4", "video/mp4");
		EXT_MAPS.put("flv", "video/x-flv");
	}

	/**
	 * 添加图片地址名称 
	 * @param urlName
	 * @return
	 */
	private String addHttpHost(String urlName){
		String urlAll = null;
		if(urlName != null){
			urlAll = fastDfsConfig.getHost() + urlName;
		}
		return urlAll;
	}

	/**
	 * 原始上传文件
	 *
	 * @param filename 上传文件名
	 * @param fileInputStream
	 * @return
	 */
	public String upload(InputStream fileInputStream, String filename) {
		return upload(fileInputStream,filename,null);
	}

	/**
	 * 原始上传文件
	 *
	 * @param filename 上传文件名
	 * @param file
	 * @return
	 */
	public String upload(File file, String filename) {
		try {
			return upload(new FileInputStream(file),filename,null);
		} catch (FileNotFoundException e) {
			logger.warn("upload",e);
			throw new XyyEcPopException("文件上传失败");
		}
	}

	/**
	 * 原始上传文件
	 *
	 * @param filename 上传文件名
	 * @param inputStream
	 * @param fileExtName
	 * @return
	 */
	public String upload(InputStream inputStream, String filename, String fileExtName) {
		// 返回路径
		String path = null;
		// 文件描述
		NameValuePair[] nvps = setFileNamePair(filename,fileExtName);
		// 文件名后缀
		String suffix = getFilenameSuffix(filename);
		TrackerServer trackerServer = null;
		try {
			trackerServer = TrackerServerPool.borrowObject(fastDfsConfig);
			StorageClient1 storageClient = new StorageClient1(trackerServer, null);
			// 读取流
			byte[] fileBuff = new byte[inputStream.available()];
			inputStream.read(fileBuff, 0, fileBuff.length);
			// 上传
			path = storageClient.upload_file1(fileBuff, suffix, nvps);
		} catch (Exception e) {
			logger.warn("fast dfs upload",e);
			throw new XyyEcPopException(e);
		} finally {
			try {
				if(trackerServer != null) {
					TrackerServerPool.returnObject(trackerServer, fastDfsConfig);
				}
			}catch (Exception e){
				logger.warn("TrackerServerPool.returnObject",e);
			}
			// 关闭流
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					logger.warn("fileInputStream.close()",e);
				}
			}
		}
		return path;
	}


	/**
	 * 转换路径中的 '\' 为 '/' <br>
	 * 并把文件后缀转为小写
	 *
	 * @param path
	 *            路径
	 * @return
	 */
	public static String toLocal(String path) {
		if (!StringUtils.isEmpty(path)) {
			path = path.replaceAll("\\\\", SEPARATOR);

			if (path.contains(POINT)) {
				String pre = path.substring(0, path.lastIndexOf(POINT) + 1);
				String suffix = path.substring(path.lastIndexOf(POINT) + 1).toLowerCase();
				path = pre + suffix;
			}
		}
		return path;
	}

	/**
	 * 获取文件名称的后缀
	 *
	 * @param filename
	 *            文件名 或 文件路径
	 * @return 文件后缀
	 */
	public static String getFilenameSuffix(String filename) {
		String suffix = null;
		String originalFilename = filename;
		if (!StringUtils.isEmpty(filename)) {
			if (filename.contains(SEPARATOR)) {
				filename = filename.substring(filename.lastIndexOf(SEPARATOR) + 1);
			}
			if (filename.contains(POINT)) {
				suffix = filename.substring(filename.lastIndexOf(POINT) + 1);
			} else {
				if (logger.isErrorEnabled()) {
					logger.error("filename error without suffix : {}", originalFilename);
				}
			}
		}
		return suffix;
	}

	/**
	 * 设置fastdfs上传文件基础属性
	 * @param fileName
	 * @return
	 */
	private NameValuePair[] setFileNamePair(String fileName) {
		// 文件描述
		NameValuePair[] nvps = null;
		List<NameValuePair> nvpsList = new ArrayList<>();
		// 文件名
		if (!StringUtils.isEmpty(fileName)) {
			nvpsList.add(new NameValuePair(FILENAME, fileName));
		}
		if (nvpsList.size() > 0) {
			nvps = new NameValuePair[nvpsList.size()];
			nvpsList.toArray(nvps);
		}
		return nvps;
	}

	/**
	 * 设置fastdfs上传文件基础属性
	 * @param fileName
	 * @param fileExtName
	 * @return
	 */
	private NameValuePair[] setFileNamePair(String fileName,String fileExtName) {
		// 文件描述
		NameValuePair[] nvps = null;
		List<NameValuePair> nvpsList = new ArrayList<>();
		// 文件名
		if (!StringUtils.isEmpty(fileName)) {
			nvpsList.add(new NameValuePair(FILENAME, fileName));
		}
		if(!StringUtils.isEmpty(fileExtName)){
			nvpsList.add(new NameValuePair(FILE_EXT_NAME, fileExtName));
		}
		if (nvpsList.size() > 0) {
			nvps = new NameValuePair[nvpsList.size()];
			nvpsList.toArray(nvps);
		}
		return nvps;
	}
	public byte[] downInputStream(String  groupName,String remoteFileName) {
		TrackerServer trackerServer = null;
		try {
			trackerServer = TrackerServerPool.borrowObject(fastDfsConfig);
			StorageClient1 storageClient = new StorageClient1(trackerServer, null);
			byte[] bytes = storageClient.download_file(groupName,remoteFileName);
			return bytes;
		} catch (Exception e) {
			logger.warn("fast dfs upload", e);
			throw new XyyEcPopException(e);
		} finally {
			try {
				if (trackerServer != null) {
					TrackerServerPool.returnObject(trackerServer, fastDfsConfig);
				}
			} catch (Exception e) {
				logger.warn("TrackerServerPool.returnObject", e);
			}
		}
	}
	
	public void download(HttpServletResponse response, String path, String fileName) {
		try(OutputStream out =response.getOutputStream()) {
			response.reset();
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(),"iso-8859-1"));
			byte[] bytes = download(path);
			out.write(bytes);
		} catch (IOException e) {
			logger.error("FDFSClient.download()出错",e);
		}
	}

	public byte[] download(String path) {
		TrackerServer trackerServer = null;
		try {
			trackerServer = TrackerServerPool.borrowObject(fastDfsConfig);
			StorageClient1 storageClient = new StorageClient1(trackerServer, null);
			byte[] bytes = storageClient.download_file1(path);
			return bytes;
		} catch (Exception e) {
			logger.warn("fast dfs upload",e);
			throw new XyyEcPopException(e);
		} finally {
			try {
				if(trackerServer != null) {
					TrackerServerPool.returnObject(trackerServer, fastDfsConfig);
				}
			}catch (Exception e){
				logger.warn("TrackerServerPool.returnObject",e);
			}
		}
	}
}
