/**
 * Copyright (C), 2015-2019, XXX有限公司
 * FileName: OrderRefundExportVO
 * Author:   danshiyu
 * Date:     2019/11/21 9:56
 * Description: 退款单导出实体
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.xyy.ec.pop.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.xyy.ec.pop.utils.DateUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈退款单导出实体〉
 *
 * <AUTHOR>
 * @create 2019/11/21
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@ToString
public class OrderRefundExportVO implements Serializable{

    private static final long serialVersionUID = -6176586835867842904L;

    private String branchCode;

    private Long merchantId;

    @Excel(name = "商户编号",width = 10)
    private String orgId;

    @Excel(name = "商户名称",width = 15)
    private String orgName;

    @Excel(name = "订单编号",width = 20)
    private String orderNo;

    @Excel(name = "退款单编号",width = 20)
    private String refundOrderNo;

    /**
     * 支付类型
     */

    private Integer payType;
    @Excel(name = "支付类型",width = 10)
    private String payTypeText;

    private String merchantName;
    @Excel(name = "客户名称",width = 20)
    private String realName;

    @Excel(name = "申请日期",width = 20,format = DateUtil.PATTERN_STANDARD)
    private Date refundCreateTime;
    @Excel(name = "审核日期",width = 20,format = DateUtil.PATTERN_STANDARD)
    private Date customerAuditTime;
    @Excel(name = "退款日期",width = 20,format = DateUtil.PATTERN_STANDARD)
    private Date refundAuditTime;

    @Excel(name = "退款金额",width = 10)
    private BigDecimal refundFee;
    private BigDecimal refundActualFee;

    private Integer refundChannel;
    @Excel(name = "发起方",width = 10)
    private String refundChannelText;

    @Excel(name = "退款状态",width = 10)
    private String adminAuditStatusName;

    private Integer refundType;

    private Integer auditState;

    private String refundReason;
    private String refundExplain;
    private String remark2;
    private String remark3;

    private Integer refundVarietyNum;
    private BigDecimal refundMoney;
    private BigDecimal refundDiscount;
    private BigDecimal refundBalance;
    private BigDecimal refundTotalAmount;
    private BigDecimal refundReBalance;

    private Integer auditProcessState;
    private String auditStatusIcon;
    private String applyRefundTime;
    private String commonName;
    private Integer orderStatus;

    private Integer orderSource;
    private Integer orderAllAmount;
    private Integer refundOrderAllAmount;

    private String contactor;
    private String mobile;

    private String imageUrl;
    private BigDecimal money;
    private Integer refundGoodsNum;
    private Integer cancelChannel;

    public String getPayTypeText() {
        if(StringUtils.isEmpty(payTypeText)){
            if(payType == null){
                return "--";
            }else {
                if(payType == 1) {
                    return "在线支付";
                } else if (payType == 2) {
                    return "货到付款";
                } else if (payType == 3) {
                    return "线下转账";
                }
            }
        }
        return payTypeText;
    }

    public String getRefundChannelText() {
        if(StringUtils.isEmpty(refundChannelText)){
            if(refundChannel == null){
                return "--";
            }
            if(refundChannel == 1){
                return "用户发起";
            } else if (refundChannel == 2) {
                return "平台发起";
            } else if (refundChannel == 4) {
                return "商家发起";
            }else {
                return "--";
            }
        }
        return refundChannelText;
    }

    public String getAdminAuditStatusName() {
        if(StringUtils.isEmpty(adminAuditStatusName)){
            if(auditState == null || auditProcessState == null || payType == null){
                return "--";
            }
            if(auditState == 0 && auditProcessState == 0){
                return "退款待审核";
            }else if(auditState == 0 && auditProcessState == -1){
                return "审核通过，待退货入库";
            }else if(auditState == 0 && auditProcessState == 1 && payType == 1){
                return "入库完成，待财务退款";
            }else if(auditState == 1 || auditState == -1){
                if(auditState == 1){
                    return "退款成功";
                }else {
                    return "退款关闭";
                }
            }else {
                return "--";
            }
        }
        return adminAuditStatusName;
    }
}
