/*

通用的框架样式

适用于user table查询的样子

*/


html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, menu, nav, output, ruby, section, summary, time, mark, audio, video{
    margin: 0;
    padding: 0;
    border: 0;
    text-decoration: none;
    font: normal;
    font-size: 12px;
    font-family: "Microsoft YaHei", "微软雅黑";
}
.top_input{
    /*width: 100%;*/
    height: 50px;
    padding-left: 20px;
}
.top_input input{
    border: 0;
    outline: none;
    height:28px;
}
.fixed-table-body{
    height: auto !important;
}
.top_input select{
	width:138px;
	border: 0 none;
	height:28px;
	outline: medium none;
}

.top_input_name{
    float: left;
    line-height: 50px;
}
.top_input_div{
    float: left;
    height: 30px;
    border: 1px solid #e3e3e3;
    border-radius: 5px;
    margin-top: 10px;
}
.top_input_button{
    width: 80px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color:#0784cb ;
    background: -webkit-linear-gradient(#0784cb, #0075a9); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(#0784cb, #0075a9); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#0784cb, #0075a9); /* Firefox 3.6 - 15 */
    background: linear-gradient(#0784cb, #0075a9); /* 标准的语法 */
    border-radius: 5px;
    margin-top: 10px;
    margin-left: 20px;
    cursor: pointer;
    color: #fff;
    float: left;
}

.top_input_update_money_button{
    width: 100px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color:#0784cb ;
    background: -webkit-linear-gradient(#0784cb, #0075a9); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(#0784cb, #0075a9); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#0784cb, #0075a9); /* Firefox 3.6 - 15 */
    background: linear-gradient(#0784cb, #0075a9); /* 标准的语法 */
    border-radius: 5px;
    margin-top: 10px;
    margin-left: 20px;
    cursor: pointer;
    color: #fff;
    float: left;
}
.multi-select-container {
    position: relative; 
    display: inline-block; 
    min-width: 200px;
}
.multi-select-display {
    border: 1px solid #ccc; 
    padding: 5px; 
    min-height: 25px;
    cursor: pointer; 
    background: white;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.multi-select-dropdown {
    display: none;
    position: absolute; 
    top: 100%; 
    left: 0; 
    right: 0; 
    border: 1px solid #ccc; 
    background: white; 
    max-height: 200px; 
    overflow-y: auto; 
    z-index: 1000;
}
.multi-select-checkbox {
    display: flex;
    align-items: center; 
    padding: 5px; 
    cursor: pointer; 
    border-bottom: 1px solid #eee;
}
.multi-select-checkbox:hover {
    color: white;
    background: -webkit-linear-gradient(#0075a9, #0784cb);;
}
.top_input_button:hover{
    background: -webkit-linear-gradient(#0075a9, #0784cb); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(#0075a9, #0784cb); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#0075a9, #0784cb); /* Firefox 3.6 - 15 */
    background: linear-gradient(#0075a9, #0784cb); /* 标准的语法 */
}
.top_input_text{
    float: right;
    line-height: 50px;
    margin-right: 20px;
}
.top_input_text span{
    color: red;
}

.top_input_div2{
    float: left;
    height: 30px;
    border: 1px solid #e3e3e3;
    border-radius: 5px;
    margin-top: 10px;
    width: 110px;
    padding-left: 5px;
}
.top_input_div2 select{
    width: 100px;
    height: 30px;
    border: 0;
    text-align: center;
}
.top_input_div2 select:focus{
    border: 0;
    outline: 0;
}

.content_button{
    width: 98%;
    height: 50px;
    background-color: #F7F7F7;
    border: 1px solid #e3e3e3;
    margin: 0 auto;
}
.content_button_div{
    float: left;
    height: 50px;
    line-height: 50px;
    margin-left: 10px;
}
.content_button_right{
    float: right;
    line-height: 50px;
}
.red{
    color: red;
}
.blue {
    color: #089AD6;
}

.green {
    color: #0EAF12;
}

.green2 {
    color: #67A462;
}

.orange {
    color: #FF5000;
}
    .sh_tab{
        width: 96%;
        margin: 0 auto;
        border: 1px solid #e2e2e2;
        padding: 0 1%;
    }
    .sh_tab ul li{
        float: left;
        list-style: none;
        margin-left: 10px;
        line-height: 40px;

    }
    a{
        color: #333;
    }
    .sh_tab_up{
        width: 100%;
        height: 40px;


    }
    .sh_tab_down{
        width: 100%;
        height: 40px;
        border-top: 1px solid #e2e2e2;
    }
    .li_text a{
        color: #0AAEDC;
    }
    .sh_tab hr{
        border: 1px solid #e2e2e2;
        width: 98%;
    }
    .sh_tab_icon{
        width: 14px;
        height: 8px;
        background-image: url("../images/icon15.png");
        float: right;
        margin-top: 15px;
        cursor: pointer;
    }
.table_td_button{
    background: rgba(0, 0, 0, 0) linear-gradient(#0784cb, #0075a9) repeat scroll 0 0;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    float: left;
    height: 25px;
    line-height: 25px;
    margin-left: 5px;
    margin-right: 5px;
    text-align: center;
    width: 40px;
}

.table_td_button_disabled{
    background: rgba(0, 0, 0, 0) linear-gradient(#708090, #c0c0c0) repeat scroll 0 0;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    float: left;
    height: 25px;
    line-height: 25px;
    margin-left: 5px;
    margin-right: 5px;
    text-align: center;
    width: 40px;
}
.container input,.box_center input,.box_center select,.box_center textarea{ border:1px solid #ccc; }
.width50{width:50px;}
.width70{width:70px;}
.width100{width:100px;}

/*add 20180920 */
.table-bottom{
    text-align: right;
    line-height: 24px;
    padding-bottom: 6px;
}
.table-bottom>span{
    margin: 0 20px;
    font-size: 14px;
}
.table-bottom>span>a{
    padding-left: 6px;
}
.table-bottom>span>a:hover{
    color: #333;
    text-decoration: none;
}
.white_button{
    background: #fff;
    border:solid 1px #0784cb;
    color:#0784cb;
}
.white_button:hover{
    background: #fff;
}
.alignC{
    text-align: center;
}
.border_no{
    border: none!important;
}
.box_center{
    padding: 20px;
}
.receiveBox p{
    line-height: 30px;
    font-size: 16px;
    padding-bottom: 10px;
}
.padd10{
    padding: 10px 0;
}
.textarea01{
    width: 80%;
}

.panel-heading, .panel-body{
    display: block;
    clear: both;
    overflow: auto;
}
 .input-group {
    margin-bottom: 10px;
}
.form-control .label{
    margin-right: 15px;
    margin-top: 3px;
    display: block;
    float: left;
}
.image_div img{
    display: block !important;
    float: left;
    margin-top: 5px;
    max-width: 200px;
}
.specialmgb20 .col-md-6{
    margin-bottom: 15px !important;
}

.image_wraps img{
    max-width: 80%;
}