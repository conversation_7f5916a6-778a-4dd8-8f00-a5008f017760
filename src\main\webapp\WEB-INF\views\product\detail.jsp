<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<html>
<head>
    <%@include file="../include/common.jsp" %>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>商品发布管理</title>
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no,minimal-ui"/>
    <meta name="keywords" content=""/>
    <meta name="description" content=""/>
    <link rel="stylesheet" href="${basePathUrl}/css/common/admin_public.css"/>
    <link rel="stylesheet" href="${basePathUrl}/js/plugins/ztree/css/jquery-ui.min.css"/>
    <script src="${basePathUrl}/js/plugins/ztree/js/jquery-ui.min.js"></script>
    <link rel="stylesheet" href="${basePathUrl}/js/plugins/ztree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="${basePathUrl}/css/ace.min.css" type="text/css">
    <script src="${basePathUrl}/js/plugins/ztree/js/jquery.ztree.core.min.js"></script>
    <script src="${basePathUrl}/js/plugins/ztree/js/jquery.ztree.excheck.min.js"></script>
    <%--<script src="${basePathUrl}/js/plugins/easyui/plugins/jquery.dialog.js"></script>--%>
    <script language="javascript">
        var categoryMap = ${categoryMap};
        var isAuditing = ${isAuditing};
        var barcode = '${productVo.barcode}';
        var firstCategoryId = '${productVo.erpFirstCategoryId}';
        function appendShowName(){
            var brand = $("#brand").val();
            var productName = $("#productName").val();
            $('#showName').val(brand + " " + productName);
        }
    </script>
    <script src="${basePathUrl}/js/product/detail.js?t=0.5"></script>
    <style type="text/css">
        .ui-dialog .ui-dialog-titlebar, .ui-dialog .ui-jqdialog-titlebar, .ui-jqdialog .ui-dialog-titlebar, .ui-jqdialog .ui-jqdialog-titlebar{
            padding:10px !important;
        }
        .input-group>.form-control {
            width: calc(94% - 123px) !important;
            float: left;
            margin: 0 10px;
            display: block;
        }
        .product-qualification img{
            width:100%;
        }
        .product-qualification ul{
            margin:0;
        }

        .product-qualification ul li{
            margin-bottom:10px;
        }

        .product-qualification .input-group.db-input>.form-control{
            width:calc(50% - 90px) !important;
        }
        .product-qualification .input-group.db-input>.spliter{
            width:auto !important;
        }
        .product-qualification .radio{
            display:inline-block;
            margin:0 0 0 20px;
            line-height: 34px;
        }
        .product-qualification .radio input[type='radio']{
            line-height: normal;
            margin:10px 0 10px -20px;
        }
        .product-qualification .input-group textarea{
            margin-left:70px;
            width:calc(50% - 110px) !important;
        }

        .product-qualification h4{
            margin-bottom:20px;
            font-size:14px;
        }

        .product-qualification h4 span{
            display:inline-block;
            line-height:30px;
            color:#fff;
            padding:0 10px;
            margin-left:10px;
        }

        .product-qualification h4 .audit-access{
            background:#6ED284;
        }

        .product-qualification h4 .audit-refuse{
            background:#F6928C;
        }

        .product-qualification .audit-box{
            margin-top:10px;
        }

        .product-qualification .audit-box .input-group-addon{
            width:auto !important;
        }
    </style>

</head>

<body>
<div class="content">
    <div class="table">
        <div class="panel panel-default">
            <input type="hidden" id="skuId" name="id" value="${id}"/>
            <input type="hidden" id="corOrgId" name="id" value="${corOrgId}"/>
            <input type="hidden" id="orgId" value="${productVo.orgId}"/>
            <input type="hidden" id="standardProductId" value="${productVo.standardProductId}"/>
            <div class="panel-heading">
                <h3 class="panel-title">${isAuditing ==1?'商品信息审核':'商品信息查看'}</h3>
                <div class="pull-right" role="group">
                    <a type="button" class="btn btn-info" id="btn_return" onClick="javascript:history.back()">返回</a>
                    <c:if test="${isAuditing == 1 && (productVo.status == 4 || productVo.status == 7 || productVo.status ==8)}">
                        <a type="button"  class="btn btn-info" id="btn_auditing">审核</a>
                    </c:if>
                </div>
            </div>
            <div class="panel-body">
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">商户名称</span>
                        <input  class="form-control" readonly value="${productVo.companyName}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">审核状态</span>
                        <input  class="form-control" readonly value="${productVo.status ==1?'销售中':productVo.status ==2?'已售尽':productVo.status ==3?'特惠中':productVo.status ==4?'下架':productVo.status ==5?'秒杀':productVo.status ==6?'待上架':productVo.status ==7?'已录入':productVo.status ==8?'待审核':productVo.status ==9?'审核未通过':'删除'}">
                    </div>
                </div>
            </div>
            <div class="panel-body">
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%">
                        <h3 class="panel-title"> 商品基本信息 </h3>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">商品编号</span>
                        <input  class="form-control" readonly id="barcode" name="barcode" value="${productVo.barcode}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon"><c:choose><c:when test="${isInstrumentCategory}">医疗器械名称</c:when><c:otherwise>通用名称</c:otherwise></c:choose> </span>
                        <c:choose>
                            <c:when test="${isAuditing == 0}">
                                <input  class="form-control" readonly value="${productVo.commonName}">
                            </c:when>
                            <c:otherwise>
                                <input  class="form-control" id="commonName" name="commonName" value="${productVo.commonName}">
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">商品名称</span>
                        <c:choose>
                            <c:when test="${isAuditing == 0}">
                                <input  class="form-control" readonly value="${productVo.productName}">
                            </c:when>
                            <c:otherwise>
                                <input  onchange="appendShowName()" class="form-control"  id="productName" name="productName" value="${productVo.productName}">
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">品牌</span>
                        <c:choose>
                            <c:when test="${isAuditing == 0}">
                                <input  class="form-control" readonly value="${productVo.brand}">
                            </c:when>
                            <c:otherwise>
                                <input  onchange="appendShowName()" class="form-control"  id="brand" name="brand" value="${productVo.brand}">
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">展示名称</span>
                        <input  class="form-control" id = "showName" readonly value="${productVo.showName}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">商品助记码</span>
                        <input  class="form-control" readonly value="${productVo.zjm}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">商品条码(69码)</span>
                        <c:choose>
                            <c:when test="${isAuditing == 0}">
                                <input  class="form-control" readonly value="${productVo.code}">
                            </c:when>
                            <c:otherwise>
                                <input  class="form-control"  id="code" name="code" value="${productVo.code}">
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">商户ERP编码</span>
                        <input  class="form-control" readonly value="${productVo.erpCode}">
                    </div>
                </div>
                <c:if test="${!isChi}">
                    <div class="col-md-6">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon"><c:choose><c:when test="${isInstrumentCategory}">医疗器械注册证或备案凭证编号</c:when><c:when test="${isCosmetics}">化妆品备案编号/注册证号</c:when><c:otherwise>批准文号</c:otherwise></c:choose></span>
                            <c:choose>
                                <c:when test="${isAuditing == 0}">
                                    <input  class="form-control" readonly value="${productVo.approvalNumber}">
                                </c:when>
                                <c:otherwise>
                                    <input  class="form-control"  id="approvalNumber" name="approvalNumber" value="${productVo.approvalNumber}">
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                </c:if>
                <c:if test="${isInstrumentCategory}">
                    <div class="col-md-3">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">医疗器械注册证或备案凭证</span>
                            <span style="margin-left: 10px;">
                                 <c:forEach items="${productVo.instrumentLicenseImagList}" var="item">
                                     <img style="width: 50;height: 50;cursor: pointer;" onclick="showShopImg('${bigDescImgUrlPrefix}${item}',1)" src="${smallDescImgUrlPrefix}${item}">
                                 </c:forEach>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="input-group">
                            <span class="input-group-addon">有效期至</span>
                            <input  class="form-control" readonly value="${productVo.instrumentLicenseEffect}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">生产许可证号或备案凭证编号</span>
                            <c:choose>
                                <c:when test="${isAuditing == 0}">
                                    <input  class="form-control" readonly value="${productVo.manufacturingLicenseNo}">
                                </c:when>
                                <c:otherwise>
                                    <input  class="form-control"  id="manufacturingLicenseNo" name="manufacturingLicenseNo" value="${productVo.manufacturingLicenseNo}">
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">生产许可证或备案凭证</span>
                            <span  style="margin-left: 10px;">
                                  <c:forEach items="${productVo.manufacturingLicenseImageList}" var="item">
                                      <img style="width: 50;height: 50;cursor: pointer;" onclick="showShopImg('${bigDescImgUrlPrefix}${item}',1)" src="${smallDescImgUrlPrefix}${item}">
                                  </c:forEach>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="input-group">
                            <span class="input-group-addon">有效期至</span>
                            <input  class="form-control" readonly value="${productVo.manufacturingLicenseEffect}">
                        </div>
                    </div>
                </c:if>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">生产厂家</span>
                        <c:choose>
                            <c:when test="${isAuditing == 0}">
                                <input  class="form-control" readonly value="${productVo.manufacturer}">
                            </c:when>
                            <c:otherwise>
                                <input  class="form-control"  id="manufacturer" name="manufacturer" value="${productVo.manufacturer}">
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">规格<c:if test="${isInstrumentCategory}">(型号)</c:if></span>
                        <c:choose>
                            <c:when test="${isAuditing == 0}">
                                <input  class="form-control" readonly value="${productVo.spec}">
                            </c:when>
                            <c:otherwise>
                                <input  class="form-control"  id="spec" name="spec" value="${productVo.spec}">
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <c:if test="${isInstrumentCategory}">
                    <div class="col-md-6">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">产品技术要求编号</span>
                            <input  class="form-control" readonly value="${productVo.technicalRequirementNo}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">医疗器械注册人/备案人</span>
                            <input  class="form-control" readonly value="${productVo.filingsAuthor}">
                        </div>
                    </div>
                </c:if>
                <c:if test="${isCosmetics}">
                    <div class="col-md-6">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">化妆品注册人/备案人</span>
                            <input  class="form-control" readonly value="${productVo.filingsAuthor}">
                        </div>
                    </div>
                </c:if>
                <c:if test="${!isInstrumentCategory && !isChi && !isCommon}">
                        <div class="col-md-6">
                            <div class="input-group" style="width: 80%">
                                <span class="input-group-addon">处方类型</span>
                                <c:choose>
                                    <c:when test="${isAuditing == 0}">
                                        <input class="form-control" readonly
                                               value="${productVo.drugClassification == 1?'甲类OTC':productVo.drugClassification == 2?'乙类OTC':productVo.drugClassification == 3?'处方药Rx':'无'}">
                                    </c:when>
                                    <c:otherwise>
                                        &nbsp&nbsp&nbsp
                                        <input type="radio" name="drugClassification" value="0"/>无
                                        &nbsp&nbsp&nbsp
                                        <input type="radio" name="drugClassification" value="1"/>甲类OTC
                                        &nbsp&nbsp&nbsp
                                        <input type="radio" name="drugClassification" value="2"/>乙类OTC
                                        &nbsp&nbsp&nbsp
                                        <input type="radio" name="drugClassification" value="3"/>处方药Rx
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </div>
                </c:if>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">中包装</span>
                        <input  class="form-control" readonly value="${productVo.mediumPackageNum}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">是否委托生产</span>
                        <c:if test="${productVo.isCommissionProduction==1}">
                            <input  class="form-control" readonly value="是">
                        </c:if>
                        <c:if test="${productVo.isCommissionProduction==0}">
                            <input  class="form-control" readonly value="否">
                        </c:if>
                       
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">受托生产厂家</span>
                        <input  class="form-control" readonly value="${productVo.entrustedManufacturer}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">受托生产厂家地址</span>
                        <input  class="form-control" readonly value="${productVo.entrustedManufacturerAddress}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">件数量</span>
                        <input  class="form-control" readonly value="${productVo.pieceLoading}">
                    </div>
                </div>

                <c:if test="${!isInstrumentCategory && !isChi && !isCommon}">
                    <div class="col-md-6">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">剂型</span>
                            <c:choose>
                            <c:when test="${isAuditing == 0}">
                                <input  class="form-control" readonly value="${productVo.dosageForm}">
                            </c:when>
                            <c:otherwise>
                                <select class="form-control" id="dosageForm" name="dosageForm">
                                        <%--                                <option value="" selected="selected">${productVo.dosageForm}</option>--%>
                                    <c:forEach var="dosageForm" items="${productVo.dosageFormList}" >
                                        <option value="${dosageForm}"  ${productVo.dosageForm == dosageForm ? 'selected' : ''} >${dosageForm}</option>
                                    </c:forEach>
                                </select>
                            </c:otherwise>
                            </c:choose>
                            &nbsp&nbsp&nbsp
                        </div>
                    </div>
                </c:if>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">单位</span>
                        <c:choose>
                            <c:when test="${isAuditing == 0}">
                                <input  class="form-control" readonly value="${productVo.productUnit}">
                            </c:when>
                            <c:otherwise>
                                <select class="form-control" id="productUnit" name="productUnit">
                                        <%--                            <option value="" selected="selected">${productVo.productUnit}</option>--%>
                                    <c:forEach var="productUnit" items="${productVo.productUnitList}" >
                                        <option value="${productUnit}" ${productVo.productUnit == productUnit ? 'selected' : ''}>${productUnit}</option>
                                    </c:forEach>
                                </select>
                            </c:otherwise>
                        </c:choose>
                        &nbsp&nbsp&nbsp
<%--                        <input  class="form-control" readonly value="${productVo.productUnit}">--%>
                    </div>
                </div>
<%--                <div class="col-md-6">--%>
<%--                    <div class="input-group" style="width: 80%">--%>
<%--                        <span class="input-group-addon">生产日期</span>--%>
<%--                        <input  class="form-control" readonly  value="${productVo.productionDate}"/>--%>
<%--                    </div>--%>
<%--                </div>--%>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">存储条件</span>
                        <c:choose>
                            <c:when test="${isAuditing == 0}">
                                <input class="form-control" readonly value="${productVo.storageCondition}">
                            </c:when>
                            <c:otherwise>
                                &nbsp&nbsp&nbsp
                                <input type="radio" name="storageCondition" value="无"/>无
                                &nbsp&nbsp&nbsp
                                <input type="radio" name="storageCondition" value="常温"/>常温
                                &nbsp&nbsp&nbsp
                                <input type="radio" name="storageCondition" value="控温"/>控温
                                &nbsp&nbsp&nbsp
                                <input type="radio" name="storageCondition" value="冷藏"/>冷藏
                                &nbsp&nbsp&nbsp
                                <input type="radio" name="storageCondition" value="冷冻"/>冷冻
                                &nbsp&nbsp&nbsp
                                <input type="radio" name="storageCondition" value="阴凉"/>阴凉
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon"><c:choose><c:when test="${isInstrumentCategory}">有效期/失效期</c:when><c:otherwise>有效期/保质期</c:otherwise></c:choose></span>
                        <c:choose>
                            <c:when test="${isAuditing == 0}">
                                <input  class="form-control" readonly value="${productVo.term}">
                            </c:when>
                            <c:otherwise>
                                <input  class="form-control"  id="term" name="term" value="${productVo.term}">
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">是否可拆零</span>
                        <input  class="form-control" readonly value="${productVo.isSplit eq '0'?'否':'是'}">
                    </div>
                </div>
                <c:if test="${isChi}">
                    <div class="col-md-6">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">产地</span>
                            <c:choose>
                                <c:when test="${isAuditing == 0}">
                                    <input  class="form-control" readonly value="${productVo.producer}">
                                </c:when>
                                <c:otherwise>
                                    <input  class="form-control"  id="producer" name="producer" value="${productVo.producer}">
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                </c:if>
                <c:if test="${isDrug}">
                    <div class="col-md-6">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">上市许可持有人</span>
                            <c:choose>
                                <c:when test="${isAuditing == 0}">
                                    <input  class="form-control" readonly value="${productVo.marketAuthor}">
                                </c:when>
                                <c:otherwise>
                                    <input  class="form-control"  id="marketAuthor" name="marketAuthor" value="${productVo.marketAuthor}">
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                </c:if>
                <div class="col-md-6">
                    <div class="input-group productCategoryRelationTd">
                        <span class="input-group-addon">*关联分类</span>
                        <input class="form-control" type="hidden" id="skuRelationCategory" name="skuRelationCategory" value="${productVo.skuRelationCategory}">
                        <input class="form-control" readonly value="${productVo.skuRelationCategoryName}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">*经营分类</span>
                        <input type="hidden" id="skuCategoryVal" value="${productVo.skuCategory}">
                        <input type="text" class="form-control" id="skuCategory" name="skuCategory"  value="${productVo.skuCategory}" readonly/>
                        <input type="hidden" id="skuCategoryId" name="skuCategoryId"  value="${productVo.skuCategoryId}"/>

                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">一级发布分类</span>
                                <input type="hidden" name="erpFirstCategoryId" id="erpFirstCategoryId"
                                       value="${productVo.erpFirstCategoryId}">
                                <input class="form-control" readonly value="${productVo.erpFirstCategoryName}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">二级发布分类</span>
                        <c:choose>
                            <c:when test="${productVo.source == 2}">
                                <input type="hidden" name="erpSecondCategoryId" id="erpSecondCategoryId" value="${productVo.erpSecondCategoryId}">
                                <input  class="form-control" readonly value="${productVo.erpSecondCategoryName}">
                            </c:when>
                            <c:otherwise>
                                <select class="form-control" name="erpSecondCategoryId" id="erpSecondCategoryId" onchange="selectedThirdLevelCategory()">
                                    <option value="">请选择</option>
                                    <c:if test="${!empty categoryMap[productVo.erpFirstCategoryId]}">
                                        <c:forEach items="${categoryMap[productVo.erpFirstCategoryId]}" var="item">
                                            <option value="${item.id}" ${item.id == productVo.erpSecondCategoryId ? 'selected' : ''}>${item.name}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">三级发布分类</span>
                        <c:choose>
                            <c:when test="${productVo.source == 2}">
                                <input type="hidden" name="erpThirdCategoryId" id="erpThirdCategoryId" value="${productVo.erpThirdCategoryId}">
                                <input  class="form-control" readonly value="${productVo.erpThirdCategoryName}">
                            </c:when>
                            <c:otherwise>
                                <select class="form-control" name="erpThirdCategoryId" id="erpThirdCategoryId" onchange="selectedFourLevelCategory()">
                                    <option value="">请选择</option>
                                    <c:if test="${!empty categoryMap[productVo.erpSecondCategoryId]}">
                                        <c:forEach items="${categoryMap[productVo.erpSecondCategoryId]}" var="item">
                                            <option value="${item.id}" ${item.id == productVo.erpThirdCategoryId ? 'selected' : ''}>${item.name}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">四级发布分类</span>
                        <c:choose>
                            <c:when test="${productVo.source == 2}">
                                <input type="hidden" name="erpFourthCategoryId" id="erpFourthCategoryId" value="${productVo.erpFourthCategoryId}">
                                <input  class="form-control" readonly value="${productVo.erpFourthCategoryName}">
                            </c:when>
                            <c:otherwise>
                                <select class="form-control" name="erpFourthCategoryId" id="erpFourthCategoryId">
                                    <option value="">请选择</option>
                                    <c:if test="${!empty categoryMap[productVo.erpThirdCategoryId]}">
                                        <c:forEach items="${categoryMap[productVo.erpThirdCategoryId]}" var="item">
                                            <option value="${item.id}" ${item.id == productVo.erpFourthCategoryId ? 'selected' : ''}>${item.name}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">佣金比例</span>
                        <input class="form-control" readonly name="commissionRatio"  value="${commissionRatio}%"/>
                    </div>
                    <%--                    <a href="javascipt:void(0)" onclick="clickUpdateSkuCommission()" style="position: absolute;top: 0;right: 0;font-size :14px;color: #555;">修改</a>--%>
                </div>
            </div>
            <div class="panel-body">
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%" style="display: flex">
                        <span class="input-group-addon">商品图片</span>
                        <span  style="margin-left: 10px">
                            <img style="width:170;height: 170;cursor: pointer;" onclick="showShopImg('${bigImgUrlPrefix}${productVo.imageUrl}',1)" src="${smallImgUrlPrefix}${productVo.imageUrl}">
                            <c:forEach items="${productVo.imagesList}" var="item">
                                <img style="width: 170px;height: 170;cursor: pointer;" onclick="showShopImg('${bigImgUrlPrefix}${item}',1)" src="${smallImgUrlPrefix}${item}">
                            </c:forEach>
                        </span>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%" style="display: flex">
                        <span class="input-group-addon">详情</span>
                        <span   style="margin-left: 10px">
                            <c:forEach items="${productVo.skuInstructionImageList}" var="item">
                                <img style="width: 170;height: 170;cursor: pointer;" onclick="showShopImg('${bigDescImgUrlPrefix}${item}',1)" src="${smallDescImgUrlPrefix}${item}">
                            </c:forEach>
                        </span>
                    </div>
                </div>
            </div>

<%--            <div class="panel-body">--%>
<%--                <div class="col-md-12">--%>
<%--                    <div class="input-group">--%>
<%--                        <h3 class="panel-title">商品资质信息</h3>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--                <div id="proQualification" class="product-qualification"></div>--%>
<%--            </div>--%>

            <div class="panel-body">
                <div class="col-md-12">
                    <div class="input-group">
                        <span class="panel-title "> 商品控销信息 </span>
                        <button id="contorBtn"
                                style="width: 60px;height: 30px;line-height: 28px;text-align: center;background-color: #0784cb;border-radius: 5px;margin-top: 10px;margin-left: 20px;cursor: pointer;color: #fff;">查看</button>
                    </div>
                </div>
            </div>

            <div class="panel-body">
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%">
                        <h3 class="panel-title"> 商品价格库存 </h3>
                    </div>
                </div>
                <c:choose>
                    <%-- 售价模式和底价模式布局不一样 --%>
                     <c:when test="${priceType == 1}">
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">单体采购价</span>
                                 <input  class="form-control" readonly value="${productVo.fob}">
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">价格是否同步ERP</span>
                                 <span class="input-group-addon" style="text-align: left !important;padding-left: 10px;">
                             <c:choose>
                                 <c:when test="${productVo.priceSyncErp == 1}">
                                     是
                                 </c:when>
                                 <c:otherwise>
                                     否
                                 </c:otherwise>
                             </c:choose>
                         </span>
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">连锁采购价</span>
                                 <input  class="form-control" readonly value="${productVo.chainPrice}">
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">建议零售价</span>
                                 <input  class="form-control" readonly value="${productVo.suggestPrice}">
                             </div>
                         </div>
                         <div class="col-md-12"  style="padding-right: 0px;">
                             <div class="col-md-6">
                                 <div class="input-group" style="width: 80%">
                                     <span class="input-group-addon">底价</span>
                                     <input  class="form-control" readonly <c:if test="${productVo.activityType == 0}">value="${productVo.basePrice}"</c:if>>
                                 </div>
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">总库存</span>
                                 <input  class="form-control" readonly value="${productVo.availableQty}">
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">库存是否同步ERP</span>
                                 <span class="input-group-addon" style="text-align: left !important;padding-left: 10px;">
                                     <c:choose>
                                         <c:when test="${productVo.stockSyncErp == 1}">
                                             是
                                         </c:when>
                                         <c:otherwise>
                                             否
                                         </c:otherwise>
                                     </c:choose>
                                 </span>
                             </div>
                         </div>
                     </c:when>
                     <c:otherwise>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">单体采购价</span>
                                 <input  class="form-control" readonly value="${productVo.fob}">
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">单体毛利率</span>
                                 <input  class="form-control" readonly value="${productVo.grossProfitMargin}">
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">连锁采购价</span>
                                 <input  class="form-control" readonly value="${productVo.chainPrice}">
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">连锁毛利率</span>
                                 <input  class="form-control" readonly value="${productVo.chainGrossProfitMargin}">
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">底价</span>
                                 <input  class="form-control" readonly value="${productVo.basePrice}">
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">建议零售价</span>
                                 <input  class="form-control" readonly value="${productVo.suggestPrice}">
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">价格是否同步ERP</span>
                                 <span class="input-group-addon" style="text-align: left !important;padding-left: 10px;">
                                     <c:choose>
                                         <c:when test="${productVo.priceSyncErp == 1}">
                                             是
                                         </c:when>
                                         <c:otherwise>
                                             否
                                         </c:otherwise>
                                     </c:choose>
                                 </span>
                             </div>
                         </div>
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">库存是否同步ERP</span>
                                 <span class="input-group-addon" style="text-align: left !important;padding-left: 10px;">
                                     <c:choose>
                                         <c:when test="${productVo.stockSyncErp == 1}">
                                             是
                                         </c:when>
                                         <c:otherwise>
                                             否
                                         </c:otherwise>
                                     </c:choose>
                                 </span>
                             </div>
                         </div>
                         <div class="col-md-5">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">总库存</span>
                                 <input  class="form-control" readonly value="${productVo.availableQty}">
                             </div>
                         </div>
                         <div class="col-md-1">
                             <button class="top_input_button" id="queryQty" style="margin:0px -20px">查看</button>
                         </div>
                         <div class="col-md-6">
                             <!--站位 -->
                         </div>
                     </c:otherwise>
                 </c:choose>
                 <div class="col-md-6">
                     <div class="input-group" style="width: 80%">
                         <span class="input-group-addon">最老生产日期</span>
                         <input  class="form-control" readonly value="${productVo.oldestProDate}">
                     </div>
                 </div>
                 <div class="col-md-6">
                     <div class="input-group" style="width: 80%">
                         <span class="input-group-addon">最新生产日期</span>
                         <input  class="form-control" readonly  value="${productVo.newProDate}">
                     </div>
                 </div>
                 <div class="col-md-6">
                     <div class="input-group" style="width: 80%">
                         <span class="input-group-addon">近效期至</span>
                         <input  class="form-control" readonly value="${productVo.nearEffect}">
                     </div>
                 </div>
                 <div class="col-md-6">
                     <div class="input-group" style="width: 80%">
                         <span class="input-group-addon">远效期至</span>
                         <input  class="form-control" readonly  value="${productVo.farEffect}">
                     </div>
                 </div>
             </div>
             <!-- 起购限购 -->
             <div class="panel-body">
                 <div class="col-md-12">
                     <div class="input-group" style="width: 80%">
                         <h1 class="panel-title "> 起购限购 </h1>
                     </div>
                 </div>
                 <div class="col-md-12"  style="padding-right: 0px;">
                     <div class="col-md-6">
                         <div class="input-group" style="width: 80%">
                             <span class="input-group-addon">客户起购数量</span>
                             <input  class="form-control" readonly  <c:if test="${productVo.minPurchaseCount > 0}">value="${productVo.minPurchaseCount}"</c:if> >
                         </div>
                     </div>
                 </div>
                 <c:choose>
                     <c:when test="${productVo.popSkuPurchaseLimitDto ==null ||productVo.popSkuPurchaseLimitDto.purchaseType ==0|| productVo.popSkuPurchaseLimitDto.isDel==1}">
                     <div class="col-md-12"  style="padding-right: 0px;">
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">客户限购数量</span>
                                 <input  class="form-control" readonly  value="不限购" >
                             </div>
                         </div>
                     </div>
                     </c:when>
                     <c:otherwise>
                     <div class="col-md-12"  style="padding-right: 0px;">
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">客户限购数量</span>
                                 <input  class="form-control" readonly  value="${productVo.popSkuPurchaseLimitDto.limitedQty}" >
                             </div>
                         </div>
                     </div>
                     <div class="col-md-12"  style="padding-right: 0px;">
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">客户限购类型</span>
                                 <input  class="form-control" readonly  <c:choose> <c:when test="${productVo.popSkuPurchaseLimitDto.purchaseType==1}">value="指定时间区间"</c:when><c:otherwise>value="单笔"</c:otherwise>  </c:choose>>
                             </div>
                         </div>
                     </div>
                     <c:if test="${productVo.popSkuPurchaseLimitDto.purchaseType==1}">
                     <div class="col-md-12"  style="padding-right: 0px;">
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">限购生效周期</span>
                                 <span  class="form-control" readonly  >
                                     <div style="width: 45%;display: inline-block;"><fmt:formatDate value="${productVo.popSkuPurchaseLimitDto.purchaseTimeStart}" pattern="yyyy-MM-dd"/></div>
                                     <div style="width: 6%;display: inline-block;">至</div>
                                     <div style="width: 45%;display: inline-block;text-align: right;"><fmt:formatDate value="${productVo.popSkuPurchaseLimitDto.purchaseTimeEnd}" pattern="yyyy-MM-dd"/></div>
                                 </span>
                             </div>
                         </div>
                     </div>
                     </c:if>
                     <div class="col-md-12"  style="padding-right: 0px;">
                         <div class="col-md-6">
                             <div class="input-group" style="width: 80%">
                                 <span class="input-group-addon">客户类型</span>
                                 <span  class="form-control" style="border: 0;padding-left: 0;">${productVo.popSkuPurchaseLimitDto.userTypeName}</span>
                             </div>
                         </div>
                     </div>
                     </c:otherwise>
                 </c:choose>
             </div>
             <div class="panel-body">
                 <div class="col-md-12">
                     <div class="input-group" style="width: 80%">
                         <span class="panel-title "> 业务商圈 </span>
                         <span>${productVo.busAreaConfigName}</span>
                     </div>
                 </div>
             </div>
             <%--<div class="panel-body">
                   <div class="col-md-12">
                       <div class="input-group" style="width: 80%">
                           <h3 class="panel-title"> 商品限购信息 </h3>
                       </div>
                   </div>

                   <div class="col-md-6">
                        <div class="input-group" style="width: 80%">
                           <span class="input-group-addon">限购数量</span>
                           <input  class="form-control" readonly value="${productVo.limitedQty}">
                       </div>
                   </div>
                   <div class="col-md-6">
                           <div class="input-group" style="width: 80%">
                               <span class="input-group-addon">限购用户类型</span>
                               <input  class="form-control" readonly value="${productVo.skuPopExtend.purchaseMerchantType == 1?'药店':'诊所'}">
                           </div>
                   </div>
                   &lt;%&ndash;<div class="col-md-12">
                      <div class="input-group" style="width: 80%">
                         <span class="input-group-addon">销售区域</span>
                         <textarea  class="form-control" readonly placeholder="">${productVo.producePopExtendVo}</textarea>
                     </div>
                 </div>&ndash;%&gt;

             </div>--%>
            <div class="panel-body">
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%">
                        <h3 class="panel-title"> 商品其他信息 </h3>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">副标题</span>
                        <textarea  class="form-control" readonly placeholder="">${productVo.subtitle}</textarea>
                    </div>
                </div>
                <c:if test="${!isChi}">
                    <div class="col-md-12">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon"><c:choose><c:when test="${isInstrumentCategory}">结构及组成</c:when><c:otherwise>成分</c:otherwise></c:choose></span>
                            <textarea  class="form-control" readonly placeholder="">${productVo.component}</textarea>
                        </div>
                    </div>
                </c:if>
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon"><c:choose><c:when test="${isInstrumentCategory}">适用范围</c:when><c:otherwise>适应症/功能主治</c:otherwise></c:choose></span>
                        <textarea  class="form-control" readonly placeholder="">${productVo.indication}</textarea>
                    </div>
                </div>
                <c:if test="${!isInstrumentCategory}">
                    <div class="col-md-12">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">用法与用量</span>
                            <textarea  class="form-control" readonly placeholder="">${productVo.usageAndDosage}</textarea>
                        </div>
                    </div>
                </c:if>
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">注意事项</span>
                        <textarea  class="form-control" readonly placeholder="">${productVo.considerations}</textarea>
                    </div>
                </div>
                <c:if test="${!isChi}">
                    <div class="col-md-12">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon"><c:choose><c:when test="${isInstrumentCategory}">禁忌症</c:when><c:otherwise>禁忌</c:otherwise></c:choose></span>
                            <textarea  class="form-control" readonly placeholder="">${productVo.abstain}</textarea>
                        </div>
                    </div>
                </c:if>
                <c:if test="${!isInstrumentCategory && !isChi}">
                    <div class="col-md-12">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">不良反应</span>
                            <textarea  class="form-control" readonly placeholder="">${productVo.untowardEffect}</textarea>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="input-group" style="width: 80%">
                            <span class="input-group-addon">药物相互作用</span>
                            <textarea  class="form-control" readonly placeholder="">${productVo.interaction}</textarea>
                        </div>
                    </div>
                </c:if>
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">备注</span>
                        <textarea  class="form-control" readonly placeholder="">${productVo.bz}</textarea>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">推荐卖点1</span>
                        <textarea  class="form-control" readonly placeholder="">${productVo.sellingProposition1}</textarea>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">推荐卖点2</span>
                        <textarea  class="form-control" readonly placeholder="">${productVo.sellingProposition2}</textarea>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" style="width: 80%">
                        <span class="input-group-addon">推荐卖点3</span>
                        <textarea  class="form-control" readonly placeholder="">${productVo.sellingProposition3}</textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 商品 关联分类 -->
    <div class="panel-body" id="categoryTreeDiv" style="display:none">
        <div>
            <ul id="cateTree" class="ztree"></ul>
        </div>
    </div>
    <!-- 商品 推荐分类 -->
    <div class="panel-body" id="skuCategoryTreeDiv" style="display:none">
        <div>
            <ul id="skuCategoryTree" class="ztree"></ul>
        </div>
    </div>
</div>

<div class="modal fade" id="edit_commission_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="floor_modal_title">修改佣金比例</h4>
            </div>
            <div class="modal-body" style="max-height:600px;">
                <label>注：佣金比例需>=${commissionConfig.minCommissionRatio}%,且<=${commissionConfig.maxCommissionRatio}%,保留两位小数</label>
                <div>
                    佣金比例 <input type="text" name="commissionRatio" id="commissionRatio" value="${commissionRatio}">%
                </div>
                <label><font color="red" id="tips"></font></label>
            </div>
            <div class="modal-footer" style="text-align:center;">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="save_commission" onclick="updateSkuCommission()">保存</button>

            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<div class="modal fade" id="img_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="img_title">图片预览</h4>
            </div>
            <div class="modal-body" style="max-height:600px;overflow-y: auto">
                <div id="img_box" style="display: none">
                    <img src="" id="img_src" alt="" style="width: 100%">
                </div>
                <div id="img_text_box" style="display: none">
                    <div>
                        <img src="" id="img_text_src" alt="" style="width: 100%">
                    </div>
                    <div>
                        <p id="img_text_name"></p>
                        <p id="img_text_code"></p>
                        <p id="img_text_time"></p>
                    </div>
                </div>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
</body>

<script language="javascript">
    $("input[name='drugClassification'][value= ${productVo.drugClassification}]").attr("checked",true);
    $("input[name='storageCondition'][value= ${productVo.storageCondition}]").attr("checked",true);
</script>
</html>

