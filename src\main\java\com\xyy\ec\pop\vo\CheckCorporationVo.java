package com.xyy.ec.pop.vo;

import lombok.Data;

import java.util.Date;

/**
 * @version v1
 * @Description 企业主信息审核记录
 * <AUTHOR>
 */
@Data
public class CheckCorporationVo {
    private Long id;

    /**
     * 企业主id
     */
    private Long cId;
    /**
     * 记录类型
     */
    private Integer recordType;
    /**
     * 审核提交批次
     */
    private String batch;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 公司名称
     */
    private String name;

    /**
     * 企业工商注册号
     */
    private String regCode;

    /**
     * 法人
     */
    private String corporat;

    /**
     * 手机
     */
    private String phone;

    /**
     * 电话
     */
    private String fixedPhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 网址
     */
    private String web;

    /**
     * 省/直辖市ID（关联'tb_dic_area.id'）
     */
    private Long provId;

    /**
     * 省/直辖市全称
     */
    private String prov;

    /**
     * 市级ID（关联'tb_dic_area.id'）
     */
    private Long cityId;

    /**
     * 市级全称
     */
    private String city;
    /**
     * 企业类型
     */
    private Integer corporationType;
    /**
     * 客服电话
     */
    private String customerServicePhone;
    /**
     * erp流程实例ID
     */
    private String erpProcessId;
    /**
     * 区域ID（关联'tb_dic_area.id'）
     */
    private Long areaId;

    /**
     * 区/县级全称
     */
    private String area;

    /**
     * （区/县级之后）详细地址
     */
    private String addr;

    /**
     * LOGO-URL
     */
    private String logoUrl;

    /**
     * 简介
     */
    private String brief;

    /**
     * 审核备注
     */
    private String remarks;

    /**
     * 状态（1-待审核；2-审核不通过；3-审核通过；）
     */
    private Byte state;

    /**
     * 逻辑删除：0-删除
     */
    private Byte del;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改人姓名
     */
    private String updateName;
    /**
     * 单据编号
     */
    private String erpNumber;
    /**
     * 街道ID
     */
    private Integer streetId;
    /**
     * 街道名称
     */
    private String streetName;
    /**
     * 综合搜索（多列值合并）
     */
    private String search;
    /**
     * 基础信息审核状态
     */
    private Integer status;
    /**
     * 动作
     */
    private String action;

    private String rejectReason;

    /**
     * 审核时间
     */
    private Date auditTime;
}
