package com.xyy.ec.pop.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @version v1
 * @Description 资质明细
 * <AUTHOR>
 */
@Data
public class QualificationsDetailVo {

    private Long id;

    /**
     * 资质名称
     */
    private String name;

    /**
     * 资质内容描述
     */
    private String remark;

    /**
     * 是否必填
     */
    private Byte isNeed;

    /**
     * 证件号是否必填
     */
    private Byte isNeedCode;

    /**
     * 开户银行是否必填
     */
    private Byte isNeedBank;

    /**
     * 开户支行是否必填
     */
    private Byte isNeedSubBank;

    /**
     * 证件有效期是否必填
     */
    private Byte isNeedTime;
    /**
     * 证书结束时间是否有长期
     */
    private Byte withLongTime;
    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改者
     */
    private String updator;

    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 是否必填
     */
    private boolean isCheck;
    /**
     * 编码
     */
    private String code;
    /**
     * 企业类型
     */
    private Byte corporationType;
    /**
     * s
     * 图片数量限制
     */
    private Integer maxImg;
    /**
     * 法人姓名:
     */
    private Byte legalPersonName;
    /**
     * 信息2是否显示0不显示1显
     */
    private boolean personNameShow;

    /**
     * 是否显示0不显示1显示
     */
    private boolean needBankShow;

    /**
     * 是否显示0不显示1显示
     */
    private boolean needSubBankShow;

    /**
     * 是否显示0不显示1显示
     */
    private boolean needCodeShow;

    /**
     * 是否显示0不显示1显示
     */
    private boolean needTimeEndShow;

    /**
     * 信息2显示名称
     */
    private String personNameShowName;

    /**
     * 开户银行显示名称
     */
    private String needBankShowName;

    /**
     * 开户支行显示名称
     */
    private String needSubBankShowName;

    /**
     * 证件号显示名称
     */
    private String needCodeShowName;

    /**
     * 证件显示名称
     */
    private String needTimeShowName;
    /**
     * 是否展示
     */
    private Integer isShow;

    /**
     * 获取日期是否必填
     */
    private Boolean isNeedGainTime;
}
