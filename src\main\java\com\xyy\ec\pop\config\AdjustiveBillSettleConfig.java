package com.xyy.ec.pop.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version v1
 * @Description 商品批量更新配置
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(value = "adjust.bill.settle")
@Data
public class AdjustiveBillSettleConfig {
    private int maxFileSize = 3;
    private int maxRows = 100;
    public final static Map<String, Integer> statusMap = new HashMap<>();
    public final static Map<String, Integer> drugsMap = new HashMap<>();

    List<String> titles = Arrays.asList("*商业编号", "平台总优惠", "佣金金额", "*应结算金额", "*应缴纳佣金", "备注");

}
