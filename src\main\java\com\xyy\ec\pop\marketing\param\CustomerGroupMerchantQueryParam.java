package com.xyy.ec.pop.marketing.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 精准营销选人临时静态人群查询条件
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerGroupMerchantQueryParam implements Serializable {

    /**
     * 人群ID，必填。
     */
    private Long customerGroupId;

    /**
     * 药店编码，可选。不可同时为null。
     */
    private String merchantIdStr;

    /**
     * 药店名称，可选。不可同时为null。
     */
    private String realName;

    /**
     * 手机号，可选。不可同时为null。
     */
    private String mobile;

    /**
     * 页码数，从1开始
     */
    private int pageNum;

    /**
     * 每页行数
     */
    private int pageSize;

}
