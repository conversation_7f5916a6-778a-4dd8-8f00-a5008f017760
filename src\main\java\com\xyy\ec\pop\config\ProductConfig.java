package com.xyy.ec.pop.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xyy.ec.pop.valid.NewProductAuditValidInfo;
import com.xyy.ec.pop.valid.ProductAuditValidInfo;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Componentadong
 * @Date 2022/7/28
 */
@Component
@Data
public class ProductConfig {
    public static ProductConfig config;
    public ProductConfig(){
        config=this;
    }
    /**
     * 判断商品为医疗器械的一级分类
     */
    @Value("${product.instrument.category}")
    public String instrumentCategory;
    @ApolloJsonValue("${product.cosmetics.category:[\"100001\",\"100007\"]}")
    public List<String> cosmeticsCategory;
    @ApolloJsonValue("${product.drug.category}")
    public List<String> drugCategory;
    @ApolloJsonValue("${product.chiMed.category}")
    public List<String> chiMedCategory;
    @ApolloJsonValue("${product.common.category}")
    public List<String> commonCategory;
    @ApolloJsonValue("${product.instrument.audit.categoryValidV2}")
    public List<ProductAuditValidInfo> instrumentAuditValidInfo;
    @ApolloJsonValue("${product.instrument.audit.newCategoryValidV2}")
    public List<NewProductAuditValidInfo> newInstrumentAuditValidInfo;
    @ApolloJsonValue("${product.instrument.audit.excludesCategory}")
    public List<Integer> instrumentAuditValidExcludesCategory;
    @ApolloJsonValue("${product.instrument.audit.newExcludesCategory}")
    public List<String> newInstrumentAuditValidExcludesCategory;
    @Value("${product.drug.approvalPattern}")
    public String drugApprovalPattern;
    @Value("${product.instrument.license.pattern}")
    public String instrumentLicensePattern;
    @Value("${product.manufacturing.license.noPattern.withInstrument}")
    public String manufacturingLicenseNoPatternWithInstrument;
    @Value("${product.manufacturing.license.pattern}")
    public String manufacturingLicensePattern;
}
