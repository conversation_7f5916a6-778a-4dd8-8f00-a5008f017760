package com.xyy.ec.pop.helper;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.config.ProductBatchUpdateConfig;
import com.xyy.ec.pop.enums.DrugClassificationEnum;
import com.xyy.ec.pop.server.api.product.dto.*;
import com.xyy.ec.pop.server.api.product.enums.PopSkuStatus;
import com.xyy.ec.pop.vo.*;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.query.EcSkuSellerAdminPageQuery;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.result.*;
import com.xyy.ec.product.back.end.ecp.stock.SkuStockLogParamDTO;
import com.xyy.ec.product.back.end.ecp.stock.dto.BPageDto;
import com.xyy.ec.product.back.end.ecp.stock.dto.EcpProductOwnerStockLogDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 商品转换工具
 * <AUTHOR>
 * @Date 2020/12/28
 */
public class ProductSkuConvertHelper {
    public static SkuAdminPageQuery convertToQuery(ProductVo productVo) {
        SkuAdminPageQuery query = new SkuAdminPageQuery();
        query.setKeyword(productVo.getKeyword());
        query.setBarcode(productVo.getBarcode());
        query.setOriginalBarcode(productVo.getOriginalBarcode());
        query.setCsuid(productVo.getCsuid());
        query.setShowName(productVo.getShowName());
        query.setManufacturer(productVo.getManufacturer());
        query.setCompanyName(productVo.getCompanyName());
        query.setName(productVo.getName());
        query.setStatus(productVo.getStatus());
        query.setCreateBeginTime(productVo.getCreateBeginTime());
        query.setCreateEndTime(productVo.getCreateEndTime());
        query.setIsThirdCompany(productVo.getIsThirdCompany());
        query.setAuditingState(productVo.getAuditingState());
        query.setPageNum(productVo.getPageNum());
        query.setPageSize(productVo.getPageSize());
        query.setApprovalNumber(productVo.getApprovalNumber());
        query.setStandardProductId(productVo.getStandardProductId());
        query.setSource(productVo.getSource());
        query.setAuthReason(productVo.getAuthReason());
        query.setReportToStandard(productVo.getReportToStandard());
        query.setStockStatus(productVo.getStockStatus());
        query.setProvIds(productVo.getProvIds());
        query.setHighGrosses(StringUtils.isEmpty(productVo.getHighGross()) ? null :
                Arrays.stream(productVo.getHighGross().split(",")).map(item -> Byte.valueOf(item)).collect(Collectors.toList()));
        query.setDirection(productVo.getDirection());
        query.setProperty(Objects.equals(productVo.getProperty(), "available_qty") ? "stock" : productVo.getProperty());
        query.setActivityType(productVo.getActivityType());
        query.setDisable(productVo.getDisable());
        query.setTracingCode(productVo.getTracingCode());
        return query;
    }
    public static Page<ProductListVo> convertToPage(PageInfo<PopSkuDetailDto> detailPage) {
        Page<ProductListVo> pageInfo = new Page<>();
        Long totalRow = detailPage.getTotal();
        pageInfo.setRows(convertToVo(detailPage.getList()));
        pageInfo.setTotal(totalRow);
        pageInfo.setPageCount(detailPage.getPages());
        pageInfo.setCurrentPage(detailPage.getPageNum());
        return pageInfo;
    }

    /**
     * 类型适配
     * @param detailPage
     * @return
     */
    public static Page<ProductListVo> convertToEcPage(PageInfo<EcSkuDetailDto> detailPage) {
        Page<ProductListVo> pageInfo = new Page<>();
        Long totalRow = detailPage.getTotal();
        pageInfo.setRows(convertToEcVo(detailPage.getList()));
        pageInfo.setTotal(totalRow);
        pageInfo.setPageCount(detailPage.getPages());
        pageInfo.setCurrentPage(detailPage.getPageNum());
        return pageInfo;
    }
    /**
     * 目前只适用商品列表
     * @param list
     * @return
     */
    private static List<ProductListVo> convertToVo(List<PopSkuDetailDto> list) {
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>(0);
        }
        return list.stream().map(ProductSkuConvertHelper::convertToVo).collect(Collectors.toList());
    }

    private static List<ProductListVo> convertToEcVo(List<EcSkuDetailDto> list) {
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>(0);
        }
        return list.stream().map(ProductSkuConvertHelper::convertToEcVo).collect(Collectors.toList());
    }
    /**
     * 目前只适用商品列表
     * @param dto
     * @return
     */
    private static ProductListVo convertToVo(PopSkuDetailDto dto) {
        ProductListVo vo = new ProductListVo();
        PopSkuDto sku = dto.getPopSku();

        vo.setDisableType(sku.getDisableType());
        vo.setDisableTypeName(sku.getDisableTypeName());
        vo.setDisableNote(sku.getDisableNote());

        vo.setOrgId(sku.getOrgId());
        vo.setSupplierId(sku.getSupplierId());
        vo.setStandardProductId(sku.getStandardProductId());
        vo.setId(sku.getId().toString());
        vo.setCommonName(sku.getCommonName());
        vo.setName(sku.getName());
        vo.setProductName(sku.getProductName());
        vo.setShowName(sku.getShowName());
        vo.setCompanyName(sku.getCompanyName());
        vo.setBarcode(sku.getBarcode());
        vo.setCsuid(sku.getCsuid());
        vo.setSource(dto.getPopSkuCategory()==null?0:dto.getPopSkuCategory().getSource());
        vo.setSpec(sku.getSpec());
        vo.setManufacturer(sku.getManufacturer());
        vo.setDrugClassification(sku.getDrugClassification().toString());
        vo.setFob(sku.getFob().toString());
        vo.setChainPrice(sku.getChainPrice()==null?"":sku.getChainPrice().toPlainString());
        vo.setSuggestPrice(sku.getSuggestPrice()==null?"-":sku.getSuggestPrice().toString());
        vo.setCreateTime(sku.getCreateTime());
        vo.setStatus(sku.getStatus().toString());
        vo.setLogicStatus(sku.getLogicStatus());
        vo.setErpCode(sku.getErpCode());
        vo.setApprovalNumber(sku.getApprovalNumber());
        vo.setTerm(sku.getTerm());
        vo.setSkuCategory(sku.getSkuCategory());
        vo.setApprovalNumber(sku.getApprovalNumber());
        vo.setImageUrl(sku.getImageUrl());
        vo.setShopCode(sku.getShopCode());
        vo.setAuthReason(sku.getAuthReason());
        vo.setAuthReasonName(sku.getAuthReasonName());
        vo.setReportToStandard(sku.getReportToStandard());
        vo.setAvailableQty(sku.getStock()==null?"0":sku.getStock().toString());
        PopSkuCategoryDto cate = dto.getPopSkuCategory();
        if(cate!=null){
            vo.setCategoryFirstId(cate.getBusinessFirstCategoryCode());
            vo.setCategorySecondId(cate.getBusinessSecondCategoryCode());
            vo.setCategoryThirdId(cate.getBusinessThirdCategoryCode());
            vo.setCategoryFourthId(cate.getBusinessFourthCategoryCode());
        }
        vo.setProvId(sku.getProvId());
        vo.setProv(sku.getProv());
        vo.setActivityType(sku.getActivityType());
        vo.setOriginalBarcode(sku.getOriginalBarcode());
        vo.setOriginalCsuid(sku.getOriginalCsuid());
        vo.setActivityId(sku.getActivityId());
        vo.setActivityStatus(sku.getActivityStatus());
        vo.setActivityPreheatTime(sku.getActivityPreheatTime());
        vo.setActivityStartTime(sku.getActivityStartTime());
        vo.setActivityEndTime(sku.getActivityEndTime());
        vo.setStandardImageUrl(sku.getStandardImageUrl());
        vo.setUseStandardImage(sku.getUseStandardImage());
        vo.setStandardImageVersion(sku.getStandardImageVersion());

        // 说明书信息
        PopSkuInstructionDto popSkuInstruction = dto.getPopSkuInstruction();
        if(null != popSkuInstruction){
            // 设置是否有追溯码字段
            vo.setTracingCode(popSkuInstruction.getTracingCode());
        }

        return vo;
    }

    private static ProductListVo convertToEcVo(EcSkuDetailDto dto) {
        ProductListVo vo = new ProductListVo();
        EcSkuDto sku = dto.getEcSku();
        vo.setOrgId(sku.getOrgId());
        vo.setSupplierId(sku.getSupplierId());
        vo.setStandardProductId(sku.getStandardProductId());
        vo.setId(sku.getId().toString());
        vo.setCommonName(sku.getCommonName());
        vo.setName(sku.getName());
        vo.setProductName(sku.getProductName());
        vo.setShowName(sku.getShowName());
        vo.setCompanyName(sku.getCompanyName());
        vo.setBarcode(sku.getBarcode());
        vo.setCsuid(sku.getCsuid());
        vo.setSource((dto.getEcSkuCategory()==null || dto.getEcSkuCategory().getSource() ==null) ?0:dto.getEcSkuCategory().getSource());
        vo.setSpec(sku.getSpec());
        vo.setManufacturer(sku.getManufacturer());
        vo.setDrugClassification(Objects.isNull(sku.getDrugClassification()) ? null : sku.getDrugClassification().toString());
        vo.setDrugClassificationName((Objects.isNull(sku.getDrugClassification()) || Objects.equals(sku.getDrugClassification(), 0)) ? null : DrugClassificationEnum.get(sku.getDrugClassification()));
        vo.setFob(Objects.isNull(sku.getFob()) ? null : sku.getFob().toString());
        vo.setChainPrice(sku.getGuidePrice()==null?"":sku.getGuidePrice().toPlainString());
        vo.setSuggestPrice(sku.getSuggestPrice()==null?"-":sku.getSuggestPrice().toString());
        vo.setCreateTime(sku.getCreateTime());
        vo.setStatus(sku.getStatus().toString());
        vo.setStatusName(PopSkuStatus.getByValue(sku.getStatus()).getName());
        vo.setLogicStatus(sku.getLogicStatus());
        vo.setErpCode(sku.getErpCode());
        vo.setApprovalNumber(sku.getApprovalNumber());
        vo.setTerm(sku.getTerm());
        vo.setSkuCategory(sku.getSkuCategory());
        vo.setApprovalNumber(sku.getApprovalNumber());
        vo.setImageUrl(sku.getImageUrl());
        vo.setShopCode(sku.getShopCode());
        vo.setAuthReason(sku.getAuthReason());
        vo.setAuthReasonName(sku.getAuthReasonName());
        vo.setReportToStandard(sku.getReportToStandard());
        vo.setAvailableQty(sku.getStock()==null?"0":sku.getStock().toString());
        EcSkuCategoryDto cate = dto.getEcSkuCategory();
        if(cate!=null){
            vo.setCategoryFirstId(cate.getBusinessFirstCategoryCode());
            vo.setCategorySecondId(cate.getBusinessSecondCategoryCode());
            vo.setCategoryThirdId(cate.getBusinessThirdCategoryCode());
            vo.setCategoryFourthId(cate.getBusinessFourthCategoryCode());
        }
        vo.setProvId(sku.getProvId());
        vo.setProv(sku.getProv());
        vo.setActivityType(sku.getProductType());
        vo.setOriginalBarcode(sku.getOriginalBarcode());
        vo.setOriginalCsuid(sku.getOriginalCsuid());
        vo.setActivityId(sku.getActivityId());
        vo.setActivityStatus(sku.getActivityStatus());
        vo.setActivityPreheatTime(sku.getActivityPreheatTime());
        vo.setActivityStartTime(sku.getActivityStartTime());
        vo.setActivityEndTime(sku.getActivityEndTime());
        vo.setStandardImageUrl(sku.getStandardImageUrl());
        vo.setUseStandardImage(sku.getUseStandardImage());
        vo.setStandardImageVersion(sku.getStandardImageVersion());
        return vo;
    }

    public static PopSkuAdminBatchUpdateDto convertToUpDateDto(SkuBatchUpdateVo vo) {
        PopSkuAdminBatchUpdateDto dto = new PopSkuAdminBatchUpdateDto();
        dto.setBarcode(vo.getBarcode());
        dto.setCommonName(vo.getCommonName());
        dto.setProductName(vo.getProductName());
        dto.setShowName(vo.getShowName());
        dto.setErpCode(vo.getErpCode());
        dto.setStatus(ProductBatchUpdateConfig.statusMap.get(vo.getStatus()));
        dto.setSkuCategory(vo.getSkuCategory());
        dto.setSkuCategoryId(vo.getSkuCategoryId());
        dto.setCode(vo.getCode());
        dto.setApprovalNumber(vo.getApprovalNumber());
        dto.setManufacturer(vo.getManufacturer());
        dto.setProductUnit(vo.getProductUnit());
        dto.setSpec(vo.getSpec());
        dto.setDosageForm(vo.getDosageForm());
        dto.setRemark(vo.getRemark());
        dto.setDrugClassification(ProductBatchUpdateConfig.drugsMap.get(vo.getDrugClassification()));
        dto.setBusinessFirstCategoryCode(vo.getBusinessFirstCategoryCode());
        dto.setBusinessSecondCategoryCode(vo.getBusinessSecondCategoryCode());
        dto.setBusinessThirdCategoryCode(vo.getBusinessThirdCategoryCode());
        dto.setBusinessFourthCategoryCode(vo.getBusinessFourthCategoryCode());
        dto.setSuggestPrice(vo.getSuggestPrice()==null?null:new BigDecimal(vo.getSuggestPrice()));
        dto.setSellingProposition1(vo.getSellingProposition1());
        dto.setSellingProposition2(vo.getSellingProposition2());
        dto.setSellingProposition3(vo.getSellingProposition3());

        return dto;
    }

    public static Page<ProductStockLogVo> convertStockLog(BPageDto<EcpProductOwnerStockLogDTO> pages, SkuStockLogParamDTO param) {
        Page<ProductStockLogVo> page = new Page<>();
        page.setLimit(pages==null?param.getPageSize():pages.getPageSize());
        int pageNum = pages==null?param.getPageNum():pages.getPageNo();
        page.setOffset((pageNum-1)*page.getLimit());
        page.setTotal(pages==null?0:pages.getTotalCount());
        page.setRows(pages==null|| CollectionUtils.isEmpty(pages.getRows()) ?new ArrayList(0)
                :pages.getRows().stream().filter(item->item!=null).map(item->convertStockLog(item)).collect(Collectors.toList()));
        return page;
    }
    public static ProductStockLogVo convertStockLog(EcpProductOwnerStockLogDTO log){
        ProductStockLogVo logvo = new ProductStockLogVo();
        logvo.setBarcode(log.getBarcode());
        logvo.setAfterQty(log.getAfterQty());
        logvo.setBeforeQty(log.getBeforeQty());
        logvo.setChangeQty(log.getChangeQty());
        logvo.setCreateTime(log.getCreateTime());
        logvo.setComment(log.getComment());
        logvo.setOrderNo(log.getOrderNo());
        logvo.setObjId(log.getObjId()==null||log.getObjId()==0?null:log.getObjId());
        return logvo;
    }

    public static ProductSkuVo convertToSkuVO(PopSkuDetailDto detail) {
        if(detail==null||detail.getPopSku()==null){
            return null;
        }
        PopSkuDto sku = detail.getPopSku();
        ProductSkuVo vo = new ProductSkuVo();
        vo.setId(sku.getId());
        vo.setStandardProductId(sku.getStandardProductId());
        vo.setCompanyName(sku.getCompanyName());
        vo.setOrgId(sku.getOrgId());
        vo.setManufacturer(sku.getManufacturer());
        vo.setProducer(sku.getProducer());
        vo.setApprovalNumber(sku.getApprovalNumber());
        vo.setAvailableQty(sku.getStock());
        vo.setBarcode(sku.getBarcode());
        vo.setBrand(sku.getBrand());
        vo.setCode(sku.getCode());
        vo.setCommonName(sku.getCommonName());
        vo.setDosageForm(sku.getDosageForm());
        vo.setDrugClassification(sku.getDrugClassification());
        vo.setFob(sku.getFob());
        vo.setChainPrice(sku.getChainPrice());
        vo.setBasePrice(sku.getBasePrice());
        vo.setGrossProfitMargin(sku.getGrossProfitMargin());
        vo.setChainGrossProfitMargin(sku.getChainGrossProfitMargin());
        vo.setIsFragileGoods(sku.getIsFragileGoods());
        vo.setIsNew(sku.getIsNew());
        vo.setIsSplit(sku.getIsSplit());
        vo.setMediumPackageNum(sku.getMediumPackageNum());
        vo.setPieceLoading(sku.getPieceLoading());
        vo.setProductName(sku.getProductName());
        vo.setProductUnit(sku.getProductUnit());
        vo.setShelfLife(sku.getShelfLife());
        vo.setShowName(sku.getShowName());

        vo.setSpec(sku.getSpec());
        vo.setStatus(sku.getStatus());
        vo.setStorageCondition(sku.getStorageCondition());
        vo.setSuggestPrice(sku.getSuggestPrice());
        vo.setTerm(sku.getTerm());
        vo.setZjm(sku.getZjm());
        vo.setSkuCategory(sku.getSkuCategory());
        vo.setSkuCategoryId(sku.getSkuCategoryId());
        vo.setErpCode(sku.getErpCode());
        vo.setOldestProDate(sku.getOldestProDate());
        vo.setNewProDate(sku.getNewProDate());
        vo.setNearEffect(sku.getNearEffect());
        vo.setFarEffect(sku.getFarEffect());
        vo.setCreateTime(sku.getCreateTime());
        vo.setIsInjection(sku.getIsInjection());
        vo.setIsPrescription(sku.getIsPrescription());
        vo.setPriceSyncErp(sku.getPriceSyncErp());
        vo.setStockSyncErp(sku.getStockSyncErp());
        vo.setAudited(sku.getAudited());
        vo.setSaleType(sku.getSaleType());
        vo.setShopCode(sku.getShopCode());
        vo.setMinPurchaseCount(sku.getMinPurchaseCount());
        vo.setBz(sku.getBz());
        vo.setPopSkuPurchaseLimitDto(detail.getPopSkuPurchaseLimitDto());
        vo.setSellingProposition1(sku.getSellingProposition1());
        vo.setSellingProposition2(sku.getSellingProposition2());
        vo.setSellingProposition3(sku.getSellingProposition3());
        boolean userStandardImage = sku.getUseStandardImage()!=null&&sku.getUseStandardImage();
        if(userStandardImage){
            List<String> list = Lists.newArrayList(sku.getStandardImageUrl().split(","));
            vo.setImageUrl(list.remove(0));
            vo.setImagesList(list);
        }else {
            vo.setImagesList(StringUtils.isEmpty(sku.getImageListUrl())?new ArrayList<>(): Arrays.asList(sku.getImageListUrl().split(",")));
            vo.setImageUrl(sku.getImageUrl());
        }
        vo.setActivityType(sku.getActivityType());


        //说明书
        PopSkuInstructionDto popSkuInstruction = detail.getPopSkuInstruction();
        if(popSkuInstruction!=null){
            vo.setAbstain(popSkuInstruction.getAbstain());
            vo.setComponent(popSkuInstruction.getComponent());
            vo.setConsiderations(popSkuInstruction.getConsiderations());
            vo.setIndication(popSkuInstruction.getIndication());
            vo.setInteraction(popSkuInstruction.getInteraction());
            vo.setUntowardEffect(popSkuInstruction.getUntowardEffect());
            vo.setUsageAndDosage(popSkuInstruction.getUsageAndDosage());
            vo.setSubtitle(popSkuInstruction.getSubtitle());
            vo.setInstrumentLicenseEffect(popSkuInstruction.getInstrumentLicenseEffect());
            vo.setManufacturingLicenseNo(popSkuInstruction.getManufacturingLicenseNo());
            vo.setManufacturingLicenseEffect(popSkuInstruction.getManufacturingLicenseEffect());
            vo.setInstrumentLicenseImagList(stringToList(popSkuInstruction.getInstrumentLicenseImage()));
            vo.setManufacturingLicenseImageList(stringToList(popSkuInstruction.getManufacturingLicenseImage()));
            vo.setTechnicalRequirementNo(popSkuInstruction.getTechnicalRequirementNo());
            vo.setMarketAuthor(popSkuInstruction.getMarketAuthor());
            vo.setAliasName(popSkuInstruction.getAliasName());
            vo.setFilingsAuthor(popSkuInstruction.getFilingsAuthor());
            vo.setIsCommissionProduction(popSkuInstruction.getIsCommissionProduction());
            vo.setEntrustedManufacturer(popSkuInstruction.getEntrustedManufacturer());
            vo.setEntrustedManufacturerAddress(popSkuInstruction.getEntrustedManufacturerAddress());
        }
        PopSkuInstructionImageDto instructionImages = detail.getPopSkuInstructionImages();
        if(instructionImages!=null){
            String inImage = userStandardImage?instructionImages.getStandardInstrutionImageUrl():instructionImages.getInstrutionImageUrl();
            vo.setSkuInstructionImageList(stringToList(inImage));
        }
        if(vo.getSkuInstructionImageList()==null){
            vo.setSkuInstructionImageList(new ArrayList<>());
        }

        PopSkuCategoryDto popSkuCategory = detail.getPopSkuCategory();
        if(popSkuCategory!=null){

        }
        vo.setSource(popSkuCategory.getSource());
        vo.setErpFirstCategoryId(popSkuCategory.getBusinessFirstCategoryCode());
        vo.setErpSecondCategoryId(popSkuCategory.getBusinessSecondCategoryCode());
        vo.setErpThirdCategoryId(popSkuCategory.getBusinessThirdCategoryCode());
        vo.setErpFourthCategoryId(popSkuCategory.getBusinessFourthCategoryCode());
        vo.setCommissionRatio(popSkuCategory.getCommissionRatio());

        PopBusAreaConifigDto busConfigDto = detail.getBusConfigDto();
        if(busConfigDto!=null){
            vo.setBusAreaId(busConfigDto.getId());
            vo.setBusAreaConfigName(busConfigDto.getBusAreaName());
        }
        return vo;
    }

    /**
     * 类型适配
     * @param detail
     * @return
     */
    public static ProductSkuVo convertToEcSkuVO(EcSkuDetailDto detail) {
        if(detail==null||detail.getEcSku()==null){
            return null;
        }
        EcSkuDto sku = detail.getEcSku();
        ProductSkuVo vo = new ProductSkuVo();
        vo.setId(sku.getId());
        vo.setStandardProductId(sku.getStandardProductId());
        vo.setCompanyName(sku.getCompanyName());
        vo.setOrgId(sku.getOrgId());
        vo.setManufacturingLicenseNo(sku.getManufacturingLicenseNo());
        vo.setManufacturer(sku.getManufacturer());
        vo.setProducer(sku.getProducer());
        vo.setApprovalNumber(sku.getApprovalNumber());
        vo.setAvailableQty(sku.getStock());
        vo.setBarcode(sku.getBarcode());
        vo.setBrand(sku.getBrand());
        vo.setCode(sku.getCode());
        vo.setCommonName(sku.getCommonName());
        vo.setDosageForm(sku.getDosageForm());
        vo.setDrugClassification(sku.getDrugClassification());
        vo.setFob(sku.getFob());
        vo.setChainPrice(sku.getChainPrice());
        vo.setBasePrice(sku.getBasePrice());
        vo.setGrossProfitMargin(sku.getGrossProfitMargin());
        vo.setChainGrossProfitMargin(sku.getChainGrossProfitMargin());
        vo.setIsFragileGoods(sku.getIsFragileGoods());
        vo.setIsNew(sku.getIsNew());
        vo.setIsSplit(sku.getIsSplit());
        vo.setMediumPackageNum(sku.getMediumPackageNum());
        vo.setPieceLoading(sku.getPieceLoading());
        vo.setProductName(sku.getProductName());
        vo.setProductUnit(sku.getProductUnit());
        vo.setShelfLife(sku.getShelfLife());
        vo.setShowName(sku.getShowName());
        vo.setSubtitle(sku.getSubTitle());

        vo.setSpec(sku.getSpec());
        vo.setStatus(sku.getStatus());
        vo.setStorageCondition(sku.getStorageCondition());
        vo.setSuggestPrice(sku.getSuggestPrice());
        vo.setTerm(sku.getTerm());
        vo.setZjm(sku.getZjm());
        vo.setSkuCategory(sku.getSkuCategory());
        vo.setSkuCategoryId(sku.getSkuCategoryId());
        vo.setErpCode(sku.getErpCode());
        vo.setOldestProDate(sku.getOldestProDate());
        vo.setNewProDate(sku.getNewProDate());
        vo.setNearEffect(sku.getNearEffect());
        vo.setFarEffect(sku.getFarEffect());
        vo.setCreateTime(sku.getCreateTime());
        vo.setIsInjection(sku.getIsInjection());
        vo.setIsPrescription(sku.getIsPrescription());
        vo.setPriceSyncErp(sku.getPriceSyncErp());
        vo.setStockSyncErp(sku.getStockSyncErp());
        vo.setAudited(sku.getAudited());
        vo.setSaleType(sku.getSaleType());
        vo.setShopCode(sku.getShopCode());
        vo.setMinPurchaseCount(sku.getMinPurchaseCount());
        vo.setBz(sku.getBz());
        if (Objects.nonNull(detail.getEcSkuPurchaseLimitDto())) {
            PopSkuPurchaseLimitDto popSkuPurchaseLimitDto = new PopSkuPurchaseLimitDto();
            popSkuPurchaseLimitDto.setId(detail.getEcSkuPurchaseLimitDto().getId());
            popSkuPurchaseLimitDto.setCsuid(detail.getEcSkuPurchaseLimitDto().getCsuId());
            popSkuPurchaseLimitDto.setLimitedQty(detail.getEcSkuPurchaseLimitDto().getLimitedQty());
            popSkuPurchaseLimitDto.setPurchaseType(detail.getEcSkuPurchaseLimitDto().getPurchaseType());
            popSkuPurchaseLimitDto.setAreaCode(detail.getEcSkuPurchaseLimitDto().getAreaCode());
            popSkuPurchaseLimitDto.setAreaCodeName(detail.getEcSkuPurchaseLimitDto().getAreaCodeName());
            popSkuPurchaseLimitDto.setUserType(detail.getEcSkuPurchaseLimitDto().getUserType());
            popSkuPurchaseLimitDto.setUserTypeName(detail.getEcSkuPurchaseLimitDto().getUserTypeName());
            popSkuPurchaseLimitDto.setCreateTime(detail.getEcSkuPurchaseLimitDto().getCreateTime());
            popSkuPurchaseLimitDto.setUpdateTime(detail.getEcSkuPurchaseLimitDto().getUpdateTime());
            popSkuPurchaseLimitDto.setIsDel(detail.getEcSkuPurchaseLimitDto().getIsDel());
            popSkuPurchaseLimitDto.setOperator(detail.getEcSkuPurchaseLimitDto().getOperator());
            vo.setPopSkuPurchaseLimitDto(popSkuPurchaseLimitDto);
        }
        vo.setSellingProposition1(sku.getSellingProposition1());
        vo.setSellingProposition2(sku.getSellingProposition2());
        vo.setSellingProposition3(sku.getSellingProposition3());
        boolean userStandardImage = sku.getUseStandardImage()!=null&&sku.getUseStandardImage();
        if(userStandardImage){
            List<String> list = Lists.newArrayList(sku.getStandardImageUrl().split(","));
            vo.setImageUrl(list.remove(0));
            vo.setImagesList(list);
        }else {
            vo.setImagesList(StringUtils.isEmpty(sku.getImageListUrl())?new ArrayList<>(): Arrays.asList(sku.getImageListUrl().split(",")));
            vo.setImageUrl(sku.getImageUrl());
        }
        vo.setActivityType(sku.getActivityType());

        //说明书
        EcSkuInstructionDto popSkuInstruction = detail.getEcSkuInstruction();
        if(popSkuInstruction!=null){
            vo.setAbstain(popSkuInstruction.getAbstain());
            vo.setComponent(popSkuInstruction.getComponent());
            vo.setConsiderations(popSkuInstruction.getConsiderations());
            vo.setIndication(popSkuInstruction.getIndication());
            vo.setInteraction(popSkuInstruction.getInteraction());
            vo.setUntowardEffect(popSkuInstruction.getUntowardEffect());
            vo.setUsageAndDosage(popSkuInstruction.getUsageAndDosage());
            vo.setInstrumentLicenseEffect(popSkuInstruction.getInstrumentLicenseEffect());
//            vo.setManufacturingLicenseNo(popSkuInstruction.getManufacturingLicenseNo());
            vo.setManufacturingLicenseEffect(popSkuInstruction.getManufacturingLicenseEffect());
            vo.setInstrumentLicenseImagList(stringToList(popSkuInstruction.getInstrumentLicenseImage()));
            vo.setManufacturingLicenseImageList(stringToList(popSkuInstruction.getManufacturingLicenseImage()));
            vo.setTechnicalRequirementNo(popSkuInstruction.getTechnicalRequirementNo());
            vo.setMarketAuthor(popSkuInstruction.getMarketAuthor());
            vo.setAliasName(popSkuInstruction.getAliasName());
        }
        EcSkuInstructionImageDto instructionImages = detail.getEcSkuInstructionImages();
        if(instructionImages!=null){
            String inImage = userStandardImage?instructionImages.getStandardInstrutionImageUrl():instructionImages.getInstrutionImageUrl();
            vo.setSkuInstructionImageList(stringToList(inImage));
        }
        if(vo.getSkuInstructionImageList()==null){
            vo.setSkuInstructionImageList(new ArrayList<>());
        }

        EcSkuCategoryDto popSkuCategory = detail.getEcSkuCategory();
        if(popSkuCategory!=null){
            vo.setSource(popSkuCategory.getSource());
            vo.setErpFirstCategoryId(popSkuCategory.getBusinessFirstCategoryCode());
            vo.setErpSecondCategoryId(popSkuCategory.getBusinessSecondCategoryCode());
            vo.setErpThirdCategoryId(popSkuCategory.getBusinessThirdCategoryCode());
            vo.setErpFourthCategoryId(popSkuCategory.getBusinessFourthCategoryCode());
            vo.setCommissionRatio(popSkuCategory.getCommissionRatio());
        }
//        PopBusAreaConifigDto busConfigDto = detail.getBusConfigDto();
//        if(busConfigDto!=null){
//            vo.setBusAreaId(busConfigDto.getId());
//            vo.setBusAreaConfigName(busConfigDto.getBusAreaName());
//        }

        return vo;
    }

    private static List<String> stringToList(String value) {
        if(StringUtils.isEmpty(value)){
            return new ArrayList<>();
        }
        return Arrays.asList(value.split(","));
    }

    /**
     * 类型适配
     * @param productVo
     * @return
     */
    public static EcSkuSellerAdminPageQuery convertToEcQuery(ProductVo productVo) {
        EcSkuSellerAdminPageQuery query = new EcSkuSellerAdminPageQuery();
        query.setKeyword(productVo.getKeyword());
        query.setShopCode(productVo.getShopCode());
        query.setBarcode(productVo.getBarcode());
        query.setOriginalBarcode(productVo.getOriginalBarcode());
        query.setCsuid(productVo.getCsuid());
        query.setShowName(productVo.getShowName());
        query.setManufacturer(productVo.getManufacturer());
        query.setCompanyName(productVo.getCompanyName());
        query.setName(productVo.getName());
        query.setStatus(productVo.getStatus());
        query.setCreateBeginTime(productVo.getCreateBeginTime());
        query.setCreateEndTime(productVo.getCreateEndTime());
        query.setIsThirdCompany(productVo.getIsThirdCompany());
        query.setAuditingState(productVo.getAuditingState());
        query.setPageNum(productVo.getPageNum());
        query.setPageSize(productVo.getPageSize());
        query.setApprovalNumber(productVo.getApprovalNumber());
        query.setStandardProductId(productVo.getStandardProductId());
        query.setSource(productVo.getSource());
        query.setAuthReason(productVo.getAuthReason());
        query.setReportToStandard(productVo.getReportToStandard());
        query.setStockStatus(productVo.getStockStatus());
        query.setBranchCodes(productVo.getBranchCodes());
        query.setHighGrosses(StringUtils.isEmpty(productVo.getHighGross()) ? null :
                Arrays.stream(productVo.getHighGross().split(",")).map(item -> Byte.valueOf(item)).collect(Collectors.toList()));
        query.setDirection(productVo.getDirection());
        query.setProperty(Objects.equals(productVo.getProperty(), "available_qty") ? "stock" : productVo.getProperty());
        query.setActivityType(productVo.getActivityType());
        return query;
    }
}
