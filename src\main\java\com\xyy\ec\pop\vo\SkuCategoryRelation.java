package com.xyy.ec.pop.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Author:xinghu.zhang
 * @Description:
 * @Date:Created in 14:01 2018/5/10
 * @Modified By:
 **/
@ApiModel("商品分类关联实体")
public class SkuCategoryRelation implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 第一分类
     */
    @ApiModelProperty("第一分类")
    private String categoryFirstId;
    /**
     * 第一分类名称
     */
    @ApiModelProperty("第一分类名称")
    private String categoryFirstName;
    /**
     * 第二分类
     */
    @ApiModelProperty("第二分类")
    private String categorySecondId;
    /**
     * 第二分类名称
     */
    @ApiModelProperty("第二分类名称")
    private String categorySecondName;
    /**
     * 第三分类
     */
    @ApiModelProperty("第三分类")
    private String categoryThirdId;
    /**
     * 第三分类名称
     */
    @ApiModelProperty("第三分类名称")
    private String categoryThirdName;

    public String getCategoryFirstId() {
        return categoryFirstId;
    }

    public void setCategoryFirstId(String categoryFirstId) {
        this.categoryFirstId = categoryFirstId;
    }

    public String getCategoryFirstName() {
        return categoryFirstName;
    }

    public void setCategoryFirstName(String categoryFirstName) {
        this.categoryFirstName = categoryFirstName;
    }

    public String getCategorySecondId() {
        return categorySecondId;
    }

    public void setCategorySecondId(String categorySecondId) {
        this.categorySecondId = categorySecondId;
    }

    public String getCategorySecondName() {
        return categorySecondName;
    }

    public void setCategorySecondName(String categorySecondName) {
        this.categorySecondName = categorySecondName;
    }

    public String getCategoryThirdId() {
        return categoryThirdId;
    }

    public void setCategoryThirdId(String categoryThirdId) {
        this.categoryThirdId = categoryThirdId;
    }

    public String getCategoryThirdName() {
        return categoryThirdName;
    }

    public void setCategoryThirdName(String categoryThirdName) {
        this.categoryThirdName = categoryThirdName;
    }
}
