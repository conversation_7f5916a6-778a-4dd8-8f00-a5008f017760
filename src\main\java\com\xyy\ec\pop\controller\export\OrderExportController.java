package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.export.param.OrderExportAdminParam;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2021/07/28
 */
@Slf4j
@RequestMapping("/orderExport")
@RestController
public class OrderExportController extends BaseController {
    @Value("${deliver.timeout.hours}")
    private Integer timeoutHours;
    @Autowired
    private DownloadRemote downloadRemote;


    /**
     * 导出订单明细数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportOrderDetails")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportOrderDetails(OrderExportAdminParam queryDto) {
        log.info("OrderExportController.exportOrderDetails#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            if(null != queryDto.getIsThirdCompany() && Objects.equals(queryDto.getIsThirdCompany(), 0)){
                return ResponseVo.errRest("导出失败，暂不支持自营订单导出数据");
            }
            queryDto.setProvIds(provIds);
            //第三方公司
            queryDto.setIsThirdCompany(ProductEnumDTO.ThirdCompany.IS_THIRD.getId());
            if (StringUtils.isNotBlank(queryDto.getStatusList())) {
                queryDto.setTimeoutHours(timeoutHours);
            }
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.ORDER)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("OrderExportController.exportOrderDetails#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("OrderExportController.exportOrderDetails#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

}
