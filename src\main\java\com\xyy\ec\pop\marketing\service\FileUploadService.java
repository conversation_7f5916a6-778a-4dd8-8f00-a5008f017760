package com.xyy.ec.pop.marketing.service;

import org.apache.commons.net.ftp.FTPFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

public interface FileUploadService {

    /**
     * 上传文件
     *
     * @param multipartFile 文件
     * @param module        二级目录
     * @return
     */
    String uploadFile(MultipartFile multipartFile, String module);

    /**
     * 上传文件
     *
     * @param inputStream
     * @param filename
     * @param module
     * @return
     */
    String uploadFile(InputStream inputStream, String filename, String module) ;

    FTPFile[] listFiles(String ftpAbsoluteDir);

    boolean deleteFile(String pathname);

    boolean removeDirectory(String pathname);

}
