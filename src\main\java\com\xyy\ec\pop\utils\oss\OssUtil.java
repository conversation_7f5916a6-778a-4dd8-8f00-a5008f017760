package com.xyy.ec.pop.utils.oss;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.utils.IOUtils;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.xyy.ec.pop.config.OssConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

@Slf4j
@Component
public class OssUtil implements ApplicationContextAware {
    private static OssConfig ossConfig;

    private static OSS getOSSClient(OssConfig ossConfig){
        // 创建ClientConfiguration实例，您可以按照实际情况修改默认参数。
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        // 设置是否支持CNAME。CNAME用于将自定义域名绑定到目标Bucket。
        conf.setSupportCname(true);

        // 创建OSSClient实例。
        return new OSSClientBuilder().build(ossConfig.getEndpoint(), ossConfig.getAccessKeyId(), ossConfig.getAccessKeySecret(), conf);
    }

    /**
     * 上传流信息到oss
     *
     * @param inputStream
     * @param folder      可以为空
     * @param fileName
     * @return 异常时返回null
     */
    public static String upload(InputStream inputStream, String folder, String fileName) {
        OSS client = getClient();
        //folder+/+fileName
        String key = StringUtils.isEmpty(folder) ? fileName : folder + "/" + fileName;
        try {
            key = fixKey(key);
            String bucketName = ossConfig.getBucketName();
            log.info("ossUtil upload use bucketName:{}", bucketName);
            client.putObject(bucketName, key, inputStream);
            return key;
        } catch (Exception e) {
            log.error("OssUtil.upload#folder：{}，fileName：{} 异常", folder, fileName, e);
            throw e;
        } finally {
            client.shutdown();
        }
    }

    /**
     * 上传文件流
     * @param ossConfig
     * @param ossFullUrl   oss完整路径url
     * @param fileInputStream  文件流
     */
    public static boolean uploadFileInputStream(OssConfig ossConfig, String ossFullUrl, InputStream fileInputStream){
        if(StringUtils.isEmpty(ossFullUrl) || fileInputStream == null){
            log.info("oss上传文件oss路径或文件流为空");
            return false;
        }
        if(ossFullUrl.startsWith("/")){
            ossFullUrl = ossFullUrl.substring(1);
        }
        OSS ossClient = getOSSClient(ossConfig);
        boolean result = true;
        try{
            // 依次填写Bucket名称（例如examplebucket）和Object完整路径（例如exampledir/exampleobject.txt）。Object完整路径中不能包含Bucket名称。
            ossClient.putObject(ossConfig.getBucketName(), ossFullUrl, fileInputStream);
        }catch (Exception e){
            log.error("oss上传文件异常:", e);
            result = false;
        }finally {
            if(ossClient != null){
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
        return result;
    }

    /**
     * 图片压缩
     *
     * @param originKey oss中的原始文件
     * @param newKey    新的文件
     * @param width
     * @param height
     * @return
     * @throws IOException
     */
    public static String reSize(String originKey, String newKey, int width, int height) throws IOException {
        OSS client = getClient();
        originKey = fixKey(originKey);
        newKey = fixKey(newKey);
        /**
         * 控制台设置的图片处理的style或者默认style
         */
        String style = String.format("image/resize,h_%s,w_%s,m_pad", width, height);
        GetObjectRequest request = new GetObjectRequest(ossConfig.getBucketName(), originKey);
        request.setProcess(style);
        OSSObject ossObject = client.getObject(request);
        InputStream inputStream = ossObject.getObjectContent();
        try {
            String bucketName = ossConfig.getBucketName();
            log.info("ossUtil resize use bucketName:{}", bucketName);
            client.putObject(bucketName, newKey, inputStream);
        } catch (Exception e) {
            log.error("OssUtil.reSize#ossFileKey：{}，newKey：{} ,w:{}，h:{}异常", originKey, newKey, width, height, e);
            throw e;
        } finally {
            client.shutdown();
        }
        return newKey;
    }

    public static String fixKey(String key) {
        //key不能已/开头
        if (key != null) {
            if (key.startsWith("/")) {
                key = key.substring(1);
            }
            key = key.replace("//", "/");
        }
        return key;
    }

    /**
     * 获取客户端
     *
     * @return
     */
    private static OSS getClient() {
        return new OSSClientBuilder().build(ossConfig.getEndpoint(), ossConfig.getAccessKeyId(), ossConfig.getAccessKeySecret());
    }

    /**
     * 下载文件
     *
     * @param file 文件路径+文件名
     * @param out
     */
    public static void downloadFile(String file, OutputStream out) throws IOException {
        OSS client = getClient();
        file = fixKey(file);
        GetObjectRequest request = new GetObjectRequest(ossConfig.getBucketName(), file);
        OSSObject ossObject = client.getObject(request);
        try {
            out.write(IOUtils.readStreamAsByteArray(ossObject.getObjectContent()));
        } catch (IOException e) {
            log.error("OssUtil.downloadFile#file：{} 异常", file, e);
            throw e;
        } finally {
            client.shutdown();
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        OssConfig config = applicationContext.getBean("ossConfig", OssConfig.class);
        OssUtil.ossConfig = config;
    }
}
