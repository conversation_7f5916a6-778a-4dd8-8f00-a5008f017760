package com.xyy.ec.pop.marketing.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.merchant.bussiness.api.pop.MerchantPopBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 客户远程调用适配
 * <AUTHOR>
 */
@Service
@Slf4j
public class MerchantRemoteAdapter {
    @Reference
    private MerchantPopBusinessApi merchantPopBusinessApi;

    public Map<Long, MerchantBussinessDto> findMerchantAndMerchantIdList(List<Long> ids) {
        try {
            log.info("MerchantRemoteAdapter.findMerchantAndMerchantIdList#ids:{}", JSON.toJSONString(ids));
            List<MerchantBussinessDto> list = merchantPopBusinessApi.findMerchantAndMerchantIdList(ids);
            log.info("MerchantRemoteAdapter.findMerchantAndMerchantIdList#ids:{} return {}", JSON.toJSONString(ids), JSON.toJSONString(list));
            return list.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
        } catch (Exception e) {
            log.error("MerchantRemoteAdapter.findMerchantAndMerchantIdList#ids:{} 异常", JSON.toJSONString(ids), e);
        }
        throw new XyyEcPopException("查询药店信息失败");
    }

}
