package com.xyy.ec.pop.controller;


import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.ResultVO;
import com.xyy.ec.pop.remote.CorporationRemote;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.product.dto.*;
import com.xyy.ec.pop.service.PopDrugReportService;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/pop")
public class PopDrugReportController extends BaseController {


    @Autowired
    private PopDrugReportService popDrugReportService;

    @Autowired
    private DownloadRemote downloadRemote;

    @Autowired
    private CorporationRemote corporationRemote;


    /**
     * 查询药检报告列表
     * @return
     */
    @RequestMapping(value = "/drug/report/query")
    public ResultVO<Object> drugReportQuery(@RequestBody PopDrugReportDto query) {
        try {
            query.setSysType(DownloadTaskSysTypeEnum.ADMIN.getType());
            return ResultVO.createSuccess(popDrugReportService.drugReportQuery(query));
        } catch (Exception e) {
            log.error("查询药检报告列表：", e);
            return ResultVO.createFail("查询药检报告列表异常");
        }
    }

    /**
     * 下载附件
     */
    @GetMapping(value = "/drug/report/downZipUrl")
    public void downZipUrl(@RequestParam(value = "barCode") String barCode,
                           @RequestParam(value = "batchCode") String batchCode,
                           @RequestParam(value = "sealStatus") String sealStatus,  HttpServletResponse response) {
        try {
            String downZipUrl = popDrugReportService.downZipUrl(barCode,batchCode,sealStatus);
            response.setCharacterEncoding("UTF-8"); // 重点突出
            response.setContentType("application/zip");// 不同类型的文件对应不同的MIME类型 // 重点突出
            // inline在浏览器中直接显示，不提示用户下载
            // attachment弹出对话框，提示用户进行下载保存本地
            // 默认为inline方式
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(barCode + "资质文件" + ("1".equals(sealStatus) ? "(盖章)" : "") + ".zip", "utf-8"));
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                HttpUtil.download(downZipUrl, outputStream, true);
            }
        } catch (Exception e) {
            log.error("下载zip包失败：", e);
        }
    }








    @PostMapping(value = "/drug/report/export")
    public ResponseVo<Boolean> export(@RequestBody PopDrugReportDto query) {
        try {

            if (StringUtils.isBlank(query.getName()) &&
                    StringUtils.isBlank(query.getCompanyName()) &&
                    StringUtils.isBlank(query.getOrgId())){
                return ResponseVo.errRest("请选择商家查询条件后进行导出");
            }

            //TODO 校验查询条件是不是只有一个商家
            CorporationDto dto = new CorporationDto();
            dto.setName(query.getName());
            dto.setCompanyName(query.getCompanyName());
            dto.setOrgId(query.getOrgId());
            List<CorporationDto> corporationDtos = corporationRemote.queryList(dto);
            if (CollectionUtils.isEmpty(corporationDtos)){
                query.setOrgId("unFound");
            }else {
                if (corporationDtos.size() > 1){
                    return ResponseVo.errRest("请选择一个商家进行导出");
                }
                query.setOrgId(corporationDtos.get(0).getOrgId());
            }

            log.info("PopDrugReportController.export#queryVo:{}", JSON.toJSONString(query));
            DownloadFileContent content = DownloadFileContent.builder()
                    .orgId(query.getOrgId())
                    .query(query)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.DRUG_REPORT_QUALIFICATION)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("PopDrugReportController.export#queryVo:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        }catch (Exception e) {
            log.error("PopDrugReportController.export#queryVo:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }






}
