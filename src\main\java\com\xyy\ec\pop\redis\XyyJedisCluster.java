package com.xyy.ec.pop.redis;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.redis.impl.RedisService;
import com.xyy.ec.pop.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

/**
 * 版权：小药药
 * 作者：<EMAIL>
 * 生成日期：2017/3/14
 * 描述：药帮忙jedis API
 */
@Component
public  class XyyJedisCluster {

    @Autowired
    private RedisService redisService;
    /**
     * 保存对象到redis
     *
     * @param key
     * @param object
     * @return
     */
    public Boolean set(String key, Object object) {
        if(object != null) {
            return redisService.setValue(key, JSON.toJSONString(object), TimeUnit.SECONDS, 3600L * 24);
        }else {
            return false;
        }
    }

    /**
     * 从redis获取对象
     *
     * @param key
     * @param clazz 获取对象的类型
     * @return
     */
    public <T> T get(String key, Class<T> clazz) {
        String json = redisService.getValue(key);
        if(StringUtils.isEmpty(json)){
            return null;
        }
        return JSON.parseObject(json,clazz);
    }

    /**
     * 将对象保存到redis并设置过期时间
     *
     * @param key
     * @param seconds
     * @param str
     * @return
     */
    public boolean setex(final String key, final int seconds, final String str) {
        if(StringUtils.isEmpty(str)){
            return false;
        }
        return redisService.setValue(key,str,TimeUnit.SECONDS,seconds);
    }

    public boolean setexHour(final String key, final int hours, final String str) {
        if(StringUtils.isEmpty(str)){
            return false;
        }
        return redisService.setValue(key,str,TimeUnit.HOURS,hours);
    }
    /**
     * 延长指定key的过期时间
     * @param key
     * @param seconds
     * @param timeUnit
     * @return
     */
    public boolean expireKey(final String key, final int seconds, TimeUnit timeUnit) {
        return redisService.expireKey(key,seconds,timeUnit);
    }

    /**
     * 从redis获取对象
     *
     * @param key
     * @return
     */
    public String  get(String key) {
        String jsonStr = redisService.getValue(key);
        if(StringUtil.isEmpty(jsonStr)){
            return null;
        }
        return jsonStr;
    }

    /**
     * 从redis获取对象
     *
     * @param key
     * @return
     */
    public boolean  exists(String key) {
        return redisService.hasKey(key);
    }

    /**
     * 通过key删除
     *
     * @param key
     * @return
     */
    public Boolean del(String key) {
        redisService.deleteKey(key);
        return true;
    }
}