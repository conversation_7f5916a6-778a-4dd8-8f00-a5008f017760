package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.shop.server.business.api.ShopUserTypeApi;
import com.xyy.ec.shop.server.business.results.ShopUserTypeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 供货类型
 */
@Component
@Slf4j
public class ShopUserTypeRemote {
    @Reference
    private ShopUserTypeApi shopUserTypeApi;

    public boolean saveOrUpdateShopUserType(ShopUserTypeDTO dto) {
        try {
            log.info("ShopUserTypeRemote.saveOrUpdateShopUserType#dto:{}", JSON.toJSONString(dto));
            ApiRPCResult<Integer> result = shopUserTypeApi.saveOrUpdateShopUserType(dto);
            log.info("ShopUserTypeRemote.saveOrUpdateShopUserType#dto:{} return {}", JSON.toJSONString(dto), JSON.toJSONString(result));
            return result.isSuccess();
        } catch (Exception e) {
            log.error("ShopUserTypeRemote.saveOrUpdateShopUserType#dto:{} 异常", JSON.toJSONString(dto), e);
            return false;
        }
    }

    public Map<String, String> getShopUserTypeByShopCode(List<String> shopCode) {
        try {
            log.info("ShopUserTypeRemote.getShopUserTypeByShopCode#shopCode:{}", JSON.toJSONString(shopCode));
            ApiRPCResult<List<ShopUserTypeDTO>> result = shopUserTypeApi.getShopUserTypeByShopCode(shopCode);
            log.info("ShopUserTypeRemote.getShopUserTypeByShopCode#shopCode:{} return {}", JSON.toJSONString(shopCode), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData().stream().collect(Collectors.toMap(item -> item.getShopCode(), item -> item.getUserType()));
            }

        } catch (Exception e) {
            log.error("ShopUserTypeRemote.getShopUserTypeByShopCode#shopCode:{} 异常", JSON.toJSONString(shopCode), e);
        }
        return new HashMap<>(0);
    }
}
