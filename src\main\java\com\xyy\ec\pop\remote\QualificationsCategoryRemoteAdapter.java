package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.api.admin.QualificationsCategoryAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.QualificationsCategoryDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @version v1
 * @Description 资质分类调用
 * <AUTHOR>
 */
@Component
@Slf4j
public class QualificationsCategoryRemoteAdapter {
    @Reference
    private QualificationsCategoryAdminApi qualificationsCategoryAdminApi;

    public QualificationsCategoryDto commonQualificationConfigs(CorporationTypeEnum corporationType) {
        try {
            log.info("qualificationsCategoryAdminApi.getCommonQualificationConfigs(type:{})", corporationType);
            ApiRPCResult<QualificationsCategoryDto> category = qualificationsCategoryAdminApi.getCommonQualificationConfigs(corporationType);
            log.info("qualificationsCategoryAdminApi.getCommonQualificationConfigs(type:{}) return {}", corporationType, JSON.toJSONString(category));
            return category.isSuccess()?category.getData():null;
        }catch (Exception e){
            log.error("qualificationsCategoryAdminApi.getCommonQualificationConfigs(type:{}) 异常", corporationType, e);
            return null;
        }
    }

    public List<QualificationsCategoryDto> getBusinessQualificationConfigs(CorporationTypeEnum corporationType) {
        try {
            log.info("qualificationsCategoryAdminApi.getBusinessQualificationConfigs(type:{})", corporationType);
            ApiRPCResult<List<QualificationsCategoryDto>> categories = qualificationsCategoryAdminApi.getBusinessQualificationConfigs(corporationType);
            log.info("qualificationsCategoryAdminApi.getBusinessQualificationConfigs(type:{}) return {}", corporationType, JSON.toJSONString(categories));
            return categories.isSuccess()?categories.getData():new ArrayList<>(0);
        }catch (Exception e){
            log.error("qualificationsCategoryAdminApi.getBusinessQualificationConfigs(type:{}) 异常", corporationType, e);
            return new ArrayList<>(0);
        }
    }

    public void saveQualifications(List<QualificationsCategoryDto> dtos, byte corporationType, String username) throws ServiceException {
        ApiRPCResult result = null;
        try {
            log.info("qualificationsCategoryAdminApi.saveQualifications(dtos:{},username:{})", JSON.toJSONString(dtos),username);
            result = qualificationsCategoryAdminApi.saveQualifications(dtos,corporationType,username);
            log.info("qualificationsCategoryAdminApi.saveQualifications(dtos:{},username:{}) return {}", JSON.toJSONString(dtos),username, JSON.toJSONString(result));
        }catch (Exception e){
            log.error("qualificationsCategoryAdminApi.saveQualifications(dtos:{},username:{}) 异常", JSON.toJSONString(dtos),username, e);
            throw new ServiceException(result.getErrMsg());
        }
        if(result.isFail()){
            throw new ServiceException(result.getErrMsg());
        }
    }
}
