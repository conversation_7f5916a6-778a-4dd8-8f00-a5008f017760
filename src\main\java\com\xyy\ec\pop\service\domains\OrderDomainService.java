package com.xyy.ec.pop.service.domains;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.backend.framework.enums.OrderStatusEnum;
import com.xyy.ec.order.backend.model.OrderOperateLogDto;
import com.xyy.ec.order.backend.order.api.OrderApi;
import com.xyy.ec.order.backend.order.api.OrderOperateLogApi;
import com.xyy.ec.order.backend.order.api.OrderOperationLogApi;
import com.xyy.ec.order.backend.order.dto.OrderOperationLogDto;
import com.xyy.ec.order.backend.order.dto.OrderRemarksDto;
import com.xyy.ec.order.backend.order.enums.OperationEnum;
import com.xyy.ec.order.backend.order.query.dto.OrderDto;
import com.xyy.ec.order.backend.pop.dto.LogisticsRemindDto;
import com.xyy.ec.order.backend.query.Page;
import com.xyy.ec.order.backend.query.Sort;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderTransactionSnapshotBusinessApi;
import com.xyy.ec.order.business.config.TerminalTypeEnum;
import com.xyy.ec.order.business.dto.ConfirmReceiptDto;
import com.xyy.ec.order.business.dto.OrderBusinessDto;
import com.xyy.ec.order.business.dto.pop.PopOrderFinishDto;
import com.xyy.ec.order.search.api.remote.enums.PopOrderStatusEnum;
import com.xyy.ec.pop.adapter.OrderAdapter;
import com.xyy.ec.pop.adapter.dto.order.OrderDeliveryAdapterDto;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.OrderHelper;
import com.xyy.ec.pop.remote.EcOrderRemote;
import com.xyy.ec.pop.remote.PopOrderExceptionRpc;
import com.xyy.ec.pop.server.api.Enum.PopOrderStatusLogEnum;
import com.xyy.ec.pop.server.api.merchant.api.OrgUserRelationApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.PopOrderConsignmentApi;
import com.xyy.ec.pop.server.api.merchant.dto.OrgUserRelationDto;
import com.xyy.ec.pop.server.api.order.api.PopOrderApi;
import com.xyy.ec.pop.server.api.order.dto.*;
import com.xyy.ec.pop.server.api.order.enums.OrderExceptionConstants;
import com.xyy.ec.pop.server.api.order.enums.OrderExceptionTypeEnum;
import com.xyy.ec.pop.service.BatchUpdateService;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.*;
import com.xyy.ec.pop.vo.order.LogisticsRemindVo;
import com.xyy.ec.pop.vo.order.OrderAdminVo;
import com.xyy.ec.pop.vo.order.OrderDeliveryVo;
import com.xyy.ec.system.business.api.SysUserBusinessApi;
import com.xyy.ec.system.business.dto.SysUserBusinessDto;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/12/21 20:02
 * @table
 */
@Slf4j
@Service
public class OrderDomainService {

    @Autowired
    private OrderAdapter orderAdapter;

    @Reference(version = "1.0.0")
    private OrderTransactionSnapshotBusinessApi orderTransactionSnapshotBusinessApi;
    @Reference
    private PopOrderApi popOrderApi;
    @Value("${deliver.timeout.hours}")
    private Integer timeoutHours;
    @Autowired
    private EcOrderRemote ecOrderRemote;
    @Reference
    private SysUserBusinessApi sysUserBusinessApi;

    @Reference
    private OrgUserRelationApi orgUserRelationApi;
    @Autowired
    private BatchUpdateService batchUpdateService;

    @Reference
    private PopOrderConsignmentApi popOrderConsignmentApi;

    @Reference
    private OrderApi orderApi;
    @Reference
    private com.xyy.ec.order.backend.pop.api.PopOrderApi ecPopOrderApi;

    @Reference
    private OrderOperateLogApi orderOperateLogApi;

    @Reference
    private OrderOperationLogApi orderOperationLogApi;

    @Reference
    private com.xyy.ec.order.backend.pop.api.PopOrderApi ecOrderApi;


    public List<OrderDeliveryVo> getOrderDeliveryList(String orderNo) {
        log.info("OrderDomainService getOrderDeliveryList request orderNo:{}", orderNo);
        String logiCompany = null;
        ApiRPCResult<List<OrderTrackInfoDto>> apiRPCResult = popOrderApi.queryOrderTrackInfoList(orderNo);
        if (apiRPCResult.isSuccess() && !CollectionUtils.isEmpty(apiRPCResult.getData())) {
            logiCompany = apiRPCResult.getData().get(0).getLogiCompany();
        }
        List<OrderDeliveryAdapterDto> orderDeliveryMessageList = orderAdapter.getOrderDeliveryMessageList(orderNo);
        log.info("OrderDomainService getOrderDeliveryList response orderNo:{} orderDeliveryMessageList:{}", orderNo, JSON.toJSONString(orderDeliveryMessageList));
        if (CollectionUtils.isEmpty(orderDeliveryMessageList)) {
            return Lists.newArrayList();
        }
        return OrderHelper.convertOrderDeliveryVo(orderDeliveryMessageList, logiCompany);
    }

    public List<String> getOrderInvoice(String orderNo) throws ServiceException{
        log.info("#OrderDomainService.getOrderInvoice#orderNo:{}", orderNo);
        ApiRPCResult<String> apiRPCResult = popOrderApi.getOrderInvoice(orderNo);
        log.info("#OrderDomainService.getOrderInvoice#orderNo:{}", orderNo, JSON.toJSONString(apiRPCResult));
        if (apiRPCResult.isSuccess()) {
            String data = apiRPCResult.getData();
            if (StringUtils.isNotBlank(data)) {
                String[] split = data.split(",");
                List<String> collect = Stream.of(split).collect(Collectors.toList());
                return collect;
            }
            return Lists.newArrayList();
        }
        throw new ServiceException("获取发票异常");
    }

    /**
     * 获取平台优惠和店铺优惠
     * @param orderNo
     * @param orderTotalDiscount
     * @param orderDetailTotalDisMap
     * @return
     */
    public Map<Long, PromoDiscountDetailDto> getPromoDiscountDetail(String orderNo, BigDecimal orderTotalDiscount, Map<Long, BigDecimal> orderDetailTotalDisMap) {
        try {
            log.info("OrderDomainService.getPromoDiscountDetail# params orderNo：{}, orderTotalDiscount:{}, orderDetailTotalDisMap:{}",
                    orderNo, orderTotalDiscount, JSON.toJSONString(orderDetailTotalDisMap));
            if(StringUtils.isEmpty(orderNo) || Objects.isNull(orderTotalDiscount) || org.springframework.util.CollectionUtils.isEmpty(orderDetailTotalDisMap)){
                return Maps.newHashMap();
            }
            ApiRPCResult<Map<Long, PromoDiscountDetailDto>> apiRPCResult = popOrderApi.getPromoDiscountDetail(orderNo, orderTotalDiscount, orderDetailTotalDisMap, 1);
            log.info("OrderDomainService.getPromoDiscountDetail apiRPCResult:{}", JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()){
                return Maps.newHashMap();
            }
            return apiRPCResult.getData();
        }catch (Exception e){
            log.info("OrderDomainService.getPromoDiscountDetail# params orderNo：{}, orderTotalDiscount:{}, orderDetailTotalDisMap:{}",
                    orderNo, orderTotalDiscount, JSON.toJSONString(orderDetailTotalDisMap), e);
            return Maps.newHashMap();
        }
    }

    /**
     * 批量获取平台优惠和店铺优惠
     * @param orderNoList
     * @return
     */
    public Map<Long, PromoDiscountDetailDto> mgetOrderPromoDetail(List<String> orderNoList) {
        try {
            if(CollectionUtils.isEmpty(orderNoList)){
                return Maps.newHashMap();
            }
            Map<Long, PromoDiscountDetailDto> map = new HashMap<>();
            List<List<String>> lists = Lists.partition(orderNoList, 200);
            for (List<String> list : lists) {
                log.info("OrderDomainService.mgetOrderPromoDetail# params orderNoList：{}", JSON.toJSONString(orderNoList));
                ApiRPCResult<Map<Long, PromoDiscountDetailDto>> apiRPCResult = popOrderApi.mgetOrderPromoDetail(orderNoList);
                log.info("OrderDomainService.getPromoDiscountDetail#params orderNoList：{}, apiRPCResult:{}", JSON.toJSONString(orderNoList),
                        JSON.toJSONString(apiRPCResult));
                if (apiRPCResult != null && apiRPCResult.isSuccess() && ! CollectionUtils.isEmpty(apiRPCResult.getData())){
                    map.putAll(apiRPCResult.getData());
                }
            }
            return map;
        }catch (Exception e){
            log.error("OrderDomainService.getPromoDiscountDetail# params orderNoList：{}", JSON.toJSONString(orderNoList), e);
            return Maps.newHashMap();
        }
    }

    public Integer queryTimeoutOrderCount() {
        //待审核，待发货订单状态集合
        List<Integer> statusList = Lists.newArrayList(OrderStatusEnum.AUDITING.getValue(), OrderStatusEnum.OUTBOUND.getValue());
        log.info("OrderDomainService.queryTimeoutOrderCount#statusList:{},timeoutHours:{}", statusList, timeoutHours);
        Integer orderCount = ecOrderRemote.queryTimeoutOrderCount(statusList, timeoutHours);
        log.info("OrderDomainService.queryTimeoutOrderCount#statusList:{},timeout:{},orderCount:{}", statusList, timeoutHours, orderCount);
        return orderCount;
    }

    /**
     * 根据用户ID查询关联信息
     * @param merchantIds
     * @return
     */
    public List<OrgUserRelationDto> selectOrgUserRelation(List<Long> merchantIds) {
        ApiRPCResult<List<OrgUserRelationDto>> apiRPCResult = orgUserRelationApi.selectOrgUserRelationByMerchant(Sets.newHashSet(merchantIds));
        if (!apiRPCResult.isSuccess()){
            return Lists.newArrayList();
        }
        return apiRPCResult.getData();
    }

    public Object getSalesMessage(Long saleId) {
        List<SysUserBusinessDto> businessDtos = sysUserBusinessApi.findByUserId(Lists.newArrayList(saleId));
        log.info("OrderDomainService.saleId#merchantId:{},getSalesMessage:{}", saleId, JSON.toJSONString(businessDtos));
        if (!CollectionUtils.isEmpty(businessDtos)){
            SysUserBusinessDto sysUserBusinessDto = businessDtos.get(0);
            StringBuffer sb = new StringBuffer();
            sb.append("姓名：");
            sb.append(sysUserBusinessDto.getRealName());
            sb.append("<br>");
            sb.append("   电话：");
            sb.append(sysUserBusinessDto.getPhone());
            String string = sb.toString();
            return string;
        }
        return null;
    }

    public List<PopOrderDto> queryPopOrdersByOrderNoList(List<String> orderNos){
        ApiRPCResult<List<PopOrderDto>> apiRPCResult = popOrderApi.queryByOrderNoList(orderNos);
        log.info("OrderDomainService.queryByOrderNoList#orderNos:{},res:{}", JSONObject.toJSONString(orderNos), JSON.toJSONString(apiRPCResult));
        if (!apiRPCResult.isSuccess()){
            return Lists.newArrayList();
        }
        return apiRPCResult.getData();
    }

    /**
     * 订单批量完成
     * @param user
     * @param vos
     * @return
     */
    public BatchUpdateResultVo batchOverOrderFromExcel(String user, List<BatchOverOrderVo> vos,int batchOverStepSize) {
        try{
            for(List<BatchOverOrderVo> steps:Lists.partition(vos,batchOverStepSize)){
                batchOverOrder(user,steps);
            }
        }catch (Exception e){
            log.error("批量完成订单导入失败，user:{}",user,e);
            //处理for循环部分成功的情况
            for(BatchOverOrderVo vo:vos){
                if(!vo.isOk()&&vo.getErrorMsg()==null){
                    vo.setErrorMsg("导入失败");
                }
            }
        }finally {
            List<BatchOverOrderVo> errors = vos.stream().filter(item -> !item.isOk()).collect(Collectors.toList());
            BatchUpdateResultVo result = batchUpdateService.writeResult(vos.size(),errors,"导入失败订单");
            return result;
        }
    }

    private void batchOverOrder(String user, List<BatchOverOrderVo> vos) throws ServiceException {
        List<String> orderNos = vos.stream().map(item -> item.getOrderNo()).collect(Collectors.toList());
        //查询订单状态
        Map<String, Integer> orderMap = ecOrderRemote.queryUnableFinishOrder(orderNos);
        vos.forEach(item->{
            Integer errorStatus = orderMap.get(item.getOrderNo());
            if(errorStatus==null){
                return;
            }
            if(errorStatus==1){
                item.setErrorMsg("订单号不存在");
            }else if(errorStatus==2){
                item.setErrorMsg("订单状态不是“配送中”");
            }else if(errorStatus==3){
                item.setErrorMsg("订单存在待处理的退款单");
            }
        });

        List<BatchOverOrderVo> toUpdate = vos.stream().filter(item -> item.getErrorMsg() == null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(toUpdate)){
            return;
        }
        //更新订单状态
        Set<PopOrderFinishDto> ecOrder = toUpdate.stream().map(item -> {
            PopOrderFinishDto popOrderFinishDto = new PopOrderFinishDto();
            popOrderFinishDto.setOrderNo(item.getOrderNo());
            popOrderFinishDto.setUpdator(user);
            popOrderFinishDto.setTerminalType(5);
            popOrderFinishDto.setRemark(PopOrderStatusLogEnum.SHIPPED_ADMIN.getLog());
            return popOrderFinishDto;
        }).collect(Collectors.toSet());
        //更新ec状态
        List<String> ecResult = ecOrderRemote.confirmOrderFinish(ecOrder);
        toUpdate.stream().filter(item->ecResult.contains(item)).forEach(item->item.setErrorMsg("更新ec状态失败"));
        List<String> orders = toUpdate.stream().filter(item -> item.getErrorMsg() == null).map(item->item.getOrderNo()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orders)){
            return;
        }
        //更新pop状态为已完成
        ApiRPCResult<Boolean> updateResult = popOrderApi.updateOrderFinish(orders);
        if(updateResult.isFail()){
            throw new ServiceException("更新pop状态失败");
        }
        toUpdate.forEach(item->item.setOk(true));
    }

    public LogisticsRemindVo queryLogisticsTrackCount() {
        LogisticsRemindDto dto = ecOrderRemote.queryLogisticsTrackCount();
        if(dto==null){
            return null;
        }
        LogisticsRemindVo vo = new LogisticsRemindVo();
        vo.setLogisticsTrackFailCount(dto.getLogisticsTrackFailCount());
        vo.setLogisticsTrackIllegalCount(dto.getLogisticsTrackIllegalCount());
        return vo;
    }

    public PageInfo<PopOrderConsignmentDetailDto> getPopOrderConsignmentPage(String orderNo, Integer pageNum, Integer pageSize){
        ApiRPCResult<PageInfo<PopOrderConsignmentDetailDto>> pageInfoApiRPCResult = popOrderConsignmentApi.selectPageList(orderNo, pageNum, pageSize);
        if (pageInfoApiRPCResult.isSuccess()){
            return pageInfoApiRPCResult.getData();
        }
        return null;
    }

    //region 订单查询
    public OrderDto getOrderByOrderNo(String orderNo) {
        log.info("#OrderDomainService.getOrderByOrderNo#info,参数:orderNo:{}", orderNo);
        try {
            ApiRPCResult<OrderDto> apiRPCResult = orderApi.getByOrderNo(orderNo);
            if (apiRPCResult.isFail()) {
                log.warn("#OrderDomainService.getOrderByOrderNo#warn,参数:orderNo:{}", orderNo);
                return null;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("#OrderDomainService.getOrderByOrderNo#error,参数:orderNo:{}", orderNo, e);
            return null;
        }
    }
    /**
     * 订单收款 只修改ec订单数据
     * @param orderNo
     * @param orderStatus
     * @param operator
     * @param currentOrgId
     * @return
     * @throws ServiceException
     */
    public boolean orderColl(String orderNo, Integer orderStatus, String operator, String currentOrgId)throws ServiceException {
        ApiRPCResult<OrderDto> apiRPCResult = orderApi.getByOrderNo(orderNo);

        if (apiRPCResult.isFail() || apiRPCResult.getData() == null) {
            throw new XyyEcPopException("未获取到订单信息");
        }
        OrderDto orderDto = apiRPCResult.getData();
        ConfirmReceiptDto confirmReceiptDto = new ConfirmReceiptDto();
        confirmReceiptDto.setOrderNo(orderNo);
        confirmReceiptDto.setOperator(operator);
        confirmReceiptDto.setTerminalType(TerminalTypeEnum.POP_ADMIN.getKey());

        boolean b = ecOrderRemote.confirmReceipt(confirmReceiptDto);
        if (!b) {
            throw new XyyEcPopException("修改订单信息失败");
        }
        return Boolean.TRUE;
    }

    public void saveOperateLog(String orderNo,String updateUserName,Integer status,String remark){
        try {
            log.info("OrderDomainService.saveOperateLog param orderNo:{}",orderNo);
            OrderDto orderDto = getOrderByOrderNo(orderNo);
            if (Objects.nonNull(orderDto)) {
                OrderOperateLogDto orderOperateLogDto = new OrderOperateLogDto();
                orderOperateLogDto.setOrderNo(orderNo);
                orderOperateLogDto.setMerchantId(orderDto.getMerchantId());
                orderOperateLogDto.setVarietyNum(orderDto.getVarietyNum());
                orderOperateLogDto.setMoney(orderDto.getMoney());
                orderOperateLogDto.setStatus(status);
                orderOperateLogDto.setCheckStatus(orderDto.getCheckStatus());
                orderOperateLogDto.setUpdator(updateUserName);
                orderOperateLogDto.setUpdateTime(new Date());
                orderOperateLogDto.setShipTime(orderDto.getShipTime());
                orderOperateLogDto.setFinishTime(orderDto.getFinishTime());
                orderOperateLogDto.setTotalAmount(orderDto.getTotalAmount());
                orderOperateLogDto.setDiscount(orderDto.getDiscount());
                orderOperateLogDto.setVisibled(orderDto.getVisibled());
                orderOperateLogDto.setRemark(remark);
                orderOperateLogApi.saveOperateLog(orderOperateLogDto);
            }
        }catch (Exception e){
            log.error("OrderEcAdapter.saveOperateLog error orderNo:{}",orderNo,e);
        }
    }

    /**
     * 订单日志查询
     *
     * @param orderNo 订单编号
     * @return
     */
    public List<OrderStatusRecordsVo> queryOperationLogByOrderNo(String orderNo) {
        if (org.springframework.util.StringUtils.isEmpty(orderNo)) {
            return null;
        }
        log.info("#EcOrderApiRemote.queryOperationLogByOrderNo#info,参数:orderNo:{}", orderNo);
        try {
           com.xyy.ec.order.backend.pop.dto.PopOrderDto popOrderDto = new com.xyy.ec.order.backend.pop.dto.PopOrderDto();
            popOrderDto.setOrderNo(orderNo);
            popOrderDto.setIsThirdCompany(-1);
            Sort sort = new Sort();
            sort.setDirection(Sort.DESC);
            ApiRPCResult<Page<com.xyy.ec.order.backend.pop.dto.PopOrderDto>> apiRPCResult = ecOrderApi.queryPopOrderList(popOrderDto, 10, 1, sort);
            if (apiRPCResult == null || apiRPCResult.isFail() || apiRPCResult.getData() == null || org.apache.commons.collections.CollectionUtils.isEmpty(apiRPCResult.getData().getRows())) {
                throw null ;
            }
            ApiRPCResult<List<OrderOperateLogDto>> apiRPCResult1 = orderOperateLogApi.queryByOrderNoForPopSeller(orderNo);
            log.info("#orderOperateLogApi.queryByOrderNoForPopSeller#orderNo:{},dtoPageInfo:{}", orderNo, JSON.toJSONString(apiRPCResult1));
            if (apiRPCResult1 == null || org.apache.commons.collections.CollectionUtils.isEmpty(apiRPCResult1.getData())) {
                return new ArrayList<>();
            }
            List<OrderStatusRecordsVo> recordsVos = OrderHelper.convertRpcRestToOrderStatusRecordsVo(apiRPCResult1.getData(), apiRPCResult.getData().getRows());

            List<OrderRemarksDto> orderRemarksDtos = queryOrderRemarks(orderNo);
            recordsVos.addAll(OrderHelper.convertOrderRemarkToOrderStatusRecord(orderRemarksDtos,apiRPCResult.getData().getRows()));
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(recordsVos)){
                recordsVos.sort(Comparator.comparing(OrderStatusRecordsVo::getOperationTime).reversed());
            }
            recordsVos.forEach(orderStatusRecordsVo -> {
                if (orderStatusRecordsVo.getOrderStatus() != null) {
                    orderStatusRecordsVo.setOrderStatusName(PopOrderStatusEnum.getNameByCode(orderStatusRecordsVo.getOrderStatus()));
                }
            });
            handleOrderExceptions(recordsVos);
            return recordsVos;
        } catch (Exception e) {
            log.error("#EcOrderApiRemote.queryOperationLogByOrderNo#error,参数:orderNo:{}", orderNo, e);
            return null;
        }

    }


    private void handleOrderExceptions(List<OrderStatusRecordsVo> orderStatusRecordsVos) {
        List<OrderStatusRecordsVo> orderExceptionLogs = orderStatusRecordsVos.stream().filter(item -> StringUtils.isNotBlank(item.getOperationContent()) &&
                item.getOperationContent().contains(OrderExceptionConstants.OPERATE_LOG_ORDER_EXCEPTION_OPERATOR)).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(orderExceptionLogs)){
            return;
        }
        List<PopOrderExceptionDto> exceptionDtoList = queryLogExceptions(orderExceptionLogs);
        orderStatusRecordsVos.forEach(item->{
            String operationContent = item.getOperationContent();
            if (StringUtils.isBlank(operationContent) || !operationContent.contains(OrderExceptionConstants.OPERATE_LOG_ORDER_EXCEPTION_OPERATOR)){
                return;
            }
            JSONObject object = JSONObject.parseObject(operationContent);
            String log = "";
            if (operationContent.contains(OrderExceptionConstants.OPERATE_LOG_ORDER_EXCEPTION_LICENSE)){
                log += "关联资质状态："+object.getString(OrderExceptionConstants.OPERATE_LOG_ORDER_EXCEPTION_LICENSE)+"</br>";
            }
            if (operationContent.contains(OrderExceptionConstants.OPERATE_LOG_ORDER_EXCEPTION_OPERATOR)){
                log += object.getString(OrderExceptionConstants.OPERATE_LOG_ORDER_EXCEPTION_OPERATOR);
            }
            if (operationContent.contains(OrderExceptionConstants.OPERATE_LOG_ORDER_EXCEPTION_KEY)){
                item.setExceptionFlag(true);
                item.setExceptionMsgJson(JSON.toJSONString(filterExceptions(exceptionDtoList,object.getJSONArray(OrderExceptionConstants.OPERATE_LOG_ORDER_EXCEPTION_KEY))));
            }
            item.setOperationContent(log);
        });

    }

    private List<OrderStatusRecordsVo.OrderExceptionLog> filterExceptions(List<PopOrderExceptionDto> exceptionDtoList, JSONArray jsonArray) {
        List<Long> javaList = jsonArray.toJavaList(Long.class);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(javaList)){
            return Lists.newArrayList();
        }
        List<PopOrderExceptionDto> collect = exceptionDtoList.stream().filter(item -> javaList.contains(item.getId())).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(collect)){
            return Lists.newArrayList();
        }
        List<OrderStatusRecordsVo.OrderExceptionLog> logs = Lists.newArrayList();
        Map<Integer, List<PopOrderExceptionDto>> exceptionMap = collect.stream().collect(Collectors.groupingBy(PopOrderExceptionDto::getExceptionType));
        for (Map.Entry<Integer, List<PopOrderExceptionDto>> integerListEntry : exceptionMap.entrySet()) {
            OrderStatusRecordsVo.OrderExceptionLog log = new OrderStatusRecordsVo.OrderExceptionLog();
            log.setExceptionTxt(OrderExceptionTypeEnum.getTxt(integerListEntry.getKey()));
            log.setExceptionDetailList(integerListEntry.getValue().stream().map(PopOrderExceptionDto::getExceptionDetail).collect(Collectors.toList()));
            Optional<PopOrderExceptionDto> max = integerListEntry.getValue().stream().max((t1, t2) -> Math.toIntExact(t1.getLatestDate().getTime() - t2.getLatestDate().getTime()));
            Date date = new Date();
            if (max.isPresent()){
                date = max.get().getLatestDate();
            }
            log.setOperateTime(DateUtil.date2Str(date,DateUtil.PATTERN_STANDARD));
            logs.add(log);
        }
        return logs;
    }

    private List<PopOrderExceptionDto> queryLogExceptions(List<OrderStatusRecordsVo> orderStatusRecordsVos) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(orderStatusRecordsVos)){
            return Lists.newArrayList();
        }
        List<OrderStatusRecordsVo> orderExceptionLogs = orderStatusRecordsVos.stream().filter(item -> StringUtils.isNotBlank(item.getOperationContent()) &&
                item.getOperationContent().contains(OrderExceptionConstants.OPERATE_LOG_ORDER_EXCEPTION_KEY)).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(orderExceptionLogs)){
            return Lists.newArrayList();
        }
        List<List<Long>> collect = orderExceptionLogs.stream().map(item -> {
            JSONObject jsonObject = JSONObject.parseObject(item.getOperationContent());
            JSONArray jsonArray = jsonObject.getJSONArray(OrderExceptionConstants.OPERATE_LOG_ORDER_EXCEPTION_KEY);
            return jsonArray.toJavaList(Long.class);
        }).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(collect)){
            return Lists.newArrayList();
        }
        List<Long> exceptionIds = collect.stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        return popOrderExceptionRpc.queryExceptionByExceptionIds(exceptionIds);
    }


    public List<OrderRemarksDto> queryOrderRemarks(String orderNo){
        try {
            ApiRPCResult<List<OrderRemarksDto>> listApiRPCResult = orderApi.queryOrderRemarks(orderNo);
            if (listApiRPCResult != null && listApiRPCResult.isSuccess()){
                return listApiRPCResult.getData();
            }

        } catch (Exception e) {
            log.error("EcOrderRemote.saveOrderRemarks#orderNo:{} 异常", orderNo, e);
        }
        return Lists.newArrayList();
    }

    private OrderOperationLogVo convertOrderOperationLogDtoToVo(OrderOperationLogDto dto) {
        OrderOperationLogVo vo = new OrderOperationLogVo();
        vo.setId(dto.getId());
        vo.setMerchantId(dto.getMerchantId());
        vo.setOrderNo(dto.getOrderNo());
        vo.setOperation(dto.getOperation());
        vo.setOperationName(OperationEnum.get(dto.getOperation()));
        vo.setMethod(dto.getMethod());
        vo.setParams(dto.getParams());
        vo.setOperator(dto.getOperator());
        vo.setOperateTime(dto.getOperateTime());

        return vo;
    }

    /**
     * 订单设置客服备注
     *
     * @param orderId       订单ID
     * @param urgencyStatus 紧急状态
     * @param remark        备注
     * @param optionUser    操作人
     * @return
     */
    public boolean updateOrderUrgencyInfo(Long orderId, int urgencyStatus, String remark, String optionUser) {
        log.info("#EcOrderApiRemote.updateOrderUrgencyInfo#info,参数:orderId:{},urgencyStatus:{},remark:{},optionUser:{}", orderId, urgencyStatus, remark, optionUser);
        try {
            ApiRPCResult apiRPCResult = orderApi.updateOrderUrgencyInfo(orderId, optionUser, urgencyStatus, remark);
            return apiRPCResult.isSuccess();

        } catch (Exception e) {
            log.error("#EcOrderApiRemote.updateOrderUrgencyInfo#error,参数:orderId:{},urgencyStatus:{},remark:{},optionUser:{}", orderId, urgencyStatus, remark, optionUser, e);
            return false;
        }

    }

    @Autowired
    private PopOrderExceptionRpc popOrderExceptionRpc;
    public List<OrderExceptionVo> queryOrderException( String orderNo) {
        List<PopOrderExceptionDto> exceptionDtoList = popOrderExceptionRpc.queryExceptionByOrderNo(orderNo);
        if (CollectionUtils.isEmpty(exceptionDtoList)){
            throw new PopAdminException("订单没有异常信息");
        }
        Map<Integer, List<PopOrderExceptionDto>> dtoMap = exceptionDtoList.stream().collect(Collectors.groupingBy(PopOrderExceptionDto::getExceptionType));
        List<OrderExceptionVo> voList = Lists.newArrayList();
        for (Map.Entry<Integer, List<PopOrderExceptionDto>> integerListEntry : dtoMap.entrySet()) {
            OrderExceptionVo vo = new OrderExceptionVo();
            String txt = OrderExceptionTypeEnum.getTxt(integerListEntry.getKey());
            String detail = integerListEntry.getValue().stream().map(PopOrderExceptionDto::getExceptionDetail).collect(Collectors.joining("\n"));
            Optional<PopOrderExceptionDto> max = integerListEntry.getValue().stream().max((t1, t2) -> Math.toIntExact(t1.getLatestDate().getTime() - t2.getLatestDate().getTime()));
            Date date = new Date();
            if (max.isPresent()){
                date = max.get().getLatestDate();
            }
            vo.setExceptionTypeTxt(txt);
            vo.setExceptionDetail(detail);
            vo.setOperateTime(DateUtil.date2Str(date,DateUtil.PATTERN_STANDARD));
            voList.add(vo);
        }
        return voList;
    }
}
