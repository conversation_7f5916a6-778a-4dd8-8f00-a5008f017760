package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.server.api.external.ec.EcPopCorporationApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCorporationDto;
import com.xyy.ec.pop.utils.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 商户信息
 * <AUTHOR>
 */
@Component
@Slf4j
public class PopCorporationRemoteAdapter {
    @Reference
    private CorporationAdminApi corporationAdminApi;
    @Reference(version = "1.0.0")
    private EcPopCorporationApi ecPopCorporationApi;
    public CorporationDto queryByOrgId(String orgId) {
        try {
            log.info("corporationAdminApi.queryCorporation(orgId:{})",orgId);
            ApiRPCResult<CorporationDto> result = corporationAdminApi.queryCorporation(orgId);
            log.info("corporationAdminApi.queryCorporation(orgId:{}) return {}",orgId, JSON.toJSONString(result));
            return result.getData();
        } catch (Exception e) {
            log.error("corporationAdminApi.queryCorporation(orgId:{}) 异常",orgId, e);
            return null;
        }
    }

    public CorporationDto queryById(Long cId) {
        try {
            log.info("corporationAdminApi.queryCorporation(cId:{})",cId);
            ApiRPCResult<CorporationDto> result = corporationAdminApi.queryCorporation(cId);
            log.info("corporationAdminApi.queryCorporation(cId:{}) return {}",cId, JSON.toJSONString(result));
            return result.getData();
        } catch (Exception e) {
            log.error("corporationAdminApi.queryCorporation(cId:{}) 异常",cId, e);
            return null;
        }
    }
    /**
     * 根据店铺名称模糊查询orgId
     * @param name 店铺名称
     * @param companyName 机构名称
     * @return
     */
    public List<String> getOrgIdByName(String name, String companyName) {
        try {
            log.info("corporationAdminApi.getOrgIdByName(name:{},companyName:{})",name,companyName);
            ApiRPCResult<List<String>> apiRPCResult = ecPopCorporationApi.getOrgIdByName(name,companyName);
            log.info("corporationAdminApi.getOrgIdByName(name:{},companyName:{}) return {}",name,companyName, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult != null && apiRPCResult.getData() != null) {
                return apiRPCResult.getData();
            }
        } catch (Exception e) {
            log.error("corporationAdminApi.getOrgIdByName(name:{},companyName:{})",name,companyName,e);
        }
        return Lists.newArrayList();
    }

    public Map<String, PopCorporationDto> queryPopCorporationDtoMap(List<String> orgIds){
        try {
            log.info("corporationAdminApi.queryPopCorporationDtoMap(orgIds:{})",JSON.toJSONString(orgIds));
            ApiRPCResult<List<PopCorporationDto>> apiRPCResult = ecPopCorporationApi.listOrgInfoByIds(orgIds);
            log.info("corporationAdminApi.queryPopCorporationDtoMap(orgIds:{}) return {}",JSON.toJSONString(orgIds), JSON.toJSONString(apiRPCResult));
            if (apiRPCResult != null && CollectionUtil.isNotEmpty(apiRPCResult.getData()) ) {
                return apiRPCResult.getData()
                        .stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(PopCorporationDto::getOrgId, Function.identity(), (o1, o2) -> o1));
            }
        } catch (Exception e) {
            log.error("corporationAdminApi.queryPopCorporationDtoMap(orgIds:{})",JSON.toJSONString(orgIds),e);
        }
        return Maps.newHashMap();
    }

    public int updateBusinessAttribute(String orgId, Byte businessAttribute, Long updateId, String updateName) {
        try {
            log.info("PopCorporationRemoteAdapter.updateBusinessAttribute#orgId:{},businessAttribute:{},updateId:{},updateName:{}", orgId, businessAttribute, updateId, updateName);
            ApiRPCResult<Integer> apiRPCResult = corporationAdminApi.updateBusinessAttribute(orgId, businessAttribute, updateId, updateName);
            log.info("PopCorporationRemoteAdapter.updateBusinessAttribute#orgId:{},businessAttribute:{},updateId:{},updateName:{},apiRPCResult:{}", orgId, businessAttribute, updateId, updateName, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult.isFail()) {
                throw new PopAdminException(apiRPCResult.getErrMsg());
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("PopCorporationRemoteAdapter.updateBusinessAttribute#error. orgId:{},businessAttribute:{},updateId:{},updateName:{}", orgId, businessAttribute, updateId, updateName, e);
            throw new PopAdminException("更新店铺经营属性失败");
        }
    }

    public void updateShopCategory(String orgId, Integer shopCategory, Long id, String user) {
        try {
            corporationAdminApi.updateShopCategory(orgId, shopCategory, id, user);
        }catch (Exception e){
            log.error("PopCorporationRemoteAdapter.updateShopCategory#error. orgId:{},shopCategory:{},id:{},user:{}", orgId, shopCategory, id, user, e);
        }
    }
}
