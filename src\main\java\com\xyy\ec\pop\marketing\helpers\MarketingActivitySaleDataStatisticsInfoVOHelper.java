package com.xyy.ec.pop.marketing.helpers;

import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataStatisticsInfoVO;
import com.xyy.ms.promotion.business.result.MarketingActivitySaleDataStatisticsInfoDTO;

import java.util.Objects;

public class MarketingActivitySaleDataStatisticsInfoVOHelper {

    public static MarketingActivitySaleDataStatisticsInfoVO create(MarketingActivitySaleDataStatisticsInfoDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        return MarketingActivitySaleDataStatisticsInfoVO.builder()
                .purchaseMerchantNum(dto.getPurchaseMerchantNum())
                .purchaseOrderNum(dto.getPurchaseOrderNum())
                .purchaseProductNum(dto.getPurchaseProductNum())
                .purchaseAmount(dto.getPurchaseAmount())
                .unpaidPurchaseMerchantNum(dto.getUnpaidPurchaseMerchantNum())
                .unpaidPurchaseOrderNum(dto.getUnpaidPurchaseOrderNum())
                .unpaidPurchaseProductNum(dto.getUnpaidPurchaseProductNum())
                .unpaidPurchaseAmount(dto.getUnpaidPurchaseAmount())
                .paidPurchaseMerchantNum(dto.getPaidPurchaseMerchantNum())
                .paidPurchaseOrderNum(dto.getPaidPurchaseOrderNum())
                .paidPurchaseProductNum(dto.getPaidPurchaseProductNum())
                .paidPurchaseAmount(dto.getPaidPurchaseAmount())
                .build();
    }

}
