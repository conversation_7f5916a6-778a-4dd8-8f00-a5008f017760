package com.xyy.ec.pop.helper;

import com.google.common.collect.Lists;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.ec.pop.dto.RegionDto;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class RegionHelper {
    private RegionHelper() {
    }

    public static List<RegionDto> buildRegionDtos(List<XyyRegionBusinessDto> xyyRegionBusinessDtos) {
        if (CollectionUtils.isEmpty(xyyRegionBusinessDtos)) {
            return Lists.newArrayList();
        }

        List<RegionDto> regionDtos = xyyRegionBusinessDtos.stream().map(xyyRegionBusinessDto -> buildRegionDto(xyyRegionBusinessDto)).filter(regionDto -> Objects.nonNull(regionDto)).collect(Collectors.toList());

        return regionDtos;
    }

    public static RegionDto buildRegionDto(XyyRegionBusinessDto xyyRegionBusinessDto) {
        if (xyyRegionBusinessDto == null) {
            return null;
        }

        RegionDto regionDto = new RegionDto();
        regionDto.setAreaCode(xyyRegionBusinessDto.getAreaCode());
        regionDto.setAreaName(xyyRegionBusinessDto.getAreaName());
        return regionDto;
    }

}
