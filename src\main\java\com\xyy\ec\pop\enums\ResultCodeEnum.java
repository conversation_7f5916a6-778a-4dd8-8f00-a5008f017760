package com.xyy.ec.pop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 返回结果状态枚举类
 */
@Getter
@AllArgsConstructor
public enum ResultCodeEnum {
    SUCCESS(1, "success"),
    FAIL(0, "fail"),
    ERROR(-1, "error"),
    USER_NOT_EXISTS(400, "用户不存在"),
    PASSWORD_OR_NAME_ERROR(401, "用户名或者密码错误"),
    USER_FROZEN(402, "用户已冻结,请联系客服"),
    TYPE_OF_KA_ONE(403,"KA客户请联系您的服务经理：${0}"),
    TYPE_OF_KA_TWO(404,"KA客户请拨打400-0505-111"),
    STATUS_NON_ACTIVATED(405,"账号未激活"),
    SHOP_CLOSED(406, "店铺关闭，不可重新上线"),
    SHOP_QUERY_ERROR(407, "店铺状态变更失败，请稍后重试"),
    SHOP_STATUS_CHANGE_ERROR(408, "店铺已%s，状态变更失败"),
    USER_HAS_NO_PERMISSION(409, "用户无操作权限");

    private int code;
    private String msg;
}
