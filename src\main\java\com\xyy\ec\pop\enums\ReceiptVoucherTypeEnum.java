package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/10/7 10:11
 */
public enum ReceiptVoucherTypeEnum {

    RECEIPTVOUCHER(1,"收款凭证");


    private  int id;
    private  String value;

    ReceiptVoucherTypeEnum(int id, String value){
        this.id = id;
        this.value = value;
    }
    private static Map<Integer, ReceiptVoucherTypeEnum> receiptVoucherTypeMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(ReceiptVoucherTypeEnum control : ReceiptVoucherTypeEnum.values()) {
            receiptVoucherTypeMaps.put(control.getId(), control);
            maps.put(control.getId(),control.getValue());
        }
    }
    public static String get(int id) {
        return receiptVoucherTypeMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
