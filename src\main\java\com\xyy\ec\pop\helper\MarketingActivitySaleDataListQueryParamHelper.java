package com.xyy.ec.pop.helper;

import com.xyy.ec.marketing.common.constants.MarketingActivitySaleDataLogicalOrderStatusEnum;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.order.search.api.remote.enums.PopOrderStatusEnum;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ms.promotion.business.params.MarketingActivitySaleDataListQueryParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Objects;

public class MarketingActivitySaleDataListQueryParamHelper {

    /**
     * 校验
     *
     * @param queryParam
     * @return
     */
    public static Boolean validate(MarketingActivitySaleDataListQueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (Objects.isNull(queryParam.getActivityType()) || Objects.isNull(MarketingEnum.getEnum(queryParam.getActivityType()))) {
            String msg = "请填写活动类型";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (Objects.isNull(queryParam.getMarketingId())) {
            String msg = "请填写活动ID";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (StringUtils.isEmpty(queryParam.getShopCode())) {
            String msg = "请填写店铺编码";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (Objects.nonNull(queryParam.getOrderStatus()) && !Objects.equals(queryParam.getOrderStatus(), -1)) {
            PopOrderStatusEnum popOrderStatusEnum = null;
            for (PopOrderStatusEnum tempPopOrderStatusEnum : PopOrderStatusEnum.values()) {
                if (Objects.equals(tempPopOrderStatusEnum.getCode(), queryParam.getOrderStatus())) {
                    popOrderStatusEnum = tempPopOrderStatusEnum;
                    break;
                }
            }
            if (Objects.isNull(popOrderStatusEnum)) {
                String msg = "请选择订单状态";
                throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }
        }
        if (Objects.nonNull(queryParam.getLogicalOrderStatus()) && Objects.isNull(MarketingActivitySaleDataLogicalOrderStatusEnum.valueOfCustom(queryParam.getLogicalOrderStatus()))) {
            String msg = "请选择订单逻辑状态";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (Objects.isNull(queryParam.getStartOrderCreateTime()) || Objects.isNull(queryParam.getEndOrderCreateTime())) {
            String msg = "请选择下单时间";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (!queryParam.getStartOrderCreateTime().before(queryParam.getEndOrderCreateTime())) {
            String msg = "下单开始时间须早于结束时间";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (DateUtils.addMonths(queryParam.getStartOrderCreateTime(), 3).before(queryParam.getEndOrderCreateTime())) {
            String msg = "下单时间间隔最大3个月";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (Objects.isNull(queryParam.getPageNum()) || queryParam.getPageNum() <= 0) {
            String msg = "请选择页码";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (Objects.isNull(queryParam.getPageSize()) || queryParam.getPageSize() <= 0) {
            String msg = "请选择每页条数";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        return true;
    }
    
}
