package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuOperationLogApi;
import com.xyy.ec.pop.server.api.product.dto.PopSkuOperationLogDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/12/28
 */
@Service
@Slf4j
public class PopSkuOperationLogRemoteAdapter {
    @Reference
    private PopSkuOperationLogApi popSkuOperationLogApi;
    public List<PopSkuOperationLogDto> getByBarcode(String barcode){
        try {
            log.info("PopSkuOperationLogRemoteAdapter.getByBarcode#barcode:{}", JSON.toJSONString(barcode));
            ApiRPCResult<List<PopSkuOperationLogDto>> result = popSkuOperationLogApi.listByBarCode(barcode);
            log.info("PopSkuOperationLogRemoteAdapter.getByBarcode#barcode:{} return {}", JSON.toJSONString(barcode), JSON.toJSONString(result));
            return result.isSuccess()?result.getData():new ArrayList<>(0);
        } catch (Exception e) {
            log.error("PopSkuOperationLogRemoteAdapter.getByBarcode#barcode:{} 异常", JSON.toJSONString(barcode), e);
            return new ArrayList<>(0);
        }
    }
}
