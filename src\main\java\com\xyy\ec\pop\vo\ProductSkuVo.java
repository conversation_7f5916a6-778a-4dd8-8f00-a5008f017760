package com.xyy.ec.pop.vo;

import com.xyy.ec.pop.server.api.product.dto.PopSkuPurchaseLimitDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ProductSkuVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 标准库id
     */
    private String standardProductId;

    /**
     * 商家名称
     */
    private String companyName;
    /**
     * 机构id
     */
    private String orgId;

    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 产地
     */
    private String producer;

    /**
     * 禁忌
     */
    private String abstain;
    /**
     * 批准文号
     */
    private String approvalNumber;
    /**
     * 库存
     */
   private Integer availableQty;
    /**
     * 条形码
     */
    private String barcode;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 备注
     */
    private String bz;
    /**
     * 关联分类id
     */
    private Long skuRelationCategory;
    /**
     * 关联分类
     */
    private String skuRelationCategoryName;
    /**
     * 商品编码
     */
    private String code;
    /**
     * 通用名称
     */
    private String commonName;
    /**
     * 成分
     */
    private String component;
    /**
     * 注意事项
     */
    private String considerations;
    /**
     * 剂型
     */
    private String dosageForm;
    /**
     * 药品分类
     */
    private Integer drugClassification;
    /**
     * 药帮忙价
     */
    private Double fob;
    /**
     * 连锁售价
     */
    private BigDecimal chainPrice;
    /**
     * 商品底价
     */
    private BigDecimal basePrice;
    /**
     * 单体毛利率'
     */
    private BigDecimal grossProfitMargin;
    /**
     * 连锁毛利率
     */
    private BigDecimal chainGrossProfitMargin;
    /**
     * 毛利率
     */
    private String grossMargin;
    /**
     * 商品原图
     */
    private String imageUrl;
    /**
     * 适应症/功能主治
     */
    private String indication;
    /**
     * 药物相互作用
     */
    private String interaction;
    /**
     * 是否易碎品
     */
    private Integer isFragileGoods;
    /**
     * 是否新品
     */
    private Integer isNew;
    /**
     * 是否可拆零
     */
    private Integer isSplit;
    /**
     * 中包装
     */
    private Integer mediumPackageNum;
    /**
     * 件装量
     */
    private String pieceLoading;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品单位
     */
    private String productUnit;

    /**
     * 保质期
     */
    private String shelfLife;
    /**
     * 展示名称
     */
    private String showName;
    /**
     * 说明书图片列表
     */
    private List<String> skuInstructionImageList;

    /**
     * 商品图片列表
     */
    private List<String> imagesList;
    /**
     * 规格
     */
    private String spec;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 存储条件
     */
    private String storageCondition;
    /**
     * 建议零售价
     */
    private BigDecimal suggestPrice;
    /**
     * 有效期
     */
    private String term;
    /**
     * 不良反应
     */
    private String untowardEffect;
    /**
     * 用法与用量
     */
    private String usageAndDosage;
    /**
     * 助记码
     */
    private String zjm;
    /**
     * 商品经营信息分类
     */
    private String skuCategory;
    /**
     * 商品经营信息分类:id
     */
    private Long skuCategoryId;
    /**
     * 副标题
     */
    private String subtitle;
    /**
     * erp编码
     */
    private String erpCode;

    /**
     * 最老生产日期
     */
    private String oldestProDate;
    /**
     * 最新生产日期
     */
    private String newProDate;
    /**
     * 近效期
     */
    private String nearEffect;
    /**
     * 远效期
     */
    private String farEffect;


    //提交时间
    private Date createTime;

    //是否针剂商品
    private Integer isInjection;

    /**
     * 是否处方药 1.是 0.否
     **/
    private Integer isPrescription;

    /**
     * 来源 1.商家自建 2.商品库
     */
    private Byte source;


 /**
  * 一级发布分类id
  */
 private String erpFirstCategoryId;
 /**
  * 一级发布分类名称
  */
 private String erpFirstCategoryName;

    /**
     * 二级发布分类id
     */
    private String erpSecondCategoryId;
    /**
     * 二级发布分类名称
     */
    private String erpSecondCategoryName;


    /**
     * 三级发布分类id
     */
    private String erpThirdCategoryId;
    /**
     * 三级发布分类名称
     */
    private String erpThirdCategoryName;

    /**
     * 四级发布分类ID
     */
    private String erpFourthCategoryId;
    /**
     * 四级发布分类名称
     */
    private String erpFourthCategoryName;

    /**
     * 佣金比例
     */
    private BigDecimal commissionRatio;


    /**
     * 价格是否同步ERP（0:否;1:是）
     */
    private Integer priceSyncErp;
    /**
     * 库存是否同步ERP（0:否;1:是）
     */
    private Integer stockSyncErp;
    /**
     * 是否已经有审核通过的状态
     */
    private Boolean audited;
    /**
     * 药品类型
     */
    private Byte saleType;
    /**
     * 商圈id
     */
    private Long busAreaId;
    /**
     * 机构编码
     */
    private String shopCode;
    /**
     * 商圈
     */
    private String busAreaConfigName;
    /**
     * 起购数量
     */
    private Integer minPurchaseCount;
    /**
     * 限购信息
     */
    private PopSkuPurchaseLimitDto popSkuPurchaseLimitDto;
    /**
     * 推荐卖点1
     */
    private String sellingProposition1;
    /**
     * 推荐卖点2
     */
    private String sellingProposition2;
    /**
     * 推荐卖点3
     */
    private String sellingProposition3;
    /**
     * 医疗器械证图片
     */
    private List<String> instrumentLicenseImagList;
    /**
     * 医疗器械证有效期至
     */
    private String instrumentLicenseEffect;
    /**
     * 生产许可证号或备案凭证编号
     */
    private String manufacturingLicenseNo;
    /**
     * 生产许可证号或备案凭证图片
     */
    private List<String> manufacturingLicenseImageList;
    /**
     * 生产许可证号或备案凭证有效期至
     */
    private String manufacturingLicenseEffect;
    /**
     * 商品来源 0：普通，1：拼团品
     */
    private Integer activityType;
    /**
     * 产品技术要求编号
     */
    private String technicalRequirementNo ;
    /**
     * 上市许可持有人
     */
    private String marketAuthor;
    /**
     * aliasName
     */
    private String aliasName;

    /**
     * 是否委托生产：0、否 ；1、是
     */
    private Integer isCommissionProduction;

    /**
     * 委托生产厂家
     */
    private String entrustedManufacturer;

    /**
     * 委托生产厂家地址
     */
    private String entrustedManufacturerAddress;

    /**
     * 剂型 调中台获取
     */
    private List<String> dosageFormList;
    /**
     * 单位 调台获取
     */
    private List<String> productUnitList;

    private String filingsAuthor;

}
