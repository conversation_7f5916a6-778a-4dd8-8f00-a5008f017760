package com.xyy.ec.pop.controller;

import com.xyy.ec.order.business.dto.shippingReminder.WorkDayDateInfoVo;
import com.xyy.ec.order.business.dto.shippingReminder.WorkdayExceptionRuleDto;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.OrderShippingReminderRemote;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/22 18:08
 */
@Slf4j
@RequestMapping("/popWorkdayExceptionRule")
@Controller
public class PopWorkDayExceptionRuleController extends BaseController {

    @Autowired
    private OrderShippingReminderRemote orderShippingReminderRemote;

    /**
     * 保存日历规则
     * @param ruleDto
     * @return
     */
    @PostMapping(value = "/save")
    @ResponseBody
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> applyWorkdayExceptionRule(@RequestBody WorkdayExceptionRuleDto ruleDto){
        try{
            SysUser sysUser = getUser();
            String userName = StringUtils.isEmpty(sysUser.getUsername())?sysUser.getRealName():sysUser.getUsername();
            ruleDto.setOperator(userName);
            Boolean rule = orderShippingReminderRemote.applyWorkdayExceptionRule(ruleDto);
            if (!rule){
                return ResponseVo.errRest("保存失败");
            }
            return ResponseVo.successResult(true);
        } catch (Exception e){
            return ResponseVo.errRest("保存失败");
        }

    }


    /**
     * 保存日历规则
     * @param ruleDto
     * @return
     */
    @PostMapping(value = "/getWorkDayDateInfo")
    @ResponseBody
    public ResponseVo<List<WorkDayDateInfoVo>> getWorkDayDateInfo(@RequestBody WorkdayExceptionRuleDto ruleDto){
        try{
            List<WorkDayDateInfoVo> workDayDateInfo = orderShippingReminderRemote.getWorkDayDateInfo(ruleDto);
            if (CollectionUtils.isNotEmpty(workDayDateInfo)){
                return ResponseVo.successResult(workDayDateInfo);
            }
            return ResponseVo.errRest("查询失败");
        } catch (Exception e){
            return ResponseVo.errRest("查询失败");
        }
    }
}
