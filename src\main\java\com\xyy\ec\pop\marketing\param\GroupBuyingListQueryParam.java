package com.xyy.ec.pop.marketing.param;

import com.xyy.ms.promotion.business.common.constants.ActivityReportEnum;
import com.xyy.ms.promotion.business.enums.MarketingActivityCreateSourceEnum;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class GroupBuyingListQueryParam implements Serializable {

    /**
     * 商户编号，文本框，精确查询
     */
    private String orgId;

    /**
     * 商户名称，文本框，模糊查询
     */
    private String orgName;

    /**
     * 店铺名称，文本框，模糊查询
     */
    private String shopName;

    /**
     * 活动id，文本框，精确查询。支持查询报名ID和活动ID
     */
    private String actIdOrReportId;

    /**
     * csuid，文本框，精确查询
     */
    private String csuId;
    /**
     * 商品编码：文本框，精确查询
     */
    private String barcode;
    /**
     * 商品ERP编码：文本框，精确查询
     */
    private String productCode;
    /**
     * 商品名称：文本框，模糊查询
     */
    private String productName;

    /**
     * 拼团主题：文本框，模糊查询
     */
    private String theme;

    /**
     * 活动主题，多个活动使用英文逗号分隔。
     */
    private String topics;

    /**
     * 活动开始时间，时间戳，单位毫秒
     */
    private Date actStartTime;

    /**
     * 活动结束时间，时间戳，单位毫秒
     */
    private Date actEndTime;

    /**
     * 活动来源。1：EC后台、2：活动提报。
     *
     * @see MarketingActivityCreateSourceEnum
     */
    private Integer sourceType;

    /**
     * 状态。1：待审核；2：审核不通过（可修改）；3：审核通过-未开始；4：进行中；5：已下线；6：已结束；
     * 查询逻辑状态-7：已下线 & 已结束；
     *
     * @see ActivityReportEnum.ActReportIndexStatusEnum
     */
    private Integer status;

    /**
     * 是否是平台补贴，0否，1是
     */
    private Integer isPlatformSubsidy;

    /**
     * 是否是虚拟供应商，0否，1是
     */
    private Integer isVirtualShop;

    /**
     * 是否阶梯价 1:是 2:否
     */
    private Integer stepPriceStatus;

    /**
     * 是否成单，1是，2否。
     *
     * @see com.xyy.ms.promotion.business.common.config.IsTrueEnum
     */
    private Integer isOrder;

    /**
     * 当前页码数，从1开始。
     */
    private Integer pageNum;

    /**
     * 每页最多显示的条数，最小是1。
     */
    private Integer pageSize;

}
