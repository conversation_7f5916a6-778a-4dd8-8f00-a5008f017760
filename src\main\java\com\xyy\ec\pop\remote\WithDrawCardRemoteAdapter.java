package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.server.api.merchant.api.admin.WithDrawCardAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.WithDrawCardDto;
import com.xyy.ec.pop.server.api.shop.dto.PopCheckLogDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * @ClassName CashAccountRemoteAdapter
 * <AUTHOR>
 * @Date 2024/5/13 19:10
 * @Version 1.0
 */

@Component
@Slf4j
public class WithDrawCardRemoteAdapter {
    @Reference(version = "1.0.0")
    private WithDrawCardAdminApi withDrawCardAdminApi;

    public void addOrUpdateWithDrawCard(WithDrawCardDto withDrawCardDto, PopCheckLogDto popCheckLogDto){
        try {
            log.info("#WithDrawCardRemoteAdapter.addOrUpdateWithDrawCard#withDrawCardDto:{}", JSON.toJSONString(withDrawCardDto));
            withDrawCardAdminApi.addOrUpdateWithDrawCard(withDrawCardDto,popCheckLogDto);
        } catch (Exception e) {
            log.error("#CashAccountRemoteAdapter.addOrUpdateWithDrawCard#withDrawCardDto error:{}", JSON.toJSONString(withDrawCardDto), e);
            throw new ServiceRuntimeException("店铺提现方式更改失败");
        }
    }


    public List<WithDrawCardDto> selectWithDrawCardByOrgId(String orgId) {
        try {
            log.info("#WithDrawCardRemoteAdapter.selectWithDrawCardByOrgId#orgId:{}", orgId);
            List<WithDrawCardDto> withDrawCardDtoList = withDrawCardAdminApi.selectWithDrawCardByOrgId(orgId);
            return withDrawCardDtoList;
        } catch (Exception e) {
            log.error("#WithDrawCardRemoteAdapter.selectWithDrawCardByOrgId#orgId error:{}", orgId, e);
            throw new ServiceRuntimeException("查询提现卡信息异常");
        }
    }
}