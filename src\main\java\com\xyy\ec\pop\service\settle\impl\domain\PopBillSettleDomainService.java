package com.xyy.ec.pop.service.settle.impl.domain;


import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.po.PopBillSettlePo;
import com.xyy.ec.pop.remote.BillSettleRemote;
import com.xyy.ec.pop.remote.PopCorporationRemoteAdapter;
import com.xyy.ec.pop.server.api.admin.dto.PopBillSettleDetailDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCorporationDto;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.service.ProductCategoryService;
import com.xyy.ec.pop.service.settle.PopBillSettleService;
import com.xyy.ec.pop.utils.CollectionUtil;
import com.xyy.ec.pop.utils.PageInfoUtils;
import com.xyy.ec.pop.vo.settle.PopBillSettleDetailVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleVo;
import com.xyy.me.product.service.read.dto.TotalDictionaryReadDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/** 结算单
* <AUTHOR>
* @date  2020/12/4 10:19
* @table
*/
@Slf4j
@Service
public class PopBillSettleDomainService {

    @Autowired
    private PopBillSettleService popBillSettleService;

    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;

    @Resource
    private ProductCategoryService productCategoryService;

    @Resource
    private BillSettleRemote billSettleRemote;

    public Long queryPopBillListCount(PopBillSettleVo popBillSettleVo){
        if (handleParam(popBillSettleVo)){
            return 0L;
        }

       return popBillSettleService.queryPopBillSettleListCount(popBillSettleVo);
    }

    private boolean handleParam(PopBillSettleVo vo) {
        if (StringUtils.isNotEmpty(vo.getName()) || StringUtils.isNotEmpty(vo.getOrgName())){
            List<String> orgIds = popCorporationRemoteAdapter.getOrgIdByName(vo.getName(),vo.getOrgName());
            vo.setName(null);
            vo.setOrgName(null);
            if (CollectionUtil.isEmpty(orgIds)){
                return true;
            }else{
                vo.setOrgIds(orgIds);
            }
        }
        return false;
    }

    public PageInfo<PopBillSettlePo> queryPopBillSettleList(PopBillSettleVo popBillSettleVo, PageInfo pageInfo) {
        if (handleParam(popBillSettleVo)){
            return new PageInfo<>();
        }
        List<PopBillSettlePo> popBillPaymentPos;
        if (pageInfo != null) {
            int pageNum = ((pageInfo.getPageNum() == 0 ? 1 : pageInfo.getPageNum()) - 1) * pageInfo.getPageSize();
            popBillPaymentPos = popBillSettleService.queryPopBillSettleList(popBillSettleVo, pageNum, pageInfo.getPageSize());
        }else {
            popBillPaymentPos = popBillSettleService.queryPopBillSettleList(popBillSettleVo, null, null);
        }
        //设置商品实付金额=单据实付金额-运费=商品金额-店铺总优惠-平台总优惠
        popBillPaymentPos.stream().filter(Objects::nonNull).forEach(m -> {
            m.setProductActualMoney(m.getMoney().subtract(m.getFreightAmount()));
            if (m.getSettlementType() == CommissionSettleTypeEnum.EVERY_MONTH.getCode()) {
                //页面上佣金金额统一取hireMoney字段
                m.setHireMoney(m.getPayableCommission());
            }
        });
        PageInfo<PopBillSettlePo> billPaymentPoPageInfo = new PageInfo<>(popBillPaymentPos);


        List<String> orgIds = popBillPaymentPos.stream()
                .filter(Objects::nonNull)
                .map(PopBillSettlePo::getOrgId)
                .collect(Collectors.toList());

        Map<String, PopCorporationDto> popCorporationDtoMap = popCorporationRemoteAdapter.queryPopCorporationDtoMap(orgIds);
        if (CollectionUtil.isNotEmpty(popCorporationDtoMap)){
            for (PopBillSettlePo po:popBillPaymentPos) {
                PopCorporationDto popCorporationDto = popCorporationDtoMap.get(po.getOrgId());
                if (popCorporationDto != null) {
                    po.setName(popCorporationDto.getName());
                    po.setOrgName(popCorporationDto.getCompanyName());
                }
            }
        }

        if (pageInfo != null) {
            billPaymentPoPageInfo.setPageSize(pageInfo.getPageSize());
            billPaymentPoPageInfo.setPageNum(pageInfo.getPageNum());
        }
        Long count = popBillSettleService.queryPopBillSettleListCount(popBillSettleVo);
        billPaymentPoPageInfo.setTotal(count);

        if (pageInfo != null) {
            Long totalPageNum = (count + pageInfo.getPageSize() - 1) / pageInfo.getPageSize();
            billPaymentPoPageInfo.setPages(totalPageNum.intValue());
        }
        return billPaymentPoPageInfo;
    }




    public PopBillSettleStatisVo queryPopBillSettleStatis(PopBillSettleVo popBillSettleVo) {
        if (handleParam(popBillSettleVo)){
            PopBillSettleStatisVo popBillSettleStatisVo = new PopBillSettleStatisVo();
            return popBillSettleStatisVo;
        }
        return popBillSettleService.queryPopBillSettleStatis(popBillSettleVo);
    }

    public PageInfo<PopBillSettleDetailVo> queryPopBillSettleDetailList(String businessNo, Integer pageNum, Integer pageSize) {
        // 查结算单明细
        PageInfo<PopBillSettleDetailDto> pageInfo = billSettleRemote.getSettleDetailList(businessNo, pageNum, pageSize);

        // 查询一级分类
        Map<String, TotalDictionaryReadDto> firstCategoryMap = productCategoryService.getFirstLevelProductCategoryMap();

        // 数据转换
        List<PopBillSettleDetailVo> voList = pageInfo.getList().stream().map(po -> PopBillSettleDetailVo.from(po, firstCategoryMap)).collect(Collectors.toList());
        return PageInfoUtils.pack(voList, pageInfo);
    }
}
