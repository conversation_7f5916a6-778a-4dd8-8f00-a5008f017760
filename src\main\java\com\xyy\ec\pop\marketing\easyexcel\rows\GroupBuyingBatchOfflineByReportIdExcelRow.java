package com.xyy.ec.pop.marketing.easyexcel.rows;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 拼团批量下线Excel行。
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class GroupBuyingBatchOfflineByReportIdExcelRow implements Serializable {

    /**
     * 报名ID
     */
    @ExcelProperty(index = 0, value = "报名ID")
    private String reportIdStr;

}
