package com.xyy.ec.pop.controller.helper;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.ResultVO;
import com.xyy.ec.pop.config.ProductConfig;
import com.xyy.ec.pop.remote.MeProductApiRemote;
import com.xyy.ec.pop.remote.SkuMeRemote;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.product.api.admin.PopBusAreaApi;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuAdminApi;
import com.xyy.ec.pop.server.api.product.api.helper.PopSkuHelperApi;
import com.xyy.ec.pop.server.api.product.dto.PopBusAreaDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDetailDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.server.api.product.enums.PopSkuStatus;
import com.xyy.ec.pop.service.impl.ProductReportingServiceImpl;
import com.xyy.ec.pop.utils.SkuConvertHelper;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.product.back.end.ecp.pop.api.PopSkuApi;
import com.xyy.ec.product.back.end.ecp.pop.dto.SkuPopDTO;
import com.xyy.me.product.general.api.dto.product.GeneralMatchProductDto;
import com.xyy.me.product.general.api.dto.product.GeneralProductDto;
import com.xyy.me.product.general.api.dto.product.ProductDto;
import com.xyy.me.product.general.api.vo.product.GeneralMatchProduct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 商品处理工具接口
 * <AUTHOR>
 * @Date 2020/12/30
 */
@RestController()
@Slf4j
public class PopSKuHelperController {
    @Reference
    private PopSkuHelperApi popSkuHelperApi;
    @Autowired
    private ProductReportingServiceImpl reportingService;
    @Autowired
    private MeProductApiRemote meProductApiRemote;
    @Reference
    private PopSkuAdminApi popSkuAdminApi;
    @Autowired
    private SkuMeRemote skuMeRemote;
    @Value("${sku.erp.match.task.approvalNumber.minLength:5}")
    private Integer approvalMinLength;
    /**
     * 重新匹配商品标准库id
     * @return
     */
    @PostMapping(value = "/sku/helper/matchStandId")
    @ResponseBody
    public ResponseVo<Object> matchStandId(){
        int offset =0;
        int limit = 100;
        boolean moreMatch = true;//是否需要进行下一次匹配
        int total = 0;
        while(moreMatch){
            ApiRPCResult<List<PopSkuDto>> prs = popSkuHelperApi.noStandIdList(limit,offset);
            if(prs.isFail()){
                return ResponseVo.errRest(prs.getErrMsg());
            }
            moreMatch = prs.getData().size()>=limit;
            //记录匹配上的
            List<PopSkuDto> matched = new ArrayList<>(prs.getData().size());
            for(PopSkuDto sku:prs.getData()){
                GeneralMatchProduct product = reportingService.convertToGeneralMatchProduct(sku);
                GeneralMatchProductDto dto = meProductApiRemote.getGeneralMatchProduct(product);
                if(dto!=null&&product.getBusinessCode().equals(dto.getOldBusinessCode())&& StringUtils.isNotEmpty(dto.getProductId())){
                    sku.setStandardProductId(dto.getProductId());
                    matched.add(sku);
                }
            }
            //有匹配一的，则更新记录
            if(matched.size()>0){
                popSkuHelperApi.updateStandId(matched);
            }
            total +=matched.size();
            offset+=limit;
        }
        return ResponseVo.successResult("共成功匹配到"+total+"个标准库id");
    }

    /**
     * 通过批准文号查询中台4级分类
     * @return
     */
    @PostMapping(value = "/skuHelper/matchCategoryByAppNo")
    @ResponseBody
    public ResponseVo<Object> matchCategoryByAppNo(){
        //查询没有4级分类的商品编码
        log.info("PopSKuHelperController.matchCategoryByAppNo start");
        ApiRPCResult<Set<String>> barcodeResult = popSkuHelperApi.noCategoryBarcodes();
        log.info("PopSKuHelperController.matchCategoryByAppNo query result:{}", JSON.toJSONString(barcodeResult));
        if(barcodeResult.isFail()){
            return ResponseVo.errRest(barcodeResult.getErrMsg());
        }
        log.info("PopSKuHelperController.matchCategoryByAppNo query size:{}",barcodeResult.getData().size());
        List<List<String>> lists = Lists.partition(new ArrayList<>(barcodeResult.getData()),50);
        log.info("PopSKuHelperController.matchCategoryByAppNo query steps:{}",lists.size());
        int step = 0;
        boolean allOk = true;
        //分批处理
        for(List<String> barcodes:lists){
            log.info("PopSKuHelperController.matchCategoryByAppNo do step:{}/{}",++step,lists.size());
            ApiRPCResult<List<PopSkuDetailDto>> skuResult = popSkuHelperApi.querySKu(barcodes);
            if(skuResult.isFail()){
                log.info("PopSKuHelperController.matchCategoryByAppNo do step:{}/{} 查询数据异常",step,lists.size(),JSON.toJSONString(skuResult));
                allOk=false;
                continue;
            }
            List<PopSkuDetailDto> changed = matchMe(skuResult.getData());
            if(CollectionUtils.isEmpty(changed)){
                log.info("PopSKuHelperController.matchCategoryByAppNo over step:{}/{}",step,lists.size());
            }
            ApiRPCResult updateResult = popSkuHelperApi.updateCates(changed);
            if(skuResult.isFail()){
                log.info("PopSKuHelperController.matchCategoryByAppNo do step:{}/{} 更新异常",step,lists.size(),JSON.toJSONString(updateResult));
                allOk=false;
            }
            log.info("PopSKuHelperController.matchCategoryByAppNo over step:{}/{}",step,lists.size());
        }
        log.info("PopSKuHelperController.matchCategoryByAppNo over ");
        return ResponseVo.successResult(allOk);
    }

    private List<PopSkuDetailDto> matchMe(List<PopSkuDetailDto> data) {
        if(CollectionUtils.isEmpty(data)) return new ArrayList<>();
        //过滤有效批准文号
        data = data.stream().filter(item->StringUtils.isNotEmpty(item.getPopSku().getApprovalNumber())&&item.getPopSku().getApprovalNumber().trim().length()>=approvalMinLength).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(data)) return new ArrayList<>();
        Map<String, ProductDto> meDto = skuMeRemote.queryByApprovalNo(data.stream().map(item->item.getPopSku().getApprovalNumber().trim()).collect(Collectors.toList()));
        List<PopSkuDetailDto> result = new ArrayList<>(data.size());
        for(PopSkuDetailDto item:data){
            ProductDto dto = meDto.get(item.getPopSku().getApprovalNumber().trim());
            if(dto==null){
                continue;
            }
            //拷贝属性
            if(copy(item,dto)){
                result.add(item);
            }
        }
        return result;
    }

    private boolean copy(PopSkuDetailDto item, ProductDto dto) {
        Long catId = NumberUtils.toLong(dto.getBusinessScopeMulti());
        boolean coped = false;
        if(catId>0&&item.getPopSku().getSkuCategoryId()==null){
            item.getPopSku().setSkuCategoryId(catId);
            item.getPopSku().setSkuCategory(dto.getBusinessScopeMultiName());
            coped = true;
        }
        //1级分类值相同，其他3级值不同，4级一样，2、3级应该一样，只判断4级不同情况
        if(Objects.equals(item.getPopSkuCategory().getBusinessFirstCategoryCode(),String.valueOf(dto.getFirstCategory()))
        &&!Objects.equals(item.getPopSkuCategory().getBusinessFourthCategoryCode(),String.valueOf(dto.getFourthCategory()))){
            item.getPopSkuCategory().setBusinessSecondCategoryCode(String.valueOf(dto.getSecondCategory()));
            item.getPopSkuCategory().setBusinessThirdCategoryCode(String.valueOf(dto.getThirdCategory()));
            item.getPopSkuCategory().setBusinessFourthCategoryCode(String.valueOf(dto.getFourthCategory()));
            coped = true;
        }
        return coped;
    }
    @Value("${product.helper.start:0}")
    private long startId;
    @Value("${product.helper.step:100}")
    private int step;
    @Value("${product.helper.step:100}")
    private int sleep;
    @Value("#{'${helper.fbpOrgIds}'.split(',')}")
    private List<String> fbpOrgIds;
    @Reference
    private CorporationAdminApi corporationAdminApi;
    @Reference(version = "1.0.0")
    private PopBusAreaApi popBusAreaApi;
    @Reference
    private PopSkuApi ecPopSku;
    @Reference
    private com.xyy.ec.pop.server.api.product.api.PopSkuApi popSkuApi;
    /**
     * 规范商品字段历史信息 一次性工具
     * @return
     */
    @PostMapping("/reSetSkuFields")
    @ResponseBody
    public ResultVO reSetSkuFields(String orgId) throws InterruptedException {
        log.info("处理商品字段----开始");
        List<String> orgIds = corporationAdminApi.allOrgId().getData();
        orgIds.removeAll(fbpOrgIds);
        if(StringUtils.isNotEmpty(orgId)){
            orgIds = Arrays.asList(orgId);
        }
        //找到非fbp店铺id列表
        for(String org:orgIds){
            log.info("处理商品字段 当前处理机构:"+org);
            CorporationDto cor = corporationAdminApi.queryCorporation(org).getData();
            long start = startId;
            while(true){
                List<PopSkuDetailDto> skus = popSkuAdminApi.allSku(org,start,step);
                log.info("处理商品字段 start:{},skus:{}",start,skus.size());
                if(CollectionUtils.isEmpty(skus)){
                    log.info("处理商品字段,机构{}结束",org);
                    break;
                }
                log.info("处理商品字段 firstBarcode:{}",skus.get(0).getPopSku().getBarcode());
                start = skus.get(skus.size()-1).getPopSku().getId();
                //只要有标品库的
                Map<String, GeneralProductDto> standMap = queryMe(skus);
                if(standMap==null){
                    log.info("处理商品字段----查询中台失败");
                    return ResultVO.createSuccess();
                }
                               //处理处理字段
                skus = handleField(skus,standMap);
                if(CollectionUtils.isEmpty(skus)){
                    continue;
                }
                //同步ec
                List<Long> busIds = skus.stream().map(item -> item.getBusAreaProductRelation().getBusAreaId()).collect(Collectors.toList());
                Map<Long, List<Integer>> areaMap = queryAreas(busIds);
                List<SkuPopDTO> skuPopDTOS = SkuConvertHelper.convertToEcSkus(skus, areaMap, cor);
                ApiRPCResult<Integer> result =ecPopSku.batchUpdatePopShopSkuInfo(skuPopDTOS,cor.getShopCode(),"system");
                if(!result.isSuccess()||result.getData()!=skuPopDTOS.size()){
                    log.info("处理商品字段.updateToEc#ecSkuDto:{},shopCode:{} return {}", JSON.toJSONString(skuPopDTOS), JSON.toJSONString(cor.getShopCode()),  JSON.toJSONString(result));
                    log.info("处理商品字段----同步ec失败");
                    return ResultVO.createSuccess();
                }
                //同步pop
                ApiRPCResult result1 = popSkuApi.updateSkus(skus, "system");
                if(!result1.isSuccess()){
                    log.info("处理商品字段.updateToPop#skus:{},return {}", JSON.toJSONString(skus), JSON.toJSONString(result1));
                    log.info("处理商品字段----同步pop失败");
                    return ResultVO.createSuccess();
                }
                Thread.sleep(sleep);
            }
        }
        log.info("处理商品字段----结束");
        return ResultVO.createSuccess();
    }

    private Map<String, GeneralProductDto> queryMe(List<PopSkuDetailDto> skus) {
        List<PopSkuDetailDto> next = skus.stream().filter(item->StringUtils.isNotEmpty(item.getPopSku().getStandardProductId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skus)){
            return new HashMap<>(0);
        }
        //查询中台
        List<String> standIds = next.stream().map(item -> item.getPopSku().getStandardProductId()).distinct().collect(Collectors.toList());
        List<GeneralProductDto> generalProduct = meProductApiRemote.getGeneralProduct(standIds);
        if(generalProduct==null){
            return null;
        }
        Map<String, GeneralProductDto> standMap = generalProduct.stream().collect(Collectors.toMap(item -> item.getProductId(), item -> item));
        return standMap;
    }


    public Map<Long, List<Integer>> queryAreas(List<Long> busIds) {
        if(CollectionUtils.isEmpty(busIds)){
            return new HashMap<>();
        }
        Map<Long, List<PopBusAreaDto>> map = popBusAreaApi.queryByBusAreaIds(busIds).getData();
        Map<Long, List<Integer>> result = new HashMap<>(map.size());
        for(Map.Entry<Long, List<PopBusAreaDto>> entry :map.entrySet()){
            result.put(entry.getKey(),toAreaList(entry.getValue()));
        }
        return result;
    }

    public List<Integer> toAreaList(List<PopBusAreaDto> areaDtos) {
        if(CollectionUtils.isEmpty(areaDtos)){
            return new ArrayList<>(0);
        }
        List<Integer> list = new ArrayList<>(128);
        for(PopBusAreaDto area:areaDtos){
            list.add(area.getAreaCode());
            list.addAll(toAreaList(area.getChildren()));
        }
        return list;
    }

    private List<PopSkuDetailDto> handleField(List<PopSkuDetailDto> skus, Map<String, GeneralProductDto> standMap) {
        List<PopSkuDetailDto> result = new ArrayList<>(skus.size());
        for (PopSkuDetailDto dto : skus) {
            if(dto.getPopSkuCategory()==null||dto.getPopSkuInstruction()==null){//测试环境
                continue;
            }
            boolean isDrug = ProductConfig.config.drugCategory.contains(dto.getPopSkuCategory().getBusinessFirstCategoryCode());
            if(!isDrug){
                if (Objects.equals(dto.getPopSku().getStatus(), PopSkuStatus.SALE.getValue())
                        || Objects.equals(dto.getPopSku().getStatus(), PopSkuStatus.UN_SHELF.getValue())
                        || Objects.equals(dto.getPopSku().getStatus(), PopSkuStatus.STAY_ON_SHELF.getValue())) {
                    boolean changed = false;
                    if(StringUtils.isEmpty(dto.getPopSku().getDosageForm())||!"无".equals(dto.getPopSku().getDosageForm())){
                        dto.getPopSku().setDosageForm("无");
                        changed = true;
                    }
                    if(!Objects.equals(dto.getPopSku().getDrugClassification(),0)){
                        dto.getPopSku().setDrugClassification(0);
                        changed = true;
                    }
                    if(changed){
                        result.add(dto);
                    }
                }
                continue;
            }
            //必须根据中台信息变动
            GeneralProductDto gen = standMap.get(dto.getPopSku().getStandardProductId());
            if (gen == null) {
                continue;
            }
            boolean changed = false;
            if (isDrug) {
                //上市许可持有人
                if (StringUtils.isEmpty(dto.getPopSkuInstruction().getMarketAuthor()) &&
                        StringUtils.isNotEmpty(gen.getMarketAuthor())) {
                    dto.getPopSkuInstruction().setMarketAuthor(gen.getMarketAuthor());
                    changed = true;
                }
                //有效期
                String stTerm = getTermFromStand(gen);
                if (("*".equals(dto.getPopSku().getTerm()) || "-".equals(dto.getPopSku().getTerm())) &&
                        !"*".equals(stTerm)&&!"-".equals(stTerm) &&!stTerm.equals(dto.getPopSku().getTerm())) {
                    dto.getPopSku().setTerm(stTerm);
                    dto.getPopSku().setShelfLife(dto.getPopSku().getTerm());
                    changed = true;
                }
                //存储条件
                if ((StringUtils.isEmpty(dto.getPopSku().getStorageCondition()) || "无".equals(dto.getPopSku().getStorageCondition())) &&
                        StringUtils.isNotEmpty(gen.getStorageCondName())&& !"无".equals(gen.getStorageCondName()) && !Objects.equals(dto.getPopSku().getStorageCondition(), gen.getStorageCondName())) {
                    dto.getPopSku().setStorageCondition(gen.getStorageCondName());
                    changed = true;
                }
            }
            if (Objects.equals(dto.getPopSku().getStatus(), PopSkuStatus.SALE.getValue())
                    || Objects.equals(dto.getPopSku().getStatus(), PopSkuStatus.UN_SHELF.getValue())
                    || Objects.equals(dto.getPopSku().getStatus(), PopSkuStatus.STAY_ON_SHELF.getValue())) {
                if (isDrug) {
                    if((StringUtils.isEmpty(dto.getPopSku().getDosageForm())||"无".equals(dto.getPopSku().getDosageForm()))
                    &&StringUtils.isNotEmpty(gen.getDosageFormName())&&!"无".equals(gen.getDosageFormName())&&!Objects.equals(gen.getDosageFormName(),dto.getPopSku().getDosageForm())){
                        dto.getPopSku().setDosageForm(gen.getDosageFormName());
                        changed = true;
                    }
                    int drugClassification = getDrugClassification(gen.getPrescriptionCategoryName());
                    if(Objects.equals(dto.getPopSku().getDrugClassification(),0)&&!Objects.equals(dto.getPopSku().getDrugClassification(),drugClassification)){
                        dto.getPopSku().setDrugClassification(drugClassification);
                        changed = true;
                    }
                }
            }
            if(changed){
                result.add(dto);
            }
        }
        return result;
    }

    public static String getTermFromStand(GeneralProductDto dto){
        if(dto.getValidity()==null||dto.getValidityUnit()==null){
            return "*";//默认表示不确定
        }else if(dto.getValidity()==0){
            return "*";
        }else if(dto.getValidity()<0){
            return "-";
        }else {
            String unit = dto.getValidityUnit()==1?"日":dto.getValidityUnit()==3?"年":"月";
            return dto.getValidity()+unit;
        }
    }

    public static int getDrugClassification(String str){
        if(str==null){
            return 0;
        }else if(str.toUpperCase().contains("甲类OTC")){
            return 1;
        }else if(str.toUpperCase().contains("乙类OTC")){
            return 2;
        }else if(str.toUpperCase().contains("处方药")){
            return 3;
        }else {
            return 0;
        }
    }
}
