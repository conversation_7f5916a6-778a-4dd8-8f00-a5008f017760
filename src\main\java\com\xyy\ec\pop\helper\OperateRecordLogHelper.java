package com.xyy.ec.pop.helper;


import com.xyy.ec.pop.po.PopOperateRecordLogPo;

import java.util.Date;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 2021/07/16
 */
public class OperateRecordLogHelper {
    private OperateRecordLogHelper() {
    }

    public static PopOperateRecordLogPo buildOperateRecordLogPo(String operateName, String eventName, String eventType, String eventContent) {
        PopOperateRecordLogPo popOperateRecordLogPo = new PopOperateRecordLogPo();
        popOperateRecordLogPo.setOperateName(operateName);
        popOperateRecordLogPo.setEventName(eventName);
        popOperateRecordLogPo.setEventType(eventType);
        popOperateRecordLogPo.setEventContent(eventContent);
        popOperateRecordLogPo.setCreateTime(new Date());
        return popOperateRecordLogPo;
    }

}
