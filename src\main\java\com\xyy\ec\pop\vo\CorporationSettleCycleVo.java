package com.xyy.ec.pop.vo;

import com.xyy.ec.pop.server.api.merchant.dto.CorporationSettleCycle;
import lombok.Data;

/**
 * <AUTHOR>
 * 商户认证管理-合作信息-结算周期
 */
@Data
public class CorporationSettleCycleVo {
    /**
     * 结算周期
     */
    private Integer settleCycle;

    /**
     * 结算周期的T取值
     */
    private Integer settleValue;


    public static CorporationSettleCycleVo from(CorporationSettleCycle corporationSettleCycle) {
        if (corporationSettleCycle == null) {
            return null;
        }
        CorporationSettleCycleVo settleCycleVo = new CorporationSettleCycleVo();
        settleCycleVo.setSettleCycle(corporationSettleCycle.getSettleCycle());
        settleCycleVo.setSettleValue(corporationSettleCycle.getSettleValue());
        return settleCycleVo;
    }
}
