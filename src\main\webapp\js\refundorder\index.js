var btnArr = [];
$(document).ready(function () {

    $(function () {
        $(".tooltip-options a").tooltip({html: true});
    });
    //1.初始化项目大类列表
    new applyPendingList.tableInit().Init();
    //2.初始化操作按钮
    new applyPendingList.buttonInit().Init();
    //3.初始化退款原因选项
    initRefundReasonOptions();

    $('#startCreateTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var searchStartTime = $("#startCreateTime").val();
        $("#endCreateTime").datetimepicker('setStartDate', searchStartTime);
    });

    $('#endCreateTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var billEndTime = $("#endCreateTime").val();
        $("#startCreateTime").datetimepicker('setEndDate', billEndTime);
    });

    $('#startRefundFinishTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var searchStartTime = $("#startRefundFinishTime").val();
        $("#endCreateTime").datetimepicker('setStartDate', searchStartTime);
    });

    $('#endRefundFinishTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var billEndTime = $("#endRefundFinishTime").val();
        $("#endRefundFinishTime").datetimepicker('setEndDate', billEndTime);
    });

    $('#startTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var searchStartTime = $("#startTime").val();
        $("#endTime").datetimepicker('setStartDate', searchStartTime);
    });

    $('#endTime').datetimepicker({
        format: 'yyyy-mm-dd',
        language: 'zh-CN',
        bootcssVer: 3,
        autoclose: true,
        todayBtn: true,
        minView: "month"
    }).on('changeDate', function (ev) {
        var billEndTime = $("#endTime").val();
        $("#startTime").datetimepicker('setEndDate', billEndTime);
    });

    var tabDom = $('#tb_pendingList');
    /* 确认退款 */
    $('#btn_refund').on('click', function (e) {
        var index = tabDom.find('tr.success').data('index');
        if (index == null) {
            layer.msg('请选择一条记录');
            return false;
        }
        var row = tabDom.bootstrapTable('getData')[index];
        if (row.payChannel != 7){
            LayTool.confirm("确认已给客户返还退款？", {icon: 3, title: '提示'}, function (index) {
                $.ajax({
                    type: "POST",
                    url: basePath + 'orderRefund/confirmRefund',
                    data: {
                        "refundOrderId": row.id,
                        "auditState":1,
                        "fee": row.refundFee,
                        "orderNo":row.orderNo,
                        "refundType":row.refundType
                    },
                    dataType: "json",
                    success: function (data) {
                        if (data.code=="200") {
                            parent.layer.msg('操作成功');
                            tabDom.bootstrapTable('refresh');
                            return false;
                        } else {
                            parent.layer.msg('操作失败');
                        }
                    }
                });
                top.layer.close(index);
            });
        }else{
            var p = LayTool.open({
                title: "确认退款提醒",
                area: ['1000px', '500px'],
                btn: [ '关闭'],
                content: [
                    '/orderRefund/checkTransferStatus?refundId=' + row.id+'&refundOrderNo=' + row.refundOrderNo, 'yes'
                ],
                end: function () {
                    top.layer.close(p);
                }
            });
        }
     });
    /* 导出列表 */
    $('#btn_export').on('click', function (e) {
        var ps = [];
        var pms = queryParams();
        for (var k in pms) {	//组装参数
            var pm = pms[k]
            if (pm)
                ps.push(k + '=' + pm);
        }
        var s = this;
        $.ajax({
            type: "GET",
            url: "/refundOrderExport/async/exportRefundOrder",
            data: queryParams(),
            success:function (res) {
                if(res.code == 0){
                    var p = LayTool.confirm("文件生成中，请到文件下载中心页面进行查看和下载", {icon: 3, title: '提示',btn: ['文件下载中心', '取消']}
                        , function (index) {
                            $(s).addtab("文件下载中心", "/webPop/#/downloadList", 1);
                            LayTool.close(p);
                        });
                } else {
                    layer.msg(data.errorMsg);
                }
            }
        })
       /* window.location.href = basePath + 'orderRefund/downloadExcel?' + encodeURI(ps.join('&'));*/
    });

    $.ajax({
        type: "GET",
        url: "/businessProvinceList",
        dataType : "json",
        success:function (res) {
            var options = '<option value="" selected="selected">全部</option>';
            if(res.code == 0 && res.data != null && res.data.length > 0){
                var data = res.data;
                for (var i = 0; i < data.length; i++) {
                    options += '<option value='+data[i].provId+'>' + data[i].prov +'</option>';
                }
            }
            $("#provId").html(options);
        }
    });

    $.ajax({
        type: "GET",
        async: false,
        url: "/getButtons",
        data: "menuUrl=/orderRefund/index",
        success:function (res) {
            if(res.code == 0 && res.data != null){
                btnArr = res.data;
                if(jQuery.inArray("查看详情", btnArr) == -1){
                    $("#btn_view").remove();
                }
                if(jQuery.inArray("导出", btnArr) == -1){
                    $("#btn_export").remove();
                }
                if(jQuery.inArray("确认退款", btnArr) == -1){
                    $("#btn_refund").remove();
                }
            }
        }
    });
    function initRefundReasonOptions() {
        let refundReasons = [
            { value: "不想要了", label: "不想要了" },
            {
                value: "订单下错",
                label: "订单下错",
                children: [
                    { value: "商品买错了", label: "商品买错了" },
                    { value: "商品买多了", label: "商品买多了" },
                    { value: "账户下错了", label: "账户下错了" },
                    { value: "商品拍少了", label: "商品拍少了" }
                ]
            },
            { value: "商家缺货/不发货", label: "商家缺货/不发货" },
            { value: "超出药店经营范围", label: "超出药店经营范围" },
            {value: "追溯码问题",label: "追溯码问题",},            
            {
                value: "未享受优惠",
                label: "未享受优惠",
                children: [
                    { value: "优惠券未使用", label: "优惠券未使用" },
                    { value: "红包未使用", label: "红包未使用" },
                    { value: "未享受活动", label: "未享受活动" }
                ]
            },
            { value: "价格高", label: "价格高" },
            { value: "药店资质问题", label: "药店资质问题" },
            { value: "与商家协商一致退款", label: "与商家协商一致退款" },
            { value: "多拍/错拍/不想要了",label: "多拍/错拍/不想要了" },
            { value: "商品破损",label: "商品破损" },
            { value: "质量问题",label: "质量问题"},
            {
                value: "商品漏发",
                label: "商品漏发"
            },
            {
                value: "商品错发",
                label: "商品错发",
            },
            {
                value: "实物与展示不符",
                label: "实物与展示不符",
            },
            {
                value: "商品召回",
                label: "商品召回"
            },
            {
                value: "无发票/无出库单",
                label: "无发票/无出库单"
            },
            {
                value: "与商家协商一致退货",
                label: "与商家协商一致退货"
            },
            {
                value: "未收到货",
                label: "未收到货"
            },
            {
                value: "商家通知拒收",
                label: "商家通知拒收"
            },
            {
                value: "无商品资料",
                label: "无商品资料"
            },
            {
                value: "虚假发货",
                label: "虚假发货",
                children: [
                    { value: "快递单号无物流", label: "快递单号无物流" },
                    { value: "收件人信息不符", label: "收件人信息不符" },
                    { value: "快递单号物流信息与实际不符", label: "快递单号物流信息与实际不符" }
                ]
            },
            {
                value: "退运费",
                label: "退运费"
            },
            {
                value: "物流慢",
                label: "物流慢"
            }
        ];
        function removeDuplicateReasons(reasons) {
            const seen = new Set();
            return reasons.filter(reason => {
                const duplicate = seen.has(reason.value);
                seen.add(reason.value);
                return !duplicate;
            });
        }
        refundReasons = removeDuplicateReasons(refundReasons);

        const dropdown = document.getElementById('refundReasonDropdown');

        // 先清理可能存在的右侧面板
        const existingRightPanel = document.getElementById('cascadeRightPanel');
        if (existingRightPanel) {
            existingRightPanel.remove();
        }

        let html = `
            <label class="multi-select-checkbox" onmouseenter="hideCascadeChildren()">
                <input type="checkbox" checked value="" onchange="updateRefundReasonSelection(event)" style="margin-right: 5px;">
                全部
            </label>
        `;

        refundReasons.forEach(reason => {
            // 渲染父级选项
            if (reason.children && reason.children.length > 0) {
                // 有子选项的父级，使用级联样式
                html += `
                    <label class="multi-select-checkbox cascade-parent" data-parent-value="${reason.value}" onmouseenter="showCascadeChildren('${reason.value}')">
                        <input type="checkbox" value="${reason.value}" onchange="updateRefundReasonSelection(event)" style="margin-right: 5px;">
                        ${reason.label}
                        <span class="cascade-arrow" style="float: right; color: #999;">▶</span>
                    </label>
                `;
            } else {
                // 没有子选项的普通选项
                html += `
                    <label class="multi-select-checkbox" onmouseenter="hideCascadeChildren()">
                        <input type="checkbox" value="${reason.value}" onchange="updateRefundReasonSelection(event)" style="margin-right: 5px;">
                        ${reason.label}
                    </label>
                `;
            }
        });

        dropdown.innerHTML = html;

        // 创建独立的右侧面板，添加到body中
        const rightPanelHtml = `
            <div class="cascade-right-panel" id="cascadeRightPanel" style="display: none;" onmouseenter="cancelHideCascadeChildren()" onmouseleave="hideCascadeChildren()">
                ${refundReasons.map(reason => {
                    if (reason.children && reason.children.length > 0) {
                        return `
                            <div class="cascade-children-panel" id="children-${reason.value}" style="display: none;">
                                ${reason.children.map(child => `
                                    <label class="multi-select-checkbox" data-parent="${reason.value}">
                                        <input type="checkbox" value="${child.value}" onchange="updateRefundReasonSelection(event)" style="margin-right: 5px;">
                                        ${child.label}
                                    </label>
                                `).join('')}
                            </div>
                        `;
                    }
                    return '';
                }).join('')}
            </div>
        `;

        // 将右侧面板添加到body中
        document.body.insertAdjacentHTML('beforeend', rightPanelHtml);
    }

    // 显示级联子选项面板
    window.showCascadeChildren = function(parentValue) {
        const rightPanel = document.getElementById('cascadeRightPanel');
        const childrenPanel = document.getElementById(`children-${parentValue}`);
        const dropdown = document.getElementById('refundReasonDropdown');

        // 取消任何延迟隐藏
        cancelHideCascadeChildren();

        // 立即隐藏所有子选项面板
        document.querySelectorAll('.cascade-children-panel').forEach(panel => {
            panel.style.display = 'none';
        });

        // 移除所有父选项的高亮
        document.querySelectorAll('.cascade-parent').forEach(parent => {
            parent.classList.remove('cascade-active');
        });

        if (childrenPanel && rightPanel) {
            // 计算右侧面板的位置
            const dropdownRect = dropdown.getBoundingClientRect();
            const parentLabel = document.querySelector(`[data-parent-value="${parentValue}"]`);

            if (parentLabel) {
                const parentRect = parentLabel.getBoundingClientRect();

                // 设置右侧面板位置：紧贴下拉框右边缘
                rightPanel.style.position = 'fixed';
                rightPanel.style.left = (dropdownRect.right + 2) + 'px'; // 右边缘 + 2px间距
                rightPanel.style.top = parentRect.top + 'px'; // 与当前hover项对齐
                rightPanel.style.zIndex = '1001';

                // 显示右侧面板
                rightPanel.style.display = 'block';
                // 显示对应的子选项面板
                childrenPanel.style.display = 'block';
                // 高亮当前父选项
                parentLabel.classList.add('cascade-active');
            }
        }
    }

    // 隐藏级联子选项面板
    window.hideCascadeChildren = function() {
        const rightPanel = document.getElementById('cascadeRightPanel');
        if (rightPanel) {
            rightPanel.style.display = 'none';
        }
        // 移除所有父选项的高亮
        document.querySelectorAll('.cascade-parent').forEach(parent => {
            parent.classList.remove('cascade-active');
        });
    }

    // 延迟隐藏级联子选项面板
    let hideTimer = null;
    window.delayHideCascadeChildren = function() {
        hideTimer = setTimeout(function() {
            hideCascadeChildren();
        }, 200); // 200ms延迟
    }

    // 取消延迟隐藏
    window.cancelHideCascadeChildren = function() {
        if (hideTimer) {
            clearTimeout(hideTimer);
            hideTimer = null;
        }
    }

    //  退款原因下拉框
    window.toggleRefundReasonDropdown = function () {
        const dropdown = document.getElementById('refundReasonDropdown');
        const isVisible = dropdown.style.display !== 'none';

        dropdown.style.display = isVisible ? 'none' : 'block';

        // 关闭下拉框时，隐藏右侧面板
        if (isVisible) {
            hideCascadeChildren();
        }
    }

    window.updateRefundReasonSelection = function (event) {
        const allCheckbox = document.querySelector('#refundReasonDropdown input[value=""]'); // "全部"选项
        const otherCheckboxes = document.querySelectorAll('#refundReasonDropdown input[type="checkbox"]:not([value=""])'); // 其他选项
        // 如果点击的是"全部"选项
        if (event && event.target.value === '') {
            if (event.target.checked) {
                // 选中"全部"时，取消其他所有选项
                otherCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
            }
        } else {
            // 如果点击的是其他选项
            if (event && event.target.checked) {
                // 选中其他选项时，取消"全部"选项
                if (allCheckbox) {
                    allCheckbox.checked = false;
                }
            }

            // 处理父子级联逻辑
            const clickedLabel = event.target.closest('label');
            const parentValue = clickedLabel.getAttribute('data-parent');

            if (parentValue) {
                // 如果点击的是子选项
                if (event.target.checked) {
                    // 选中子选项时，取消对应的父选项
                    const parentCheckbox = document.querySelector(`#refundReasonDropdown input[value="${parentValue}"]`);
                    if (parentCheckbox) {
                        parentCheckbox.checked = false;
                    }

                    // 检查该父级下的所有子选项是否都被选中
                    const allChildCheckboxes = document.querySelectorAll(`#refundReasonDropdown label[data-parent="${parentValue}"] input[type="checkbox"]`);
                    const checkedChildCheckboxes = document.querySelectorAll(`#refundReasonDropdown label[data-parent="${parentValue}"] input[type="checkbox"]:checked`);

                    if (allChildCheckboxes.length === checkedChildCheckboxes.length && allChildCheckboxes.length > 0) {
                        // 所有子选项都被选中，自动勾选父选项并取消所有子选项
                        if (parentCheckbox) {
                            parentCheckbox.checked = true;
                        }
                        allChildCheckboxes.forEach(checkbox => {
                            checkbox.checked = false;
                        });
                        // 隐藏右侧面板
                        hideCascadeChildren();
                    }
                } else {
                    // 取消选中子选项时，确保父选项也未选中
                    const parentCheckbox = document.querySelector(`#refundReasonDropdown input[value="${parentValue}"]`);
                    if (parentCheckbox) {
                        parentCheckbox.checked = false;
                    }
                }
            } else {
                // 如果点击的是父选项
                if (event.target.checked) {
                    // 选中父选项时，取消所有对应的子选项
                    const childCheckboxes = document.querySelectorAll(`#refundReasonDropdown label[data-parent="${event.target.value}"] input[type="checkbox"]`);
                    childCheckboxes.forEach(checkbox => {
                        checkbox.checked = false;
                    });
                    // 隐藏右侧面板
                    hideCascadeChildren();
                }
            }
        }
        // 收集选中的非空值选项
        const selected = [];
        otherCheckboxes.forEach(checkbox => {
            if (checkbox.checked && checkbox.value) {
                selected.push(checkbox.value);
            }
        });

        // 检查是否所有非空选项都被选中
        const allOtherOptionsSelected = Array.from(otherCheckboxes).every(checkbox => checkbox.checked);

        let finalValue = '';
        let displayText = '';

        if (allCheckbox && allCheckbox.checked) {
            // 选中了"全部"选项
            finalValue = '';
            displayText = '全部';
        } else if (allOtherOptionsSelected && selected.length > 0) {
            // 所有其他选项都被选中，等同于"全部"
            finalValue = '';
            displayText = '全部';
            // 自动勾选"全部"选项，取消其他选项
            if (allCheckbox) {
                allCheckbox.checked = true;
            }
            otherCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        } else if (selected.length > 0) {
            // 选中了部分选项
            finalValue = selected.join(',');
            displayText = selected.join(', ');
        } else {
            // 没有选中任何选项
            finalValue = '';
            if (allCheckbox) {
                allCheckbox.checked = true;
            }
            displayText = '全部';
        }
        // 更新隐藏input的值
        document.getElementById('refundReason').value = finalValue;
        // 更新显示文本
        document.getElementById('refundReasonText').textContent = displayText;
        document.getElementById('refundReasonText').style.color = '#333';
    }

    // 点击外部关闭下拉框
    document.addEventListener('click', function(event) {
        const container = document.querySelector('.multi-select-container');
        const rightPanel = document.getElementById('cascadeRightPanel');

        // 检查点击是否在下拉框容器或右侧面板内
        const isInsideContainer = container && container.contains(event.target);
        const isInsideRightPanel = rightPanel && rightPanel.contains(event.target);

        if (!isInsideContainer && !isInsideRightPanel) {
            document.getElementById('refundReasonDropdown').style.display = 'none';
            // 隐藏右侧面板
            hideCascadeChildren();
        }
    });
});
function clearReasonSelection() {
    const allCheckbox = document.querySelector('#refundReasonDropdown input[value=""]'); // "全部"选项
    const otherCheckboxes = document.querySelectorAll('#refundReasonDropdown input[type="checkbox"]:not([value=""])'); // 其他选项
    otherCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    })
    allCheckbox.checked = true;
    document.getElementById('refundReason').value = "";
    document.getElementById('refundReasonText').textContent = '全部';
    document.getElementById('refundReasonText').style.color = '#333';
}
var applyPendingList = $.applyPendingList = ({
    //项目大类列表初始化
    tableInit: function () {
        var applyPendingTableObj = new Object();
        //初始化Table
        applyPendingTableObj.Init = function () {
            var $table = $('#tb_pendingList');
            $table.bootstrapTable({
                url: '/orderRefund/list', //请求后台的URL（*）
                method: 'get', //请求方式（*）
                contentType: 'application/x-www-form-urlencoded',
                dataType: 'json', //传入的类型
                toolbar: '#toolbar', //工具按钮用哪个容器
                striped: true, //是否显示行间隔色
                cache: false, //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                pagination: true, //是否显示分页（*）
                sortable: false, //是否启用排序
                sortOrder: "asc", //排序方式
                queryParams: applyPendingTableObj.queryParams, //传递参数（*）
                sidePagination: "server", //分页方式：client客户端分页，server服务端分页（*）
                formatLoadingMessage: function () {
                    return '请稍后,正在加载中...';
                },
                showFooter: false,
                pageNumber: 1, //初始化加载第一页，默认第一页
                pageSize: 10, //每页的记录行数（*）
                pageList: [10, 20, 50, 100], //可供选择的每页的行数（*）
                strictSearch: true,
                onLoadSuccess: function (data) {
                },
                clickToSelect: true, //是否启用点击选中行
                //height: 550, //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                onClickRow: function (row, $element) {
                    $('.success').removeClass('success');
                    $($element).addClass('success');
                    if(row.isThirdCompany != 0){
                        $("#btn_refund").show();
                        $("#btn_export").show();
                    }
                    if(row.auditState==0 && row.payType==3 && row.auditProcessState==1 && row.payChannel != 8 && row.isFbp == 0){
                        $("#btn_refund").show();
                    }else{
                        $("#btn_refund").hide();
                    }
                    if(row.isThirdCompany == 0){
                        $("#btn_refund").hide();
                        $("#btn_export").hide();
                    }
                },
                uniqueId: "id", //每一行的唯一标识，一般为主键列
                columns: [{
                    field: 'orgId',
                    title: '商户编号',
                    align: 'center',
                    sortable: false
                },{
                    field: 'companyName',
                    title: '商户名称',
                    align: 'center',
                    sortable: true
                },{
                    field: 'businessName',
                    title: '店铺名称',
                    align: 'center',
                    sortable: true
                },{
                    field: 'prov',
                    title: '商户注册省份',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'merchantName',
                    title: '客户名称',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'branchName',
                    title: '省份',
                    align: 'center',
                    sortable: true
                },{
                    field: 'orderNo',
                    title: '销售单号',
                    align: 'center',
                    sortable: true
                },{
                    field: 'payType',
                    title: '支付类型',
                    align: 'center',
                    sortable: true,
                    formatter: function (val, row) {
                        switch (val) {
                            case 1:
                                return '<span style="color:#99CC33">在线支付</span>';
                            case 2:
                                return '<span style="color:#99CC33">货到付款</span>';
                            case 3:
                                return '<span style="color:#99CC99">线下转账</span>';
                        }
                    }
                },{
                    field: 'payChannel',
                    title: '支付渠道',
                    align: 'center',
                    sortable: true,
                    formatter: paymentChannelFormatter
                },{
                    field: 'refundOrderNo',
                    title: '退款单号',
                    align: 'center',
                    sortable: true
                },{
                    field: 'refundFee',
                    title: '总实退金额',
                    align: 'center',
                    sortable: true,
                    formatter:function (val,row) {
                        /* if(row.auditState == 1){
                            return row.refundActualFee;
                        } */
                        let result = `<p>${val}</p>`
                        if (row.freightAmount) {
                            result +=  `<p>含运费：${row.freightAmount}</p>`;
                        }
                        return result;
                    }
                },{
                    field: 'virtualGold',
                    title: '购物金实退金额',
                    align: 'center',
                    sortable: true
                },{
                    field: 'cashPayAmount',
                    title: '现金实退金额',
                    align: 'center',
                    sortable: true
                },{
                    field: 'indemnityMoney',
                    title: '赔偿金额',
                    align: 'center'
                },{
                    field: 'indemnitySource',
                    title: '赔偿金额来源账户',
                    align: 'center',
                    formatter: function(val) {
                        if(val == 1){
                            return "营销服务额度";
                        } else if (val == 2) {
                            return "保证金";
                        }
                    }
                },{
                    field: 'auditStatusName',
                    title: '退款单状态',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'payStatus',
                    title: '退款支付状态',
                    align: 'center',
                    sortable: true,
                    formatter: function payStatusFormatter(val) {
                        //1：已发起，2：退款成功，3：退款失败，4：处理中
                        if (val == 2) {
                            return "退款成功";
                        } else if (val == 3) {
                            return "退款失败";
                        } else {
                            return "退款中";
                        }
                    }
                }, {
                    field: 'createTime',
                    title: '下单时间',
                    align: 'center',
                    sortable: true,
                    formatter: datetimeFormatterHms
                } ,{
                    field: 'refundChannel',
                    title: '发起方',
                    align: 'center',
                    formatter: function refundChannelFormatter(val) {
                        if(val == 1){
                            return "用户发起";
                        } else if (val == 2) {
                            return "平台发起";
                        } else if (val == 4) {
                            return "商户发起";
                        } else if(val  == 5){
                            return "系统发起";
                        }
                    }
                }, {
                    field: 'refundCreateTime',
                    title: '申请退款时间',
                    align: 'center',
                    sortable: true,
                    formatter: datetimeFormatterHms
                }, {
                    field: 'refundReason',
                    title: '退款原因',
                    align: 'center',
                    sortable: true,
                }, {
                    field: 'owner',
                    title: '退款账户名',
                    align: 'center',
                    sortable: true
                }, {
                    field: 'bankCard',
                    title: '账号',
                    align: 'center'
                },{
                    field: "bankName",
                    title: '开户行',
                    align: 'center'
                },{
                    field: 'refundAuditTime',
                    title: '退款时间',
                    align: 'center',
                    formatter: datetimeFormatterHms
                },{
                    field: 'isFbp',
                    title: '是否入仓订单',
                    align: 'center',
                    formatter: function (val) {
                        if(val==1){
                            return "是";
                        }
                        return "否"
                    }
                },{
                    field: 'urgencyinfo',
                    title: '订单客服备注',
                    align: 'center',
                    sortable: true
                }]
            });
        };
        //查询的参数
        applyPendingTableObj.queryParams = function (params) {
            return queryParams(params);
        };
        return applyPendingTableObj;
    },
    buttonInit: function () {
        var $table = $('#tb_pendingList');
        var oInit = new Object();
        oInit.Init = function () {
            function getSelectedRow() {
                var index = $table.find('tr.success').data('index');
                return $table.bootstrapTable('getData')[index];
            }

            //初始化页面上面的按钮事件
            //条件查询事件
            $("#btn_query").click(function () {
                $table.bootstrapTable('selectPage',1);
            });
            //条件清空事件
            $("#btn_clear").click(function () {
                $('#corporationNo').val('');
                $('#corporationName').val('');
                $("#orderNo").val("");
                $("#companyName").val("");
                $("#refundOrderNo").val("");
                $("#auditState").val("");
                $("#startCreateTime").val("");
                $("#endCreateTime").val("");
                $("#startRefundFinishTime").val("");
                $("#endRefundFinishTime").val("");
                $("#startTime").val("");
                $("#endTime").val("");
                $("#orgId").val("");
                $("#merchantName").val("");
                $("#payType").val("");
                $("#branchCode").val("XS000000");
                $("#refundChannel").val("");
                $("#refundReason").val("");
                $("#payChannel").val("");
                $("#provId").val("");
                $("#isFbp").val("");
                $("#payStatus").val("");
                clearReasonSelection()
                // $table.bootstrapTable('refresh');
                $table.bootstrapTable('selectPage',1);
            });

            $("#btn_view").click(function () {
                var row = getSelectedRow();
                if (row) {
                    LayTool.open({
                        title: "退款单信息",
                        area: ['1200px', '600px'],
                        content: [
                            '/orderRefund/toRefundInfo?refundOrderNo=' + row.refundOrderNo, 'yes'
                        ]
                    });
                } else {
                    LayTool.alert("请选择一条记录");
                }
            });

            $.ajax({
                type: "GET",
                url: "/businessProvinceList",
                dataType : "json",
                success:function (res) {
                    var options = '<option value="" selected="selected">全部</option>';
                    if(res.code == 0 && res.data != null && res.data.length > 0){
                        var data = res.data;
                        for (var i = 0; i < data.length; i++) {
                            options = '<option value='+data[i].provId+'>' + data[i].prov +'</option>';
                        }
                    }
                    $("#provId").html(options);
                }
            });

        };
        return oInit;
    }
});

function paymentChannelFormatter(val) {
    if (val == 1) {
        return "支付宝";
    } else if (val == 2) {
        return "微信";
    } else if (val == 3) {
        return "银联";
    }else if (val == 7) {
        return "电汇平台";
    }else if (val == 8) {
        return "电汇商业";
    } else if (val == 10) {
        return "平安ePay";
    } else if (val == 11) {
        return "JD银行卡支付"
    } else if (val == 12) {
        return "京东采购融资";
    }else if (val == 13) {
        return "农行链e贷";
    }else if (val == 15) {
      return "金蝶信用付";
  }
}

function datetimeFormatter(val) {
    if (val != null) {
        return ToolUtil.dateFormat(val, 'yyyy-MM-dd');
    }
}

function datetimeFormatterHms(val) {
    if (val != null) {
        return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
    }
}

function fileClick(node) {
    var $file = $(node).siblings("input[type='file']");
    return $($file).click();
}

var queryParams = function(params) {
    params = params || {};
    return {
        //每页显示条数
        limit: params.limit,
        //起始页数
        offset: params.offset,
        //排序字段
        property: (params.sort && params.sort !== '') ? ToolUtil.underscoreName(params.sort) : '',
        //排序方式
        direction: params.order,
        //查询条件（将对象属性封装成查询实体对象）
        corporationNo:$.trim($('#corporationNo').val()),
        corporationName:$.trim($('#corporationName').val()),
        companyName:$.trim($('#companyName').val()),
        orderNo: $.trim($("#orderNo").val()),
        refundOrderNo: $.trim($("#refundOrderNo").val()),
        auditState: $.trim($("#auditState").val()),
        startCreateTime: $.trim($("#startCreateTime").val()),
        endCreateTime: $.trim($("#endCreateTime").val()),
        startTime: $.trim($("#startTime").val()),
        endRefundFinishTime: $.trim($("#endRefundFinishTime").val()==""?"":$("#endRefundFinishTime").val()+" 23:59:59"),
        startRefundFinishTime: $.trim($("#startRefundFinishTime").val()),
        endTime: $.trim($("#endTime").val()),
        payType:$.trim($("#payType").val()),
        merchantName:$.trim($("#merchantName").val()),
        provinceCode:$.trim($("#branchCode").val()),
        refundChannel:$.trim($("#refundChannel").val()),
        refundReason:$.trim($("#refundReason").val()),
        payChannel:$.trim($("#payChannel").val()),
        provId:$.trim($("#provId").val()),
        payStatus:$.trim($("#payStatus").val()),
        isThirdCompany:$.trim($("#txt_search_isThirdCompany").val()),
        isFbp:$.trim($("#isFbp").val())
        // branchCode:$.trim($("#branchCode").val())
    };
}

