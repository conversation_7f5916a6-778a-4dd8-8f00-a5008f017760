package com.xyy.ec.pop.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.xyy.ec.pop.remote.UploadRemote;
import com.xyy.ec.pop.service.FastDfsUtilService;
import com.xyy.ec.pop.utils.cos.CosUploadPopConfig;
import com.xyy.ec.pop.utils.cos.CosUploadPopUtils;
import com.xyy.ec.pop.utils.fastdfs.FDFSClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collection;
import java.util.Objects;
import java.util.UUID;

/**
 * @Description 文件服务工具
 * <AUTHOR>
 * @Date 2021/3/12
 */
@Service
@Slf4j
public class FastDfsUtilServiceImpl implements FastDfsUtilService {

    @Autowired
    private FDFSClient fdfsClient;
    @Autowired
    private UploadRemote uploadRemote;
    @Autowired
    private CosUploadPopConfig cosUploadPopConfig;

    @Override
    public String writeDateToFastDfs(ExportParams entity, Class<?> pojoClass, Collection<?> dataSet) {
        Workbook workbook = ExcelExportUtil.exportExcel(entity, pojoClass, dataSet);
        String fileUrl = toFdfs(workbook, entity.getSheetName(),entity.getType()==ExcelType.XSSF?"xlsx":"xls");
        return fileUrl;
    }

    public String toFdfs(Workbook workbook, String fileName,String extName) {
        if(StringUtils.isEmpty(fileName)){
            fileName = UUID.randomUUID().toString();
        }
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            workbook.close();
            String str;
            if (Objects.equals(cosUploadPopConfig.getSwitchFlag(), true)) {
                //将文件上传fastdfs
                str = fdfsClient.upload(new ByteArrayInputStream(out.toByteArray()), fileName, extName);
            } else {
                str = CosUploadPopUtils.uploadAndGetRelativePath(new ByteArrayInputStream(out.toByteArray()), fileName + "." + extName);
            }
            return str; //直接返回全路径
        } catch (IOException e) {
            log.error("导出excel错误。", e);
        }
        return null;
    }
}
