package com.xyy.ec.pop.utils;

import com.alibaba.druid.util.StringUtils;
import com.xyy.ec.pop.enums.JavaTypeEnum;
import com.xyy.ec.pop.exception.ServiceException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 导出数据表格的工具类
 * 暂定支持最大导出1048575条,如有需要,另行再调大
 * @ClassName: UtilOrganizeExcelData
 * <AUTHOR>
 * @date 2016-2-23 下午11:08:43
 */
public class UtilOrganizeExcelData {
	private static Logger LOGGER = LoggerFactory.getLogger(UtilOrganizeExcelData.class);

	/**
	 * @param titleCn 中文标题名
	 * @param colsCn 中文列名
	 * @param dataFileds 数据域名称
	 * @param styles 数据的格式 0:顺序号 1：文本 2：布尔  3：金额（实际金额带小数点） 4:金额 5:日期 6:时间 7:利率 9:特殊金额
	 * @param alignStyles 对齐方式 ALIGN_LEFT = 1;ALIGN_CENTER = 2;ALIGN_RIGHT = 3;
	 * @param widths 列的宽度 默认为4000
	 * @param dataList 数据List
	 * @param excelFileName 下载文件名称
	 * @return
	 */
	public static boolean listToExcel(HttpServletResponse response, String titleCn, String colsCn, String dataFileds,
                                      String styles, String alignStyles, String widths, List<? extends Object> dataList, String excelFileName) throws ServiceException {
		String excelSplitChar = ",";// 域分割符号
		String excelRowSplitChar = ";";// 行分割符号
		// ---------------------------------------------------
		String columnFiledName[] = StringUtil.splitStringToString(dataFileds, excelSplitChar);// context中的变量名
		String columnStyles[] = StringUtil.splitStringToString(styles, excelSplitChar);// 数据类型
		String columnAlignStyles[] = StringUtil.splitStringToString(alignStyles, excelSplitChar);// 对齐方式
		String columnWidths[] = StringUtil.splitStringToString(widths, excelSplitChar);// 宽度

		int columnNum = columnFiledName.length;// 列数
		/**
		 * closName的格式如下 "序号,项目,年初余额,收入,-,支出,-,期末余额;|,|,|,本月,本年累计,本月,本年累计,|"
		 * 可以多行，行与行之间用“;”隔开 “|”表示改列别合并了，不处理
		 */
		String columnRowName[] = StringUtil.splitStringToString(colsCn, excelRowSplitChar);
		String columnName[][] = new String[columnRowName.length][columnNum];
		for (int i = 0; i < columnRowName.length; i++) {
			columnName[i] = StringUtil.splitStringToString(columnRowName[i], excelSplitChar);
		}

		// 创建一个工作表
		XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
		SXSSFWorkbook excelWorkBook = new SXSSFWorkbook(xssfWorkbook, 1000);

		// 设置字体位置
		CellStyle style1_LEFT = excelWorkBook.createCellStyle();// 普通_左
		CellStyle style1_CENTER = excelWorkBook.createCellStyle();// 普通_中
		CellStyle style1_RIGHT = excelWorkBook.createCellStyle();// 普通_右
		CellStyle style2 = excelWorkBook.createCellStyle();// 表头
		CellStyle style3 = excelWorkBook.createCellStyle();// 标题

		CellStyle styleTemp = null;
		// 设置字体
		Font font1 = excelWorkBook.createFont();
		Font font2 = excelWorkBook.createFont();
		Font font3 = excelWorkBook.createFont();

		font1.setFontHeightInPoints((short) 11); // 字体大小
		font1.setFontName("宋体");
		style1_LEFT.setFont(font1);
		style1_CENTER.setFont(font1);
		style1_RIGHT.setFont(font1);

		font2.setFontHeightInPoints((short) 11);
		font2.setFontName("宋体");
		font2.setBold(true); // 粗体
		style2.setFont(font2);

		font3.setFontHeightInPoints((short) 15);
		font3.setFontName("宋体");
		font3.setBold(true);
		style3.setFont(font3);

		// 设置格式
		style1_LEFT.setAlignment(HorizontalAlignment.LEFT);// 水平左
		style1_LEFT.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直举重
		style1_LEFT.setBorderBottom(BorderStyle.THIN);// 下边框
		style1_LEFT.setBorderLeft(BorderStyle.THIN);// 左边框
		style1_LEFT.setBorderRight(BorderStyle.THIN);// 右边框
		style1_LEFT.setBorderTop(BorderStyle.THIN);// 上边框
		style1_LEFT.setWrapText(true);// 自动换行

		style1_CENTER.setAlignment(HorizontalAlignment.CENTER);// 水平居中
		style1_CENTER.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直举重
		style1_CENTER.setBorderBottom(BorderStyle.THIN);// 下边框
		style1_CENTER.setBorderLeft(BorderStyle.THIN);// 左边框
		style1_CENTER.setBorderRight(BorderStyle.THIN);// 右边框
		style1_CENTER.setBorderTop(BorderStyle.THIN);// 上边框
		style1_CENTER.setWrapText(true);// 自动换行

		style1_RIGHT.setAlignment(HorizontalAlignment.RIGHT);// 水平居右
		style1_RIGHT.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直举重
		style1_RIGHT.setBorderBottom(BorderStyle.THIN);// 下边框
		style1_RIGHT.setBorderLeft(BorderStyle.THIN);// 左边框
		style1_RIGHT.setBorderRight(BorderStyle.THIN);// 右边框
		style1_RIGHT.setBorderTop(BorderStyle.THIN);// 上边框
		style1_RIGHT.setWrapText(true);// 自动换行

		style2.setAlignment(HorizontalAlignment.CENTER);// 水平居中
		style2.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直举重
		style2.setBorderBottom(BorderStyle.THIN);// 下边框
		style2.setBorderLeft(BorderStyle.THIN);// 左边框
		style2.setBorderRight(BorderStyle.THIN);// 右边框
		style2.setBorderTop(BorderStyle.THIN);// 上边框
		style2.setFillForegroundColor(IndexedColors.GOLD.getIndex());
		style2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		style2.setWrapText(true);// 自动换行

		style3.setAlignment(HorizontalAlignment.CENTER);// 水平居中
		style3.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直举重
		style3.setWrapText(true);// 自动换行
		/******* 以下开始填充数据 ************/
		int rownum = 0;// 行序号
		Sheet sheet = null;// 创建表单
		Row row = null;// 行
		Cell cell = null;// 单元格

		// 创建一个Sheet
		sheet = excelWorkBook.createSheet("sheet");
		sheet.setDefaultColumnWidth(20);
		excelWorkBook.setSheetName(0, "SHEET1");
		// 为列设置宽度
		if (columnWidths != null && columnWidths.length == columnNum) {
			for (int j = 0; j < columnNum; j++) {
				sheet.setColumnWidth(j, Integer.parseInt(columnWidths[j] == "" ? "4000" : columnWidths[j]));
			}
		} else {
			for (int j = 0; j < columnNum; j++) {
				sheet.setColumnWidth(j, 4000);
			}
		}

		//如果存在大标题，就设置大的标题，如果不存在就不设置
		if(StringUtil.isNotEmpty(titleCn)){
			// 写入数据
			row = sheet.createRow(rownum++);
			row.setHeight((short) 800);
			cell = row.createCell(0);
			// 大的标题
			cell.setCellType(CellType.STRING);
			cell.setCellValue(titleCn);
			cell.setCellStyle(style3);
			// 合并单元格
			sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, (columnNum - 1)));
		}

		// 标题栏
		for (int i = 0; i < columnName.length; i++) {
			row = sheet.createRow(rownum);
			row.setHeight((short) 500);
			for (int j = 0; j < columnNum; j++) {
				if (columnName[i][j].equals("-")) {
					sheet.addMergedRegion(new CellRangeAddress(rownum, j - 1, rownum, j));
				}
				if (columnName[i][j].equals("|")) {
					sheet.addMergedRegion(new CellRangeAddress(rownum - 1, j, rownum, j));
				}
				cell = row.createCell(j);
				cell.setCellType(CellType.STRING);
				cell.setCellValue(columnName[i][j]);
				cell.setCellStyle(style2);
			}
			rownum++;
		}

		// 数据部分
		try {
			String contentTemp = null;
			int size = dataList.size();
			for (int i = 0; i < size; i++) {
				row = sheet.createRow(rownum);// 创建行
				Object kColl = dataList.get(i);
				for (int j = 0; j < columnNum; j++) {
					// 取出数据域的值，如果含有‘|’，则处理每一个数据域（一个列里含有多个数据域）
					String colsFiledName[] = StringUtil.splitStringToString(columnFiledName[j], "\\|");
					String colsStyles[] = StringUtil.splitStringToString(columnStyles[j], "\\|");
					contentTemp = "";
					String gap = "";
					if (colsFiledName.length > 1)
						gap = " ";
					for (int k = 0; k < colsFiledName.length; k++) {

						// 如果从当前的循环kColl取不到值，则从context从去取值 add 2007-11-22
						String valueStr = "";

						// 如果是顺序号，则不需要获取数据域值
						if (!"0".equals(colsStyles[k].trim())) {
							try {
								if (colsFiledName[k].indexOf("#") != -1) {
									String speName[] = StringUtil.splitStringToString(colsFiledName[k], "#");
									if (speName.length > 0) {
										for (int m = 0; m < speName.length; m++) {
											try {
												valueStr += ReflectionUtils.getFieldValue(kColl, speName[m]).toString();
											} catch (Exception ex) {
												valueStr += ReflectionUtils.getFieldValue(kColl, speName[m]).toString();
											}
										}
									}
								} else {
									valueStr = ReflectionUtils.getFieldValue(kColl, colsFiledName[k]).toString();
								}
							} catch (Exception oe) {
								if(ReflectionUtils.getFieldValue(kColl, colsFiledName[k])!=null){
									valueStr = ReflectionUtils.getFieldValue(kColl, colsFiledName[k]).toString();
								}else{
									valueStr = "";
								}
							}
						}
						// 根据列的类型分别做处理
						// 0:顺序号 1：文本 2：应用参数 3：金额（实际金额） 4:金额（不处理）5:日期 6：时间
						// 7:利率 8:错误信息
						// 如果是5类型，则需要输入参数
						if(Integer.parseInt(colsStyles[k]) == 0) {
							contentTemp += String.valueOf(i + 1) + gap;
						}else {
							contentTemp += formatCellValue(valueStr, Integer.parseInt(colsStyles[k])) + gap;
						}
					}
					row.setHeight((short) 400);
					cell = row.createCell(j);
					cell.setCellType(CellType.STRING);
					// 设置样式
					switch (Integer.parseInt(columnAlignStyles[j])) {
					case 1: // 左
						styleTemp = style1_LEFT;
						break;
					case 2: // 中
						styleTemp = style1_CENTER;
						break;
					case 3: // 右
						styleTemp = style1_RIGHT;
						break;
					default:
						styleTemp = style1_RIGHT;
						break;
					}

					cell.setCellStyle(styleTemp);
					//columnAlignStyles = 64代表长整型
					if(Integer.parseInt(columnAlignStyles[j]) == JavaTypeEnum.LONG.getId())
						if(StringUtil.isNotEmpty(contentTemp) &&
								RegexUtils.checkDigit(contentTemp)){
							cell.setCellValue(Long.parseLong(contentTemp));
						}
						else{
							cell.setCellValue(contentTemp);
						}
					else{
						cell.setCellValue(contentTemp);
					}
				}
				rownum++;
			}
		} catch (Exception e) {
			LOGGER.error("数据解析异常啦啦啦",e);
			throw new ServiceException(e);
		}
		// 处理文件名
		if (excelFileName.trim() == "") {
			excelFileName = "小药药数据下载";
		}
		excelFileName += "_" + DateUtil.date2String(new Date(), DATE_FORMAT) + ".xlsx";
		ServletOutputStream output = null;
		try {
			excelFileName = new String(excelFileName.getBytes("UTF-8"), "ISO-8859-1"); //文件名转码
			// 保存文件
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
			response.setHeader("Content-Disposition","attachment;filename="+excelFileName);
			response.setHeader("Pragma","public");
			response.setHeader("Cache-Control","max-age=0");
			output = response.getOutputStream();
			excelWorkBook.write(output);
		} catch (UnsupportedEncodingException e) {
			LOGGER.error("数据解析异常",e);
			throw new ServiceException(e);
		} catch (IOException e) {
			LOGGER.error("数据解析异常",e);
			throw new ServiceException(e);
		} finally {
			if(null != output) {
				try {
					output.close();
				} catch (IOException e) {
					LOGGER.error("输出流关闭异常",e);
				}
			}
		}
		return true;
	}
    private static final String DATE_FORMAT = "yyyy-MM-dd";


	// param path 文件夹完整绝对路径
	// 删除指定文件夹下所有文件过滤 是不是文件且文件夹是今天之前的
	public static boolean delAllFile(String path) {
		boolean flag = false;
		File file = new File(path);
		if (!file.exists()) {
			return flag;
		}
		if (!file.isDirectory()) {
			return flag;
		}
		String[] tempList = file.list();
		File temp = null;
		for (int i = 0; i < tempList.length; i++) {
			if (path.endsWith(File.separator)) {
				temp = new File(path + tempList[i]);
			} else {
				temp = new File(path + File.separator + tempList[i]);
			}
			if (temp.isFile()) {
				if (temp.getPath().indexOf("export_") > -1) {
					temp.delete();
				}else {
					continue;
				}
			}
			if (temp.isDirectory()) {
				String dateTime = new SimpleDateFormat(DATE_FORMAT).format(new Date());
				if (!dateTime.equals(tempList[i])) {
					delAllFile(path + "/" + tempList[i]);// 先删除文件夹里面的文件
					delFolder(path + "/" + tempList[i]);// 再删除空文件夹
					flag = true;
				}
			}
		}
		return flag;
	}
	// 删除文件夹
	// param folderPath 文件夹完整绝对路径
	public static void delFolder(String folderPath) {
		try {
			delAllFile(folderPath); // 删除完里面所有内容
			String filePath = folderPath.toString();
			File myFilePath = new File(filePath);
			myFilePath.delete(); // 删除空文件夹
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	public static List<String> excelToList(InputStream inputStream, String fileName,String splitFlag,int beginRow) throws ServiceException {
		if(StringUtils.isEmpty(splitFlag)){
			splitFlag = "";
		}
		List<String> content = new ArrayList<String>();
		String str = "";
		Workbook wb = null;
		try {
			if (ExcelCommonUtil.isExcel2003(fileName)) {
				wb = new HSSFWorkbook(inputStream);
			} else if (ExcelCommonUtil.isExcel2007(fileName)) {
				wb = new XSSFWorkbook(inputStream);
			}
		} catch (IOException e) {
			LOGGER.error("excel转换异常", e);
			throw new ServiceException(e);
		}
		Sheet sheet = wb.getSheetAt(0);
		// 得到总行数
		int rowNum = sheet.getLastRowNum();
		Row row = sheet.getRow(0);
		int colNum = row.getPhysicalNumberOfCells();
		// 正文内容应该从第二行开始,第一行为表头的标题
		for (int i = beginRow; i <= rowNum; i++) {
			row = sheet.getRow(i);
			// 处理存在空白行问题
			if (row == null) {
				break;
			}
			int j = 0;
			while (j < colNum) {
				str += ExcelCommonUtil.getCellFormatValue(row.getCell(j)).trim() + splitFlag;
				j++;
			}
			content.add(str);
			str = "";
		}
		return content;
	}






	/**
	 * 格式化单元格数据
	 * @Title: formatCellValue
	 * @param cellValue	单元格值
	 * @param type 0:顺序号 1：文本 2：应用参数 3：金额（实际金额） 4:金额（不处理）5:日期 6：时间 7:利率  8:错误信息 9:货币制
	 * @return
	 * String
	 * <AUTHOR>
	 * @date 2017年9月27日 下午4:59:54
	 */
	public static String formatCellValue(String cellValue,int type) {
		String returnValue = "";
		// 0:顺序号 1：文本 2:布尔 3：金额（实际金额） 4:金额（不处理）5:日期 6：时间 7:利率 9:货币制
		switch (type) {
		case 1: // 文本
			returnValue = cellValue;
			break;
		case 2:// 布尔
			if (StringUtil.isNotEmpty(cellValue) && (!"0".equals(cellValue) || "TRUE".equalsIgnoreCase(cellValue)) && !"FALSE".equalsIgnoreCase(cellValue)) {
				returnValue = "是";
			} else {
				returnValue = "否";
			}
			break;
		case 3:// 金额
			returnValue = cellValue;
			break;
		case 4:// 金额
			returnValue = cellValue;
			break;
		case 5: // 日期
			returnValue = DateUtil.date2String(DateUtil.string2Date(cellValue, "EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH), DATE_FORMAT);
			break;
		case 6: // 时间
			returnValue = DateUtil.date2String(DateUtil.string2Date(cellValue, "EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH), "yyyy-MM-dd HH:mm:ss");
			break;
		case 7: // 利率
			returnValue = String.valueOf(Double.parseDouble(cellValue) / (double) 10000000);
			break;
		case 8:// 暂留
			returnValue = cellValue;
			break;
		case 9:// 金额 处理 xxxx.xx这中输入，输出x,xxx.xx
			if (cellValue != null && cellValue.startsWith("."))
				cellValue = "0" + cellValue;
			returnValue += cellValue;
			break;
			default:
		}
		return returnValue;
	}
}
