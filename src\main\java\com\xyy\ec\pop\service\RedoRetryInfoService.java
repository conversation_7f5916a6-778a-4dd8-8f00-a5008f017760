package com.xyy.ec.pop.service;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.vo.RedoRetryInfoVo;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/09/18/10:49
 * @Description:
 */
public interface RedoRetryInfoService {
    RedoRetryInfoVo getRetryTaskInfoById(Long id) throws ServiceException;

    Boolean updateRetryTask(RedoRetryInfoVo retryInfo) throws ServiceException;
}
