package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.export.param.CheckPaymentProveParam;
import com.xyy.ec.pop.server.api.merchant.api.admin.CheckCorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.CheckPaymentProveApi;
import com.xyy.ec.pop.server.api.merchant.dto.CheckCorporationDto;
import com.xyy.ec.pop.server.api.merchant.dto.CheckPaymentProveModelDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class CheckPaymentProveRemoteAdapter {
    @Reference
    private CheckPaymentProveApi checkPaymentProveApi;

    public PageInfo<CheckPaymentProveModelDto> selectPageList(CheckPaymentProveParam proveParam){
        try {
            log.info("#CheckPaymentProveRemoteAdapter.selectPageList#proveParam:{}", proveParam);
            ApiRPCResult<PageInfo<CheckPaymentProveModelDto>> result = checkPaymentProveApi.selectPageList(proveParam);
            log.info("#CheckPaymentProveRemoteAdapter.selectPageList#proveParam:{} return:{}",  JSON.toJSONString(proveParam),JSON.toJSONString(result));

            return result.isSuccess()? result.getData():new PageInfo<>();
        } catch (Exception e) {
            log.error("#CheckPaymentProveRemoteAdapter.selectPageList#proveParam:{}", proveParam, e);
            return new PageInfo<>();
        }
    }
}
