package com.xyy.ec.pop.service;

import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.dto.ProductAuditDTO;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDetailDto;
import com.xyy.ec.pop.vo.*;

import java.util.List;

/**
 * @version v1
 * @Description 商品服务
 * <AUTHOR>
 */
public interface ProductService {
    List<ZTree> getSkuCategoryTree(Long skuCategoryId, String orgId);

    void afterProductReport(String barcode, String standId) throws ServiceException;

    BatchUpdateResultVo batchUpdate(String user, List<SkuBatchUpdateVo> vos) throws ServiceException;
    void skuAuditingByBarcode(ProductAuditDTO productAuditDTO, SysUser user) throws ServiceException;

    ProductSkuVo getProductInfo(String orgId, String barcode);

    /**
     * 查询ec数据
     * @param OrgId
     * @param id
     * @return
     */
    ProductSkuVo getEcProductInfo(String OrgId, Long id);

    /**
     * 校验审核内容是否正确
     * @param productAuditDTO
     * @param skuDetailDto
     * @return
     */
    String validAudit(ProductAuditDTO productAuditDTO, PopSkuDetailDto skuDetailDto);

    /**
     * ec商品审核校验
     * @param productVo
     * @return
     */
    String ecValidAudit(ProductSkuVo productVo);

    /**
     * 根据商品编码获取商品预览详情
     * @param orgId
     * @param barcode
     * @return
     */
    public ProductEcVo getProductPreviewInfo(String orgId, String barcode) throws ServiceException;

    void fillPriceMerchantGroup(List<PopSkuDetailDto> list, Page<ProductListVo> ecSkuList);
}
