package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家资金账户审核表
 *
 * <AUTHOR>
 * @date 2024/9/13
 */
@Data
public class PopMerchantFundAuditVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 商户编码
     */
    private String orgId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 商户注册省份
     */
    private String provName;

    /**
     * 流水单号
     */
    private String flowOrderNumber;

    /**
     * 资金性质状态（1:保证金，2：营销服务额度）
     */
    private Integer fundPropertyStatus;
    /**
     * 资金变动类型 （10:充值保证金,11:多次退款被拒,产生额外赔付,12:退还保证金 20:售后额外赔偿,21:小额打款,22:小额打款-退回,23:购买额度, 24：退营销服务额度，30：扣罚）
     */
    private Integer fundChangeType;

    /**
     * 资金变动状态1：增加 2：扣减
     */
    private Integer fundChangeStatus;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单 id
     */
    private Integer orderId;

    /**
     * 退款单号
     */
    private String refundOrderNo;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 额度标准
     */
    private BigDecimal standardLimit;

    /**
     * 实际到账金额
     */
    private BigDecimal actualReceivedAmount;

    /**
     * 扣除比例
     */
    private BigDecimal deductionRatio;

    /**
     * 实际到账时间
     */
    private Date actualReceivedTime;

    /**
     * 支付方式（1：线下，2：线上）
     */
    private Integer paymentMethod;

    /**
     * 提交人
     */
    private String submitter;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核状态 （1：待审核 2:审核通过 3:已取消 4:审核驳回）
     */
    private Integer auditStatus;

    /**
     * 审核留言
     */
    private String auditComment;

    /**
     * 打款证明（多个 url
     */
    private String paymentProof;

    /**
     * 凭证（多个 url）
     */
    private String voucher;

    /**
     * 收据
     */
    private String receipt;

    /**
     * 是否开发票 （1：是，2：否）
     */
    private Integer isInvoice;

    /**
     * 是否首充 （1：是，2：否）
     */
    private Integer isFirstCharge;

    /**
     * 发票信息（url）
     */
    private String invoiceInformation;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
