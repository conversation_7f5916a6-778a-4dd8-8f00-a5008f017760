package com.xyy.ec.pop.utils.excel.style;

import cn.afterturn.easypoi.excel.export.styler.ExcelExportStylerDefaultImpl;
import cn.afterturn.easypoi.excel.export.styler.IExcelExportStyler;
import org.apache.poi.ss.usermodel.*;

/**
 * excel导出通用样式
 * Created by <PERSON><PERSON><PERSON> on 2019/11/1.
 */
public class ExcelExportCommonStyle extends ExcelExportStylerDefaultImpl implements IExcelExportStyler{

    public ExcelExportCommonStyle(Workbook workbook) {
        super(workbook);
    }

    @Override
    public CellStyle getHeaderStyle(short headerColor) {
        CellStyle style2 = this.workbook.createCellStyle();
        Font font2 = this.workbook.createFont();
        font2.setFontHeightInPoints((short) 11);
        font2.setFontName("宋体");
        font2.setBold(true); // 粗体
        style2.setFont(font2);
        style2.setAlignment(HorizontalAlignment.CENTER);
        style2.setVerticalAlignment(VerticalAlignment.CENTER);
        style2.setBorderBottom(BorderStyle.THIN);
        style2.setBorderLeft(BorderStyle.THIN);
        style2.setBorderRight(BorderStyle.THIN);
        style2.setBorderTop(BorderStyle.THIN);
        style2.setFillForegroundColor(IndexedColors.GOLD.getIndex());
        style2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 自动换行
        style2.setWrapText(true);
        return style2;
    }

    @Override
    public CellStyle getTitleStyle(short color) {
        CellStyle style3 = this.workbook.createCellStyle();
        Font font3 = this.workbook.createFont();
        font3.setFontHeightInPoints((short) 15);
        font3.setFontName("宋体");
        font3.setBold(true);
        style3.setFont(font3);
        // 水平居中
        style3.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        style3.setVerticalAlignment(VerticalAlignment.CENTER);
        // 自动换行
        style3.setWrapText(true);
        return style3;
    }

    @Override
    public CellStyle stringSeptailStyle(Workbook workbook, boolean isWarp) {
        CellStyle style1_LEFT = workbook.createCellStyle();
        // 设置字体
        Font font1 = workbook.createFont();
        font1.setFontHeightInPoints((short) 11);
        font1.setFontName("宋体");
        style1_LEFT.setFont(font1);
        // 设置格式
        style1_LEFT.setAlignment(HorizontalAlignment.CENTER);
        style1_LEFT.setVerticalAlignment(VerticalAlignment.CENTER);
        style1_LEFT.setBorderBottom(BorderStyle.THIN);
        style1_LEFT.setBorderLeft(BorderStyle.THIN);
        style1_LEFT.setBorderRight(BorderStyle.THIN);
        style1_LEFT.setBorderTop(BorderStyle.THIN);
        // 自动换行
        style1_LEFT.setWrapText(true);
        return style1_LEFT;
    }
}
