package com.xyy.ec.pop.vo;

import lombok.Data;

import java.util.List;

/**
 * @Description 商品佣金设置 列表查询
 */
@Data
public class ProductCommissionQueryVo {
    /**
     * 商户编码
     */
    private String orgId;
    /**
     * 商户名称
     */
    private String orgName;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 商品编码
     */
    private String barcode;
    /**
     * 展示名称
     */
    private String showName;
    /**
     * 标准库ID
     */
    private String standardProductId;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 商品状态
     */
    private Integer status;
    /**
     * 商品类型，参考 ActivityTypeEnum
     */
    private Integer activityType;
    /**
     * 机构id列表 通过店铺名，商家名转换而来
     */
    private List<String> orgIds;
    private Integer pageSize;
    private Integer pageNum;
    /**
     * csuid
     */
    private Long csuid;
}
