package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.product.api.seller.PopSkuSaleTimeApi;
import com.xyy.ec.pop.server.api.product.api.seller.PopSkuSaleTimeConfigApi;
import com.xyy.ec.pop.server.api.product.dto.PopSkuSaleTimeDto;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Description 商品销售时间
 */
@Slf4j
@Component
public class SkuSaleTimeRemote {
    @Reference
    private PopSkuSaleTimeConfigApi popSkuSaleTimeConfigApi;
    @Reference
    private PopSkuSaleTimeApi popSkuSaleTimeApi;

    public PopSkuSaleTimeDto skuSaleTime(String barcode) {
        try {
            log.info("SkuSaleTimeRemote.skuSaleTime#barcode:{}", barcode);
            ApiRPCResult<Map<String, PopSkuSaleTimeDto>> result = popSkuSaleTimeApi.queryByBarcodes(null, Lists.newArrayList(barcode));
            log.info("SkuSaleTimeRemote.skuSaleTime#barcode:{} return {}", barcode, JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData().get(barcode);
            }

        } catch (Exception e) {
            log.error("SkuSaleTimeRemote.skuSaleTime#barcode:{} 异常", barcode, e);
        }
        return null;
    }

    public Boolean reSetSkuTimeByConfig(Long configId, String updateName) {
        try {
            log.info("SkuSaleTimeRemote.reSetSkuTimeByConfig#configId:{},updateName:{}", configId, JSON.toJSONString(updateName));
            ApiRPCResult<Boolean> result = popSkuSaleTimeConfigApi.reSetSkuTimeByConfig(configId, updateName);
            log.info("SkuSaleTimeRemote.reSetSkuTimeByConfig#configId:{},updateName:{} return {}",configId, JSON.toJSONString(updateName), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            if(result.getCode()!= ApiResultCodeEum.SYSTEM_ERROR.getCode()){
                throw new XyyEcPopException(result.getErrMsg());
            }
        }catch (XyyEcPopException e){
            throw e;
        }catch (Exception e) {
            log.error("SkuSaleTimeRemote.reSetSkuTimeByConfig#configId:{},updateName:{} 异常", configId, JSON.toJSONString(updateName), e);
        }
        return false;
    }

    public Boolean reSetSkuTime(List<String> barcodes) {
        try {
            log.info("SkuSaleTimeRemote.reSetSkuTime#barcodes:{}", JSON.toJSONString(barcodes));
            ApiRPCResult<Boolean> result = popSkuSaleTimeApi.reSyncTimeToEc(barcodes);
            log.info("SkuSaleTimeRemote.reSetSkuTime#barcodes:{} return {}", JSON.toJSONString(barcodes), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            if(result.getCode()!= ApiResultCodeEum.SYSTEM_ERROR.getCode()){
                throw new XyyEcPopException(result.getErrMsg());
            }
        }catch (XyyEcPopException e){
            throw e;
        } catch (Exception e) {
            log.error("SkuSaleTimeRemote.reSetSkuTime#barcodes:{} 异常", JSON.toJSONString(barcodes), e);
        }
        return false;
    }
}
