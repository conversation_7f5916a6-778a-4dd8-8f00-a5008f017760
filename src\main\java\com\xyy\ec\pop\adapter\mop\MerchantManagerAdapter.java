package com.xyy.ec.pop.adapter.mop;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pop.dto.mop.MopMerchantBatchBindTemplateDTO;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.utils.CollectionUtil;
import com.xyy.ec.pop.utils.easyexcel.EasyExcelUtil;
import com.xyy.ec.pop.utils.mop.MopDataFillerUtils;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.common.enumerate.MopMerchantBindTypeEnum;
import com.xyy.pop.mop.api.remote.MeSysUserRemote;
import com.xyy.pop.mop.api.remote.MerchantBindPoiRemote;
import com.xyy.pop.mop.api.remote.MerchantManagerRemote;
import com.xyy.pop.mop.api.remote.parameter.MeSysUserParame;
import com.xyy.pop.mop.api.remote.parameter.MerchantBatchBindParameter;
import com.xyy.pop.mop.api.remote.parameter.MerchantBindParameter;
import com.xyy.pop.mop.api.remote.parameter.MerchantErpJointParameter;
import com.xyy.pop.mop.api.remote.parameter.MerchantLocationParameter;
import com.xyy.pop.mop.api.remote.parameter.MerchantManagerPageParameter;
import com.xyy.pop.mop.api.remote.parameter.MerchantPoiParameter;
import com.xyy.pop.mop.api.remote.parameter.MerchantSuccessEnterTimeParameter;
import com.xyy.pop.mop.api.remote.result.MeSysUserDto;
import com.xyy.pop.mop.api.remote.result.MerchantBindPoiDTO;
import com.xyy.pop.mop.api.remote.result.MerchantManagerPageDTO;
import com.xyy.scm.constant.entity.Result;
import com.xyy.scm.constant.entity.pagination.Paging;
import com.xyy.scm.constant.foundation.GeneralEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 商户管理
 *
 * @author: duHao
 * @since: 11:38 2024/12/9
 */
@Slf4j
@Component
public class MerchantManagerAdapter implements MopBaseAdapter {

    @Reference(version = "1.0.0")
    private MerchantManagerRemote merchantManagerRemote;
    @Reference(version = "1.0.0")
    private MerchantBindPoiRemote merchantBindPoiRemote;
    @Reference(version = "1.0.0")
    private MeSysUserRemote meSysUserRemote;

    /**
     * 绑定招商/运用用户查询
     *
     * @param param
     * @return
     */
    public ResponseVo<List<MeSysUserDto>> getUserList(MeSysUserParame param) {
        return to(() -> meSysUserRemote.getUserList(param));
    }


    public String queryMerchantName(String orgId) {
        Result<String> companyNameByOrgId = merchantManagerRemote.findCompanyNameByOrgId(orgId);
        if (companyNameByOrgId.isFailure()) {
            return null;
        }
        return companyNameByOrgId.getResult();
    }

    /**
     * 换绑门店查询
     *
     * @param param
     * @return
     */
    public ResponseVo<List<MerchantBindPoiDTO>> getBindPoiList(MerchantPoiParameter param) {
        return to(() -> merchantBindPoiRemote.getBindPoiByParam(param));
    }

    /**
     * 商户管理-列表
     *
     * @param param
     * @return
     */
    public ResponseVo<Paging<MerchantManagerPageDTO>> queryMerchantPageByParam(MerchantManagerPageParameter param) {
        return to(() -> merchantManagerRemote.queryMerchantPageByParam(param));
    }

    /**
     * 商户绑定招商
     *
     * @param param
     * @return
     */
    public ResponseVo bindZs(MerchantBindParameter param) {
        param.setMerchantBindType(MopMerchantBindTypeEnum.ZS.getCode());
        return to(() -> merchantManagerRemote.bindZsAccount(param));
    }

    /**
     * 商户绑定运营
     *
     * @param param
     * @return
     */
    public ResponseVo bindYy(MerchantBindParameter param) {
        param.setMerchantBindType(MopMerchantBindTypeEnum.YY.getCode());
        return to(() -> merchantManagerRemote.bindYyAccount(param));
    }

    /**
     * 商户换绑门店
     *
     * @param param
     * @return
     */
    public ResponseVo bindPoi(MerchantBindParameter param) {
        param.setMerchantBindType(MopMerchantBindTypeEnum.POI.getCode());
        return to(() -> merchantManagerRemote.bindPoi(param));
    }

    /**
     * 设置店铺成功入驻时间
     *
     * @param param
     * @return
     */
    public ResponseVo setMerchantSuccessEnterTime(MerchantSuccessEnterTimeParameter param) {
        return to(() -> merchantManagerRemote.setMerchantSuccessEnterTime(param));
    }

    /**
     * ERP暂停对接
     *
     * @param param
     * @return
     */
    public ResponseVo erpJointPause(MerchantErpJointParameter param) {
        Result result = merchantManagerRemote.erpJointPause(param);
        return to(() -> result);
    }

    /**
     * 修改经纬度信息
     *
     * @param param
     * @return
     */
    public ResponseVo updateLocation(MerchantLocationParameter param) {
        Result result = merchantManagerRemote.updateLocation(param);
        return to(() -> result);
    }


    /**
     * ERP对接完成
     *
     * @param param
     * @return
     */
    public ResponseVo erpJointFinish(MerchantErpJointParameter param) {
        Result result = merchantManagerRemote.erpJointFinish(param);
        return to(() -> result);
    }

    /**
     * Excel批量绑定招商
     *
     * @param file
     * @return
     */
    public ResponseVo batchBindZs(MultipartFile file, SysUser getUser) {
        return batchBind(file, MopMerchantBindTypeEnum.ZS.getCode(), getUser);
    }

    /**
     * Excel批量绑定运营
     *
     * @param file
     * @return
     */
    public ResponseVo batchBindYy(MultipartFile file, SysUser getUser) {
        return batchBind(file, MopMerchantBindTypeEnum.YY.getCode(), getUser);
    }

    /**
     * Excel批量换绑门店
     *
     * @param file
     * @return
     */
    public ResponseVo batchBindPoi(MultipartFile file, SysUser getUser) {
        return batchBind(file, MopMerchantBindTypeEnum.POI.getCode(), getUser);
    }

    /**
     * 批量绑定招商/运营/poi
     *
     * @param file
     * @param bindType
     * @return
     */
    private ResponseVo batchBind(MultipartFile file, String bindType, SysUser getUser) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName) || (!fileName.toLowerCase().endsWith(".xls") && !fileName.toLowerCase().endsWith(".xlsx"))) {
            return ResponseVo.errCodeRest("请选择下载的excel模版进行操作!");
        }
        try {
            List<MopMerchantBatchBindTemplateDTO> templateDTOList = EasyExcelUtil.readExcel(file.getInputStream(), MopMerchantBatchBindTemplateDTO.class);
            if (CollectionUtil.isEmpty(templateDTOList)) {
                return ResponseVo.errCodeRest("导入数据解析为空!");
            }

            List<MerchantBindParameter> bindList = new ArrayList<>();
            for (MopMerchantBatchBindTemplateDTO templateDTO : templateDTOList) {
                MerchantBindParameter bindParam = new MerchantBindParameter();
                bindParam.setMerchantBindType(bindType);
                bindParam.setBindPoiIdStr(templateDTO.getPoiId());
                bindParam.setOrgId(templateDTO.getOrgId());

                if (StringUtils.equals(MopMerchantBindTypeEnum.ZS.getCode(), bindType)) {
                    bindParam.setBindZsAccountId(templateDTO.getBindZsAccountId());
                }
                if (StringUtils.equals(MopMerchantBindTypeEnum.YY.getCode(), bindType)) {
                    bindParam.setBindYyAccountId(templateDTO.getBindYyAccountId());
                }

                bindList.add(bindParam);
            }

            MerchantBatchBindParameter batchBindParam = new MerchantBatchBindParameter();
            batchBindParam.setMerchantBindType(bindType);
            batchBindParam.setBindList(bindList);
            MopDataFillerUtils.fillData(getUser, batchBindParam,true);
            if (StringUtils.equals(MopMerchantBindTypeEnum.ZS.getCode(), bindType)) {
                return to(() -> merchantManagerRemote.batchBindZsAccount(batchBindParam));
            }
            if (StringUtils.equals(MopMerchantBindTypeEnum.YY.getCode(), bindType)) {
                return to(() -> merchantManagerRemote.batchBindYyAccount(batchBindParam));
            }
            if (StringUtils.equals(MopMerchantBindTypeEnum.POI.getCode(), bindType)) {
                return to(() -> merchantManagerRemote.batchBindPoi(batchBindParam));
            }
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("MerchantManagerController.batchBind {} error:", bindType, e);
            return ResponseVo.errRest(String.format("批量%s异常!", GeneralEnum.of(bindType, MopMerchantBindTypeEnum.class).getMsg()));
        }
    }


}
