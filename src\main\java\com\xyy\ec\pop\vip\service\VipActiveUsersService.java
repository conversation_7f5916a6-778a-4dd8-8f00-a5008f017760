package com.xyy.ec.pop.vip.service;

import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.vip.vo.VipActiveUserVo;
import com.xyy.ec.pop.vip.vo.VipActiveUsersQueryVo;
import com.xyy.ec.pop.vo.ResponseVo;

import java.util.List;

public interface VipActiveUsersService {
    ResponseVo<Page<VipActiveUserVo>> pageVipActiveUsers(VipActiveUsersQueryVo vipActiveUsersQuery);

    List<VipActiveUserVo> getVipActiveUsers(VipActiveUsersQueryVo vipActiveUsersQuery);

    ResponseVo<Boolean> exportActiveUsers(VipActiveUsersQueryVo vipActiveUsersQuery, SysUser user);
}
