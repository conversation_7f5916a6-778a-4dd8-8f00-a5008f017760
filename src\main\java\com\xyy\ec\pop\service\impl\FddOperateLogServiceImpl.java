package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pop.server.api.fdd.api.FddOperateLogApi;
import com.xyy.ec.pop.server.api.fdd.dto.TbXyyPopFddOperateLogDTO;
import com.xyy.ec.pop.service.FddOperateLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.List;

@Service
@Slf4j
public class FddOperateLogServiceImpl implements FddOperateLogService {

    @Reference(version = "1.0.0")
    private FddOperateLogApi fddOperateLogApi;


    @Override
    public List<TbXyyPopFddOperateLogDTO> operateLogList(Integer relationId) {
        return fddOperateLogApi.operateLogList(relationId);
    }
}
