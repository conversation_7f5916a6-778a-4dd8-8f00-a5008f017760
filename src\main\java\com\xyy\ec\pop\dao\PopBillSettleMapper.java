package com.xyy.ec.pop.dao;

import com.xyy.ec.pop.po.PopBillSettlePo;
import com.xyy.ec.pop.vo.settle.PopBillSettleStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface PopBillSettleMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PopBillSettlePo record);

    int insertSelective(PopBillSettlePo record);

    PopBillSettlePo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PopBillSettlePo record);

    int updateByPrimaryKey(PopBillSettlePo record);

    List<PopBillSettlePo> queryPopBillSettleList(@Param("popBillSettle") PopBillSettleVo popBillSettleVo, @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    Long queryPopBillSettleListCount(@Param("popBillSettle") PopBillSettleVo popBillSettleVo);

    PopBillSettleStatisVo queryPopBillSettleStatis(@Param("popBillSettle") PopBillSettleVo popBillSettleVo);

    List<PopBillSettlePo> selectByBusinessNos(@Param("businessNos") List<String> businessNos);

    void updateCommissionCalcFlag(@Param("businessNos")List<String> businessNos,@Param("commissionCalcFlag") Integer commissionCalcFlag);

    PopBillSettlePo selectByBusinessNo(@Param("businessNo") String businessNo);
}