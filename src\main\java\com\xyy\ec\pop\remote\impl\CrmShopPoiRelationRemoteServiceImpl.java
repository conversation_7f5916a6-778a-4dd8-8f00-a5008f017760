package com.xyy.ec.pop.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.crm.operation.api.api.CrmShopPoiRelationApi;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.remote.CrmShopPoiRelationRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CrmShopPoiRelationRemoteServiceImpl implements CrmShopPoiRelationRemoteService {

    @Reference(version = "1.0.0", group = "operation_dy")
    private CrmShopPoiRelationApi crmShopPoiRelationApi;

    @Override
    public List<String> listShopCodesByOaId(String oaId) {
        if (StringUtils.isEmpty(oaId)) {
            return Lists.newArrayList();
        }
        try {
            Long oaIdLong = Long.parseLong(oaId);
            ApiRPCResult<List<String>> apiRPCResult = crmShopPoiRelationApi.queryShopRelationByOaId(oaIdLong);
            if (log.isDebugEnabled()) {
                log.debug("listShopCodesByOaId 查询用户关联的店铺编码列表，入参：{}，出参：{}",
                        oaIdLong, JSONObject.toJSONString(apiRPCResult));
            }
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
                log.error("listShopCodesByOaId 查询用户关联的店铺编码列表失败，入参：{}，出参：{}",
                        oaIdLong, JSONObject.toJSONString(apiRPCResult));
                return Lists.newArrayList();
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("listShopCodesByOaId 查询用户关联的店铺编码列表失败，入参：{}", oaId, e);
            return Lists.newArrayList();
        }
    }

}
