package com.xyy.ec.pop.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel("pop商品扩展实体")
public class ProducePopExtendVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty("机构id")
    private String orgId;
    @ApiModelProperty("区域编码")
    private String branchCode;
    @ApiModelProperty("商品id")
    private Long skuId;
    @ApiModelProperty("市场价")
    private BigDecimal marketPrice;
    @ApiModelProperty("是否特长药")
    private Integer isSpecialMedicine;
    @ApiModelProperty("是否专供")
    private Integer isSpecialOffer;
    @ApiModelProperty("是否特许专供")
    private Integer isFranchiseMedicine;
    @ApiModelProperty("特殊药物路径")
    private String specialMedicineUrl;
    @ApiModelProperty("特价商品路径")
    private String specialOfferUrl;
    @ApiModelProperty("代理商路径")
    private String agentUrl;
    @ApiModelProperty("特许药品路径")
    private String franchiseMedicineUrl;
    @ApiModelProperty("采购商类型")
    private String purchaseMerchantType;
    @ApiModelProperty("销售区域")
    private String salesArea;
    @ApiModelProperty("是否销售")
    private Integer isSalesOn;
    @ApiModelProperty("商户ERP编码")
    private String erpCode;
    @ApiModelProperty("副标题")
    private String subtitle;
    @ApiModelProperty("在线时间")
    private Date onlineTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode == null ? null : branchCode.trim();
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public Integer getIsSpecialMedicine() {
        return isSpecialMedicine;
    }

    public void setIsSpecialMedicine(Integer isSpecialMedicine) {
        this.isSpecialMedicine = isSpecialMedicine;
    }

    public Integer getIsSpecialOffer() {
        return isSpecialOffer;
    }

    public void setIsSpecialOffer(Integer isSpecialOffer) {
        this.isSpecialOffer = isSpecialOffer;
    }

    public Integer getIsFranchiseMedicine() {
        return isFranchiseMedicine;
    }

    public void setIsFranchiseMedicine(Integer isFranchiseMedicine) {
        this.isFranchiseMedicine = isFranchiseMedicine;
    }

    public String getSpecialMedicineUrl() {
        return specialMedicineUrl;
    }

    public void setSpecialMedicineUrl(String specialMedicineUrl) {
        this.specialMedicineUrl = specialMedicineUrl == null ? null : specialMedicineUrl.trim();
    }

    public String getSpecialOfferUrl() {
        return specialOfferUrl;
    }

    public void setSpecialOfferUrl(String specialOfferUrl) {
        this.specialOfferUrl = specialOfferUrl == null ? null : specialOfferUrl.trim();
    }

    public String getAgentUrl() {
        return agentUrl;
    }

    public void setAgentUrl(String agentUrl) {
        this.agentUrl = agentUrl == null ? null : agentUrl.trim();
    }

    public String getFranchiseMedicineUrl() {
        return franchiseMedicineUrl;
    }

    public void setFranchiseMedicineUrl(String franchiseMedicineUrl) {
        this.franchiseMedicineUrl = franchiseMedicineUrl == null ? null : franchiseMedicineUrl.trim();
    }

    public String getPurchaseMerchantType() {
        return purchaseMerchantType;
    }

    public void setPurchaseMerchantType(String purchaseMerchantType) {
        this.purchaseMerchantType = purchaseMerchantType == null ? null : purchaseMerchantType.trim();
    }

    public String getSalesArea() {
        return salesArea;
    }

    public void setSalesArea(String salesArea) {
        this.salesArea = salesArea == null ? null : salesArea.trim();
    }

    public Integer getIsSalesOn() {
        return isSalesOn;
    }

    public void setIsSalesOn(Integer isSalesOn) {
        this.isSalesOn = isSalesOn;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle == null ? null : subtitle.trim();
    }

    public Date getOnlineTime() {
        return onlineTime;
    }

    public void setOnlineTime(Date onlineTime) {
        this.onlineTime = onlineTime;
    }
}