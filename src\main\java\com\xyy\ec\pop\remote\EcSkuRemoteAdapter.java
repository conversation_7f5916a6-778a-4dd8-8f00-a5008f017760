package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.product.back.end.ecp.pop.api.PopSkuApi;
import com.xyy.ec.product.back.end.ecp.pop.api.PopStockApi;
import com.xyy.ec.product.back.end.ecp.pop.dto.*;
import com.xyy.ec.product.back.end.ecp.skucategory.dto.CategoryBackEndDTO;
import com.xyy.ec.product.back.end.ecp.stock.SkuStockLogParamDTO;
import com.xyy.ec.product.back.end.ecp.stock.api.StockApi;
import com.xyy.ec.product.back.end.ecp.stock.dto.BPageDto;
import com.xyy.ec.product.back.end.ecp.stock.dto.CsuStockDTO;
import com.xyy.ec.product.back.end.ecp.stock.dto.EcpProductOwnerStockLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @version v1
 * @Description ec商品接口调用
 * <AUTHOR>
 */
@Slf4j
@Component
public class EcSkuRemoteAdapter {
    @Reference
    private PopSkuApi popSkuApi;
    @Reference
    private PopStockApi popStockApi;
    @Reference
    private StockApi ecStockApi;

    @Deprecated
    public boolean batchUpdateSkuStatusByBarcodesAndShopCode(String username, List<String> barcodes, String shopCode, int afterStatus, List<Integer> beforeStatus) {
        try {
            log.info("EcSkuRemoteAdapter.batchUpdateSkuStatusByBarcodesAndShopCode#username:{},barcodes:{},shopCode:{},afterStatus:{},beforeStatus:{}", JSON.toJSONString(username), JSON.toJSONString(barcodes), JSON.toJSONString(shopCode), JSON.toJSONString(afterStatus), JSON.toJSONString(beforeStatus));
            ApiRPCResult<Integer> result = popSkuApi.batchUpdateSkuStatusByBarcodesAndShopCode(username, barcodes, shopCode, afterStatus, beforeStatus);
            log.info("EcSkuRemoteAdapter.batchUpdateSkuStatusByBarcodesAndShopCode#username:{},barcodes:{},shopCode:{},afterStatus:{},beforeStatus:{} return {}", JSON.toJSONString(username), JSON.toJSONString(barcodes), JSON.toJSONString(shopCode), JSON.toJSONString(afterStatus), JSON.toJSONString(beforeStatus), JSON.toJSONString(result));
            return result.isSuccess();
        } catch (Exception e) {
            log.error("EcSkuRemoteAdapter.batchUpdateSkuStatusByBarcodesAndShopCode#username:{},barcodes:{},shopCode:{},afterStatus:{},beforeStatus:{} 异常", JSON.toJSONString(username), JSON.toJSONString(barcodes), JSON.toJSONString(shopCode), JSON.toJSONString(afterStatus), JSON.toJSONString(beforeStatus), e);
            return false;
        }
    }

    public boolean auditingByBarcodeAndShopCode(SkuPopAuditingDTO skuPopAuditingDTO) {
        try {
            log.info("EcSkuRemoteAdapter.auditingByBarcodeAndShopCode#skuPopAuditingDTO:{}", JSON.toJSONString(skuPopAuditingDTO));
            ApiRPCResult<String> result = popSkuApi.auditingByBarcodeAndShopCode(skuPopAuditingDTO);
            log.info("EcSkuRemoteAdapter.auditingByBarcodeAndShopCode#skuPopAuditingDTO:{} return {}", JSON.toJSONString(skuPopAuditingDTO), JSON.toJSONString(result));
            return result.isSuccess();
        } catch (Exception e) {
            log.error("EcSkuRemoteAdapter.auditingByBarcodeAndShopCode#skuPopAuditingDTO:{} 异常", JSON.toJSONString(skuPopAuditingDTO), e);
            return false;
        }

    }

    public BPageDto<EcpProductOwnerStockLogDTO> selectStock(SkuStockLogParamDTO param) {
        try {
            log.info("EcSkuRemoteAdapter.selectStock#param:{}", JSON.toJSONString(param));
            BPageDto<EcpProductOwnerStockLogDTO> page = ecStockApi.selectStockLogByParam(param);
            log.info("EcSkuRemoteAdapter.selectStock#param:{} return {}", JSON.toJSONString(param), JSON.toJSONString(page));
            return page;
        } catch (Exception e) {
            log.error("EcSkuRemoteAdapter.selectStock#param:{} 异常", JSON.toJSONString(param), e);
            return null;
        }
    }

    /**
     * 批量修改商品状态
     *
     * @param userName     修改人名称
     * @param csuIds     商品条码列表
     * @param status       商品状态
     * @param beforeStatus 之前的状态
     * @return
     */
    public boolean batchUpdateSkuStatusBySkuIdList(String userName, List<Long> csuIds, int status, List<Integer> beforeStatus) {
        try {
            log.info("EcProductApiRemote.batchUpdateSkuStatusBySkuIdList#userName:{},csuIds:{},status:{},beforeStatus:{}", userName, JSON.toJSONString(csuIds), status, JSON.toJSONString(beforeStatus));
            PopSkuStatusUpdateDTO popSkuStatusUpdateDTO = new PopSkuStatusUpdateDTO();
            popSkuStatusUpdateDTO.setSkuIdList(csuIds);
            popSkuStatusUpdateDTO.setStatus(status);
            popSkuStatusUpdateDTO.setOldStatus(beforeStatus);
            popSkuStatusUpdateDTO.setOperatorUserName(userName);
            ApiRPCResult<Integer> result = popSkuApi.batchUpdateSkuStatusBySkuIdList(popSkuStatusUpdateDTO);
            log.info("EcProductApiRemote.batchUpdateSkuStatusBySkuIdList#userName:{},csuIds:{},status:{},beforeStatus:{} return {}", userName, JSON.toJSONString(csuIds), status, JSON.toJSONString(beforeStatus), JSON.toJSONString(result));
            return result.isSuccess() ? true : false;
        } catch (Exception e) {
            log.error("EcProductApiRemote.batchUpdateSkuStatusBySkuIdList#userName:{},csuIds:{},status:{},beforeStatus:{} 异常", userName, JSON.toJSONString(csuIds), status, JSON.toJSONString(beforeStatus), e);
            return false;
        }
    }

    public List<CsuStockDTO> getStockBySkuIdList(List<Long> csuIds) {
        try {
            log.info("EcSkuRemoteAdapter.getStockBySkuIdList#csuIds:{}", JSON.toJSONString(csuIds));
            ApiRPCResult<List<CsuStockDTO>> result = popStockApi.getStockBySkuIdList(csuIds);
            if (result.isSuccess()) {
                return result.getData();
            }
            log.info("EcSkuRemoteAdapter.getStockBySkuIdList#csuIds:{} return {}", JSON.toJSONString(csuIds), JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("EcSkuRemoteAdapter.getStockBySkuIdList#csuIds:{} 异常", JSON.toJSONString(csuIds), e);
        }
        return new ArrayList<>();
    }


    /**
     * 根据三级分类查询展示分类
     *
     * @param catId
     * @return
     */
    public CategoryBackEndDTO getCategoryInfoByErpCategoryId(Long catId) {
        try {
            if (catId == null) {
                return null;
            }
            log.info("EcProductApiRemote.getCategoryInfoByErpCategoryId#catId:{}", catId);
            ApiRPCResult<CategoryBackEndDTO> result = popSkuApi.getCategoryInfoByErpCategoryId(catId);
            log.info("EcProductApiRemote.getCategoryInfoByErpCategoryId#catId:{} return {}", catId, JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("EcProductApiRemote.getCategoryInfoByErpCategoryId#catId:{} 异常", catId, e);
            return null;
        }
    }
}
