package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.address.api.BaseRegionBusinessApi;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.address.dto.XyyRegionParams;
import com.xyy.ec.pop.dto.RegionDto;
import com.xyy.ec.pop.helper.RegionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/19 17:08
 * @table
 */
@Slf4j
@Component("adminRegionAdapter")
public class RegionAdapter {

    @Reference(version = "1.0.0")
    private BaseRegionBusinessApi baseRegionBusinessApi;

    public Map<Integer, String> getProvinceCodeNameMap() {
        //查询省份编码和省份名称集合
        XyyRegionParams xyyRegionParams = new XyyRegionParams();
        xyyRegionParams.setParentCode(0);
        try {
            log.info("#RegionAdapter.getProvinceCodeNameMap#xyyRegionParams:{}", JSON.toJSONString(xyyRegionParams));
            List<XyyRegionBusinessDto> regionBusinessDtos = baseRegionBusinessApi.queryRegionByParentCode(xyyRegionParams);
            log.info("#RegionAdapter.getProvinceCodeNameMap#xyyRegionParams:{},regionBusinessDtos:{}", JSON.toJSONString(xyyRegionParams), JSON.toJSONString(regionBusinessDtos));
            //<省份code,省份名称>
            Map<Integer, String> provinceCodeNameMap = regionBusinessDtos.stream().collect(Collectors.toMap(XyyRegionBusinessDto::getAreaCode, XyyRegionBusinessDto::getAreaName));
            log.info("#RegionAdapter.getProvinceCodeNameMap#xyyRegionParams:{},provinceCodeNameMap:{}", JSON.toJSONString(xyyRegionParams), JSON.toJSONString(provinceCodeNameMap));
            return provinceCodeNameMap;
        } catch (Exception e) {
            log.error("#RegionAdapter.getProvinceCodeNameMap#查询省份信息error. xyyRegionParams:{}", JSON.toJSONString(xyyRegionParams), e);
            return Maps.newHashMap();
        }
    }

    public List<RegionDto> getAllProvinces() {
        //查询省份编码和省份名称集合
        XyyRegionParams xyyRegionParams = new XyyRegionParams();
        xyyRegionParams.setParentCode(0);
        try {
            log.info("#RegionAdapter.getProvinceCodeNameMap#xyyRegionParams:{}", JSON.toJSONString(xyyRegionParams));
            List<XyyRegionBusinessDto> regionBusinessDtos = baseRegionBusinessApi.queryRegionByParentCode(xyyRegionParams);
            log.info("#RegionAdapter.getProvinceCodeNameMap#xyyRegionParams:{},regionBusinessDtos:{}", JSON.toJSONString(xyyRegionParams), JSON.toJSONString(regionBusinessDtos));
            List<RegionDto> regionDtos = RegionHelper.buildRegionDtos(regionBusinessDtos);
            return regionDtos;
        } catch (Exception e) {
            log.error("#RegionAdapter.getProvinceCodeNameMap#查询省份信息error. xyyRegionParams:{}", JSON.toJSONString(xyyRegionParams), e);
            return Lists.newArrayList();
        }
    }

}
