package com.xyy.ec.pop.controller.mop;

import com.xyy.ec.pop.adapter.mop.ConfigCommonAdapter;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.utils.mop.MopDataFillerUtils;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.parameter.ConfigAddOrUpdateParameter;
import com.xyy.pop.mop.api.remote.result.ConfigBasicDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 店铺活动配置(new)
 */
@Slf4j
@RequestMapping("/mop/storeActive")
@RestController
public class StoreActiveController extends BaseController {
    @Autowired
    ConfigCommonAdapter configCommonAdapter;
    private final String CONFIG_NAME = "storeActive";
    @GetMapping("/queryConfig")
    public ResponseVo<ConfigBasicDTO> queryStoreActive() {
//        ConfigBasicDTO configBasicDTO = new ConfigBasicDTO();
//        configBasicDTO.setConfigName(CONFIG_NAME);
        return configCommonAdapter.configDetail(CONFIG_NAME);
    }
    @PostMapping("/insertOrUpdate")
    public ResponseVo insertOrUpdate(@RequestBody ConfigAddOrUpdateParameter param) {
//        if (StringUtils.isEmpty(configValue)) {
//            return ResponseVo.errRest("请填写活动配置条件");
//        }
//        ConfigBasicDTO configBasicDTO = new ConfigBasicDTO();
//        configBasicDTO.setUpdateBy(getUser().getUsername());
//        if (configBasicDTO.getId()==null){
//            configBasicDTO.setCreateBy(getUser().getUsername());
//        }
//        configBasicDTO.setConfigName(CONFIG_NAME);
//        configBasicDTO.setConfigValue(configValue);
        if (Objects.isNull(param.getId())) {
            MopDataFillerUtils.fillData(getUser(),param,false);
        } else {
            MopDataFillerUtils.fillData(getUser(),param,true);
        }
        return configCommonAdapter.insertOrUpdateConfig(param);
    }
}
