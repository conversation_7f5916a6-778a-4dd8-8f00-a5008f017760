package com.xyy.ec.pop.redis.impl;


import com.xyy.ec.pop.constants.Constant;
import com.xyy.ec.pop.redis.IRedisService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCommands;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Created by liyang-macbook on 2017/6/22.
 */
@Service("adminRedisService")
public class RedisService implements IRedisService {

    @Autowired
    private StringRedisTemplate redisTemplate;


    @Override
    public Boolean setValue(String key, String value, long timeOut) {
        key = getFinalKey(key);
        redisTemplate.opsForValue().set(key, value, timeOut, TimeUnit.MINUTES);
        return redisTemplate.hasKey(key);
    }

    @Override
    public Boolean setValue(String key, String value, TimeUnit timeUnit, long timeOut) {
        key = getFinalKey(key);
        redisTemplate.opsForValue().set(key,value,timeOut,timeUnit);
        return redisTemplate.hasKey(key);
    }

    @Override
    public long incrementValue(String key, long value) {
        key = getFinalKey(key);
        return redisTemplate.opsForValue().increment(key,value);
    }

    @Override
    public String getValue(String key) {
        key = getFinalKey(key);
        return redisTemplate.opsForValue().get(key);
    }

    @Override
    public void setHashValue(String key, String hashKey, Object value) {
        key = getFinalKey(key);
        redisTemplate.opsForHash().put(key, hashKey, value);
    }

    @Override
    public void setHashAll(String key, Map<String, Object> map) {
        key = getFinalKey(key);
        redisTemplate.opsForHash().putAll(key, map);
    }

    @Override
    public Object getHashValue(String key, String hashKey) {
        key = getFinalKey(key);
        return redisTemplate.opsForHash().get(key, hashKey);
    }

    @Override
    public Set<Object> getKey(String key) {
        key = getFinalKey(key);
        return redisTemplate.opsForHash().keys(key);
    }

    @Override
    public void addSetValue(String key, Set<TypedTuple<String>> values) {
        key = getFinalKey(key);
        redisTemplate.opsForZSet().add(key,values);
    }

    @Override
    public Set<String> getSetValue(String key, int start, int end) {
        key = getFinalKey(key);
        return redisTemplate.opsForZSet().range(key,start,end);
    }

    @Override
    public Long countSet(String key) {
        key = getFinalKey(key);
        return redisTemplate.opsForZSet().size(key);
    }

    @Override
    public void deleteKey(String key) {
        key = getFinalKey(key);
        redisTemplate.delete(key);
    }

    @Override
    public boolean expireKey(String key, long timeOut) {
        key = getFinalKey(key);
        return redisTemplate.expire(key, timeOut, TimeUnit.MINUTES);
    }

    @Override
    public boolean expireKey(String key, long timeOut, TimeUnit timeUnit) {
        key = getFinalKey(key);
        return redisTemplate.expire(key, timeOut, timeUnit);
    }

    @Override
    public void setValue(String key, String value) {
        key = getFinalKey(key);
        redisTemplate.opsForValue().set(key, value);
    }

    @Override
    public boolean hasKey(String key) {
        key = getFinalKey(key);
        return redisTemplate.hasKey(key);
    }

    private String getFinalKey(String key) {
        key = Constant.BUSINESS_PREFIX + key;
        return key;
    }

    @Override
    public Long setNx(String key,String value) {
        try {
            RedisCallback<Long> callback = (connection) -> {
                JedisCommands commands = (JedisCommands) connection.getNativeConnection();
                String finalKey = getFinalKey(key);
                return commands.setnx(finalKey, value);
            };
            Long result = redisTemplate.execute(callback);
            return result;
        } catch (Exception e) {
        }
        return null;
    }

    @Override
    public boolean setNx(String key,String value, long time) {
        try {
            RedisCallback<String> callback = (connection) -> {
                JedisCommands commands = (JedisCommands) connection.getNativeConnection();
                String k = getFinalKey(key);
                return commands.set(k, value, "NX", "EX", time);
            };
            String result = redisTemplate.execute(callback);
            return !StringUtils.isEmpty(result);
        } catch (Exception e) {
        }
        return false;
    }

    @Override
    public String getNx(String key) {
        try {
            RedisCallback<String> callback = (connection) -> {
                JedisCommands commands = (JedisCommands) connection.getNativeConnection();
                getFinalKey(key);
                return commands.get(key);
            };
            String result = redisTemplate.execute(callback);
            return result;
        } catch (Exception e) {
        }
        return "";
    }
}
