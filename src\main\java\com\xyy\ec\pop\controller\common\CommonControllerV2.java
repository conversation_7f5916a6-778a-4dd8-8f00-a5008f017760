package com.xyy.ec.pop.controller.common;

import com.alibaba.fastjson.JSONObject;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.ec.merchant.bussiness.enums.BusinessTypeEnum;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.remote.BaseRegionBusinessRPCService;
import com.xyy.ec.pop.vo.ResponseVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "公共")
@Slf4j
@Controller
@RequestMapping("/common")
public class CommonControllerV2 extends BaseController {
    @Autowired
    private BaseRegionBusinessRPCService baseRegionBusinessRPCService;

    @RequestMapping("/customerTypes")
    @ResponseBody
    public ResponseVo customerTypes() {
        try {
            Map<Integer, String> maps = BusinessTypeEnum.maps;
            TreeMap<Integer, String> sortedMap = new TreeMap<>(maps);
            return ResponseVo.successResult(sortedMap.entrySet().stream().map(item -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("code", item.getKey());
                jsonObject.put("text", item.getValue());
                return jsonObject;
            }).collect(Collectors.toList()));

        }catch (Exception e){
            log.error("CommonControllerV2-获取客户类型失败",e);
            return ResponseVo.errCodeRest("获取客户类型失败");
        }
    }


    @RequestMapping("/areas")
    @ResponseBody
    public ResponseVo areas(String parentCodeStr) {
        try {
            List<Integer> parentCodes = new ArrayList<>();
            if (StringUtils.isNotBlank(parentCodeStr)){
                parentCodes = Arrays.stream(parentCodeStr.split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
            }
            List<XyyRegionBusinessDto> dtoList = baseRegionBusinessRPCService.queryChildrenRegionParam(parentCodes);
            if (CollectionUtils.isEmpty(dtoList)){
                return ResponseVo.successResult(new ArrayList<>());
            }
            dtoList.sort(Comparator.comparing(XyyRegionBusinessDto::getId));
            return ResponseVo.successResult(dtoList.stream().map(item->{
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("areaCode",item.getAreaCode());
                jsonObject.put("areaName",item.getAreaName());
                jsonObject.put("level",item.getLevel());
                return jsonObject;
            }).collect(Collectors.toList()));
        }catch (Exception e){
            log.error("CommonControllerV2-获取地区失败",e);
            return ResponseVo.errCodeRest("获取地区失败");
        }
    }
}
