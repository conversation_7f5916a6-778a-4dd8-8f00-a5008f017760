package com.xyy.ec.pop.marketing.controller;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.marketing.shop.activity.dto.CouponRecallRecordReqDTO;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.marketing.service.VoucherRecallService;
import com.xyy.ec.pop.marketing.vo.CouponInstanceVO;
import com.xyy.ec.pop.marketing.vo.CouponRecallRecordVO;
import com.xyy.ec.pop.marketing.vo.CouponRecallResultVo;
import com.xyy.ec.pop.marketing.vo.CouponTemplateVO;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.model.XyyJsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 优惠券召回
 */
@RequestMapping("/voucherRecall")
@Controller
@Slf4j
public class VoucherRecallController extends BaseController {

    @Autowired
    private VoucherRecallService voucherRecallService;

    @RequestMapping("/pageList")
    @ResponseBody
    public XyyJsonResult pageList(@RequestBody CouponRecallRecordReqDTO recordReqDTO) {
        try {
            PageInfo<CouponRecallRecordVO> pageInfo = voucherRecallService.pageInfo(recordReqDTO);
            return XyyJsonResult.createSuccess().addResult("pageInfo", pageInfo);
        } catch (PopAdminException e) {
            return XyyJsonResult.createFailure().msg(e.getMsg());
        } catch (Exception e) {
            log.error("voucherRecall.pageList 查询异常", e);
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        }
    }

    @RequestMapping("/recallVoucher")
    @ResponseBody
    public XyyJsonResult recallVoucher(Long templateId) {
        try {
            SysUser user = super.getUser();
            CouponRecallResultVo recallResult = voucherRecallService.recallVoucher(templateId, user.getUsername());
            return XyyJsonResult.createSuccess().addResult("recallResult", recallResult);
        } catch (PopAdminException e) {
            return XyyJsonResult.createFailure().msg(e.getMsg());
        } catch (Exception e) {
            log.error("召回优惠券异常", e);
            return XyyJsonResult.createFailure().msg("召回失败，请稍后重试！");
        }
    }

    @RequestMapping("/recallVoucherInstance")
    @ResponseBody
    public XyyJsonResult recallVoucherInstance(String voucherInstanceIdStr){
        try {
            SysUser user = super.getUser();
            CouponRecallResultVo recallResult = voucherRecallService.recallVoucherInstance(voucherInstanceIdStr, user.getUsername());
            return XyyJsonResult.createSuccess().addResult("recallResult", recallResult);
        } catch (PopAdminException e) {
            return XyyJsonResult.createFailure().msg(e.getMsg());
        } catch (Exception e) {
            log.error("召回优惠券实例异常", e);
            return XyyJsonResult.createFailure().msg("召回失败，请稍后重试！");
        }
    }

    @RequestMapping("/selectVoucherInstance")
    @ResponseBody
    public XyyJsonResult selectVoucherInstance(Long templateId, String phone,
                                               String merchantName, Long merchantId,
                                               Integer pageNum, Integer pageSize){
        try {
            PageInfo<CouponInstanceVO> pageInfo = voucherRecallService.selectVoucherInstance(templateId, phone, merchantName, merchantId, pageNum, pageSize);
            return XyyJsonResult.createSuccess().addResult("pageInfo", pageInfo);
        } catch (PopAdminException e) {
            return XyyJsonResult.createFailure().msg(e.getMsg());
        } catch (Exception e) {
            log.error("查询优惠券实例异常", e);
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        }
    }

    @RequestMapping("/getRecallTotalNum")
    @ResponseBody
    public XyyJsonResult getRecallTotalNum(Long templateId) {
        try {
            Integer recallTotalNum = voucherRecallService.getRecallTotalNum(templateId);
            return XyyJsonResult.createSuccess().addResult("recallTotalNum", recallTotalNum);
        } catch (PopAdminException e) {
            return XyyJsonResult.createFailure().msg(e.getMsg());
        } catch (Exception e) {
            log.error("查询召回数量异常", e);
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        }
    }

    @RequestMapping("/selectVoucherTemplate")
    @ResponseBody
    public XyyJsonResult selectVoucherTemplate(Long templateId, String templateName,
                                               Integer pageNum, Integer pageSize) {
        try {
            PageInfo<CouponTemplateVO> pageInfo = voucherRecallService.selectVoucherTemplate(templateId, templateName, pageNum, pageSize);
            return XyyJsonResult.createSuccess().addResult("pageInfo", pageInfo);
        } catch (PopAdminException e) {
            return XyyJsonResult.createFailure().code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("召回优惠券异常", e);
            return XyyJsonResult.createFailure().msg("召回失败，请稍后重试！");
        }
    }
}
