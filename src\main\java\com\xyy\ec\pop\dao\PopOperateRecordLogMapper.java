package com.xyy.ec.pop.dao;

import com.xyy.ec.pop.po.PopOperateRecordLogPo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface PopOperateRecordLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PopOperateRecordLogPo record);

    int insertSelective(PopOperateRecordLogPo record);

    PopOperateRecordLogPo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PopOperateRecordLogPo record);

    int updateByPrimaryKey(PopOperateRecordLogPo record);
}