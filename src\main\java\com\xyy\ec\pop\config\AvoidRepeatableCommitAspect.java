package com.xyy.ec.pop.config;

import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.redis.impl.RedisService;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * 防重提交
 */
@Aspect
@Slf4j
@Component
public class AvoidRepeatableCommitAspect extends BaseController {

    @Autowired
    protected RedisService redisService;

    @Around("@annotation(com.xyy.ec.pop.config.AvoidRepeatableCommit)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Long userId = getUser().getId();
        //获取注解
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        AvoidRepeatableCommit annotation = method.getAnnotation(AvoidRepeatableCommit.class);
        //获取方法名
        String methodName = method.getName();
        String repeatCheckKey = "repeat:chcek:" + userId + ":" + methodName;
        Boolean aBoolean = redisService.hasKey(repeatCheckKey);
        if (aBoolean) {
            //按最后一次点击计算时间
            redisService.setValue(repeatCheckKey, StringUtils.EMPTY, TimeUnit.SECONDS, annotation.timeout());
            return ResponseVo.errRest("提交频繁,请稍后再试");
        } else {
            //执行方法
            redisService.setValue(repeatCheckKey, StringUtils.EMPTY, TimeUnit.SECONDS, annotation.timeout());
            return point.proceed();
        }
    }
}
