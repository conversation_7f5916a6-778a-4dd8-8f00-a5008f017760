package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.merchant.api.admin.CheckCorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.CheckCorporationAndUserExtDto;
import com.xyy.ec.pop.server.api.merchant.dto.CheckCorporationDto;
import com.xyy.ec.pop.server.api.merchant.dto.QueryCheckCorporationParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @version v1
 * <AUTHOR>
 */
@Component
@Slf4j
public class CheckCorporationRemoteAdapter {
    @Reference
    private CheckCorporationAdminApi checkCorporationAdminApi;
    public List<CheckCorporationDto> listCorporations(String orgId){
        try {
            log.info("#CheckCorporationAdminApiImpl.listCorporations#queryCheckCorporationParam:{}", orgId);
            ApiRPCResult<List<CheckCorporationDto>> result = checkCorporationAdminApi.listOrgCorporations(orgId);
            log.info("#CheckCorporationAdminApiImpl.listCorporations#queryCheckCorporationParam:{} return:{}", orgId,JSON.toJSONString(result));

            return result.isSuccess()?result.getData():new ArrayList<>();
        } catch (Exception e) {
            log.error("#CheckCorporationAdminApiImpl.listCorporations#queryCheckCorporationParam:{}", orgId, e);
            return new ArrayList<>(0);
        }
    }
}
