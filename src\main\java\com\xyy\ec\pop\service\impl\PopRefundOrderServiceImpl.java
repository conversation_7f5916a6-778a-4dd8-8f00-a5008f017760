package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.backend.pop.api.PopRefundManageApi;
import com.xyy.ec.order.backend.pop.dto.PopOrderRefundExpressBusinessDto;
import com.xyy.ec.order.backend.pop.dto.RefundResultDto;
import com.xyy.ec.order.backend.pop.param.RefundParamDto;
import com.xyy.ec.order.search.api.remote.dto.OrderRefundSearchQueryDto;
import com.xyy.ec.order.search.api.remote.dto.OrderRefundSearchQueryPlusDto;
import com.xyy.ec.order.search.api.remote.result.SearchResultDto;
import com.xyy.ec.pop.ecToPop.EcToPopCorporationService;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.OrderEsVoHelper;
import com.xyy.ec.pop.remote.CorporationRemote;
import com.xyy.ec.pop.remote.EcOrderEsRemote;
import com.xyy.ec.pop.remote.EcOrderRemote;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.service.PopRefundOrderService;
import com.xyy.ec.pop.vo.RefundOrderDetailAdminVo;
import com.xyy.ec.pop.vo.RefundOrderParamVo;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PopRefundOrderServiceImpl implements PopRefundOrderService {
    @Autowired
    private EcOrderRemote ecOrderRemote;
    //订单es 开关 0 开 1关
    @Value("${order.orderes.refund.switch}")
    private String orderRefundEsSwitch;
    @Autowired
    private EcOrderEsRemote ecOrderEsRemote;

    @Reference
    private PopRefundManageApi popRefundManageApi;
    @Autowired
    private CorporationRemote corporationRemote;
    @Autowired
    private EcToPopCorporationService ecToPopCorporationService;
    @Override
    public PageInfo<RefundOrderDetailAdminVo> queryRefundOrderPage(int pageNum, int pageSize, RefundOrderParamVo paramVo) {
        try {
            Long totalCount;
            List<RefundOrderDetailAdminVo> list;
            if (Objects.equals("0",orderRefundEsSwitch)){
                //TODO 根据配置文件判断走新旧逻辑
                if (true){
                    OrderRefundSearchQueryPlusDto orderRefundSearchQueryPlusDto = OrderEsVoHelper.convertRefundOrderToOrderRefundSearchQueryPlusDto(paramVo,pageNum,pageSize);
                    SearchResultDto<String> searchResultDto = ecOrderEsRemote.refundOrderSearchPlus(orderRefundSearchQueryPlusDto);
                    if (Objects.nonNull(searchResultDto) && !CollectionUtils.isEmpty(searchResultDto.getResultList())) {
                        RefundOrderParamVo p = new RefundOrderParamVo();
                        p.setRefundOrderNos(searchResultDto.getResultList());
                        p.setIsThirdCompany(null == paramVo.getIsThirdCompany() ? -1 : paramVo.getIsThirdCompany());
                        PageInfo<RefundOrderDetailAdminVo> pageInfo = ecOrderRemote.queryRefundOrderPage(p, 1, pageSize);
                        list = pageInfo.getList();
                        totalCount = searchResultDto.getTotalCount();
                    }else{
                        list = Lists.newArrayList();
                        totalCount = 0L;
                    }
                }else {
                    OrderRefundSearchQueryDto orderRefundSearchQueryDto = OrderEsVoHelper.convertRefundOrderToOrderRefundSearchQueryDto(paramVo,pageNum,pageSize);
                    SearchResultDto<String> searchResultDto = ecOrderEsRemote.refundOrderSearch(orderRefundSearchQueryDto);
                    if (Objects.nonNull(searchResultDto) && !CollectionUtils.isEmpty(searchResultDto.getResultList())) {
                        RefundOrderParamVo p = new RefundOrderParamVo();
                        p.setRefundOrderNos(searchResultDto.getResultList());
                        p.setIsThirdCompany(null == paramVo.getIsThirdCompany() ? -1 : paramVo.getIsThirdCompany());
                        PageInfo<RefundOrderDetailAdminVo> pageInfo = ecOrderRemote.queryRefundOrderPage(p, 1, pageSize);
                        list = pageInfo.getList();
                        totalCount = searchResultDto.getTotalCount();
                    }else{
                        list = Lists.newArrayList();
                        totalCount = 0L;
                    }
                }
            }else{
                if (true){
                    //TODO 根据配置文件判断走新旧逻辑
                    PageInfo<RefundOrderDetailAdminVo> pageInfo = ecOrderRemote.queryRefundOrderPagePlus(paramVo, pageNum, pageSize);
                    list = pageInfo.getList();
                    totalCount = pageInfo.getTotal();
                }else {
                    PageInfo<RefundOrderDetailAdminVo> pageInfo = ecOrderRemote.queryRefundOrderPage(paramVo, pageNum, pageSize);
                    list = pageInfo.getList();
                    totalCount = pageInfo.getTotal();
                }
            }
            PageInfo<RefundOrderDetailAdminVo> page = new PageInfo<>();
            page.setPageNum(pageNum);
            page.setPageSize(pageSize);
            page.setTotal(totalCount);
            page.setList(list);
            //是否包含ec店铺
            boolean isContainsEc = ecToPopCorporationService.isContainsEc(paramVo.getIsThirdCompany());
            Map<String, String> ecToPopOrgIdMap = isContainsEc ? ecToPopCorporationService.getEcToPopOrgIdMap() : Maps.newHashMap();
            if (!CollectionUtils.isEmpty(page.getList())) {
                List<String> orgIds = page.getList().stream().map(RefundOrderDetailAdminVo::getOrgId).collect(Collectors.toList());
                if(MapUtils.isNotEmpty(ecToPopOrgIdMap)){
                    orgIds.addAll(ecToPopOrgIdMap.values());
                }
//                List<CorporationExt> corporationList = branchOrganizationService.selectCorporationInfo(orgIds);
                List<CorporationDto> corporationList = corporationRemote.queryCorpBaseByOrgIds(orgIds);
                if (CollectionUtils.isEmpty(corporationList)) {
                    return page;
                }
                Map<String, CorporationDto> shOrgIdMap = corporationList.stream().collect(Collectors.toMap(CorporationDto::getOrgId, Function.identity(), (key1, key2) -> key2));
                page.getList().forEach(row -> {
                    String orgId = row.getOrgId();
                    CorporationDto shExt = shOrgIdMap.get(row.getOrgId());
                    if(MapUtils.isNotEmpty(ecToPopOrgIdMap) && StringUtils.isNotEmpty(ecToPopOrgIdMap.get(row.getOrgId()))){
                        String popOrgId = ecToPopOrgIdMap.get(row.getOrgId());
                        shExt = shOrgIdMap.get(popOrgId);
                    }
                    if (null != shExt) {//新数据
                        row.setBusinessName(shExt.getName());
                        row.setOrgId(shExt.getOrgId());
                        row.setCompanyName(shExt.getCompanyName());
                    }
                    if(StringUtils.isNotEmpty(row.getOrgId())
                            && StringUtils.isNotEmpty(orgId)
                            && !Objects.equals(row.getOrgId(), orgId)){
                        String nOrgId = row.getOrgId() + "(" + orgId + ")";
                        row.setOrgId(nOrgId);
                    }
                });
            }
            return page;
        } catch (XyyEcPopException e) {
            log.error("queryRefundOrderPage#paramVo{} 异常", JSON.toJSONString(paramVo), e);
        } catch (Exception e) {
            log.error("queryRefundOrderPage#paramVo{} 异常", JSON.toJSONString(paramVo), e);
        }
        return null;
    }

    @Override
    public PopOrderRefundExpressBusinessDto queryRefundExpressByOrderRefundId(Long orderRefundId) {
        return ecOrderRemote.queryRefundExpressByOrderRefundId(orderRefundId);
    }

    @Override
    public Boolean refundOrder(Long refundId, Integer operateType, String currentUser,String evidenceImages) {
        RefundParamDto param = new RefundParamDto();
        param.setRefundId(refundId);
        param.setOperateType(operateType);
        param.setOperator(currentUser);
        if (StringUtils.isNotBlank(evidenceImages)){
//            param.setEvidenceImages(JSONObject.toJSONString(Lists.newArrayList(evidenceImages)));
            param.setRefundPayEvidence(evidenceImages);
        }
        param.setRequestSource(1);
        log.info("PopRefundOrderServiceImpl.refundOrder#param:{}", JSON.toJSONString(param));
        //调用EC接口
        ApiRPCResult<RefundResultDto> result = popRefundManageApi.refund(param);
        log.info("PopRefundOrderServiceImpl.refundOrder#param:{},result:{}", JSON.toJSONString(param), JSON.toJSONString(result));
        if (result == null || result.isFail()) {
            return false;
        }
        return true;
    }
}
