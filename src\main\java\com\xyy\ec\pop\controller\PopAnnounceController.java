package com.xyy.ec.pop.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.enums.PopAnnounceStatusEnum;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.PopAnnounceRemoteAdapter;
import com.xyy.ec.pop.server.api.announce.enums.AnnounceStatusEnum;
import com.xyy.ec.pop.vo.PopAnnounceQueryParamVo;
import com.xyy.ec.pop.vo.PopAnnounceVo;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: Jincheng.Li
 * @Date: 2021/07/13/16:14
 * @Description: 系统公告
 */
@Slf4j
@Controller
@RequestMapping("/announce")
public class PopAnnounceController extends BaseController {

    @Value("${using.announce.num.max}")
    private Integer usingAnnounceCount;
    @Autowired
    private PopAnnounceRemoteAdapter popAnnounceRemoteAdapter;

    /**
     * 系统公告列表
     * @param queryParamVo
     * @return
     */
    @RequestMapping("/list")
    @ResponseBody
    public ResponseVo pageList( PopAnnounceQueryParamVo queryParamVo) {
        try {
            log.info("PopAnnounceController.pageList param sysNoticeQueryParamVo:{}",JSON.toJSONString(queryParamVo));
            PageInfo<PopAnnounceVo> pageList = popAnnounceRemoteAdapter.getPageList( queryParamVo);
            log.info("PopAnnounceController.pageList result:{}", JSON.toJSONString(pageList));
            return pageList == null ? ResponseVo.errRest("查询失败") : ResponseVo.successResult(pageList);
        } catch (Exception e) {
            log.error("SysNoticeController.pageList error# 查询公告异常",e);
            return ResponseVo.errRest("查询失败");
        }
    }

    /**
     * 保存公告
     *
     * @param popAnnounceVo
     * @return
     */
    @PostMapping("/save")
    @ResponseBody
    public ResponseVo insertOrUpdate( PopAnnounceVo popAnnounceVo) {
        try {
            log.info("PopAnnounceController.save param:{}", JSON.toJSONString(popAnnounceVo));
            Boolean aBoolean = validPopAnnounce(popAnnounceVo);
            if (!aBoolean) {
                return ResponseVo.errRest("请填写完整信息");
            }
            SysUser user = getUser();
            if (user == null){
                return ResponseVo.errRest("操作失败，没有登录信息");
            }
            //新增
            if (popAnnounceVo.getId() == null){
                return insertAnnounce(popAnnounceVo,user);
            }
            //编辑保存
            return updateAnnounce(popAnnounceVo,user);
        } catch (Exception e) {
            log.error("PopAnnounceController.save error", e);
            return ResponseVo.errRest("保存失败");
        }
    }

    private ResponseVo insertAnnounce(PopAnnounceVo popAnnounceVo,SysUser user) throws Exception{
        log.info("PopAnnounceController.insertAnnounce param pop:{}",JSON.toJSONString(popAnnounceVo));
        boolean aBoolean1 = popAnnounceVo.getStatus().intValue() == AnnounceStatusEnum.RUN_ANNOUNCE.getCode() && Objects.equals(AnnounceStatusEnum.IS_SHOW_BANNER.getCode(),popAnnounceVo.getIsShowBanner());
        boolean aBoolean2 = usingAnnounceNum();
        if (aBoolean1 && !aBoolean2){
            return ResponseVo.errRest(2, "同一时间只允许启用"+usingAnnounceCount+"个banner公告，当前已达上限\n" +
                    "若确认则会将当前公告状态置为“停用”后保存，是否确认？");
        }
        Boolean aBoolean = popAnnounceRemoteAdapter.savePopAnnounce(popAnnounceVo, user.getUsername());
        return aBoolean ? ResponseVo.successResultNotData() : ResponseVo.errRest("保存失败");
    }

    private ResponseVo updateAnnounce(PopAnnounceVo popAnnounceVo,SysUser user) throws Exception{
        log.info("PopAnnounceController.updateAnnounce param pop:{}",JSON.toJSONString(popAnnounceVo));
        PopAnnounceVo detail = popAnnounceRemoteAdapter.getDetail(popAnnounceVo.getId());
        log.info("PopAnnounceController.save announceDetail:{}", JSON.toJSONString(detail));
        if (detail == null) {
            return ResponseVo.errRest("id为" + popAnnounceVo.getId() + "的公告不存在");
        }
        boolean aBoolean1 = popAnnounceVo.getStatus().intValue() == AnnounceStatusEnum.RUN_ANNOUNCE.getCode() && Objects.equals(AnnounceStatusEnum.IS_SHOW_BANNER.getCode(),popAnnounceVo.getIsShowBanner());
        boolean aBoolean2 = usingAnnounceNum();
        if (aBoolean1 && !aBoolean2){
            if (!(detail.getStatus().intValue() == AnnounceStatusEnum.RUN_ANNOUNCE.getCode() && Objects.equals(AnnounceStatusEnum.IS_SHOW_BANNER.getCode(),detail.getIsShowBanner()))){
                return ResponseVo.errRest(2, "同一时间只允许启用"+usingAnnounceCount+"个banner公告，当前已达上限。请先停用其他banner公告");
            }
        }
        Boolean aBoolean = popAnnounceRemoteAdapter.savePopAnnounce(popAnnounceVo, user.getUsername());
        return aBoolean ? ResponseVo.successResultNotData() : ResponseVo.errRest("保存失败");
    }


    public Boolean usingAnnounceNum() throws Exception {
        Integer usingAnnounce = popAnnounceRemoteAdapter.getUsingAnnounce();
        if (usingAnnounce == -1) {
            throw new RuntimeException("获取正在启用系统公告数量失败");
        }
        return usingAnnounce < usingAnnounceCount;
    }

    /**
     * 查看公告详情
     *
     * @param id
     * @return
     */
    @RequestMapping("/detail")
    @ResponseBody
    public ResponseVo getDetail(Integer id) {
        try {
            if (id == null) {
                return ResponseVo.errRest("参数不能为空");
            }
            log.info("PopAnnounceController.gerDetail param:{}", id);
            PopAnnounceVo detail = popAnnounceRemoteAdapter.getDetail(id);
            log.info("PopAnnounceController.gerDetail result:{}", JSON.toJSONString(detail));
            return detail == null ? ResponseVo.errRest("查询失败") : ResponseVo.successResult(detail);
        } catch (Exception e) {
            log.error("PopAnnounceController.getDetail error id:{}", id, e);
            return ResponseVo.errRest("查看详情失败");
        }
    }

    /**
     * 删除公告
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/delete",method = RequestMethod.POST)
    @ResponseBody
    public ResponseVo delete(Integer id) {
        try {
            if (id == null) {
                return ResponseVo.errRest("参数不能为空");
            }
            log.info("PopAnnounceController.delete param id:{}", id);
            PopAnnounceVo detail = popAnnounceRemoteAdapter.getDetail(id);
            if (detail == null) {
                return ResponseVo.errRest("该公告不存在");
            }
            SysUser user = getUser();
            if (user == null){
                return ResponseVo.errRest("操作失败，没有登录信息");
            }
            Boolean aBoolean = popAnnounceRemoteAdapter.deleteAnnounceById(id, user.getUsername());
            return aBoolean ? ResponseVo.successResult("删除成功") : ResponseVo.errRest("删除失败");
        } catch (Exception e) {
            log.error("PopAnnounceController.delete error", e);
            return ResponseVo.errRest("删除失败");
        }
    }

    /**
     * 停用广告
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/stopUsing",method = RequestMethod.POST)
    @ResponseBody
    public ResponseVo stopUsing(Integer id, Integer status) {
        try {
            if (id == null || status == null) {
                return ResponseVo.errRest("参数不能为空");
            }
            log.info("PopAnnounceController.stopUsing param id:{}，status:{}", id,status);
            PopAnnounceVo detail = popAnnounceRemoteAdapter.getDetail(id);
            if (detail == null) {
                return ResponseVo.errRest("该公告不存在");
            }
            if (status == detail.getStatus().intValue()) {
                return ResponseVo.errRest("该公告状态不正确");
            }
            Boolean aBoolean1 = usingAnnounceNum();
            if (status.equals(PopAnnounceStatusEnum.ANNOUNCE_START.getCode()) && Objects.equals(AnnounceStatusEnum.IS_SHOW_BANNER.getCode(),detail.getIsShowBanner())&& !aBoolean1) {
                return ResponseVo.errRest(2, "同一时间只允许启用"+usingAnnounceCount+"个banner公告，当前已达上限。请先停用其他banner公告");
            }
            SysUser user = getUser();
            if (user == null){
                return ResponseVo.errRest("操作失败，没有登录信息");
            }
            Boolean aBoolean = popAnnounceRemoteAdapter.stopUsing(id, status,detail.getStatus(), user.getUsername());
            return aBoolean ? ResponseVo.successResult("操作成功") : ResponseVo.errRest("操作失败");
        } catch (Exception e) {
            log.error("PopAnnounceController.stopUsing error id:{}", id, e);
            return ResponseVo.errRest("出现异常，操作失败");
        }
    }

    @RequestMapping(value = "/closeBanner",method = RequestMethod.POST)
    @ResponseBody
    public ResponseVo closeBanner(Integer id) {
        try {
            if (id == null) {
                return ResponseVo.errRest("参数不能为空");
            }
            log.info("PopAnnounceController.closeBanner param id:{}", id);
            PopAnnounceVo detail = popAnnounceRemoteAdapter.getDetail(id);
            if (detail == null) {
                return ResponseVo.errRest("该公告不存在");
            }
            SysUser user = getUser();
            if (user == null){
                return ResponseVo.errRest("操作失败，没有登录信息");
            }
            Boolean aBoolean = popAnnounceRemoteAdapter.closeBanner(id, user.getUsername());
            return aBoolean ? ResponseVo.successResult("操作成功") : ResponseVo.errRest("操作失败");
        } catch (Exception e) {
            log.error("PopAnnounceController.closeBanner error", e);
            return ResponseVo.errRest("停用banner失败");
        }
    }

    private Boolean validPopAnnounce(PopAnnounceVo popAnnounceVo) {
        if (popAnnounceVo == null) {
            return false;
        }
        boolean aBoolean = StringUtils.isBlank(popAnnounceVo.getContent()) || StringUtils.isBlank(popAnnounceVo.getTitle()) || (StringUtils.isBlank(popAnnounceVo.getUrl()) && Objects.equals(AnnounceStatusEnum.IS_SHOW_BANNER.getCode(),popAnnounceVo.getIsShowBanner())) || popAnnounceVo.getStatus() == null;
        return !aBoolean;
    }

}
