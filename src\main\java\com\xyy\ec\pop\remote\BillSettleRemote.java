package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.server.api.admin.api.PopBillSettleApi;
import com.xyy.ec.pop.server.api.admin.dto.AdjustiveBillSettleDto;
import com.xyy.ec.pop.server.api.admin.dto.PopBillSettleDetailDto;
import com.xyy.ec.pop.server.api.export.param.BillSettleDetailExportAdminParam;
import com.xyy.ec.pop.server.api.order.dto.PopBillSettleDto;
import com.xyy.ec.pop.server.api.seller.dto.PopBillSettleBatchDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @version v1
 * <AUTHOR>
 */
@Service
@Slf4j
public class BillSettleRemote {
    @Reference
    private PopBillSettleApi popBillSettleApi;


    /**
     * 批量导入调账单
     *
     * @param billSettleDtos
     * @return
     */
    public void batchImportAdjustiveBillSettle(List<PopBillSettleDto> billSettleDtos) {
        try {
            log.info("BillSettleRemote.batchImportAdjustiveBillSettle#billSettleDtos:{}", JSON.toJSONString(billSettleDtos));
            ApiRPCResult result = popBillSettleApi.batchImportAdjustiveBillSettle(billSettleDtos);
            log.info("BillSettleRemote.batchImportAdjustiveBillSettle#result:{}", JSON.toJSONString(result));
            if (result.isFail()) {
                throw new ServiceRuntimeException(result.getErrMsg());
            }
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.batchImportAdjustiveBillSettle#billSettleDtos:{} 异常", JSON.toJSONString(billSettleDtos), e);
            throw new ServiceRuntimeException("批量导入调账单失败");
        }
    }

    public void batchUpdateAdjustiveBillSettle(List<AdjustiveBillSettleDto> params) {
        try {
            log.info("BillSettleRemote.batchUpdateAdjustiveBillSettle#params:{}", JSON.toJSONString(params));
            ApiRPCResult result = popBillSettleApi.batchUpdateAdjustiveBillSettle(params);
            log.info("BillSettleRemote.batchUpdateAdjustiveBillSettle#result:{}", JSON.toJSONString(result));
            if (result.isFail()) {
                throw new ServiceRuntimeException(result.getErrMsg());
            }
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.batchUpdateAdjustiveBillSettle#params:{} 异常", JSON.toJSONString(params), e);
            throw new ServiceRuntimeException("批量更新调账单失败");
        }
    }

    public List<PopBillSettleDto> listImportAdjustiveBillSettle(String userName) {
        try {
            log.info("BillSettleRemote.listImportAdjustiveBillSettle#userName:{}", userName);
            ApiRPCResult<List<PopBillSettleDto>> result = popBillSettleApi.listImportAdjustiveBillSettle(userName);
            log.info("BillSettleRemote.listImportAdjustiveBillSettle#userName:{},result:{}", userName, JSON.toJSONString(result));
            if (result.isFail()) {
                throw new ServiceRuntimeException(result.getErrMsg());
            }
            return result.getData();
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.listImportAdjustiveBillSettle#userName:{} 异常", userName, e);
            throw new ServiceRuntimeException("查询导入的调账单列表失败");
        }
    }

    public Boolean deleteAdjustiveBillSettleByIds(List<String> ids, String userName) {
        try {
            log.info("BillSettleRemote.deleteAdjustiveBillSettleByIds#ids:{},userName:{}", JSON.toJSONString(ids), userName);
            ApiRPCResult<Boolean> result = popBillSettleApi.deleteAdjustiveBillSettleByIds(ids, userName);
            log.info("BillSettleRemote.deleteAdjustiveBillSettleByIds#ids:{},userName:{},result:{}", JSON.toJSONString(ids), userName, JSON.toJSONString(result));
            if (result.isFail()) {
                throw new ServiceRuntimeException(result.getErrMsg());
            }
            return result.getData();
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.deleteAdjustiveBillSettleByIds#error. ids:{},userName:{}", JSON.toJSONString(ids), userName, e);
            throw new ServiceRuntimeException("批量移除导入调账单失败");
        }
    }
    public Boolean updateCommissionCalcFlag(List<String> businessNos,Integer commissionCalcFlag, String operator) {
        try {
            log.info("updateCommissionCalcFlag param:businessNos={}&commissionCalcFlag={}&operator={}", businessNos,commissionCalcFlag,operator);
            ApiRPCResult<Boolean> result = popBillSettleApi.updateCommissionCalcFlag(businessNos,commissionCalcFlag, operator);
            log.info("updateCommissionCalcFlag result:businessNos={}&commissionCalcFlag={}&operator={},result:{}", businessNos,commissionCalcFlag,operator, JSON.toJSONString(result));
            if (result.isFail()) {
                throw new ServiceRuntimeException(result.getErrMsg());
            }
            return result.getData();
        } catch (Exception e) {
            log.error("updateCommissionCalcFlag error:businessNos={}&commissionCalcFlag={}&operator={}", businessNos,commissionCalcFlag,operator,e);
            throw new ServiceRuntimeException("更新失败");
        }
    }

    public void updateBatchCommissionCalcFlag(List<PopBillSettleBatchDto> batchList) {
        try {
            ApiRPCResult<Boolean> result = popBillSettleApi.updateBatchCommissionCalcFlag(batchList);
            if (result.isFail()) {
                throw new ServiceRuntimeException(result.getErrMsg());
            }
        } catch (Exception e) {
            throw new ServiceRuntimeException("更新失败");
        }
    }

    public PageInfo<PopBillSettleDetailDto> getSettleDetailList(String businessNo, Integer pageNum, Integer pageSize) {
        try {
            ApiRPCResult<PageInfo<PopBillSettleDetailDto>> result = popBillSettleApi.getSettleDetailList(businessNo, pageNum, pageSize);
            if (result.isFail() || result.getData() == null) {
                throw new ServiceRuntimeException(result.getErrMsg());
            }
            return result.getData();
        } catch (Exception e) {
            log.error("getSettleDetailList error:{}", e.getMessage(), e);
            throw new ServiceRuntimeException("查询结算单明细异常" + e.getMessage());
        }

    }

    public String exportBillSettleAdminDetail(BillSettleDetailExportAdminParam exportParam, String operator) {
        try {
            ApiRPCResult result = popBillSettleApi.exportBillSettleAdminDetail(exportParam, operator);
            if (result.isFail()) {
               return result.getErrMsg();
            }
            return null;
        } catch (Exception e) {
            log.error("exportBillSettleAdminDetail error:{}", e.getMessage(), e);
            throw new ServiceRuntimeException("导出结算单明细异常" + e.getMessage());
        }
    }
}
