package com.xyy.ec.pop.po;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * tb_xyy_pop_bill_detail
 * <AUTHOR>
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PopBillDetailPo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 商家名称
     */
    private String orgName;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 域orgId(EC传的orgId)
     */
    private String areaOrgId;

    /**
     * 区域编码
     */
    private String branchCode;

    /**
     * 账单号
     */
    private String billNo;

    /**
     * 单据类型(1:订单 2:退款单）
     */
    private Byte businessType;

    /**
     * 订单或退款单号
     */
    private String businessNo;

    /**
     * 客户名称
     */
    private String merchantName;

    /**
     * 商品总金额
     */
    private BigDecimal productMoney;

    /**
     * 单据总金额
     */
    private BigDecimal totalMoney;

    /**
     * 订单实际付款金额
     */
    private BigDecimal money;

    /**
     * 运费
     */
    private BigDecimal freightAmount;

    /**
     * 店铺优惠券金额
     */
    private BigDecimal couponShopAmount;

    /**
     * 店铺营销活动优惠金额
     */
    private BigDecimal marketingShopAmount;

    /**
     * 店铺总优惠金额
     */
    private BigDecimal shopTotalDiscount;

    /**
     * 平台优惠券金额
     */
    private BigDecimal couponPlatformAmount;

    /**
     * 平台营销活动优惠金额
     */
    private BigDecimal marketingPlatformAmount;

    /**
     * 平台总优惠金额
     */
    private BigDecimal platformTotalDiscount;

    /**
     * 佣金
     */
    private BigDecimal hireMoney;
    /**
     * 应收佣金
     */
    private BigDecimal payableCommission;
    /**
     * 冲抵平台优惠后的佣金
     */
    private BigDecimal deductedCommission;

    /**
     * 是否冲抵优惠 0-未冲抵 1-已冲抵
     */
    private Byte deducted;
    /**
     * 佣金结算方式，1：非月结；2：月结
     */
    private Byte settlementType;
    /**
     * 处罚金额
     */
    private BigDecimal penaltyAmount;

    /**
     * 应结算金额
     */
    private BigDecimal statementTotalMoney;

    /**
     * 支付时间
     */
    private Date orderPayTime;

    /**
     * 订单或退款单完成时间
     */
    private Date orderFinishTime;

    /**
     * 支付类型(1:在线支付  3:线下转账）
     */
    private Byte payType;

    /**
     * 订单结算状态 0-待结算 1-结算完成
     */
    private Byte orderSettlementStatus;

    /**
     * 订单结算时间
     */
    private Date orderSettlementTime;

    /**
     * 账单生成时间
     */
    private Date billCreateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 用户erp编码
     */
    private String merchantErpCode;

    /**
     * 用户ID
     */
    private Long merchantId;

    /**
     * 佣金折扣
     */
    private BigDecimal commissionDiscount;

    /**
     * 折扣原因
     */
    private String discountReason;

    /**
     * 实际需缴纳佣金
     */
    private BigDecimal actualCommissionMoney;

    /**
     * 佣金优惠金额
     */
    private BigDecimal commissionDiscountMoney;

    /**
     * 购物金实付金额
     */
    private BigDecimal virtualGold;

    /**
     * 现金实付金额
     */
    private BigDecimal cashPayAmount;

    /**
     * 结算方式:1:直连支付  3:平安支付
     */
    private Integer paymentChannel;

    private static final long serialVersionUID = 1L;
}