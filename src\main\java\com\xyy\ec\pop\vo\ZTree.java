/**
 * 文件名:ZTree<br/>
 * 版权:Copyright 2014-2015易互通科技（武汉）有限公司 All Rights Reserved.<br/>
 * 描述:
 * 创建人:<EMAIL><br/>
 * 创建时间:2016/1/1216:14<br/>
 * 修改人:
 * 修改时间:2016/1/1216:14 选写<br/>
 * 修改内容:
 */
package com.xyy.ec.pop.vo;

import com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.List;

/**
 * ClassName: ZTree <br/>
 * Function: ztree实体类 <br/>
 * @version
 * @since JDK 1.8
 */
public class ZTree implements Serializable{
    private static final long serialVersionUID = -8491876129118554880L;
    //树节点id")
    private Long id;
    //节点父类id")
    private Long pId;
    //节点名")
    private String name;
    private int level;
    //设置其他属性")
    private Object attributes;
    //树形是否展开")
    private boolean open;
    //是否是父类")
    private boolean isParent;
    //路径")
    private String url;
    //图标")
    private String icon;
    //标题")
    private String title;
    //树子节点")
    private List<ZTree> children;
    //是否选中")
    private boolean checked;
    //是否不可选")
    private boolean checkDisable;

    public ZTree(){}

    public ZTree(Long id, String name, boolean open, boolean isParent) {
        this.id = id;
        this.name = name;
        this.open = open;
        this.isParent = isParent;
        this.children = Lists.newArrayList();
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getpId() {
        return pId;
    }

    public void setpId(Long pId) {
        this.pId = pId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<ZTree> getChildren() {
        return children;
    }

    public void setChildren(List<ZTree> children) {
        this.children = children;
    }

    public boolean isIsParent() {
        return isParent;
    }

    public void setIsParent(boolean isParent) {
        this.isParent = isParent;
    }

    public Object getAttributes() {
        return attributes;
    }

    public void setAttributes(Object attributes) {
        this.attributes = attributes;
    }

    public boolean isCheckDisable() {
        return checkDisable;
    }

    public void setCheckDisable(boolean checkDisable) {
        this.checkDisable = checkDisable;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }
}
