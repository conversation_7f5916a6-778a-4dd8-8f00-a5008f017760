package com.xyy.ec.pop.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 发票申请导入
 */
@Data
public class InvoiceApplyExportVo {
    @Excel(name = "*发票申请单号", width = 20)
    private String invoiceApplyNo;

    /**
     * 物流公司名称
     */
    @Excel(name = "*物流公司", width = 20)
    private String logisticsCompanyName;
    /**
     * 物流公司单号
     */
    @Excel(name = "*运单号", width = 20)
    private String trackingNo;
    @Excel(name = "错误原因", width = 15)
    protected String errorMsg;
    /**
     * 表示该条记录是否错误
     */
    private boolean failed;
}
