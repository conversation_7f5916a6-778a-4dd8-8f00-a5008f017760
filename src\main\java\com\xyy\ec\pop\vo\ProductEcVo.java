package com.xyy.ec.pop.vo;

import com.xyy.ec.pop.server.api.product.dto.PopSkuPurchaseLimitDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author:xinghu.zhang
 * @Description: Ec商品实体
 * @Date:Created in 13:56 2018/5/10
 * @Modified By:
 **/
@Data
public class ProductEcVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 标准库id
     */
    private String standardProductId;

    /**
     * 商家名称
     */
    private String companyName;
    /**
     * 机构id
     */
    private String orgId;

    /**
     * 价格类型：1     2
     */
    private String price;
    /**
     * 价格类型 1     2
     */
    private String priceType;
    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 限购数量
     */
    private String limitedQty;
    /**
     * 区域编码
     */
    private String branchCode;

    /**
     * 禁忌
     */
    private String abstain;
    /**
     * 批准文号
     */
    private String approvalNumber;
    /**
     * 库存
     */
   private Integer availableQty;
    /**
     * 库存类型（真实库存，虚拟库存）
     */
    private String availableQtyType;
    /**
     * 条形码
     */
    private String barcode;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 备注
     */
    private String bz;
    /**
     * 关联分类id
     */
    private Long skuRelationCategory;
    /**
     * 关联分类
     */
    private String skuRelationCategoryName;
    /**
     * 推荐分类id
     */
    private String categoryId;
    /**
     * 推荐分类
     */
    private String categoryName;
    /**
     * 商品编码
     */
    private String code;
    /**
     * 通用名称
     */
    private String commonName;
    /**
     * 成分
     */
    private String component;
    /**
     * 注意事项
     */
    private String considerations;
    /**
     * 商品描述
     */
    private String description;
    /**
     * 剂型
     */
    private String dosageForm;
    /**
     * 药品分类
     */
    private Integer drugClassification;
    /**
     * 药帮忙价
     */
    private String fob;
    /**
     * 连锁售价
     */
    private String chainPrice;
    /**
     * 等级分类
     */
    private String grade;
    /**
     * 毛利率
     */
    private String grossMargin;
    /**
     * 商品原图
     */
    private String imageUrl;
    /**
     * 适应症/功能主治
     */
    private String indication;
    /**
     * 药物相互作用
     */
    private String interaction;
    /**
     * 是否底价
     */
    private String isBasePrice;
    /**
     * 是否易碎品
     */
    private String isFragileGoods;
    /**
     * 是否新品
     */
    private String isNew;
    /**
     * 是否可拆零
     */
    private String isSplit;
    /**
     * 中包装
     */
    private String mediumPackageNum;
    /**
     * 件装量
     */
    private String pieceLoading;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品单位
     */
    private String productUnit;
    /**
     * 生产日期
     */
    private String productionDate;
    /**
     * 对 比 价
     */
    private String retailPrice;
    /**
     * 保质期
     */
    private String shelfLife;
    /**
     * 展示名称
     */
    private String showName;
    /**
     * 分类信息（原关联分类 废弃）
     */
    private List<SkuCategoryRelation> skuCategoryRelations;
    /**
     * 说明书图片列表
     */
    private List<SkuInstructionImageVo> skuInstructionImageList;
    /**
     * 首营资质
     */
    private List<PopSkuQualificationVo> skuQualificationVoList;

    /**
     * 商品分类关联
     */
    private String skuCategoryRelationsStr;

    /**
     * 图片列表
     */
    private String imagesListStr;

    /**
     * 商品适应症/功能主治图片
     */
    private String skuInstructionImageListStr;

    /**
     * 商品图片列表
     */
    private List<String> imagesList;
    /**
     * 规格
     */
    private String spec;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 存储条件
     */
    private String storageCondition;
    /**
     * 建议零售价
     */
    private String suggestPrice;
    /**
     * 有效期
     */
    private String term;
    /**
     * 零 售 价
     */
    private String uniformPrice;
    /**
     * 不良反应
     */
    private String untowardEffect;
    /**
     * 用法与用量
     */
    private String usageAndDosage;
    /**
     * 有效期至
     */
    private String validity;
    /**
     * 助记码
     */
    private String zjm;
    /**
     * '是否第三方 1：是,
     */
    private Integer isThirdCompany;
    /**
     * 商品经营信息分类
     */
    private String skuCategory;
    /**
     * 商品经营信息分类:id
     */
    private Long skuCategoryId;
    /**
     * 中包装
     */
    private String mediumPackage;
    /**
     * 视频
     */
    private String videoUrl;
    /**
     * 副标题
     */
    private String subtitle;
    /**
     * pop商品扩展信息
     */
    private ProducePopExtendVo skuPopExtend;

    /**
     * pop商品扩展信息集合
     */
    private List<SkuPopExtend> skuPopExtendNewList;

    /**
     * pop商品扩展信息
     */
    private ProducePopExtendVo producePopExtendVo;
    /**
     * 最老生产日期
     */
    private String oldestProDate;
    /**
     * 最新生产日期
     */
    private String newProDate;
    /**
     * 近效期
     */
    private String nearEffect;
    /**
     * 远效期
     */
    private String farEffect;

    //库存
    private Integer totalAvailableQty;

    //提交时间
    private Date createTime;

    //是否针剂商品
    private String isInjection;

    /**
     * 是否处方药 1.是 0.否
     **/
    private String isPrescription;

    /**
     * 来源 1.商家自建 2.商品库
     */
    private Byte source;


    private String skuPopExtendNewListStr;

 /**
  * 一级发布分类id
  */
 private Long erpFirstCategoryId;
 /**
  * 一级发布分类名称
  */
 private String erpFirstCategoryName;

    /**
     * 二级发布分类id
     */
    private Long erpSecondCategoryId;
    /**
     * 二级发布分类名称
     */
    private String erpSecondCategoryName;


    /**
     * 三级发布分类id
     */
    private Long erpThirdCategoryId;
    /**
     * 三级发布分类名称
     */
    private String erpThirdCategoryName;

    /**
     * 四级发布分类ID
     */
    private Long erpFourthCategoryId;
    /**
     * 四级发布分类名称
     */
    private String erpFourthCategoryName;

    /**
     * 佣金比例
     */
    private String commissionRatioStr;

    /**
     * 商品图片url路径BaseUrl
     */
    private String productImgBaseUrl;
    /**
     * 商品详情图片url路径BaseUrl
     */
    private String productDescImgBaseUrl;
    /**
     * 价格是否同步ERP（0:否;1:是）
     */
    private Integer priceSyncErp;
    /**
     * 库存是否同步ERP（0:否;1:是）
     */
    private Integer stockSyncErp;
    /**
     * 是否已经有审核通过的状态
     */
    private Boolean audited;
    /**
     * 药品类型
     */
    private Byte saleType;
    /**
     * 商圈id
     */
    private Long busAreaId;
    /**
     * 机构编码
     */
    private String shopCode;
    /**
     * 商圈
     */
    private String busAreaConfigName;
    /**
     * 起购数量
     */
    private Integer minPurchaseCount;
    /**
     * 限购信息
     */
    private PopSkuPurchaseLimitDto popSkuPurchaseLimitDto;
}
