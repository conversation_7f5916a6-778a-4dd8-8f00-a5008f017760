package com.xyy.ec.pop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *  退款单
 * <AUTHOR>
 */
@Data
@ApiModel("退款扩展实体")
public class RefundOrderDTO implements Serializable {

    private static final long serialVersionUID = 665299949249674728L;

    @ApiModelProperty("区域机构id集合")
    private List<String> orgIdList;

    private String orgId;

    @ApiModelProperty("区域编码")
    private String branchCode;

    @ApiModelProperty("省份编码")
    private Integer provinceCode;
    /**
     * 商户编号
     */
    @ApiModelProperty("商户编号")
    private String corporationNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty("店铺名称")
    private String corporationName;

    /**
     * 机构名称
     */
    @ApiModelProperty("机构名称")
    private String companyName;
    /**
     *下单用户
     */
    @ApiModelProperty("下单用户")
    private String merchantName;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("退款单编号")
    private String refundOrderNo;

    /**
     * 退款状态
     */
    @ApiModelProperty("退款状态")
    private Integer auditState;
    /**
     * 申请开始日期
     */
    @ApiModelProperty("申请开始日期")
    private Date startCreateTime;
    /**
     * 审请结束日期
     */
    @ApiModelProperty("审请结束日期")
    private Date endCreateTime;
    @ApiModelProperty("开始时间")
    private Date startTime;
    @ApiModelProperty("结束时间")
    private Date endTime;

    /**
     * 退款状态名称
     */
    @ApiModelProperty("退款状态名称")
    private String adminAuditStatusName;
    @ApiModelProperty("退款发起方")
    private Integer refundChannel;

    @ApiModelProperty("退款原因")
    private String refundReason;

    /**
     * 支付类型
     */
    @ApiModelProperty("支付类型")
    public Integer payType;
    @ApiModelProperty("起始页")
    private Integer offset;
    @ApiModelProperty("每页显示的条数")
    private Integer limit;

    private Long provId;

    private List<Long> provIds;
}
