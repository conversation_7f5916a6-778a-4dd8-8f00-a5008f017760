package com.xyy.ec.pop.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Sets;
import com.google.common.collect.Lists;
import com.xyy.address.api.BaseRegionBusinessApi;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.address.dto.XyyRegionParams;
import com.xyy.ec.pop.remote.BaseRegionBusinessRPCService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * {@link BaseRegionBusinessApi} RPC Service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BaseRegionBusinessRPCServiceImpl implements BaseRegionBusinessRPCService {

    @Reference(version = "1.0.0")
    private BaseRegionBusinessApi baseRegionBusinessApi;

    @Override
    public List<XyyRegionBusinessDto> listProvinceRegions() {
        XyyRegionParams xyyRegionParams = new XyyRegionParams();
        xyyRegionParams.setLevel((byte) 1);
        List<XyyRegionBusinessDto> xyyRegionBusinessDtos = baseRegionBusinessApi.queryRegionByLevel(xyyRegionParams);
        return xyyRegionBusinessDtos;
    }

    @Override
    public List<XyyRegionBusinessDto> listRegionsByCodes(List<Integer> areaCodes) {
        if (CollectionUtils.isEmpty(areaCodes)) {
            return Lists.newArrayList();
        }
        XyyRegionParams xyyRegionParams = new XyyRegionParams();
        xyyRegionParams.setAreaCodes(areaCodes);
        List<XyyRegionBusinessDto> xyyRegionBusinessDtos = baseRegionBusinessApi.queryRegionByAreaCodeList(xyyRegionParams);
        if (CollectionUtils.isEmpty(xyyRegionBusinessDtos)) {
            return Lists.newArrayList();
        }
        return xyyRegionBusinessDtos;
    }

    @Override
    public List<XyyRegionBusinessDto> listChildrenRegionsByParentCode(Integer parentCode) {
        if (parentCode == null) {
            return Lists.newArrayList();
        }
        XyyRegionParams xyyRegionParams = new XyyRegionParams();
        xyyRegionParams.setParentCode(parentCode);
        List<XyyRegionBusinessDto> xyyRegionBusinessDtos = baseRegionBusinessApi.queryRegionByParentCode(xyyRegionParams);
        if (CollectionUtils.isEmpty(xyyRegionBusinessDtos)) {
            return Lists.newArrayList();
        }
        return xyyRegionBusinessDtos;
    }

    @Override
    public List<XyyRegionBusinessDto> queryRegionParam(Integer parentCode) {
        try {
            log.info("#BaseRegionBusinessRPCService.queryRegionParam#request#:{}", JSON.toJSONString(parentCode));
            XyyRegionParams params = new XyyRegionParams();
            if (parentCode == null){
                params.setParentCode(0);
            }else {
                params.setParentCode(parentCode);
            }
            List<XyyRegionBusinessDto> xyyRegionBusinessDtos = baseRegionBusinessApi.queryRegionByParentCode(params);
            log.info("#BaseRegionBusinessRPCService.queryRegionParam#rest#:{}", JSON.toJSONString(xyyRegionBusinessDtos));
            if (CollectionUtils.isEmpty(xyyRegionBusinessDtos)) {
                return Lists.newArrayList();
            }
            return xyyRegionBusinessDtos;
        }catch (Exception e){
            log.error("BaseRegionBusinessRPCService-queryRegionParam error",e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<XyyRegionBusinessDto> queryChildrenRegionParam(List<Integer> parentCodes) {
        try {
            log.info("#BaseRegionBusinessRPCService.queryChildrenRegionParam#request#:{}", JSON.toJSONString(parentCodes));
            XyyRegionParams params = new XyyRegionParams();
            if (CollectionUtils.isEmpty(parentCodes)){
                params.setParentCodes(Lists.newArrayList(0));
            }else {
                params.setParentCodes(parentCodes);
            }
            List<XyyRegionBusinessDto> xyyRegionBusinessDtos = baseRegionBusinessApi.queryRegionByParentCodeList(params);
            log.info("#BaseRegionBusinessRPCService.queryChildrenRegionParam#rest#:{}", JSON.toJSONString(xyyRegionBusinessDtos));
            if (CollectionUtils.isEmpty(xyyRegionBusinessDtos)) {
                return Lists.newArrayList();
            }
            return xyyRegionBusinessDtos;
        }catch (Exception e){
            log.error("BaseRegionBusinessRPCService-queryChildrenRegionParam error",e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<XyyRegionBusinessDto> queryParentToProvinceByAreaList(List<Integer> areaCodes) {
        if (CollectionUtils.isEmpty(areaCodes)){
            return Lists.newArrayList();
        }
        Set<XyyRegionBusinessDto> result = Sets.newHashSet();
        recursionQuery(result, areaCodes);
        return new ArrayList<>(result);
    }

    private void recursionQuery(Set<XyyRegionBusinessDto> result, List<Integer> areaCodeList) {
        if (CollectionUtils.isEmpty(areaCodeList)){
            return;
        }
        List<XyyRegionBusinessDto> xyyRegionBusinessDtos = listRegionsByCodes(areaCodeList);
        if (CollectionUtils.isEmpty(xyyRegionBusinessDtos)){
            return;
        }
        result.addAll(xyyRegionBusinessDtos);
        List<Integer> parentCodeList = xyyRegionBusinessDtos.stream().map(XyyRegionBusinessDto::getParentCode).distinct().filter(item->item != 0).collect(Collectors.toList());
        recursionQuery(result, parentCodeList);
    }
}
