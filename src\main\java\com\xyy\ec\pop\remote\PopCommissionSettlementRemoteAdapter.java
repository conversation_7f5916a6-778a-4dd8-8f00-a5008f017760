package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.order.dto.PopBillSettleDto;
import com.xyy.ec.pop.server.api.seller.api.PopCommissionSettleApi;
import com.xyy.ec.pop.server.api.seller.dto.*;
import com.xyy.ec.pop.vo.CommissionsSettlementCollectVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Description 佣金结算
 * <AUTHOR>
 */
@Component
@Slf4j
public class PopCommissionSettlementRemoteAdapter {
    @Reference
    private PopCommissionSettleApi popCommissionSettleApi;

    public boolean setCooperationCommissionsSettlementType(String orgId,Integer settlementType,String operator) throws ServiceException {
        try {
            log.info("setCooperationCommissionsSettlementType(orgId:{},type:{})",orgId,settlementType);
            ApiRPCResult<Boolean> result = popCommissionSettleApi.setCooperationCommissionsSettlementType(orgId,settlementType,operator);
            log.info("setCooperationCommissionsSettlementType(orgId:{}) return:{}",orgId, JSONObject.toJSONString(result));
            if(result.isFail()){
                return false;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("setCooperationCommissionsSettlementType(orgId:{}) 异常",orgId, e);
            throw new ServiceException("设置商家佣金结算方式异常");
        }
    }

    public List<PopCommissionSettleSetDto> queryCooperationCommissionsSettlementType(String orgId) throws ServiceException {
        try {
            log.info("queryCooperationCommissionsSettlementType,orgId:{}",orgId);
            ApiRPCResult<List<PopCommissionSettleSetDto>> result = popCommissionSettleApi.queryCooperationCommissionsSettlementType(orgId);
            log.info("queryCooperationCommissionsSettlementType,orgId:{},return:{}",orgId, JSONObject.toJSONString(result));
            if(result.isFail()){
                throw new ServiceException("查询商家佣金结算方式异常:"+result.getErrMsg());
            }
            return result.getData();
        } catch (Exception e) {
            log.error("queryCooperationCommissionsSettlementType,orgId:{},异常",orgId, e);
            throw new ServiceException("查询商家佣金结算方式异常");
        }
    }

    public PageInfo<PopCommissionSettleDto> queryPopCommissionSettleForPage(PopCommissionSettleAdminQueryDto commissionSettlementQueryDTO, PageInfo pageInfo) throws ServiceException {
        try {
            ApiRPCResult<PageInfo<PopCommissionSettleDto>> rpcResult = popCommissionSettleApi.queryPopCommissionSettleForPage(commissionSettlementQueryDTO,pageInfo);
            log.info("popCommissionSettleApi.listPopCommissionSettleForPage,queryDto:{},return {}",
                    JSONObject.toJSONString(commissionSettlementQueryDTO),JSONObject.toJSONString(rpcResult));
            if (rpcResult.isFail()) {
                return null;
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("查询统计佣金结算列表异常,{},{}", JSONObject.toJSONString(commissionSettlementQueryDTO) ,JSONObject.toJSONString(pageInfo),e);
            throw new ServiceException("查询统计佣金结算列表异常");
        }
    }

    public List<PopCommissionSettleDto> queryPopCommissionSettleList(PopCommissionSettleAdminQueryDto commissionSettlementQueryDTO) throws ServiceException {
        try {
            ApiRPCResult<List<PopCommissionSettleDto>> rpcResult = popCommissionSettleApi.queryPopCommissionSettleList(commissionSettlementQueryDTO);
            log.info("popCommissionSettleApi.queryPopCommissionSettleList,queryDto:{},return {}",
                    JSONObject.toJSONString(commissionSettlementQueryDTO),JSONObject.toJSONString(rpcResult));
            if (rpcResult.isFail()) {
                return Lists.newArrayList();
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("查询佣金结算列表异常,{}", JSONObject.toJSONString(commissionSettlementQueryDTO),e);
            throw new ServiceException("查询佣金结算列表异常");
        }
    }

    public PopCommissionSettleStatisticsDto queryPopCommissionSettleStatistics(PopCommissionSettleAdminQueryDto commissionSettlementQueryDTO) throws ServiceException {
        try {
            PopCommissionSettleStatisticsDto commissionSettleStatisticsDto = new PopCommissionSettleStatisticsDto();
            //查询各状态条数
            ApiRPCResult<PopCommissionSettleStatisticsDto> statisticsRpcResult = popCommissionSettleApi.queryPopCommissionSettleStatistics(null);
            log.info("popCommissionSettleApi.queryPopCommissionSettleStatistics,return:{}",JSONObject.toJSONString(statisticsRpcResult));
            if (statisticsRpcResult.isSuccess()) {
                PopCommissionSettleStatisticsDto count = statisticsRpcResult.getData();
                if(Objects.nonNull(count)){
                    commissionSettleStatisticsDto.setWaitCommercialPaymentNum(count.getWaitCommercialPaymentNum());
                    commissionSettleStatisticsDto.setWaitPlatformAuditNum(count.getWaitPlatformAuditNum());
                    commissionSettleStatisticsDto.setFailedAuditNum(count.getFailedAuditNum());
                    commissionSettleStatisticsDto.setOverdueNum(count.getOverdueNum());
                    commissionSettleStatisticsDto.setCommercialUploadPendingNum(count.getCommercialUploadPendingNum());
                }
            }
            //查询佣金总金额
            ApiRPCResult<PopCommissionSettleStatisticsDto> rpcResult = popCommissionSettleApi.queryCommissionSettleHireMoneyTotal(commissionSettlementQueryDTO);
            log.info("popCommissionSettleApi.selectCommissionSettleHireMoneyTotal,queryDto:{},return:{}",
                    JSONObject.toJSONString(commissionSettlementQueryDTO),JSONObject.toJSONString(rpcResult));
            if (rpcResult.isSuccess()) {
                PopCommissionSettleStatisticsDto sumHire = rpcResult.getData();
                if(Objects.nonNull(sumHire)){
                    commissionSettleStatisticsDto.setHireMoneyTotal(sumHire.getHireMoneyTotal());
                }
            }
            return commissionSettleStatisticsDto;
        } catch (Exception e) {
            log.error("查询统计佣金结算统计数据异常,{}", JSONObject.toJSONString(commissionSettlementQueryDTO) ,e);
            throw new ServiceException("查询统计佣金结算统计数据异常");
        }
    }

    public ApiRPCResult commissionSettleConfirmCollect(CommissionsSettlementCollectVo collectVo, String operator) throws ServiceException {
        try {
            ApiRPCResult rpcResult = popCommissionSettleApi.commissionSettleConfirmCollect(collectVo.getIds(),collectVo.getState(), operator, collectVo.getRemarks());
            log.info("popCommissionSettleApi.commissionSettleConfirmCollect,collectVo:{},return:{}",
                    JSONObject.toJSONString(collectVo),JSONObject.toJSONString(rpcResult));
            return rpcResult;
        } catch (Exception e) {
            log.error("确认收款异常,{}", JSONObject.toJSONString(collectVo) ,e);
            throw new ServiceException("确认收款异常",e);
        }
    }

    public PopCommissionSettleDto queryPopCommissionSettleByHireNo(String hireNo) throws ServiceException {
        try {
            ApiRPCResult<PopCommissionSettleDto> rpcResult = popCommissionSettleApi.queryPopCommissionSettleByHireNo(hireNo);
            log.info("popCommissionSettleApi.queryPopCommissionSettleByHireNo,hireNo:{},return:{}",
                    hireNo,JSONObject.toJSONString(rpcResult));
            if (rpcResult.isFail()) {
                return null;
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("查询佣金结算详情信息异常,hireNo:{}", hireNo ,e);
            throw new ServiceException("查询佣金结算详情信息异常");
        }
    }

    public PageInfo<PopBillSettleDto> queryCommissionSettleBillListByPage(String hireNo, PageInfo pageInfo) throws ServiceException {
        try {
            ApiRPCResult<PageInfo<PopBillSettleDto>> rpcResult = popCommissionSettleApi.queryCommissionSettleDetailListByPage(hireNo, pageInfo);
            log.info("popCommissionSettleApi.queryCommissionSettleBillPoListByPage,hireNo:{},return:{}",
                    hireNo, JSONObject.toJSONString(rpcResult));
            if (rpcResult.isFail()) {
                return null;
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("查询佣金结算的账单明细列表异常,hireNo:{},pageInfo:{}", hireNo,JSONObject.toJSONString(pageInfo),e);
            throw new ServiceException("查询佣金结算的账单明细列表异常");
        }
    }

    public Long countPopCommissionSettle(PopCommissionSettleAdminQueryDto commissionSettlementQueryDTO) throws ServiceException {
        try {
            ApiRPCResult<Long> countRPCResult = popCommissionSettleApi.countPopCommissionSettle(commissionSettlementQueryDTO);
            log.info("popCommissionSettleApi.countPopCommissionSettle,queryDTO:{},return:{}",
                    JSONObject.toJSONString(commissionSettlementQueryDTO), JSONObject.toJSONString(countRPCResult));
            if (countRPCResult.isFail()) {
                return null;
            }
            return countRPCResult.getData();
        } catch (Exception e) {
            log.error("查询佣金结算列表数量异常,queryDto:{}", JSONObject.toJSONString(commissionSettlementQueryDTO),e);
            throw new ServiceException("查询佣金结算列表数量异常");
        }
    }

    public Long countPopCommissionSettleBill(List<String> hireNoList) throws ServiceException {
        try {
            ApiRPCResult<Long> billSizeRPCResult = popCommissionSettleApi.countPopCommissionSettleBill(hireNoList);
            log.info("popCommissionSettleApi.countPopCommissionSettleBill,hireNoList:{},return:{}",
                    hireNoList, JSONObject.toJSONString(billSizeRPCResult));
            if (billSizeRPCResult.isFail()) {
                return null;
            }
            return billSizeRPCResult.getData();
        } catch (Exception e) {
            log.error("导出佣金结算详情账单查询账单数量异常,hireNoList:{}", hireNoList ,e);
            throw new ServiceException("导出佣金结算详情账单查询账单数量异常");
        }
    }

    public List<PopCommissionSettleBillDto> queryCommissionSettleBillListByHireNoList(List<String> hireNos) throws ServiceException {
        try {
            log.info("popCommissionSettleApi.queryCommissionSettleBillPoListByHireNoList(hireNos:{})",hireNos);
            ApiRPCResult<List<PopCommissionSettleBillDto>> settleBillDtoRPCResult = popCommissionSettleApi.queryCommissionSettleBillPoListByHireNoList(hireNos);
            log.info("popCommissionSettleApi.queryCommissionSettleBillPoListByHireNoList,hireNos:{},return:{}",
                    hireNos, JSONObject.toJSONString(settleBillDtoRPCResult));
            if(settleBillDtoRPCResult.isFail()){
                return Lists.newArrayList();
            }
            return settleBillDtoRPCResult.getData();
        } catch (Exception e) {
            log.error("popCommissionSettleApi.queryCommissionSettleBillPoListByHireNoList,hireNos:{},异常",hireNos, e);
            throw new ServiceException("查询佣金账单列表异常");
        }
    }
}
