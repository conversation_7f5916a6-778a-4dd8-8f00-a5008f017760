package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 账期状态
 * <AUTHOR>
 */
public enum AccountPeriodEnum {

    RECEIVE_STATUS(1,"已收款"),
    NOT_RECEIVE_STATUS(2,"未收款"),
    NO_AUDITING_STATUS(1,"待审核"),
    AUDITING_STATUS(2,"已审核"),
    NOT_AUDITING_STATUS(0,"未通过");
    private  int id;
    private  String value;
    AccountPeriodEnum(int id, String value){
        this.id = id;
        this.value = value;
    }
    private static Map<Integer, AccountPeriodEnum> controlMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(AccountPeriodEnum apEnum : AccountPeriodEnum.values()) {
        	controlMaps.put(apEnum.getId(), apEnum);
            maps.put(apEnum.getId(),apEnum.getValue());
        }
    }
    public static String get(int id) {
        return controlMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
