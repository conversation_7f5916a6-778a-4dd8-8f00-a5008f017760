package com.xyy.ec.pop.marketing.vo;

import com.xyy.ec.marketing.elephant.results.MarketingGroupBuyingInitiatorInfoDTO;
import com.xyy.ec.pop.model.KeyValueDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
*   拼团活动阶梯价dto
*/
@Data
public class MarketingActivityLevelVo implements java.io.Serializable {

   /**主键ID**/
   private Long id;
   /**活动编号**/
   private Long marketingId;
    /**阶梯等级 从1开始**/
    private Integer level;
    /**促销起始数量**/
    private Integer startQty;
    /** 发起列表JSON */
    private String initiatorJson;
    /**
     * 平台补贴费用承担方及金额信息列表
     */
    private String subsidyInitiatorJson;
    /**
     * 费用承担方及金额信息列表
     */
    private List<MarketingGroupBuyingInitiatorInfoDTO> initiators;

    /**
     * 平台补贴费用承担方及金额信息列表
     */
    private List<MarketingGroupBuyingInitiatorInfoDTO> subsidyInitiators;
    /**拼团价格**/
    private BigDecimal discountPrice;
    /**状态:1:生效 2 无效**/
    private Integer valid;
   /**创建时间**/
   private java.util.Date ctime;
   /**创建人**/
   private String creator;
   /**更新时间**/
   private java.util.Date utime;
   /**更新人**/
   private String updater;
    /**
     * 店铺补贴
     */
    private BigDecimal shopSubsidy;

    /**
     * 运营补贴列表
     */
    private List<KeyValueDTO> operationSubsidies;

}
