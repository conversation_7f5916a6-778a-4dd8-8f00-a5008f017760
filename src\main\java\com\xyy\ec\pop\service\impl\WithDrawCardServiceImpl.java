package com.xyy.ec.pop.service.impl;

import com.xyy.ec.pop.helper.WithDrawCardHelper;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.WithDrawCardRemoteAdapter;
import com.xyy.ec.pop.server.api.merchant.dto.WithDrawCardDto;
import com.xyy.ec.pop.server.api.shop.dto.PopCheckLogDto;
import com.xyy.ec.pop.service.WithDrawCardService;
import com.xyy.ec.pop.vo.WithDrawCardVo;
import com.xyy.ec.shop.server.business.enums.ShopStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName CashAccountServiceImpl
 * <AUTHOR>
 * @Date 2024/5/13 18:45
 * @Version 1.0
 */

@Service
@Slf4j
public class WithDrawCardServiceImpl implements WithDrawCardService {

    @Autowired
    private WithDrawCardRemoteAdapter withDrawCardRemoteAdapter;

    @Override
    public void addOrUpdateWithDrawCard(WithDrawCardVo withDrawCardVo, Long cid, SysUser user) {
        WithDrawCardDto withDrawCardDto = WithDrawCardHelper.convertToDto(withDrawCardVo);
        PopCheckLogDto popCheckLogDto = new PopCheckLogDto();
        popCheckLogDto.setTableId(cid);
        popCheckLogDto.setTableName("checkPopUpdateShopStatus");
        popCheckLogDto.setCreateId(Long.parseLong(user.getOaId()));
        popCheckLogDto.setCreateName(user.getUsername());
        popCheckLogDto.setState((byte) ShopStatusEnum.CHANGE_CASH_ACCOUNT.getType());
        withDrawCardRemoteAdapter.addOrUpdateWithDrawCard(withDrawCardDto,popCheckLogDto);
    }

    @Override
    public List<WithDrawCardVo> selectWithDrawCardByOrgId(String orgId) {
        List<WithDrawCardDto> withDrawCardDtoList = withDrawCardRemoteAdapter.selectWithDrawCardByOrgId(orgId);
        if(CollectionUtils.isEmpty(withDrawCardDtoList)){
            return Lists.newArrayList();
        }
       return withDrawCardDtoList.stream().map(withDrawCardDto -> {
            WithDrawCardVo withDrawCardVo = new WithDrawCardVo();
            BeanUtils.copyProperties(withDrawCardDto, withDrawCardVo);
            return withDrawCardVo;
        }).collect(Collectors.toList());
    }
}