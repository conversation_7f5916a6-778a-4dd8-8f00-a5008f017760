package com.xyy.ec.pop.utils.easyexcel;

import com.alibaba.excel.EasyExcel;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * easyExcel的简单封装
 * <AUTHOR>
 */
public class EasyExcelUtil {

    private static final Logger logger = LoggerFactory.getLogger(EasyExcelUtil.class);

    /**
     * 读取第一个 sheet 的 Excel
     *
     * @param stream    文件
     * @param tClass 实体类映射，
     * @return Excel 数据 list
     */
    public  static <T>  List<T> readExcel(InputStream stream, Class<T> tClass) {
       return readExcel(stream,tClass,1);
    }

    /**
     * 读取某个 sheet 的 Excel
     *
     * @param stream    文件
     * @param tClass  实体类映射
     * @param sheetNo sheet 的序号 从1开始 /sheetNO 为空则读取第一个sheetNO
     * @return Excel 数据 list
     */
    public  static <T>  List<T> readExcel(InputStream stream, Class<T> tClass,Integer sheetNo) {
        if (Objects.isNull(sheetNo)||sheetNo==0){
            sheetNo=1;
        }
        EasyExcelListener<T> listener=new EasyExcelListener<T>();

        //easyExcel sheetNo从零开始
        EasyExcel.read(stream, tClass, listener).sheet(sheetNo-1).doRead();
        return listener.getDatas();
    }

    /**
     * 导出 Excel ：一个 sheet，带表头
     *
     * @param outputStream  OutputStream
     * @param list      数据 list，
     * @param clazz   导出结构体
     * @param sheetName 导入文件的 sheet 名
     */
    public static <T> void writeExcel(OutputStream outputStream,List<T> list,Class clazz,String sheetName) {
        EasyExcel.write(outputStream,clazz).sheet(sheetName).doWrite(list);
    }
    /**
     * 导出 Excel ：一个 sheet，带表头
     *
     * @param pathName  OutputStream
     * @param list      数据 list，
     * @param clazz   导出结构体
     * @param sheetName 导入文件的 sheet 名
     */
    public static <T> void writeExcel(String pathName,List<T> list,Class clazz,String sheetName) {
        EasyExcel.write(pathName,clazz).sheet(sheetName).doWrite(list);
    }

    /**
     * 导出 Excel 直接可以在Spring web Controller 中使用
     *
     * @param response  HttpServletResponse
     * @param fileName  文件名
     * @param list      数据 list，
     * @param clazz   导出结构体
     * @param sheetName 导入文件的 sheet 名
     */
    public static <T> void writeExcel(HttpServletResponse response, String fileName, List<T> list, Class<T> clazz, String sheetName)  {
        try{
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileNameEnCode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEnCode + ".xlsx");
            writeExcel(response.getOutputStream(),list,clazz,sheetName);

        }catch (Exception e){
         logger.warn("执行EXCEL导出异常",e);
        }

    }

    /**
     * 导出 Excel ：一个 sheet，带表头
     *
     * @param list      数据 list，
     * @param clazz   导出结构体
     * @param sheetName 导入文件的 sheet 名
     * @param excludeColumnList 指定那些列不导出
     */
    public static <T> void writeExcel(HttpServletResponse response,String fileName,List<T> list,Class clazz,String sheetName,List<String> excludeColumnList)  {
        try{
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileNameEnCode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEnCode + ".xlsx");
            EasyExcel.write(response.getOutputStream(),clazz).excludeColumnFiledNames(excludeColumnList).sheet(sheetName).doWrite(list);
        }catch (Exception e){
          logger.warn("执行EXCEL导出异常",e);
        }


    }

    /**
     * 默认 CellStyle
     * 居中、单元格的自动换行、黑色字体、加粗
     *
     * @param wb
     * @return
     */
    public static CellStyle generateTemplateDefaultStyle(Workbook wb) {
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setWrapText(true);
        Font titleFont = wb.createFont();
        titleFont.setBold(true);
        titleFont.setColor(IndexedColors.BLACK.getIndex());
        cellStyle.setFont(titleFont);
        return cellStyle;
    }
}
