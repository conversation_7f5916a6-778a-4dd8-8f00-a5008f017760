package com.xyy.ec.pop.service.settle;

import com.xyy.ec.pop.po.PopBillDetailPo;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/1 10:41
* @table
*/
public interface PopBillDetailService {
    int insert(PopBillDetailPo record);

    int insertSelective(PopBillDetailPo record);

    PopBillDetailPo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PopBillDetailPo record);

    int updateByPrimaryKey(PopBillDetailPo record);

    List<PopBillDetailPo> queryPopBillDetail(PopBillDetailPo popBillDetailPo, Integer pageNum, Integer pageSize);

    Long queryPopBillDetailCount(List<String> billNoList);

    List<PopBillDetailPo> queryPopBillDetailByBillNoList(List<String> billNoList);

    String queryPopBillNoByBusinessNo(String BusinessNo);
}
