package com.xyy.ec.pop.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.model.CorporationAndUserExtVo;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationAndUserExtDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.vo.CooperationInfoVo;
import com.xyy.ec.pop.vo.ResponseVo;

import java.util.List;

/**
 * @version v1
 * @Description 店铺信息，替换 CorporationService
 * <AUTHOR>
 */
public interface PopCorporationService {
    CooperationInfoVo cooperationInfo(String orgId) throws ServiceException;

    ResponseVo<PageInfo<CorporationAndUserExtVo>> queryShopInfoShopCodes(PageInfo<CorporationAndUserExtDto> data);

    ResponseVo<Boolean> updateShopStatusByShopCode(SysUser user, String shopCode, Integer status,String remark) throws ServiceException;

    /**
     * 设置可售区域、非可售区域、佣金比例
     */
    void setDrugAreaInfo(List<CorporationAndUserExtDto> extDtos);

    /**
     * 设置店铺下线
     * @param corporationDto
     */
    ResponseVo<Boolean> offlineShop(SysUser user,CorporationDto corporationDto);

    /**
     * 更新店铺经营属性
     *
     * @param orgId
     * @param businessAttribute
     * @param id
     * @param user
     * @return
     */
    int updateBusinessAttribute(String orgId, Byte businessAttribute, Long id, String user);

    /**
     * 更新店铺商圈属性
     * @param orgId
     * @param shopCategory
     * @param id
     * @param user
     */
    void updateShopCategory(String orgId, Integer shopCategory, Long id, String user);

    void updateSettleCycle(String orgId, Integer settleCycle, Integer settleValue, String operator);
}
