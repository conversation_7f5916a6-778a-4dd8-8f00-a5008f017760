<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<html>
<head>
    <%@include file="../include/common.jsp" %>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>退款订单管理</title>
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no,minimal-ui"/>
    <meta name="keywords" content=""/>
    <meta name="description" content=""/>
    <link rel="stylesheet" href="${basePathUrl}/css/common/table-index.css"/>
    <style>
        /* 级联选择器样式 */
        .multi-select-container {
            position: relative;
            display: inline-block;
            min-width: 200px;
        }

        .multi-select-display {
            border: 1px solid #ccc;
            padding: 5px;
            min-height: 25px;
            cursor: pointer;
            background: white;
        }

        .multi-select-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            border: 1px solid #ccc;
            background: white;
            z-index: 1000;
        }

        .cascade-container {
            display: flex;
            min-height: 200px;
            max-height: 300px;
        }

        .cascade-left-panel {
            width: 200px;
            border-right: 1px solid #eee;
            overflow-y: auto;
        }

        .cascade-right-panel {
            width: 200px;
            overflow-y: auto;
            background-color: #fafafa;
        }

        .multi-select-checkbox {
            display: block;
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            margin: 0;
            transition: background-color 0.2s;
        }

        .multi-select-checkbox:hover {
            background-color: #f5f5f5;
        }

        .cascade-parent {
            position: relative;
        }

        .cascade-parent.cascade-active {
            background-color: #e6f7ff;
            border-color: #91d5ff;
        }

        .cascade-arrow {
            color: #999;
            font-size: 12px;
        }

        .cascade-children-panel {
            height: 100%;
        }

        .cascade-children-panel .multi-select-checkbox {
            border-bottom: 1px solid #eee;
            background-color: white;
        }

        .cascade-children-panel .multi-select-checkbox:hover {
            background-color: #f5f5f5;
        }

        .cascade-children-panel .multi-select-checkbox:last-child {
            border-bottom: none;
        }
    </style>
    <script src="${basePathUrl}/js/bootstrap/bootstrap-table.js"></script>
    <script src="${basePathUrl}/js/bootstrap/bootstrap-table-zh-CN.js"></script>
    <link rel="stylesheet" href="${basePathUrl}/js/plugins/datetimepicker/css/bootstrap-datetimepicker.min.css"/>
    <script src="${basePathUrl}/js/plugins/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
    <script src="${basePathUrl}/js/plugins/datetimepicker/js/locales/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="${basePathUrl}/js/refundorder/index.js?t=${t_v}"></script>
</head>
<body>
<div class="panel-body" style="padding-bottom: 0px;margin-top: 0px;padding: 0;">
    <div class="top_input">
        <div class="top_input_name" for="txt_barcode">&nbsp;&nbsp;&nbsp;&nbsp;销售单号：</div>
        <div class="top_input_div"><input type="text" name="orderNo" id="orderNo" style="width: 180px;"></div>

        <div class="top_input_name">&nbsp;&nbsp;&nbsp;&nbsp;支付类型：</div>
        <div class="top_input_div">
            <select id="payType" name="payType" style="width: 100px;border: 0 none;outline: medium none;">
                <option value="" selected="selected">全部</option>
                <option value="1">在线支付</option>
                <option value="2">货到付款</option>
                <option value="3">线下转账</option>
            </select>
        </div>
        <div class="top_input_name">&nbsp;&nbsp;&nbsp;&nbsp;退款状态：</div>
        <div class="top_input_div">
            <select id="auditState" name="auditState" style="border: 0 none;outline: medium none;">
                <option value="" selected="selected">全部</option>
                <option value="7">待客户确认</option>
                <option value="8">客户已拒绝</option>
                <option value="0">待审核</option>
                <option value="3">审核通过，待退货入库</option>
                <option value="4">入库完成，待财务退款</option>
                <option value="5">入库完成，待会计审核</option>
                <option value="1">退款成功</option>
                <option value="-1">退款关闭</option>
                <option value="10">财务经理审核中</option>
                <option value="11">待资金确认</option>
                <option value="12">付款中</option>
            </select>
        </div>

        <div class="top_input_name">&nbsp;&nbsp;&nbsp;&nbsp;客户收货省份：</div>
        <div class="top_input_div">
            <select id="branchCode" name="branchCode" style="width: 100px;border: 0 none;outline: medium none;">
                <option value="" selected="selected">全国</option>
                <c:forEach var="branch" items="${branchList}" >
                    <option value="${branch.branchCode}">${branch.branchName}</option>
<%--                    <option value="${branch.branchCode}" <c:if test="${'XS000000' eq branch.branchCode}">selected="selected"</c:if>>${branch.branchName}</option>--%>
                </c:forEach>
            </select>
        </div>
        <div class="top_input_name">&nbsp;&nbsp;&nbsp;&nbsp;商户注册省份：</div>
        <div class="top_input_div">
            <select id="provId" name="provId" style="width: 100px;border: 0 none;outline: medium none;"></select>
        </div>
        <div class="top_input_name" for="txt_search_orderNo">&nbsp;&nbsp;&nbsp;&nbsp;商户编号：</div>
        <div class="top_input_div" ><input type="text" id="corporationNo" style="width: 180px;" ></div>
        <div class="top_input_name" for="txt_search_orderNo">&nbsp;&nbsp;&nbsp;&nbsp;商户名称：</div>
        <div class="top_input_div" ><input type="text" id="companyName" style="width: 180px;"></div>
        <div class="top_input_name" for="txt_search_orderNo">&nbsp;&nbsp;&nbsp;&nbsp;店铺名称：</div>
        <div class="top_input_div" ><input type="text" id="corporationName" style="width: 180px;" ></div>
    </div>
    <div class="top_input">
        <div class="top_input_name" for="txt_refundOrderNo">&nbsp;&nbsp;&nbsp;&nbsp;退款单号：</div>
        <div class="top_input_div"><input type="text" name="refundOrderNo" id="refundOrderNo" style="width: 180px;"></div>
        <div class="top_input_name" for="txt_common_name">&nbsp;&nbsp;&nbsp;&nbsp;下单时间：</div>
        <div class="top_input_div"><input type="text" name="startTime" id="startTime"></div>
        <div class="top_input_name">~</div>
        <div class="top_input_div"><input type="text" name="endTime" id="endTime"></div>
        <div class="top_input_name" for="txt_barcode">&nbsp;&nbsp;&nbsp;&nbsp;客户名称：</div>
        <div class="top_input_div"><input type="text" name="merchantName" id="merchantName"></div>
        <div class="top_input_name" for="txt_common_name">&nbsp;&nbsp;&nbsp;&nbsp;申请退款时间：</div>
        <div class="top_input_div"><input type="text" name="startCreateTime" id="startCreateTime" value="${defaultStartCreateTime}"></div>
        <div class="top_input_name">~</div>
        <div class="top_input_div"><input type="text" name="endCreateTime" id="endCreateTime"></div>
        <div class="top_input_name">&nbsp;&nbsp;&nbsp;&nbsp;发起方：</div>
        <div class="top_input_div">
            <select id="refundChannel" name="refundChannel" style="border: 0 none;outline: medium none;">
                <option value="" selected="selected">全部</option>
                <option value="1">用户发起</option>
                <option value="2">平台发起</option>
                <option value="4">商家发起</option>
                <option value="5">系统发起</option>
            </select>
        </div>
        <div class="top_input_name">&nbsp;&nbsp;&nbsp;&nbsp;退款原因：</div>
        <div class="top_input_div">
            <div class="multi-select-container">
                <!-- 显示区域 -->
                <div id="refundReasonDisplay" class="multi-select-display" 
                    onclick="toggleRefundReasonDropdown()">
                    <span id="refundReasonText" style="color: #999;">全部</span>
                </div>
                <!-- 下拉选项 -->
                <div id="refundReasonDropdown" class="multi-select-dropdown">
                    <!-- 选项将通过JavaScript动态生成 -->
                </div>
                <input type="hidden" id="refundReason" name="refundReason" value="">
            </div>
            <!-- <select id="refundReason" name="refundReason" style="border: 0 none;outline: medium none;">
                <option value="" selected="selected">全部</option>
                <option value="未享受优惠/价格高">未享受优惠/价格高</option>
                <option value="下错了/下漏了">下错了/下漏了</option>
                <option value="药店资质问题">药店资质问题</option>
                <option value="超出药店经营范围">超出药店经营范围</option>
                <option value="商品近效期">商品近效期</option>
                <option value="商品缺货">商品缺货</option>
                <option value="批号不符">批号不符</option>
                <option value="破损或质量问题">破损或质量问题</option>
                <option value="商品发错">商品发错</option>
                <option value="商品发货少货">商品发货少货</option>
                <option value="商品实物与展示不符">商品实物与展示不符</option>
                <option value="到货慢">到货慢</option>
                <option value="退运费">退运费</option>
                <option value="其他">其他</option>
            </select> -->
        </div>
        <div class="top_input_name">&nbsp;&nbsp;支付渠道：</div>
        <div class="top_input_div">
            <select id="payChannel" name="payChannel">
                <option value="" selected="selected">全部</option>
                <option value="1">支付宝</option>
                <option value="2">微信</option>
                <option value="3">银联</option>
                <option value="7">电汇平台</option>
                <option value="8">电汇商业</option>
                <option value="10">平安ePay</option>
                <option value="11">JD银行卡支付</option>
                <option value="12">京东采购融资</option>
                <option value="13">农行链e贷</option>
                <option value="14">小雨点白条</option>
                <option value="15">金蝶信用付</option>
            </select>
        </div>
        <div class="top_input_name" for="txt_common_name">&nbsp;&nbsp;&nbsp;&nbsp;退款时间：</div>
        <div class="top_input_div"><input type="text" name="startRefundFinishTime" id="startRefundFinishTime"></div>
        <div class="top_input_name">~</div>
        <div class="top_input_div"><input type="text" name="endRefundFinishTime" id="endRefundFinishTime"></div>

        <div class="top_input_name">&nbsp;&nbsp;是否入仓订单：</div>
        <div class="top_input_div">
            <select id="isFbp" name="isFbp">
                <option value="" selected="selected">全部</option>
                <option value="1">是</option>
                <option value="0">否</option>
            </select>
        </div>

        <div class="top_input_name">&nbsp;&nbsp;退款支付状态：</div>
        <div class="top_input_div">
            <select id="payStatus" name="payStatus">
                <option value="" selected="selected">全部</option>
                <option value="2">退款成功</option>
                <option value="3">退款失败</option>
                <option value="4">退款中</option>
            </select>
        </div>
        <div class="top_input_name">&nbsp;&nbsp;是否第三方厂家：</div>
        <div class="top_input_div">
            <select id="txt_search_isThirdCompany" name="txt_search_isThirdCompany">
                <option value="1" selected="selected">是</option>
                <option value="0">否</option>
            </select>
        </div>
    </div>
    <div class="top_input">
        <div class="top_input_button" style="margin-left: 50px;" id="btn_query">查询</div>
        <div class="top_input_button white_button" id="btn_clear">清空</div>
        <div class="top_input_button" id="btn_view"><a href="javascript:void(0);" ><font color="white">查看详情</font></a></div>
        <div class="top_input_button" id="btn_refund">确认退款</div>
        <div class="top_input_button" id="btn_export">导出</div>
    </div>
    <div style="width:98%;margin: -5px auto 0;">
        <table id="tb_pendingList"></table>
    </div>
</div>
</body>
</html>
