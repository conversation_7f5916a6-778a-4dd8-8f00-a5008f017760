package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.export.param.CashAdvanceAdminParam;
import com.xyy.ec.pop.server.api.seller.api.PopCashAdvanceApi;
import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceDto;
import com.xyy.ec.pop.server.api.seller.enums.PopCashAdvanceStatusEnum;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import com.xyy.pop.framework.core.vo.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 提现报表
 */
@Controller
@RequestMapping("/cashAdvance")
public class CashAdvanceController extends BaseController {
    private static final Logger LOG = LoggerFactory.getLogger(CashAdvanceController.class);

    @Reference
    private PopCashAdvanceApi popCashAdvanceApi;

    /**
     * 提现加载页面
     * @return
     */
    @RequestMapping(value = "/index" ,method = RequestMethod.GET)
    public String index(){
        return "cashAdvance/index";
    }

    /**
     * 提现报表列表
     * @param limit
     * @param offset
     * @param cashAdvanceDTO
     * @return
     */
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    @ResponseBody
    public Response<PageInfo> cashAdvanceList(Integer limit , Integer offset , CashAdvanceAdminParam cashAdvanceDTO){
        try {
            cashAdvanceDTO.setPaymentStatus(Integer.valueOf(PopCashAdvanceStatusEnum.SUCCESS.getStatus()).byteValue());
            cashAdvanceDTO.setEndCreateTime(DateUtil.date2Str(DateUtil.modifyEndTime(DateUtil.string2Date(cashAdvanceDTO.getEndCreateTime(),DateUtil.PATTERN_DATE)),DateUtil.PATTERN_STANDARD));
            ApiRPCResult<PageInfo<PopCashAdvanceDto>> pageInfoApiRPCResult = popCashAdvanceApi.adminQueryPage(limit, offset, cashAdvanceDTO);
            if (pageInfoApiRPCResult.isSuccess()){
                return ResponseUtils.returnObjectSuccess(pageInfoApiRPCResult.getData());
            }
        }catch (Exception e){
            LOG.info("cashAdvanceService.cashAdvanceList(limit :{},offset:{},cashAdvanceDTO:{})",limit,offset,cashAdvanceDTO.toString(),e);
        }
        return ResponseUtils.returnSuccess();
    }



}
