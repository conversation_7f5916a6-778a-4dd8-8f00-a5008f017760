package com.xyy.ec.pop.helper;

import com.google.common.collect.Lists;
import com.xyy.ec.pop.enums.ShopStatusEnum;
import com.xyy.ec.pop.model.*;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationAndUserExtDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationBusinessDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationQualificationDto;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 2020/12/01
 */
public class CorporationConverHelper {

    public static List<CorporationAndUserExtVo> convertCorporationAndUserExtDtosToVoList(List<CorporationAndUserExtDto> dtos){
        if(CollectionUtils.isEmpty(dtos)){
            return Lists.newArrayList();
        }
        return dtos.stream().map(CorporationConverHelper::convertCorporationAndUserExtDtoToVo).collect(Collectors.toList());
    }

    public static CorporationAndUserExtVo convertCorporationAndUserExtDtoToVo(CorporationAndUserExtDto dto){
        if(Objects.isNull(dto)){
            return null;
        }
        CorporationAndUserExtVo extVo = new CorporationAndUserExtVo();
        extVo.setId(dto.getId());
        extVo.setOrgId(dto.getOrgId());
        extVo.setName(dto.getName());
        extVo.setCompanyName(dto.getCompanyName());
        extVo.setRegCode(dto.getRegCode());
        extVo.setCorporat(dto.getCorporat());
        extVo.setPhone(dto.getPhone());
        extVo.setFixedPhone(dto.getFixedPhone());
        extVo.setEmail(dto.getEmail());
        extVo.setWeb(dto.getWeb());
        extVo.setProvId(dto.getProvId());
        extVo.setProv(dto.getProv());
        extVo.setCityId(dto.getCityId());
        extVo.setCity(dto.getCity());
        extVo.setAreaId(dto.getAreaId());
        extVo.setArea(dto.getArea());
        extVo.setAddr(dto.getAddr());
        extVo.setLogoUrl(dto.getLogoUrl());
        extVo.setBrief(dto.getBrief());
        extVo.setRemarks(dto.getRemarks());
        extVo.setState(dto.getState());
        extVo.setDel(dto.getDel());
        extVo.setCreateTime(dto.getCreateTime());
        extVo.setCreateId(dto.getCreateId());
        extVo.setCreateName(dto.getCreateName());
        extVo.setUpdateTime(dto.getUpdateTime());
        extVo.setUpdateId(dto.getUpdateId());
        extVo.setUpdateName(dto.getUpdateName());
        extVo.setSearch(dto.getSearch());
        extVo.setCorporationType(dto.getCorporationType());
        extVo.setCustomerServicePhone(dto.getCustomerServicePhone());
        extVo.setErpProcessId(dto.getErpProcessId());
        extVo.setBond(dto.getBond());
        extVo.setStreetId(dto.getStreetId());
        extVo.setStreetName(dto.getStreetName());
        extVo.setErpNumber(dto.getErpNumber());
        extVo.setStatus(dto.getStatus());
        extVo.setUserId(dto.getUserId());
        extVo.setSupplierId(dto.getSupplierId());
        extVo.setUserName(dto.getUserName());
        extVo.setPassword(dto.getPassword());
        extVo.setRegMobile(dto.getRegMobile());
        extVo.setRealName(dto.getRealName());
        extVo.setCreateBy(dto.getCreateBy());
        extVo.setLastModifyTime(dto.getLastModifyTime());
        extVo.setLastModifyBy(dto.getLastModifyBy());
        extVo.setUserStatus(dto.getUserStatus());
        extVo.setIsAdmin(dto.getIsAdmin());
        extVo.setSettlementType(dto.getSettlementType());
        extVo.setShopCode(dto.getShopCode());
        //店铺默认待上线
        extVo.setShopStatus(ShopStatusEnum.WAITING_ONLINE.getCode());
        extVo.setShopStatusStr(ShopStatusEnum.WAITING_ONLINE.getValue());
        extVo.setPaymentChannel(dto.getPaymentChannel());
        extVo.setTransferTo(dto.getTransferTo());
        extVo.setPopDeliveryStatus(dto.getPopDeliveryStatus());
        extVo.setSignaturesStatus(dto.getSignaturesStatus());
        extVo.setSignaturesStatusStr(dto.getSignaturesStatusStr());
        extVo.setFddAdminStatus(dto.getFddAdminStatus());
        extVo.setFddAdminStatusStr(dto.getFddAdminStatusStr());
        extVo.setFddAdminPhone(dto.getFddAdminPhone());
        extVo.setSettleWithdrawType(dto.getSettleWithdrawType());
        extVo.setShopCategory(dto.getShopCategory());
        return extVo;
    }






}
