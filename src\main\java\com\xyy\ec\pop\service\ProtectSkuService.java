package com.xyy.ec.pop.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.server.api.product.dto.ProtectSkuExcelDto;
import com.xyy.ec.pop.vo.ProtectSkuExcelVo;

import java.util.List;

/**
 * @version v1
 * @Description 保护品种
 * <AUTHOR>
 */
public interface ProtectSkuService {
    /**
     * 更新保护品种
     * @param path
     * @param fileName
     * @param ids
     * @param user
     */
    void update(String path, String fileName, List<Long> ids, SysUser user) throws ServiceException;

    /**
     * 分页列表
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageInfo<ProtectSkuExcelVo> page(Integer pageNum, Integer pageSize) throws ServiceException;
}
