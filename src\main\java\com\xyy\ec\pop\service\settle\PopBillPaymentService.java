package com.xyy.ec.pop.service.settle;

import com.xyy.ec.pop.po.PopBillPaymentPo;
import com.xyy.ec.pop.vo.settle.PopBillPayStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillPayVo;
import com.xyy.ec.pop.vo.settle.PopBillPayVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/1 10:46
* @table
*/
public interface PopBillPaymentService {

    int insert(PopBillPaymentPo record);

    int insertSelective(PopBillPaymentPo record);

    PopBillPaymentPo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PopBillPaymentPo record);

    int updateByPrimaryKey(PopBillPaymentPo record);

    List<PopBillPaymentPo> queryPopBillList(PopBillPayVo popBillPayVo, Integer pageNum, Integer pageSize);

    Long queryPopBillListCount(PopBillPayVo popBillPayVo);

    PopBillPayStatisVo queryPopBillPayStatis(PopBillPayVo popBillPayVo);

    PopBillPaymentPo selectByFlowNo(String flowNo);

    List<String> queryByOrderNos (PopBillPaymentPo popBillPaymentPo);

    List<PopBillPaymentPo> queryPopBillPayByFlowNoList(List<String> flowNoList);

    void batchUpdateById(List<PopBillPaymentPo> popBillPaymentPos);
}
