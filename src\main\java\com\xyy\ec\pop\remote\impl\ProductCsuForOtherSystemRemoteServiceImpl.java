package com.xyy.ec.pop.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.remote.ProductCsuForOtherSystemRemoteService;
import com.xyy.ec.product.back.end.ecp.csu.api.CsuForOtherSystemApi;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuDTO;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuQuery;
import com.xyy.ec.product.back.end.ecp.priceGroup.api.PriceMerchantGroupApi;
import com.xyy.ec.product.back.end.ecp.priceGroup.dto.PriceMerchantGroupDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductCsuForOtherSystemRemoteServiceImpl implements ProductCsuForOtherSystemRemoteService {

    @Reference(version = "1.0.0")
    private CsuForOtherSystemApi csuForOtherSystemApi;
    @Reference(version = "1.0.0")
    private PriceMerchantGroupApi priceMerchantGroupApi;

    @Override
    public List<CsuDTO> listCsuInfosByCsuIds(List<Long> csuIds) {
        if (CollectionUtils.isEmpty(csuIds)) {
            return Lists.newArrayList();
        }
        csuIds = Lists.newArrayList(Sets.newHashSet(csuIds));
        if (CollectionUtils.isEmpty(csuIds)) {
            return Lists.newArrayList();
        }
        List<CsuDTO> result = Lists.newArrayListWithExpectedSize(csuIds.size());
        List<List<Long>> partitions = Lists.partition(csuIds, 100);
        CsuQuery csuQuery;
        PageInfo<CsuDTO> pageInfo;
        for (List<Long> partition : partitions) {
            csuQuery = new CsuQuery();
            csuQuery.setSkuIdList(partition);
            csuQuery.setOffset(0);
            csuQuery.setIsNeedSaleTime(false);
            csuQuery.setLimit(partition.size());
            pageInfo = csuForOtherSystemApi.queryPageCsuList(csuQuery);
            if (Objects.nonNull(pageInfo) && CollectionUtils.isNotEmpty(pageInfo.getList())) {
                result.addAll(pageInfo.getList());
            }
        }
        return result;
    }

    public List<PriceMerchantGroupDto> queryPriceMerchantGroupList(List<Long> groupIdList){
        log.info("ProductCsuForOtherSystemRemoteService.queryPriceMerchantGroupList param groupId:{}", JSON.toJSONString(groupIdList));
        try {
            ApiRPCResult<List<PriceMerchantGroupDto>> result = priceMerchantGroupApi.queryPriceMerchantGroupList(groupIdList);
            log.info("ProductCsuForOtherSystemRemoteService.queryPriceMerchantGroupList result:{}", JSON.toJSONString(result));
            if (result == null || result.isFail()) {
                return org.apache.commons.compress.utils.Lists.newArrayList();
            }
            return result.getData();
        }catch (Exception e){
            log.error("ProductCsuForOtherSystemRemoteService.queryPriceMerchantGroupList error",e);
            return org.apache.commons.compress.utils.Lists.newArrayList();
        }
    }
    @Override
    public Map<Long,PriceMerchantGroupDto> queryPriceMerchantGroupMap(List<Long> groupIdList){
        List<PriceMerchantGroupDto> priceMerchantGroupDtos = queryPriceMerchantGroupList(groupIdList);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(priceMerchantGroupDtos)){
            return Maps.newHashMap();
        }
        return priceMerchantGroupDtos.stream().collect(Collectors.toMap(PriceMerchantGroupDto::getId, Function.identity(), (oldKey, newKey) -> oldKey));
    }
}
