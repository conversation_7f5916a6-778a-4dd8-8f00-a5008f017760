package com.xyy.ec.pop.erpUtil.util;

import com.alibaba.fastjson.JSONArray;
import com.xyy.ec.pop.server.api.erpUtil.dto.PopCorporationDto;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @project_name: xyy-pop-admin
 * @Auther: Jincheng.Li
 * @Date: 2021/03/29/18:05
 * @Description:
 */
public class PopCorpOrgIdListHelper {
    public static List<PopCorporationDto> covertJsonToList(String source) {
        return JSONArray.parseArray(source, PopCorporationDto.class);
    }

    /**
     * Json字符串转列表，提取orgId集合
     *
     * @param source
     * @return
     */
    public static List<String> covertOrgId(String source) {
        List<PopCorporationDto> corporationDtoList = JSONArray.parseArray(source, PopCorporationDto.class);
        return CollectionUtils.isNotEmpty(corporationDtoList) ? corporationDtoList.stream().map(PopCorporationDto::getOrgId).collect(Collectors.toList()) : null;
    }
}
