package com.xyy.ec.pop.vo;

import lombok.Data;

import java.util.List;

/**
 * @version v1
 * @Description 合作信息
 * <AUTHOR>
 */
@Data
public class CooperationInfoVo {
    private CorporationVo corporation;
    private List<CorporationAreaInfoVo> drugAreas;
    private List<CorporationAreaInfoVo> nonDrugAreas;
    /**
     * 区域：合并drugAreas、nonDrugAreas
     */
    private List<CorporationAreaInfoVo> areas;
    private List<BusinessCategoryCommissionVo> commissions;
    private BondVo bond;
    //当月和次月的佣金结算方式
    private List<CommissionsSettlementTypeVo> settlementTypes;
    /**
     * 对公信息
     */
    private PopCorporationToCorTransferVo toCorTransferVo;
    /**
     *
     */
    private CorporationPriceTypeVo corporationPriceTypeVo;

    /**
     * 结算周期
     */
    private CorporationSettleCycleVo settleCycle;
}
