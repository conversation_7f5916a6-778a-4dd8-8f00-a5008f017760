package com.xyy.ec.pop.marketing.vo;

import com.xyy.ec.pop.model.KeyValueDTO;
import com.xyy.ms.promotion.business.common.constants.ActivityReportEnum;
import com.xyy.ms.promotion.business.enums.MarketingActivityCreateSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 拼团活动信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GroupBuyingInfoVO implements Serializable {

    /**
     * 活动ID
     */
    private Long actId;
    /**
     * 报名ID
     */
    private Long reportId;

    /**
     * 状态。1：待审核；2：审核不通过（可修改）；3：审核通过-未开始；4：进行中；5：已下线；6：已结束；
     *
     * @see ActivityReportEnum.ActReportIndexStatusEnum
     */
    private Integer status;

    /**
     * 状态名称。1：待审核；2：审核不通过（可修改）；3：审核通过-未开始；4：进行中；5：已下线；6：已结束；
     *
     * @see ActivityReportEnum.ActReportIndexStatusEnum
     */
    private String statusName;

    /**
     * 商户编号
     */
    private String orgId;
    /**
     * 商户名称
     */
    private String orgName;

    /**
     * 店铺编码
     */
    private String shopCode;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * CSUID
     */
    private Long csuId;
    /**
     * 商品编码
     */
    private String barcode;
    /**
     * 商品ERP编码
     */
    private String productCode;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 规格
     */
    private String spec;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 药帮忙价格
     */
    private BigDecimal fob;

    /**
     * 拼团准入价（前端使用）
     */
    private BigDecimal accessGroupPrice;
    /**
     * EC拼团准入价
     */
    private BigDecimal ecAccessGroupPrice;
    /**
     * POP拼团准入价
     */
    private BigDecimal popAccessGroupPrice;

    /**
     * 拼团价
     */
    private BigDecimal groupPrice;
    /**
     * 成团数量/起拼数量
     */
    private Integer groupNum;
    /**
     * 店铺补贴
     */
    private BigDecimal shopSubsidy;
    /**
     * 运营补贴列表
     */
    private List<KeyValueDTO> operationSubsidies;

    /**
     * 是否是复制商品建模，1是，2否；
     */
    private Integer isCopyCsuModel;
    /**
     * 是否复制原品销售范围，1是，2否；仅复制商品建模时有效；
     */
    private Integer isCopySaleArea;
    /**
     * 人群ID
     */
    private Long customerGroupId;
    /**
     * 人群名称
     */
    private String customerGroupName;

    /**
     * 人群定义
     */
    private List<List<String>> tagDef;

    /**
     * 库存是否同步ERP。0：否；1：是；
     */
    private Integer stockSyncErp;
    /**
     * 库存是否同步ERP描述。0：否；1：是；
     */
    private String stockSyncErpDesc;
    /**
     * 活动总限购，若不限购，则为null。
     */
    private Integer totalLimitNum;
    /**
     * 剩余活动库存，若不限购，则为null。
     */
    private Integer surplusStockNum;
    /**
     * 单客户限购数量，若不限购，则为null。
     */
    private Integer personalLimitNum;
    /**
     * 在途库存
     */
    private Integer onTheWayStock;

    /**
     * 拼团主题
     */
    private String theme;

    /**
     * 活动主题，若有多个则以英文逗号分隔。
     */
    private String topics;
    /**
     * 活动主题列表
     */
    private List<String> topicList;
    /**
     * 活动主题名称，若有多个则以中文顿号分隔。
     */
    private String topicNames;

    /**
     * 活动开始时间
     */
    private Date actStartTime;
    /**
     * 活动开始时间字符串，格式yyyy-MM-dd HH:mm:ss
     */
    private String actStartTimeStr;
    /**
     * 活动结束时间
     */
    private Date actEndTime;
    /**
     * 活动结束时间字符串，格式yyyy-MM-dd HH:mm:ss
     */
    private String actEndTimeStr;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建时间字符串，格式yyyy-MM-dd HH:mm:ss
     */
    private String createTimeStr;
    /**
     * 活动来源。1：EC后台、2：活动提报。
     *
     * @see MarketingActivityCreateSourceEnum
     */
    private Integer sourceType;
    /**
     * 活动来源名称。1：EC后台、2：活动提报。例如：活动提报
     *
     * @see MarketingActivityCreateSourceEnum
     */
    private String sourceTypeName;

    /**
     * 是否阶梯价 1:是 2:否
     */
    private Integer stepPriceStatus;

    /**
     * 是否阶梯价描述
     */
    private String stepPriceStatusDesc;

    /**
     * 关联活动阶梯价格列表
     **/
    private List<MarketingActivityLevelVo> marketingActivityLevelList;

    /**
     * 是否成单，1是，2否。
     *
     * @see com.xyy.ms.promotion.business.common.config.IsTrueEnum
     */
    private Integer isOrder;

    /**
     * 驳回原因
     */
    private String remark;

    /**销售区域**/
    private GroupBuyingSaleScopeVO groupBuyingSaleScopeVO;
}
