package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.excel.entity.OfflinePopBillDetailExportVo;
import com.xyy.ec.pop.excel.entity.OfflinePopBillExportVo;
import com.xyy.ec.pop.excel.entity.OnlinePayPopBillDetailExportVo;
import com.xyy.ec.pop.excel.entity.OnlinePayPopBillExportVo;
import com.xyy.ec.pop.po.PopBillDetailPo;
import com.xyy.ec.pop.po.PopBillPo;
import com.xyy.ec.pop.vo.settle.PopBillVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class PopBillHelper {

    public static PopBillPo convertPopBillPay(PopBillVo popBillVo) {
        if(null == popBillVo){
            return null;
        }
        PopBillPo popBillPo = new PopBillPo();
        popBillPo.setId(popBillVo.getId());
        popBillPo.setOrgId(popBillVo.getOrgId());
        popBillPo.setOrgName(popBillVo.getOrgName());
        popBillPo.setName(popBillVo.getName());
        popBillPo.setBillNo(popBillVo.getBillNo());
        popBillPo.setProductMoney(popBillVo.getProductMoney());
        popBillPo.setTotalMoney(popBillVo.getTotalMoney());
        popBillPo.setMoney(popBillVo.getMoney());
        popBillPo.setFreightAmount(popBillVo.getFreightAmount());
        popBillPo.setCouponShopAmount(popBillVo.getCouponShopAmount());
        popBillPo.setMarketingShopAmount(popBillVo.getMarketingShopAmount());
        popBillPo.setShopTotalDiscount(popBillVo.getShopTotalDiscount());
        popBillPo.setCouponPlatformAmount(popBillVo.getCouponPlatformAmount());
        popBillPo.setMarketingPlatformAmount(popBillVo.getMarketingPlatformAmount());
        popBillPo.setPlatformTotalDiscount(popBillVo.getPlatformTotalDiscount());
        popBillPo.setHireMoney(popBillVo.getHireMoney());
        popBillPo.setPayableCommission(popBillVo.getPayableCommission());
        popBillPo.setSettlementType(popBillVo.getSettlementType());
        popBillPo.setPenaltyAmount(popBillVo.getPenaltyAmount());
        popBillPo.setStatementTotalMoney(popBillVo.getStatementTotalMoney());
        popBillPo.setPayType(popBillVo.getPayType());
        popBillPo.setBillCreateTime(popBillVo.getBillCreateTime());
        popBillPo.setBillPaymentStatus(popBillVo.getBillPaymentStatus());
        popBillPo.setBillPaymentTime(popBillVo.getBillPaymentTime());
        popBillPo.setRemitStatus(popBillVo.getRemitStatus());
        popBillPo.setRemitTime(popBillVo.getRemitTime());
        popBillPo.setCreateTime(popBillVo.getCreateTime());
        popBillPo.setUpdateTime(popBillVo.getUpdateTime());
        popBillPo.setInvoiceStatus(popBillVo.getInvoiceStatus());
        popBillPo.setInvoiceTime(popBillVo.getInvoiceTime());
        popBillPo.setStartCreateTime(popBillVo.getStartCreateTime());
        popBillPo.setEndCreateTime(popBillVo.getEndCreateTime());
        popBillPo.setStartBillPaymentTime(popBillVo.getStartBillPaymentTime());
        popBillPo.setEndBillPaymentTime(popBillVo.getEndBillPaymentTime());
        popBillPo.setStartRemitTime(popBillVo.getStartRemitTime());
        popBillPo.setEndRemitTime(popBillVo.getEndRemitTime());
        popBillPo.setStartInvoiceTime(popBillVo.getStartInvoiceTime());
        popBillPo.setEndInvoiceTime(popBillVo.getEndInvoiceTime());
        popBillPo.setOrgIds(popBillVo.getOrgIds());
        popBillPo.setBillShareStatus(popBillVo.getBillShareStatus());
        popBillPo.setPaymentChannel(popBillVo.getPaymentChannel());
        popBillPo.setDeducted(popBillVo.getDeducted());
        return popBillPo;
    }

    public static List<OfflinePopBillExportVo> convetPopBillExportVo(List<PopBillPo> list) {
        List<OfflinePopBillExportVo> offlinePopBillExportVoList = new ArrayList<>();
        list.forEach(popBillPo->{
            OfflinePopBillExportVo offlinePopBillExportVo = new OfflinePopBillExportVo();
            offlinePopBillExportVo.setOrgId(popBillPo.getOrgId());
            offlinePopBillExportVo.setOrgName(popBillPo.getOrgName());
            offlinePopBillExportVo.setName(popBillPo.getName());
            offlinePopBillExportVo.setBillNo(popBillPo.getBillNo());
            offlinePopBillExportVo.setProductMoney(popBillPo.getProductMoney());
            offlinePopBillExportVo.setFreightAmount(popBillPo.getFreightAmount());
            offlinePopBillExportVo.setTotalMoney(popBillPo.getTotalMoney());
            offlinePopBillExportVo.setShopTotalDiscount(popBillPo.getShopTotalDiscount());
            offlinePopBillExportVo.setPlatformTotalDiscount(popBillPo.getPlatformTotalDiscount());
            offlinePopBillExportVo.setMoney(popBillPo.getMoney());
            offlinePopBillExportVo.setHireMoney(popBillPo.getHireMoney());
            offlinePopBillExportVo.setPayableCommission(popBillPo.getPayableCommission());
            offlinePopBillExportVo.setSettlementType(popBillPo.getSettlementType());
            offlinePopBillExportVo.setStatementTotalMoney(popBillPo.getStatementTotalMoney());
            offlinePopBillExportVo.setBillPaymentStatus(popBillPo.getBillPaymentStatus());
            offlinePopBillExportVo.setBillCreateTime(popBillPo.getBillCreateTime());
            offlinePopBillExportVo.setBillPaymentTime(popBillPo.getBillPaymentTime());
            offlinePopBillExportVo.setRemitStatus(popBillPo.getRemitStatus());
            offlinePopBillExportVo.setRemitTime(popBillPo.getRemitTime());
            offlinePopBillExportVo.setInvoiceStatus(popBillPo.getInvoiceStatus());
            offlinePopBillExportVo.setInvoiceTime(popBillPo.getInvoiceTime());
            offlinePopBillExportVoList.add(offlinePopBillExportVo);
        });
        return offlinePopBillExportVoList;
    }
    public static List<OnlinePayPopBillExportVo> convetOnlinePayPopBillExportVo(List<PopBillPo> list) {
        List<OnlinePayPopBillExportVo> onlinePayPopBillExportVoList = new ArrayList<>();
        list.forEach(popBillPo->{
            OnlinePayPopBillExportVo onlinePayPopBillExportVo = new OnlinePayPopBillExportVo();
            onlinePayPopBillExportVo.setOrgId(popBillPo.getOrgId());
            onlinePayPopBillExportVo.setOrgName(popBillPo.getOrgName());
            onlinePayPopBillExportVo.setName(popBillPo.getName());
            onlinePayPopBillExportVo.setBillNo(popBillPo.getBillNo());
            onlinePayPopBillExportVo.setProductMoney(popBillPo.getProductMoney());
            onlinePayPopBillExportVo.setFreightAmount(popBillPo.getFreightAmount());
            onlinePayPopBillExportVo.setTotalMoney(popBillPo.getTotalMoney());
            onlinePayPopBillExportVo.setShopTotalDiscount(popBillPo.getShopTotalDiscount());
            onlinePayPopBillExportVo.setPlatformTotalDiscount(popBillPo.getPlatformTotalDiscount());
            onlinePayPopBillExportVo.setMoney(popBillPo.getMoney());
            onlinePayPopBillExportVo.setHireMoney(popBillPo.getHireMoney());
            onlinePayPopBillExportVo.setStatementTotalMoney(popBillPo.getStatementTotalMoney());
            onlinePayPopBillExportVo.setBillPaymentStatus(popBillPo.getBillPaymentStatus());
            onlinePayPopBillExportVo.setBillCreateTime(popBillPo.getBillCreateTime());
            onlinePayPopBillExportVo.setBillPaymentTime(popBillPo.getBillPaymentTime());
            onlinePayPopBillExportVo.setRemitStatus(popBillPo.getRemitStatus());
            onlinePayPopBillExportVo.setInvoiceTime(popBillPo.getInvoiceTime());
            onlinePayPopBillExportVo.setPayableCommission(popBillPo.getPayableCommission());
            onlinePayPopBillExportVo.setSettlementType(popBillPo.getSettlementType());
            onlinePayPopBillExportVoList.add(onlinePayPopBillExportVo);
        });
        return onlinePayPopBillExportVoList;
    }
    public static List<OfflinePopBillDetailExportVo> convertOfflinePopBillDetailExport(List<PopBillDetailPo> popBillDetailPos, Map<String, PopBillPo> popBillPoMap) {
        List<OfflinePopBillDetailExportVo> offlinePopBillDetailExportVoList = new ArrayList<>();
        popBillDetailPos.forEach(o->{
            PopBillPo popBillPo = popBillPoMap.get(o.getBillNo());
            OfflinePopBillDetailExportVo offlinePopBillDetailExportVo = new OfflinePopBillDetailExportVo();
            offlinePopBillDetailExportVo.setOrgId(o.getOrgId());
            offlinePopBillDetailExportVo.setOrgName(o.getOrgName());
            offlinePopBillDetailExportVo.setName(o.getName());
            offlinePopBillDetailExportVo.setBillNo(o.getBillNo());
            if(null != popBillPo){
                offlinePopBillDetailExportVo.setBillPaymentStatus(popBillPo.getBillPaymentStatus());
                offlinePopBillDetailExportVo.setBillCreateTime(popBillPo.getBillCreateTime());
                offlinePopBillDetailExportVo.setBillPaymentTime(popBillPo.getBillPaymentTime());
                offlinePopBillDetailExportVo.setRemitStatus(popBillPo.getRemitStatus());
                offlinePopBillDetailExportVo.setRemitTime(popBillPo.getRemitTime());
                offlinePopBillDetailExportVo.setInvoiceStatus(popBillPo.getInvoiceStatus());
                offlinePopBillDetailExportVo.setInvoiceTime(popBillPo.getInvoiceTime());
            }
            offlinePopBillDetailExportVo.setBusinessNo(o.getBusinessNo());
            offlinePopBillDetailExportVo.setBusinessType(o.getBusinessType());
            offlinePopBillDetailExportVo.setMerchantName(o.getMerchantName());
            offlinePopBillDetailExportVo.setProductMoney(o.getProductMoney());
            offlinePopBillDetailExportVo.setFreightAmount(o.getFreightAmount());
            offlinePopBillDetailExportVo.setTotalMoney(o.getTotalMoney());
            offlinePopBillDetailExportVo.setShopTotalDiscount(o.getShopTotalDiscount());
            offlinePopBillDetailExportVo.setPlatformTotalDiscount(o.getPlatformTotalDiscount());
            offlinePopBillDetailExportVo.setMoney(o.getMoney());
            offlinePopBillDetailExportVo.setHireMoney(o.getHireMoney());
            offlinePopBillDetailExportVo.setPayableCommission(o.getPayableCommission());
            offlinePopBillDetailExportVo.setSettlementType(o.getSettlementType());
            offlinePopBillDetailExportVo.setStatementTotalMoney(o.getStatementTotalMoney());
            offlinePopBillDetailExportVo.setOrderPayTime(o.getOrderPayTime());
            offlinePopBillDetailExportVoList.add(offlinePopBillDetailExportVo);
        });
        return offlinePopBillDetailExportVoList;
    }

    public static List<OnlinePayPopBillDetailExportVo> convertOnlinePopBillDetailExport(List<PopBillDetailPo> popBillDetailPos, Map<String, PopBillPo> popBillPoMap) {
        List<OnlinePayPopBillDetailExportVo> onlinePayPopBillDetailExportVos = new ArrayList<>();
        popBillDetailPos.forEach(o->{
            PopBillPo popBillPo = popBillPoMap.get(o.getBillNo());
            OnlinePayPopBillDetailExportVo onlinePayPopBillDetailExportVo = new OnlinePayPopBillDetailExportVo();
            onlinePayPopBillDetailExportVo.setOrgId(o.getOrgId());
            onlinePayPopBillDetailExportVo.setOrgName(o.getOrgName());
            onlinePayPopBillDetailExportVo.setName(o.getName());
            onlinePayPopBillDetailExportVo.setBillNo(o.getBillNo());
            if(null != popBillPo){
                onlinePayPopBillDetailExportVo.setBillPaymentStatus(popBillPo.getBillPaymentStatus());
                onlinePayPopBillDetailExportVo.setBillCreateTime(popBillPo.getBillCreateTime());
                onlinePayPopBillDetailExportVo.setBillPaymentTime(popBillPo.getBillPaymentTime());
                onlinePayPopBillDetailExportVo.setInvoiceStatus(popBillPo.getInvoiceStatus());
                onlinePayPopBillDetailExportVo.setInvoiceTime(popBillPo.getInvoiceTime());
            }
            onlinePayPopBillDetailExportVo.setBusinessNo(o.getBusinessNo());
            onlinePayPopBillDetailExportVo.setBusinessType(o.getBusinessType());
            onlinePayPopBillDetailExportVo.setMerchantName(o.getMerchantName());
            onlinePayPopBillDetailExportVo.setProductMoney(o.getProductMoney());
            onlinePayPopBillDetailExportVo.setFreightAmount(o.getFreightAmount());
            onlinePayPopBillDetailExportVo.setTotalMoney(o.getTotalMoney());
            onlinePayPopBillDetailExportVo.setShopTotalDiscount(o.getShopTotalDiscount());
            onlinePayPopBillDetailExportVo.setPlatformTotalDiscount(o.getPlatformTotalDiscount());
            onlinePayPopBillDetailExportVo.setMoney(o.getMoney());
            onlinePayPopBillDetailExportVo.setHireMoney(o.getHireMoney());
            onlinePayPopBillDetailExportVo.setPayableCommission(o.getPayableCommission());
            onlinePayPopBillDetailExportVo.setSettlementType(o.getSettlementType());
            onlinePayPopBillDetailExportVo.setStatementTotalMoney(o.getStatementTotalMoney());
            onlinePayPopBillDetailExportVo.setOrderPayTime(o.getOrderPayTime());
            onlinePayPopBillDetailExportVos.add(onlinePayPopBillDetailExportVo);
        });
        return onlinePayPopBillDetailExportVos;
    }
}
