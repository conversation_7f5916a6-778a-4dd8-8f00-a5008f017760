package com.xyy.ec.pop.base;

import java.io.Serializable;


public class ResultVO<T> implements Serializable {
    private static final long serialVersionUID = 74704857074895138L;
    private String code;
    private String msg;
    private String displayMsg;
    private T result;

    public ResultVO() {
        this.displayMsg = "";
        this.code = ResultCodeEnum.SUCCESS.getCode();
        this.msg = ResultCodeEnum.SUCCESS.getMsg();
    }

    public ResultVO(T result) {
        this(ResultCodeEnum.SUCCESS, result);
    }

    public ResultVO(String code, String msg) {
        this(code, msg, "");
    }

    public ResultVO(String code, String msg, String displayMsg) {
        this.displayMsg = "";
        this.code = code;
        this.msg = msg;
        this.displayMsg = displayMsg;
    }

    public ResultVO(String code, String msg, String displayMsg, T result) {
        this.displayMsg = "";
        this.result = result;
        this.code = code;
        this.msg = msg;
        this.displayMsg = displayMsg;
    }

    public ResultVO(String code, String msg, T result) {
        this.displayMsg = "";
        this.result = result;
        this.code = code;
        this.msg = msg;
    }

    public ResultVO(ResultCodeEnum resultCodeEnum, T result) {
        this.displayMsg = "";
        this.result = result;
        this.code = resultCodeEnum.getCode();
        this.msg = resultCodeEnum.getMsg();
        this.displayMsg = resultCodeEnum.getDisplayMsg();
    }

    public static <T> ResultVO<T> createSuccess(T result) {
        return new ResultVO(result);
    }

    public static <T> ResultVO<T> createSuccess() {
        ResultVO resultVO = new ResultVO();
        resultVO.setCode(ResultCodeEnum.SUCCESS.getCode());
        resultVO.setMsg(ResultCodeEnum.SUCCESS.getMsg());
        resultVO.setDisplayMsg(ResultCodeEnum.SUCCESS.getDisplayMsg());
        return resultVO;
    }

    public static <T> ResultVO<T> create(ResultCodeEnum resultCodeEnum) {
        return new ResultVO(resultCodeEnum.getCode(), resultCodeEnum.getMsg(), resultCodeEnum.getDisplayMsg());
    }

    public static <T> ResultVO<T> create(String code) {
        if (code == null) {
            return create(ResultCodeEnum.DEFAULT_ERROR);
        } else {
            ResultCodeEnum[] resultCodeEnums = ResultCodeEnum.values();
            ResultCodeEnum[] var2 = resultCodeEnums;
            int var3 = resultCodeEnums.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                ResultCodeEnum resultCodeEnum = var2[var4];
                if (resultCodeEnum.getCode().equals(code)) {
                    return create(resultCodeEnum);
                }
            }

            return create(ResultCodeEnum.DEFAULT_ERROR);
        }
    }

    public static <T> ResultVO<T> createError(String code, String msg, T result) {
        return result == null ? new ResultVO(code, msg, result) : new ResultVO(code, msg, (String) result, result);
    }

    public static <T> ResultVO<T> createError(String code, String msg, String displayMsg, T result) {
        return result == null ? new ResultVO(code, msg, displayMsg, result) : new ResultVO(code, msg, displayMsg + "-" + result, result);
    }

    public static <T> ResultVO<T> createError(ResultCodeEnum codeEnum, T result) {
        return result == null ? new ResultVO(codeEnum.getCode(), codeEnum.getMsg(), codeEnum.getDisplayMsg(), result) : new ResultVO(codeEnum.getCode(), codeEnum.getMsg(), codeEnum.getDisplayMsg() + "-" + result, result);
    }

    public static <T> ResultVO<T> createError(String msg) {
        return new ResultVO(ResultCodeEnum.DEFAULT_ERROR.getCode(), msg, ResultCodeEnum.DEFAULT_ERROR.getDisplayMsg());
    }

    public static <T> ResultVO<T> createError(String errorCode, String errorMsg) {
        return new ResultVO(errorCode, errorMsg, errorMsg);
    }

    public static <T> ResultVO<T> createError(ResultCodeEnum codeEnum) {
        return new ResultVO(codeEnum.getCode(), codeEnum.getMsg(), codeEnum.getDisplayMsg());
    }

    public static <T> ResultVO<T> createParamError() {
        return createParamError("");
    }

    public static <T> ResultVO<T> createParamError(String displayMsg) {
        return new ResultVO(ResultCodeEnum.PARAM_ERROR.getCode(), ResultCodeEnum.PARAM_ERROR.getMsg(), displayMsg);
    }


    /**
     * @deprecated
     */
    @Deprecated
    public static <T> ResultVO<T> createFail(String msg) {
        return createError(msg);
    }

    public boolean isSuccess() {
        return ResultCodeEnum.SUCCESS.getCode().equals(this.code);
    }

    public boolean isFail() {
        return !this.isSuccess();
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getResult() {
        return this.result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public String getDisplayMsg() {
        return this.displayMsg;
    }

    public void setDisplayMsg(String displayMsg) {
        this.displayMsg = displayMsg;
    }

    public String toString() {
        return "ResultVO{code='" + this.code + '\'' + ", msg='" + this.msg + '\'' + ", displayMsg='" + this.displayMsg + '\'' + ", result=" + this.result + '}';
    }
}
