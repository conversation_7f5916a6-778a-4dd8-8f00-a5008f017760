package com.xyy.ec.pop;

import com.alibaba.dubbo.config.spring.context.annotation.EnableDubbo;
import com.alibaba.dubbo.config.spring.context.annotation.EnableDubboConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.dianping.cat.Cat;
import com.xyy.ec.pop.config.SsoClientConfiguration;
import com.xyy.ec.pop.utils.autoconfigure.EnableXyyFtpClientPool;
import com.xyy.me.sso.client.EnableSsoClient;
import com.xyy.me.sso.client.shiro.ShiroAutoConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableDubbo
@EnableDubboConfig(multiple = true)
@EnableTransactionManagement
@ComponentScan(basePackages={"com.xyy.*"})
@EnableScheduling
@EnableApolloConfig
@EnableXyyFtpClientPool
@EnableSsoClient
public class Application extends SpringBootServletInitializer {
	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(Application.class);
	}
	public static void main(String[] args) {
		Cat.getProducer();
		System.setProperty("dubbo.start.notiry.waiting.millis","5000");
		System.setProperty("dubbo.shutownhook.notiry.waiting.millis","5000");
		System.setProperty("dubbo.service.shutdown.wait","15000");
		SpringApplication.run(Application.class, args);
	}

	@Bean
	public FilterRegistrationBean corsFilter() {
		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
		CorsConfiguration config = new CorsConfiguration();
		config.setAllowCredentials(true);
		//允许的网站域名，如果全允许则设为 *
		//config.setAllowedOrigins(originList);
		config.addAllowedOrigin("*");
		//允许任何请求头
		config.addAllowedHeader("*");
		//允许任何请求方式
		config.addAllowedMethod("*");
		//安全证书。携带cookie配置为true
		config.setAllowCredentials(true);
		source.registerCorsConfiguration("/**", config);
		FilterRegistrationBean bean = new FilterRegistrationBean(new CorsFilter(source));
		// 这个顺序很重要哦，为避免麻烦请设置在最前
		bean.setOrder(0);
		return bean;
	}

	@Bean
	public ShiroAutoConfig shiroAutoConfig(){
		return new ShiroAutoConfig(SsoClientConfiguration.class);
	}
}
