package com.xyy.ec.pop.vo.settle;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
/**
* <AUTHOR>
* @date  2020/12/8 13:43
* @table
*/
@Data
public class PopBillStatisVo implements Serializable {

    /**
     * 佣金合计
     */
    private BigDecimal hireMoneyTotal = BigDecimal.ZERO;
    /**
     * 应结算金额合计
     */
    private BigDecimal statementTotalMoneyTotal = BigDecimal.ZERO;
    /**
     * 实际需缴纳佣金合计
     */
    private BigDecimal actualCommissionMoneyTotal = BigDecimal.ZERO;
    /**
     * 佣金优惠合计
     */
    private BigDecimal commissionDiscountMoneyTotal = BigDecimal.ZERO;
    /**
     * 应缴纳佣金合计
     */
    private BigDecimal deductedCommissionTotal = BigDecimal.ZERO;
}
