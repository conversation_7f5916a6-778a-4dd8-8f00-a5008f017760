/**
 * Copyright (C), 2015-2019,  武汉小药药医药科技有限公司
 * FileName: RedisConstants
 * Author:   dell
 * Date:     2019/6/14 9:58
 * Description: redis前缀常量维护
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.xyy.ec.pop.constants;


import com.xyy.ec.pop.enums.SmsTypeEnum;

/**
 * 〈一句话功能简述〉<br> 
 * 〈redis前缀常量维护〉
 *
 * <AUTHOR>
 * @create 2019/6/14
 * @since 1.0.0
 */
public class RedisConstants {
    /** 字典缓存key */
    private static final String CODE_ITEM_KEY = "code_item";
    /**
     * 字典项队列缓存key
     */
    private static final String CODE_MAP_SET_KEY = "code_map_set";
    /** 字典缓存过期时间 /d */
    public static final int CODE_ITEM_EXPIRE_TIME = 7;
    /**
     * 短信验证码缓存key
     */
    private static final String SMS_VERIFY_CODE_KEY = "SMS_VERIFY_CODE";
    /** 短信验证码发送时间缓存 key */
    private static final String SMS_VERIFY_SEND_TIME_KEY = "SMS_VERIFY_SEND_TIME";
    /** 短信验证码发送总次数缓存key */
    private static final String SMS_VERIFY_SEND_TOTAL_KEY = "SMS_VERIFY_SEND_TOTAL";
    /** api ip风控缓存key */
    private static final String API_IP_CONTROL_KEY = "API_IP_CONTROL";
    /** 登录密码输入错误次数key */
    private static final String LOGIN_PASS_WRONG_TIMES_KEY = "LOGIN_PASS_WRONG_TIMES";
    /** 结算单退款通知幂等处理key */
    private static final String SETTLE_REFUND_DISTINCT_KEY = "SETTLE_FD";
    /** 保存商品接口连续点击加锁处理key */
    public static final String PRODUCT_SAVE_LOCK_KEY = "PRODUCT_SAVE_LOCK_KEY";
    /** 导出进度缓存key */
    private static final String EXPORT_RATE_KEY = "EXPORT_RATE_KEY";

    /** 省份区域缓存key */
    public static final String ALL_BRANCH_LIST_KEY = "ALL_BRANCH_LIST_KEY";

    /** 省份区域缓存key */
    public static final String ALL_PROVINCE_LIST_KEY = "ALL_PROVINCE_LIST_KEY";

    public static final String BRANCH_LIST ="branchList" ;
    public static final String CURRENT_BRANCH = "currentBranch";
    public static final String SN_SKU_BATCH_PUBLISH_KEY = "SN_SKU_BATCH_PUBLISH_KEY:";

    /**
     * 短信验证码缓存key
     * @param phone
     * @param smsTypeEnum @see {@link SmsTypeEnum}
     * @return
     */
    public static String getSmsVerifyCodeKey(String phone, SmsTypeEnum smsTypeEnum) {
        return String.format("%s:%s:%s",SMS_VERIFY_CODE_KEY,phone,smsTypeEnum.getType());
    }

    /**
     * 短信验证码发送时间缓存 key
     * @param phone
     * @return
     */
    public static String getSmsVerifySendTimeKey(String phone) {
        return String.format("%s:%s",SMS_VERIFY_SEND_TIME_KEY,phone);
    }

    /**
     * 短信验证码发送总次数缓存key
     * @param phone
     * @return
     */
    public static String getSmsVerifySendTotalKey(String phone) {
        return String.format("%s:%s",SMS_VERIFY_SEND_TOTAL_KEY,phone);
    }

    /**
     * api ip风控缓存key
     * @param apiUri 接口地址
     * @param ip ip地址
     * @return
     */
    public static String getApiIpControlKey(String apiUri,String ip) {
        return String.format("%s:%s:%s",API_IP_CONTROL_KEY,apiUri,ip);
    }
    /**
     * 获取字典缓存key
     * @param codeMap
     * @return
     */
    public static String getCodeItemKey(String codeMap) {
        return String.format("%s:%s",CODE_ITEM_KEY,codeMap);
    }

    /**
     * 获取登录密码错误次数缓存key
     * @param userName
     * @return
     */
    public static String getLoginPassWrongTimesKey(String userName) {
        return String.format("%s:%s",LOGIN_PASS_WRONG_TIMES_KEY,userName);
    }

    /**
     * 获取字典项队列缓存key
     * @return
     */
    public static String getCodeMapSetKey() {
        return CODE_MAP_SET_KEY;
    }

    /**
     * 获取结算单退款通知幂等处理key
     * @param orderNo
     * @return
     */
    public static String getSettleRefundDistinctKey(String orderNo) {
        return String.format("%s:%s",SETTLE_REFUND_DISTINCT_KEY,orderNo);
    }

    /**
     * 得到excel导出进度key
     * @param uuid
     * @return
     */
    public static String getExportRateKey(String uuid) {
        return String.format("%s:%s",EXPORT_RATE_KEY,uuid);
    }
}