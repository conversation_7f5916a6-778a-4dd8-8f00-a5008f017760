package com.xyy.ec.pop.excel.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.xyy.ec.pop.vo.AdjustiveBillSettleVo;
import org.apache.commons.lang3.StringUtils;

/**
 * @version v1
 * @Description 调账单导入过滤空行
 * <AUTHOR>
 */
public class AdjustiveBillSettleExcelVerifyHandler implements IExcelVerifyHandler<AdjustiveBillSettleVo> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(AdjustiveBillSettleVo vo) {
        if (StringUtils.isBlank(vo.getOrgId()) && StringUtils.isBlank(vo.getPlatformTotalDiscount()) && StringUtils.isBlank(vo.getHireMoney()) && StringUtils.isBlank(vo.getStatementTotalMoney()) && StringUtils.isBlank(vo.getDeductedCommission()) && StringUtils.isBlank(vo.getRemark())) {
            return new ExcelVerifyHandlerResult(false);
        }
        return new ExcelVerifyHandlerResult(true);
    }
}
