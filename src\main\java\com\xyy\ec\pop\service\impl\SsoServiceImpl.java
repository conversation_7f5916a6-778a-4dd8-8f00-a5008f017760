package com.xyy.ec.pop.service.impl;

import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.pop.enums.SpecialCharactersEnum;
import com.xyy.ec.pop.helper.PopProvinceHelper;
import com.xyy.ec.pop.service.SsoService;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.login.Menu;
import com.xyy.ec.pop.vo.login.UserInfo;
import com.xyy.ec.pop.vo.zhongtai.PopProvinceVo;
import com.xyy.ec.pop.vo.zhongtai.ZhongTaiParamVo;
import com.xyy.ec.pop.vo.zhongtai.ZhongTaiProvinceVo;
import com.xyy.ec.pop.vo.zhongtai.ZhongTaiResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: LoginServiceImpl
 * @Package
 * @Description: 对接中台登录
 * @date 2020/5/28
 */
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Service
public class SsoServiceImpl implements SsoService {
    private static final String RESULT_NODE = "result";

    @Value("${zhongtai.my-system-id}")
    private int mySystemId;

    @Value("${zhongtai.white-list-url}")
    private String whiteListUrl;

    @Value("${zhongtai.sso-url}")
    private String ssoUrl;

    @Value("${zhongtai.sso-logout-url}")
    private String ssoLogoutUrl;

    @Value("${zhongtai.base-url}")
    private String baseUrl;

    @Value("${zhongtai.query-user-by-account-url}")
    private String queryUserByAccountUrl;

    @Value("${zhongtai.query-menu-of-logged-url}")
    private String queryMenuOfLoggedUrl;

    @Value("${zhongtai.find-permission-value-list}")
    private String findPermissionValueList;

    @Value("${zhongtai.query-permission-of-logged}")
    private String queryPermissionOfLogged;

    private final HttpServletRequest request;

    private final HttpServletResponse response;


    @Override
    public ResponseVo<Object> getToken(String username, String password) {
        log.info("{}登录尝试获取Token", username);
        HttpURLConnection connection = null;
        OutputStream os = null;
        try {
            username = this.getEscapeSpecialCharacters(username);
            password = this.getEscapeSpecialCharacters(password);
            String param = "username=" + this.getEscapeSpecialCharacters(username) + "&password=" + password + "&service=" + whiteListUrl;
            URL url = new URL(ssoUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(60000);
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            os = connection.getOutputStream();
            os.write(param.getBytes());
            int code1 = connection.getResponseCode();
            log.info("{}登录尝试获取Token，响应码:{}", username, code1);
            if (code1 != 201) {
                String errorStr = StreamUtils.copyToString(connection.getErrorStream(), StandardCharsets.UTF_8);
                log.info("登录账号 " + username + " , 错误信息 " + errorStr);
                String warnText = errorStr.replace("AccountLockedException:", "")
                        .replace("FailedSuccessiveException:", "")
                        .replace("AccountNotFoundException", "")
                        .replace("AccountDisabledException", "")
                        .replace("FailedLoginException", "")
                        .replace("\"", "").replace(":", "");
                return ResponseVo.errRest(warnText);
            }
            // 认证成功！
            String token = connection.getHeaderFields().get("Location").get(0).substring(connection.getHeaderFields().get("Location").get(0).lastIndexOf('/') + 1);
            log.info("{}登录成功,TGC:{}", username, token);
            if (!StringUtils.isEmpty(token)) {
                return ResponseVo.successResult(token);
            }
        } catch (Exception e) {
            log.error("login认证异常", e);
        }
        return ResponseVo.errRest("登录认证失败");
    }

    @Override
    public ResponseVo<UserInfo> login(String username, String password) {
        log.info("{}尝试登录", username);
        try {
            //1.中台认证并获取Token
            ResponseVo<Object> tokenResp = getToken(username, password);
            if (tokenResp.isFail()) {
                //认证失败
                return ResponseVo.errRest(tokenResp.getMessage());
            }
            // 认证成功！
            String token = tokenResp.getData().toString();
            log.info("{}登录成功,TGC:{}", username, token);
            if (!StringUtils.isEmpty(token)) {
                UserInfo userInfo = getUserInfoByUsername(username, token);
                if (userInfo == null) {
                    return ResponseVo.errRest("登录失败,稍后重试");
                }
                userInfo.setUsername(username);
                userInfo.setToken(token);
//                List<Menu> menuList = getUserMenuByOaId(userInfo.getOaId(), token);
//                userInfo.setMenuList(menuList);
                request.getSession().setAttribute("userInfo", userInfo);
                return ResponseVo.successResult(userInfo);
            }
        } catch (Exception e) {
            log.error("login异常", e);
        }
        return ResponseVo.errRest("登录失败");
    }

    @Override
    public ResponseVo<Object> logout(String token) {
        HttpURLConnection connection = null;
        OutputStream os = null;
        try {
            URL url = new URL(ssoLogoutUrl + token);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("DELETE");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            os = connection.getOutputStream();
            os.write(token.getBytes());
            if (connection.getResponseCode() == 200) {
                //注销成功！清除客户端票据
                response.addCookie(new Cookie("TGC", ""));
                request.getSession().invalidate();
            } else {
                log.error("注销失败");
            }
        } catch (Exception e) {
            log.error("注销异常", e);
        }
        return ResponseVo.successResultNotData();
    }

    private UserInfo getUserInfoByUsername(String username, String token) {
        String requestUrl = baseUrl + queryUserByAccountUrl;
        log.info("SsoServiceImpl.getUserInfoByUsername:请求获取中台用户信息，url:{},username:{},token:{}", requestUrl, username, token);
        String result = HttpRequest.get(requestUrl)
                .setConnectionTimeout(2000).timeout(5000)
                .header("TGC", token)
                .form("account", username)
                .execute().body();
        log.info("SsoServiceImpl.getUserInfoByUsername:请求获取中台用户信息，url:{},username:{},token:{},返回：{}", requestUrl, username, token, result);
        JSONObject jsonObject = JSON.parseObject(result).getJSONObject(RESULT_NODE);
        if (jsonObject == null) {
            return null;
        }
        return JSON.parseObject(jsonObject.toJSONString(), UserInfo.class);
    }

    @Override
    public List<Menu> getUserMenuByOaId(String oaId, String token) {
        String requestUrl = baseUrl + queryMenuOfLoggedUrl;
        log.info("SsoServiceImpl.getUserMenuByOaId:请求获取中台菜单信息，url:{},oaId:{},token:{}", requestUrl, oaId, token);
        String result = HttpRequest.post(requestUrl)
                .setConnectionTimeout(2000).timeout(5000)
                .header("TGC", token)
                .body("{\"oaId\":\"" + oaId + "\",\"sysType\":" + mySystemId + "}")
                .execute().body();
        log.info("SsoServiceImpl.getUserMenuByOaId:请求获取中台菜单信息，url:{},oaId:{},token:{}，返回:{}", requestUrl, oaId, token, result);
        JSONArray jsonArray = JSON.parseObject(result).getJSONArray(RESULT_NODE);
        if (jsonArray == null) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(jsonArray.toJSONString(), Menu.class);
    }

    @Override
    public ResponseVo<UserInfo> getUser(String username, String token) {
        UserInfo userInfo = getUserInfoByUsername(username, token);
        return ResponseVo.successResult(userInfo);
    }

    /**
     * 获取用户省份权限
     *
     * @param token 用户的token
     */
    @Override
    public List<PopProvinceVo> getProvinces(String token) {
        try {
            log.info("SsoServiceImpl.getProvinces请求获取中台省份权限信息findPermissionValueList:{},token:{}", findPermissionValueList, token);
            String jsonStr = HttpRequest.post(baseUrl + findPermissionValueList)
                    .setConnectionTimeout(2000).timeout(5000)
                    .header("TGC", token)
                    .header("sourceSysCode", String.valueOf(mySystemId))
                    .body(JSON.toJSONString(new ZhongTaiParamVo()))
                    .execute().body();
            log.info("SsoServiceImpl.getProvinces请求获取中台省份权限信息findPermissionValueList:{},token:{},返回:{}", findPermissionValueList, token, jsonStr);
            if(StringUtils.isBlank(jsonStr)){
                return Lists.newArrayList();
            }
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            Integer code = jsonObject.getInteger("code");
            String result = jsonObject.getString(RESULT_NODE);
            boolean bool = code != null && code == 200 && StringUtils.isNotBlank(result);
            if(bool){
                List<ZhongTaiProvinceVo> zhongTaiProvinceVos = JSON.parseArray(JSON.parseObject(jsonStr).getString(RESULT_NODE), ZhongTaiProvinceVo.class);
                List<PopProvinceVo> provinceVos = zhongTaiProvinceVos.stream().filter(vo -> vo != null && vo.getLev() == 1 && vo.getPermissStatus() == 1)
                        .map(PopProvinceHelper::convertToVo).collect(Collectors.toList());
                log.info("SsoServiceImpl.getProvinces最终处理结果：provinceVos:{}", JSON.toJSONString(provinceVos));
                return provinceVos;
            }
            return Lists.newArrayList();
        } catch (Exception e) {
            log.error("SsoServiceImpl.getProvinces请求获取中台省份权限信息异常findPermissionValueList:{},token:{}", findPermissionValueList, token, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 获取用户省份权限
     *
     * @param token 用户的token
     * @return
     */
    @Override
    public List<ZhongTaiResource> initButtons(String token) {
        try {
            log.info("SsoServiceImpl.getProvinces请求获取中台pop菜单按钮权限信息queryPermissionOfLogged:{},token:{}", queryPermissionOfLogged, token);
            String jsonStr = HttpRequest.post(baseUrl + queryPermissionOfLogged)
                    .setConnectionTimeout(2000).timeout(5000)
                    .header("TGC", token)
                    .header("sourceSysCode", String.valueOf(mySystemId))
                    .body(JSON.toJSONString(new ZhongTaiParamVo()))
                    .execute().body();
            log.info("SsoServiceImpl.getProvinces请求获取中台pop菜单按钮权限信息queryPermissionOfLogged:{},token:{},返回:{}", queryPermissionOfLogged, token, jsonStr);
            if(StringUtils.isBlank(jsonStr)){
                return Lists.newArrayList();
            }
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            Integer code = jsonObject.getInteger("code");
            String result = jsonObject.getString(RESULT_NODE);
            boolean bool = code != null && code == 200 && StringUtils.isNotBlank(result);
            if (bool) {
                return JSON.parseArray(JSON.parseObject(jsonStr).getString(RESULT_NODE), ZhongTaiResource.class);
            }
            return Lists.newArrayList();
        } catch (Exception e) {
            log.info("SsoServiceImpl.getProvinces请求获取中台pop菜单按钮权限信息异常:queryPermissionOfLogged:{},token:{}", queryPermissionOfLogged, token, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 获取转义后的含特殊字符的字符串
     *
     * @param str 字符串
     * @return
     */
    private String getEscapeSpecialCharacters(String str) {
        if (StringUtils.isEmpty(str)) {
            return "";
        }
        // 是否含有特殊字符
        if (specialSymbols(str)) {
            str = SpecialCharactersEnum.getEscapeSpecialCharacters(str);
        }
        return str;
    }

    /**
     * 校验字符串里是否包含特殊字符，有-true 无-false
     *
     * @param param param
     * @return
     */
    private static boolean specialSymbols(String param) {
        // 特殊字符
        String regex = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）\"\"《》\\\\——+|{}【】‘；：”“’。，、？]";

        boolean flag = false;
        try {
            Pattern p = Pattern.compile(regex);
            Matcher m = p.matcher(param);
            flag = m.find();
        } catch (Exception e) {
            log.error("specialSymbols方法异常", e);
        }
        return flag;
    }
}
