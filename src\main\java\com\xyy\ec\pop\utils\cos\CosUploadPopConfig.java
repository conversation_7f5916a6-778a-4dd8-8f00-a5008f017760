package com.xyy.ec.pop.utils.cos;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @author: yangpeng3
 * @Description:
 * @date: 2022/4/22 11:30
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cos.upload.pop")
public class CosUploadPopConfig {

    private String secretId;
    private String secretKey;
    private String bucketName;
    private String domain;
    private String region;
    private String dir;
    private Integer retryCount;
    private Boolean switchFlag;

}
