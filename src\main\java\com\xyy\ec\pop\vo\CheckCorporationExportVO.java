package com.xyy.ec.pop.vo;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 2020/05/04
 */
@Data
public class CheckCorporationExportVO {
    @Excel(name = "单据编号", width = 20)
    private String batch;
    @Excel(name = "商户编号", width = 20)
    private String orgId;
    @Excel(name = "名称", width = 20)
    private String companyName;
    @Excel(name = "店铺名称", width = 20)
    private String name;
    @Excel(name = "商户账户", width = 20)
    private String suUserName;
    @Excel(name = "联系人", width = 20)
    private String corporat;
    @Excel(name = "联系电话", width = 20)
    private String phone;
    @Excel(name = "申请时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date suCreateTime;
    @Excel(name = "审核状态", width = 20, replace = {"_null", "待审核_1", "审核不通过_2", "审核通过_3"})
    private Byte state;
    //@Excel(name = "审核通过时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    //private Date updateTime;

}
