package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.helper.PopQualificationsConfigValidHelper;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.server.api.merchant.api.admin.PopQualificationsConfigAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationTypeEnum;
import com.xyy.ec.pop.service.BusinessCategoryService;
import com.xyy.ec.pop.service.QualificationsConfigService;
import com.xyy.ec.pop.vo.QualificationsCategoryRelationVo;
import com.xyy.ec.pop.vo.QualificationsDetailVo;
import com.xyy.ec.pop.vo.EcErpCategoryVo;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import com.xyy.pop.framework.core.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Controller
@RequestMapping("/popQualificationsConfig")
@Api(tags = "商户资质配置类")
public class PopQualificationsConfigController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PopQualificationsConfigController.class);

    @Reference
    private PopQualificationsConfigAdminApi popQualificationsConfigAdminApi;
    @Autowired
    private QualificationsConfigService qualificationsConfigService;
    @Autowired
    private BusinessCategoryService businessCategoryService;
    @GetMapping(value = "index")
    @ApiOperation("资质配置页面")
    public String index(@ApiParam(name = "request",value = "通过request获取请求参数") HttpServletRequest request, @ApiIgnore Model model){
//        List<EcErpCategoryVo> erpCategoryVoList = ecProductApiRemote.getErpCategorysAsTree();
        List<EcErpCategoryVo> erpCategoryVoList=businessCategoryService.getValidBusinessScope();
        model.addAttribute("erpCategoryVoList", JSON.toJSONString(erpCategoryVoList));
        return "/qualificationsConfig/index";
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @ApiOperation("保存资质")
    public Response<String> save(@ApiParam(name = "relationVoList",value = "资质实体保存集合")@RequestBody List<QualificationsCategoryRelationVo> relationVoList,@RequestParam byte corporationType){
        try {
            SysUser sysUser = getUser();
//            popQualificationsConfigService.checkBeforeSave(relationVoList);
            PopQualificationsConfigValidHelper.checkBeforeSave(relationVoList,corporationType);
            //保存资质配置
//            popQualificationsConfigService.savePopQualifications(relationVoList, sysUser.getUsername());
            //保存资质配置
            qualificationsConfigService.savePopQualifications(relationVoList, corporationType,sysUser.getUsername());
            return ResponseUtils.returnSuccess("保存成功");
        }catch (Exception e){
            LOGGER.error("保存资质配置异常", e);
            return ResponseUtils.returnException(e);
        }

    }

    /**
     * 获取共有的企业资质配置(非经营类的)
     * @param
     * @return
     */
    @GetMapping(value = "/getPopCommonQualificationConfigs")
    @ResponseBody
    @ApiOperation("获取共有的企业资质配置(非经营类的)")
    public Response<List<QualificationsDetailVo>> getPopCommonQualificationConfigs(@RequestParam byte corporationType ){
        try {
            //获取所有资质(包含统一类资质和经营类资质)
            CorporationTypeEnum enu = CorporationTypeEnum.getByCode(corporationType);
            if(enu==null){
                return ResponseUtils.returnCommonException("错误的企业类型");
            }
            QualificationsCategoryRelationVo relationVo = qualificationsConfigService.commonQualificationConfigs(enu);
            return ResponseUtils.returnObjectSuccess(relationVo==null?new ArrayList<>(0):relationVo.getQualificationsDetailList());
        }catch (Exception e){
            LOGGER.error("查询企业资质信息异常", e);
            return ResponseUtils.returnException(e);
        }
    }

    @GetMapping(value = "/getPopBusinessQualificationConfigs")
    @ResponseBody
    @ApiOperation("获取非共有的企业资质配置")
    public Response<List<QualificationsCategoryRelationVo>> getPopBusinessQualificationConfigs(@RequestParam byte corporationType ){
        try {
            //获取经营类资质
            CorporationTypeEnum enu = CorporationTypeEnum.getByCode(corporationType);
            if(enu==null){
                return ResponseUtils.returnCommonException("错误的企业类型");
            }
            List<QualificationsCategoryRelationVo> popBusinessCategoryRelationVoList =  qualificationsConfigService.getBusinessQualificationConfigs(enu);
            return ResponseUtils.returnObjectSuccess(popBusinessCategoryRelationVoList);
        }catch (Exception e){
            LOGGER.error("查询企业资质信息异常", e);
            return ResponseUtils.returnException(e);
        }
    }

    //验证detail是否可删除
    @PostMapping(value = "/validDetailCardIsDelete")
    @ResponseBody
    @ApiOperation("验证detail是否可删除")
    public Response validDetailCardIsDelete(@ApiParam(name = "detailId",value = "资质明细detailId") Long detailId){
        if(null == detailId){
            return ResponseUtils.returnCommonException("需校验资质明细Id不能为空");
        }
        ApiRPCResult<List<Long>> apiRPCResult = popQualificationsConfigAdminApi.filterForbiddenDeleteDetailId(Lists.newArrayList(detailId));
        if(apiRPCResult != null && apiRPCResult.isSuccess() && CollectionUtils.isEmpty(apiRPCResult.getData())){
            return ResponseUtils.returnObjectSuccess(Boolean.TRUE);
        }else{
            return ResponseUtils.returnObjectSuccess(Boolean.FALSE);
        }
    }

    //验证category对应关系是否可删除
    @PostMapping(value = "/validCategoryRelationIsDelete")
    @ResponseBody
    @ApiOperation("验证category对应关系是否可删除")
    public Response validCategoryRelationIsDelete(@ApiParam(name = "categoryId",value = "分类id") Long categoryId){
        if(null == categoryId){
            return ResponseUtils.returnCommonException("需校验资质类别Id不能为空");
        }
        ApiRPCResult<List<Long>> apiRPCResult = popQualificationsConfigAdminApi.filterForbiddenDeleteCategoryId(Lists.newArrayList(categoryId));
        if(apiRPCResult != null && apiRPCResult.isSuccess() && CollectionUtils.isEmpty(apiRPCResult.getData())){
            return ResponseUtils.returnObjectSuccess(Boolean.TRUE);
        }else{
            return ResponseUtils.returnObjectSuccess(Boolean.FALSE);
        }
    }
}
