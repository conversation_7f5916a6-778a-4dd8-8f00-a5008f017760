package com.xyy.ec.pop.utils;

/**.
 * 
 * <AUTHOR>
 */
public final class Constants {

	/**
	 *.
	 */
	private Constants(){
		
	}
	/**
	 * sessionId.
	 */
	public static final String SESSION_ID = "sys_user_session_id";
	
	/**
	 * 永不过期.
	 */
	public static final int OUT_TIME_FOREVER = -1;
	
	/**
	 * 失效时间30分钟.
	 */
	public static final int OUT_TIME_30 = 30*60;
	
	/**
	 * 失效时间30*60.
	 */
	public static final int OUT_TIME_1800 = 1800;
	
	
	public static final String MENUS_LIST = "menus_list";
	
	/**
	 * model中PageBaen的name.
	 */
	public static final String NAMEOFPAGEBEAN="pb";
	/**
	 * 用户可以操作的对象的id的名称(更新,删除操作 防止手动修改id值).
	 */
	public static final String NAMEOFIDINPB="nameOfIdInPb";
	/**
	 *  model中要操作对象的name.
	 */
	public static final String NAMEOFOBJECT = "data";
	/**
	 * 用户可以操作的model中对象的id的名称
	 */
	public static final String NAMEOFID="nameOfId";
	
	/**
	 * TOKEN key.
	 */
	public static final String TOKEN = "token";
	
}
