<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<html>
<head>
    <%@include file="../include/common.jsp" %>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>商品控销信息</title>
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no,minimal-ui"/>
    <meta name="keywords" content=""/>
    <meta name="description" content=""/>
    <link rel="stylesheet" href="${basePathUrl}/css/common/admin_public.css"/>
    <link rel="stylesheet" href="${basePathUrl}/js/plugins/ztree/css/jquery-ui.min.css"/>
    <link rel="stylesheet" href="${basePathUrl}/css/common/table-index.css"/>
    <script src="${basePathUrl}/js/plugins/ztree/js/jquery-ui.min.js"></script>
    <link rel="stylesheet" href="${basePathUrl}/js/plugins/ztree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="${basePathUrl}/css/ace.min.css" type="text/css">
    <script src="${basePathUrl}/js/plugins/ztree/js/jquery.ztree.core.min.js"></script>
    <script src="${basePathUrl}/js/bootstrap/bootstrap-table.js"></script>
    <script src="${basePathUrl}/js/bootstrap/bootstrap-table-zh-CN.js"></script>
    <script src="${basePathUrl}/js/plugins/ztree/js/jquery.ztree.excheck.min.js"></script>
    <style>
        .content{
            padding: 2% 5%;
            background-color: white;
        }
        .panel-body{
            width: 100%;
        }
        .info-label{
            padding: 0 5px;
        }
        .tab-title{
            border-bottom: 1px solid #999999;
            padding: 0 4%;
        }
        .tab-title div{
            border: 1px solid #999999;
            display: inline-block;
            margin-bottom: -1px;
            padding: 4px;
            cursor: pointer;
        }
        .selectedTab{
            border-bottom: 1px solid white !important;
        }
        .tab-cont > div{
            display: none;
        }
        .selectedCont{
            display: block !important;
        }
        .blod-title{
            font-weight:bold;
        }
    </style>
    <script>
        querying = false;
        var weekMap = {1:"星期一",2:"星期二",3:"星期三",4:"星期四",5:"星期五",6:"星期六",7:"星期七"}
        function freshInfo(){
            var barcode = $("#queryBarcode").val();
            if(!barcode){
                return err("请输入商品编码");
            }
            if(querying){
                alter("查询中，请稍后")
                return;
            }
            err("查询中，请稍后...")
            querying=true;
            $.ajax({
                type: "GET",
                url: "/salesControl/detail?barcode="+barcode,
                success:function (res) {
                    if(res.code!=0){
                        querying = false;
                        return err(res.message);
                    }
                    succ(res.data);
                    $("#queryBarcode").val("");
                    querying = false;
                },error:function(){
                    querying = false;
                    return err("查询失败，请重试");
                }
            });
        }
        function err(tip){
            $("#errorTip").css("display","block");
            $("#info").css("display","none");
            $("#errorTip").text(tip)
        }
        function succ(data) {
            $("#info").css("display","block");
            $("#errorTip").css("display","none");
            //设置基本信息
            $("#barcode").text(data.sku.barcode);
            $("#showName").text(data.sku.showName);
            var status = data.sku.status;
            $("#status").text(status==1?"销售中":status==4?"下架":status==6?"待上架":status==8?"待审核":status==9?"审核不通过":"删除");
            $("#erpCode").text(data.sku.erpCode||"");
            $("#skuCategory").text(data.sku.skuCategory||"");
            $("#saleType").text(data.sku.saleType==1?"药品":"非药");
            $("#mediumPackageNum").text(data.sku.mediumPackageNum+(data.sku.isSplit==1?" 可拆零":" 不可拆零"));
            $("#stock").text(data.sku.stock);
            $("#minPurchaseCount").text(data.sku.minPurchaseCount||"");
            $("#shopControl").text(data.shopControl||"无");
            $("#shopSaleArea").text(data.shopSaleArea||"无");
            $("#shopBlackBuyer").text(data.shopBlackBuyer||"无");
            showProductLimit(data);
            showSaleTimeInfo(data);
            //商圈
            var arr = [];
            (data.areaDtos||[]).forEach(item=>arr.push(item.areaName));
            $("#busArea").text(arr.join(","));
            $("#controlUserTypes").text("无");
            $("#controlBlackGroupId").text("无");
            $("#controlWhiteGroupId").text("无");
            if(data.popSkuInstruction&&data.popSkuInstruction.controlUserTypes){
                $("#controlUserTypes").text(data.popSkuInstruction.controlUserTypes);
                var merchantIds = data.saleControlMerchantIds.join(",");
                if(data.popSkuInstruction.controlRosterType===1){
                    $("#controlBlackGroupId").text(data.popSkuInstruction.controlGroupId+" 药店ID: "+merchantIds);
                }else if(data.popSkuInstruction.controlRosterType===2){
                    $("#controlWhiteGroupId").text(data.popSkuInstruction.controlGroupId+" 药店ID: "+merchantIds);
                }
            }

            if(data.control){
                showControl(data);
            }else{
                disableControl();
            }

        }
        function showSaleTimeInfo(data) {
            if(!data.timeDto){
                $(".saleTimeInfo").css("display","none");
                return;
            }
            $(".saleTimeInfo").css("display","inline-block");
            $("#saleTimeConfigId").text(data.timeDto.configId?data.timeDto.configId:"自定义");
            $("#saleTimeStatus").text(data.timeDto.status==1?"启用":"禁用");
            var time = JSON.parse(data.timeDto.time);
            var timeH="";
            time.forEach(item=>{
                timeH+="<div>"+weekMap[item.week]+" : ";
                for(var index = 0;index<item.time.length;index++){
                    var ti = item.time[index];
                    timeH+=ti.startHour+":"+ti.startMinute+"至"+ti.endHour+":"+ti.endMinute;
                    if(index<item.time.length-1){
                        timeH+=" , ";
                    }
                }
                timeH+="</div>"
            })
            $("#saleTime").html(timeH);
        }
        function showProductLimit(data) {//商品限购信息
            if(!data.limitDto||data.limitDto.purchaseType<1||data.limitDto.isDel==1){
                $(".productLimit").css("display","none");
                $(".productLimitTime").css("display","none");
                $("#purchaseType").text("不限购");
                return;
            }
            $(".productLimit").css("display","inline-block");
            if(data.limitDto.purchaseType==1){
                $(".productLimitTime").css("display","inline-block");
            }else{
                $(".productLimitTime").css("display","none");
            }
            $("#purchaseType").text(data.limitDto.purchaseType==1?"时间范围":"单笔");
            $("#limitedQty").text(data.limitDto.limitedQty);
            $("#purchaseTimeStart").text(data.limitDto.purchaseTimeStart||"");
            $("#purchaseTimeEnd").text(data.limitDto.purchaseTimeEnd||"");
            $("#areaCodeName").text(data.limitDto.areaCodeName||"");
            $("#userTypeName").text(data.limitDto.userTypeName||"");
        }
        function disableControl() {//隐藏控销信息
            $(".productControl").css("display","none");
            $(".noneProductControl").css("display","block");
        }
        function showControl(data) {//展示控销信息
            $(".productControl").css("display","block");
            $(".noneProductControl").css("display","none");
            $("#conId").text(data.control.id);
            $("#csuid").text(data.control.skuId);
            $("#conStart").text(fomartDate(new Date(data.control.startTime)));
            $("#conEnd").text(fomartDate(new Date(data.control.endTime)));

            //加载内容
            $('#whiteCust').bootstrapTable('load', data.whiteCustData||[]);
            $('#hideCust').bootstrapTable('load', data.hideCustData||[]);
            $('#whitePlan').bootstrapTable('load', data.whitePlan||[]);
            $('#hidePlan').bootstrapTable('load', data.hidePlan||[]);
            //展示第一个
            changeTitle(0);
        }
        function fomartDate(date){
            return date.getFullYear()+"-"+fomart(date.getMonth()+1)+"-"+fomart(date.getDate())+" "+fomart(date.getHours())+":"+fomart(date.getMinutes())+":"+fomart(date.getSeconds());
        }
        function fomart( value) {
            if(value>9)return value;
            return "0"+value;
        }
        function changeTitle(index){
            //标题
            var titles= $(".tab-title")[0].children;
            for(var i = 0;i<titles.length;i++){
                $(titles[i]).removeClass("selectedTab")
            }
            $(titles[index]).addClass("selectedTab")
            //内容
            var conts= $(".tab-cont")[0].children;
            for(var i = 0;i<conts.length;i++){
                $(conts[i]).removeClass("selectedCont")
            }
            $(conts[index]).addClass("selectedCont")
        }

    </script>
</head>
<body class="content">
    商品编码：<input type="text" id="queryBarcode"> <button id="queryBtn" onclick="freshInfo()">查询</button>
    <div id="saleContent">
        <div id="errorTip" style="color: red;padding-top:10px;display: none;"></div>
        <div id="info" style="width: 100%;display: none;">
            <div class="panel-body">
                <!-- 商品基本信息 -->
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">商户编码:</span>
                        <span id="barcode"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">商品名称:</span>
                        <span id="showName"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">状态:</span>
                        <span id="status"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">经营分类:</span>
                        <span id="skuCategory"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">药品类型:</span>
                        <span id="saleType"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">erp编码:</span>
                        <span id="erpCode"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">中包装:</span>
                        <span id="mediumPackageNum"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">库存:</span>
                        <span id="stock"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">起购数量:</span>
                        <span id="minPurchaseCount"></span>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" >
                        <span class="info-label blod-title">商圈:</span>
                        <span id="busArea"></span>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" >
                        <span class="info-label blod-title">供货对象:</span>
                        <span id="controlUserTypes"></span>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" >
                        <span class="info-label blod-title">黑名单控销组id:</span>
                        <span id="controlBlackGroupId"  style="word-break:break-all;"></span>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" >
                        <span class="info-label blod-title">白名单控销组id:</span>
                        <span id="controlWhiteGroupId" style="word-break:break-all;"></span>
                    </div>
                </div>
            </div>
            <!-- 商品限购信息 -->
            <div class="panel-body">
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label blod-title">限购类型:</span>
                        <span id="purchaseType"></span>
                    </div>
                </div>
                <div class="col-md-4  productLimit">
                    <div class="input-group" >
                        <span class="info-label">限购数量:</span>
                        <span id="limitedQty"></span>
                    </div>
                </div>
                <div class="col-md-4  productLimit productLimitTime">
                    <div class="input-group" >
                        <span class="info-label">限购开始时间:</span>
                        <span id="purchaseTimeStart"></span>
                    </div>
                </div>
                <div class="col-md-4  productLimit productLimitTime">
                    <div class="input-group" >
                        <span class="info-label">限购结束时间:</span>
                        <span id="purchaseTimeEnd"></span>
                    </div>
                </div>
                <div class="col-md-12 productLimit">
                    <div class="input-group" >
                        <span class="info-label blod-title">限购区域:</span>
                        <span id="areaCodeName"></span>
                    </div>
                </div>
                <div class="col-md-12 productLimit">
                    <div class="input-group" >
                        <span class="info-label blod-title">限购客户类型:</span>
                        <span id="userTypeName"></span>
                    </div>
                </div>
            </div>
            <!-- 店铺控销 -->
            <div class="panel-body">
                <div class="col-md-12">
                    <div class="input-group" >
                        <span class="info-label blod-title">店铺供货类型:</span>
                        <span id="shopControl"></span>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" >
                        <span class="info-label blod-title">店铺可售区域:</span>
                        <span id="shopSaleArea"></span>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" >
                        <span class="info-label blod-title">店铺黑名单:</span>
                        <span id="shopBlackBuyer"></span>
                    </div>
                </div>
            </div>
            <!-- 商品销售时间-->
            <div class="panel-body saleTimeInfo" >
                <div class="col-md-12">
                    <div class="input-group" >
                        <span class="info-label">销售时间计划Id:</span>
                        <span id="saleTimeConfigId"></span>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" >
                        <span class="info-label">销售时间状态:</span>
                        <span id="saleTimeStatus"></span>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="input-group" >
                        <span class="info-label blod-title">销售时间:</span>
                        <span id="saleTime" style="display: inline-grid;"></span>
                    </div>
                </div>
            </div>
            <!-- 商品控销信息 -->
            <div class="panel-body noneProductControl">
                <span class="info-label" style="color: red;">暂无控销信息</span>
            </div>
            <div class="panel-body productControl">
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label blod-title">控销id:</span>
                        <span id="conId"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">csuid:</span>
                        <span id="csuid"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">控销开始时间:</span>
                        <span id="conStart"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group" >
                        <span class="info-label">控销结束时间:</span>
                        <span id="conEnd"></span>
                    </div>
                </div>
            </div>
            <div class="panel-body productControl">
                <!-- 控销信息-->
                <div class="tab-title">
                    <div class="selectedTab" onclick="changeTitle(0)">可见可买</div>
                    <div onclick="changeTitle(1)">不可见不可买</div>
                </div>
                <div class="tab-cont">
                    <!-- 可见可买 -->
                    <div class="selectedCont">
                        <!-- 控销计划 -->
                        <div>控销计划</div>
                        <table id="whitePlan">

                        </table>
                        <!-- 控销客户 -->
                        <div>控销用户</div>
                        <table id="whiteCust">

                        </table>
                    </div>
                    <div>
                        <!-- 控销计划 -->
                        <div>控销计划</div>
                        <table id="hidePlan">
                        </table>
                        <!-- 控销客户 -->
                        <div>控销用户</div>
                        <table id="hideCust">

                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script>
    //列表定义
    var columns = [
        {
            field: 'merchantId',
            title: '客户编码'
        },{
            field: 'realName',
            title: '客户名称'
        },{
            field: 'mobile',
            title: '手机号'
        },{
            field: 'address',
            title: '地址'
        },{
            field: 'businessTypeName',
            title: '客户类型'
        }
    ];
    $('#whiteCust').bootstrapTable({
        data: [],
        classes: 'table table-hover',
        striped: true,                      //是否显示行间隔色
        search: false,                      //是否显示表格搜索
        strictSearch: false,
        showColumns: false,                  //是否显示所有的列（选择显示的列）
        showRefresh: false,                  //是否显示刷新按钮
        clickToSelect: true,                //是否启用点击选中行
        uniqueId: "fid",                     //每一行的唯一标识，一般为主键列
        showToggle: false,                   //是否显示详细视图和列表视图的切换按钮
        cardView: false,                    //是否显示详细视图
        detailView: false,                  //是否显示父子表
        columns: columns
    });
    $('#hideCust').bootstrapTable({
        data: [],
        classes: 'table table-hover',
        striped: true,                      //是否显示行间隔色
        search: false,                      //是否显示表格搜索
        strictSearch: false,
        showColumns: false,                  //是否显示所有的列（选择显示的列）
        showRefresh: false,                  //是否显示刷新按钮
        clickToSelect: true,                //是否启用点击选中行
        uniqueId: "fid",                     //每一行的唯一标识，一般为主键列
        showToggle: false,                   //是否显示详细视图和列表视图的切换按钮
        cardView: false,                    //是否显示详细视图
        detailView: false,                  //是否显示父子表
        columns: columns
    });

    var planColumns = [
        {
            field: 'areaCodes',
            title: '控销区域',
            formatter:function(values, row){
                if(!values){
                    return "";
                }
                var arrs = [];
                values.forEach(item=>arrs.push(item.areaName));
                return arrs.join(",");
            }
        },{
            field: 'userTypes',
            title: '客户类型',
            formatter:function(values, row){
                if(!values){
                    return "";
                }
                var arrs = [];
                values.forEach(item=>arrs.push(item.value));
                return arrs.join(",");
            }
        }
    ];
    $('#whitePlan').bootstrapTable({
        data: [],
        classes: 'table table-hover',
        striped: true,                      //是否显示行间隔色
        search: false,                      //是否显示表格搜索
        strictSearch: false,
        showColumns: false,                  //是否显示所有的列（选择显示的列）
        showRefresh: false,                  //是否显示刷新按钮
        clickToSelect: true,                //是否启用点击选中行
        uniqueId: "fid",                     //每一行的唯一标识，一般为主键列
        showToggle: false,                   //是否显示详细视图和列表视图的切换按钮
        cardView: false,                    //是否显示详细视图
        detailView: false,                  //是否显示父子表
        columns: planColumns
    });
    $('#hidePlan').bootstrapTable({
        data: [],
        classes: 'table table-hover',
        striped: true,                      //是否显示行间隔色
        search: false,                      //是否显示表格搜索
        strictSearch: false,
        showColumns: false,                  //是否显示所有的列（选择显示的列）
        showRefresh: false,                  //是否显示刷新按钮
        clickToSelect: true,                //是否启用点击选中行
        uniqueId: "fid",                     //每一行的唯一标识，一般为主键列
        showToggle: false,                   //是否显示详细视图和列表视图的切换按钮
        cardView: false,                    //是否显示详细视图
        detailView: false,                  //是否显示父子表
        columns: planColumns
    });
</script>
</html>
