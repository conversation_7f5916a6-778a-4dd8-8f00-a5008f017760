package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.product.api.PopSkuApi;
import com.xyy.ec.pop.server.api.product.api.task.PopSkuTaskApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class PopSkuTaskRemote {
    @Reference
    private PopSkuTaskApi popSkuTaskApi;
    @Reference
    private PopSkuApi popSkuApi;

    public Boolean deletePopSkus(List<String> barcodes){
        if (CollectionUtils.isEmpty(barcodes)){
            return Boolean.FALSE;
        }
        try {
            ApiRPCResult<Boolean> result = popSkuApi.deleteLogicByBarcodes(barcodes, "system");
            if (result == null || result.isFail()){
                return Boolean.FALSE;
            }
            return result.getData();
        }catch (Exception e){
            log.error("PopSkuTaskRemote.deletePopSku#barcodes:{} 异常", JSON.toJSONString(barcodes), e);
            return Boolean.FALSE;
        }
    }

    public String saveProductSyncTask(List<String> barcodes){
        if (CollectionUtils.isEmpty(barcodes)){
            return "barcodes不能为空";
        }
        try {
            ApiRPCResult<String> result = popSkuTaskApi.saveProductSyncTask(barcodes);
            if (result == null || result.isFail()){
                return "任务保存异常";
            }
            return result.getData();
        }catch (Exception e){
            log.error("PopSkuTaskRemote.saveProductSyncTask#barcodes:{} 异常", JSON.toJSONString(barcodes), e);
            return "任务保存异常";
        }
    }
}
