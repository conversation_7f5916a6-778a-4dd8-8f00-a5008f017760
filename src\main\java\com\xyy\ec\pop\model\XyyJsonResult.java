package com.xyy.ec.pop.model;

import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 信息JSON响应信息。
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class XyyJsonResult implements Serializable {

    private static final long serialVersionUID = 2356247226403445810L;

    /**
     * status：成功
     */
    private static final String STATUS_SUCCESS = "success";
    /**
     * status：失败
     */
    private static final String STATUS_FAILURE = "failure";

    /**
     * 应用名
     */
    private String appName;

    /**
     * 返回码
     */
    private int code;

    /**
     * 状态，后端设计为此值根据code码生成。
     */
    private String status;

    /**
     * 返回信息
     */
    private String msg;

    /**
     * 返回值数据
     */
    private Map<String, Object> data = new HashMap<>(16);

    /**
     * 直接返回数据
     *
     * @deprecated 过时，禁止使用。
     */
    @Deprecated
    private Object result;


    /**
     * 创建
     *
     * @param appName
     * @param code
     * @param msg
     * @return
     */
    public static XyyJsonResult create(String appName, int code, String msg) {
        return new XyyJsonResult(appName, code, msg);
    }

    /**
     * 创建
     *
     * @param xyyJsonResultCodeEnum
     * @return
     */
    public static XyyJsonResult create(XyyJsonResultCodeEnum xyyJsonResultCodeEnum) {
        return new XyyJsonResult(xyyJsonResultCodeEnum);
    }

    /**
     * 创建
     *
     * @param xyyJsonResultCodeEnum
     * @param overrideApiResultCodeMsg
     * @return
     */
    public static XyyJsonResult create(XyyJsonResultCodeEnum xyyJsonResultCodeEnum, String overrideApiResultCodeMsg) {
        return new XyyJsonResult(xyyJsonResultCodeEnum, overrideApiResultCodeMsg);
    }

    /**
     * 创建成功
     *
     * @return
     */
    public static XyyJsonResult createSuccess() {
        return new XyyJsonResult(XyyJsonResultCodeEnum.SUCCESS);
    }

    /**
     * 创建失败
     *
     * @return
     */
    public static XyyJsonResult createFailure() {
        return new XyyJsonResult(XyyJsonResultCodeEnum.FAIL);
    }

    /**
     * 设置appName
     *
     * @param appName
     * @return
     */
    public XyyJsonResult appName(String appName) {
        this.appName = appName;
        return this;
    }

    /**
     * 设置code
     *
     * @param code
     * @return
     */
    public XyyJsonResult code(int code) {
        this.code = code;
        return this;
    }

    /**
     * 设置信息
     *
     * @param msg
     * @return
     */
    public XyyJsonResult msg(String msg) {
        this.msg = msg;
        return this;
    }

    /**
     * 添加结果
     *
     * @param key
     * @param value
     * @return
     */
    public XyyJsonResult addResult(String key, Object value) {
        this.data.put(key, value);
        return this;
    }

    /**
     * 添加结果
     *
     * @param result
     * @return
     *
     * @deprecated 过时，禁止使用。
     */
    @Deprecated
    public XyyJsonResult addResult(Object result) {
        this.result = result;
        return this;
    }


    /**
     * JSON序列化后，对应属性key为<code>success</code>。
     *
     * @return
     */
    public boolean isSuccess() {
        return XyyJsonResultCodeEnum.SUCCESS.getCode() == this.code;
    }

    /**
     * JSON序列化后，对应属性key为<code>globalCode</code>。
     *
     * @return
     */
    public String getGlobalCode() {
        return this.appName + "_" + this.code;
    }

    /* 默认构造 */
    public XyyJsonResult() {
    }

    /* （私有）构造方法 */
    private XyyJsonResult(String appName, int code, String msg) {
        this.appName = appName;
        this.code = code;
        this.status = this.generateStatusByCode(code);
        this.msg = msg;
    }

    private XyyJsonResult(XyyJsonResultCodeEnum xyyJsonResultCodeEnum) {
        this.appName = xyyJsonResultCodeEnum.getAppName();
        this.code = xyyJsonResultCodeEnum.getCode();
        this.status = this.generateStatusByCode(xyyJsonResultCodeEnum.getCode());
        this.msg = xyyJsonResultCodeEnum.getMsg();
    }

    // 为了应对可变参数，强制覆盖MessageCodeEnum的msg

    /**
     * 构造方法。强制覆盖{@link XyyJsonResultCodeEnum}的<code>msg</code>。
     *
     * @param xyyJsonResultCodeEnum
     * @param overrideApiResultCodeMsg
     */
    private XyyJsonResult(XyyJsonResultCodeEnum xyyJsonResultCodeEnum, String overrideApiResultCodeMsg) {
        this.appName = xyyJsonResultCodeEnum.getAppName();
        this.code = xyyJsonResultCodeEnum.getCode();
        this.status = this.generateStatusByCode(xyyJsonResultCodeEnum.getCode());
        this.msg = overrideApiResultCodeMsg;
    }

    /**
     * 根据code生成status
     *
     * @param code
     * @return
     */
    private String generateStatusByCode(int code) {
        String status = STATUS_SUCCESS;
        if (XyyJsonResultCodeEnum.SUCCESS.getCode() != code) {
            status = STATUS_FAILURE;
        }
        return status;
    }

}
