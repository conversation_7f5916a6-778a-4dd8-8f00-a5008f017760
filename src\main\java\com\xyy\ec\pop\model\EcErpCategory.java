package com.xyy.ec.pop.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 分类信息
 * <AUTHOR>
 */
@ApiModel("分类信息")
public class EcErpCategory implements Serializable{

    private static final long serialVersionUID = 1L;

    /** 分类id */
    @ApiModelProperty("分类id")
    private Long id;

    /** 分类编码 */
    @ApiModelProperty("分类编码")
    private String code;

    /** 分类名称 */
    @ApiModelProperty("分类名称")
    private String name;

    /** 排序序号 */
    @ApiModelProperty("排序序号")
    private Integer priority;

    /** 父分类id */
    @ApiModelProperty("父分类id")
    private Long parentId;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    
    /**所属级别*/
    @ApiModelProperty("所属级别")
    private Integer level;

    /** 级别名称 */
    @ApiModelProperty("级别名称")
    private String levelName;

    /** 操作人 */
    @ApiModelProperty("操作人")
    private String operator;

    /** erp对应名称 */
    @ApiModelProperty("erp对应名称")
    private String erpName;

    /**
     * 一级分类id
     */
    @ApiModelProperty("一级分类id")
    private Long firstCategoryId;
    /**
     * 一级分类名称
     */
    @ApiModelProperty("一级分类名称")
    private String firstCategoryName;
    /**
     * 二级分类id
     */
    @ApiModelProperty("二级分类id")
    private Long secondCategoryId;
    /**
     * 二级分类名称
     */
    @ApiModelProperty("二级分类名称")
    private String secondCategoryName;
    /**
     * 三级分类id
     */
    @ApiModelProperty("三级分类id")
    private Long thirdCategoryId;
    /**
     * 三级分类名称
     */
    @ApiModelProperty("三级分类名称")
    private String thirdCategoryName;

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    /**是否显示**/
    @ApiModelProperty("是否显示")
    private Byte status;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    /**图标**/
    @ApiModelProperty("图标")
    private String icon;

    /**
     *
     * @return 返回数据库表tb_category的id字段值
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id 对应数据库表tb_category的id字段
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return 返回数据库表tb_category的code字段值
     */
    public String getCode() {
        return code;
    }

    /**
     * @param code 对应数据库表tb_category的code字段
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     *
     * @return 返回数据库表tb_category的name字段值
     */
    public String getName() {
        return name;
    }

    /**
     * @param name 对应数据库表tb_category的name字段
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     *
     * @return 返回数据库表tb_category的priority字段值
     */
    public Integer getPriority() {
        return priority;
    }

    /**
     * @param priority 对应数据库表tb_category的priority字段
     */
    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    /**
     *
     * @return 返回数据库表tb_category的parent_id字段值
     */
    public Long getParentId() {
        return parentId;
    }

    /**
     * @param parentId 对应数据库表tb_category的parent_id字段
     */
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    /**
     *
     * @return 返回数据库表tb_category的create_time字段值
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime 对应数据库表tb_category的create_time字段
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     *
     * @return 返回数据库表tb_category的update_time字段值
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime 对应数据库表tb_category的update_time字段
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getErpName() {
        return erpName;
    }

    public void setErpName(String erpName) {
        this.erpName = erpName;
    }

    public Long getFirstCategoryId() {
        return firstCategoryId;
    }

    public void setFirstCategoryId(Long firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public Long getSecondCategoryId() {
        return secondCategoryId;
    }

    public void setSecondCategoryId(Long secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public void setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
    }

    public Long getThirdCategoryId() {
        return thirdCategoryId;
    }

    public void setThirdCategoryId(Long thirdCategoryId) {
        this.thirdCategoryId = thirdCategoryId;
    }

    public String getThirdCategoryName() {
        return thirdCategoryName;
    }

    public void setThirdCategoryName(String thirdCategoryName) {
        this.thirdCategoryName = thirdCategoryName;
    }
}