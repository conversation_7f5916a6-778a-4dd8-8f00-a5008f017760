package com.xyy.ec.pop.service.settle.impl;

import com.google.common.collect.Lists;
import com.xyy.ec.pop.dao.PopBillDetailMapper;
import com.xyy.ec.pop.po.PopBillDetailPo;
import com.xyy.ec.pop.service.settle.PopBillDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @date  2020/12/1 10:42
* @table
*/
@Slf4j
@Service
public class PopBillDetailServiceImpl implements PopBillDetailService {

    @Autowired
    private PopBillDetailMapper popBillDetailMapper;

    @Override
    public int insert(PopBillDetailPo record) {
        return popBillDetailMapper.insert(record);
    }

    @Override
    public int insertSelective(PopBillDetailPo record) {
        return popBillDetailMapper.insertSelective(record);
    }

    @Override
    public PopBillDetailPo selectByPrimaryKey(Long id) {
        return popBillDetailMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(PopBillDetailPo record) {
        return popBillDetailMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PopBillDetailPo record) {
        return popBillDetailMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<PopBillDetailPo> queryPopBillDetail(PopBillDetailPo popBillDetailPo, Integer pageNum, Integer pageSize) {
        return popBillDetailMapper.queryPopBillDetail(popBillDetailPo,pageNum,pageSize);
    }

    @Override
    public Long queryPopBillDetailCount(List<String> billNoList) {
        return popBillDetailMapper.queryPopBillDetailCount(billNoList);
    }

    @Override
    public List<PopBillDetailPo> queryPopBillDetailByBillNoList(List<String> billNoList) {
        return popBillDetailMapper.queryPopBillDetailByBillNoList(billNoList);
    }

    @Override
    public String queryPopBillNoByBusinessNo(String BusinessNo) {
        return popBillDetailMapper.selectPopBillNoByBusinessNo(BusinessNo);
    }
}
