package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.backend.pop.api.PopOrderApi;
import com.xyy.ec.order.backend.pop.dto.AfterSalesDto;
import com.xyy.ec.order.backend.pop.dto.PopOrderDto;
import com.xyy.ec.order.backend.sellercenter.api.SellerOrderRefundApi;
import com.xyy.ec.order.business.api.OrderRefundBusinessApi;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesDetailVo;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesQueryParam;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesSellerParamDto;
import com.xyy.ec.order.business.dto.afterSales.ReturnLogisticsDto;
import com.xyy.ec.order.business.enums.afterSales.AfterSalesTypeEnum;
import com.xyy.ec.order.search.api.remote.dto.AfterSalesEsDto;
import com.xyy.ec.order.search.api.remote.dto.OrderRefundSearchQueryDto;
import com.xyy.ec.order.search.api.remote.result.SearchResultDto;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.helper.AfterSalesBeanHelper;
import com.xyy.ec.pop.helper.OrderEsVoHelper;
import com.xyy.ec.pop.remote.CorporationRemote;
import com.xyy.ec.pop.remote.EcOrderEsRemote;
import com.xyy.ec.pop.remote.EcOrderRemote;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.service.AfterSalesServiceV2;
import com.xyy.ec.pop.utils.CollectionUtil;
import com.xyy.ec.pop.utils.CopyUtil;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.afterSales.AfterSaleQueryParamVo;
import com.xyy.ec.pop.vo.afterSales.AfterSalesInfoVo;
import com.xyy.ec.pop.vo.afterSales.AfterSalesLogisticsVo;
import com.xyy.ec.pop.vo.afterSales.AfterSalesVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AfterSalesServiceV2Impl implements AfterSalesServiceV2 {

    @Autowired
    private EcOrderRemote ecOrderRemote;
    @Autowired
    private EcOrderEsRemote ecOrderEsRemote;

    @Reference
    private PopOrderApi ecPopOrderApi;
    @Reference
    private OrderRefundBusinessApi orderRefundBusinessApi;
    @Reference
    private SellerOrderRefundApi sellerOrderRefundApi;
    @Autowired
    private CorporationRemote corporationRemote;
    @Override
    public PageInfo<AfterSalesVo> queryPage(AfterSaleQueryParamVo param) {
        log.info("afterSales queryPage param:{}",JSONObject.toJSONString(param));
        OrderRefundSearchQueryDto orderRefundSearchQueryDto = OrderEsVoHelper.convertAfterSalesParam2SearchQueryDto(param);
        orderRefundSearchQueryDto.setDocType(2);
        log.info("afterSales queryPage es param:{}",JSONObject.toJSONString(orderRefundSearchQueryDto));
        SearchResultDto<AfterSalesEsDto> searchResultDto = ecOrderEsRemote.afterSalesOrderSearch(orderRefundSearchQueryDto);
        log.info("afterSales queryPage es result:{}",JSONObject.toJSONString(searchResultDto));

        if (Objects.isNull(searchResultDto)  || CollectionUtils.isEmpty(searchResultDto.getResultList())) {
            PageInfo page = new PageInfo<>();
            page.setPageSize(10);
            page.setPageNum(1);
            page.setTotal(0);
            page.setPages(1);
            return page;
        }
        List<AfterSalesEsDto> resultList = searchResultDto.getResultList();
        long totalCount = searchResultDto.getTotalCount();
        List<AfterSalesVo> afterSalesVos = CopyUtil.copyList(resultList, AfterSalesVo.class);
//        AfterSalesBeanHelper.
        List<String> afterSalesNoList = resultList.stream().filter(x-> StringUtils.isNotBlank(x.getAfterSalesNo())).map(AfterSalesEsDto::getAfterSalesNo).distinct().collect(Collectors.toList());
        log.info("afterSales queryPage afterSalesNoList:{}",JSONObject.toJSONString(afterSalesNoList));

        //查询退款单列表
        ApiRPCResult<List<AfterSalesDto>> result = sellerOrderRefundApi.queryAfterSalesList(afterSalesNoList);
        log.info("queryAfterSalesList response ecParam:{},result:{}", afterSalesNoList, JSON.toJSONString(result));
        if (result.isFail() || CollectionUtils.isEmpty(result.getData())) {
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL,"查询售后单异常");
        }
        final List<AfterSalesDto> afterSalesDtos = result.getData();
        Map<String, AfterSalesDto> afterSalesMap = afterSalesDtos.stream().collect(Collectors.toMap(AfterSalesDto::getAfterSalesNo, Function.identity(), (x1, x2) -> x1));

        List<String> orderNoList = resultList.stream().map(AfterSalesEsDto::getOrderNo).distinct().collect(Collectors.toList());
        ApiRPCResult<List<PopOrderDto>> apiRPCResult = ecPopOrderApi.queryOrderBaseList(orderNoList);
        log.info("afterSales queryPage queryOrderBase :{}",JSON.toJSONString(apiRPCResult));
        if (apiRPCResult.isFail()) {
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL,"查询异常");
        }
        List<PopOrderDto> orderList = apiRPCResult.getData();
        List<String> orgIds = orderList.stream().map(PopOrderDto::getOrgId).distinct().collect(Collectors.toList());

        List<CorporationDto> corporationList = corporationRemote.queryCorpBaseByOrgIds(orgIds);
        if (CollectionUtil.isEmpty(corporationList)) {

        }
        final Map<String, CorporationDto> orgIdMap = corporationList.stream().distinct().collect(Collectors.toMap(CorporationDto::getOrgId, Function.identity(), (key1, key2) -> key2));


        Map<String, PopOrderDto> orderMap = orderList.stream().collect(Collectors.toMap(PopOrderDto::getOrderNo, Function.identity(), (x1, x2) -> x1));
        for (AfterSalesVo vo : afterSalesVos) {
            AfterSalesDto afterSalesDto = afterSalesMap.get(vo.getAfterSalesNo());
            if (afterSalesDto == null) {
                log.info("queryAfterSalesList afterSalesMap is empty:{}",vo.getAfterSalesNo());
            continue;
            }

            vo.setOrderNo(afterSalesDto.getOrderNo());
            vo.setCreateTime(DateUtil.date2Str(afterSalesDto.getCreateTime(),DateUtil.PATTERN_STANDARD));
            vo.setAuditProcessState(afterSalesDto.getAuditProcessState());
            vo.setOperateTime(afterSalesDto.getOperateTime());
            vo.setOperator(afterSalesDto.getOperator());
            vo.setAfterSalesType(afterSalesDto.getAfterSalesType());
            vo.setAfterSalesTypeName(afterSalesDto.getAfterSalesTypeName());
            vo.setLastDealInfo(afterSalesDto.getLastDealInfo());
            vo.setSellerRemark(afterSalesDto.getSellerRemark());
            vo.setAfterSalesCount(afterSalesDto.getAfterSalesCount());

            if (AfterSalesTypeEnum.CREDENTIAL.getKey().equals(afterSalesDto.getAfterSalesType())
                    && StringUtils.isNotBlank(afterSalesDto.getAfterSalesInfo())
                    && afterSalesDto.getAfterSalesInfo().length() >30) {
                vo.setAfterSalesInfo(afterSalesDto.getAfterSalesInfo().substring(0,30)+"...");
            } else {
                vo.setAfterSalesInfo(afterSalesDto.getAfterSalesInfo());
            }

            PopOrderDto popOrderDto = orderMap.get(vo.getOrderNo());
            vo.setOrderId(vo.getPopOrderId());
            vo.setOrgId(popOrderDto.getOrgId());
            vo.setMerchantName(popOrderDto.getMerchantName());
            vo.setMobile(popOrderDto.getMobile());
            vo.setAddress(popOrderDto.getAddress());
            vo.setOrderStatus(popOrderDto.getStatus());
            vo.setShopName(popOrderDto.getCompanyName());

            CorporationDto corInfo = orgIdMap.get(popOrderDto.getOrgId());
            vo.setCompanyName(corInfo.getCompanyName());


        }
        PageInfo page = new PageInfo<>();
        page.setPageSize(param.getPageSize());
        page.setPageNum(param.getPageNo());
        page.setTotal(searchResultDto.getTotalCount());
        page.setList(afterSalesVos);
        int pages = (searchResultDto.getTotalCount().intValue() + param.getPageSize() - 1) / param.getPageSize();

        page.setPages(pages);

        return page;
    }

    @Override
    public Map<String,Object> queryAfterSalesDetail(String afterSalesNo) {
        Map result = new HashMap<>();

        AfterSalesQueryParam param = new AfterSalesQueryParam();
        param.setAfterSalesNo(afterSalesNo);
        final ApiRPCResult<AfterSalesDetailVo> res = orderRefundBusinessApi.queryAfterSalesDetail(param);
        if (res.isFail()) {
            throw new RuntimeException();
        }
        AfterSalesDetailVo data = res.getData();
        final AfterSalesInfoVo afterSalesInfoVo = AfterSalesBeanHelper.convert(data);
        result.put("afterSaleInfo",afterSalesInfoVo);

        result.put("auditRecords",data.getAuditRecords());

        result.put("auditProcessList",data.getAuditProcessList());
        return result;
    }

    @Override
    public Map<String, Integer> queryAfterSalesStatusCount(AfterSaleQueryParamVo param) {
        Map result = new HashMap<>();
        result.put("waitSellerHandleCount",6);
        result.put("waitSellerReceiveCount",7);
        result.put("waitDeliveryCount",8);
        return result;
    }

    @Override
    public void saveSellerRemark(AfterSalesSellerParamDto param) {
        final ApiRPCResult apiRPCResult = orderRefundBusinessApi.saveSellerRemark(param);
    }

    @Override
    public void saveSellerOperate(AfterSalesSellerParamDto param) {

        final ApiRPCResult apiRPCResult = orderRefundBusinessApi.saveSellerOperate(param);
    }

    @Override
    public AfterSalesLogisticsVo queryLogistics(String afterSalesNo) {

        final ApiRPCResult<ReturnLogisticsDto> result = orderRefundBusinessApi.queryReturnLogistics(afterSalesNo);
        if (result.isSuccess() && result.getData() != null) {
            final ReturnLogisticsDto data = result.getData();
            AfterSalesLogisticsVo vo = new AfterSalesLogisticsVo();
            vo.setLogisticsCompany(data.getExpressName());
            vo.setTrackingNo(data.getExpressNo());
            vo.setEvidences(data.getEvidences());

            return vo;

        }
        return null;
    }
}
