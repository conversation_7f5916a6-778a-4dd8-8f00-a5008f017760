package com.xyy.ec.pop.service;


import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.vo.*;

import java.util.List;

public interface PopSkuCategoryService {
    Page<ProductCommissionVo> list(ProductCommissionQueryVo queryVo);

    BatchUpdateResultVo batchUpdate(List<ProductCommissionBatchUpdateVo> vos, String userName) throws ServiceException;

    boolean updateCommission(ProductCommissionBatchUpdateVo updateVo, String username) throws ServiceException;

    CommissionRatioOperateLogVo queryCommissionRatioLog(String barcode) throws ServiceException;

    boolean deleteCommission(List<String> barcodes, String username) throws ServiceException;

}
