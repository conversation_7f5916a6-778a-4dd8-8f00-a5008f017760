<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.ec.pop.dao.PopBillPaymentDetailMapper">
  <resultMap id="BaseResultMap" type="com.xyy.ec.pop.po.PopBillPaymentDetailPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="product_money" jdbcType="DECIMAL" property="productMoney" />
    <result column="total_money" jdbcType="DECIMAL" property="totalMoney" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="freight_amount" jdbcType="DECIMAL" property="freightAmount" />
    <result column="coupon_shop_amount" jdbcType="DECIMAL" property="couponShopAmount" />
    <result column="marketing_shop_amount" jdbcType="DECIMAL" property="marketingShopAmount" />
    <result column="shop_total_discount" jdbcType="DECIMAL" property="shopTotalDiscount" />
    <result column="coupon_platform_amount" jdbcType="DECIMAL" property="couponPlatformAmount" />
    <result column="marketing_platform_amount" jdbcType="DECIMAL" property="marketingPlatformAmount" />
    <result column="platform_total_discount" jdbcType="DECIMAL" property="platformTotalDiscount" />
    <result column="hire_money" jdbcType="DECIMAL" property="hireMoney" />
    <result column="payable_commission" jdbcType="DECIMAL" property="payableCommission" />
    <result column="deducted_commission" jdbcType="DECIMAL" property="deductedCommission" />
    <result column="deducted" jdbcType="TINYINT" property="deducted" />
    <result column="settlement_type" jdbcType="TINYINT" property="settlementType" />
    <result column="penalty_amount" jdbcType="DECIMAL" property="penaltyAmount" />
    <result column="statement_total_money" jdbcType="DECIMAL" property="statementTotalMoney" />
    <result column="pay_type" jdbcType="TINYINT" property="payType" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="bill_create_time" jdbcType="TIMESTAMP" property="billCreateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="cash_pay_amount" jdbcType="DECIMAL" property="cashPayAmount" />
    <result column="virtual_gold" jdbcType="DECIMAL" property="virtualGold" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_id, org_name, bill_no, product_money, total_money, money, freight_amount, 
    coupon_shop_amount, marketing_shop_amount, shop_total_discount, coupon_platform_amount, 
    marketing_platform_amount, platform_total_discount, hire_money,payable_commission,deducted_commission,deducted,settlement_type, penalty_amount, statement_total_money,
    pay_type, flow_no, bill_create_time, create_time, update_time, cash_pay_amount, virtual_gold
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_xyy_pop_bill_payment_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tb_xyy_pop_bill_payment_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xyy.ec.pop.po.PopBillPaymentDetailPo" useGeneratedKeys="true">
    insert into tb_xyy_pop_bill_payment_detail (org_id, org_name, bill_no, 
      product_money, total_money, money, 
      freight_amount, coupon_shop_amount, marketing_shop_amount, 
      shop_total_discount, coupon_platform_amount, 
      marketing_platform_amount, platform_total_discount, 
      hire_money,payable_commission,settlement_type, penalty_amount, statement_total_money,
      pay_type, flow_no, bill_create_time, 
      create_time, update_time)
    values (#{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, #{billNo,jdbcType=VARCHAR}, 
      #{productMoney,jdbcType=DECIMAL}, #{totalMoney,jdbcType=DECIMAL}, #{money,jdbcType=DECIMAL}, 
      #{freightAmount,jdbcType=DECIMAL}, #{couponShopAmount,jdbcType=DECIMAL}, #{marketingShopAmount,jdbcType=DECIMAL}, 
      #{shopTotalDiscount,jdbcType=DECIMAL}, #{couponPlatformAmount,jdbcType=DECIMAL}, 
      #{marketingPlatformAmount,jdbcType=DECIMAL}, #{platformTotalDiscount,jdbcType=DECIMAL}, 
      #{hireMoney,jdbcType=DECIMAL},#{payableCommission,jdbcType=DECIMAL},#{settlementType,jdbcType=TINYINT}, #{penaltyAmount,jdbcType=DECIMAL}, #{statementTotalMoney,jdbcType=DECIMAL},
      #{payType,jdbcType=TINYINT}, #{flowNo,jdbcType=VARCHAR}, #{billCreateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xyy.ec.pop.po.PopBillPaymentDetailPo" useGeneratedKeys="true">
    insert into tb_xyy_pop_bill_payment_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="productMoney != null">
        product_money,
      </if>
      <if test="totalMoney != null">
        total_money,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="freightAmount != null">
        freight_amount,
      </if>
      <if test="couponShopAmount != null">
        coupon_shop_amount,
      </if>
      <if test="marketingShopAmount != null">
        marketing_shop_amount,
      </if>
      <if test="shopTotalDiscount != null">
        shop_total_discount,
      </if>
      <if test="couponPlatformAmount != null">
        coupon_platform_amount,
      </if>
      <if test="marketingPlatformAmount != null">
        marketing_platform_amount,
      </if>
      <if test="platformTotalDiscount != null">
        platform_total_discount,
      </if>
      <if test="hireMoney != null">
        hire_money,
      </if>
      <if test="payableCommission != null">
        payable_commission,
      </if>
      <if test="settlementType != null">
        settlement_type,
      </if>
      <if test="penaltyAmount != null">
        penalty_amount,
      </if>
      <if test="statementTotalMoney != null">
        statement_total_money,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="flowNo != null">
        flow_no,
      </if>
      <if test="billCreateTime != null">
        bill_create_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="productMoney != null">
        #{productMoney,jdbcType=DECIMAL},
      </if>
      <if test="totalMoney != null">
        #{totalMoney,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="freightAmount != null">
        #{freightAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponShopAmount != null">
        #{couponShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingShopAmount != null">
        #{marketingShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="shopTotalDiscount != null">
        #{shopTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="couponPlatformAmount != null">
        #{couponPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingPlatformAmount != null">
        #{marketingPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="platformTotalDiscount != null">
        #{platformTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="hireMoney != null">
        #{hireMoney,jdbcType=DECIMAL},
      </if>
      <if test="payableCommission != null">
        #{payableCommission,jdbcType=DECIMAL},
      </if>
      <if test="settlementType  != null">
        #{settlementType ,jdbcType=TINYINT},
      </if>
      <if test="penaltyAmount != null">
        #{penaltyAmount,jdbcType=DECIMAL},
      </if>
      <if test="statementTotalMoney != null">
        #{statementTotalMoney,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=TINYINT},
      </if>
      <if test="flowNo != null">
        #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="billCreateTime != null">
        #{billCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xyy.ec.pop.po.PopBillPaymentDetailPo">
    update tb_xyy_pop_bill_payment_detail
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="productMoney != null">
        product_money = #{productMoney,jdbcType=DECIMAL},
      </if>
      <if test="totalMoney != null">
        total_money = #{totalMoney,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="freightAmount != null">
        freight_amount = #{freightAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponShopAmount != null">
        coupon_shop_amount = #{couponShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingShopAmount != null">
        marketing_shop_amount = #{marketingShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="shopTotalDiscount != null">
        shop_total_discount = #{shopTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="couponPlatformAmount != null">
        coupon_platform_amount = #{couponPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingPlatformAmount != null">
        marketing_platform_amount = #{marketingPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="platformTotalDiscount != null">
        platform_total_discount = #{platformTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="hireMoney != null">
        hire_money = #{hireMoney,jdbcType=DECIMAL},
      </if>
      <if test="payableCommission != null">
        payable_commission =  #{payableCommission,jdbcType=DECIMAL},
      </if>
      <if test="settlementType  != null">
        settlement_type = #{settlementType ,jdbcType=TINYINT},
      </if>
      <if test="penaltyAmount != null">
        penalty_amount = #{penaltyAmount,jdbcType=DECIMAL},
      </if>
      <if test="statementTotalMoney != null">
        statement_total_money = #{statementTotalMoney,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=TINYINT},
      </if>
      <if test="flowNo != null">
        flow_no = #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="billCreateTime != null">
        bill_create_time = #{billCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xyy.ec.pop.po.PopBillPaymentDetailPo">
    update tb_xyy_pop_bill_payment_detail
    set org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      bill_no = #{billNo,jdbcType=VARCHAR},
      product_money = #{productMoney,jdbcType=DECIMAL},
      total_money = #{totalMoney,jdbcType=DECIMAL},
      money = #{money,jdbcType=DECIMAL},
      freight_amount = #{freightAmount,jdbcType=DECIMAL},
      coupon_shop_amount = #{couponShopAmount,jdbcType=DECIMAL},
      marketing_shop_amount = #{marketingShopAmount,jdbcType=DECIMAL},
      shop_total_discount = #{shopTotalDiscount,jdbcType=DECIMAL},
      coupon_platform_amount = #{couponPlatformAmount,jdbcType=DECIMAL},
      marketing_platform_amount = #{marketingPlatformAmount,jdbcType=DECIMAL},
      platform_total_discount = #{platformTotalDiscount,jdbcType=DECIMAL},
      hire_money = #{hireMoney,jdbcType=DECIMAL},
      payable_commission =  #{payableCommission,jdbcType=DECIMAL},
      settlement_type = #{settlementType ,jdbcType=TINYINT},
      penalty_amount = #{penaltyAmount,jdbcType=DECIMAL},
      statement_total_money = #{statementTotalMoney,jdbcType=DECIMAL},
      pay_type = #{payType,jdbcType=TINYINT},
      flow_no = #{flowNo,jdbcType=VARCHAR},
      bill_create_time = #{billCreateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="queryPopBillPaymentDetail" resultMap="BaseResultMap" parameterType="com.xyy.ec.pop.po.PopBillPaymentDetailPo">
    SELECT
    <include refid="Base_Column_List"/>
    FROM tb_xyy_pop_bill_payment_detail
    <where>
      <if test="null != popBillPayDetail.payType">
        AND pay_type = #{popBillPayDetail.payType,jdbcType=TINYINT}
      </if>
      <if test="null != popBillPayDetail.flowNo and '' != popBillPayDetail.flowNo">
        AND flow_no = #{popBillPayDetail.flowNo,jdbcType=VARCHAR}
      </if>
    </where>
    order by create_time desc,id desc
    <!--<if test="pageNum != null and pageSize != null">
      limit #{pageNum, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
    </if>-->
  </select>
  <select id="queryPopBillPaymentDetailCount" resultType="java.lang.Long"  parameterType="java.lang.String">
    select count(id) from tb_xyy_pop_bill_payment_detail
    where flow_no in
    <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
  <select id="queryPopBillPaymentDetailByFlowNoList" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_xyy_pop_bill_payment_detail
    where flow_no in
    <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>