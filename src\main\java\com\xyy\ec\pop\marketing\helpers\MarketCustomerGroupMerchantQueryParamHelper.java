package com.xyy.ec.pop.marketing.helpers;

import com.xyy.ec.marketing.insight.params.MarketCustomerGroupMerchantQueryParam;
import com.xyy.ec.pop.marketing.param.CustomerGroupMerchantQueryParam;

import java.util.Objects;

/**
 * {@link MarketCustomerGroupMerchantQueryParam} 帮助类
 *
 * <AUTHOR>
 */
public class MarketCustomerGroupMerchantQueryParamHelper {

    public static MarketCustomerGroupMerchantQueryParam create(CustomerGroupMerchantQueryParam customerGroupMerchantQueryParam) {
        if (Objects.isNull(customerGroupMerchantQueryParam)) {
            return null;
        }
        MarketCustomerGroupMerchantQueryParam marketCustomerGroupMerchantQueryParam = MarketCustomerGroupMerchantQueryParam.builder()
                .customerGroupId(customerGroupMerchantQueryParam.getCustomerGroupId())
                .merchantIdStr(customerGroupMerchantQueryParam.getMerchantIdStr())
                .realName(customerGroupMerchantQueryParam.getRealName())
                .mobile(customerGroupMerchantQueryParam.getMobile())
                .build();
        return marketCustomerGroupMerchantQueryParam;
    }

}
