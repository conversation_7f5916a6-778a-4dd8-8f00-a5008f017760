package com.xyy.ec.pop.utils;

import com.fasterxml.jackson.core.JsonProcessingException;

import java.io.*;

/**
 * 数组工具类
 * <AUTHOR>
 *
 */
public class ByteUtil {
	
	
	/**
	 * 将对象序列化成byte数组
	 * @param object
	 * @return
	 */
	public static byte[] serialize(Object object) {
		ObjectOutputStream oos = null;
		ByteArrayOutputStream baos = null;
		try {
			// 序列化
			baos = new ByteArrayOutputStream();
			oos = new ObjectOutputStream(baos);
			oos.writeObject(object);
			byte[] bytes = baos.toByteArray();
			return bytes;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 数组反序列化成对象
	 * @param bytes
	 * @return
	 */
	public static Object unserialize(byte[] bytes) {
		ByteArrayInputStream bais = null;
		try {
			// 反序列化
			bais = new ByteArrayInputStream(bytes);
			ObjectInputStream ois = new ObjectInputStream(bais);
			return ois.readObject();
		} catch (Exception e) {

		}
		return null;
	}

	 /**
     * java对象转byte
     * @param object
     * @return
     * @throws JsonProcessingException
     */
    public static byte[] wapperObjToByte(Object object) throws JsonProcessingException {
    	byte[] bytehVal = serialize(object);
        return bytehVal;
    }

    /**
     * json转java对象
     * @param jsonString
     * @param clazz
     * @return
     * @throws IOException
     */
    public static <T> T wapperStringToObj(byte[] objectByte,Class<T> clazz) throws IOException {
        return  (T)unserialize(objectByte);
    }
}
