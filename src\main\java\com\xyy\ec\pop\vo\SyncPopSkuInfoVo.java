package com.xyy.ec.pop.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> liuchao
 * @Date: 2024/10/17 16:45
 * @Description:
 */
@NoArgsConstructor
@Data
public class SyncPopSkuInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标品ID
     */
    private String productId;

    /**
     * 停用状态:0-停用、1-启用、2-删除
     */
    private Integer disableStatus;

    /**
     * 商品状态是否发生变化:0-否、1-是
     */
    private Integer disableStatusChange;

    /**
     * 停用类型：0-默认值；1、公司规定停用；2、药监要求停用；3、数据错误；4、商品退市；5、其它；停用说明(自定义文本)
     */
    private Integer disableType;

    /**
     * 停用说明
     */
    private String disableNote;
}
