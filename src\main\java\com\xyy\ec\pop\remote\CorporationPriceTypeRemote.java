package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationPriceTypeAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationPriceTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.PopCorporationErrorPriceCountDto;
import com.xyy.ec.pop.server.api.merchant.dto.PopCorporationPriceTypeDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CorporationPriceTypeRemote {
    @Reference
    private CorporationPriceTypeAdminApi priceTypeAdminApi;

    public PopCorporationPriceTypeDto queryByOrgId(String orgId) {
        try {
            ApiRPCResult<PopCorporationPriceTypeDto> result = priceTypeAdminApi.queryByOrgId(orgId);
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("CorporationPriceTypeRemote.queryByOrgId#orgId:{} 异常", JSON.toJSONString(orgId), e);
            return null;
        }
    }

    public Byte queryPriceTypeByOrgId(String orgId) {
        try {
            ApiRPCResult<PopCorporationPriceTypeDto> result = priceTypeAdminApi.queryByOrgId(orgId);
            if(result.isSuccess()){
                return result.getData()==null? CorporationPriceTypeEnum.defaultType.getCode():result.getData().getPriceType();
            }
        } catch (Exception e) {
            log.error("CorporationPriceTypeRemote.queryByOrgId#orgId:{} 异常", JSON.toJSONString(orgId), e);
        }
        return null;
    }

    public boolean updateByOrgId(PopCorporationPriceTypeDto type) throws ServiceException {
        try {
            log.info("CorporationPriceTypeRemote.updateByOrgId#type:{}", JSON.toJSONString(type));
            ApiRPCResult<Boolean> result = priceTypeAdminApi.updateByOrgId(type);
            log.info("CorporationPriceTypeRemote.updateByOrgId#type:{} return {}", JSON.toJSONString(type), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            throw new ServiceException(result.getErrMsg());
        } catch (ServiceException e) {
            log.warn("CorporationPriceTypeRemote.updateByOrgId#type:{} 失败:{}", JSON.toJSONString(type), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("CorporationPriceTypeRemote.updateByOrgId#type:{} 异常", JSON.toJSONString(type), e);
            throw new ServiceException("更新失败");
        }
    }

    public PopCorporationErrorPriceCountDto errorPriceCount(String orgId) {
        try {
            log.info("CorporationPriceTypeRemote.errorPriceCount#orgId:{}", JSON.toJSONString(orgId));
            ApiRPCResult<PopCorporationErrorPriceCountDto> result = priceTypeAdminApi.errorPriceCount(orgId);
            log.info("CorporationPriceTypeRemote.errorPriceCount#orgId:{} return {}", JSON.toJSONString(orgId), JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("CorporationPriceTypeRemote.errorPriceCount#orgId:{} 异常", JSON.toJSONString(orgId), e);
        }
        return null;
    }
}
