package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.product.api.admin.ProtectSkuExcelApi;
import com.xyy.ec.pop.server.api.product.dto.ProtectSkuExcelDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @version v1
 * @Description 保护品种
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProtectSkuRemoteAdapter {
    @Reference
    private ProtectSkuExcelApi protectSkuExcelApi;

    public void update(ProtectSkuExcelDto excelDto, List<Long> ids, String username,Long userId) throws ServiceException {
        ApiRPCResult<Boolean> result;
        try{
            log.info("protectSkuExcelApi.update#excelDto:{},ids:{},username:{}",JSON.toJSONString(excelDto),JSON.toJSONString(ids),username);
            result = protectSkuExcelApi.update(excelDto, ids, username,userId);
            log.info("protectSkuExcelApi.update#excelDto:{},ids:{},username:{} return {}",JSON.toJSONString(excelDto),JSON.toJSONString(ids),username, JSON.toJSONString(result));
        }catch (Exception e){
            log.error("protectSkuExcelApi.update#excelDto:{},ids:{},username:{} 异常",JSON.toJSONString(excelDto),JSON.toJSONString(ids),username,e);
            throw new ServiceException("保护品种上传失败");
        }
        if(result.isFail()){
            throw new ServiceException("保护品种上传失败");
        }
    }

    public PageInfo<ProtectSkuExcelDto> page(Integer pageNum,Integer pageSize) throws ServiceException {
        ApiRPCResult<PageInfo<ProtectSkuExcelDto>> result;
        try{
            log.info("protectSkuExcelApi.page#pageNum:{},pageSize:{}",pageNum,pageSize);
            result = protectSkuExcelApi.page(pageNum, pageSize);
            log.info("protectSkuExcelApi.page#pageNum:{},pageSize:{} return {}",pageNum,pageSize,JSON.toJSONString(result));
        }catch (Exception e){
            log.error("protectSkuExcelApi.page#pageNum:{},pageSize:{} 异常",pageNum,pageSize,e);
            throw new ServiceException("查询保护品种失败");
        }
        if(result.isFail()){
            throw new ServiceException("查询保护品种失败");
        }
        return result.getData();
    }
}
