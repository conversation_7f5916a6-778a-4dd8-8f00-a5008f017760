package com.xyy.ec.pop.remote;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.param.EcSkuExamineParamDTO;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.query.EcSkuSellerAdminPageQuery;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.result.EcSkuDetailDto;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.result.SkuExamineLogDTO;

import java.util.List;

public interface EcCsuRpcService {

    /**
     * 根据条件分页查询自营商品信息
     *
     * @param pageQuery
     * @return
     */
    PageInfo<EcSkuDetailDto> adminQuery(EcSkuSellerAdminPageQuery pageQuery);

    /**
     * 自营根据id和店铺code查询商品信息
     * @param id
     * @param shopCode
     * @return
     */
    EcSkuDetailDto getSkuDetailByIdAndShopCode(Long id, String shopCode);

    /**
     * 商品审核
     * @param param
     * @return
     */
    Boolean audit(EcSkuExamineParamDTO param);

    /**
     * 商品审核日志
     * @param skuId
     * @return
     */
    List<SkuExamineLogDTO> getExamineLogsBySkuId(Long skuId);

}
