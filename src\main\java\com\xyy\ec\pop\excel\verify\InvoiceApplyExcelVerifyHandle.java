package com.xyy.ec.pop.excel.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.xyy.ec.pop.vo.AdjustiveBillSettleVo;
import com.xyy.ec.pop.vo.BatchOverOrderVo;
import com.xyy.ec.pop.vo.InvoiceApplyExportVo;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.regex.Pattern;

/**
 * @version v1
 * @Description 调账单导入过滤空行
 * <AUTHOR>
 */
public class InvoiceApplyExcelVerifyHandle implements IExcelVerifyHandler<InvoiceApplyExportVo> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(InvoiceApplyExportVo vo) {
        if (StringUtils.isBlank(vo.getInvoiceApplyNo()) && StringUtils.isBlank(vo.getTrackingNo()) && StringUtils.isBlank(vo.getLogisticsCompanyName())) {
            return new ExcelVerifyHandlerResult(false);
        }
        return new ExcelVerifyHandlerResult(true);
    }

    public static void trim(List<InvoiceApplyExportVo> vos) {
        vos.forEach(vo -> {
            vo.setInvoiceApplyNo(org.apache.commons.lang.StringUtils.trimToNull(vo.getInvoiceApplyNo()));
            vo.setLogisticsCompanyName(org.apache.commons.lang.StringUtils.trimToNull(vo.getLogisticsCompanyName()));
            vo.setTrackingNo(org.apache.commons.lang.StringUtils.trimToNull(vo.getTrackingNo()));
        });
    }

    public static void valid(List<InvoiceApplyExportVo> vos,List<String> effectiveInvoiceApplyNos){
        vos.forEach(vo -> {
            StringBuilder errMsg = new StringBuilder();
            if (org.apache.commons.lang.StringUtils.isBlank(vo.getInvoiceApplyNo())) {
                errMsg.append("发票单号不允许为空,");
            }else{
                if (!effectiveInvoiceApplyNos.contains(vo.getInvoiceApplyNo())){
                    errMsg.append("发票申请单号不存在或者为电子发票,");
                }
            }
            if (org.apache.commons.lang.StringUtils.isBlank(vo.getLogisticsCompanyName())) {
                errMsg.append("物流公司不允许为空,");
            }else {
                if (vo.getLogisticsCompanyName().length() > 10) {
                    errMsg.append("物流公司名称限10个字,");
                }
            }
            if (org.apache.commons.lang.StringUtils.isBlank(vo.getTrackingNo())) {
                errMsg.append("运单号不允许为空,");
            }else{
                boolean isMatch = Pattern.matches("^[A-Za-z0-9-]+$", vo.getTrackingNo());
                if (!isMatch) {
                    errMsg.append("运单号不规范,");
                }

                if (vo.getTrackingNo().length() < 5) {
                    errMsg.append("运单号需大于等于5个字符,");
                }
            }

            if (errMsg.length() > 0) {
                vo.setErrorMsg(errMsg.substring(0, errMsg.length() - 1));
                vo.setFailed(true);
            }
        });
    }

}
