//package com.xyy.ec.pop.vo;
//
//import com.fasterxml.jackson.annotation.JsonIgnore;
//import com.xyy.ec.pop.model.Order;
//import com.xyy.ec.pop.model.OrderDetail;
//import com.xyy.ec.pop.model.OrderExtend;
//
//import java.math.BigDecimal;
//import java.util.Date;
//import java.util.List;
//
//public class OrderEc {
//
//    /** 订单信息 */
//    private Long id;
//
//    /**  */
//    private String branchCode;
//
//    /** 机构ID */
//    private String orgId;
//
//    /** 商户编号 */
//    private Long merchantId;
//
//    /** 订单编号 */
//    private String orderNo;
//
//    /** 品种数量 */
//    private Integer varietyNum;
//
//    /** 收货人 */
//    private String contactor;
//
//    /** 手机号 */
//    private String mobile;
//
//    /** 订单实付金额 */
//    private BigDecimal money;
//
//    /** 支付类型(1:在线支付 2:货到付款 3:线下转账） */
//    private Integer payType;
//
//    /** 支付渠道(1-支付宝,2-微信,3-银联) */
//    private Integer payChannel;
//
//    /** 在线支付时间 */
//    private Date payTime;
//
//    /** 转账时间(争对线下转账) */
//    private Date paymentTime;
//
//    /** 支付有效截止时间 */
//    private Date payExpireTime;
//
//    /** 发票信息 */
//    private String billInfo;
//
//    /** 发票类型 1:普通发票 2:专用发票 */
//    private Integer billType;
//
//    /** 订单状态：1-待配送，2-配送中，3-已配送，4-取消，5-已删除,6-已拆单,7-出库中,10-未支付,90-退款审核中,91-已退款,20-已送达,21-已拒签 */
//    private Integer status;
//
//    /** 审单流程状态：默认：0-尚未进入审单流程,1-审单中，2-开票中，3-发货中，9-审单流程结束 */
//    private Integer checkStatus;
//
//    /** 计划配送时间 */
//    private Date deliveryTime;
//
//    /** 实际配送时间 */
//    private Date shipTime;
//
//    /** 完成时间 */
//    private Date finishTime;
//
//    /** 总金额=实付金额+优惠金额 */
//    private BigDecimal totalAmount;
//
//    /** 优惠金额 */
//    private BigDecimal discount;
//
//    /** 父订单ID */
//    private Long parentId;
//
//    /** 是否可见 */
//    private Integer visibled;
//    public static final Integer STATUS_VISIBLED = 1;
//    public static final Integer STATUS_NOTVISIBLED = 0;
//
//    /** 紧急程度 0(默认:备注) 1:低  5:中  10:高 */
//    private Integer urgencyDegree;
//
//    /** 紧急程度描述 */
//    private String urgencyInfo;
//
//    /** 备注最后更新时间 */
//    private Date urgencyUpdateTime;
//
//    /** 订单原始支付金额(用于退款渠道和对账单作验证使用) */
//    private BigDecimal sourcePayMoney;
//
//    /** 省份编码 */
//    private Integer provinceCode;
//
//    /** 市区编码 */
//    private Integer cityCode;
//
//    /** 区域编码 */
//    private Integer areaCode;
//
//    /** 订单来源 0:手动添加 1:Android 2:IOS 3:H5 4:PC */
//    private Integer orderSource;
//
//    /**  */
//    private Integer totalcount;
//
//    /** 验证重复下单 */
//    private String tranNo;
//
//    /** 订单(签收/拒签)时间 */
//    private Date deliveredTime;
//
//    /** 申请退款次数 */
//    private Integer refundCount;
//
//    /** 销售工号 */
//    private String salesJobNo;
//
//    /** 销售姓名 */
//    private String salesName;
//
//    /** 销售员ID */
//    private Long salesId;
//
//    /** 配送方式: 0:未知 1:自营 2:第三方 3:其他 */
//    private Integer shipType;
//
//    /** 时空编号:SKPZxxxxxx */
//    private String skCode;
//
//    /** 集货区编码 */
//    private String storeCode;
//
//    /** 箱数 */
//    private Integer boxes;
//
//    /** 是否分配 0:未分配 1:已分配 */
//    private Integer haveSchedule;
//
//    /** 过单时间 */
//    private Date passTime;
//
//    /** 地址id */
//    private Long addressId;
//
//    /** 商品总数量 */
//    private Integer productNum;
//
//    /** 药店名称 */
//    private String merchantName;
//
//    /** 不返点商品总金额 */
//    private BigDecimal noRebateTotalAmount;
//
//    /** 不返点商品满减金额 */
//    private BigDecimal noRebateDiscountAmount;
//
//    /** 不返点商品优惠券金额 */
//    private BigDecimal noRebateVoucherAmount;
//
//    /** 返点商品总金额 */
//    private BigDecimal rebateTotalAmount;
//
//    /** 返点商品满减金额 */
//    private BigDecimal rebateDiscountAmount;
//
//    /** 返点商品优惠券金额 */
//    private BigDecimal rebateVoucherAmount;
//
//    /** 全场满减金额 */
//    private BigDecimal wholeDiscountAmount;
//
//    /** 全场优惠券金额 */
//    private BigDecimal wholeVoucherAmount;
//
//    /** 商户类型标识,暂提供连锁药店使用(3) */
//    private Integer businessType;
//
//    /** 收货人地址 */
//    private String address;
//
//    /** 备注 */
//    private String remark;
//
//    /** 创建人 */
//    private String creator;
//
//    /** 创建时间 */
//    private Date createTime;
//
//    /** 修改人 */
//    private String updator;
//
//    /** 修改时间 */
//    private Date updateTime;
//
//    /**发票文案**/
//    private String invoinceText;
//
//    /**是否显示查看发票按钮**/
//    private Integer isShowInvoinceButton;
//
//    /** 订单状态名称 */
//    private String statusName;
//
//    /** 订单状态名称 */
//    private String trackingNo;
//    //运费
//    private BigDecimal freightAmount;
//    //卖家名称
//    private String companyName;
//
//
//
//
//
//    /************************************************** 扩展字段 *************************************************/
//
//    /** 是否可拆单还原:0 = 不可还原; 1 = 可还原 */
//    private Integer separateFlag;
//
//    /** 创建时间始 */
//    private Date startCreateTime;
//
//    /** 创建时间止 */
//    private Date endCreateTime;
//
//    /** 支付开始时间 */
//    private Date startPayTime;
//
//    /** 支付结束时间 */
//    private Date endPayTime;
//
//    /** 详细地址 */
//    private String gspAddress;
//
//    /** 地址状态: 0:默认无修改 1:用户已点击过弹窗 2:用户有修改过备注地址 3:用户备注地址已审过 4:用户备注地址没有审核通过,需要复审 */
//    private Integer gspAuditState;
//
//    /** 会员备注 */
//    private String gspRemark;
//
//    /** 订单扩展信息 */
//    private OrderExtend orderExtend;
//
//    /** 拆分订单明细 */
//    private List<Order> separateOrderList;
//
//    /** 订单明细 */
//    private List<OrderDetail> detailList;
//
//    /** 活动返余额 */
//    private BigDecimal rebateBalanceAmt;
//
//    /**订单来源数组*/
//    private Integer[] orderSourceArray;
//
//    /** 支付方式名称 */
//    private String payTypeName;
//
//    /** 支付渠道名称 */
//    private String payChannelName;
//
//    //是否使用余额 默认为true
//    private boolean useBalance = true;
//
//    //是否有领取余额(0:未领取 1：已领取 2：不能领 )
//    private Integer balanceStatus;
//
//
////    private List<ActivityPackage> packageList; //套餐列表
//
//    /** 订单支付倒计时 */
//    private String payEndTime;
//
//    /** IOS使用的倒计时支付毫秒数 */
//    private Long countDownNewTime;
//
//    /** 倒计时相应的时间戳 毫秒数*/
//    private Long countDownTime;
//
//    //领取余额文案
//    private String balanceText;
//
//    //使用的余额金额
//    private BigDecimal balanceAmount;
//
//    private String balanceRemark;		//余额备注  --已领取余额100.0元/订单完成后预计可领取余额300.00元
//
//    //申请退款文案
//    private String refundText;
//
//    /** 可以确认收货按钮 */
//    private Integer canConfirmReceipt;
//
//    private BigDecimal fullDivPrice;		//订单满减金额
//    private BigDecimal voucherDivPrice;		//订单优惠券减免金额
//    private BigDecimal rebate;				//订单返点金额
//
//    /** 订单起送价格 */
//    private BigDecimal startingPrice;
//    /** 使用的优惠券 */
//    private String voucherId;
//    /** 选中的多个优惠券ID，用逗号分隔 */
//    private String voucherIds;
//    //参与优惠计算明细总金额
//    private BigDecimal takeDiscountTotalAmt;
//    /** app版本 */
//    @JsonIgnore
//    private Integer appVersion;
//
//    private Integer[] statuses;
//    //退款审核状态数组
//    private Integer[] auditStateArray;
//    //根据药品名查询
//    private String name;
//    //是否首字母查询
//    @JsonIgnore
//    private Boolean isLettersQuery;
//
//    // 区分查询订单详情列表时候的状态
//    private Integer sceneType;
//
//
//    private String imageUrl;
//    /** 优惠券优惠总金额 */
//    private BigDecimal voucherDiscountAmount;
//    /** 促销活动优惠总金额 */
//    private BigDecimal promoDiscountAmount;
//
//    /**为了不对原订单编号造成污染，所以先开一个字段*/
//    //预生成订单号
//    @JsonIgnore
//    private String preOrderNo;
//
//    //优惠类型
//    private Integer discountType;
//    //跳转类型 1 只有自营跳支付 2 包含第三方订单跳订单列表
//    @JsonIgnore
//    private Integer jumpType;
//    /** 是否第三方厂家（0：否；1：是） */
//    private Integer isThirdCompany;
//
//    /** 有无退款文本 */
//    private String hasRefundText;
//    /** 查询条件 0 -无退款 1 - 有退款 */
//    private Integer hasRefund;
//
//    /**商户类型（业务类型）：1-个体药店，2-连锁药店（加盟），3-连锁药店（直营），4-诊所 */
//    private Integer businessTypeSearch;
//
//    /** 注册手机号 */
//    private String loginMobile;
//    //评价状态 1：未评价，2：已经评价，3：评价时间已经过期
//    private  Integer appraiseStatus;
//    //判断是否评价了
//    private  Integer appraiseFlag;
//
//    public Integer getAppraiseFlag() {
//        return appraiseFlag;
//    }
//
//    public void setAppraiseFlag(Integer appraiseFlag) {
//        this.appraiseFlag = appraiseFlag;
//    }
//
//    public Integer getDiscountType() {
//        return discountType;
//    }
//
//    public void setDiscountType(Integer discountType) {
//        this.discountType = discountType;
//    }
//
//    public String getName() {
//        return name;
//    }
//    public void setName(String name) {
//        this.name = name;
//    }
//    public Boolean getIsLettersQuery() {
//        return isLettersQuery;
//    }
//
//    public void setIsLettersQuery(Boolean isLettersQuery) {
//        this.isLettersQuery = isLettersQuery;
//    }
//
//    public Integer[] getAuditStateArray() {
//        return auditStateArray;
//    }
//    public void setAuditStateArray(Integer[] auditStateArray) {
//        this.auditStateArray = auditStateArray;
//    }
//
//    public Integer[] getStatuses() {
//        return statuses;
//    }
//
//    public void setStatuses(Integer[] statuses) {
//        this.statuses = statuses;
//    }
//
//    public Integer getBalanceStatus() {
//        return balanceStatus;
//    }
//
//    public void setBalanceStatus(Integer balanceStatus) {
//        this.balanceStatus = balanceStatus;
//    }
//
//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }
//
//    public String getBranchCode() {
//        return branchCode;
//    }
//
//    public void setBranchCode(String branchCode) {
//        this.branchCode = branchCode;
//    }
//
//    public String getOrgId() {
//        return orgId;
//    }
//
//    public void setOrgId(String orgId) {
//        this.orgId = orgId;
//    }
//
//    public Long getMerchantId() {
//        return merchantId;
//    }
//
//    public void setMerchantId(Long merchantId) {
//        this.merchantId = merchantId;
//    }
//
//    public String getOrderNo() {
//        return orderNo;
//    }
//
//    public void setOrderNo(String orderNo) {
//        this.orderNo = orderNo;
//    }
//
//    public Integer getVarietyNum() {
//        return varietyNum;
//    }
//
//    public void setVarietyNum(Integer varietyNum) {
//        this.varietyNum = varietyNum;
//    }
//
//    public String getContactor() {
//        return contactor;
//    }
//
//    public void setContactor(String contactor) {
//        this.contactor = contactor;
//    }
//
//    public String getMobile() {
//        return mobile;
//    }
//
//    public void setMobile(String mobile) {
//        this.mobile = mobile;
//    }
//
//    public BigDecimal getMoney() {
//        return money;
//    }
//
//    public void setMoney(BigDecimal money) {
//        this.money = money;
//    }
//
//    public Integer getPayType() {
//        return payType;
//    }
//
//    public void setPayType(Integer payType) {
//        this.payType = payType;
//    }
//
//    public Integer getPayChannel() {
//        return payChannel;
//    }
//
//    public void setPayChannel(Integer payChannel) {
//        this.payChannel = payChannel;
//    }
//
//    public Date getPayTime() {
//        return payTime;
//    }
//
//    public void setPayTime(Date payTime) {
//        this.payTime = payTime;
//    }
//
//    public Date getPaymentTime() {
//        return paymentTime;
//    }
//
//    public void setPaymentTime(Date paymentTime) {
//        this.paymentTime = paymentTime;
//    }
//
//    public Date getPayExpireTime() {
//        return payExpireTime;
//    }
//
//    public void setPayExpireTime(Date payExpireTime) {
//        this.payExpireTime = payExpireTime;
//    }
//
//    public String getBillInfo() {
//        return billInfo;
//    }
//
//    public void setBillInfo(String billInfo) {
//        this.billInfo = billInfo;
//    }
//
//    public Integer getBillType() {
//        return billType;
//    }
//
//    public void setBillType(Integer billType) {
//        this.billType = billType;
//    }
//
//    public Integer getStatus() {
//        return status;
//    }
//
//    public void setStatus(Integer status) {
//        this.status = status;
//    }
//
//    public Integer getCheckStatus() {
//        return checkStatus;
//    }
//
//    public void setCheckStatus(Integer checkStatus) {
//        this.checkStatus = checkStatus;
//    }
//
//    public Date getDeliveryTime() {
//        return deliveryTime;
//    }
//
//    public void setDeliveryTime(Date deliveryTime) {
//        this.deliveryTime = deliveryTime;
//    }
//
//    public Date getShipTime() {
//        return shipTime;
//    }
//
//    public void setShipTime(Date shipTime) {
//        this.shipTime = shipTime;
//    }
//
//    public Date getFinishTime() {
//        return finishTime;
//    }
//
//    public void setFinishTime(Date finishTime) {
//        this.finishTime = finishTime;
//    }
//
//    public BigDecimal getTotalAmount() {
//        return totalAmount;
//    }
//
//    public void setTotalAmount(BigDecimal totalAmount) {
//        this.totalAmount = totalAmount;
//    }
//
//    public BigDecimal getDiscount() {
//        return discount;
//    }
//
//    public void setDiscount(BigDecimal discount) {
//        this.discount = discount;
//    }
//
//    public Long getParentId() {
//        return parentId;
//    }
//
//    public void setParentId(Long parentId) {
//        this.parentId = parentId;
//    }
//
//    public Integer getVisibled() {
//        return visibled;
//    }
//
//    public void setVisibled(Integer visibled) {
//        this.visibled = visibled;
//    }
//
//    public Integer getUrgencyDegree() {
//        return urgencyDegree;
//    }
//
//    public void setUrgencyDegree(Integer urgencyDegree) {
//        this.urgencyDegree = urgencyDegree;
//    }
//
//    public String getUrgencyInfo() {
//        return urgencyInfo;
//    }
//
//    public void setUrgencyInfo(String urgencyInfo) {
//        this.urgencyInfo = urgencyInfo;
//    }
//
//    public Date getUrgencyUpdateTime() {
//        return urgencyUpdateTime;
//    }
//
//    public void setUrgencyUpdateTime(Date urgencyUpdateTime) {
//        this.urgencyUpdateTime = urgencyUpdateTime;
//    }
//
//    public BigDecimal getSourcePayMoney() {
//        return sourcePayMoney;
//    }
//
//    public void setSourcePayMoney(BigDecimal sourcePayMoney) {
//        this.sourcePayMoney = sourcePayMoney;
//    }
//
//    public Integer getProvinceCode() {
//        return provinceCode;
//    }
//
//    public void setProvinceCode(Integer provinceCode) {
//        this.provinceCode = provinceCode;
//    }
//
//    public Integer getCityCode() {
//        return cityCode;
//    }
//
//    public void setCityCode(Integer cityCode) {
//        this.cityCode = cityCode;
//    }
//
//    public Integer getAreaCode() {
//        return areaCode;
//    }
//
//    public void setAreaCode(Integer areaCode) {
//        this.areaCode = areaCode;
//    }
//
//    public Integer getOrderSource() {
//        return orderSource;
//    }
//
//    public void setOrderSource(Integer orderSource) {
//        this.orderSource = orderSource;
//    }
//
//    public Integer getTotalcount() {
//        return totalcount;
//    }
//
//    public void setTotalcount(Integer totalcount) {
//        this.totalcount = totalcount;
//    }
//
//    public String getTranNo() {
//        return tranNo;
//    }
//
//    public void setTranNo(String tranNo) {
//        this.tranNo = tranNo;
//    }
//
//    public Date getDeliveredTime() {
//        return deliveredTime;
//    }
//
//    public void setDeliveredTime(Date deliveredTime) {
//        this.deliveredTime = deliveredTime;
//    }
//
//    public Integer getRefundCount() {
//        return refundCount;
//    }
//
//    public void setRefundCount(Integer refundCount) {
//        this.refundCount = refundCount;
//    }
//
//    public String getSalesJobNo() {
//        return salesJobNo;
//    }
//
//    public void setSalesJobNo(String salesJobNo) {
//        this.salesJobNo = salesJobNo;
//    }
//
//    public String getSalesName() {
//        return salesName;
//    }
//
//    public void setSalesName(String salesName) {
//        this.salesName = salesName;
//    }
//
//    public Long getSalesId() {
//        return salesId;
//    }
//
//    public void setSalesId(Long salesId) {
//        this.salesId = salesId;
//    }
//
//    public Integer getShipType() {
//        return shipType;
//    }
//
//    public void setShipType(Integer shipType) {
//        this.shipType = shipType;
//    }
//
//    public String getSkCode() {
//        return skCode;
//    }
//
//    public void setSkCode(String skCode) {
//        this.skCode = skCode;
//    }
//
//    public String getStoreCode() {
//        return storeCode;
//    }
//
//    public void setStoreCode(String storeCode) {
//        this.storeCode = storeCode;
//    }
//
//    public Integer getBoxes() {
//        return boxes;
//    }
//
//    public void setBoxes(Integer boxes) {
//        this.boxes = boxes;
//    }
//
//    public Integer getHaveSchedule() {
//        return haveSchedule;
//    }
//
//    public void setHaveSchedule(Integer haveSchedule) {
//        this.haveSchedule = haveSchedule;
//    }
//
//    public Date getPassTime() {
//        return passTime;
//    }
//
//    public void setPassTime(Date passTime) {
//        this.passTime = passTime;
//    }
//
//    public Long getAddressId() {
//        return addressId;
//    }
//
//    public void setAddressId(Long addressId) {
//        this.addressId = addressId;
//    }
//
//    public Integer getProductNum() {
//        return productNum;
//    }
//
//    public void setProductNum(Integer productNum) {
//        this.productNum = productNum;
//    }
//
//    public String getMerchantName() {
//        return merchantName;
//    }
//
//    public void setMerchantName(String merchantName) {
//        this.merchantName = merchantName;
//    }
//
//    public BigDecimal getNoRebateTotalAmount() {
//        return noRebateTotalAmount;
//    }
//
//    public void setNoRebateTotalAmount(BigDecimal noRebateTotalAmount) {
//        this.noRebateTotalAmount = noRebateTotalAmount;
//    }
//
//    public BigDecimal getNoRebateDiscountAmount() {
//        return noRebateDiscountAmount;
//    }
//
//    public void setNoRebateDiscountAmount(BigDecimal noRebateDiscountAmount) {
//        this.noRebateDiscountAmount = noRebateDiscountAmount;
//    }
//
//    public BigDecimal getNoRebateVoucherAmount() {
//        return noRebateVoucherAmount;
//    }
//
//    public void setNoRebateVoucherAmount(BigDecimal noRebateVoucherAmount) {
//        this.noRebateVoucherAmount = noRebateVoucherAmount;
//    }
//
//    public BigDecimal getRebateTotalAmount() {
//        return rebateTotalAmount;
//    }
//
//    public void setRebateTotalAmount(BigDecimal rebateTotalAmount) {
//        this.rebateTotalAmount = rebateTotalAmount;
//    }
//
//    public BigDecimal getRebateDiscountAmount() {
//        return rebateDiscountAmount;
//    }
//
//    public void setRebateDiscountAmount(BigDecimal rebateDiscountAmount) {
//        this.rebateDiscountAmount = rebateDiscountAmount;
//    }
//
//    public BigDecimal getRebateVoucherAmount() {
//        return rebateVoucherAmount;
//    }
//
//    public void setRebateVoucherAmount(BigDecimal rebateVoucherAmount) {
//        this.rebateVoucherAmount = rebateVoucherAmount;
//    }
//
//    public BigDecimal getWholeDiscountAmount() {
//        return wholeDiscountAmount;
//    }
//
//    public void setWholeDiscountAmount(BigDecimal wholeDiscountAmount) {
//        this.wholeDiscountAmount = wholeDiscountAmount;
//    }
//
//    public BigDecimal getWholeVoucherAmount() {
//        return wholeVoucherAmount;
//    }
//
//    public void setWholeVoucherAmount(BigDecimal wholeVoucherAmount) {
//        this.wholeVoucherAmount = wholeVoucherAmount;
//    }
//
//    public Integer getBusinessType() {
//        return businessType;
//    }
//
//    public void setBusinessType(Integer businessType) {
//        this.businessType = businessType;
//    }
//
//    public String getAddress() {
//        return address;
//    }
//
//    public void setAddress(String address) {
//        this.address = address;
//    }
//
//    public String getRemark() {
//        return remark;
//    }
//
//    public void setRemark(String remark) {
//        this.remark = remark;
//    }
//
//    public String getCreator() {
//        return creator;
//    }
//
//    public void setCreator(String creator) {
//        this.creator = creator;
//    }
//
//    public Date getCreateTime() {
//        return createTime;
//    }
//
//    public void setCreateTime(Date createTime) {
//        this.createTime = createTime;
//    }
//
//    public String getUpdator() {
//        return updator;
//    }
//
//    public void setUpdator(String updator) {
//        this.updator = updator;
//    }
//
//    public Date getUpdateTime() {
//        return updateTime;
//    }
//
//    public void setUpdateTime(Date updateTime) {
//        this.updateTime = updateTime;
//    }
//
//    public String getInvoinceText() {
//        return invoinceText;
//    }
//
//    public void setInvoinceText(String invoinceText) {
//        this.invoinceText = invoinceText;
//    }
//
//    public Integer getIsShowInvoinceButton() {
//        return isShowInvoinceButton;
//    }
//
//    public void setIsShowInvoinceButton(Integer isShowInvoinceButton) {
//        this.isShowInvoinceButton = isShowInvoinceButton;
//    }
//
//    public Integer getSeparateFlag() {
//        return separateFlag;
//    }
//
//    public void setSeparateFlag(Integer separateFlag) {
//        this.separateFlag = separateFlag;
//    }
//
//    public Date getStartCreateTime() {
//        return startCreateTime;
//    }
//
//    public void setStartCreateTime(Date startCreateTime) {
//        this.startCreateTime = startCreateTime;
//    }
//
//    public Date getEndCreateTime() {
//        return endCreateTime;
//    }
//
//    public void setEndCreateTime(Date endCreateTime) {
//        this.endCreateTime = endCreateTime;
//    }
//
//    public Date getStartPayTime() {
//        return startPayTime;
//    }
//
//    public void setStartPayTime(Date startPayTime) {
//        this.startPayTime = startPayTime;
//    }
//
//    public Date getEndPayTime() {
//        return endPayTime;
//    }
//
//    public void setEndPayTime(Date endPayTime) {
//        this.endPayTime = endPayTime;
//    }
//
//    public String getGspAddress() {
//        return gspAddress;
//    }
//
//    public void setGspAddress(String gspAddress) {
//        this.gspAddress = gspAddress;
//    }
//
//    public Integer getGspAuditState() {
//        return gspAuditState;
//    }
//
//    public void setGspAuditState(Integer gspAuditState) {
//        this.gspAuditState = gspAuditState;
//    }
//
//    public String getGspRemark() {
//        return gspRemark;
//    }
//
//    public void setGspRemark(String gspRemark) {
//        this.gspRemark = gspRemark;
//    }
//
//    public OrderExtend getOrderExtend() {
//        return orderExtend;
//    }
//
//    public void setOrderExtend(OrderExtend orderExtend) {
//        this.orderExtend = orderExtend;
//    }
//
//    public List<Order> getSeparateOrderList() {
//        return separateOrderList;
//    }
//
//    public void setSeparateOrderList(List<Order> separateOrderList) {
//        this.separateOrderList = separateOrderList;
//    }
//
//    public List<OrderDetail> getDetailList() {
//        return detailList;
//    }
//
//    public void setDetailList(List<OrderDetail> detailList) {
//        this.detailList = detailList;
//    }
//
//    public BigDecimal getRebateBalanceAmt() {
//        return rebateBalanceAmt;
//    }
//
//    public void setRebateBalanceAmt(BigDecimal rebateBalanceAmt) {
//        this.rebateBalanceAmt = rebateBalanceAmt;
//    }
//
//    public Integer[] getOrderSourceArray() {
//        return orderSourceArray;
//    }
//
//    public void setOrderSourceArray(Integer[] orderSourceArray) {
//        this.orderSourceArray = orderSourceArray;
//    }
//
//    public String getStatusName() {
//        return statusName;
//    }
//
//    public void setStatusName(String statusName) {
//        this.statusName = statusName;
//    }
//
//    public String getPayTypeName() {
//        return payTypeName;
//    }
//
//    public void setPayTypeName(String payTypeName) {
//        this.payTypeName = payTypeName;
//    }
//
//    public String getPayChannelName() {
//        return payChannelName;
//    }
//
//    public void setPayChannelName(String payChannelName) {
//        this.payChannelName = payChannelName;
//    }
//
//    public boolean getUseBalance() {
//        return useBalance;
//    }
//
//    public void setUseBalance(boolean useBalance) {
//        this.useBalance = useBalance;
//    }
//
//    public String getPayEndTime() {
//        return payEndTime;
//    }
//
//    public void setPayEndTime(String payEndTime) {
//        this.payEndTime = payEndTime;
//    }
//
//    public Long getCountDownNewTime() {
//        return countDownNewTime;
//    }
//
//    public void setCountDownNewTime(Long countDownNewTime) {
//        this.countDownNewTime = countDownNewTime;
//    }
//
//    public Long getCountDownTime() {
//        return countDownTime;
//    }
//
//    public void setCountDownTime(Long countDownTime) {
//        this.countDownTime = countDownTime;
//    }
//
//    public String getBalanceText() {
//        return balanceText;
//    }
//
//    public void setBalanceText(String balanceText) {
//        this.balanceText = balanceText;
//    }
//
//    public BigDecimal getBalanceAmount() {
//        return balanceAmount;
//    }
//
//    public void setBalanceAmount(BigDecimal balanceAmount) {
//        this.balanceAmount = balanceAmount;
//    }
//
//    public String getBalanceRemark() {
//        return balanceRemark;
//    }
//
//    public void setBalanceRemark(String balanceRemark) {
//        this.balanceRemark = balanceRemark;
//    }
//
//    public String getRefundText() {
//        return refundText;
//    }
//
//    public void setRefundText(String refundText) {
//        this.refundText = refundText;
//    }
//
//    public Integer getCanConfirmReceipt() {
//        return canConfirmReceipt;
//    }
//
//    public void setCanConfirmReceipt(Integer canConfirmReceipt) {
//        this.canConfirmReceipt = canConfirmReceipt;
//    }
//
//    public BigDecimal getFullDivPrice() {
//        return fullDivPrice;
//    }
//
//    public void setFullDivPrice(BigDecimal fullDivPrice) {
//        this.fullDivPrice = fullDivPrice;
//    }
//
//    public BigDecimal getVoucherDivPrice() {
//        return voucherDivPrice;
//    }
//
//    public void setVoucherDivPrice(BigDecimal voucherDivPrice) {
//        this.voucherDivPrice = voucherDivPrice;
//    }
//
//    public BigDecimal getRebate() {
//        return rebate;
//    }
//
//    public void setRebate(BigDecimal rebate) {
//        this.rebate = rebate;
//    }
//
//    public BigDecimal getStartingPrice() {
//        return startingPrice;
//    }
//
//    public void setStartingPrice(BigDecimal startingPrice) {
//        this.startingPrice = startingPrice;
//    }
//
//    public String getVoucherId() {
//        return voucherId;
//    }
//
//    public void setVoucherId(String voucherId) {
//        this.voucherId = voucherId;
//    }
//
//    public String getVoucherIds() {
//        return voucherIds;
//    }
//
//    public void setVoucherIds(String voucherIds) {
//        this.voucherIds = voucherIds;
//    }
//
//    public Integer getAppVersion() {
//        return appVersion;
//    }
//
//    public void setAppVersion(Integer appVersion) {
//        this.appVersion = appVersion;
//    }
//
//    public BigDecimal getTakeDiscountTotalAmt() {
//        return takeDiscountTotalAmt;
//    }
//
//    public void setTakeDiscountTotalAmt(BigDecimal takeDiscountTotalAmt) {
//        this.takeDiscountTotalAmt = takeDiscountTotalAmt;
//    }
//    public Integer getSceneType() {
//        return sceneType;
//    }
//    public void setSceneType(Integer sceneType) {
//        this.sceneType = sceneType;
//    }
//
//    public String getImageUrl() {
//        return imageUrl;
//    }
//    public void setImageUrl(String imageUrl) {
//        this.imageUrl = imageUrl;
//    }
//
//    public BigDecimal getVoucherDiscountAmount() {
//        return voucherDiscountAmount;
//    }
//
//    public void setVoucherDiscountAmount(BigDecimal voucherDiscountAmount) {
//        this.voucherDiscountAmount = voucherDiscountAmount;
//    }
//
//    public BigDecimal getPromoDiscountAmount() {
//        return promoDiscountAmount;
//    }
//
//    public void setPromoDiscountAmount(BigDecimal promoDiscountAmount) {
//        this.promoDiscountAmount = promoDiscountAmount;
//    }
//
//
//    public String getPreOrderNo() {
//        return preOrderNo;
//    }
//    public void setPreOrderNo(String preOrderNo) {
//        this.preOrderNo = preOrderNo;
//    }
//
//    public Integer getJumpType() {
//        return jumpType;
//    }
//
//    public void setJumpType(Integer jumpType) {
//        this.jumpType = jumpType;
//    }
//
//    public Integer getIsThirdCompany() {
//        return isThirdCompany;
//    }
//
//    public void setIsThirdCompany(Integer isThirdCompany) {
//        this.isThirdCompany = isThirdCompany;
//    }
//
//    public String getTrackingNo() {
//        return trackingNo;
//    }
//
//    public void setTrackingNo(String trackingNo) {
//        this.trackingNo = trackingNo;
//    }
//
//    public String getHasRefundText() {
//        if (this.refundCount == null) {
//            return null;
//        }
//
//        if (this.refundCount > 0) {
//            return "有退款";
//        } else {
//            return "无退款";
//        }
//    }
//
//    public void setHasRefundText(String hasRefundText) {
//        this.hasRefundText = hasRefundText;
//    }
//
//    public Integer getHasRefund() {
//        return hasRefund;
//    }
//
//    public void setHasRefund(Integer hasRefund) {
//        this.hasRefund = hasRefund;
//    }
//
//    public Integer getBusinessTypeSearch() {
//        return businessTypeSearch;
//    }
//
//    public void setBusinessTypeSearch(Integer businessTypeSearch) {
//        this.businessTypeSearch = businessTypeSearch;
//    }
//
//    public String getLoginMobile() {
//        return loginMobile;
//    }
//
//    public void setLoginMobile(String loginMobile) {
//        this.loginMobile = loginMobile;
//    }
//
//    public Integer getAppraiseStatus() {
//        return appraiseStatus;
//    }
//
//    public void setAppraiseStatus(Integer appraiseStatus) {
//        this.appraiseStatus = appraiseStatus;
//    }
//
//
//    public BigDecimal getFreightAmount() {
//        return freightAmount;
//    }
//
//    public void setFreightAmount(BigDecimal freightAmount) {
//        this.freightAmount = freightAmount;
//    }
//
//    public String getCompanyName() {
//        return companyName;
//    }
//
//    public void setCompanyName(String companyName) {
//        this.companyName = companyName;
//    }
//}
//
