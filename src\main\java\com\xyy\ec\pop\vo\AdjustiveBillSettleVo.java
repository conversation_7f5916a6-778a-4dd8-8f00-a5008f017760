package com.xyy.ec.pop.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * @Description 批量导入调账单信息
 * <AUTHOR>
 * @Date 2022/8/15
 */
@Data
public class AdjustiveBillSettleVo {
    @Excel(name = "*商业编号", width = 20)
    private String orgId;
    @Excel(name = "平台总优惠", width = 20)
    private String platformTotalDiscount;
    @Excel(name = "佣金金额", width = 20)
    private String hireMoney;
    @Excel(name = "*应结算金额", width = 20)
    private String statementTotalMoney;
    @Excel(name = "*应缴纳佣金", width = 20)
    private String deductedCommission;
    @Excel(name = "备注", width = 20)
    private String remark;
    @Excel(name = "错误原因", width = 15)
    protected String errorMsg;
    /**
     * 表示该条记录是否错误
     */
    private boolean failed;
}
