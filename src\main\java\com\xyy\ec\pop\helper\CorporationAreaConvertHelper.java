package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.server.api.merchant.dto.CorporationAreaDto;
import com.xyy.ec.pop.vo.CorporationAreaInfoVo;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 区域
 * <AUTHOR>
 */
public class CorporationAreaConvertHelper {
    public static List<CorporationAreaInfoVo> convertToVo(List<CorporationAreaDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return new ArrayList<>(0);
        }
        return dtos.stream().map(item -> convertToVo(item)).collect(Collectors.toList());
    }

    private static CorporationAreaInfoVo convertToVo(CorporationAreaDto dto) {
        CorporationAreaInfoVo vo = new CorporationAreaInfoVo();
        vo.setId(dto.getId());
        vo.setCId(dto.getCId());
        vo.setProvId(dto.getProvId());
        vo.setProv(dto.getProv());
        vo.setSaleType(dto.getSaleType());

        return vo;
    }

    public static List<CorporationAreaDto> convertToDto(List<CorporationAreaInfoVo> vos, Map<Integer, String> map, byte code) {
        if (CollectionUtils.isEmpty(vos)) {
            return new ArrayList<>(0);
        }
        return vos.stream().map(item -> convertToDto(item, map,code)).collect(Collectors.toList());
    }

    public static CorporationAreaDto convertToDto(CorporationAreaInfoVo vo, Map<Integer, String> map, byte code) {
        CorporationAreaDto dto = new CorporationAreaDto();
        dto.setCId(vo.getCId());
        dto.setProvId(vo.getProvId());
        dto.setProv(map.get(vo.getProvId().intValue()));
        dto.setSaleType(code);

        return dto;
    }
}
