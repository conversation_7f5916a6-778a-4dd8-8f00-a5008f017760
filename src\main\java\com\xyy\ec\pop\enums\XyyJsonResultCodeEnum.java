package com.xyy.ec.pop.enums;

import lombok.Getter;

/**
 * 信息错误码枚举
 *
 * <AUTHOR>
 */
@Getter
public enum XyyJsonResultCodeEnum {
    SUCCESS(1000, "成功"),
    FAIL(true, 9999, "失败"),
    //201 ~300 区间通用异常
    NETWORK_ERROR(201, "网络异常请稍后再试"),
    PARAMETER_ERROR(202, "{0}入参错误"),
    AUTHORITY_ERROR(203, "鉴权异常"),
    DATA_EMPTY(204, "未查到{0}数据"),
    DATA_REPEAT(205, "重复提交"),
    FTP_CONNECT_FAILURE(true, 210, "建立FTP连接失败"),
    FTP_MAKE_DIRECTORY_FAILURE(true, 211, "FTP创建目录失败"),
    FTP_UPLOAD_FILE_FAILURE(true, 212, "FTP上传文件失败"),
    EXPORT_FREQUENCY_LIMIT(213, "导出频次受限"),

    //301~ 9998 业务异常
    /**
     * 店铺服务远程调用失败
     */
    SHOP_REMOTE_ERROR(true, 301, "店铺服务远程调用失败"),

    ;

    XyyJsonResultCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    XyyJsonResultCodeEnum(boolean warn, int code, String msg) {
        this.warn = warn;
        this.code = code;
        this.msg = msg;
    }

    /**
     * 是否告警
     */
    private boolean warn;
    /**
     * 应用名
     */
    private String appName = "MarketingAdmin";
    /**
     * 业务错误码
     */
    private int code;
    /**
     * 描述
     */
    private String msg;
}
