package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> lizhiwei 
 * @description: 商品首营资质
 * create at:  2020/12/15  17:46
 * */
@Data
public class PopSkuQualificationVo implements Serializable {
    private Long id;

    /**
     * 商品编号
     */
    private String barcode;

    /**
     * 资质证书名称
     */
    private String name;

    /**
     * 资质证书号
     */
    private String code;

    /**
     * 资质起始日期
     */
    private Date startDate;

    /**
     * 资质终止日期
     */
    private Date endDate;

    /**
     * 证书文件URL
     */
    private String url;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 状态（1-待审核；2-审核不通过；3-审核通过；4-已过期；）
     */
    private Byte state;

    /**
     * 逻辑删除：0-删除
     */
    private Byte del;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改人姓名
     */
    private String updateName;

    /**
     * 资质明细Id
     */
    private Long qualificationsDetailId;

    private static final long serialVersionUID = 1L;
}
