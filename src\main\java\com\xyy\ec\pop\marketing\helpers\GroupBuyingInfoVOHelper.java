package com.xyy.ec.pop.marketing.helpers;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.marketing.elephant.dto.actReport.FrameActReportSaleScopeDTO;
import com.xyy.ec.marketing.elephant.dto.obs.MarketingActivityLevelDto;
import com.xyy.ec.marketing.elephant.dto.obs.MarketingActivityLevelExtendDto;
import com.xyy.ec.marketing.elephant.results.MarketingGroupBuyingInfoDTO;
import com.xyy.ec.marketing.elephant.results.MarketingGroupBuyingInitiatorInfoDTO;
import com.xyy.ec.pop.marketing.constants.MarketingConstants;
import com.xyy.ec.pop.marketing.dto.FrameActReportRemarkDTO;
import com.xyy.ec.pop.marketing.vo.GroupBuyingInfoVO;
import com.xyy.ec.pop.marketing.vo.GroupBuyingSaleScopeVO;
import com.xyy.ec.pop.marketing.vo.MarketingActivityLevelVo;
import com.xyy.ec.pop.model.KeyValueDTO;
import com.xyy.ms.promotion.business.common.config.IsTrueEnum;
import com.xyy.ms.promotion.business.common.constants.ActivityReportEnum;
import com.xyy.ms.promotion.business.enums.MarketingActivityCreateSourceEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public class GroupBuyingInfoVOHelper {

    public static List<GroupBuyingInfoVO> creates(List<MarketingGroupBuyingInfoDTO> marketingGroupBuyingInfoDTOS) {
        if (CollectionUtils.isEmpty(marketingGroupBuyingInfoDTOS)) {
            return Lists.newArrayList();
        }
        return marketingGroupBuyingInfoDTOS.stream().map(GroupBuyingInfoVOHelper::create)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static GroupBuyingInfoVO create(MarketingGroupBuyingInfoDTO marketingGroupBuyingInfoDTO) {
        if (Objects.isNull(marketingGroupBuyingInfoDTO)) {
            return null;
        }
        GroupBuyingInfoVO groupBuyingInfoVO = GroupBuyingInfoVO.builder()
                .actId(marketingGroupBuyingInfoDTO.getActId())
                .reportId(marketingGroupBuyingInfoDTO.getReportId())
                .status(marketingGroupBuyingInfoDTO.getStatus())
                .orgId(marketingGroupBuyingInfoDTO.getOrgId())
                .shopCode(marketingGroupBuyingInfoDTO.getShopCode())
                .csuId(marketingGroupBuyingInfoDTO.getCsuId())
                .barcode(marketingGroupBuyingInfoDTO.getBarcode())
                .productName(marketingGroupBuyingInfoDTO.getProductName())
                .fob(marketingGroupBuyingInfoDTO.getFob())
                .ecAccessGroupPrice(marketingGroupBuyingInfoDTO.getEcAccessGroupPrice())
                .popAccessGroupPrice(marketingGroupBuyingInfoDTO.getPopAccessGroupPrice())
                .groupPrice(marketingGroupBuyingInfoDTO.getGroupPrice())
                .groupNum(marketingGroupBuyingInfoDTO.getGroupNum())
                .isCopyCsuModel(marketingGroupBuyingInfoDTO.getIsCopyCsuModel())
                .isCopySaleArea(marketingGroupBuyingInfoDTO.getIsCopySaleArea())
                .customerGroupId(marketingGroupBuyingInfoDTO.getCustomerGroupId())
                .stockSyncErp(marketingGroupBuyingInfoDTO.getStockSyncErp())
                .onTheWayStock(marketingGroupBuyingInfoDTO.getOnTheWayStock())
                .theme(marketingGroupBuyingInfoDTO.getTheme())
                .topicList(marketingGroupBuyingInfoDTO.getTopicList())
                .topics(marketingGroupBuyingInfoDTO.getTopics())
                .actStartTime(marketingGroupBuyingInfoDTO.getActStartTime())
                .actEndTime(marketingGroupBuyingInfoDTO.getActEndTime())
                .createTime(marketingGroupBuyingInfoDTO.getCreateTime())
                .sourceType(marketingGroupBuyingInfoDTO.getSourceType())
                .stepPriceStatus(marketingGroupBuyingInfoDTO.getStepPriceStatus())
                .isOrder(marketingGroupBuyingInfoDTO.getIsOrder())
                .build();
        Integer status = marketingGroupBuyingInfoDTO.getStatus();
        String statusName = "";
        if (Objects.nonNull(status)) {
            ActivityReportEnum.ActReportIndexStatusEnum statusEnum = ActivityReportEnum.ActReportIndexStatusEnum.getByType(status);
            if (Objects.nonNull(statusEnum)) {
                statusName = statusEnum.getTypeText();
            }
        }
        groupBuyingInfoVO.setStatusName(statusName);
        List<MarketingGroupBuyingInitiatorInfoDTO> initiators = marketingGroupBuyingInfoDTO.getInitiators();
        BigDecimal shopSubsidy = GroupBuyingInfoVOHelper.getInitiatorAmount(initiators, Sets.newHashSet(MarketingConstants.INITIATOR_POP_SHOP));
        groupBuyingInfoVO.setShopSubsidy(shopSubsidy);
        List<MarketingGroupBuyingInitiatorInfoDTO> subsidyInitiators = marketingGroupBuyingInfoDTO.getSubsidyInitiators();
        List<KeyValueDTO> operationSubsidies = GroupBuyingInfoVOHelper.listInitiatorVOS(subsidyInitiators);
        groupBuyingInfoVO.setOperationSubsidies(operationSubsidies);
        String stockSyncErpDesc = "";
        Integer stockSyncErp = marketingGroupBuyingInfoDTO.getStockSyncErp();
        if (Objects.equals(stockSyncErp, 0)) {
            stockSyncErpDesc = "否";
        } else if (Objects.equals(stockSyncErp, 1)) {
            stockSyncErpDesc = "是";
        }
        groupBuyingInfoVO.setStockSyncErpDesc(stockSyncErpDesc);
        Integer totalLimitNum = marketingGroupBuyingInfoDTO.getTotalLimitNum();
        Integer surplusStockNum = marketingGroupBuyingInfoDTO.getSurplusStockNum();
        if (Objects.isNull(totalLimitNum) || totalLimitNum <= 0) {
            // 总限购：不限购
            groupBuyingInfoVO.setTotalLimitNum(null);
            groupBuyingInfoVO.setSurplusStockNum(null);
        } else {
            // 总限购：限购
            groupBuyingInfoVO.setTotalLimitNum(totalLimitNum);
            groupBuyingInfoVO.setSurplusStockNum(Objects.isNull(surplusStockNum) ? totalLimitNum : (surplusStockNum <= 0 ? 0 : surplusStockNum));
        }
        Integer personalLimitNum = marketingGroupBuyingInfoDTO.getPersonalLimitNum();
        if (Objects.isNull(personalLimitNum) || personalLimitNum <= 0) {
            groupBuyingInfoVO.setPersonalLimitNum(null);
        } else {
            groupBuyingInfoVO.setPersonalLimitNum(personalLimitNum);
        }
        Date actStartTime = marketingGroupBuyingInfoDTO.getActStartTime();
        String actStartTimeStr = GroupBuyingInfoVOHelper.getDateFormatStr(actStartTime);
        groupBuyingInfoVO.setActStartTimeStr(actStartTimeStr);
        Date actEndTime = marketingGroupBuyingInfoDTO.getActEndTime();
        String actEndTimeStr = GroupBuyingInfoVOHelper.getDateFormatStr(actEndTime);
        groupBuyingInfoVO.setActEndTimeStr(actEndTimeStr);
        Date createTime = marketingGroupBuyingInfoDTO.getCreateTime();
        String createTimeStr = GroupBuyingInfoVOHelper.getDateFormatStr(createTime);
        groupBuyingInfoVO.setCreateTimeStr(createTimeStr);
        String sourceTypeName = "";
        Integer sourceType = marketingGroupBuyingInfoDTO.getSourceType();
        if (Objects.nonNull(sourceType)) {
            MarketingActivityCreateSourceEnum sourceEnum = MarketingActivityCreateSourceEnum.valueOfCustom(sourceType);
            if (Objects.nonNull(sourceEnum)) {
                sourceTypeName = sourceEnum.getName();
            }
        }
        groupBuyingInfoVO.setSourceTypeName(sourceTypeName);
        //是否阶梯价
        Integer stepPriceStatus = marketingGroupBuyingInfoDTO.getStepPriceStatus();
        if(null != stepPriceStatus){
            IsTrueEnum stepPriceStatusEnum = IsTrueEnum.valueOfCustom(stepPriceStatus);
            if(null != stepPriceStatusEnum){
                groupBuyingInfoVO.setStepPriceStatusDesc(stepPriceStatusEnum.getDesc());
            }
        }
        //添加阶梯价信息
        List<MarketingActivityLevelExtendDto> marketingActivityLevelDtoList = marketingGroupBuyingInfoDTO.getMarketingActivityLevelDtoList();
        if(CollectionUtils.isNotEmpty(marketingActivityLevelDtoList)){
            List<MarketingActivityLevelVo> marketingActivityLevelList = JSONObject.parseArray(JSONObject.toJSONString(marketingActivityLevelDtoList), MarketingActivityLevelVo.class);
            //填充补贴信息
            fillLevelSubsidyInfo(marketingActivityLevelList);
            groupBuyingInfoVO.setMarketingActivityLevelList(marketingActivityLevelList);
        }
        //解析驳回原因
        if (StringUtils.isNotEmpty(marketingGroupBuyingInfoDTO.getRemark()) && Objects.equals(status, ActivityReportEnum.ActReportIndexStatusEnum.REJECT.getType())) {
            String rejectReason;
            try {
                FrameActReportRemarkDTO frameActReportRemarkDTO = JSONObject.parseObject(marketingGroupBuyingInfoDTO.getRemark(), FrameActReportRemarkDTO.class);
                rejectReason = frameActReportRemarkDTO.getRejectReason();
            } catch (Exception e) {
                rejectReason = "";
            }
            groupBuyingInfoVO.setRemark(rejectReason);
        }
        //添加销售区域信息
        FrameActReportSaleScopeDTO saleScopeDTO = marketingGroupBuyingInfoDTO.getSaleScopeDTO();
        if (saleScopeDTO != null) {
            GroupBuyingSaleScopeVO groupBuyingSaleScopeVO = buildGroupBuyingSaleScopeVO(saleScopeDTO);
            groupBuyingInfoVO.setGroupBuyingSaleScopeVO(groupBuyingSaleScopeVO);
        }
        return groupBuyingInfoVO;
    }

    private static void fillLevelSubsidyInfo(List<MarketingActivityLevelVo> marketingActivityLevelList){
        if(CollectionUtils.isEmpty(marketingActivityLevelList)){
            return;
        }
        for(MarketingActivityLevelVo levelVo : marketingActivityLevelList){

            List<MarketingGroupBuyingInitiatorInfoDTO> initiators = levelVo.getInitiators();
            BigDecimal shopSubsidy = GroupBuyingInfoVOHelper.getInitiatorAmount(initiators, Sets.newHashSet(MarketingConstants.INITIATOR_POP_SHOP));
            levelVo.setShopSubsidy(shopSubsidy);
            List<MarketingGroupBuyingInitiatorInfoDTO> subsidyInitiators = levelVo.getSubsidyInitiators();
            List<KeyValueDTO> operationSubsidies = GroupBuyingInfoVOHelper.listInitiatorVOS(subsidyInitiators);
            levelVo.setOperationSubsidies(operationSubsidies);
        }

    }

    private static String getDateFormatStr(Date date) {
        if (Objects.isNull(date)) {
            return "";
        }
        String dateFormatPattern = "yyyy-MM-dd HH:mm:ss";
        return DateFormatUtils.format(date, dateFormatPattern);
    }

    /**
     * 获取费用承担方VO
     *
     * @param initiators
     * @return
     */
    private static List<KeyValueDTO> listInitiatorVOS(List<MarketingGroupBuyingInitiatorInfoDTO> initiators) {
        if (CollectionUtils.isEmpty(initiators)) {
            return Lists.newArrayList();
        }
        return initiators.stream().filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getId())
                        && StringUtils.isNotEmpty(item.getName()) && Objects.nonNull(item.getAmount()))
                .map(item -> KeyValueDTO.builder().key(item.getName()).value(item.getAmount().stripTrailingZeros().toPlainString()).build())
                .collect(Collectors.toList());
    }

    /**
     * 获取指定费用承担方的金额。
     *
     * @param initiators
     * @param initiatorIdsSet
     * @return
     */
    private static BigDecimal getInitiatorAmount(List<MarketingGroupBuyingInitiatorInfoDTO> initiators, Set<Long> initiatorIdsSet) {
        if (CollectionUtils.isEmpty(initiators) || CollectionUtils.isEmpty(initiatorIdsSet)) {
            return null;
        }
        BigDecimal sumBigDecimal = BigDecimal.ZERO;
        for (MarketingGroupBuyingInitiatorInfoDTO initiator : initiators) {
            if (Objects.isNull(initiator) || Objects.isNull(initiator.getId()) || Objects.isNull(initiator.getAmount())) {
                continue;
            }
            if (initiatorIdsSet.contains(initiator.getId())) {
                sumBigDecimal = sumBigDecimal.add(initiator.getAmount());
            }
        }
        if (sumBigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return sumBigDecimal;
    }

    private static GroupBuyingSaleScopeVO buildGroupBuyingSaleScopeVO(FrameActReportSaleScopeDTO frameActReportSaleScopeDTO){
        if (frameActReportSaleScopeDTO == null) {
            return null;
        }
        GroupBuyingSaleScopeVO groupBuyingSaleScopeVO = new GroupBuyingSaleScopeVO();
        groupBuyingSaleScopeVO.setIsCopySaleArea(frameActReportSaleScopeDTO.getIsCopySaleArea());
        groupBuyingSaleScopeVO.setScopeType(frameActReportSaleScopeDTO.getScopeType());
        groupBuyingSaleScopeVO.setCustomerGroupId(frameActReportSaleScopeDTO.getCustomerGroupId());
        groupBuyingSaleScopeVO.setIsCopyBusArea(frameActReportSaleScopeDTO.getIsCopyBusArea());
        groupBuyingSaleScopeVO.setIsCopyControlUser(frameActReportSaleScopeDTO.getIsCopyControlUser());
        groupBuyingSaleScopeVO.setIsCopyControlRoster(frameActReportSaleScopeDTO.getIsCopyControlRoster());
        groupBuyingSaleScopeVO.setBusAreaId(frameActReportSaleScopeDTO.getBusAreaId());
        groupBuyingSaleScopeVO.setBusAreaName(frameActReportSaleScopeDTO.getBusAreaName());
        groupBuyingSaleScopeVO.setControlUserTypes(frameActReportSaleScopeDTO.getControlUserTypes());
        groupBuyingSaleScopeVO.setControlUserTypeList(frameActReportSaleScopeDTO.getControlUserTypeList());
        groupBuyingSaleScopeVO.setControlRosterType(frameActReportSaleScopeDTO.getControlRosterType());
        groupBuyingSaleScopeVO.setControlGroupId(frameActReportSaleScopeDTO.getControlGroupId());
        groupBuyingSaleScopeVO.setControlGroupName(frameActReportSaleScopeDTO.getControlGroupName());
        return groupBuyingSaleScopeVO;
    }

}
