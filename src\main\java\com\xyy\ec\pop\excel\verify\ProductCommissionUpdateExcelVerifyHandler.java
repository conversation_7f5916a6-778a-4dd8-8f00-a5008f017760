package com.xyy.ec.pop.excel.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.xyy.ec.pop.vo.ProductCommissionBatchUpdateVo;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description 商品佣金比例校验
 */
public class ProductCommissionUpdateExcelVerifyHandler implements IExcelVerifyHandler<ProductCommissionBatchUpdateVo> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(ProductCommissionBatchUpdateVo updateVo) {
        if(null != updateVo.getCsuid()){
            return new ExcelVerifyHandlerResult(true);
        }
        return new ExcelVerifyHandlerResult(false);
    }
}
