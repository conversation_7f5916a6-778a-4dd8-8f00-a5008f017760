package com.xyy.ec.pop.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.dto.PopSupplierMenuAdminQueryParam;
import com.xyy.ec.pop.server.api.seller.dto.PopSupplierMenuDto;

import java.util.List;

public interface SupplierMenuService{

    PageInfo<PopSupplierMenuDto> queryPageList(PopSupplierMenuAdminQueryParam popSupplierMenuAdminQueryParam);

    List<PopSupplierMenuDto> queryRootMenu(PopSupplierMenuDto menu) throws ServiceException;

    void insert(PopSupplierMenuDto menu) throws ServiceException;

    PopSupplierMenuDto findById(Integer menuId) throws ServiceException;

    void updateBySelective(PopSupplierMenuDto menu);
}
