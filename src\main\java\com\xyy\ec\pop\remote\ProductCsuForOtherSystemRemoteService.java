package com.xyy.ec.pop.remote;

import com.xyy.ec.product.back.end.ecp.csu.api.CsuForOtherSystemApi;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuDTO;
import com.xyy.ec.product.back.end.ecp.priceGroup.dto.PriceMerchantGroupDto;

import java.util.List;
import java.util.Map;

/**
 * {@link CsuForOtherSystemApi} 远程调用Service
 *
 * <AUTHOR>
 */
public interface ProductCsuForOtherSystemRemoteService {

    /**
     * 查询商品信息
     *
     * @param csuIds
     * @return
     */
    List<CsuDTO> listCsuInfosByCsuIds(List<Long> csuIds);

    Map<Long, PriceMerchantGroupDto> queryPriceMerchantGroupMap(List<Long> groupIds);

}
