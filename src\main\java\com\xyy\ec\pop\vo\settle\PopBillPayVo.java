package com.xyy.ec.pop.vo.settle;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/1 14:58
* @table
*/
@Data
public class PopBillPayVo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 商家名称
     */
    private String orgName;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 入帐单
     */
    private String flowNo;

    /**
     * 商品总金额
     */
    private BigDecimal productMoney;

    /**
     * 单据总金额
     */
    private BigDecimal totalMoney;

    /**
     * 订单实际付款金额
     */
    private BigDecimal money;

    /**
     * 运费
     */
    private BigDecimal freightAmount;

    /**
     * 店铺优惠券金额
     */
    private BigDecimal couponShopAmount;

    /**
     * 店铺营销活动优惠金额
     */
    private BigDecimal marketingShopAmount;

    /**
     * 店铺总优惠金额
     */
    private BigDecimal shopTotalDiscount;

    /**
     * 平台优惠券金额
     */
    private BigDecimal couponPlatformAmount;

    /**
     * 平台营销活动优惠金额
     */
    private BigDecimal marketingPlatformAmount;

    /**
     * 平台总优惠金额
     */
    private BigDecimal platformTotalDiscount;

    /**
     * 佣金
     */
    private BigDecimal hireMoney;
    /**
     * 应收佣金
     */
    private BigDecimal payableCommission;
    /**
     * 佣金结算方式，1：非月结；2：月结
     */
    private Byte settlementType;
    /**
     * 处罚金额
     */
    private BigDecimal penaltyAmount;

    /**
     * 应结算金额
     */
    private BigDecimal statementTotalMoney;

    /**
     * 支付类型(1:在线支付 2:货到付款 3:线下转账）
     */
    private Byte payType;
    /**
     * 多个支付类型
     */
    private List<Byte> payTypes;
    /**
     * 账单入账状态 0-未入账 1-已入账
     */
    private Byte billPaymentStatus;

    /**
     * 账单入账时间
     */
    private Date billPaymentTime;

    /**
     * 打款状态 0：未打款，1:已打款
     */
    private Byte remitStatus;

    /**
     * 确认打款日期
     */
    private Date remitTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 账单生成开始时间
     */
    private Date startCreateTime;
    /**
     * 账单生成结束时间
     */
    private Date endCreateTime;
    /**
     * 打款开始时间
     */
    private Date startRemitTime;
    /**
     * 打款结束时间
     */
    private Date endRemitTime;
    /**
     * 入帐单号确认打款时用
     */
    private List<String> flowNoList;

    private List<String> orgIds;

    /**
     * 分润状态 1-未分润 2-分润成功 3-分润失败
     */
    private Byte billShareStatus;

    /**
     * 入账单分润开始时间
     */
    private Date startBillShareTime;

    /**
     * 入账单分润结束时间
     */
    private Date endBillShareTime;

    /**
     * 支付通道 1-直连支付 2-富民支付
     */
    private Integer paymentChannel;

    /**
     * 支持多个支付通道查询
     */
    private List<Integer> paymentChannels;

    /**
     * 账单号
     */
    private String billNo;


    private Long provId;

    private List<Long> provIds;

    /**
     * 补贴冲抵佣金 0-否 1-是
     */
    private Byte deducted;
}
