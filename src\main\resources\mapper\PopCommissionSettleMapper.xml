<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.ec.pop.dao.PopCommissionSettleMapper">
    <resultMap id="BaseResultMap" type="com.xyy.ec.pop.po.PopCommissionSettlePo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="hire_no" jdbcType="VARCHAR" property="hireNo"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="hire_money" jdbcType="DECIMAL" property="hireMoney"/>
        <result column="deducted_commission" jdbcType="DECIMAL" property="deductedCommission" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="hire_months" jdbcType="VARCHAR" property="hireMonths"/>
        <result column="payment_term" jdbcType="TIMESTAMP" property="paymentTerm"/>
        <result column="payment_time" jdbcType="TIMESTAMP" property="paymentTime"/>
        <result column="payment_certificate" jdbcType="VARCHAR" property="paymentCertificate"/>
        <result column="upload_time" jdbcType="TIMESTAMP" property="uploadTime"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updator" jdbcType="VARCHAR" property="updator"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , hire_no, org_id, org_name, hire_money, deducted_commission, create_time, hire_months, payment_term, payment_time, payment_certificate, upload_time,
    state, remarks, update_time, updator
    </sql>

    <select id="queryBillNoListByForHireMoneyZero" resultType="java.lang.String">
        select bill_no
        from tb_xyy_pop_commission_settle cs
        inner join tb_xyy_pop_commission_settle_detail csd on cs.hire_no = csd.hire_no
        where cs.org_id in
        <foreach collection="orgIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        and hire_money = 0
    </select>


</mapper>