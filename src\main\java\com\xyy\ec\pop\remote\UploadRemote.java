package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.upload.api.UploadApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * @Description 查询区域
 * <AUTHOR>
 * @Date 2020/12/19
 */
@Component
@Slf4j
public class UploadRemote {
    @Reference(version = "1.0.0")
    private UploadApi uploadApi;

    public String uploadAndGetFullPath(String filePath, String fileName) {
        log.info("UploadRemote.uploadAndGetFullPath(java.lang.String, java.lang.String)#filePath:{},fileName:{}", filePath, fileName);
        try {
            ApiRPCResult<String> apiRPCResult = uploadApi.uploadAndGetFullPath(filePath, fileName);
            log.info("UploadRemote.uploadAndGetFullPath(java.lang.String, java.lang.String)#filePath:{},fileName:{},apiRPCResult:{}", filePath, fileName, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return null;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("UploadRemote.uploadAndGetFullPath(java.lang.String, java.lang.String)#error. filePath:{},fileName:{}", filePath, fileName, e);
            return null;
        }
    }

    public String uploadAndGetFullPath(InputStream inputStream, String fileName) {
        log.info("UploadRemote.uploadAndGetFullPath(java.io.InputStream, java.lang.String)#inputStream:{},fileName:{}", inputStream, fileName);
        try {
            ApiRPCResult<String> apiRPCResult = uploadApi.uploadAndGetFullPath(inputStream, fileName);
            log.info("UploadRemote.uploadAndGetFullPath(java.io.InputStream, java.lang.String)#inputStream:{},fileName:{},apiRPCResult:{}", inputStream, fileName, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return null;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("UploadRemote.uploadAndGetFullPath(java.io.InputStream, java.lang.String)#error. inputStream:{},fileName:{}", inputStream, fileName, e);
            return null;
        }
    }

    public String uploadAndGetRelativePath(String filePath, String fileName) {
        log.info("UploadRemote.uploadAndGetRelativePath(java.lang.String, java.lang.String)#filePath:{},fileName:{}", filePath, fileName);
        try {
            ApiRPCResult<String> apiRPCResult = uploadApi.uploadAndGetRelativePath(filePath, fileName);
            log.info("UploadRemote.uploadAndGetRelativePath(java.lang.String, java.lang.String)#filePath:{},fileName:{},apiRPCResult:{}", filePath, fileName, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return null;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("UploadRemote.uploadAndGetRelativePath(java.lang.String, java.lang.String)#error. filePath:{},fileName:{}", filePath, fileName, e);
            return null;
        }
    }

    public String uploadAndGetRelativePath(InputStream inputStream, String fileName) {
        log.info("UploadRemote.uploadAndGetRelativePath(java.io.InputStream, java.lang.String)#inputStream:{},fileName:{}", inputStream, fileName);
        try {
            ApiRPCResult<String> apiRPCResult = uploadApi.uploadAndGetRelativePath(inputStream, fileName);
            log.info("UploadRemote.uploadAndGetRelativePath(java.io.InputStream, java.lang.String)#inputStream:{},fileName:{},apiRPCResult:{}", inputStream, fileName, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return null;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("UploadRemote.uploadAndGetRelativePath(java.io.InputStream, java.lang.String)#error. inputStream:{},fileName:{}", inputStream, fileName, e);
            return null;
        }
    }

    public void downloadFile(HttpServletResponse response, String path, String fileName) {
        log.info("UploadRemote.downloadFile#path:{},fileName:{}", path, fileName);
        try {
            try (OutputStream out = response.getOutputStream()) {
                response.reset();
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), "iso-8859-1"));
                ApiRPCResult<byte[]> apiRPCResult = uploadApi.downloadFile(path);
                log.info("UploadRemote.downloadFile#path:{},fileName:{},apiRPCResult:{}", path, fileName, JSON.toJSONString(apiRPCResult));
                if (apiRPCResult == null || apiRPCResult.isFail()) {
                    return;
                }
                out.write(apiRPCResult.getData());

            } catch (IOException e) {
                log.error("UploadRemote.downloadFile#IO异常. path:{},fileName:{}", path, fileName, e);
            }
        } catch (Exception e) {
            log.error("UploadRemote.downloadFile#error. path:{},fileName:{}", path, fileName, e);
        }
    }

}
