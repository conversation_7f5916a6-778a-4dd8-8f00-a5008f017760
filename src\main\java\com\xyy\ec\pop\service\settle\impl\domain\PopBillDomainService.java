package com.xyy.ec.pop.service.settle.impl.domain;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.excel.entity.OfflinePopBillDetailExportVo;
import com.xyy.ec.pop.excel.entity.OnlinePayPopBillDetailExportVo;
import com.xyy.ec.pop.excel.style.RefundExcelExportStyler;
import com.xyy.ec.pop.helper.PopBillHelper;
import com.xyy.ec.pop.po.PopBillDetailPo;
import com.xyy.ec.pop.po.PopBillPo;
import com.xyy.ec.pop.po.PopBillSettlePo;
import com.xyy.ec.pop.remote.PopCorporationRemoteAdapter;
import com.xyy.ec.pop.server.api.seller.dto.PopCorporationDto;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.service.settle.PopBillDetailService;
import com.xyy.ec.pop.service.settle.PopBillService;
import com.xyy.ec.pop.service.settle.PopBillSettleService;
import com.xyy.ec.pop.service.settle.PopCommissionSettleService;
import com.xyy.ec.pop.utils.CollectionUtil;
import com.xyy.ec.pop.utils.EncodeUtil;
import com.xyy.ec.pop.vo.settle.PopBillStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @date  2020/12/2 19:21
* @table
*/
@Slf4j
@Service
public class PopBillDomainService {

    @Autowired
    private PopBillService popBillService;
    @Autowired
    private PopBillDetailService popBillDetailService;
    @Autowired
    private PopCommissionSettleService popCommissionSettleService;
    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;
    @Autowired
    private PopBillSettleService popBillSettleService;

    public Long queryPopBillListCount(PopBillVo popBillVo){
        if(handleParam(popBillVo)) {
            return 0L;
        }
        return popBillService.queryPopBillListCount(popBillVo);
    }

    private boolean handleParam(PopBillVo popBillVo) {
        if (StringUtils.isNotEmpty(popBillVo.getName()) || StringUtils.isNotEmpty(popBillVo.getOrgName())){
            List<String> orgIds = popCorporationRemoteAdapter.getOrgIdByName(popBillVo.getName(),popBillVo.getOrgName());
            popBillVo.setName(null);
            popBillVo.setOrgName(null);
            if (CollectionUtil.isEmpty(orgIds)){
                return true;
            }else{
                popBillVo.setOrgIds(orgIds);
            }
        }
        return false;
    }

    public PageInfo<PopBillPo> queryPopBillList(PopBillVo popBillVo, PageInfo pageInfo) {
        if(handleParam(popBillVo)) {
            return new PageInfo<>();
        }
        if(StringUtils.isNotBlank(popBillVo.getBusinessNo())){
            String billNo = popBillDetailService.queryPopBillNoByBusinessNo(popBillVo.getBusinessNo());
            if(StringUtils.isEmpty(billNo) || (StringUtils.isNotBlank(popBillVo.getBillNo()) && ! popBillVo.getBillNo().equals(billNo))){
                return new PageInfo<>();
            }
            popBillVo.setBillNo(billNo);
        }

        PopBillPo popBillPo = PopBillHelper.convertPopBillPay(popBillVo);
        List<PopBillPo> popBillPos;
        if (pageInfo != null) {
            int pageNum = ((pageInfo.getPageNum() == 0 ? 1 : pageInfo.getPageNum()) - 1) * pageInfo.getPageSize();
            popBillPos = popBillService.queryPopBillList(popBillVo, pageNum, pageInfo.getPageSize());
        }else {
            popBillPos = popBillService.queryPopBillList(popBillVo, null, null);
        }
        //月结和非月结佣金统一合并到hireMoney字段
        popBillPos.stream().forEach(item->{
            if (item.getSettlementType() == CommissionSettleTypeEnum.EVERY_MONTH.getCode()) {
                item.setHireMoney(item.getPayableCommission());
            }
        });
        PageInfo<PopBillPo> billPoPageInfo = new PageInfo<>(popBillPos);

        //查询佣金结算金额为0的佣金结算单，并查询对应的账单号集合
        if (!CollectionUtils.isEmpty(popBillPos)) {
            List<String> orgIds = popBillPos.stream().map(PopBillPo::getOrgId).collect(Collectors.toList());
            List<String> billNoList = popCommissionSettleService.queryBillNoListByForHireMoneyZero(orgIds);
            Map<String, PopCorporationDto> popCorporationDtoMap = popCorporationRemoteAdapter.queryPopCorporationDtoMap(orgIds);
            if (CollectionUtil.isNotEmpty(popCorporationDtoMap)){
                for (PopBillPo po:popBillPos) {
                    PopCorporationDto popCorporationDto = popCorporationDtoMap.get(po.getOrgId());
                    if (popCorporationDto != null) {
                        po.setName(popCorporationDto.getName());
                        po.setOrgName(popCorporationDto.getCompanyName());
                    }
                }
            }
            popBillPos.stream().forEach(item -> {
                if (billNoList.contains(item.getBillNo())) {
                    //账单所属佣金结算单开票金额为0，则设置发票状态为2-无需开票
                    item.setInvoiceStatus((byte) 2);
                }
            });
        }

        if (pageInfo != null) {
            billPoPageInfo.setPageSize(pageInfo.getPageSize());
            billPoPageInfo.setPageNum(pageInfo.getPageNum());
        }
        Long count = popBillService.queryPopBillListCount(popBillVo);
        billPoPageInfo.setTotal(count);

        if (pageInfo != null) {
            Long totalPageNum = (count + pageInfo.getPageSize() - 1) / pageInfo.getPageSize();
            billPoPageInfo.setPages(totalPageNum.intValue());
        }
        return billPoPageInfo;
    }

    public PageInfo<PopBillDetailPo> queryPopBillDetail(String billNo, PageInfo pageInfos){
        PageInfo<PopBillDetailPo> pageInfo = new PageInfo<>();
        Page<PopBillDetailPo> page = PageMethod.offsetPage((pageInfos.getPageNum()-1)*pageInfos.getPageSize(),pageInfos.getPageSize());
        List<PopBillDetailPo> popBillPaymentDetailPos = popBillDetailService.queryPopBillDetail(PopBillDetailPo.builder().billNo(billNo).build(),pageInfos.getPageNum(),pageInfos.getPageSize());
        if(CollectionUtils.isEmpty(popBillPaymentDetailPos)){
            pageInfo.setList(Collections.emptyList());
            pageInfo.setTotal(0);
            pageInfo.setPages(0);
            return pageInfo;
        }
        List<String> businessNos = popBillPaymentDetailPos.stream().map(PopBillDetailPo::getBusinessNo).collect(Collectors.toList());
        //查询佣金折扣和折扣原因
        List<PopBillSettlePo> popBillSettlePos = popBillSettleService.selectByBusinessNos(businessNos);
        Map<String, PopBillSettlePo> businessNoSettleMap = popBillSettlePos.stream().collect(Collectors.toMap(PopBillSettlePo::getBusinessNo, Function.identity()));
        //填充佣金折扣和折扣原因、处理佣金金额（月结和非月结合并到一个字段）
        popBillPaymentDetailPos.stream().forEach(item -> {
            if (item.getSettlementType() == CommissionSettleTypeEnum.EVERY_MONTH.getCode()) {
                item.setHireMoney(item.getPayableCommission());
            }
            PopBillSettlePo popBillSettlePo = businessNoSettleMap.get(item.getBusinessNo());
            if (popBillSettlePo == null) {
                return;
            }
            item.setCommissionDiscount(popBillSettlePo.getCommissionDiscount());
            item.setDiscountReason(popBillSettlePo.getDiscountReason());
            item.setPaymentChannel(popBillSettlePo.getPaymentChannel());
        });
        pageInfo.setPages(page.getPages());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setList(popBillPaymentDetailPos);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    public void offlineAccountExportBillPaymemtDetailList(PopBillVo popBillVo, HttpServletResponse res) throws IOException {
        if (handleParam(popBillVo)){
            return;
        }
        List<String> billNoList = popBillService.queryByOrderNos(PopBillHelper.convertPopBillPay(popBillVo));
        if(CollectionUtils.isEmpty(billNoList)){
            return;
        }
        Workbook workbook = null;
        ExportParams params = new ExportParams();
        params.setSheetName("账单明细");
        params.setType(ExcelType.XSSF);
        params.setColor(IndexedColors.BLUE_GREY.index);
        params.setFreezeCol(2);
        params.setStyle(RefundExcelExportStyler.class);

        List<List<String>> pageList = Lists.partition(billNoList, 200);
        for(List<String> billNos: pageList){
            List<PopBillDetailPo> popBillDetailPos = popBillDetailService.queryPopBillDetailByBillNoList(billNos);
            List<String> orgIds = popBillDetailPos.stream().map(PopBillDetailPo::getOrgId).collect(Collectors.toList());
            Map<String, PopCorporationDto> popCorporationDtoMap = popCorporationRemoteAdapter.queryPopCorporationDtoMap(orgIds);
            if (CollectionUtil.isNotEmpty(popCorporationDtoMap)){
                for (PopBillDetailPo po:popBillDetailPos) {
                    PopCorporationDto popCorporationDto = popCorporationDtoMap.get(po.getOrgId());
                    if (popCorporationDto != null) {
                        po.setName(popCorporationDto.getName());
                        po.setOrgName(popCorporationDto.getCompanyName());
                    }
                }
            }
            List<OfflinePopBillDetailExportVo> offlinePopBillDetailExportVo = getOfflinePopBillDetailExportVo(popBillDetailPos);
            workbook = ExcelExportUtil.exportBigExcel(params, OfflinePopBillDetailExportVo.class, offlinePopBillDetailExportVo);
        }
        ExcelExportUtil.closeExportBigExcel();
        ServletOutputStream out = res.getOutputStream();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = URLEncoder.encode("账单明细-", EncodeUtil.DEFAULT_URL_ENCODING) + sdf.format(new Date());	//对中文进行URL编码，防止乱码
        res.setHeader("Content-type", "application/x-xls; charset=" + EncodeUtil.DEFAULT_URL_ENCODING);
        res.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xlsx");
        res.setContentType("application/msexcel");
        if(null != workbook){
            workbook.write(out);
        }
    }

    private List<OfflinePopBillDetailExportVo> getOfflinePopBillDetailExportVo(List<PopBillDetailPo> popBillDetailPos) {
        if(CollectionUtils.isEmpty(popBillDetailPos)){
            return Lists.newArrayList();
        }
        List<String> billNoList = popBillDetailPos.stream().map(o -> o.getBillNo()).distinct().collect(Collectors.toList());
        List<PopBillPo> popBillPos = popBillService.queryPopBillByBillNoList(billNoList);
        if(CollectionUtils.isEmpty(popBillPos)){
            return Lists.newArrayList();
        }
        //查询佣金结算金额为0的佣金结算单，并查询对应的账单号集合
        if (!CollectionUtils.isEmpty(popBillPos)) {
            List<String> orgIds = popBillPos.stream().map(PopBillPo::getOrgId).collect(Collectors.toList());
            List<String> billNos = popCommissionSettleService.queryBillNoListByForHireMoneyZero(orgIds);
            popBillPos.stream().forEach(item -> {
                if (billNos.contains(item.getBillNo())) {
                    //账单所属佣金结算单开票金额为0，则设置发票状态为2-无需开票
                    item.setInvoiceStatus((byte) 2);
                }
            });
        }
        Map<String, PopBillPo> popBillPoMap = popBillPos.stream().collect(Collectors.toMap(PopBillPo::getBillNo, Function.identity()));
        List<OfflinePopBillDetailExportVo> offlinePopBillDetailExportVoList = PopBillHelper.convertOfflinePopBillDetailExport(popBillDetailPos, popBillPoMap);
        return offlinePopBillDetailExportVoList;
    }



    public Long queryExprotBillDetailCount(PopBillVo popBillVo) {
        Long popBillDetailtCount = 0L;
        if (handleParam(popBillVo)){
            return 0L;
        }
        PopBillPo popBillPo = PopBillHelper.convertPopBillPay(popBillVo);
        Long popBillCount = popBillService.queryPopBillListCount(popBillVo);
        if(popBillCount > 5000){
            return popBillCount;
        }
        List<String> billNoList = popBillService.queryByOrderNos(popBillPo);
        if(CollectionUtils.isEmpty(billNoList)){
            return popBillDetailtCount;
        }
        List<List<String>> partition = Lists.partition(billNoList, 200);
        for(List<String> billNos : partition){
            popBillDetailtCount+= popBillDetailService.queryPopBillDetailCount(billNos);
        }
        return popBillDetailtCount;
    }

    public PopBillStatisVo queryPopBillPayStatis(PopBillVo popBillVo) {
        PopBillStatisVo popBillStatisVo = new PopBillStatisVo();
        if (handleParam(popBillVo)){
            return popBillStatisVo;
        }
        if (StringUtils.isNotBlank(popBillVo.getBusinessNo())) {
            String billNo = popBillDetailService.queryPopBillNoByBusinessNo(popBillVo.getBusinessNo());
            if (StringUtils.isEmpty(billNo) || (StringUtils.isNotBlank(popBillVo.getBillNo()) && !popBillVo.getBillNo().equals(billNo))) {
                return popBillStatisVo;
            }
            popBillVo.setBillNo(billNo);
        }
        return popBillService.queryPopBillStatis(popBillVo);
    }

    public PopBillPo selectByBillNo(String billNo) {
        PopBillPo popBillPo = popBillService.selectByBillNo(billNo);
        if(Objects.nonNull(popBillPo)){
            List<String> billNoList = popCommissionSettleService.queryBillNoListByForHireMoneyZero(Arrays.asList(popBillPo.getOrgId()));
            if (billNoList.contains(billNo)) {
                //账单所属佣金结算单开票金额为0，则设置发票状态为2-无需开票
                popBillPo.setInvoiceStatus((byte) 2);
            }
        }
        return popBillPo;
    }

    public void updateBatchPopBill(List<PopBillPo> popBillPos,PopBillVo popBillVo) {
        popBillPos.forEach(o->{
            o.setInvoiceTime(popBillVo.getInvoiceTime());
            o.setInvoiceStatus((byte) 1);
        });
        popBillService.batchUpdateById(popBillPos);
    }

    public void olinePayExportBillPaymemtDetailList(PopBillVo popBillVo, HttpServletResponse res) throws IOException {
        if (handleParam(popBillVo)){
            return;
        }
        List<String> billNoList = popBillService.queryByOrderNos(PopBillHelper.convertPopBillPay(popBillVo));
        if(CollectionUtils.isEmpty(billNoList)){
            return;
        }
        Workbook workbook = null;
        ExportParams params = new ExportParams();
        params.setSheetName("账单明细");
        params.setType(ExcelType.XSSF);
        params.setColor(IndexedColors.BLUE_GREY.index);
        params.setFreezeCol(2);
        params.setStyle(RefundExcelExportStyler.class);

        List<List<String>> pageList = Lists.partition(billNoList, 200);
        for(List<String> billNos: pageList){
            List<PopBillDetailPo> popBillDetailPos = popBillDetailService.queryPopBillDetailByBillNoList(billNos);

            List<String> orgIds = popBillDetailPos.stream().map(PopBillDetailPo::getOrgId).collect(Collectors.toList());
            Map<String, PopCorporationDto> popCorporationDtoMap = popCorporationRemoteAdapter.queryPopCorporationDtoMap(orgIds);
            if (CollectionUtil.isNotEmpty(popCorporationDtoMap)){
                for (PopBillDetailPo po:popBillDetailPos) {
                    PopCorporationDto popCorporationDto = popCorporationDtoMap.get(po.getOrgId());
                    if (popCorporationDto != null) {
                        po.setName(popCorporationDto.getName());
                        po.setOrgName(popCorporationDto.getCompanyName());
                    }
                }
            }
            List<OnlinePayPopBillDetailExportVo> onlinePayPopBillDetailExportVos = getOlinePopBillDetailExportVo(popBillDetailPos);
            workbook = ExcelExportUtil.exportBigExcel(params, OnlinePayPopBillDetailExportVo.class, onlinePayPopBillDetailExportVos);
        }
        ExcelExportUtil.closeExportBigExcel();
        ServletOutputStream out = res.getOutputStream();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = URLEncoder.encode("账单明细-", EncodeUtil.DEFAULT_URL_ENCODING) + sdf.format(new Date());	//对中文进行URL编码，防止乱码
        res.setHeader("Content-type", "application/x-xls; charset=" + EncodeUtil.DEFAULT_URL_ENCODING);
        res.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xlsx");
        res.setContentType("application/msexcel");
        if(null != workbook){
            workbook.write(out);
        }
    }

    private List<OnlinePayPopBillDetailExportVo> getOlinePopBillDetailExportVo(List<PopBillDetailPo> popBillDetailPos) {
        if(CollectionUtils.isEmpty(popBillDetailPos)){
            return Lists.newArrayList();
        }
        List<String> billNoList = popBillDetailPos.stream().map(o -> o.getBillNo()).distinct().collect(Collectors.toList());
        List<PopBillPo> popBillPos = popBillService.queryPopBillByBillNoList(billNoList);
        if(CollectionUtils.isEmpty(popBillPos)){
            return Lists.newArrayList();
        }
        //查询佣金结算金额为0的佣金结算单，并查询对应的账单号集合
        if (!CollectionUtils.isEmpty(popBillPos)) {
            List<String> orgIds = popBillPos.stream().map(PopBillPo::getOrgId).collect(Collectors.toList());
            List<String> billNos = popCommissionSettleService.queryBillNoListByForHireMoneyZero(orgIds);
            popBillPos.stream().forEach(item -> {
                if (billNos.contains(item.getBillNo())) {
                    //账单所属佣金结算单开票金额为0，则设置发票状态为2-无需开票
                    item.setInvoiceStatus((byte) 2);
                }
            });
        }
        Map<String, PopBillPo> popBillPoMap = popBillPos.stream().collect(Collectors.toMap(PopBillPo::getBillNo, Function.identity()));
        List<OnlinePayPopBillDetailExportVo> offlinePopBillDetailExportVoList = PopBillHelper.convertOnlinePopBillDetailExport(popBillDetailPos, popBillPoMap);
        return offlinePopBillDetailExportVoList;
    }
}
