package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: Jincheng.Li
 * @Date: 2021/07/13/16:21
 * @Description:
 */
@Data
public class PopAnnounceQueryParamVo implements Serializable {
    private static final long serialVersionUID = 2938118917886226167L;

    /**
     * 公告标题
     */
    private String title;

    /**
     * 创建时间起始时间
     */
    private Long createTimeStart;

    /**
     * 创建时间截至时间
     */
    private Long createTimeEnd;

    /**
     * 最后更新时间起始时间
     */
    private Long updateTimeStart;

    /**
     * 最后更新时间截至时间
     */
    private Long updateTimeEnd;

    /**
     * 状态
     */
    private Integer status;

    private Integer type;

    /**
     * 是否在首页展示 0否 1是
     */
    private Integer isShowBanner;


    /**
     * 适用店铺类型
     */
    private Integer applicableType;

    /**
     * 适用店铺具体值
     */
    private String applicableCode;

    /**
     * 是否弹框展示 0否 1是
     */
    private Integer showDialog;

    /**
     * 排序类型 1：创建时间倒叙  0创建时间正序
     */
    private Integer sortType;

    /**
     * 是否重要
     */
    private Integer redTitle;

    private Integer pageNum;
    private Integer pageSize;
}
