package com.xyy.ec.pop.controller.mop;

import com.xyy.ec.pop.adapter.mop.AccountEnterpriseQrcodeAdapter;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.result.AccountEnterpriseQrcodeBasicDTO;
import com.xyy.scm.constant.entity.pagination.Paging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RequestMapping("/mop/qrcode")
@RestController
public class AccountEnterpriseQrcodeController {

    @Autowired
    private AccountEnterpriseQrcodeAdapter accountEnterpriseQrcodeAdapter;


    @GetMapping("/list")
    public ResponseVo<Paging<AccountEnterpriseQrcodeBasicDTO>> listAccountEnterpriseQrcode(AccountEnterpriseQrcodeBasicDTO qrcodeBasicDTO) {
        return accountEnterpriseQrcodeAdapter.listAccountEnterpriseQrcode(qrcodeBasicDTO);
    }
}
