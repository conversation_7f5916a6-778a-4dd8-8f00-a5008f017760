package com.xyy.ec.pop.marketing.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.marketing.shop.activity.dto.CouponRecallRecordReqDTO;
import com.xyy.ec.pop.marketing.vo.CouponInstanceVO;
import com.xyy.ec.pop.marketing.vo.CouponRecallRecordVO;
import com.xyy.ec.pop.marketing.vo.CouponRecallResultVo;
import com.xyy.ec.pop.marketing.vo.CouponTemplateVO;

public interface VoucherRecallService {

    /**
     * 查询列表
     *
     * @param recordReqDTO 查询参数
     * @return 列表数据
     */
    PageInfo<CouponRecallRecordVO> pageInfo(CouponRecallRecordReqDTO recordReqDTO);

    /**
     * 查询优惠券
     *
     * @param templateId   模板id
     * @param templateName 模板名称
     * @param pageNum      页数
     * @param pageSize     每页条数
     * @return 优惠券模板信息
     */
    PageInfo<CouponTemplateVO> selectVoucherTemplate(Long templateId, String templateName,
                                                     Integer pageNum, Integer pageSize);


    /**
     * 查询模板id下未使用的优惠券实例数量
     *
     * @param templateId 优惠券模板id
     * @return 数量
     */
    Integer getRecallTotalNum(Long templateId);

    /**
     * 召回优惠券
     *
     * @param templateId 券模板id
     * @return 成功召回数量
     */
    CouponRecallResultVo recallVoucher(Long templateId, String username);

    /**
     * 召回优惠券实例
     * @param voucherInstanceIdStr 优惠券实例id
     * @return 成功召回数量
     */
    CouponRecallResultVo recallVoucherInstance(String voucherInstanceIdStr, String username);

    /**
     * 查询用户券实例
     * @param phone 手机号
     * @param merchantName 客户名称
     * @param merchantId 客户id
     * @param pageNum 页数
     * @param pageSize 每页条数
     * @return 用户券实例
     */
    PageInfo<CouponInstanceVO> selectVoucherInstance(Long templateId, String phone,
                                                     String merchantName, Long merchantId,
                                                     Integer pageNum, Integer pageSize);
}
