package com.xyy.ec.pop.service.settle.impl;

import com.xyy.ec.pop.dao.PopBillMapper;
import com.xyy.ec.pop.po.PopBillPo;
import com.xyy.ec.pop.service.settle.PopBillService;
import com.xyy.ec.pop.vo.settle.PopBillStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/1 10:39
* @table
*/
@Slf4j
@Service
public class PopBillServiceImpl implements PopBillService {

    @Autowired
    private PopBillMapper popBillMapper;

    @Override
    public int insert(PopBillPo record) {
        return popBillMapper.insert(record);
    }

    @Override
    public int insertSelective(PopBillPo record) {
        return popBillMapper.insertSelective(record);
    }

    @Override
    public PopBillPo selectByPrimaryKey(Long id) {
        return popBillMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(PopBillPo record) {
        return popBillMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PopBillPo record) {
        return popBillMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<PopBillPo> queryPopBillList(PopBillVo popBillVo, Integer pageNum, Integer pageSize) {
        return popBillMapper.queryPopBillList(popBillVo,pageNum,pageSize);
    }

    @Override
    public Long queryPopBillListCount(PopBillVo popBillVo) {
        return popBillMapper.queryPopBillListCount(popBillVo);
    }

    @Override
    public PopBillStatisVo queryPopBillStatis(PopBillVo popBillVo) {
        return popBillMapper.queryPopBillStatis(popBillVo);
    }

    @Override
    public PopBillPo selectByBillNo(String billNo) {
        return popBillMapper.selectByBillNo(billNo);
    }

    @Override
    public List<String> queryByOrderNos(PopBillPo popBillPo) {
        return popBillMapper.queryByOrderNos(popBillPo);
    }

    @Override
    public List<PopBillPo> queryPopBillByBillNoList(List<String> billNoList) {
        return popBillMapper.queryPopBillByBillNoList(billNoList);
    }

    @Override
    public void batchUpdateById(List<PopBillPo> popBillPos) {
        popBillMapper.batchUpdateById(popBillPos);
    }
}
