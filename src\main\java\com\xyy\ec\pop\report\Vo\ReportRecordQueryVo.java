package com.xyy.ec.pop.report.Vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xyy.me.product.core.vo.PageSortVo;
import com.xyy.me.product.core.vo.product.sku.ProductPageVo;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ReportRecordQueryVo extends PageSortVo implements Serializable {
    private static final long serialVersionUID = 1811926810597472389L;
    /**
     * 客户名称或者药店编码
     */
    private String merchantName;
    /**
     * 客户类型
     */
    private String merchantTypes;
    /**
     * 省code
     */
    private Long provinceCode;
    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 举报时间
     */
    private String startFirstReportTime;
    private String endFirstReportTime;
    private String startLastReportTime;
    private String endLastReportTime;
}
