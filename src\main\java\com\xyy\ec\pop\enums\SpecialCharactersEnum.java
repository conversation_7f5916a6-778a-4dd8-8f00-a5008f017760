package com.xyy.ec.pop.enums;

/**
 * 特殊字符串
 */
public enum SpecialCharactersEnum {
    CHARACTER_ONE("%","%25"),//
    CHARACTER_TWO("+","%2B"),//
    CHARACTER_THREE("?","%3F"),//
    CHARACTER_FOUR("#","%23"),//
    CHARACTER_FIVE("&","%26"),//
    CHARACTER_SIX("=","%3D"),//
    CHARACTER_SEVEN("/","%2F"), //
    CHARACTER_EIGHT("*","%2A"),//
    CHARACTER_NINE("!","%21"),//
    CHARACTER_TEN("\"","%22"),//
    CHARACTER_ELEVEN("'","%27"),//
    CHARACTER_TWELVE("(","%28"),//
    CHARACTER_THIRTEEN(")","%29"),//
    CHARACTER_FOURTEEN(";","%3B"),//
    CHARACTER_FIFTEEN(":","%3A"),//
    CHARACTER_SIXTEEN("@","%40"),//
    CHARACTER_SEVENTEEN("$","%24"),//
    CHARACTER_EIGHTEEN(",","%2C"),//
    CHARACTER_NINETEEN("[","%5B"),//
    CHARACTER_TWENTY("]","%5D"),//
    ;

    SpecialCharactersEnum(String character, String escapeCode) {
        this.character = character;
        this.escapeCode = escapeCode;
    }

    /**
     * 循环转义特殊字符
     * @param str
     * @return
     */
    public static String getEscapeSpecialCharacters(String str) {
        for (SpecialCharactersEnum obj : SpecialCharactersEnum.values()) {
            str = str.replace(obj.getCharacter(),obj.getEscapeCode());
        }
        return str;
    }

    /**
     * 特殊字符
     */
    private String character;

    /**
     * 转义编码
     */
    private String escapeCode;

    public String getCharacter()
    {
        return character;
    }

    public String getEscapeCode()
    {
        return escapeCode;
    }
}
