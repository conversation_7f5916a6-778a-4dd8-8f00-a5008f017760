package com.xyy.ec.pop.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.insight.api.InsightChosenCustomerAdminApi;
import com.xyy.ec.marketing.insight.api.InsightChosenCustomerApi;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupBaseInfoDTO;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupDTO;
import com.xyy.ec.pop.remote.InsightChosenCustomerRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InsightChosenCustomerRemoteServiceImpl implements InsightChosenCustomerRemoteService {

    @Reference(version = "1.0.0")
    private InsightChosenCustomerApi insightChosenCustomerApi;

    @Reference(version = "1.0.0")
    private InsightChosenCustomerAdminApi insightChosenCustomerAdminApi;

    @Override
    public List<MarketCustomerGroupBaseInfoDTO> mgetChoseCustomerBaseInfo(List<Long> customerGroupIds) {
        if (CollectionUtils.isEmpty(customerGroupIds)) {
            return Lists.newArrayList();
        }
        customerGroupIds = Lists.newArrayList(Sets.newHashSet(customerGroupIds));
        if (CollectionUtils.isEmpty(customerGroupIds)) {
            return Lists.newArrayList();
        }
        List<MarketCustomerGroupBaseInfoDTO> result = Lists.newArrayListWithExpectedSize(customerGroupIds.size());
        List<List<Long>> customerGroupIdsLists = Lists.partition(customerGroupIds, 200);
        ApiRPCResult<List<MarketCustomerGroupBaseInfoDTO>> apiRPCResult;
        for (List<Long> customerGroupIdsList : customerGroupIdsLists) {
            apiRPCResult = insightChosenCustomerAdminApi.mgetChoseCustomerBaseInfo(customerGroupIdsList);
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
                log.error("InsightChosenCustomerAdminApi.mgetChoseCustomerBaseInfo 查询失败，入参：{}，出参：{}",
                        JSONArray.toJSONString(customerGroupIdsList), JSONObject.toJSONString(apiRPCResult));
                continue;
            }
            if (CollectionUtils.isNotEmpty(apiRPCResult.getData())) {
                result.addAll(apiRPCResult.getData());
            }
        }
        return result;
    }

    @Override
    public List<MarketCustomerGroupDTO> mgetChoseCustomer(List<Long> customerGroupIds) {
        if (CollectionUtils.isEmpty(customerGroupIds)) {
            return Lists.newArrayList();
        }
        customerGroupIds = customerGroupIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerGroupIds)) {
            return Lists.newArrayList();
        }
        List<MarketCustomerGroupDTO> result = Lists.newArrayListWithCapacity(customerGroupIds.size());
        List<List<Long>> customerGroupIdsLists = Lists.partition(customerGroupIds, 200);
        for (List<Long> customerGroupIdsList : customerGroupIdsLists) {
            List<MarketCustomerGroupDTO> marketCustomerGroupDTOS = insightChosenCustomerAdminApi.mgetChoseCustomer(customerGroupIdsList);
            if (CollectionUtils.isEmpty(marketCustomerGroupDTOS)) {
                log.error("InsightChosenCustomerAdminApi.mgetChoseCustomer 查询失败，入参：{}，出参：{}",
                        JSONArray.toJSONString(customerGroupIdsList), JSONObject.toJSONString(marketCustomerGroupDTOS));
                continue;
            }
            result.addAll(marketCustomerGroupDTOS);
        }
        return result;
    }
}
