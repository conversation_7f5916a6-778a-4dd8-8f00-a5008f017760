package com.xyy.ec.pop.report.Vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class MerchantDetailsVo implements Serializable {
    private static final long serialVersionUID = -1284651709429279815L;
    /**
     * 药店id
     */
    private Long merchantId;
    /**
     * 药店名称
     */
    private String merchantName;
    /**
     * 店铺id
     */
    private String orgId;
    private String companyName;
    private String corporationName;
    /**
     * 店铺编码
     */
    private String shopCode;
    /**
     * 商家名称
     * 取companyName
     */
    private String orgName;
    /**
     * 商品编码
     */
    private String barcode;

    private Long csuid;

    private String productName;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 规格
     */
    private String spec;
    /**
     * 批准文号
     */
    private String approvalNumber;
    /**
     * 举报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportTime;

    /**
     * 举报审核状态：0-待审核, 1-审核通过, -1-核驳回，2-移除
     */
    private Integer auditStatus;
    /**
     * 举报审核状态：0-待审核, 1-审核通过, -1-核驳回，2-移除
     */
    private String auditStatusStr;
    /**
     * 审核原因
     */
    private String auditDesc;
    /**
     * 审核人
     */
    private String operator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    private String sellerUserId;
}
