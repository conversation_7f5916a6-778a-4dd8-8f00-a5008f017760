package com.xyy.ec.pop.service.settle;

import com.xyy.ec.pop.po.PopBillSettlePo;
import com.xyy.ec.pop.server.api.admin.dto.AdjustiveBillSettleDto;
import com.xyy.ec.pop.server.api.admin.dto.CommissionSettleSetDto;
import com.xyy.ec.pop.server.api.order.dto.PopBillSettleDto;
import com.xyy.ec.pop.vo.AdjustiveBillSettleResultVo;
import com.xyy.ec.pop.vo.AdjustiveBillSettleVo;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.activity.UpdateBatchResultVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @date  2020/12/1 10:51
* @table
*/
public interface PopBillSettleService {

    int insert(PopBillSettlePo record);

    int insertSelective(PopBillSettlePo record);

    PopBillSettlePo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PopBillSettlePo record);

    int updateByPrimaryKey(PopBillSettlePo record);

    List<PopBillSettlePo> queryPopBillSettleList(PopBillSettleVo popBillSettleVo, Integer pageNum, Integer pageSize);

    Long queryPopBillSettleListCount( PopBillSettleVo popBillSettleVo);

    PopBillSettleStatisVo queryPopBillSettleStatis(PopBillSettleVo popBillSettleVo);

    List<PopBillSettlePo> selectByBusinessNos(List<String> businessNos);

    AdjustiveBillSettleResultVo batchImportAdjustiveBillSettle(List<AdjustiveBillSettleVo> vos, String user, Map<String, CommissionSettleSetDto> orgIdSettleSetMap);

    void batchUpdateAdjustiveBillSettle(List<AdjustiveBillSettleDto> params);

    List<PopBillSettleDto> listImportAdjustiveBillSettle(String userName);

    Boolean deleteAdjustiveBillSettleByIds(List<String> ids, String userName);

    ResponseVo updateCommissionCalcFlag(PopBillSettleVo vo, String operator);

    ResponseVo<UpdateBatchResultVo> updateBatchCommissionCalcFlag(MultipartFile file, String userName);

    void downloadCommissionCalcFlag(HttpServletResponse response);
}
