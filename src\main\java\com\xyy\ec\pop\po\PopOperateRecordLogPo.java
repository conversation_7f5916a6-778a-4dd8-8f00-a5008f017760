package com.xyy.ec.pop.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_xyy_pop_operate_record_log
 *
 * <AUTHOR>
@Data
public class PopOperateRecordLogPo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 操作人姓名
     */
    private String operateName;

    /**
     * 事件名称(表名称)
     */
    private String eventName;

    /**
     * 事件类型 insert update delete
     */
    private String eventType;

    /**
     * 事件内容字段信息转sjon或描述信息
     */
    private String eventContent;

    /**
     * 创建时间
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}