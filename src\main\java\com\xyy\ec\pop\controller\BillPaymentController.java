package com.xyy.ec.pop.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.po.PopBillPaymentDetailPo;
import com.xyy.ec.pop.po.PopBillPaymentPo;
import com.xyy.ec.pop.server.api.order.enums.OrderPayTypeEnums;
import com.xyy.ec.pop.server.api.seller.dto.PopShareProfitDto;
import com.xyy.ec.pop.service.settle.PopBillPaymentService;
import com.xyy.ec.pop.service.settle.impl.domain.PopBillPaymentDomainService;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.settle.PopBillPayStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillPayVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 入账单
 *
 * <AUTHOR>
 * @date 2022/8/16
 * @table
 */
@Slf4j
@RequestMapping("/billPayment")
@Controller
public class BillPaymentController extends BaseController {

    @Autowired
    private PopBillPaymentService popBillPaymentService;
    @Autowired
    private PopBillPaymentDomainService popBillPaymentDomainService;


    /**
     * 列表查询
     *
     * @param popBillPayVo
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/list")
    public ResponseVo<PageInfo<PopBillPaymentPo>> list(PopBillPayVo popBillPayVo, PageInfo pageInfo) {
        log.info("BillPaymentController.list#popBillPayVo:{},pageNum:{},pageSize:{}", JSON.toJSONString(popBillPayVo), pageInfo.getPageNum(), pageInfo.getPageSize());
        try {
            if (validateProv(popBillPayVo)) {
                return ResponseVo.successResult(new PageInfo<>(Lists.newArrayList()));
            }
            PageInfo<PopBillPaymentPo> result = popBillPaymentDomainService.queryPopBillList(popBillPayVo, pageInfo);
            log.info("BillPaymentController.list#popBillPayVo:{},pageNum:{},pageSize:{},result:{}", JSON.toJSONString(popBillPayVo), pageInfo.getPageNum(), pageInfo.getPageSize(), JSON.toJSONString(result));
            return ResponseVo.successResult(result);
        } catch (ServiceRuntimeException e) {
            log.error("BillPaymentController.list#自定义异常 popBillPayVo:{},pageNum:{},pageSize:{}", JSON.toJSONString(popBillPayVo), pageInfo.getPageNum(), pageInfo.getPageSize(), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillPaymentController.list#未知异常 popBillPayVo:{},pageNum:{},pageSize:{}", JSON.toJSONString(popBillPayVo), pageInfo.getPageNum(), pageInfo.getPageSize(), e);
            return ResponseVo.errRest("查询入账单列表异常");
        }
    }

    private boolean validateProv(PopBillPayVo popBillPayVo) {
        List<Long> provIds = getProvIds(popBillPayVo.getProvId());
        if (CollectionUtils.isEmpty(provIds)) {
            return true;
        }
        popBillPayVo.setProvIds(provIds);
        return false;
    }

    /**
     * 入账单金额统计
     *
     * @param popBillPayVo
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/queryBillPaymentStatistic")
    public ResponseVo<PopBillPayStatisVo> queryBillPaymentStatistic(PopBillPayVo popBillPayVo) {
        log.info("BillPaymentController.queryBillPaymentStatistic#popBillPayVo:{}", JSON.toJSONString(popBillPayVo));
        try {
            if (validateProv(popBillPayVo)) {
                PopBillPayStatisVo popBillPayStatisVo = new PopBillPayStatisVo();
                return ResponseVo.successResult(popBillPayStatisVo);
            }
            PopBillPayStatisVo popBillPaymentPo = popBillPaymentDomainService.queryPopBillPayStatis(popBillPayVo);
            if (popBillPaymentPo == null) {
                popBillPaymentPo = new PopBillPayStatisVo();
            }
            return ResponseVo.successResult(popBillPaymentPo);
        } catch (ServiceRuntimeException e) {
            log.error("BillPaymentController.queryBillPaymentStatistic#自定义异常 popBillPayVo:{}", JSON.toJSONString(popBillPayVo), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillPaymentController.queryBillPaymentStatistic#未知异常 popBillPayVo:{}", JSON.toJSONString(popBillPayVo), e);
            return ResponseVo.errRest("查询入账单金额统计异常");
        }
    }

    /**
     * 根据入帐单号查询入账单
     *
     * @param flowNo
     * @return
     */
    @RequestMapping(value = "/queryBillPaymentByNo", method = RequestMethod.GET)
    @ResponseBody
    public ResponseVo<PopBillPaymentPo> queryBillPaymentByNo(String flowNo) {
        log.info("BillPaymentController.queryBillPaymentByNo#flowNo:{}", flowNo);
        try {
            if (StringUtils.isBlank(flowNo)) {
                return ResponseVo.errRest("入账单号不能为空");
            }
            PopBillPaymentPo popBillPaymentPo = popBillPaymentService.selectByFlowNo(flowNo);
            log.info("BillPaymentController.queryBillPaymentByNo#flowNo:{}, result:{}", flowNo, JSON.toJSONString(popBillPaymentPo));
            //佣金字段合并
            if (popBillPaymentPo != null) {
                popBillPaymentPo.setHireMoney(popBillPaymentPo.getPayableCommission().add(popBillPaymentPo.getHireMoney()));
            }
            return ResponseVo.successResult(popBillPaymentPo);
        } catch (Exception e) {
            log.error("BillPaymentController.queryBillPaymentByNo#异常 flowNo:{}", flowNo, e);
            return ResponseVo.errRest("查询入账单信息异常");
        }
    }

    /**
     * 查询入账单明细，带分页
     *
     * @param flowNo
     * @return
     */
    @GetMapping(value = "/billPaymentDetail")
    @ResponseBody
    public ResponseVo billPaymentDetail(String flowNo, PageInfo pageInfo) {
        log.info("BillPaymentController.billPaymentDetail#flowNo:{},pageNum:{},pageSize:{}", flowNo, pageInfo.getPageNum(), pageInfo.getPageSize());
        try {
            if (StringUtils.isBlank(flowNo)) {
                return ResponseVo.errRest("入账单号不能为空");
            }
            PageInfo<PopBillPaymentDetailPo> popBillPaymentDetailPoPageInfo = popBillPaymentDomainService.queryPopBillPaymentDetail(flowNo, pageInfo);
            log.info("BillPaymentController.billPaymentDetail#flowNo:{},pageNum:{},pageSize:{},result:{}", flowNo, pageInfo.getPageNum(), pageInfo.getPageSize(), JSON.toJSONString(popBillPaymentDetailPoPageInfo));
            return ResponseVo.successResult(popBillPaymentDetailPoPageInfo);
        } catch (Exception e) {
            log.error("BillPaymentController.billPaymentDetail#异常 flowNo:{},pageNum:{},pageSize:{}", flowNo, pageInfo.getPageNum(), pageInfo.getPageSize(), e);
            return ResponseVo.errRest("查询入账单明细异常");
        }

    }

    /**
     * 入帐单列表查询分润失败入帐单数接口
     */
    @RequestMapping(value = "/queryBillShareFailCount", method = RequestMethod.GET)
    @ResponseBody
    public ResponseVo<Integer> queryBillShareFailCount() {
        try {
            return ResponseVo.successResult(popBillPaymentDomainService.queryBillPaymentCount());
        } catch (Exception e) {
            log.error("BillPaymentController.queryBillShareFailCount异常", e);
            return ResponseVo.errRest("查询分润失败的入账单数异常");
        }
    }

    /**
     * 分润失败的入账单重新分润
     */
    @PostMapping(value = "/againShareProfit")
    @ResponseBody
    public ResponseVo againShareProfit(@RequestBody List<PopShareProfitDto> shareProfitDtos) {
        try {
            log.info("BillPaymentController.againShareProfit#shareProfitDtos:{}", JSON.toJSONString(shareProfitDtos));
            if (CollectionUtils.isEmpty(shareProfitDtos)) {
                return ResponseVo.errRest("重新分润参数不能为空");
            }
            popBillPaymentDomainService.againShareProfit(shareProfitDtos);
            return ResponseVo.successResultNotData("重新分润推送成功");
        } catch (ServiceRuntimeException e) {
            log.error("BillPaymentController.againShareProfit 自定义异常#shareProfitDtos:{}", JSON.toJSONString(shareProfitDtos), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillPaymentController.againShareProfit 异常#shareProfitDtos:{}", JSON.toJSONString(shareProfitDtos), e);
            return ResponseVo.errRest("重新分润推送失败，请稍后重试");
        }
    }

    /**
     * 查询入账单导出的条数  超过5000条数据，前端给出提示
     *
     * @param popBillPayVo
     * @return
     */
    @GetMapping(value = "/queryBillPaymentExportCount")
    @ResponseBody
    public ResponseVo<Long> queryBillPaymentExportCount(PopBillPayVo popBillPayVo) {
        log.info("BillPaymentController.queryBillPaymentExportCount#popBillPayVo:{}", JSON.toJSONString(popBillPayVo));
        try {
            Long count = popBillPaymentDomainService.queryPopBillListCount(popBillPayVo);
            log.info("BillPaymentController.queryBillPaymentExportCount#popBillPayVo:{},count:{}", JSON.toJSONString(popBillPayVo), count);
            return ResponseVo.successResult(count);
        } catch (Exception e) {
            log.error("BillPaymentController.queryBillPaymentExportCount#异常 popBillPayVo:{}", JSON.toJSONString(popBillPayVo), e);
            return ResponseVo.errRest("查询入账单导出数量异常");
        }
    }

    /**
     * 查询入账单明细导出的条数  超过5000条数据，前端给出提示
     *
     * @param popBillPayVo
     * @return
     */
    @GetMapping(value = "/queryBillPaymentDetailExportCount")
    @ResponseBody
    public ResponseVo<Long> queryBillPaymentDetailExportCount(PopBillPayVo popBillPayVo) {
        log.info("BillPaymentController.queryBillPaymentDetailExportCount#popBillPayVo:{}", JSON.toJSONString(popBillPayVo));
        try {
            Long billPaymentDetailCount = popBillPaymentDomainService.queryExprotBillPaymentDetailCount(popBillPayVo);
            log.info("BillPaymentController.queryBillPaymentDetailExportCount#popBillPayVo:{},count:{}", JSON.toJSONString(popBillPayVo), billPaymentDetailCount);
            return ResponseVo.successResult(billPaymentDetailCount);
        } catch (Exception e) {
            log.error("BillPaymentController.queryBillPaymentDetailExportCount#异常 popBillPayVo:{}", JSON.toJSONString(popBillPayVo), e);
            return ResponseVo.errRest("查询入账单明细导出数量异常");
        }
    }

}
