/**
 * Copyright (C), 2015-2019,  武汉小药药医药科技有限公司
 * FileName: SmsTypeEnum
 * Author:   dell
 * Date:     2019/6/12 16:16
 * Description: 短信验证码类型枚举
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 〈一句话功能简述〉<br> 
 * 〈短信验证码类型枚举〉
 *
 * <AUTHOR>
 * @create 2019/6/12
 * @since 1.0.0
 */
public enum SmsTypeEnum {
    /** 注册 */
    REGISTER(0,"注册"),
    /** 忘记密码找回 */
    FORGET_PASSWORD(1,"忘记密码找回");

    private int type;
    private String name;

    SmsTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }
    private static Map<Integer, SmsTypeEnum> enumMaps = new HashMap<>();
    private static Map<String,SmsTypeEnum> getEnumMaps = new HashMap<>();
    static {
        for(SmsTypeEnum e : SmsTypeEnum.values()) {
            enumMaps.put(e.getType(), e);
            getEnumMaps.put(e.getName(),e);
        }
    }
    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static SmsTypeEnum getByType(int type){
        return enumMaps.get(type);
    }

}