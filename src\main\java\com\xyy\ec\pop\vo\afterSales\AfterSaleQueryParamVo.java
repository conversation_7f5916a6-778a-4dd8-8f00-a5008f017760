package com.xyy.ec.pop.vo.afterSales;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 退款售后
 */
@Data
public class AfterSaleQueryParamVo implements Serializable {
    private static final long serialVersionUID = -8241154846923131769L;
    /**
     * 订单编号
     */
    private String orderNo;
    private String popOrderId;
    private String orgId;
    /**
     * 售后编号
     */
    private String afterSalesNo;
    /**
     * 客户erp编码
     */
    private String customerErpCode;
    /**
     * 下单用户
     */
    private String merchantName;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 店铺名称
     */
    private String shopName;
    //多选
    private List<Integer> provinceCodes;
    //单选
    private Integer provinceCode;
    /**
     * 申请开始日期
     */
    private Long startCreateTime;
    /**
     * 申请结束日期
     */
    private Long endCreateTime;
    /**
     * 处理状态:0-等待商家确认、1-用户取消、2-商家已处理、3-申请被退回、4-待客户退回发票、5-客户已退回发票, 6-商家已补发
     */
    private Integer auditProcessState;
    private List<Integer> auditProcessStates;
    /**
     * 售后类型:1-资质售后；2-发票售后
     */
    private Integer afterSalesType;
    /**
     * 订单状态：2-配送中；3-已完成;91-已退款
     */
    private Integer orderStatus;
    /**
     * 售后原因：1-无票、2-错票、3-申请专票
     */
    private Integer subType;

    /**
     * 售后备注
     */
    private String remark;
    /**
     * 卖家备注
     */
    private String sellerRemark;
    /**
     * 页码
     */
    private Integer pageNo;
    /**
     * 每页大小
     */
    private Integer pageSize;

}
