package com.xyy.ec.pop.marketing.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarketingActivitySaleDataStatisticsInfoVO implements Serializable {

    /**
     * 采购店数
     */
    private Long purchaseMerchantNum;

    /**
     * 采购订单数
     */
    private Long purchaseOrderNum;

    /**
     * 采购数量
     */
    private Long purchaseProductNum;

    /**
     * 采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 采购店数
     */
    private Long unpaidPurchaseMerchantNum;

    /**
     * 采购订单数
     */
    private Long unpaidPurchaseOrderNum;

    /**
     * 采购数量
     */
    private Long unpaidPurchaseProductNum;

    /**
     * 采购金额
     */
    private BigDecimal unpaidPurchaseAmount;

    /**
     * 采购店数
     */
    private Long paidPurchaseMerchantNum;

    /**
     * 采购订单数
     */
    private Long paidPurchaseOrderNum;

    /**
     * 采购数量
     */
    private Long paidPurchaseProductNum;

    /**
     * 采购金额
     */
    private BigDecimal paidPurchaseAmount;
}
