package com.xyy.ec.pop.base;

/**
 * 返回结果状态枚举类
 */
public enum ResultCodeEnum {
    SUCCESS("1000", "success", "成功"),
    DEFAULT_ERROR("2000", "default_error", "【系统错误(2000)】"),
    PARAM_ERROR("3001", "param_error", "【参数错误(3001)】"),
    NOT_CONTINUOUS_CLICK("7002", "continuous_click", "【请勿重复操作】"),
    ;

    private String code;
    private String msg;
    private String displayMsg;

    private ResultCodeEnum(String code, String msg, String displayMsg) {
        this.code = code;
        this.msg = msg;
        this.displayMsg = displayMsg;
    }

    public String getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public String getDisplayMsg() {
        return this.displayMsg;
    }

    public void setDisplayMsg(String displayMsg) {
        this.displayMsg = displayMsg;
    }
}
