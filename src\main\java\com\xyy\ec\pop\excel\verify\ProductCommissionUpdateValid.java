package com.xyy.ec.pop.excel.verify;


import com.xyy.ec.pop.config.ProductCommissionUpdateConfig;
import com.xyy.ec.pop.server.api.product.dto.PopSkuCategoryDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.server.api.product.enums.ActivityTypeEnum;
import com.xyy.ec.pop.server.api.product.enums.PopHighGrossLabelEnum;
import com.xyy.ec.pop.vo.ProductCommissionBatchUpdateVo;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ProductCommissionUpdateValid {
    public static void trim(List<ProductCommissionBatchUpdateVo> vos) {
        vos.forEach(item->{
            item.setBarcode(StringUtils.trimToNull(item.getBarcode()));
            item.setCommissionRatio(StringUtils.trimToNull(item.getCommissionRatio()));
        });
    }

    public static void valid(List<ProductCommissionBatchUpdateVo> vos, ProductCommissionUpdateConfig config, Map<String, PopSkuDto> barcodeSkuMap, Map<String, PopSkuCategoryDto> activityCateMap, BigDecimal highGrossThreshold) {
        vos.forEach(item->{
            PopSkuDto skuDto = barcodeSkuMap.get(item.getBarcode());
            if(null == skuDto){
                item.setErrorMessage("未找到商品信息");
                return;
            }
            Integer activityType = skuDto.getActivityType();
            if (Objects.equals(activityType, ActivityTypeEnum.GIFT.getCode())) {
                item.setErrorMessage("商品为"+(ActivityTypeEnum.getOuterNameByCode(activityType))+"，请在表格里剔除这类商品后再提交");
                //如果拼团品，无需再往下校验
                return;
            }
            if(null == item.getBarcode()){
                item.setErrorMessage("未找到商品信息");
            }
            if(item.getCommissionRatio()==null){
                item.setErrorMessage("佣金比例为空");
            }else if(!item.getCommissionRatio().matches(config.getCommissionPattern())){
                item.setErrorMessage("0<佣金比例<100，仅允许录入整数");
            }else if (Objects.equals(activityType, ActivityTypeEnum.GROUP.getCode())) {
                PopSkuCategoryDto his = activityCateMap.get(item.getBarcode());
                if(his==null){
                    item.setErrorMessage("当前拼团商品不存在，若需变更活动商品佣金请先创建拼团活动");
                }
//                if(Objects.equals(PopHighGrossLabelEnum.STRICT_SELECT.getValue(), skuDto.getHighGross())){
//                    BigDecimal commissionRatio = new BigDecimal(item.getCommissionRatio());
//                    if(his==null){
//                        item.setErrorMessage("当前拼团商品不存在，若需变更活动商品佣金请先创建拼团活动");
//                    }else if(!his.isCommissionRatioSetted() && commissionRatio.compareTo(highGrossThreshold)>=0){
//                        item.setErrorMessage("商品为拼团商品且非高毛，佣金比例范围为0<佣金比例<"+ highGrossThreshold +"，仅允许录入整数");
//                    }
//                } else {
//                    if(his==null||!his.isCommissionRatioSetted()){
//                        item.setErrorMessage("当前拼团商品不存在，若需变更活动商品佣金请先创建拼团活动");
//                    }else if(new BigDecimal(item.getCommissionRatio()).compareTo(highGrossThreshold)<0){
//                        item.setErrorMessage("商品为拼团商品，佣金比例需≥"+highGrossThreshold);
//                    }
//                }
            }
        });
    }
}
