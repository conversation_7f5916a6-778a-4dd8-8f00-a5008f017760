package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.server.api.merchant.dto.BusinessCategoryCommissionDto;
import com.xyy.ec.pop.vo.BusinessCategoryCommissionVo;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 企业佣金比例
 * <AUTHOR>
 */
public class BusinessCategoryCommissionHelper {
    public static List<BusinessCategoryCommissionDto> convertToDto(List<BusinessCategoryCommissionVo> commissions, String orgId) {
        return commissions.stream().map(item->convertToDto(item,orgId)).collect(Collectors.toList());
    }

    private static BusinessCategoryCommissionDto convertToDto(BusinessCategoryCommissionVo vo, String orgId) {
        BusinessCategoryCommissionDto dto = new BusinessCategoryCommissionDto();
        dto.setId(vo.getId());
        dto.setCId(vo.getCId());
        dto.setOrgId(orgId);
        dto.setCategoryName(vo.getCategoryName());
        dto.setBusinessFirstCategoryCode(vo.getBusinessFirstCategoryCode());
        dto.setBusinessSecondCategoryCode(vo.getBusinessSecondCategoryCode());
        dto.setCommissionRatio(vo.getCommissionRatio());

        return dto;
    }
}
