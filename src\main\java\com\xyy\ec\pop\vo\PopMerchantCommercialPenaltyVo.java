package com.xyy.ec.pop.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date 2024/9/2 15:29
 */
@Data
// 头背景设置
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL, fillForegroundColor = 10)
// 头字体设置成20
@HeadFontStyle(fontHeightInPoints = 12)
public class PopMerchantCommercialPenaltyVo {
    /**
     * 商户编号
     */
    @ExcelProperty(value = "扣罚原因",index = 0)
    @ColumnWidth(value = 20)
    private String penaltyReason;

    /**
     * 商家名称
     */
    @ExcelProperty(value = "扣罚说明",index =1)
    @ColumnWidth(value = 20)
    private String description;

    /**
     * 商家名称
     */
    @ExcelProperty(value = "YBM单号",index = 2)
    @ColumnWidth(value = 20)
    private String orderNo;

    /**
     * 扣罚金额
     */
    @ExcelProperty(value = "扣罚金额",index = 3)
    @ColumnWidth(value = 20)
    private BigDecimal actualReceivedAmount;

    /**
     * 提现手续费
     */
    @ExcelProperty(value = "是否赔偿客户",index =4)
    @ColumnWidth(value = 20)
    private String isCompensateCustomer;
}
