package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.product.api.PopSkuMeProductApi;
import com.xyy.me.product.common.beans.ResponseInfo;
import com.xyy.me.product.general.api.dto.product.ProductDto;
import com.xyy.me.product.general.api.facade.product.ProductApi;
import com.xyy.me.product.general.api.vo.product.ProductQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/12
 */
@Slf4j
@Component
public class SkuMeRemote {
    @Reference(version = "1.0.0", registry = "me")
    private ProductApi productApi;
    @Reference
    private PopSkuMeProductApi skuMeProductApi;

    public Map<String,ProductDto> queryByApprovalNo(List<String> approvalNumbers) {
        try {
            log.info("SkuMeRemote.queryByApprovalNo#approvalNumber:{}", JSON.toJSONString(approvalNumbers));
            ProductQueryVo vo = new ProductQueryVo();
            vo.setApprovalNos(approvalNumbers);
            ResponseInfo<List<ProductDto>> res = productApi.listProduct(vo);
            log.info("SkuMeRemote.queryByApprovalNo#approvalNumber:{} return {}", JSON.toJSONString(approvalNumbers), JSON.toJSONString(res));
            //辅助输入4级分类，查不到返回空
            if (res.isSuccess() && !CollectionUtils.isEmpty(res.getData())) {
                return res.getData().stream().collect(Collectors.toMap(item->item.getApprovalNo(),item->item,(item1,item2)->item1));
            }
            if(res.isFailure()){
                log.error("SkuMeRemote.queryByApprovalNo#approvalNumber:{} 查询结果异常", JSON.toJSONString(approvalNumbers));
            }
            return new HashMap<>(0);
        } catch (Exception e) {
            log.error("SkuMeRemote.queryByApprovalNo#approvalNumber:{} 异常", JSON.toJSONString(approvalNumbers), e);
            return new HashMap<>(0);
        }
    }
    
    public Integer getSpuCategory(Integer firstErpCode,String approvalNumber){
        try {
            log.info("SkuMeRemote.getSpuCategory#firstErpCode:{},approvalNumber:{}", firstErpCode,approvalNumber);
            ApiRPCResult<Integer> result = skuMeProductApi.getReportSkuCate(firstErpCode, approvalNumber);
            log.info("SkuMeRemote.getSpuCategory#firstErpCode:{},approvalNumber:{} return {}", firstErpCode, approvalNumber, JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("SkuMeRemote.getSpuCategory#firstErpCode:{},approvalNumber:{} 异常", firstErpCode,approvalNumber, e);
        }
        return null;
    }
}
