package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.product.api.admin.SkuSyncApi;
import com.xyy.ec.pop.server.api.product.dto.SyncSkuDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @version v1
 * @Description 商品同步任务
 * <AUTHOR>
 */
@Service
@Slf4j
public class SkuSyncApiRemote {
    @Reference
    private SkuSyncApi skuSyncApi;

    public SyncSkuDto next() {
        try {
            log.info("SkuSyncApiImpl.next");
            ApiRPCResult<SyncSkuDto> result = skuSyncApi.next();
            log.info("SkuSyncApiImpl.next return {}", JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("SkuSyncApiImpl.next 异常", e);
            return null;
        }
    }

    public boolean updateStatus(Long id, int status, Long current) {
        try {
            log.info("SkuSyncApiImpl.updateStatus#id:{},status:{},current:{}", id, status, current);
            ApiRPCResult<Boolean> result = skuSyncApi.updateStatus(id,status,current);
            log.info("SkuSyncApiImpl.updateStatus#id:{},status:{},current:{} return {}", id, status, current, JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : false;
        } catch (Exception e) {
            log.error("SkuSyncApiImpl.updateStatus#id:{},status:{},current:{} 异常", id, status, current, e);
            return false;
        }
    }
}
