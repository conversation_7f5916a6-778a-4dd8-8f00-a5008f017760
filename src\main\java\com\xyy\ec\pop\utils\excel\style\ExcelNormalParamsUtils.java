package com.xyy.ec.pop.utils.excel.style;

import cn.afterturn.easypoi.entity.vo.NormalExcelConstants;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.List;
import java.util.Map;

/**
 * excel参数工具
 * Created by danshiyu on 2019/11/4.
 */
public class ExcelNormalParamsUtils{
    public static final String PATTERN_STANDARD = "yyyy-MM-dd HH:mm:ss";

    public static final String PATTERN_TIMESTAMP = "yyyyMMddHHmmss";
    /**
     * 普通excel通用参数设置
     * @param map
     * @param fileName
     * @param exportEntity
     * @param data
     */
    public static <T> void buildNormalParams(Map map,String fileName, Class<T> exportEntity, List data){
        ExportParams params = new ExportParams();
        params.setSheetName(fileName);
        params.setType(ExcelType.XSSF);
        params.setStyle(ExcelExportCommonStyle.class);
        params.setColor(IndexedColors.BLUE_GREY.index);
        params.setFreezeCol(2);
        // 数据集合
        map.put(NormalExcelConstants.DATA_LIST, data);
        //导出实体
        map.put(NormalExcelConstants.CLASS, exportEntity);
        //参数
        map.put(NormalExcelConstants.PARAMS, params);
        //文件名称
        map.put(NormalExcelConstants.FILE_NAME, String.format("%s%s",fileName, DateFormatUtils.format(System.currentTimeMillis(), PATTERN_STANDARD)));
    }
}
