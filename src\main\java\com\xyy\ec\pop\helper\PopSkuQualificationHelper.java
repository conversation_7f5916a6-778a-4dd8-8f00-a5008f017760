package com.xyy.ec.pop.helper;

import com.google.common.collect.Lists;
import com.xyy.ec.pop.server.api.product.dto.PopSkuQualificationDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuQualificationsDetailDto;
import com.xyy.ec.pop.vo.PopSkuQualificationVo;
import com.xyy.ec.pop.vo.PopSkuQualificationsDetailVo;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

public class PopSkuQualificationHelper {
    public static PopSkuQualificationVo popSkuQualificationDto2Vo(PopSkuQualificationDto dto){
        if(null == dto){
            return null;
        }
        PopSkuQualificationVo vo = new PopSkuQualificationVo();
        vo.setId(dto.getId());
        vo.setBarcode(dto.getBarcode());
        vo.setName(dto.getName());
        vo.setCode(dto.getCode());
        vo.setStartDate(dto.getStartDate());
        vo.setEndDate(dto.getEndDate());
        vo.setUrl(dto.getUrl());
        vo.setRemarks(dto.getRemarks());
        vo.setState(dto.getState());
        vo.setDel(dto.getDel());
        vo.setCreateTime(dto.getCreateTime());
        vo.setCreateId(dto.getCreateId());
        vo.setCreateName(dto.getCreateName());
        vo.setUpdateTime(dto.getUpdateTime());
        vo.setUpdateId(dto.getUpdateId());
        vo.setUpdateName(dto.getUpdateName());
        vo.setQualificationsDetailId(dto.getQualificationsDetailId());
        return vo;
    }

    public static List<PopSkuQualificationVo> popSkuQualificationDto2VoList(List<PopSkuQualificationDto> dtoList){
        if (CollectionUtils.isEmpty(dtoList)){
            return Lists.newArrayList();
        }
        List<PopSkuQualificationVo> voList = dtoList.stream().map(dto->popSkuQualificationDto2Vo(dto))
                .collect(Collectors.toList());
        return voList;
    }

    public static PopSkuQualificationsDetailVo popSkuQualificationsDetailDto2Vo(PopSkuQualificationsDetailDto dto){
        if (null == dto){
            return null;
        }
        PopSkuQualificationsDetailVo vo = new PopSkuQualificationsDetailVo();
        vo.setId(dto.getId());
        vo.setName(dto.getName());
        vo.setCode(dto.getCode());
        vo.setMaxImg(dto.getMaxImg());
        vo.setRemark(dto.getRemark());
        vo.setIsNeed(dto.getIsNeed());
        vo.setIsNeedCode(dto.getIsNeedCode());
        vo.setIsNeedBank(dto.getIsNeedBank());
        vo.setIsNeedTime(dto.getIsNeedTime());
        vo.setWithLongTime(dto.getWithLongTime());
        vo.setCreator(dto.getCreator());
        vo.setCreateTime(dto.getCreateTime());
        vo.setUpdator(dto.getUpdator());
        vo.setUpdateTime(dto.getUpdateTime());
        return vo;
    }
    public static List<PopSkuQualificationsDetailVo> popSkuQualificationsDetailDto2VoList(List<PopSkuQualificationsDetailDto> dtoList){
        if (CollectionUtils.isEmpty(dtoList)){
            return Lists.newArrayList();
        }
        List<PopSkuQualificationsDetailVo> voList = dtoList.stream().map(dto->popSkuQualificationsDetailDto2Vo(dto))
                .collect(Collectors.toList());
        return voList;
    }
}
