package com.xyy.ec.pop.marketing.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.elephant.api.ElephantMarketingGroupBuyingApi;
import com.xyy.ec.marketing.elephant.params.MarketingGroupBuyingListExportPagingQueryParam;
import com.xyy.ec.marketing.elephant.params.MarketingGroupBuyingListQueryParam;
import com.xyy.ec.marketing.elephant.params.MarketingGroupBuyingStatisticsQueryParam;
import com.xyy.ec.marketing.elephant.params.MarketingGroupBuyingStatusCountQueryParam;
import com.xyy.ec.marketing.elephant.results.MarketingGroupBuyingInfoDTO;
import com.xyy.ec.marketing.elephant.results.MarketingGroupBuyingStatisticsDTO;
import com.xyy.ec.marketing.elephant.results.MarketingGroupBuyingStatusCountDTO;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupBaseInfoDTO;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupDTO;
import com.xyy.ec.pop.config.AppCdnProperties;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.marketing.config.MarketingAppProperties;
import com.xyy.ec.pop.marketing.constants.MarketingConstants;
import com.xyy.ec.pop.marketing.dto.*;
import com.xyy.ec.pop.marketing.easyexcel.rows.GroupBuyingBatchOfflineByActIdExcelRow;
import com.xyy.ec.pop.marketing.easyexcel.rows.GroupBuyingBatchOfflineByReportIdExcelRow;
import com.xyy.ec.pop.marketing.enums.GroupBuyingBatchOfflineTypeEnum;
import com.xyy.ec.pop.marketing.helpers.*;
import com.xyy.ec.pop.marketing.param.GroupBuyingListExportParam;
import com.xyy.ec.pop.marketing.param.GroupBuyingListQueryParam;
import com.xyy.ec.pop.marketing.param.GroupBuyingStatusCountQueryParam;
import com.xyy.ec.pop.marketing.remote.MarketingTopicRemoteService;
import com.xyy.ec.pop.marketing.service.FileUploadService;
import com.xyy.ec.pop.marketing.service.GroupBuyingService;
import com.xyy.ec.pop.marketing.service.InsightService;
import com.xyy.ec.pop.marketing.vo.GroupBuyingInfoVO;
import com.xyy.ec.pop.marketing.vo.GroupBuyingStatisticsVO;
import com.xyy.ec.pop.marketing.vo.GroupBuyingStatusCountVO;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.redis.impl.RedisService;
import com.xyy.ec.pop.remote.*;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.utils.easyexcel.EasyExcelDataAnalysisEventListener;
import com.xyy.ec.pop.utils.easyexcel.EasyExcelDataProcessor;
import com.xyy.ec.pop.utils.easyexcel.EasyExcelRowDataWrapper;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuDTO;
import com.xyy.ec.shop.server.business.results.ShopCodeAndNameDTO;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import com.xyy.ms.promotion.business.api.admin.MarketingGroupBuyingAdminApi;
import com.xyy.ms.promotion.business.dto.marketingactivity.MarketingTopicDTO;
import com.xyy.ms.promotion.business.params.MarketingGroupBuyingOfflineByActIdsBatchParam;
import com.xyy.ms.promotion.business.params.MarketingGroupBuyingOfflineByReportIdsBatchParam;
import com.xyy.ms.promotion.business.result.MarketingGroupBuyingBatchOfflineFailureDTO;
import com.xyy.ms.promotion.business.result.MarketingGroupBuyingBatchOfflineResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GroupBuyingServiceImpl implements GroupBuyingService {

    @Autowired
    private MarketingAppProperties marketingAppProperties;

    @Resource
    private RedisService redisService;

    @Reference(version = "1.0.0")
    private ElephantMarketingGroupBuyingApi elephantMarketingGroupBuyingApi;

    @Autowired
    private CrmShopPoiRelationRemoteService crmShopPoiRelationRemoteService;

    @Autowired
    private CorporationRemote corporationRemote;

    @Autowired
    private ShopAdminRemoteService shopAdminRemoteService;

    @Autowired
    private ProductCsuForOtherSystemRemoteService productCsuForOtherSystemRemoteService;

    @Autowired
    private MarketingTopicRemoteService marketingTopicRemoteService;

    @Autowired
    private InsightChosenCustomerRemoteService insightChosenCustomerRemoteService;

    @Autowired
    private DownloadRemote downloadRemote;

    @Reference(version = "1.0.0")
    private MarketingGroupBuyingAdminApi marketingGroupBuyingAdminApi;

    @Autowired
    private AppCdnProperties appCdnProperties;

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private InsightService insightService;

    @Override
    public GroupBuyingStatisticsVO getStatistics(String userOaId) {
        // 获取用户关联的店铺
        List<String> shopCodes = crmShopPoiRelationRemoteService.listShopCodesByOaId(userOaId);
        if (CollectionUtils.isEmpty(shopCodes)) {
            return GroupBuyingStatisticsVO.builder().platformSubsidy(0).build();
        }
        MarketingGroupBuyingStatisticsQueryParam queryParam = MarketingGroupBuyingStatisticsQueryParam.builder()
                .shopCodes(shopCodes).build();
        ApiRPCResult<MarketingGroupBuyingStatisticsDTO> apiRPCResult = elephantMarketingGroupBuyingApi.getStatisticsInfo(queryParam);
        if (log.isDebugEnabled()) {
            log.debug("ElephantMarketingGroupBuyingApi.getStatisticsInfo 入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
        }
        if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
            log.error("ElephantMarketingGroupBuyingApi.getStatisticsInfo 查询失败，入参：{}，出参：{}",
                    JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "查询失败，请稍后重试！");
        }
        return GroupBuyingStatisticsVOHelper.create(apiRPCResult.getData());
    }

    @Override
    public PageInfo<GroupBuyingInfoVO> paging(String userOaId, GroupBuyingListQueryParam queryParam) {
        GroupBuyingListQueryParamHelper.trimIfNecessary(queryParam);
        // 参数校验
        Boolean isSuccess = GroupBuyingListQueryParamHelper.validate(queryParam);
        if (BooleanUtils.isNotTrue(isSuccess)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        PageInfo<GroupBuyingInfoVO> pageInfo = new PageInfo<>(Lists.newArrayList());
        pageInfo.setPages(0);
        pageInfo.setTotal(0L);
        pageInfo.setPageNum(queryParam.getPageNum());
        pageInfo.setPageSize(queryParam.getPageSize());
        // 获取用户关联的店铺
        List<String> shopCodes = this.listUserShopCodesByQueryParam(userOaId, queryParam.getOrgId(),
                queryParam.getOrgName(), queryParam.getShopName());
        if (CollectionUtils.isEmpty(shopCodes)) {
            return pageInfo;
        }
        MarketingGroupBuyingListQueryParam marketingGroupBuyingListQueryParam = MarketingGroupBuyingListQueryParamHelper.create(queryParam);
        if (Objects.isNull(marketingGroupBuyingListQueryParam)) {
            return pageInfo;
        }
        marketingGroupBuyingListQueryParam.setShopCodes(shopCodes);
        ApiRPCResult<PageInfo<MarketingGroupBuyingInfoDTO>> apiRPCResult = elephantMarketingGroupBuyingApi.paging(marketingGroupBuyingListQueryParam);
        if (log.isDebugEnabled()) {
            log.debug("ElephantMarketingGroupBuyingApi.paging 入参：{}，出参：{}",
                    JSONObject.toJSONString(marketingGroupBuyingListQueryParam), JSONObject.toJSONString(apiRPCResult));
        }
        if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
            log.error("ElephantMarketingGroupBuyingApi.paging 查询失败，入参：{}，出参：{}",
                    JSONObject.toJSONString(marketingGroupBuyingListQueryParam), JSONObject.toJSONString(apiRPCResult));
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "查询失败，请稍后重试！");
        }
        PageInfo<MarketingGroupBuyingInfoDTO> elephantPageInfo = apiRPCResult.getData();
        List<GroupBuyingInfoVO> groupBuyingInfoVOS = GroupBuyingInfoVOHelper.creates(elephantPageInfo.getList());
        GroupBuyingListQueryContext context = GroupBuyingListQueryContext.builder()
                .csuIdToInfoMap(Maps.newHashMap()).build();
        // 填充商品信息
        this.fillProductInfo(groupBuyingInfoVOS, context);
        // 填充商户名称
        this.fillOrgName(groupBuyingInfoVOS);
        // 填充店铺名称
        this.fillShopName(groupBuyingInfoVOS);
        // 填充主题名称
        this.fillTopicName(groupBuyingInfoVOS);
        // 重设批团剩余活动库存
        this.resetSurplusStockNum(groupBuyingInfoVOS, context);
        // 填充拼团准入价
        this.fillAccessGroupPrice(groupBuyingInfoVOS, context);
        // 为了与页面逻辑保持一致，重设是否复制原品销售范围和人群ID。
        this.resetIsCopySaleAreaAndCustomerGroupId(groupBuyingInfoVOS, context);
        // 填充人群名称
        this.fillCustomerGroupName(groupBuyingInfoVOS);
        pageInfo.setPageNum(elephantPageInfo.getPageNum());
        pageInfo.setPageSize(elephantPageInfo.getPageSize());
        pageInfo.setTotal(elephantPageInfo.getTotal());
        pageInfo.setPages(elephantPageInfo.getPages());
        pageInfo.setList(groupBuyingInfoVOS);
        return pageInfo;
    }

    /**
     * 填充商品信息
     *
     * @param groupBuyingInfoVOS
     */
    private void fillProductInfo(List<GroupBuyingInfoVO> groupBuyingInfoVOS, GroupBuyingListQueryContext context) {
        if (CollectionUtils.isEmpty(groupBuyingInfoVOS)) {
            return;
        }
        List<Long> csuIds = groupBuyingInfoVOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCsuId()))
                .map(GroupBuyingInfoVO::getCsuId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(csuIds)) {
            return;
        }
        List<CsuDTO> csuDTOS = productCsuForOtherSystemRemoteService.listCsuInfosByCsuIds(csuIds);
        if (CollectionUtils.isEmpty(csuDTOS)) {
            return;
        }
        Map<Long, CsuDTO> csuIdToInfoMap = csuDTOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getId()))
                .collect(Collectors.toMap(CsuDTO::getId, Function.identity(), (s, f) -> s));
        if (MapUtils.isEmpty(csuIdToInfoMap)) {
            return;
        }
        context.setCsuIdToInfoMap(csuIdToInfoMap);
        groupBuyingInfoVOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCsuId()))
                .forEach(item -> {
                    CsuDTO csuDTO = csuIdToInfoMap.get(item.getCsuId());
                    if (Objects.nonNull(csuDTO)) {
                        item.setOrgId(csuDTO.getOrgId());
                        String productCode = Objects.equals(csuDTO.getIsThirdCompany(), 1) ? csuDTO.getErpCode() : csuDTO.getProductCode();
                        item.setProductCode(productCode);
                        item.setProductName(csuDTO.getProductName());
                        item.setSpec(csuDTO.getSpec());
                        item.setManufacturer(csuDTO.getManufacturer());
                    }
                });
    }

    /**
     * 填充商户名称
     *
     * @param groupBuyingInfoVOS
     */
    private void fillOrgName(List<GroupBuyingInfoVO> groupBuyingInfoVOS) {
        if (CollectionUtils.isEmpty(groupBuyingInfoVOS)) {
            return;
        }
        List<String> orgIds = groupBuyingInfoVOS.stream()
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getOrgId()))
                .map(GroupBuyingInfoVO::getOrgId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgIds)) {
            return;
        }
        List<CorporationDto> corporations = this.listCorporationsByOrgIds(orgIds);
        if (CollectionUtils.isEmpty(corporations)) {
            return;
        }
        Map<String, String> orgIdToOrgNameMap = corporations.stream()
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getOrgId())
                        && StringUtils.isNotEmpty(item.getCompanyName()))
                .collect(Collectors.toMap(CorporationDto::getOrgId, CorporationDto::getCompanyName, (s, f) -> s));
        if (MapUtils.isEmpty(orgIdToOrgNameMap)) {
            return;
        }
        groupBuyingInfoVOS.stream()
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getOrgId()))
                .forEach(item -> {
                    String orgName = orgIdToOrgNameMap.getOrDefault(item.getOrgId(), "");
                    item.setOrgName(orgName);
                });
    }

    /**
     * 获取商品信息
     *
     * @param orgIds
     * @return
     */
    private List<CorporationDto> listCorporationsByOrgIds(List<String> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Lists.newArrayList();
        }
        orgIds = Lists.newArrayList(Sets.newHashSet(orgIds));
        if (CollectionUtils.isEmpty(orgIds)) {
            return Lists.newArrayList();
        }
        List<CorporationDto> result = Lists.newArrayListWithExpectedSize(orgIds.size());
        List<List<String>> orgIdsLists = Lists.partition(orgIds, 100);
        List<CorporationDto> corporations;
        for (List<String> orgIdsList : orgIdsLists) {
            corporations = corporationRemote.queryCorpBaseByOrgIds(orgIdsList);
            if (CollectionUtils.isEmpty(corporations)) {
                continue;
            }
            result.addAll(corporations);
        }
        if (log.isDebugEnabled()) {
            log.debug("【listCorporationsByOrgIds】CorporationRemote.queryCorpBaseByOrgIds，orgIds：{}，result：{}",
                    JSONArray.toJSONString(orgIds), JSONArray.toJSONString(result));
        }
        return result;
    }

    /**
     * 填充店铺名称
     *
     * @param groupBuyingInfoVOS
     */
    private void fillShopName(List<GroupBuyingInfoVO> groupBuyingInfoVOS) {
        if (CollectionUtils.isEmpty(groupBuyingInfoVOS)) {
            return;
        }
        List<String> shopCodes = groupBuyingInfoVOS.stream()
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getShopCode()))
                .map(GroupBuyingInfoVO::getShopCode)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopCodes)) {
            return;
        }
        Collection<ShopCodeAndNameDTO> shopCodeAndNameDTOS = shopAdminRemoteService.queryShopCodeAndNamesByShopCodes(shopCodes);
        if (CollectionUtils.isEmpty(shopCodeAndNameDTOS)) {
            return;
        }
        Map<String, String> shopCodeToShopNameMap = shopCodeAndNameDTOS.stream()
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getShopCode())
                        && StringUtils.isNotEmpty(item.getShowName()))
                .collect(Collectors.toMap(ShopCodeAndNameDTO::getShopCode, ShopCodeAndNameDTO::getShowName, (s, f) -> s));
        if (MapUtils.isEmpty(shopCodeToShopNameMap)) {
            return;
        }
        groupBuyingInfoVOS.stream()
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getShopCode()))
                .forEach(item -> {
                    String shopName = shopCodeToShopNameMap.getOrDefault(item.getShopCode(), "");
                    item.setShopName(shopName);
                });
    }

    /**
     * 填充主题名称
     *
     * @param groupBuyingInfoVOS
     */
    private void fillTopicName(List<GroupBuyingInfoVO> groupBuyingInfoVOS) {
        if (CollectionUtils.isEmpty(groupBuyingInfoVOS)) {
            return;
        }
        // 获取全部的主题列表
        List<MarketingTopicDTO> marketingTopicDTOS = marketingTopicRemoteService.listAllTopic();
        if (CollectionUtils.isEmpty(marketingTopicDTOS)) {
            return;
        }
        Map<String, String> topicNameToTitleMap = marketingTopicDTOS.stream()
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getName())
                        && StringUtils.isNotEmpty(item.getTitle()))
                .collect(Collectors.toMap(MarketingTopicDTO::getName, MarketingTopicDTO::getTitle, (s, f) -> s));
        if (MapUtils.isEmpty(topicNameToTitleMap)) {
            return;
        }
        groupBuyingInfoVOS.stream()
                .filter(item -> Objects.nonNull(item) && CollectionUtils.isNotEmpty(item.getTopicList()))
                .forEach(item -> {
                    List<String> topicList = item.getTopicList();
                    List<String> topicTitles = topicList.stream()
                            .map(topicName -> topicNameToTitleMap.get(topicName))
                            .filter(StringUtils::isNotEmpty)
                            .collect(Collectors.toList());
                    item.setTopicNames(String.join("、", topicTitles));
                });
    }

    /**
     * 重设批团剩余活动库存
     *
     * @param groupBuyingInfoVOS
     * @param context
     */
    private void resetSurplusStockNum(List<GroupBuyingInfoVO> groupBuyingInfoVOS, GroupBuyingListQueryContext context) {
        if (CollectionUtils.isEmpty(groupBuyingInfoVOS)) {
            return;
        }
        Map<Long, CsuDTO> csuIdToInfoMap = context.getCsuIdToInfoMap();
        Long csuId;
        CsuDTO csuDTO;
        Integer availableQty;
        Integer totalLimitNum;
        Integer surplusStockNum;
        for (GroupBuyingInfoVO groupBuyingInfoVO : groupBuyingInfoVOS) {
            csuId = groupBuyingInfoVO.getCsuId();
            csuDTO = csuIdToInfoMap.get(csuId);
            if (Objects.isNull(csuDTO) || Objects.isNull(csuDTO.getAvailableQty())) {
                continue;
            }
            availableQty = csuDTO.getAvailableQty();
            totalLimitNum = groupBuyingInfoVO.getTotalLimitNum();
            surplusStockNum = groupBuyingInfoVO.getSurplusStockNum();
            if (Objects.nonNull(totalLimitNum)) {
                // 限购时，使用活动剩余库存与商品可用库存取小
                surplusStockNum = Objects.isNull(surplusStockNum) ? 0 : surplusStockNum;
                surplusStockNum = Math.min(surplusStockNum, availableQty);
            } else {
                // 不限购，则使用商品可用库存
                surplusStockNum = availableQty;
            }
            groupBuyingInfoVO.setSurplusStockNum(surplusStockNum);
        }
    }

    /**
     * 填充拼团准入价
     *
     * @param groupBuyingInfoVOS
     * @param context
     */
    private void fillAccessGroupPrice(List<GroupBuyingInfoVO> groupBuyingInfoVOS, GroupBuyingListQueryContext context) {
        if (CollectionUtils.isEmpty(groupBuyingInfoVOS)) {
            return;
        }
        Map<Long, CsuDTO> csuIdToInfoMap = context.getCsuIdToInfoMap();
        Long csuId;
        CsuDTO csuDTO;
        boolean isPopCsu;
        for (GroupBuyingInfoVO groupBuyingInfoVO : groupBuyingInfoVOS) {
            csuId = groupBuyingInfoVO.getCsuId();
            csuDTO = csuIdToInfoMap.get(csuId);
            if (Objects.isNull(csuDTO)) {
                continue;
            }
            isPopCsu = this.isPopCsu(csuDTO);
            if (isPopCsu) {
                groupBuyingInfoVO.setAccessGroupPrice(groupBuyingInfoVO.getPopAccessGroupPrice());
            } else {
                groupBuyingInfoVO.setAccessGroupPrice(groupBuyingInfoVO.getEcAccessGroupPrice());
            }
        }
    }

    /**
     * <pre>
     * 为了与页面逻辑保持一致，重设是否复制原品销售范围和人群ID。
     * 与页面逻辑一致：
     * if (isCopyCsuModel === 2 || (isCopyCsuModel === 1 && isCopySaleArea === 2)) {
     *    是否复制原品销售范围：否
     *    人群ID:  XXXXXX
     *    人群定义: yyyyyy
     * } else {
     *    是否复制原品销售范围：是
     * }
     * </pre>
     *
     * @param groupBuyingInfoVOS
     * @param context
     */
    private void resetIsCopySaleAreaAndCustomerGroupId(List<GroupBuyingInfoVO> groupBuyingInfoVOS, GroupBuyingListQueryContext context) {
        if (CollectionUtils.isEmpty(groupBuyingInfoVOS)) {
            return;
        }
        Map<Long, CsuDTO> csuIdToInfoMap = context.getCsuIdToInfoMap();
        Long csuId;
        CsuDTO csuDTO;
        boolean isPopCsu;
        for (GroupBuyingInfoVO groupBuyingInfoVO : groupBuyingInfoVOS) {
            csuId = groupBuyingInfoVO.getCsuId();
            csuDTO = csuIdToInfoMap.get(csuId);
            if (Objects.isNull(csuDTO)) {
                continue;
            }
            isPopCsu = this.isPopCsu(csuDTO);
            if (isPopCsu) {
                if (Objects.equals(groupBuyingInfoVO.getIsCopyCsuModel(), 1)
                        && Objects.equals(groupBuyingInfoVO.getIsCopySaleArea(), 1)) {
                    // 置空人群ID
                    groupBuyingInfoVO.setCustomerGroupId(null);
                }
            } else {
                groupBuyingInfoVO.setIsCopySaleArea(2);
            }
        }
    }

    /**
     * 是否是POP商品
     *
     * @param csuDTO
     * @return
     */
    private boolean isPopCsu(CsuDTO csuDTO) {
        if (Objects.isNull(csuDTO)) {
            return false;
        }
        return Objects.equals(csuDTO.getIsThirdCompany(), 1);
    }

    /**
     * 填充人群名称
     *
     * @param groupBuyingInfoVOS
     */
    private void fillCustomerGroupName(List<GroupBuyingInfoVO> groupBuyingInfoVOS) {
        if (CollectionUtils.isEmpty(groupBuyingInfoVOS)) {
            return;
        }
        List<Long> customerGroupIds = groupBuyingInfoVOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCustomerGroupId())
                        && item.getCustomerGroupId() > 0)
                .map(GroupBuyingInfoVO::getCustomerGroupId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerGroupIds)) {
            return;
        }
        List<MarketCustomerGroupDTO> customerGroupBaseInfoDTOS = insightChosenCustomerRemoteService
                .mgetChoseCustomer(customerGroupIds);
        if (CollectionUtils.isEmpty(customerGroupBaseInfoDTOS)) {
            return;
        }
        Map<Long, String> customerGroupIdToNameMap = customerGroupBaseInfoDTOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getId()) && StringUtils.isNotEmpty(item.getGroupName()))
                .collect(Collectors.toMap(MarketCustomerGroupDTO::getId, MarketCustomerGroupDTO::getGroupName, (s, f) -> s));
        if (MapUtils.isEmpty(customerGroupIdToNameMap)) {
            return;
        }
        groupBuyingInfoVOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCustomerGroupId())
                        && item.getCustomerGroupId() > 0)
                .forEach(item -> {
                    String groupName = customerGroupIdToNameMap.getOrDefault(item.getCustomerGroupId(), "");
                    item.setCustomerGroupName(groupName);
                });

        Map<Long, MarketCustomerGroupDTO> customerGroupInfoDTOMap = customerGroupBaseInfoDTOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getId()) && StringUtils.isNotEmpty(item.getGroupName()))
                .collect(Collectors.toMap(MarketCustomerGroupDTO::getId, Function.identity(), (s, f) -> s));
        if (MapUtils.isEmpty(customerGroupInfoDTOMap)) {
            return;
        }
        groupBuyingInfoVOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCustomerGroupId())
                        && item.getCustomerGroupId() > 0)
                .forEach(item -> {
                    MarketCustomerGroupDTO marketCustomerGroupInfoDTO = customerGroupInfoDTOMap.get(item.getCustomerGroupId());
                    item.setTagDef(insightService.getContentBundleDescriptions(marketCustomerGroupInfoDTO));
                });
    }

    @Override
    public GroupBuyingStatusCountVO getStatusCount(String userOaId, GroupBuyingStatusCountQueryParam queryParam) {
        GroupBuyingStatusCountQueryParamHelper.trimIfNecessary(queryParam);
        // 参数校验
        Boolean isSuccess = GroupBuyingStatusCountQueryParamHelper.validate(queryParam);
        if (BooleanUtils.isNotTrue(isSuccess)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        GroupBuyingStatusCountVO groupBuyingStatusCountVO = GroupBuyingStatusCountVO.builder().totalCount(0)
                .waitCount(0).rejectedCount(0).unStartCount(0).startingCount(0).stopOrOffLineCount(0).build();
        // 获取用户关联的店铺
        List<String> shopCodes = this.listUserShopCodesByQueryParam(userOaId, queryParam.getOrgId(),
                queryParam.getOrgName(), queryParam.getShopName());
        if (CollectionUtils.isEmpty(shopCodes)) {
            return groupBuyingStatusCountVO;
        }
        MarketingGroupBuyingStatusCountQueryParam marketingGroupBuyingListQueryParam = MarketingGroupBuyingStatusCountQueryParamHelper.create(queryParam);
        if (Objects.isNull(marketingGroupBuyingListQueryParam)) {
            return groupBuyingStatusCountVO;
        }
        marketingGroupBuyingListQueryParam.setShopCodes(shopCodes);
        ApiRPCResult<MarketingGroupBuyingStatusCountDTO> apiRPCResult = elephantMarketingGroupBuyingApi.getStatusCountInfo(marketingGroupBuyingListQueryParam);
        if (log.isDebugEnabled()) {
            log.debug("ElephantMarketingGroupBuyingApi.getStatusCountInfo 入参：{}，出参：{}",
                    JSONObject.toJSONString(marketingGroupBuyingListQueryParam), JSONObject.toJSONString(apiRPCResult));
        }
        if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
            log.error("ElephantMarketingGroupBuyingApi.getStatusCountInfo 查询失败，入参：{}，出参：{}",
                    JSONObject.toJSONString(marketingGroupBuyingListQueryParam), JSONObject.toJSONString(apiRPCResult));
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "查询失败，请稍后重试！");
        }
        return GroupBuyingStatusCountVOHelper.create(apiRPCResult.getData());
    }

    @Override
    public void asyncExport(SysUser user, GroupBuyingListExportParam queryParam) {
        String redisCacheKey = null;
        Boolean isHaveLock = false;
        try {
            // 导出频次限制
            redisCacheKey = "marketingGroupBuying:popAdmin:export_" + user.getId();
            // 最多锁60秒
            isHaveLock = redisService.setNx(redisCacheKey, StringUtils.EMPTY, 60);
            if (BooleanUtils.isNotTrue(isHaveLock)) {
                throw new PopAdminException(XyyJsonResultCodeEnum.EXPORT_FREQUENCY_LIMIT, "您目前有正在导出的任务，请等待其完成后再进行导出！");
            }
            log.info("拼团活动提报导出频次，标记：{}", redisCacheKey);
            // 导出
            GroupBuyingListExportParamHelper.trimIfNecessary(queryParam);
            // 参数校验
            Boolean isSuccess = GroupBuyingListExportParamHelper.validate(queryParam);
            if (BooleanUtils.isNotTrue(isSuccess)) {
                String msg = "参数非法";
                throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }
            // 获取用户关联的店铺
            List<String> shopCodes = this.listUserShopCodesByQueryParam(user.getOaId(), queryParam.getOrgId(),
                    queryParam.getOrgName(), queryParam.getShopName());
            if (CollectionUtils.isEmpty(shopCodes)) {
                String msg = "导出失败，没有可导出的数据！";
                throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }
            MarketingGroupBuyingListExportPagingQueryParam exportQueryParam = MarketingGroupBuyingListExportPagingQueryParamHelper
                    .create(queryParam);
            exportQueryParam.setShopCodes(shopCodes);
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(exportQueryParam)
                    .operator(user.getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.ADMIN_MARKETING_GROUP_BUYING_LIST)
                    .build();
            try {
                Boolean asyncResult = downloadRemote.saveTask(content);
                if (BooleanUtils.isNotTrue(asyncResult)) {
                    log.error("导出失败，DownloadRemote.saveTask，content：{}", JSONObject.toJSONString(content));
                    throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "导出失败，请稍后重试！");
                }
            } catch (ServiceRuntimeException e) {
                throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, e.getMessage());
            }
        } finally {
            if (StringUtils.isNotEmpty(redisCacheKey) && BooleanUtils.isTrue(isHaveLock)) {
                // 释放锁
                try {
                    redisService.deleteKey(redisCacheKey);
                } catch (Exception e) {
                    log.error("释放导出频次锁失败，redisCacheKey：{}", redisCacheKey, e);
                }
            }
        }
    }

    /**
     * 根据查询参数获取用户有权限的店铺编码列表
     *
     * @param userOaId
     * @param orgId
     * @param orgName
     * @param shopName
     * @return
     */
    private List<String> listUserShopCodesByQueryParam(String userOaId, String orgId, String orgName, String shopName) {
        // 获取用户关联的店铺
        List<String> shopCodes = crmShopPoiRelationRemoteService.listShopCodesByOaId(userOaId);
        if (log.isDebugEnabled()) {
            log.debug("【listUserShopCodesByQueryParam】CrmShopPoiRelationRemoteService.listShopCodesByOaId，userOaId：{}，shopCodes：{}",
                    userOaId, JSONArray.toJSONString(shopCodes));
        }
        if (CollectionUtils.isEmpty(shopCodes)) {
            return Lists.newArrayList();
        }
        if (StringUtils.isEmpty(orgId) && StringUtils.isEmpty(orgName) && StringUtils.isEmpty(shopName)) {
            return shopCodes;
        }
        // 查询店铺信息
        Collection<ShopInfoDTO> shopInfoDTOS = shopAdminRemoteService.queryShopInfoByShopCodes(shopCodes);
        if (log.isDebugEnabled()) {
            log.debug("【listUserShopCodesByQueryParam】ShopAdminRemoteService.queryShopInfoByShopCodes，shopCodes：{}，shopInfoDTOS：{}",
                    JSONArray.toJSONString(shopCodes), JSONArray.toJSONString(shopInfoDTOS));
        }
        if (CollectionUtils.isEmpty(shopInfoDTOS)) {
            return Lists.newArrayList();
        }
        // 店铺名称，文本框，模糊查询
        if (StringUtils.isNotEmpty(shopName)) {
            shopInfoDTOS = shopInfoDTOS.stream()
                    .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getShowName())
                            && item.getShowName().contains(shopName))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shopInfoDTOS)) {
                return Lists.newArrayList();
            }
        }
        // 商户编号，文本框，精确查询
        if (StringUtils.isNotEmpty(orgId)) {
            shopInfoDTOS = shopInfoDTOS.stream()
                    .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getOrgId())
                            && Objects.equals(orgId, item.getOrgId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shopInfoDTOS)) {
                return Lists.newArrayList();
            }
        }
        // 商户名称，文本框，模糊查询
        if (StringUtils.isNotEmpty(orgName)) {
            List<String> orgIds = shopInfoDTOS.stream()
                    .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getOrgId()))
                    .map(ShopInfoDTO::getOrgId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orgIds)) {
                return Lists.newArrayList();
            }
            List<CorporationDto> corporations = this.listCorporationsByOrgIds(orgIds);
            if (CollectionUtils.isEmpty(corporations)) {
                return Lists.newArrayList();
            }
            Set<String> orgIdsSet = corporations.stream()
                    .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getCompanyName())
                            && item.getCompanyName().contains(orgName))
                    .map(CorporationDto::getOrgId)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(orgIdsSet)) {
                return Lists.newArrayList();
            }
            shopInfoDTOS = shopInfoDTOS.stream()
                    .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getOrgId())
                            && orgIdsSet.contains(item.getOrgId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shopInfoDTOS)) {
                return Lists.newArrayList();
            }
        }
        return shopInfoDTOS.stream()
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getShopCode()))
                .map(ShopInfoDTO::getShopCode)
                .collect(Collectors.toList());
    }

    @Override
    public GroupBuyingBatchOfflineResultDTO batchOffline(SysUser user, Integer type, MultipartFile excelFile) {
        // 方法参数校验
        if (Objects.isNull(type) || Objects.isNull(GroupBuyingBatchOfflineTypeEnum.valueOfCustom(type))) {
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "请选择下线方式！");
        }
        if (Objects.isNull(excelFile)) {
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "请选择Excel文件！");
        }
        if (Objects.equals(type, GroupBuyingBatchOfflineTypeEnum.BY_ACT_ID.getType())) {
            return this.batchOfflineByActId(user, excelFile);
        }
        if (Objects.equals(type, GroupBuyingBatchOfflineTypeEnum.BY_REPORT_ID.getType())) {
            return this.batchOfflineByReportId(user, excelFile);
        }
        return null;
    }

    private GroupBuyingBatchOfflineResultDTO batchOfflineByActId(SysUser user, MultipartFile excelFile) {
        // 整体权限校验
        String userOaId = user.getOaId();
        // 获取用户关联的店铺
        List<String> userShopCodes = crmShopPoiRelationRemoteService.listShopCodesByOaId(userOaId);
        if (log.isDebugEnabled()) {
            log.debug("【batchOfflineByActId】CrmShopPoiRelationRemoteService.listShopCodesByOaId，userOaId：{}，userShopCodes：{}",
                    userOaId, JSONArray.toJSONString(userShopCodes));
        }
        if (CollectionUtils.isEmpty(userShopCodes)) {
            throw new PopAdminException(XyyJsonResultCodeEnum.AUTHORITY_ERROR, "批量下线失败，没有店铺数据权限！");
        }
        Set<String> userShopCodeSet = Sets.newHashSet(userShopCodes);
        Integer totalExcelRowNum = null;
        String readTimeStr = null;
        String checkTimeStr = null;
        String saveTimeStr = null;
        String uploadTimeStr = null;
        String totalTimeStr = null;
        String operatorEmail = user.getEmail();
        try {
            StopWatch totalStopWatch = new StopWatch();
            totalStopWatch.start();
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            // 表头校验

            /* 解析Excel */
            // 表头
            Map<Integer, String> headMaps = Maps.newHashMapWithExpectedSize(16);
            // 行数据
            List<EasyExcelRowDataWrapper<GroupBuyingBatchOfflineByActIdExcelRow>> excelRows = Lists.newArrayList();
            // 读取Excel
            EasyExcel.read(excelFile.getInputStream(), GroupBuyingBatchOfflineByActIdExcelRow.class,
                    new EasyExcelDataAnalysisEventListener<GroupBuyingBatchOfflineByActIdExcelRow>(new EasyExcelDataProcessor<GroupBuyingBatchOfflineByActIdExcelRow>() {
                        @Override
                        public void process(List<EasyExcelRowDataWrapper<GroupBuyingBatchOfflineByActIdExcelRow>> rows, AnalysisContext context) {
                            if (CollectionUtils.isNotEmpty(rows)) {
                                excelRows.addAll(rows);
                            }
                        }

                        @Override
                        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                            Integer rowIndex = context.readRowHolder().getRowIndex();
                            if (Objects.equals(rowIndex, 0) && MapUtils.isNotEmpty(headMap)) {
                                headMaps.putAll(headMap);
                            }
                        }
                    })).sheet().headRowNumber(1).doRead();
            stopWatch.stop();
            readTimeStr = stopWatch.toString();
            // Excel 行数量上限限制
            Integer batchOfflineGroupBuyingImportExcelMaxRowNum = marketingAppProperties.getBatchOfflineGroupBuyingImportExcelMaxRowNum();
            totalExcelRowNum = excelRows.size();
            if (totalExcelRowNum > batchOfflineGroupBuyingImportExcelMaxRowNum) {
                String excelRowNumLimitFailureMsg = MessageFormat.format("单次批量下线的最大可接受行数为{0}，当前文件有{1}行，请拆分文件进行多次批量创建",
                        String.valueOf(batchOfflineGroupBuyingImportExcelMaxRowNum), String.valueOf(totalExcelRowNum));
                GroupBuyingBatchOfflineResultDTO groupBuyingBatchOfflineResultDTO = GroupBuyingBatchOfflineResultDTO.builder()
                        .excelRowNumLimitFlag(true)
                        .excelRowNumLimitFailureMsg(excelRowNumLimitFailureMsg)
                        .build();
                log.info("根据活动ID批量下线条数受限，总条数：{}；解析总耗时：{}", totalExcelRowNum, readTimeStr);
                return groupBuyingBatchOfflineResultDTO;
            }
            stopWatch.reset();
            stopWatch.start();
            /* 数据内容及业务规则校验 */
            GroupBuyingBatchOfflineContext context = GroupBuyingBatchOfflineContext.builder()
                    .userShopCodeSet(userShopCodeSet).operator(user)
                    .build();
            List<GroupBuyingBatchOfflineByActIdRowDTO> rows = checkGroupBuyingBatchOfflineByActIdExcelRows(excelRows, context);
            // 校验成功的行
            List<GroupBuyingBatchOfflineByActIdRowDTO> checkSuccessRows = rows.stream()
                    .filter(item -> item != null && BooleanUtils.isTrue(item.getCheckResult()))
                    .collect(Collectors.toList());
            // 校验失败的行
            List<GroupBuyingBatchOfflineByActIdRowDTO> checkFailureRows = rows.stream()
                    .filter(item -> item != null && BooleanUtils.isNotTrue(item.getCheckResult()))
                    .collect(Collectors.toList());
            stopWatch.stop();
            checkTimeStr = stopWatch.toString();
            /* 批量创建 */
            stopWatch.reset();
            stopWatch.start();
            if (CollectionUtils.isNotEmpty(checkSuccessRows)) {
                /* 保存 */
                // 是否保存失败
                boolean isFailure;
                // 失败原因
                String failureReason;
                List<Long> actIds = checkSuccessRows.stream()
                        .map(GroupBuyingBatchOfflineByActIdRowDTO::getActId)
                        .distinct().collect(Collectors.toList());
                List<List<Long>> actIdsLists = Lists.partition(actIds, 20);
                for (List<Long> actIdsList : actIdsLists) {
                    isFailure = false;
                    failureReason = "";
                    MarketingGroupBuyingOfflineByActIdsBatchParam batchParam = MarketingGroupBuyingOfflineByActIdsBatchParam.builder()
                            .actIds(actIdsList)
                            .userShopCodeSet(userShopCodeSet)
                            .operateUserId(user.getId())
                            .operateUsername(user.getUsername())
                            .operateUserRealName(user.getRealName())
                            .build();
                    try {
                        ApiRPCResult<MarketingGroupBuyingBatchOfflineResultDTO> apiRPCResult = marketingGroupBuyingAdminApi.batchOfflineByActIds(batchParam);
                        if (log.isDebugEnabled()) {
                            log.debug("marketingGroupBuyingAdminApi.batchOfflineByActIds，入参：{}，出参：{}",
                                    JSONObject.toJSONString(batchParam), JSONObject.toJSONString(apiRPCResult));
                        }
                        if (apiRPCResult.isSuccess() && Objects.nonNull(apiRPCResult.getData())) {
                            MarketingGroupBuyingBatchOfflineResultDTO batchOfflineResultDTO = apiRPCResult.getData();
                            List<Long> successIds = batchOfflineResultDTO.getSuccessIds();
                            List<MarketingGroupBuyingBatchOfflineFailureDTO> failures = batchOfflineResultDTO.getFailures();
                            Set<Long> successIdSet = Sets.newHashSet(successIds);
                            Map<Long, MarketingGroupBuyingBatchOfflineFailureDTO> failureIdToInfoMap = failures.stream()
                                    .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getFailureId()))
                                    .collect(Collectors.toMap(MarketingGroupBuyingBatchOfflineFailureDTO::getFailureId, Function.identity(), (s, f) -> s));
                            Set<Long> actIdsSet = Sets.newHashSet(actIdsList);
                            for (GroupBuyingBatchOfflineByActIdRowDTO row : checkSuccessRows) {
                                // 找出本批次操作的行
                                if (!actIdsSet.contains(row.getActId())) {
                                    continue;
                                }
                                if (successIdSet.contains(row.getActId())) {
                                    row.setOptResult(true);
                                } else {
                                    MarketingGroupBuyingBatchOfflineFailureDTO failureInfo = failureIdToInfoMap.get(row.getActId());
                                    String finalFailureReason = Objects.isNull(failureInfo) ? "" : failureInfo.getFailureReason();
                                    row.setOptResult(false);
                                    List<String> failureReasonItems = row.getFailureReasonItems();
                                    failureReasonItems.add(finalFailureReason);
                                }
                            }
                        } else {
                            log.error("导入Excel根据活动ID批量下线拼团活动失败，operatorEmail：{}，batchParam：{}，响应信息：{}",
                                    operatorEmail, JSONObject.toJSONString(batchParam), JSONObject.toJSONString(apiRPCResult));
                            isFailure = true;
                            failureReason = "下线失败，失败原因：" + apiRPCResult.getMsg();
                        }
                    } catch (Exception e) {
                        log.error("导入Excel根据活动ID批量下线拼团活动异常，operatorEmail：{}，batchParam：{}",
                                operatorEmail, JSONObject.toJSONString(batchParam), e);
                        isFailure = true;
                        failureReason = "下线失败，失败原因：" + e.getMessage();
                    }
                    if (BooleanUtils.isTrue(isFailure)) {
                        String finalFailureReason = failureReason;
                        Set<Long> actIdSet = Sets.newHashSet(actIdsList);
                        for (GroupBuyingBatchOfflineByActIdRowDTO row : checkSuccessRows) {
                            if (actIdSet.contains(row.getActId())) {
                                row.setOptResult(false);
                                List<String> failureReasonItems = row.getFailureReasonItems();
                                failureReasonItems.add(finalFailureReason);
                            }
                        }
                    }
                }
            }
            stopWatch.stop();
            saveTimeStr = stopWatch.toString();
            // 操作失败的行
            List<GroupBuyingBatchOfflineByActIdRowDTO> optFailureRows = checkSuccessRows.stream()
                    .filter(item -> BooleanUtils.isNotTrue(item.getOptResult())).collect(Collectors.toList());
            /* 将错误行生成到Excel中 */
            List<GroupBuyingBatchOfflineByActIdRowDTO> failureRows = Lists.newArrayListWithExpectedSize(16);
            if (CollectionUtils.isNotEmpty(checkFailureRows)) {
                failureRows.addAll(checkFailureRows);
            }
            if (CollectionUtils.isNotEmpty(optFailureRows)) {
                failureRows.addAll(optFailureRows);
            }
            stopWatch.reset();
            stopWatch.start();
            String failureExcelFileDownloadUrl = "";
            if (CollectionUtils.isNotEmpty(failureRows)) {
                failureExcelFileDownloadUrl = this.uploadBatchOfflineByActIdFailureExcel(failureRows, context);
            }
            stopWatch.stop();
            uploadTimeStr = stopWatch.toString();
            int successNum = checkSuccessRows.size() - optFailureRows.size();
            int failureNum = failureRows.size();
            GroupBuyingBatchOfflineResultDTO groupBuyingBatchOfflineResultDTO = GroupBuyingBatchOfflineResultDTO.builder()
                    .excelRowNumLimitFlag(false)
                    .successNum(successNum)
                    .failureNum(failureNum)
                    .failureExcelFileDownloadUrl(failureExcelFileDownloadUrl)
                    .build();
            totalStopWatch.stop();
            totalTimeStr = totalStopWatch.toString();
            log.info("根据活动ID批量下线拼团活动成功，operatorEmail：{}，总条数：{}，成功条数：{}，失败条数：{}，失败Excel文件：{}；" +
                            "总耗时：{}，解析总耗时：{}，校验总耗时：{}，保存总耗时：{}，上传失败文件总耗时：{}",
                    operatorEmail, totalExcelRowNum, successNum, failureNum, failureExcelFileDownloadUrl,
                    totalTimeStr, readTimeStr, checkTimeStr, saveTimeStr, uploadTimeStr);
            return groupBuyingBatchOfflineResultDTO;
        } catch (Exception e) {
            log.error("根据活动ID批量下线拼团活动失败，operatorEmail：{}，总条数：{}；总耗时：{}，解析总耗时：{}，校验总耗时：{}，保存总耗时：{}，上传失败文件总耗时：{}",
                    operatorEmail, totalExcelRowNum, totalTimeStr, readTimeStr, checkTimeStr, saveTimeStr, uploadTimeStr, e);
            throw new PopAdminException(e, XyyJsonResultCodeEnum.FAIL, "批量下线失败，请稍后重试！");
        }
    }

    /**
     * 批量下线拼团时，校验Excel行
     *
     * @param easyExcelRowDataWrappers
     * @param context
     * @return
     */
    private List<GroupBuyingBatchOfflineByActIdRowDTO> checkGroupBuyingBatchOfflineByActIdExcelRows(List<EasyExcelRowDataWrapper<GroupBuyingBatchOfflineByActIdExcelRow>> easyExcelRowDataWrappers,
                                                                                                    GroupBuyingBatchOfflineContext context) {
        if (CollectionUtils.isEmpty(easyExcelRowDataWrappers)) {
            return Lists.newArrayList();
        }
        Set<String> userShopCodeSet = context.getUserShopCodeSet();
        if (Objects.isNull(userShopCodeSet)) {
            userShopCodeSet = Sets.newHashSet();
            context.setUserShopCodeSet(userShopCodeSet);
        }
        if (log.isDebugEnabled()) {
            log.debug("简单校验开始，easyExcelRowDataWrappers：{}，context：{}", JSONArray.toJSONString(easyExcelRowDataWrappers), JSONObject.toJSONString(context));
        }
        List<GroupBuyingBatchOfflineByActIdRowDTO> rows = easyExcelRowDataWrappers.stream()
                .map(item -> {
                    GroupBuyingBatchOfflineByActIdRowDTO row = simpleCheckGroupBuyingBatchOfflineByActIdExcelRow(item, context);
                    return row;
                })
                .collect(Collectors.toList());
        if (log.isDebugEnabled()) {
            log.debug("简单校验结束，rows：{}，context：{}", JSONArray.toJSONString(rows), JSONObject.toJSONString(context));
        }
        return rows;
    }

    /**
     * <pre>
     * 简单校验。
     * 数据必填项、数据本身合法性（是否是数字、格式等）。
     * </pre>
     *
     * @param excelRowEasyExcelRowDataWrapper
     * @param context
     * @return 返回校验后的行
     */
    private GroupBuyingBatchOfflineByActIdRowDTO simpleCheckGroupBuyingBatchOfflineByActIdExcelRow(EasyExcelRowDataWrapper<GroupBuyingBatchOfflineByActIdExcelRow> excelRowEasyExcelRowDataWrapper,
                                                                                                   GroupBuyingBatchOfflineContext context) {
        if (Objects.isNull(excelRowEasyExcelRowDataWrapper)) {
            return null;
        }
        Set<String> userShopCodeSet = context.getUserShopCodeSet();
        SysUser operator = context.getOperator();
        Integer sheetNo = excelRowEasyExcelRowDataWrapper.getSheetNo();
        String sheetName = excelRowEasyExcelRowDataWrapper.getSheetName();
        Integer rowIndex = excelRowEasyExcelRowDataWrapper.getRowIndex();
        GroupBuyingBatchOfflineByActIdExcelRow excelRow = excelRowEasyExcelRowDataWrapper.getRowData();
        GroupBuyingBatchOfflineByActIdRowDTO rowDTO = new GroupBuyingBatchOfflineByActIdRowDTO();
        rowDTO.setExcelRowIndex(rowIndex);
        rowDTO.setExcelRow(excelRow);
        List<String> failureReasonItems = Lists.newArrayListWithExpectedSize(16);
        String tempFailureReasonItem = null;
        if (excelRow != null) {
            /* 活动id */
            {
                boolean actIdCheckResult = true;
                String actIdStr = excelRow.getActIdStr();
                Long actId = null;
                if (StringUtils.isNotEmpty(actIdStr)) {
                    actIdStr = actIdStr.trim();
                }
                if (StringUtils.isEmpty(actIdStr)) {
                    actIdCheckResult = false;
                } else {
                    try {
                        actId = Long.parseLong(actIdStr);
                        if (actId <= 0L) {
                            actIdCheckResult = false;
                        }
                    } catch (Exception e) {
                        actIdCheckResult = false;
                    }
                }
                if (!actIdCheckResult) {
                    tempFailureReasonItem = "活动ID为必填项且为正整数";
                    failureReasonItems.add(tempFailureReasonItem);
                }
                rowDTO.setActIdCheckResult(actIdCheckResult);
                if (actIdCheckResult) {
                    rowDTO.setActId(actId);
                }
            }
        } else {
            tempFailureReasonItem = "当前行没有数据";
            failureReasonItems.add(tempFailureReasonItem);
            rowDTO.setActIdCheckResult(false);
        }
        rowDTO.setFailureReasonItems(failureReasonItems);
        return rowDTO;
    }

    /**
     * 上传根据活动ID批量下线拼团活动失败Excel
     *
     * @param rows
     * @param context
     * @return
     */
    private String uploadBatchOfflineByActIdFailureExcel(List<GroupBuyingBatchOfflineByActIdRowDTO> rows, GroupBuyingBatchOfflineContext context) {
        ByteArrayOutputStream byteArrayOutputStream = null;
        InputStream inputStream = null;
        String operatorEmail = null;
        try {
            operatorEmail = context.getOperator().getEmail();
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
            // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
            HSSFSheet sheet = hssfWorkbook.createSheet("sheet");
            // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
            HSSFRow row = sheet.createRow(0);
            // 第四步，创建单元格，并设置值表头 设置表头居中
            HSSFCellStyle style = hssfWorkbook.createCellStyle();
            //水平居中
            style.setAlignment(HorizontalAlignment.CENTER_SELECTION);
            //垂直居中
            style.setVerticalAlignment(VerticalAlignment.TOP);
            //绿色背景
            style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.GREEN.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setFillBackgroundColor(HSSFColor.HSSFColorPredefined.YELLOW.getIndex());
            // 设置边框
            style.setBorderBottom(BorderStyle.THIN);
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);
            style.setBorderTop(BorderStyle.THIN);
            // 自动换行
            style.setWrapText(true);
            HSSFFont font = hssfWorkbook.createFont();
            font.setFontHeightInPoints((short) 10);
            //红色字体配绿色背景就是这么和谐
            font.setColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
            font.setFontName("宋体");
            // 把字体 应用到当前样式
            style.setFont(font);
            //声明列对象
            HSSFCell cell = null;
            //创建表头
            String headerStr = "活动ID*,失败原因";
            String[] headers = headerStr.split(",");
            for (int i = 0; i < headers.length; i++) {
                cell = row.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(style);
                if (Objects.equals(i, headers.length - 1)) {
                    sheet.setColumnWidth(i, 3766 * 6);
                } else {
                    sheet.setColumnWidth(i, 3766);
                }
            }
            int i = 0;
            for (GroupBuyingBatchOfflineByActIdRowDTO rowDTO : rows) {
                // 从第二行开始
                i++;
                row = sheet.createRow(i);
                row.setHeight((short) 400);
                GroupBuyingBatchOfflineByActIdExcelRow excelRow = rowDTO.getExcelRow();
                // 活动ID
                row.createCell(0).setCellValue(excelRow.getActIdStr());
                // 失败原因
                List<String> failureReasonItems = rowDTO.getFailureReasonItems();
                row.createCell(1).setCellValue(String.join("；\n", failureReasonItems));
            }
            byteArrayOutputStream = new ByteArrayOutputStream();
            hssfWorkbook.write(byteArrayOutputStream);
            byte[] brray = byteArrayOutputStream.toByteArray();
            inputStream = new ByteArrayInputStream(brray);
            // 8位随机码数字
            StringBuilder randomStr = new StringBuilder();
            for (int j = 1; j <= 8; j++) {
                randomStr.append(RandomUtils.nextInt(0, 10));
            }
            String filename = MessageFormat.format("批量下线拼团活动{0}{1}.xls",
                    DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"), randomStr.toString());
            // 注释：上传的错误文件统一由marketing-admin服务的清理文件任务清理。
            String relativePath = fileUploadService.uploadFile(inputStream, filename, MarketingConstants.FTP_MODULE_DIR_MARKETING_POP_ADMIN_GROUP_BUYING_BATCH_OFFLINE_FAILURE);
            log.info("上传根据活动ID批量下线拼团活动失败Excel响应，operatorEmail：{}，rows'Size：{}，relativePath：{}",
                    operatorEmail, rows.size(), relativePath);
            if (StringUtils.isEmpty(relativePath)) {
                return null;
            }
            return appCdnProperties.getDomain() + relativePath;
        } catch (Exception e) {
            log.error("上传根据活动ID批量下线拼团活动失败Excel异常，operatorEmail：{}，rows：{}",
                    operatorEmail, JSONArray.toJSONString(rows), e);
            return null;
        } finally {
            // 其实这里不需要关闭，本着对流的敬畏的理念，显示关闭一下。
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                }
            }
            if (byteArrayOutputStream != null) {
                try {
                    byteArrayOutputStream.close();
                } catch (Exception e) {
                }
            }
        }
    }

    private GroupBuyingBatchOfflineResultDTO batchOfflineByReportId(SysUser user, MultipartFile excelFile) {
        // 整体权限校验
        String userOaId = user.getOaId();
        // 获取用户关联的店铺
        List<String> userShopCodes = crmShopPoiRelationRemoteService.listShopCodesByOaId(userOaId);
        if (log.isDebugEnabled()) {
            log.debug("【batchOfflineByReportId】CrmShopPoiRelationRemoteService.listShopCodesByOaId，userOaId：{}，userShopCodes：{}",
                    userOaId, JSONArray.toJSONString(userShopCodes));
        }
        if (CollectionUtils.isEmpty(userShopCodes)) {
            throw new PopAdminException(XyyJsonResultCodeEnum.AUTHORITY_ERROR, "批量下线失败，没有店铺数据权限！");
        }
        Set<String> userShopCodeSet = Sets.newHashSet(userShopCodes);
        Integer totalExcelRowNum = null;
        String readTimeStr = null;
        String checkTimeStr = null;
        String saveTimeStr = null;
        String uploadTimeStr = null;
        String totalTimeStr = null;
        String operatorEmail = user.getEmail();
        try {
            StopWatch totalStopWatch = new StopWatch();
            totalStopWatch.start();
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            // 表头校验

            /* 解析Excel */
            // 表头
            Map<Integer, String> headMaps = Maps.newHashMapWithExpectedSize(16);
            // 行数据
            List<EasyExcelRowDataWrapper<GroupBuyingBatchOfflineByReportIdExcelRow>> excelRows = Lists.newArrayList();
            // 读取Excel
            EasyExcel.read(excelFile.getInputStream(), GroupBuyingBatchOfflineByReportIdExcelRow.class,
                    new EasyExcelDataAnalysisEventListener<GroupBuyingBatchOfflineByReportIdExcelRow>(new EasyExcelDataProcessor<GroupBuyingBatchOfflineByReportIdExcelRow>() {
                        @Override
                        public void process(List<EasyExcelRowDataWrapper<GroupBuyingBatchOfflineByReportIdExcelRow>> rows, AnalysisContext context) {
                            if (CollectionUtils.isNotEmpty(rows)) {
                                excelRows.addAll(rows);
                            }
                        }

                        @Override
                        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                            Integer rowIndex = context.readRowHolder().getRowIndex();
                            if (Objects.equals(rowIndex, 0) && MapUtils.isNotEmpty(headMap)) {
                                headMaps.putAll(headMap);
                            }
                        }
                    })).sheet().headRowNumber(1).doRead();
            stopWatch.stop();
            readTimeStr = stopWatch.toString();
            // Excel 行数量上限限制
            Integer batchOfflineGroupBuyingImportExcelMaxRowNum = marketingAppProperties.getBatchOfflineGroupBuyingImportExcelMaxRowNum();
            totalExcelRowNum = excelRows.size();
            if (totalExcelRowNum > batchOfflineGroupBuyingImportExcelMaxRowNum) {
                String excelRowNumLimitFailureMsg = MessageFormat.format("单次批量下线的最大可接受行数为{0}，当前文件有{1}行，请拆分文件进行多次批量创建",
                        String.valueOf(batchOfflineGroupBuyingImportExcelMaxRowNum), String.valueOf(totalExcelRowNum));
                GroupBuyingBatchOfflineResultDTO groupBuyingBatchOfflineResultDTO = GroupBuyingBatchOfflineResultDTO.builder()
                        .excelRowNumLimitFlag(true)
                        .excelRowNumLimitFailureMsg(excelRowNumLimitFailureMsg)
                        .build();
                log.info("根据提报ID批量下线条数受限，总条数：{}；解析总耗时：{}", totalExcelRowNum, readTimeStr);
                return groupBuyingBatchOfflineResultDTO;
            }
            stopWatch.reset();
            stopWatch.start();
            /* 数据内容及业务规则校验 */
            GroupBuyingBatchOfflineContext context = GroupBuyingBatchOfflineContext.builder()
                    .userShopCodeSet(userShopCodeSet).operator(user)
                    .build();
            List<GroupBuyingBatchOfflineByReportIdRowDTO> rows = checkGroupBuyingBatchOfflineByReportIdExcelRows(excelRows, context);
            // 校验成功的行
            List<GroupBuyingBatchOfflineByReportIdRowDTO> checkSuccessRows = rows.stream()
                    .filter(item -> item != null && BooleanUtils.isTrue(item.getCheckResult()))
                    .collect(Collectors.toList());
            // 校验失败的行
            List<GroupBuyingBatchOfflineByReportIdRowDTO> checkFailureRows = rows.stream()
                    .filter(item -> item != null && BooleanUtils.isNotTrue(item.getCheckResult()))
                    .collect(Collectors.toList());
            stopWatch.stop();
            checkTimeStr = stopWatch.toString();
            /* 批量创建 */
            stopWatch.reset();
            stopWatch.start();
            if (CollectionUtils.isNotEmpty(checkSuccessRows)) {
                /* 保存 */
                // 是否保存失败
                boolean isFailure;
                // 失败原因
                String failureReason;
                List<Long> reportIds = checkSuccessRows.stream()
                        .map(GroupBuyingBatchOfflineByReportIdRowDTO::getReportId)
                        .distinct().collect(Collectors.toList());
                List<List<Long>> reportIdsLists = Lists.partition(reportIds, 20);
                for (List<Long> reportIdsList : reportIdsLists) {
                    isFailure = false;
                    failureReason = "";
                    MarketingGroupBuyingOfflineByReportIdsBatchParam batchParam = MarketingGroupBuyingOfflineByReportIdsBatchParam.builder()
                            .reportIds(reportIdsList)
                            .userShopCodeSet(userShopCodeSet)
                            .operateUserId(user.getId())
                            .operateUsername(user.getUsername())
                            .operateUserRealName(user.getRealName())
                            .build();
                    try {
                        ApiRPCResult<MarketingGroupBuyingBatchOfflineResultDTO> apiRPCResult = marketingGroupBuyingAdminApi.batchOfflineByReportIds(batchParam);
                        if (log.isDebugEnabled()) {
                            log.debug("marketingGroupBuyingAdminApi.batchOfflineByReportIds，入参：{}，出参：{}",
                                    JSONObject.toJSONString(batchParam), JSONObject.toJSONString(apiRPCResult));
                        }
                        if (apiRPCResult.isSuccess() && Objects.nonNull(apiRPCResult.getData())) {
                            MarketingGroupBuyingBatchOfflineResultDTO batchOfflineResultDTO = apiRPCResult.getData();
                            List<Long> successIds = batchOfflineResultDTO.getSuccessIds();
                            List<MarketingGroupBuyingBatchOfflineFailureDTO> failures = batchOfflineResultDTO.getFailures();
                            Set<Long> successIdSet = Sets.newHashSet(successIds);
                            Map<Long, MarketingGroupBuyingBatchOfflineFailureDTO> failureIdToInfoMap = failures.stream()
                                    .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getFailureId()))
                                    .collect(Collectors.toMap(MarketingGroupBuyingBatchOfflineFailureDTO::getFailureId, Function.identity(), (s, f) -> s));
                            Set<Long> reportIdsSet = Sets.newHashSet(reportIdsList);
                            for (GroupBuyingBatchOfflineByReportIdRowDTO row : checkSuccessRows) {
                                // 找出本批次操作的行
                                if (!reportIdsSet.contains(row.getReportId())) {
                                    continue;
                                }
                                if (successIdSet.contains(row.getReportId())) {
                                    row.setOptResult(true);
                                } else {
                                    MarketingGroupBuyingBatchOfflineFailureDTO failureInfo = failureIdToInfoMap.get(row.getReportId());
                                    String finalFailureReason = Objects.isNull(failureInfo) ? "" : failureInfo.getFailureReason();
                                    row.setOptResult(false);
                                    List<String> failureReasonItems = row.getFailureReasonItems();
                                    failureReasonItems.add(finalFailureReason);
                                }
                            }
                        } else {
                            log.error("导入Excel根据提报ID批量下线拼团活动失败，operatorEmail：{}，batchParam：{}，响应信息：{}",
                                    operatorEmail, JSONObject.toJSONString(batchParam), JSONObject.toJSONString(apiRPCResult));
                            isFailure = true;
                            failureReason = "下线失败，失败原因：" + apiRPCResult.getMsg();
                        }
                    } catch (Exception e) {
                        log.error("导入Excel根据提报ID批量下线拼团活动异常，operatorEmail：{}，batchParam：{}",
                                operatorEmail, JSONObject.toJSONString(batchParam), e);
                        isFailure = true;
                        failureReason = "下线失败，失败原因：" + e.getMessage();
                    }
                    if (BooleanUtils.isTrue(isFailure)) {
                        String finalFailureReason = failureReason;
                        Set<Long> reportIdSet = Sets.newHashSet(reportIdsList);
                        for (GroupBuyingBatchOfflineByReportIdRowDTO row : checkSuccessRows) {
                            if (reportIdSet.contains(row.getReportId())) {
                                row.setOptResult(false);
                                List<String> failureReasonItems = row.getFailureReasonItems();
                                failureReasonItems.add(finalFailureReason);
                            }
                        }
                    }
                }
            }
            stopWatch.stop();
            saveTimeStr = stopWatch.toString();
            // 操作失败的行
            List<GroupBuyingBatchOfflineByReportIdRowDTO> optFailureRows = checkSuccessRows.stream()
                    .filter(item -> BooleanUtils.isNotTrue(item.getOptResult())).collect(Collectors.toList());
            /* 将错误行生成到Excel中 */
            List<GroupBuyingBatchOfflineByReportIdRowDTO> failureRows = Lists.newArrayListWithExpectedSize(16);
            if (CollectionUtils.isNotEmpty(checkFailureRows)) {
                failureRows.addAll(checkFailureRows);
            }
            if (CollectionUtils.isNotEmpty(optFailureRows)) {
                failureRows.addAll(optFailureRows);
            }
            stopWatch.reset();
            stopWatch.start();
            String failureExcelFileDownloadUrl = "";
            if (CollectionUtils.isNotEmpty(failureRows)) {
                failureExcelFileDownloadUrl = this.uploadBatchOfflineByReportIdFailureExcel(failureRows, context);
            }
            stopWatch.stop();
            uploadTimeStr = stopWatch.toString();
            int successNum = checkSuccessRows.size() - optFailureRows.size();
            int failureNum = failureRows.size();
            GroupBuyingBatchOfflineResultDTO groupBuyingBatchOfflineResultDTO = GroupBuyingBatchOfflineResultDTO.builder()
                    .excelRowNumLimitFlag(false)
                    .successNum(successNum)
                    .failureNum(failureNum)
                    .failureExcelFileDownloadUrl(failureExcelFileDownloadUrl)
                    .build();
            totalStopWatch.stop();
            totalTimeStr = totalStopWatch.toString();
            log.info("根据提报ID批量下线拼团活动成功，operatorEmail：{}，总条数：{}，成功条数：{}，失败条数：{}，失败Excel文件：{}；" +
                            "总耗时：{}，解析总耗时：{}，校验总耗时：{}，保存总耗时：{}，上传失败文件总耗时：{}",
                    operatorEmail, totalExcelRowNum, successNum, failureNum, failureExcelFileDownloadUrl,
                    totalTimeStr, readTimeStr, checkTimeStr, saveTimeStr, uploadTimeStr);
            return groupBuyingBatchOfflineResultDTO;
        } catch (Exception e) {
            log.error("根据提报ID批量下线拼团活动失败，operatorEmail：{}，总条数：{}；总耗时：{}，解析总耗时：{}，校验总耗时：{}，保存总耗时：{}，上传失败文件总耗时：{}",
                    operatorEmail, totalExcelRowNum, totalTimeStr, readTimeStr, checkTimeStr, saveTimeStr, uploadTimeStr, e);
            throw new PopAdminException(e, XyyJsonResultCodeEnum.FAIL, "批量下线失败，请稍后重试！");
        }
    }

    /**
     * 批量下线拼团时，校验Excel行
     *
     * @param easyExcelRowDataWrappers
     * @param context
     * @return
     */
    private List<GroupBuyingBatchOfflineByReportIdRowDTO> checkGroupBuyingBatchOfflineByReportIdExcelRows(List<EasyExcelRowDataWrapper<GroupBuyingBatchOfflineByReportIdExcelRow>> easyExcelRowDataWrappers,
                                                                                                          GroupBuyingBatchOfflineContext context) {
        if (CollectionUtils.isEmpty(easyExcelRowDataWrappers)) {
            return Lists.newArrayList();
        }
        Set<String> userShopCodeSet = context.getUserShopCodeSet();
        if (Objects.isNull(userShopCodeSet)) {
            userShopCodeSet = Sets.newHashSet();
            context.setUserShopCodeSet(userShopCodeSet);
        }
        if (log.isDebugEnabled()) {
            log.debug("简单校验开始，easyExcelRowDataWrappers：{}，context：{}", JSONArray.toJSONString(easyExcelRowDataWrappers), JSONObject.toJSONString(context));
        }
        List<GroupBuyingBatchOfflineByReportIdRowDTO> rows = easyExcelRowDataWrappers.stream()
                .map(item -> {
                    GroupBuyingBatchOfflineByReportIdRowDTO row = simpleCheckGroupBuyingBatchOfflineByReportIdExcelRow(item, context);
                    return row;
                })
                .collect(Collectors.toList());
        if (log.isDebugEnabled()) {
            log.debug("简单校验结束，rows：{}，context：{}", JSONArray.toJSONString(rows), JSONObject.toJSONString(context));
        }
        return rows;
    }

    /**
     * <pre>
     * 简单校验。
     * 数据必填项、数据本身合法性（是否是数字、格式等）。
     * </pre>
     *
     * @param excelRowEasyExcelRowDataWrapper
     * @param context
     * @return 返回校验后的行
     */
    private GroupBuyingBatchOfflineByReportIdRowDTO simpleCheckGroupBuyingBatchOfflineByReportIdExcelRow(EasyExcelRowDataWrapper<GroupBuyingBatchOfflineByReportIdExcelRow> excelRowEasyExcelRowDataWrapper,
                                                                                                         GroupBuyingBatchOfflineContext context) {
        if (Objects.isNull(excelRowEasyExcelRowDataWrapper)) {
            return null;
        }
        Set<String> userShopCodeSet = context.getUserShopCodeSet();
        SysUser operator = context.getOperator();
        Integer sheetNo = excelRowEasyExcelRowDataWrapper.getSheetNo();
        String sheetName = excelRowEasyExcelRowDataWrapper.getSheetName();
        Integer rowIndex = excelRowEasyExcelRowDataWrapper.getRowIndex();
        GroupBuyingBatchOfflineByReportIdExcelRow excelRow = excelRowEasyExcelRowDataWrapper.getRowData();
        GroupBuyingBatchOfflineByReportIdRowDTO rowDTO = new GroupBuyingBatchOfflineByReportIdRowDTO();
        rowDTO.setExcelRowIndex(rowIndex);
        rowDTO.setExcelRow(excelRow);
        List<String> failureReasonItems = Lists.newArrayListWithExpectedSize(16);
        String tempFailureReasonItem = null;
        if (excelRow != null) {
            /* 报名id */
            {
                boolean reportIdCheckResult = true;
                String reportIdStr = excelRow.getReportIdStr();
                Long reportId = null;
                if (StringUtils.isNotEmpty(reportIdStr)) {
                    reportIdStr = reportIdStr.trim();
                }
                if (StringUtils.isEmpty(reportIdStr)) {
                    reportIdCheckResult = false;
                } else {
                    try {
                        reportId = Long.parseLong(reportIdStr);
                        if (reportId <= 0L) {
                            reportIdCheckResult = false;
                        }
                    } catch (Exception e) {
                        reportIdCheckResult = false;
                    }
                }
                if (!reportIdCheckResult) {
                    tempFailureReasonItem = "报名ID为必填项且为正整数";
                    failureReasonItems.add(tempFailureReasonItem);
                }
                rowDTO.setReportIdCheckResult(reportIdCheckResult);
                if (reportIdCheckResult) {
                    rowDTO.setReportId(reportId);
                }
            }
        } else {
            tempFailureReasonItem = "当前行没有数据";
            failureReasonItems.add(tempFailureReasonItem);
            rowDTO.setReportIdCheckResult(false);
        }
        rowDTO.setFailureReasonItems(failureReasonItems);
        return rowDTO;
    }

    /**
     * 上传根据提报ID批量下线拼团活动失败Excel
     *
     * @param rows
     * @param context
     * @return
     */
    private String uploadBatchOfflineByReportIdFailureExcel(List<GroupBuyingBatchOfflineByReportIdRowDTO> rows, GroupBuyingBatchOfflineContext context) {
        ByteArrayOutputStream byteArrayOutputStream = null;
        InputStream inputStream = null;
        String operatorEmail = null;
        try {
            operatorEmail = context.getOperator().getEmail();
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
            // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
            HSSFSheet sheet = hssfWorkbook.createSheet("sheet");
            // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
            HSSFRow row = sheet.createRow(0);
            // 第四步，创建单元格，并设置值表头 设置表头居中
            HSSFCellStyle style = hssfWorkbook.createCellStyle();
            //水平居中
            style.setAlignment(HorizontalAlignment.CENTER_SELECTION);
            //垂直居中
            style.setVerticalAlignment(VerticalAlignment.TOP);
            //绿色背景
            style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.GREEN.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setFillBackgroundColor(HSSFColor.HSSFColorPredefined.YELLOW.getIndex());
            // 设置边框
            style.setBorderBottom(BorderStyle.THIN);
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);
            style.setBorderTop(BorderStyle.THIN);
            // 自动换行
            style.setWrapText(true);
            HSSFFont font = hssfWorkbook.createFont();
            font.setFontHeightInPoints((short) 10);
            //红色字体配绿色背景就是这么和谐
            font.setColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
            font.setFontName("宋体");
            // 把字体 应用到当前样式
            style.setFont(font);
            //声明列对象
            HSSFCell cell = null;
            //创建表头
            String headerStr = "报名ID*,失败原因";
            String[] headers = headerStr.split(",");
            for (int i = 0; i < headers.length; i++) {
                cell = row.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(style);
                if (Objects.equals(i, headers.length - 1)) {
                    sheet.setColumnWidth(i, 3766 * 6);
                } else {
                    sheet.setColumnWidth(i, 3766);
                }
            }
            int i = 0;
            for (GroupBuyingBatchOfflineByReportIdRowDTO rowDTO : rows) {
                // 从第二行开始
                i++;
                row = sheet.createRow(i);
                row.setHeight((short) 400);
                GroupBuyingBatchOfflineByReportIdExcelRow excelRow = rowDTO.getExcelRow();
                // 提报ID
                row.createCell(0).setCellValue(excelRow.getReportIdStr());
                // 失败原因
                List<String> failureReasonItems = rowDTO.getFailureReasonItems();
                row.createCell(1).setCellValue(String.join("；\n", failureReasonItems));
            }
            byteArrayOutputStream = new ByteArrayOutputStream();
            hssfWorkbook.write(byteArrayOutputStream);
            byte[] brray = byteArrayOutputStream.toByteArray();
            inputStream = new ByteArrayInputStream(brray);
            // 8位随机码数字
            StringBuilder randomStr = new StringBuilder();
            for (int j = 1; j <= 8; j++) {
                randomStr.append(RandomUtils.nextInt(0, 10));
            }
            String filename = MessageFormat.format("批量下线拼团活动{0}{1}.xls",
                    DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"), randomStr.toString());
            // 注释：上传的错误文件统一由marketing-admin服务的清理文件任务清理。
            String relativePath = fileUploadService.uploadFile(inputStream, filename, MarketingConstants.FTP_MODULE_DIR_MARKETING_POP_ADMIN_GROUP_BUYING_BATCH_OFFLINE_FAILURE);
            log.info("上传根据提报ID批量下线拼团活动失败Excel响应，operatorEmail：{}，rows'Size：{}，relativePath：{}",
                    operatorEmail, rows.size(), relativePath);
            if (StringUtils.isEmpty(relativePath)) {
                return null;
            }
            return appCdnProperties.getDomain() + relativePath;
        } catch (Exception e) {
            log.error("上传根据提报ID批量下线拼团活动失败Excel异常，operatorEmail：{}，rows：{}",
                    operatorEmail, JSONArray.toJSONString(rows), e);
            return null;
        } finally {
            // 其实这里不需要关闭，本着对流的敬畏的理念，显示关闭一下。
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                }
            }
            if (byteArrayOutputStream != null) {
                try {
                    byteArrayOutputStream.close();
                } catch (Exception e) {
                }
            }
        }
    }
}
