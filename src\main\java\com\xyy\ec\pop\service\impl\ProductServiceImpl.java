package com.xyy.ec.pop.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.ec.merchant.bussiness.enums.BusinessTypeEnum;
import com.xyy.ec.merchant.bussiness.utils.JsonUtil;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.config.ProductConfig;
import com.xyy.ec.pop.dto.ProductAuditDTO;
import com.xyy.ec.pop.excel.verify.ProductBatchUpdateValid;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.PopSkuConvertHelper;
import com.xyy.ec.pop.helper.ProductSkuConvertHelper;
import com.xyy.ec.pop.remote.BusinessCategoryRemoteAdapter;
import com.xyy.ec.pop.remote.PopCorporationRemoteAdapter;
import com.xyy.ec.pop.remote.ProductSkuRemoteAdapter;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.*;
import com.xyy.ec.pop.server.api.merchant.dto.BusinessCategoryDictDto;
import com.xyy.ec.pop.server.api.merchant.dto.BusinessScopeDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuAuditParamDTO;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDetailDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuPriceDto;
import com.xyy.ec.pop.server.api.product.enums.*;
import com.xyy.ec.pop.service.FastDfsUtilService;
import com.xyy.ec.pop.service.ProductCategoryService;
import com.xyy.ec.pop.service.ProductService;
import com.xyy.ec.pop.utils.CollectionUtil;
import com.xyy.ec.pop.valid.NewProductAuditValidInfo;
import com.xyy.ec.pop.valid.ProductAuditValidInfo;
import com.xyy.ec.pop.vo.*;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.result.EcSkuDetailDto;
import com.xyy.ec.product.back.end.ecp.pop.dto.SkuPopAuditingDTO;
import com.xyy.ec.product.back.end.ecp.priceGroup.dto.PriceMerchantGroupConditionDto;
import com.xyy.ec.product.back.end.ecp.priceGroup.dto.PriceMerchantGroupDto;
import com.xyy.ec.product.back.end.ecp.priceGroup.eunms.PriceMerchantGroupEnum;
import com.xyy.me.product.service.read.dto.TotalDictionaryReadDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 商品服务
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductServiceImpl implements ProductService {
    @Autowired
    private BusinessCategoryRemoteAdapter businessCategoryRemoteAdapter;
    @Autowired
    private ProductSkuRemoteAdapter productSkuRemoteAdapter;
    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;
    @Autowired
    private FastDfsUtilService fastDfsUtilService;
    @Autowired
    private ProductCategoryService productCategoryService;
    @Autowired
    private EcSkuRemoteAdapter ecSkuRemoteAdapter;
    @Autowired
    private PopSkuCategoryRemote popSkuCategoryRemote;
    @Autowired
    private EcCsuRpcService ecCsuRpcService;

    @Value("${product.highGross.suggestPrice.lowThreshold}")
    public BigDecimal highGrossSuggestPriceLowThreshold;
    @Value("${product.highGross.suggestPrice.highThreshold}")
    public BigDecimal highGrossSuggestPriceHighThreshold;

    @Autowired
    private ProductCsuForOtherSystemRemoteService productCsuForOtherSystemRemoteService;

    @Override
    public List<ZTree> getSkuCategoryTree(Long skuCategoryId, String orgId) {
        List<BusinessCategoryDictDto> treeCat = getSkuCategory(orgId);
        List<ZTree> result = Lists.newArrayList();
        for (BusinessCategoryDictDto dictDto : treeCat) {
            ZTree zTree = new ZTree();
            zTree.setId(dictDto.getCategoryId());
            zTree.setName(dictDto.getCategoryName());
            zTree.setOpen(true);
            List<BusinessScopeDto> businessScopeDtoList = dictDto.getBusinessScopeDtoList();
            List<ZTree> relation = convertCategoryToZtree(businessScopeDtoList, skuCategoryId);
            zTree.setChildren(relation);
            result.add(zTree);
        }
        return result;
    }

    private List<BusinessCategoryDictDto> getSkuCategory(String orgId) {
        CorporationDto dto = popCorporationRemoteAdapter.queryByOrgId(orgId);
        if(dto==null){
            return new ArrayList<>(0);
        }
        int corporationType = dto.getCorporationType();
        return businessCategoryRemoteAdapter.getBusinessTree(corporationType);
    }

    /**
     * 商品上报回调
     * @param barcode
     * @param standId
     */
    @Override
    public void afterProductReport(String barcode, String standId) throws ServiceException {
        PopSkuDto sku = productSkuRemoteAdapter.getSkuByBarcode(barcode);
        sku.setAuthType(1);
        sku.setAuthAction(0);
        sku.setAuthTime(new Date());
        sku.setAuthReasonCode(null);
        sku.setAuthReasonName(null);
        sku.setAuthReason(MeProductAuthEnum.OK.getCode());
        sku.setStandardProductId(standId);
        productSkuRemoteAdapter.updateStandardIdAndAuthReason(sku);
    }


    @Override
    public void skuAuditingByBarcode(ProductAuditDTO productAuditDTO, SysUser user) throws ServiceException {
        PopSkuDto hisSku = productSkuRemoteAdapter.getSkuByBarcode(productAuditDTO.getBarcode());
        if(hisSku==null){
            throw new ServiceException("没有查询到此商品");
        }
        int status = productAuditDTO.getStatus();
        String barcode = productAuditDTO.getBarcode();
        //查询原品关联的拼团品csuids
        List<Long> groupSkuCsuIds = productSkuRemoteAdapter.findGroupSkuCsuIdsByOriginalBarcode(barcode);
        ArrayList<Long> csuIds = Lists.newArrayList(hisSku.getCsuid());
        if (CollectionUtils.isNotEmpty(groupSkuCsuIds)) {
            csuIds.addAll(groupSkuCsuIds);
        }
        //YBM接口参数
        SkuPopAuditingDTO skuPopAuditingDTO = new SkuPopAuditingDTO();
        skuPopAuditingDTO.setSkuIdList(csuIds);
        skuPopAuditingDTO.setBarcode(barcode);
        skuPopAuditingDTO.setStatus(status);
        skuPopAuditingDTO.setOperatorUserName(user.getUsername());
        //POP接口参数
        PopSkuAuditParamDTO popSkuAuditParamDTO = new PopSkuAuditParamDTO();
        popSkuAuditParamDTO.setBarcode(productAuditDTO.getBarcode());
        popSkuAuditParamDTO.setStatus(status);
        popSkuAuditParamDTO.setOperatorUserName(user.getUsername());
        popSkuAuditParamDTO.setBz(productAuditDTO.getRemark());
        popSkuAuditParamDTO.setSkuQualificationList(productAuditDTO.getSkuQualificationList());

        //当为审核不通过时，不修改原信息，只有审核通过才会修改信息
        if (status == 6) {
            String productCategoryId = productAuditDTO.getProductCategoryId();
            if (StringUtils.isNotEmpty(productCategoryId)) {
                popSkuAuditParamDTO.setSkuCategoryId(Long.valueOf(productCategoryId));
            }
            //POP审核更新对象
            String skuCategory = productAuditDTO.getSkuCategory();
            skuCategory = skuCategory == null ? "" : skuCategory;
            popSkuAuditParamDTO.setSkuCategoryId(productAuditDTO.getSkuCategoryId());
            popSkuAuditParamDTO.setSkuCategory(skuCategory);  //经营类别
            popSkuAuditParamDTO.setErpFirstCategoryId(productAuditDTO.getErpFirstCategoryId());
            popSkuAuditParamDTO.setErpSecondCategoryId(productAuditDTO.getErpSecondCategoryId());
            popSkuAuditParamDTO.setErpThirdCategoryId(productAuditDTO.getErpThirdCategoryId());
            popSkuAuditParamDTO.setErpFourthCategoryId(productAuditDTO.getErpFourthCategoryId());
            popSkuAuditParamDTO.setProductUnit(productAuditDTO.getProductUnit());
            popSkuAuditParamDTO.setDosageForm(productAuditDTO.getDosageForm());
            popSkuAuditParamDTO.setStorageCondition(productAuditDTO.getStorageCondition());
            popSkuAuditParamDTO.setDrugClassification(productAuditDTO.getDrugClassification());
            popSkuAuditParamDTO.setProductName(productAuditDTO.getProductName());
            popSkuAuditParamDTO.setShowName(productAuditDTO.getShowName());
            popSkuAuditParamDTO.setCommonName(productAuditDTO.getCommonName());
            popSkuAuditParamDTO.setManufacturer(productAuditDTO.getManufacturer());
            popSkuAuditParamDTO.setBrand(productAuditDTO.getBrand());
            popSkuAuditParamDTO.setCode(productAuditDTO.getCode());
            popSkuAuditParamDTO.setApprovalNumber(productAuditDTO.getApprovalNumber());
            popSkuAuditParamDTO.setSpec(productAuditDTO.getSpec());
            popSkuAuditParamDTO.setMarketAuthor(productAuditDTO.getMarketAuthor());
            popSkuAuditParamDTO.setTerm(productAuditDTO.getTerm());
            popSkuAuditParamDTO.setProducer(productAuditDTO.getProducer());
            popSkuAuditParamDTO.setManufacturingLicenseNo(productAuditDTO.getManufacturingLicenseNo());


            //YBM审核更新对象
            skuPopAuditingDTO.setSkuCategory(skuCategory);
            skuPopAuditingDTO.setErpFirstCategoryId(productAuditDTO.getErpFirstCategoryId());
            skuPopAuditingDTO.setErpSecondCategoryId(productAuditDTO.getErpSecondCategoryId());
            skuPopAuditingDTO.setErpThirdCategoryId(productAuditDTO.getErpThirdCategoryId());
            skuPopAuditingDTO.setErpFourthCategoryId(productAuditDTO.getErpFourthCategoryId());
            skuPopAuditingDTO.setProductUnit(productAuditDTO.getProductUnit());
            skuPopAuditingDTO.setDosageForm(productAuditDTO.getDosageForm());
            skuPopAuditingDTO.setStorageCondition(productAuditDTO.getStorageCondition());
            skuPopAuditingDTO.setDrugClassification(productAuditDTO.getDrugClassification());
            skuPopAuditingDTO.setProductName(productAuditDTO.getProductName());
            skuPopAuditingDTO.setShowName(productAuditDTO.getShowName());
            skuPopAuditingDTO.setCommonName(productAuditDTO.getCommonName());
            skuPopAuditingDTO.setManufacturer(productAuditDTO.getManufacturer());
            skuPopAuditingDTO.setBrand(productAuditDTO.getBrand());
            skuPopAuditingDTO.setCode(productAuditDTO.getCode());
            skuPopAuditingDTO.setApprovalNumber(productAuditDTO.getApprovalNumber());
            skuPopAuditingDTO.setSpec(productAuditDTO.getSpec());
            skuPopAuditingDTO.setMarketAuthor(productAuditDTO.getMarketAuthor());
            skuPopAuditingDTO.setTerm(productAuditDTO.getTerm());
            skuPopAuditingDTO.setProducer(productAuditDTO.getProducer());
            skuPopAuditingDTO.setManufacturingLicenseNo(productAuditDTO.getManufacturingLicenseNo());
        }
        skuPopAuditingDTO.setShopCode(hisSku.getShopCode());
        boolean auditOk = ecSkuRemoteAdapter.auditingByBarcodeAndShopCode(skuPopAuditingDTO);
        if(!auditOk){
            log.error("ec店铺商品审核接口失败,barcode:{}",skuPopAuditingDTO.getBarcode());
            throw new ServiceException("POP调用EC审核商品RPC执行异常");
        }
        //2、pop商品审核
        boolean ok = productSkuRemoteAdapter.auditSku(popSkuAuditParamDTO);
        if (!ok) {
            System.out.println("PopSkuExtendServiceImpl.skuAuditingByBarcode");
            log.error("#PopSkuExtendServiceImpl.skuAuditingByBarcode,POP审核商品RPC执行异常，参数：{}", JSON.toJSONString(popSkuAuditParamDTO));
            throw new ServiceException("POP审核商品RPC执行异常");
        }

    }

    @Override
    public ProductSkuVo getProductInfo(String orgId, String barcode) {
        PopSkuDetailDto detail = productSkuRemoteAdapter.getSkuDetailByBarcodeWithArchive(barcode);
        ProductSkuVo vo = ProductSkuConvertHelper.convertToSkuVO(detail);
        setCategoryName(vo);
        return vo;
    }

    @Override
    public ProductSkuVo getEcProductInfo(String shopCode, Long id) {
        EcSkuDetailDto ecSkuDetailDto = ecCsuRpcService.getSkuDetailByIdAndShopCode(id, shopCode);
        ProductSkuVo vo = ProductSkuConvertHelper.convertToEcSkuVO(ecSkuDetailDto);
        setCategoryName(vo);
        return vo;
    }

    @Override
    public String validAudit(ProductAuditDTO aud, PopSkuDetailDto skuDetailDto) {
        if(Objects.equals(aud.getStatus(),PopSkuStatus.AUDIT_FAILED.getValue())){
            return null;
        }
        int cat = aud.getSkuCategoryId().intValue();
        //不需要校验的经营分类
        if(ProductConfig.config.instrumentAuditValidExcludesCategory.contains(cat)){
            return null;
        }
        //医疗器械校验医疗器械注册证编号和经营分类关系
        if(StringUtils.isEmpty(skuDetailDto.getPopSku().getApprovalNumber())){
            return null;
        }
        //经营分类有层级关系，需要查询字典
        List<BusinessCategoryDictDto> treeCat = getSkuCategory(skuDetailDto.getPopSku().getOrgId());
        List<BusinessScopeDto> categories = treeCat.stream().flatMap(item -> item.getBusinessScopeDtoList().stream()).collect(Collectors.toList());
        for(ProductAuditValidInfo validInfo:ProductConfig.config.instrumentAuditValidInfo){
            //命中规则
            if(skuDetailDto.getPopSku().getApprovalNumber().matches(validInfo.getPattern())){
                //直接选的对应分类
                if(validInfo.getCategory().contains(aud.getSkuCategoryId())){
                    return null;
                }
                //校验是否选的子级
                for(Long category:validInfo.getCategory()){
                    BusinessScopeDto bus = findBusiness(categories,category);
                    if(bus!=null&&isChild(bus.getChildDictionaryList(),aud.getSkuCategoryId())){
                        return null;
                    }
                }
                return validInfo.getErrorTip();
            }
        }
        return null;
    }

    @Override
    public String ecValidAudit(ProductSkuVo productVo) {
        //经营分类名称多个以逗号隔开
        List<String> skuCategory = Arrays.stream(productVo.getSkuCategory().split(",")).collect(Collectors.toList());
        //不需要校验的经营分类
        if (CollectionUtils.containsAny(ProductConfig.config.newInstrumentAuditValidExcludesCategory, skuCategory)) {
            return null;
        }
        //当一级分类为“中西成药、注射用药”，若批准文号不满足正则校验
        if (ProductConfig.config.drugCategory.contains(productVo.getErpFirstCategoryId())) {
            if (StringUtils.isEmpty(productVo.getApprovalNumber()) || !productVo.getApprovalNumber().matches(ProductConfig.config.drugApprovalPattern))
                return "批准文号格式不正确";
        }
        //当一级分类为“医疗器械”，若“医疗器械注册证或备案凭证编号”不满足正则校验
        if (ProductConfig.config.instrumentCategory.equals(productVo.getErpFirstCategoryId())) {
            if (StringUtils.isEmpty(productVo.getApprovalNumber()) || !productVo.getApprovalNumber().matches(ProductConfig.config.getInstrumentLicensePattern())) {
                return "医疗器械注册证或备案凭证编号格式不正确";
            }
            if (!productVo.getApprovalNumber().matches(ProductConfig.config.getManufacturingLicenseNoPatternWithInstrument())) {
                //当一级分类为“医疗器械”，若“生产许可证号或备案凭证编号”为空或不满足正则校验
                if (StringUtils.isEmpty(productVo.getManufacturingLicenseNo()) || !productVo.getManufacturingLicenseNo().matches(ProductConfig.config.getManufacturingLicensePattern())) {
                    return "生产许可证号或备案凭证编号格式不正确";
                }
            }
        }
        //经营分类有层级关系，需要查询字典 ec默认经营企业
        List<BusinessCategoryDictDto> treeCat = businessCategoryRemoteAdapter.getBusinessTree(0);
        List<BusinessScopeDto> categories = treeCat.stream().flatMap(item -> item.getBusinessScopeDtoList().stream()).collect(Collectors.toList());
        for (NewProductAuditValidInfo validInfo : ProductConfig.config.newInstrumentAuditValidInfo) {
            //命中规则
            if (productVo.getApprovalNumber().matches(validInfo.getPattern())) {
                //直接选的对应分类
                if (CollectionUtils.containsAny(validInfo.getCategory(), skuCategory)) {
                    return null;
                }
                //校验是否选的子级
                for (String category : validInfo.getCategory()) {
                    BusinessScopeDto bus = findBusinessByName(categories, category);
                    if (bus != null && isChildByName(bus.getChildDictionaryList(), skuCategory)) {
                        return null;
                    }
                }
                return validInfo.getErrorTip();
            }
        }
        return null;
    }

    private boolean isChild(List<BusinessScopeDto> buss, Long toFind) {
        if(CollectionUtils.isEmpty(buss)){
            return false;
        }
        for(BusinessScopeDto bus:buss){
            if(bus.getId().equals(toFind)){
                return true;
            }
            if(isChild(bus.getChildDictionaryList(),toFind)){
                return true;
            }
        }
        return false;
    }

    private boolean isChildByName(List<BusinessScopeDto> buss, List<String> skuCategoryList) {
        if (CollectionUtils.isEmpty(buss)) {
            return false;
        }
        for (BusinessScopeDto bus : buss) {
            if (skuCategoryList.contains(bus.getDictName())) {
                return true;
            }
            if (isChildByName(bus.getChildDictionaryList(), skuCategoryList)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 找到指定经营范围
     * @param treeCat
     * @param category
     * @return
     */
    private BusinessScopeDto findBusiness(List<BusinessScopeDto> treeCat, Long category) {
        if(CollectionUtils.isEmpty(treeCat)){
            return null;
        }
        for(BusinessScopeDto bus:treeCat){
            if(category.equals(bus.getId())){
                return bus;
            }
            BusinessScopeDto find = findBusiness(bus.getChildDictionaryList(),category);
            if(find!=null){
                return find;
            }
        }
        return null;
    }

    private BusinessScopeDto findBusinessByName(List<BusinessScopeDto> treeCat, String categoryName) {
        if(CollectionUtils.isEmpty(treeCat)){
            return null;
        }
        for(BusinessScopeDto bus:treeCat){
            if(categoryName.equals(bus.getDictName())){
                return bus;
            }
            BusinessScopeDto find = findBusinessByName(bus.getChildDictionaryList(),categoryName);
            if(find!=null){
                return find;
            }
        }
        return null;
    }

    /**
     * 根据自有分类id 查询分类名
     * @param productVo
     */
    private void setCategoryName(ProductSkuVo productVo) {
        if(productVo.getErpFirstCategoryId()==null){
            return;
        }
        List<Integer> categoryIds = Lists.newArrayList(productVo.getErpFirstCategoryId(),productVo.getErpSecondCategoryId(),
                productVo.getErpThirdCategoryId(),productVo.getErpFourthCategoryId()).stream().filter(item->item!=null)
                .map(item-> NumberUtils.toInt(item)).collect(Collectors.toList());
        //获取四级分类字典
        List<TotalDictionaryReadDto> dictionaryReadDtos =  productCategoryService.getFourLevelProductCategoryByIds(categoryIds);
        if(org.springframework.util.CollectionUtils.isEmpty(dictionaryReadDtos)){
            return;
        }
        Map<Byte, TotalDictionaryReadDto> dicMap = dictionaryReadDtos.stream().collect(Collectors.toMap(TotalDictionaryReadDto:: getLevelNode, a -> a,(k1, k2)->k1));
        log.info("setProductCategory skuId:{},result:{}",productVo.getId(), JsonUtil.toJson(dicMap));
        if(dicMap.containsKey((byte)1)){
            productVo.setErpFirstCategoryName(dicMap.get((byte)1).getDictName());
        }
        if(dicMap.containsKey((byte)2)){
            productVo.setErpSecondCategoryName(dicMap.get((byte)2).getDictName());
        }
        if(dicMap.containsKey((byte)3)){
            productVo.setErpThirdCategoryName(dicMap.get((byte)3).getDictName());
        }
        if(dicMap.containsKey((byte)4)){
            productVo.setErpFourthCategoryName(dicMap.get((byte)4).getDictName());
        }
    }


    @Override
    public BatchUpdateResultVo batchUpdate(String user, List<SkuBatchUpdateVo> vos) throws ServiceException {

        List<String> barcodes = vos.stream().map(SkuBatchUpdateVo::getBarcode).collect(Collectors.toList());
        List<PopSkuDto> skuByBarCodes = productSkuRemoteAdapter.findSkuByBarCodes(barcodes);
        List<PopSkuDto> wholesaleList = skuByBarCodes.stream().filter(popSkuDto -> popSkuDto.getActivityType().equals(ActivityTypeEnum.WHOLESALE.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(wholesaleList)){
            Map<String, PopSkuDto> barcodeMap = wholesaleList.stream().collect(Collectors.toMap(item -> item.getBarcode(), item -> item));
            List<SkuBatchUpdateVo> errors = vos.stream().filter(skuBatchUpdateVo -> barcodeMap.containsKey(skuBatchUpdateVo.getBarcode())).collect(Collectors.toList());
            errors.forEach(skuBatchUpdateVo -> skuBatchUpdateVo.setErrorMsg("批购包邮商品，请在表格里剔除这类商品后再提交"));
            BatchUpdateResultVo resultVo = new BatchUpdateResultVo();
            resultVo.setError(errors.size());
            String uuid = UUID.randomUUID().toString();
            resultVo.setErrorFileUrl(uuid);
            resultVo.setSuccess(0);
            //将数据写入excel文件
            String fileUrl = fastDfsUtilService.writeDateToFastDfs(new ExportParams(null, "导入失败商品", ExcelType.XSSF), SkuBatchUpdateVo.class, new ArrayList<>(errors));
            resultVo.setErrorFileUrl(fileUrl);
            return resultVo;
        }

        //校验经营范围
        validAndSetSkuCategory(vos);
        //校验4级分类名
        validAndSetBusiness(vos);
        //校验建议零售价
        validSuggest(vos);
        //记录原始文件：包含错误原因，方便后期分析
        String fileUrl = fastDfsUtilService.writeDateToFastDfs(new ExportParams(null, "批量导入的商品-"+user, ExcelType.XSSF), SkuBatchUpdateVo.class, new ArrayList<>(vos));
        if(StringUtils.isEmpty(fileUrl)){
            throw new ServiceException("导入商品失败：记录原始文件异常");
        }
        //过滤可以更新的商品
        List<SkuBatchUpdateVo> okVos = vos.stream().filter(item->!item.isFailed()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(okVos)){
            BatchUpdateResultVo resultVo = batchUpdateResultVoBase(vos.size(),vos);
            log.info("批量更新商品信息：没有可更新商品:user:{},result:{}",user, JSON.toJSONString(resultVo));
            return resultVo;
        }
        //返回不存在的商品
        List<String> notExistsBarCodes = productSkuRemoteAdapter.batchUpdate(user,okVos.stream().map(ProductSkuConvertHelper::convertToUpDateDto).collect(Collectors.toList()),fileUrl);
        log.info("批量更新商品信息，成功更新：{}个",okVos.size()-notExistsBarCodes.size());
        okVos.forEach(item->{
            if(notExistsBarCodes.contains(item.getBarcode())){
                item.setFailed(true);
                item.setErrorMsg("商品编码不存在");
            }
        });
        //错误信息写入文件
        List<SkuBatchUpdateVo> errorsVos = vos.stream().filter(item->item.isFailed()).collect(Collectors.toList());
        BatchUpdateResultVo resultVo = batchUpdateResultVoBase(vos.size(),errorsVos);
        log.info("批量更新商品信息：更新结果:user:{},result:{}",user, JSON.toJSONString(resultVo));
        return resultVo;
    }

    private void validSuggest(List<SkuBatchUpdateVo> vos) throws ServiceException {
        vos = vos.stream().filter(item->!item.isFailed()&&item.getSuggestPrice()!=null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        List<String> barcodes = vos.stream().map(item -> item.getBarcode()).collect(Collectors.toList());
        Map<String, PopSkuDto> skuMap = productSkuRemoteAdapter.findSkuByBarCodes(barcodes).stream().collect(Collectors.toMap(item -> item.getBarcode(), item -> item));
        vos.forEach(item->{
            PopSkuDto sku = skuMap.get(item.getBarcode());
            if(sku==null){
                item.setErrorMsg("没有查询到指定商品");
                item.setFailed(true);
                return;
            }
            try{
                checkHighGross(sku.getHighGross(),new BigDecimal(item.getSuggestPrice()),new BigDecimal(sku.getFob().toString()));
            }catch (IllegalArgumentException e){
                item.setErrorMsg(e.getMessage());
                item.setFailed(true);
            }
        });
    }

    public void checkHighGross(byte highGross, BigDecimal suggestPrice, BigDecimal fob) {
        if(highGross!= PopHighGrossLabelEnum.HIGH_GROSS.getValue()
                &&highGross!= PopHighGrossLabelEnum.FIRST_CHOOSE.getValue()){
            return;
        }
        BigDecimal percent = suggestPrice.subtract(fob).divide(suggestPrice,2, RoundingMode.DOWN).multiply(new BigDecimal("100"));
        if(percent.compareTo(highGrossSuggestPriceLowThreshold)<0){
            throw new IllegalArgumentException("高毛商品建议零售价需满足“20%≤(建议零售价-药帮忙价)/建议零售价≤75%”");
        }
        if(percent.compareTo(highGrossSuggestPriceHighThreshold)>0){
            throw new IllegalArgumentException("高毛商品建议零售价需满足“20%≤(建议零售价-药帮忙价)/建议零售价≤75%”");
        }
    }

    private void validAndSetBusiness(List<SkuBatchUpdateVo> vos) {
        List<SkuBatchUpdateVo> toUpdate = vos.stream().filter(item->!item.isFailed()&&StringUtils.isNotEmpty(item.getBusinessFirstCategoryName())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(toUpdate)){
            return;
        }
        //找4级分类对照关系,为了避免有同名的分类所以不转换 name:code map (比如"其他")
        Map<Integer, List<TotalDictionaryReadDto>> map = productCategoryService.getFourLevelProductCategory().stream().collect(Collectors.groupingBy(item -> item.getParentId()));
        ProductBatchUpdateValid.validAndSetBusiness(toUpdate,map);
        Map<String,String> hisMap = popSkuCategoryRemote.firstCateMap(toUpdate.stream().map(item->item.getBarcode()).collect(Collectors.toList()));
        toUpdate.forEach(item->{
            if(item.getBusinessFirstCategoryCode()!=null&&!Objects.equals(item.getBusinessFirstCategoryCode(),hisMap.get(item.getBarcode()))){
                item.setErrorMsg("一级分类与商品原有一级分类不一致，请保持一致后修改");
                item.setFailed(true);
            }
        });
    }

    private void validAndSetSkuCategory(List<SkuBatchUpdateVo> vos) {
        List<SkuBatchUpdateVo> toUpdate = vos.stream().filter(item->!item.isFailed()&&StringUtils.isNotEmpty(item.getSkuCategory())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(toUpdate)){
            return;
        }
        Map<String,Long> catMap = getBusinessCategory(SkuSaleType.DRUG.getCode());
        catMap.putAll(getBusinessCategory(SkuSaleType.NON_DRUG.getCode()));
        toUpdate.forEach(item->{
            item.setSkuCategoryId(catMap.get(item.getSkuCategory()));
            if(item.getSkuCategoryId()==null){
                item.setFailed(true);
                item.setErrorMsg("没有找到对应经营范围");
            }
        });
    }

    /**
     * 返回经营范围名称和id关系
     * @param saleType
     * @return
     */
    private Map<String, Long> getBusinessCategory(int saleType) {
        List<BusinessCategoryDictDto> treeCat = businessCategoryRemoteAdapter.getBusinessTree(saleType);
        Map<String, Long> map = new HashMap<>(1024);
        treeCat.forEach(item->{
            addCategories(map,item.getBusinessScopeDtoList());
        });
        return map;
    }

    private void addCategories(Map<String, Long> map, List<BusinessScopeDto> businessScopeDtoList) {
        if(CollectionUtil.isEmpty(businessScopeDtoList)){
            return;
        }
        businessScopeDtoList.forEach(bus->{
            map.put(StringUtils.trimToNull(bus.getDictName()),bus.getId());
            addCategories(map,bus.getChildDictionaryList());
        });
    }

    /**
     * 记录失败文件
     * @param totalSize
     * @param errorVos
     * @return
     */
    private BatchUpdateResultVo batchUpdateResultVoBase(int totalSize, List<SkuBatchUpdateVo> errorVos) {
        BatchUpdateResultVo resultVo = new BatchUpdateResultVo();
        resultVo.setError(errorVos.size());
        String uuid = UUID.randomUUID().toString();
        resultVo.setErrorFileUrl(uuid);
        resultVo.setSuccess(totalSize - resultVo.getError());
        //将数据写入excel文件
        String fileUrl = fastDfsUtilService.writeDateToFastDfs(new ExportParams(null, "导入失败商品", ExcelType.XSSF), SkuBatchUpdateVo.class, new ArrayList<>(errorVos));
        resultVo.setErrorFileUrl(fileUrl);
        return resultVo;
    }
    private List<ZTree> convertCategoryToZtree(List<BusinessScopeDto> treeCat, Long productCategoryId) {
        List<ZTree> list = new ArrayList<>(treeCat.size());
        treeCat.forEach(item -> {
            ZTree tree = new ZTree();
            tree.setId(item.getId());
            tree.setName(item.getDictName());
            tree.setLevel(item.getLevelNode());
            tree.setOpen(true);
            tree.setpId(item.getParentId().longValue());
            tree.setCheckDisable(!CollectionUtils.isEmpty(item.getChildDictionaryList()));
            tree.setChecked(item.getId().equals(productCategoryId));
            List<BusinessScopeDto> chils = item.getChildDictionaryList();
            if (CollectionUtils.isEmpty(chils)) {
                tree.setIsParent(false);
            } else {
                tree.setIsParent(true);
                tree.setOpen(true);
                tree.setChildren(convertCategoryToZtree(chils, productCategoryId));
            }
            list.add(tree);
        });
        return list;
    }

    @Override
    public ProductEcVo getProductPreviewInfo(String orgId, String barcode) throws ServiceException {
        log.info("#ProductSkuServiceImpl.getProductInfo#info,参数：orgId:{},barcode:{}",orgId,barcode);
        if (StringUtils.isEmpty(orgId) || StringUtils.isEmpty(barcode)){
            throw new ServiceException("缺少必要参数");
        }
        PopSkuDetailDto popSkuDetailDto = productSkuRemoteAdapter.getDetailByBarcode(orgId,barcode);
        if(popSkuDetailDto ==null ){
            return null;
        }
//        if(popSkuDetailDto.getPopSku()==null || popSkuDetailDto.getPopSkuExclusives()==null || popSkuDetailDto.getPopSkuInstruction()==null){
//            log.error("#ProductSkuServiceImpl.getProductInfo#error,查询结果缺少必要信息，参数：orgId:{},barcode:{}，结果：{}",orgId,barcode, JSON.toJSONString(popSkuDetailDto));
//            throw new ServiceException("查询结果缺少必要信息");
//        }
        ProductEcVo productVo = PopSkuConvertHelper.popSkuDetailDtoToProductEcVo(popSkuDetailDto);
        List<SkuPopExtend> skuPopExtendNewList = productVo.getSkuPopExtendNewList();
        if(skuPopExtendNewList != null && !skuPopExtendNewList.isEmpty()){
            SkuPopExtend skuPopExtendNew = skuPopExtendNewList.get(0);
            ProducePopExtendVo skuPopExtend = new ProducePopExtendVo();
            skuPopExtend.setId(skuPopExtendNew.getId());
            skuPopExtend.setErpCode(skuPopExtendNew.getErpCode());
            skuPopExtend.setPurchaseMerchantType(skuPopExtendNew.getPurchaseMerchantType());
            skuPopExtend.setMarketPrice(skuPopExtendNew.getMarketPrice());
            if(popSkuDetailDto.getPopSku()!=null){
                skuPopExtend.setErpCode(popSkuDetailDto.getPopSku().getErpCode());
            }

            productVo.setSkuPopExtend(skuPopExtend);
        }
        BigDecimal fb = new BigDecimal("0");
        if(popSkuDetailDto.getPopSku().getSuggestPrice()!=null && popSkuDetailDto.getPopSku().getFob()!=null){
            BigDecimal fob = new BigDecimal(popSkuDetailDto.getPopSku().getFob());
            if(fob.compareTo(new BigDecimal("0"))>1){
                fb = popSkuDetailDto.getPopSku().getSuggestPrice().subtract(fob).divide(fob,2,BigDecimal.ROUND_HALF_UP);
                if(fb.compareTo(new BigDecimal("0"))==-1){
                    fb = new BigDecimal("0");
                }
            }
        }
        productVo.setGrossMargin(fb.toString());
        //发布分类
        setProductCategory(productVo);

        return productVo;
    }
    private void setProductCategory(ProductEcVo productVo){
        if(productVo==null || productVo.getSource()==null){
            return;
        }
        setCategoryName(productVo);

    }

    /**
     * 根据自有分类id 查询分类名
     * @param productVo
     */
    private void setCategoryName(ProductEcVo productVo) {
        if(productVo.getErpFirstCategoryId()==null){
            return;
        }
        List<Integer> categoryIds =getFourcategoryIds(productVo);
        //获取四级分类字典
        List<TotalDictionaryReadDto> dictionaryReadDtos =  productCategoryService.getFourLevelProductCategoryByIds(categoryIds);
        if(org.springframework.util.CollectionUtils.isEmpty(dictionaryReadDtos)){
            return;
        }
        Map<Byte, TotalDictionaryReadDto> dicMap = dictionaryReadDtos.stream().collect(Collectors.toMap(TotalDictionaryReadDto:: getLevelNode, a -> a,(k1, k2)->k1));
        log.info("setProductCategory skuId:{},result:{}",productVo.getId(), JsonUtil.toJson(dicMap));
        if(dicMap.containsKey((byte)1)){
            productVo.setErpFirstCategoryId((long)dicMap.get((byte)1).getId());
            productVo.setErpFirstCategoryName(dicMap.get((byte)1).getDictName());
        }
        if(dicMap.containsKey((byte)2)){
            productVo.setErpSecondCategoryId((long)dicMap.get((byte)2).getId());
            productVo.setErpSecondCategoryName(dicMap.get((byte)2).getDictName());
        }
        if(dicMap.containsKey((byte)3)){
            productVo.setErpThirdCategoryId((long)dicMap.get((byte)3).getId());
            productVo.setErpThirdCategoryName(dicMap.get((byte)3).getDictName());
        }
        if(dicMap.containsKey((byte)4)){
            productVo.setErpFourthCategoryId((long)dicMap.get((byte)4).getId());
            productVo.setErpFourthCategoryName(dicMap.get((byte)4).getDictName());
        }
    }

    private List<Integer>  getFourcategoryIds(ProductEcVo productVo){
        List<Integer> categoryIds = Lists.newArrayList();
        if(productVo.getErpFirstCategoryId()!=null){
            categoryIds.add(productVo.getErpFirstCategoryId().intValue());
        }
        if(productVo.getErpSecondCategoryId()!=null){
            categoryIds.add(productVo.getErpSecondCategoryId().intValue());
        }
        if(productVo.getErpThirdCategoryId()!=null){
            categoryIds.add(productVo.getErpThirdCategoryId().intValue());
        }
        if(productVo.getErpFourthCategoryId() != null){
            categoryIds.add(productVo.getErpFourthCategoryId().intValue());
        }
        return categoryIds;
    }

    @Override
    public void fillPriceMerchantGroup(List<PopSkuDetailDto> list, Page<ProductListVo> ecSkuList) {
        Set<Long> groupIds = new HashSet<>();
        Map<Long, List<PopSkuPriceDto>> priceMerchantGroupMap = Maps.newHashMap();
        for (PopSkuDetailDto popSkuDetailDto : list) {
            if (org.springframework.util.CollectionUtils.isEmpty(popSkuDetailDto.getPopSkuPriceList())) {
                continue;
            }
            groupIds.addAll(popSkuDetailDto.getPopSkuPriceList().stream().map(PopSkuPriceDto::getGroupId).collect(Collectors.toList()));
            priceMerchantGroupMap.put(popSkuDetailDto.getPopSku().getId(), popSkuDetailDto.getPopSkuPriceList());
        }
        if (CollectionUtils.isEmpty(groupIds)){
            return;
        }
        Map<Long, PriceMerchantGroupDto> groupDtoMap = productCsuForOtherSystemRemoteService.queryPriceMerchantGroupMap(new ArrayList<>(groupIds));
        if (groupDtoMap == null || groupDtoMap.isEmpty()){
            return;
        }
        Map<Integer,XyyRegionBusinessDto> areaMaps = queryAreaMap(groupDtoMap);
        List<XyyRegionBusinessDto> xyyRegionBusinessDtos = baseRegionBusinessRPCService.listProvinceRegions();
        List<Integer> allAreaCodes = CollectionUtils.isEmpty(xyyRegionBusinessDtos) ? Lists.newArrayList() :
                xyyRegionBusinessDtos.stream().map(XyyRegionBusinessDto::getAreaCode).collect(Collectors.toList());
        ecSkuList.getRows().forEach(item->{
            List<PopSkuPriceDto> popSkuPriceDtos = priceMerchantGroupMap.get(Long.valueOf(item.getId()));
            if (CollectionUtils.isEmpty(popSkuPriceDtos)){
                item.setHaveAreaPrice(0);
                return;
            }
            List<PopSkuAreaPriceVo> priceVos = popSkuPriceDtos.stream().map(priceDto -> {
                PriceMerchantGroupDto groupDto = groupDtoMap.get(priceDto.getGroupId());
                if (groupDto == null) {
                    return null;
                }
                PopSkuAreaPriceVo areaPriceVo = new PopSkuAreaPriceVo();
                areaPriceVo.setGroupName(groupDto.getGroupName());
                areaPriceVo.setPrice(priceDto.getPrice());
                List<PriceMerchantGroupConditionDto> priceMerchantGroupConditionDtoList = groupDto.getPriceMerchantGroupConditionDtoList();
                if (CollectionUtils.isEmpty(priceMerchantGroupConditionDtoList)) {
                    return areaPriceVo;
                }
                List<String> areaNames = Lists.newArrayList();
                List<Integer> areaCodes = Lists.newArrayList();
                List<String> customerTyNames = Lists.newArrayList();
                List<Integer> customerTypes = Lists.newArrayList();
                for (PriceMerchantGroupConditionDto priceMerchantGroupConditionDto : priceMerchantGroupConditionDtoList) {
                    if (!NumberUtil.isInteger(priceMerchantGroupConditionDto.getThreshold())) {
                        continue;
                    }
                    Integer threshold = Integer.valueOf(priceMerchantGroupConditionDto.getThreshold());
                    if (Boolean.TRUE.equals(PriceMerchantGroupEnum.ConditionTypeEnum.isArea(priceMerchantGroupConditionDto.getConditionType()))) {
                        XyyRegionBusinessDto xyyRegionBusinessDto = areaMaps.get(threshold);
                        if (xyyRegionBusinessDto != null){
                            areaNames.add(xyyRegionBusinessDto.getAreaName());
                            areaCodes.add(threshold);
                        }
                    } else {
                        customerTyNames.add(BusinessTypeEnum.get(threshold));
                        customerTypes.add(threshold);
                    }
                }
                if (new HashSet<>(allAreaCodes).equals(new HashSet<>(areaCodes))) {
                    areaPriceVo.setAllAreaCodes(allAreaCodes);
                    areaPriceVo.setAreaNames("全国");
                } else {
                    areaPriceVo.setAreaNames(String.join(",", areaNames));
                    Set<Integer> tmpList = new HashSet<>();
                    for (Integer integer : areaCodes) {
                        tmpList.add(integer);
                        XyyRegionBusinessDto areaOne = areaMaps.get(integer);
                        if (areaOne == null || areaOne.getParentCode() == 0){
                            continue;
                        }
                        tmpList.add(areaOne.getParentCode());
                        XyyRegionBusinessDto areaTwo = areaMaps.get(areaOne.getParentCode());
                        if (areaTwo == null || areaTwo.getParentCode() == 0){
                            continue;
                        }
                        tmpList.add(areaTwo.getParentCode());
                    }
                    areaPriceVo.setAllAreaCodes(new ArrayList<>(tmpList));
                }
                areaPriceVo.setCustomerTypes(String.join(",", customerTyNames));
                areaPriceVo.setCustomerTypeIds(customerTypes);
                return areaPriceVo;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(priceVos)){
                item.setHaveAreaPrice(1);
                item.setSkuAreaPriceVos(priceVos);
            }
        });

    }

    @Autowired
    private BaseRegionBusinessRPCService baseRegionBusinessRPCService;

    private Map<Integer, XyyRegionBusinessDto> queryAreaMap(Map<Long, PriceMerchantGroupDto> groupDtoMap) {
        List<Integer> areaCodes = groupDtoMap.values().stream().flatMap(groupDto -> groupDto.getPriceMerchantGroupConditionDtoList().stream().filter(conditionDto -> PriceMerchantGroupEnum.ConditionTypeEnum.isArea(conditionDto.getConditionType())).map(conditionDto -> Integer.valueOf(conditionDto.getThreshold()))).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areaCodes)){
            return Maps.newHashMap();
        }
        List<XyyRegionBusinessDto> xyyRegionBusinessDtos = baseRegionBusinessRPCService.queryParentToProvinceByAreaList(areaCodes);
        if (CollectionUtils.isEmpty(xyyRegionBusinessDtos)){
            return Maps.newHashMap();
        }
        return xyyRegionBusinessDtos.stream().collect(Collectors.toMap(XyyRegionBusinessDto::getAreaCode, Function.identity(), (v1, v2)->v1));
    }

}
