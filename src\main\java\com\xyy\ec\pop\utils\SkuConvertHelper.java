package com.xyy.ec.pop.utils;

import com.google.common.collect.Lists;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDetailDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuInstructionDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuInstructionImageDto;
import com.xyy.ec.pop.server.api.product.enums.ActivityTypeEnum;
import com.xyy.ec.pop.server.api.product.enums.PopControlRosterType;
import com.xyy.ec.product.back.end.ecp.csuinstruction.dto.CsuInstructionImageDTO;
import com.xyy.ec.product.back.end.ecp.csuinstruction.dto.CsuRegisterCertificateImageDTO;
import com.xyy.ec.product.back.end.ecp.pop.dto.SkuMerchantGroupRelationDTO;
import com.xyy.ec.product.back.end.ecp.pop.dto.SkuPopDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 商品转换工具类
 * <AUTHOR>
 */
public class SkuConvertHelper {
    private final static Integer COPY_YES=1;

    public static SkuPopDTO convertToEcSku(PopSkuDetailDto sku,  List<Integer> areas, CorporationDto cor) {
        SkuPopDTO dto = new SkuPopDTO();
//        if (CollectionUtils.isNotEmpty(sku.getPopSkuExclusives())) {
//            dto.setSkuPopExtendNewList(sku.getPopSkuExclusives().stream().map(item -> convertToExtend(sku, item)).collect(Collectors.toList()));
//        }
        //设置csuid
        dto.setId(sku.getPopSku().getCsuid());
        dto.setOrgId(sku.getPopSku().getOrgId());
        dto.setShopCode(sku.getPopSku().getShopCode());
        dto.setNearManuf(sku.getPopSku().getOldestProDate());
        dto.setFarManuf(sku.getPopSku().getNewProDate());
        dto.setBrand(sku.getPopSku().getBrand());
        dto.setErpCode(sku.getPopSku().getErpCode());
        dto.setCompanyName(cor.getCompanyName());
        dto.setCategoryId(NumberUtils.toLong(sku.getPopSkuCategory().getBusinessFirstCategoryCode()));
        dto.setCode(sku.getPopSku().getCode());
        dto.setBarcode(sku.getPopSku().getBarcode());
        dto.setProductName(sku.getPopSku().getProductName());
        dto.setCommonName(sku.getPopSku().getCommonName());
        dto.setShowName(sku.getPopSku().getShowName());
        dto.setManufacturer(sku.getPopSku().getManufacturer());
        dto.setSpec(sku.getPopSku().getSpec());
        dto.setFob(sku.getPopSku().getFob());
        dto.setGuidePrice(sku.getPopSku().getChainPrice());
        dto.setApprovalNumber(sku.getPopSku().getApprovalNumber());
        dto.setProducer(sku.getPopSku().getProducer());
        dto.setProductionDate(sku.getPopSku().getProductionDate());
        dto.setSuggestPrice(sku.getPopSku().getSuggestPrice());

        dto.setZjm(sku.getPopSku().getZjm());

        dto.setDrugClassification(sku.getPopSku().getDrugClassification());

        dto.setDosageForm(sku.getPopSku().getDosageForm());
        dto.setPieceLoading(sku.getPopSku().getPieceLoading());
        dto.setShelfLife(sku.getPopSku().getShelfLife());
        dto.setTerm(sku.getPopSku().getTerm());

        dto.setMediumPackageNum(sku.getPopSku().getMediumPackageNum());
        dto.setProductUnit(sku.getPopSku().getProductUnit());
        dto.setIsSplit(sku.getPopSku().getIsSplit());
        dto.setNearEffect(sku.getPopSku().getNearEffect());
        dto.setFarEffect(sku.getPopSku().getFarEffect());

        dto.setIsNew(sku.getPopSku().getIsNew());
        dto.setIsFragileGoods(sku.getPopSku().getIsFragileGoods());
        dto.setSkuCategory(sku.getPopSku().getSkuCategory());
        dto.setIsThirdCompany(1);
        dto.setStatus(sku.getPopSku().getStatus());
        if(sku.getPopSkuCategory()!=null){
            dto.setErpFirstCategoryId(NumberUtils.toLong(sku.getPopSkuCategory().getBusinessFirstCategoryCode()));
            dto.setErpSecondCategoryId(NumberUtils.toLong(sku.getPopSkuCategory().getBusinessSecondCategoryCode()));
            dto.setErpThirdCategoryId(NumberUtils.toLong(sku.getPopSkuCategory().getBusinessThirdCategoryCode()));
            dto.setErpFourCategoryId(NumberUtils.toLong(sku.getPopSkuCategory().getBusinessFourthCategoryCode()));
        }

        if(sku.getPopSku().getUseStandardImage()){
            String standardImageUrl = sku.getPopSku().getStandardImageUrl();
            List<String> list = Lists.newArrayList(standardImageUrl.split(","));
            dto.setImageUrl(list.remove(0));
            dto.setImagesList(list);
        }else{
            dto.setImagesList(join(sku.getPopSku().getImageListUrl()));
            dto.setImageUrl(sku.getPopSku().getImageUrl());
        }

        dto.setStorageCondition(sku.getPopSku().getStorageCondition());

        dto.setIndication(sku.getPopSkuInstruction().getIndication());
        dto.setUntowardEffect(sku.getPopSkuInstruction().getUntowardEffect());
        dto.setUsageAndDosage(sku.getPopSkuInstruction().getUsageAndDosage());
        dto.setConsiderations(sku.getPopSkuInstruction().getConsiderations());
        dto.setInteraction(sku.getPopSkuInstruction().getInteraction());
        dto.setComponent(sku.getPopSkuInstruction().getComponent());
        dto.setAbstain(sku.getPopSkuInstruction().getAbstain());
        dto.setSubtitle(sku.getPopSkuInstruction().getSubtitle());
        dto.setTechnicalRequirementNo(sku.getPopSkuInstruction().getTechnicalRequirementNo());
        dto.setCsuRegisterCertificateImageList(convertToCertImageDto(sku.getPopSkuInstruction().getInstrumentLicenseImage()));
        dto.setManufacturingLicenseNo(sku.getPopSkuInstruction().getManufacturingLicenseNo());
        dto.setMarketAuthor(sku.getPopSkuInstruction().getMarketAuthor());
        dto.setAliasName(sku.getPopSkuInstruction().getAliasName());

        if (sku.getPopSkuInstructionImages()!=null) {
            dto.setSkuInstructionImageList(convertToImageDto(sku, sku.getPopSkuInstructionImages()));
        }
        dto.setStandardProductId(sku.getPopSku().getStandardProductId());
        dto.setAvailableQty(sku.getPopSku().getStock());
        dto.setSellingProposition1(sku.getPopSku().getSellingProposition1());
        dto.setSellingProposition2(sku.getPopSku().getSellingProposition2());
        dto.setSellingProposition3(sku.getPopSku().getSellingProposition3());
        dto.setBranchCode(cor.getBranchCode());
        dto.setProductOwnerId(cor.getProductOwnerId());
        dto.setProductCode(sku.getPopSku().getProductCode());

        dto.setImagesListStr(StringUtils.join(dto.getImagesList(),","));
        PopSkuInstructionDto ins = sku.getPopSkuInstruction();
        dto.setUseParentSalesArea(ins.getIsCopyBusArea());
        dto.setUseParentMerchantGroup(ins.getIsCopyControlRoster());
        dto.setUseParentUserType(ins.getIsCopyControlUser());

        boolean isActivity = Objects.equals(sku.getPopSku().getActivityType(),ActivityTypeEnum.GROUP.getCode());
        //控销组信息
        boolean noGroup = (isActivity&&Objects.equals(ins.getIsCopyControlRoster(),COPY_YES)) || Objects.equals(sku.getPopSkuInstruction().getControlRosterType(),PopControlRosterType.NO.getValue());
        if(!noGroup){
            SkuMerchantGroupRelationDTO merchantGroupRelationDTO = new SkuMerchantGroupRelationDTO();
            merchantGroupRelationDTO.setRosterType(ins.getControlRosterType());
            merchantGroupRelationDTO.setGroupId(ins.getControlGroupId());
            dto.setSkuMerchantGroupRelationDTO(merchantGroupRelationDTO);
        }
        if(!(isActivity&&Objects.equals(ins.getIsCopyControlUser(),COPY_YES))){
            dto.setUserType(ins.getControlUserTypes());
        }
        //控销组信息
        if((isActivity&&Objects.equals(ins.getIsCopyBusArea(),COPY_YES))||Objects.equals(sku.getPopSku().getActivityType(), ActivityTypeEnum.GIFT.getCode())){
            return dto;
        }
        dto.setSalesAreaList(areas);
        return dto;
    }

    private static List<CsuRegisterCertificateImageDTO> convertToCertImageDto(String instrumentLicenseImage) {
        if(StringUtils.isEmpty(instrumentLicenseImage)){
            return new ArrayList<>();
        }
        return Arrays.stream(instrumentLicenseImage.split(",")).map(item->{
                CsuRegisterCertificateImageDTO dto = new CsuRegisterCertificateImageDTO();
                dto.setImageUrl(item);
                return dto;
        }).collect(Collectors.toList());
    }

    private static List<String> join(String imageListUrl) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(imageListUrl)) {
            return new ArrayList<>(0);
        }
        return Arrays.asList(imageListUrl.split(","));
    }

    private static List<CsuInstructionImageDTO> convertToImageDto(PopSkuDetailDto sku, PopSkuInstructionImageDto item) {
        if (item == null) {
            return new ArrayList<>(0);
        }
        String url = sku.getPopSku().getUseStandardImage()?item.getStandardInstrutionImageUrl():item.getInstrutionImageUrl();
        if (org.apache.commons.lang3.StringUtils.isBlank(url)) {
            return new ArrayList<>(0);
        }
        String[] urls = url.split(",");
        return Arrays.stream(urls).map(subUrl -> {
            CsuInstructionImageDTO dto = new CsuInstructionImageDTO();
            dto.setBarcode(sku.getPopSku().getBarcode());
            dto.setInstrutionImageUrl(subUrl);
            return dto;
        }).collect(Collectors.toList());

    }


    public static List<SkuPopDTO> convertToEcSkus(List<PopSkuDetailDto> activitySkus, Map<Long, List<Integer>> areaMap, CorporationDto cor) {
        return activitySkus.stream().map(item->convertToEcSku(item,areaMap.get(item.getBusAreaProductRelation().getBusAreaId()),cor)).collect(Collectors.toList());
    }
}
