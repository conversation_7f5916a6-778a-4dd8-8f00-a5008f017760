package com.xyy.ec.pop.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.server.api.seller.param.PopReceivingReportParam;
import com.xyy.ec.pop.vo.PopReceivingReportVo;

import java.math.BigDecimal;

public interface ReceivingReportService {

    PageInfo<PopReceivingReportVo> popReceivingReportList(PopReceivingReportParam receivingReportParam);

    void syncMerchant();

    BigDecimal popReceivingReportSummation(PopReceivingReportParam receivingReportParam);
}
