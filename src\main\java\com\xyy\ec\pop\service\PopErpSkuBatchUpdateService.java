package com.xyy.ec.pop.service;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.vo.BatchUpdateResultVo;
import com.xyy.ec.pop.vo.ErpSkuBatchUpdateVo;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @project_name: xyy-ec-pop-service
 * @Auther: Jincheng.Li
 * @Date: 2021/04/19/14:58
 * @Description:
 */
public interface PopErpSkuBatchUpdateService {

    /**
     * 批量修改erp商品信息
     * @param user
     * @param vos
     * @return
     * @throws ServiceException
     * <AUTHOR>
     */
    BatchUpdateResultVo batchUpdateErpSku(String user, List<ErpSkuBatchUpdateVo> vos) throws ServiceException;

}
