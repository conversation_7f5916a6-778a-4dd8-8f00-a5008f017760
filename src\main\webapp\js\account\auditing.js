$(document).ready(function () {
    var $form = $('form');
    var index = top.layer.getFrameIndex(window.name);

    //审核
    $("#btn_save").click(function () {

        var auditingState = $("input[name='auditingState']:checked").val();
        var remark = $("#remark").val();
        if(auditingState == 3 && !remark){
            parent.layer.msg("请输入不通过原因");
            return false;
        }
        if ($form.valid()) {
            $.ajax({
                type: 'post',
                cache: false,
                url: basePath + 'accountStatement/auditing',
                data: $form.serialize(),
                dataType: 'json',
                success: function (data) {
                    if (data.status == "success") {
                        parent.layer.msg('操作成功');
                        top.layer.close(index);
                    } else {
                        parent.layer.msg(data.errorMsg);
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    parent.layer.msg('网络异常');
                }
            });
        }

        return false;
    });

    //取消
    $("#btn_cancel").click(function () {
        top.layer.close(index);
    });
});