package com.xyy.ec.pop.controller.mop;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.adapter.mop.MerchantManagerAdapter;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.dto.mop.MopMerchantBatchBindPoiDTO;
import com.xyy.ec.pop.dto.mop.MopMerchantBatchBindYyDTO;
import com.xyy.ec.pop.dto.mop.MopMerchantBatchBindZsDTO;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.utils.easyexcel.EasyExcelUtil;
import com.xyy.ec.pop.utils.mop.MopDataFillerUtils;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.common.enumerate.MopMerchantBindTypeEnum;
import com.xyy.pop.mop.api.remote.parameter.MeSysUserParame;
import com.xyy.pop.mop.api.remote.parameter.MerchantBindParameter;
import com.xyy.pop.mop.api.remote.parameter.MerchantErpJointParameter;
import com.xyy.pop.mop.api.remote.parameter.MerchantLocationParameter;
import com.xyy.pop.mop.api.remote.parameter.MerchantManagerPageParameter;
import com.xyy.pop.mop.api.remote.parameter.MerchantPoiParameter;
import com.xyy.pop.mop.api.remote.parameter.MerchantSuccessEnterTimeParameter;
import com.xyy.pop.mop.api.remote.result.MeSysUserDto;
import com.xyy.pop.mop.api.remote.result.MerchantBindPoiDTO;
import com.xyy.pop.mop.api.remote.result.MerchantManagerPageDTO;
import com.xyy.scm.constant.entity.pagination.Paging;
import com.xyy.scm.constant.foundation.GeneralEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 商家运营-商户管理(新加入参、枚举翻译、导出)
 *
 * @author: duHao
 * @since: 11:38 2024/12/4
 */
@Slf4j
@RequestMapping("/mop/merchant/manager")
@RestController
public class MerchantManagerController extends BaseController implements InitializingBean {

    /**
     * 绑定招商/运营/poi 配置
     * 外层key:绑定类型 com.xyy.pop.mop.api.common.enumerate.MopMerchantBindTypeEnum
     * 内层key:表头下标 value:表头集合
     */
//    private static final Map<String, Map<Integer, List<String>>> BIND_TEMPLATE_HEADER_MAP = new ConcurrentHashMap<>();
    private static final Map<String, Class> BIND_TEMPLATE_CLASS_MAP = new ConcurrentHashMap<>();

    @Autowired
    private MerchantManagerAdapter merchantManagerAdapter;
    @Autowired
    private DownloadRemote downloadRemote;

    /**
     * 绑定招商/运营用户查询
     *
     * @param param
     * @return
     */
    @PostMapping("/bind/user/list")
    public ResponseVo<List<MeSysUserDto>> getBindUser(@RequestBody MeSysUserParame param) {
        return merchantManagerAdapter.getUserList(param);
    }

    /**
     * 换绑门店查询
     *
     * @param param
     * @return
     */
    @PostMapping("/bind/poi/list")
    @AvoidRepeatableCommit
    public ResponseVo<List<MerchantBindPoiDTO>> getBindPoi(@RequestBody MerchantPoiParameter param) {
        return merchantManagerAdapter.getBindPoiList(param);
    }

    /**
     * 商户管理-列表
     *
     * @param param
     * @return
     */
    @PostMapping("/page")
    public ResponseVo<Paging<MerchantManagerPageDTO>> page(@RequestBody MerchantManagerPageParameter param) {
        param.setAccountId(getUser().getJobNumber());
        return merchantManagerAdapter.queryMerchantPageByParam(param);
    }

    /**
     * 商户绑定招商
     *
     * @param param
     * @return
     */
    @PostMapping("/bind/zs")
    public ResponseVo bindZs(@RequestBody MerchantBindParameter param) {
        MopDataFillerUtils.fillData(getUser(),param,true);
        return merchantManagerAdapter.bindZs(param);
    }

    /**
     * 商户绑定运营
     *
     * @param param
     * @return
     */
    @PostMapping("/bind/yy")
    public ResponseVo bindYy(@RequestBody MerchantBindParameter param) {
        MopDataFillerUtils.fillData(getUser(),param,true);
        return merchantManagerAdapter.bindYy(param);
    }

    /**
     * 商户换绑门店
     *
     * @param param
     * @return
     */
    @PostMapping("/bind/poi")
    public ResponseVo bindPoi(@RequestBody MerchantBindParameter param) {
        MopDataFillerUtils.fillData(getUser(),param,true);
        return merchantManagerAdapter.bindPoi(param);
    }


    /**
     * 设置店铺成功入驻时间
     *
     * @param param
     * @return
     */
    @PostMapping("/success/enter")
    public ResponseVo setMerchantSuccessEnterTime(@RequestBody MerchantSuccessEnterTimeParameter param) {
        MopDataFillerUtils.fillData(getUser(),param,true);
        return merchantManagerAdapter.setMerchantSuccessEnterTime(param);
    }

    /**
     * ERP暂停对接
     *
     * @param param
     * @return
     */
    @PostMapping("/erp/joint/pause")
    public ResponseVo erpJointPause(@RequestBody MerchantErpJointParameter param) {
        MopDataFillerUtils.fillData(getUser(),param,true);
        return merchantManagerAdapter.erpJointPause(param);
    }

    /**
     * 修改经纬度信息
     *
     * @param param
     * @return
     */
    @PostMapping("/update/location")
    public ResponseVo updateLocation(@RequestBody MerchantLocationParameter param) {
        MopDataFillerUtils.fillData(getUser(),param,true);
        return merchantManagerAdapter.updateLocation(param);
    }

    /**
     * ERP对接完成
     *
     * @param param
     * @return
     */
    @PostMapping("/erp/joint/finish")
    public ResponseVo erpJointFinish(@RequestBody MerchantErpJointParameter param) {
        MopDataFillerUtils.fillData(getUser(),param,true);
        return merchantManagerAdapter.erpJointFinish(param);
    }

    /**
     * Excel批量绑定招商
     *
     * @param file
     * @return
     */
    @PostMapping("/batch/bind/zs")
    public ResponseVo batchBindZs(@RequestParam("file") MultipartFile file) {
        return merchantManagerAdapter.batchBindZs(file, getUser());
    }

    /**
     * Excel批量绑定运营
     *
     * @param file
     * @return
     */
    @PostMapping("/batch/bind/yy")
    public ResponseVo batchBindYy(@RequestParam("file") MultipartFile file) {
        return merchantManagerAdapter.batchBindYy(file, getUser());
    }

    /**
     * Excel批量换绑门店
     *
     * @param file
     * @return
     */
    @PostMapping("/batch/bind/poi")
    public ResponseVo batchBindPoi(@RequestParam("file") MultipartFile file) {
        return merchantManagerAdapter.batchBindPoi(file, getUser());
    }

    /**
     * 导出不含电话
     *
     * @param query
     */
    @PostMapping(value = "/export/include/tel")
    public ResponseVo<Boolean> exportIncludeTel(@RequestBody MerchantManagerPageParameter query) {
        try {
            log.info("exportIncludeTel:{}", JSON.toJSONString(query));
            query.setAccountId(getUser().getJobNumber());
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.MOP_MERCHANT_INCLUDE_TEL_LIST_EXPORT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("exportIncludeTel result:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        } catch (Exception e) {
            log.error("exportIncludeTel error:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出不含电话
     *
     * @param query
     */
    @PostMapping(value = "/export/exclude/tel")
    public ResponseVo<Boolean> exportExcludeTel(@RequestBody MerchantManagerPageParameter query) {
        try {
            log.info("exportExcludeTel:{}", JSON.toJSONString(query));
            query.setAccountId(getUser().getJobNumber());
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.MOP_MERCHANT_EXCLUDE_TEL_LIST_EXPORT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("exportExcludeTel result:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        } catch (Exception e) {
            log.error("exportExcludeTel error:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 下载批量导入招商/运营/POI模板
     *
     * @param bindType
     * @param response
     */
    @GetMapping(value = "/downloadTemplate")
    public void downloadTemplate(@RequestParam String bindType, HttpServletResponse response) {
        MopMerchantBindTypeEnum bindTypeEnum = GeneralEnum.of(bindType, MopMerchantBindTypeEnum.class);
        if (Objects.nonNull(bindTypeEnum)) {
            Class templateClass = BIND_TEMPLATE_CLASS_MAP.get(bindTypeEnum.getCode());
            Optional.ofNullable(templateClass).ifPresent(e -> EasyExcelUtil.writeExcel(response, bindTypeEnum.getMsg(), new ArrayList<>(), e, bindTypeEnum.getMsg()));
        }

//        Workbook workbook = new XSSFWorkbook();
//        Sheet sheet = workbook.createSheet(bindTypeEnum.getMsg());
//        Map<Integer, List<String>> bindTemplateHeaderMap = BIND_TEMPLATE_HEADER_MAP.get(bindType);
//        for (Map.Entry<Integer, List<String>> entry : bindTemplateHeaderMap.entrySet()) {
//            Integer headerIndex = entry.getKey();
//            List<String> headColumnList = entry.getValue();
//            Row headRow = sheet.createRow(headerIndex);
//            headRow.setHeight((short) ((1.6 + 0.72) * 256));
//            for (String headColumn : headColumnList) {
//                Cell headRowCell = headRow.createCell(headColumnList.indexOf(headColumn), CellType.STRING);
//                headRowCell.setCellStyle(EasyExcelUtil.generateTemplateDefaultStyle(sheet.getWorkbook()));
//                headRowCell.setCellValue(headColumn);
//            }
//        }
//
//        String fileName = bindTypeEnum.getMsg() + ".xlsx";
//        ServletOutputStream outputStream = null;
//        try {
//            response.setContentType("application/binary;charset=UTF-8");
//            outputStream = response.getOutputStream();
//            response.setContentType("application/vnd.ms-excel");
//            response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes(), "ISO8859-1"));
//            workbook.write(outputStream);
//        } catch (IOException e) {
//            log.error("MerchantManagerController.downloadTemplate error:", e);
//        } finally {
//            try {
//                if (Objects.nonNull(outputStream)) {
//                    outputStream.flush();
//                    outputStream.close();
//                }
//            } catch (IOException e) {
//                log.error("MerchantManagerController.downloadTemplate close io error:", e);
//            }
//        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        Map<Integer, List<String>> bindZsHeaderMap = new LinkedHashMap<>(2);
//        bindZsHeaderMap.putIfAbsent(0, Lists.newArrayList("poiId", "商户编号", "招商工号"));
//        BIND_TEMPLATE_HEADER_MAP.put(MopMerchantBindTypeEnum.ZS.getCode(), bindZsHeaderMap);
//
//        Map<Integer, List<String>> bindYyHeaderMap = new LinkedHashMap<>(2);
//        bindYyHeaderMap.putIfAbsent(0, Lists.newArrayList("poiId", "商户编号", "运营工号"));
//        BIND_TEMPLATE_HEADER_MAP.put(MopMerchantBindTypeEnum.YY.getCode(), bindYyHeaderMap);
//
//        Map<Integer, List<String>> bindPoiHeaderMap = new LinkedHashMap<>(2);
//        bindPoiHeaderMap.putIfAbsent(0, Lists.newArrayList("poiId", "商户编号"));
//        BIND_TEMPLATE_HEADER_MAP.put(MopMerchantBindTypeEnum.POI.getCode(), bindPoiHeaderMap);

        BIND_TEMPLATE_CLASS_MAP.put(MopMerchantBindTypeEnum.ZS.getCode(), MopMerchantBatchBindZsDTO.class);
        BIND_TEMPLATE_CLASS_MAP.put(MopMerchantBindTypeEnum.YY.getCode(), MopMerchantBatchBindYyDTO.class);
        BIND_TEMPLATE_CLASS_MAP.put(MopMerchantBindTypeEnum.POI.getCode(), MopMerchantBatchBindPoiDTO.class);
    }


}
