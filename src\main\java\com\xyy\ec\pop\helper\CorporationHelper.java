package com.xyy.ec.pop.helper;


import com.xyy.ec.pop.server.api.merchant.dto.CorporationBusinessDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationQualificationDto;
import com.xyy.ec.pop.server.api.merchant.dto.ErpApprovalModelDto;
import com.xyy.ec.pop.vo.corporation.CorporationBusinessVo;
import com.xyy.ec.pop.vo.corporation.CorporationQualificationVo;
import com.xyy.ec.pop.vo.corporation.CorporationVo;
import com.xyy.ec.pop.vo.corporation.ErpApprovalModelVo;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collector;
import java.util.stream.Collectors;

public class CorporationHelper {

    public static CorporationVo convertToVo(CorporationDto data) {
        CorporationVo corporationVo = new CorporationVo();
        corporationVo.setId(data.getId());
        corporationVo.setOrgId(data.getOrgId());
        corporationVo.setShopCode(data.getShopCode());
        corporationVo.setName(data.getName());
        corporationVo.setCompanyName(data.getCompanyName());
        corporationVo.setRegCode(data.getRegCode());
        corporationVo.setCorporat(data.getCorporat());
        corporationVo.setPhone(data.getPhone());
        corporationVo.setFixedPhone(data.getFixedPhone());
        corporationVo.setEmail(data.getEmail());
        corporationVo.setWeb(data.getWeb());
        corporationVo.setProvId(data.getProvId());
        corporationVo.setProv(data.getProv());
        corporationVo.setCityId(data.getCityId());
        corporationVo.setCity(data.getCity());
        corporationVo.setAreaId(data.getAreaId());
        corporationVo.setArea(data.getArea());
        corporationVo.setAddr(data.getAddr());
        corporationVo.setLogoUrl(data.getLogoUrl());
        corporationVo.setBrief(data.getBrief());
        corporationVo.setRemarks(data.getRemarks());
        corporationVo.setState(data.getState());
        corporationVo.setDel(data.getDel());
        corporationVo.setCreateTime(data.getCreateTime());
        corporationVo.setCreateId(data.getCreateId());
        corporationVo.setCreateName(data.getCreateName());
        corporationVo.setUpdateTime(data.getUpdateTime());
        corporationVo.setUpdateId(data.getUpdateId());
        corporationVo.setUpdateName(data.getUpdateName());
        corporationVo.setSearch(data.getSearch());
        corporationVo.setCorporationType(data.getCorporationType());
        corporationVo.setCustomerServicePhone(data.getCustomerServicePhone());
        corporationVo.setErpProcessId(data.getErpProcessId());
        corporationVo.setDrugsArea(CorporationAreaConvertHelper.convertToVo(data.getDrugsArea()));
        corporationVo.setNonDrugArea(CorporationAreaConvertHelper.convertToVo(data.getNonDrugArea()));
        corporationVo.setRejectTips(data.getRejectTips());
        corporationVo.setStreetId(data.getStreetId());
        corporationVo.setStreetName(data.getStreetName());
        corporationVo.setErpNumber(data.getErpNumber());
        corporationVo.setStatus(data.getStatus());
        corporationVo.setErpApprovalModelDto(convertToErpVo(data.getErpApprovalModelDto()));
        corporationVo.setCorporationBusiness(convertToBusinessVos(data.getCorporationBusiness()));
        corporationVo.setCorporationQualifications(convertToQualifications(data.getCorporationQualifications()));
        corporationVo.setSettlementType(data.getSettlementType());
        corporationVo.setSupplyCustomerType(null);
        return corporationVo;
    }

    private static List<CorporationQualificationVo> convertToQualifications(List<CorporationQualificationDto> corporationQualifications) {
        if(CollectionUtils.isEmpty(corporationQualifications)){
            return new ArrayList<>();
        }
        return corporationQualifications.stream().map(item->convertToQualification(item)).collect(Collectors.toList());
    }

    private static CorporationQualificationVo convertToQualification(CorporationQualificationDto item) {
        CorporationQualificationVo corporationQualificationVo = new CorporationQualificationVo();
        corporationQualificationVo.setId(item.getId());
        corporationQualificationVo.setCId(item.getCId());
        corporationQualificationVo.setName(item.getName());
        corporationQualificationVo.setCode(item.getCode());
        corporationQualificationVo.setStartDate(item.getStartDate());
        corporationQualificationVo.setEndDate(item.getEndDate());
        corporationQualificationVo.setUrl(item.getUrl());
        corporationQualificationVo.setBankName(item.getBankName());
        corporationQualificationVo.setSubBankName(item.getSubBankName());
        corporationQualificationVo.setLegalPersonName(item.getLegalPersonName());
        corporationQualificationVo.setRemarks(item.getRemarks());
        corporationQualificationVo.setState(item.getState());
        corporationQualificationVo.setDel(item.getDel());
        corporationQualificationVo.setCreateTime(item.getCreateTime());
        corporationQualificationVo.setCreateId(item.getCreateId());
        corporationQualificationVo.setCreateName(item.getCreateName());
        corporationQualificationVo.setUpdateTime(item.getUpdateTime());
        corporationQualificationVo.setUpdateId(item.getUpdateId());
        corporationQualificationVo.setUpdateName(item.getUpdateName());
        corporationQualificationVo.setQualificationsDetailId(item.getQualificationsDetailId());
        corporationQualificationVo.setLongTerm(item.getLongTerm());
        return corporationQualificationVo;
    }

    private static List<CorporationBusinessVo> convertToBusinessVos(List<CorporationBusinessDto> corporationBusiness) {
        if(CollectionUtils.isEmpty(corporationBusiness)){
            return new ArrayList<>();
        }
        return corporationBusiness.stream().map(item->convertToBusinessVo(item)).collect(Collectors.toList());
    }

    private static CorporationBusinessVo convertToBusinessVo(CorporationBusinessDto item) {
        CorporationBusinessVo corporationBusinessVo = new CorporationBusinessVo();
        corporationBusinessVo.setId(item.getId());
        corporationBusinessVo.setCId(item.getCId());
        corporationBusinessVo.setCategoryId(item.getCategoryId());
        corporationBusinessVo.setPId(item.getPId());
        corporationBusinessVo.setLevel(item.getLevel());
        corporationBusinessVo.setName(item.getName());
        corporationBusinessVo.setCompanyName(item.getCompanyName());
        corporationBusinessVo.setBrief(item.getBrief());
        corporationBusinessVo.setState(item.getState());
        corporationBusinessVo.setCreateTime(item.getCreateTime());
        corporationBusinessVo.setCreateId(item.getCreateId());
        corporationBusinessVo.setCreateName(item.getCreateName());
        return corporationBusinessVo;

    }

    public static ErpApprovalModelVo convertToErpVo(ErpApprovalModelDto erpApprovalModelDto) {
        if(erpApprovalModelDto==null){
            return null;
        }
        ErpApprovalModelVo erpApprovalModelVo = new ErpApprovalModelVo();
        erpApprovalModelVo.setId(erpApprovalModelDto.getId());
        erpApprovalModelVo.setNumber(erpApprovalModelDto.getNumber());
        erpApprovalModelVo.setProcessInstanceId(erpApprovalModelDto.getProcessInstanceId());
        erpApprovalModelVo.setBusinessKey(erpApprovalModelDto.getBusinessKey());
        erpApprovalModelVo.setInitiatorId(erpApprovalModelDto.getInitiatorId());
        erpApprovalModelVo.setNodeName(erpApprovalModelDto.getNodeName());
        erpApprovalModelVo.setHandStatus(erpApprovalModelDto.getHandStatus());
        erpApprovalModelVo.setNodeId(erpApprovalModelDto.getNodeId());
        erpApprovalModelVo.setTaskId(erpApprovalModelDto.getTaskId());
        return erpApprovalModelVo;

    }
}
