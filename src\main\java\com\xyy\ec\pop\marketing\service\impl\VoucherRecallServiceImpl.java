package com.xyy.ec.pop.marketing.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.shop.activity.api.CouponRecallApi;
import com.xyy.ec.marketing.shop.activity.api.ShopCouponAdminApi;
import com.xyy.ec.marketing.shop.activity.dto.*;
import com.xyy.ec.marketing.shop.activity.enums.MarketingShopCouponStatusEnum;
import com.xyy.ec.marketing.shop.activity.enums.MarketingShopTypeEnum;
import com.xyy.ec.marketing.shop.activity.params.ShopCouponQueryParam;
import com.xyy.ec.marketing.shop.activity.params.ShopCouponUserQueryParam;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.marketing.helpers.CouponRecallRecordHelper;
import com.xyy.ec.pop.marketing.param.CouponRecallFailParam;
import com.xyy.ec.pop.marketing.service.VoucherRecallService;
import com.xyy.ec.pop.marketing.vo.CouponInstanceVO;
import com.xyy.ec.pop.marketing.vo.CouponRecallRecordVO;
import com.xyy.ec.pop.marketing.vo.CouponRecallResultVo;
import com.xyy.ec.pop.marketing.vo.CouponTemplateVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class VoucherRecallServiceImpl implements VoucherRecallService {
    @Reference(version = "1.0.0")
    private CouponRecallApi couponRecallApi;
    @Reference(version = "1.0.0")
    private ShopCouponAdminApi shopCouponAdminApi;
    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Override
    public PageInfo<CouponRecallRecordVO> pageInfo(CouponRecallRecordReqDTO recordReqDTO) {
        if (recordReqDTO == null) {
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "参数错误");
        }
        ApiRPCResult<PageInfo<CouponRecallRecordDTO>> rpcResult = couponRecallApi.selectRecallList(recordReqDTO);
        if (!rpcResult.isSuccess() || rpcResult.getData() == null) {
            return new PageInfo<>();
        }
        PageInfo<CouponRecallRecordDTO> rpcResultData = rpcResult.getData();
        List<CouponRecallRecordDTO> rpcList = rpcResultData.getList();
        if (CollectionUtils.isEmpty(rpcList)) {
            return new PageInfo<>();
        }
        List<CouponRecallRecordVO> recallRecordVOS = rpcList.stream().map(CouponRecallRecordHelper::createCouponRecallRecordVO).collect(Collectors.toList());
        PageInfo<CouponRecallRecordVO> retPageInfo = new PageInfo<>();
        retPageInfo.setPageNum(rpcResultData.getPageNum());
        retPageInfo.setPageSize(rpcResultData.getPageSize());
        retPageInfo.setTotal(rpcResultData.getTotal());
        retPageInfo.setList(recallRecordVOS);
        return retPageInfo;
    }

    @Override
    public PageInfo<CouponTemplateVO> selectVoucherTemplate(Long templateId, String templateName, Integer pageNum, Integer pageSize) {
        if ((templateId == null && StringUtils.isEmpty(templateName)) || pageNum == null || pageSize == null) {
            return new PageInfo<>();
        }
        ShopCouponQueryParam queryParam = ShopCouponQueryParam.builder()
                .shopType(MarketingShopTypeEnum.POP_SHOP.getType())
                .id(templateId)
                .name(templateName)
                .build();
        ApiRPCResult<PageInfo<ShopCouponDTO>> apiRpcResult = shopCouponAdminApi.listShopCouponsForRecall(queryParam,
                pageNum, pageSize);
        if (!apiRpcResult.isSuccess() || apiRpcResult.getData() == null) {
            return new PageInfo<>();
        }
        PageInfo<ShopCouponDTO> resultData = apiRpcResult.getData();
        List<ShopCouponDTO> resultDataList = resultData.getList();
        if (CollectionUtils.isEmpty(resultDataList)) {
            return new PageInfo<>();
        }
        List<CouponTemplateVO> templateVOS = resultDataList.stream().map(CouponRecallRecordHelper::createCouponTemplateVO).collect(Collectors.toList());
        PageInfo<CouponTemplateVO> pageInfo = new PageInfo<>();
        pageInfo.setList(templateVOS);
        pageInfo.setTotal(resultData.getTotal());
        pageInfo.setPageNum(resultData.getPageNum());
        pageInfo.setPageSize(resultData.getPageSize());
        return pageInfo;
    }

    @Override
    public Integer getRecallTotalNum(Long templateId) {
        if (templateId == null) {
            return 0;
        }
        ApiRPCResult<Integer> rpcResult = shopCouponAdminApi.selectUnUsedInstanceByTemplateId(templateId);
        if (!rpcResult.isSuccess()) {
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, rpcResult.getErrMsg());
        }
        return rpcResult.getData();
    }

    @Override
    public CouponRecallResultVo recallVoucher(Long templateId, String username) {
        if (templateId == null) {
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "请选择优惠券模板");
        }
        ApiRPCResult<CouponRecallResultDTO> rpcResult = couponRecallApi.recallVoucher(templateId, username);
        if (!rpcResult.isSuccess()) {
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, rpcResult.getErrMsg());
        }
        CouponRecallResultDTO resultData = rpcResult.getData();
        if (resultData == null){
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "召回失败");
        }

        CouponRecallResultVo couponRecallResultVo = new CouponRecallResultVo();
        couponRecallResultVo.setSuccessNum(resultData.getSuccessNum());

        Map<Long, String> failInfoMap = resultData.getFailInfoMap();
        if (MapUtils.isNotEmpty(failInfoMap)){
            List<CouponRecallFailParam> failParamList = Lists.newArrayList();
            for (Long voucherId : failInfoMap.keySet()) {
                String failReason = failInfoMap.get(voucherId);
                CouponRecallFailParam failParam = CouponRecallFailParam.builder()
                        .id(voucherId)
                        .failReason(failReason)
                        .build();
                failParamList.add(failParam);
            }
            Map<String, List<CouponRecallFailParam>> groupByFailReason = failParamList.stream().collect(Collectors.groupingBy(CouponRecallFailParam::getFailReason));
            StringBuilder stringBuilder = new StringBuilder();
            for (String failReason : groupByFailReason.keySet()) {
                List<CouponRecallFailParam> failParams = groupByFailReason.get(failReason);
                List<Long> voucherIdList = failParams.stream().map(CouponRecallFailParam::getId).collect(Collectors.toList());
                stringBuilder.append("优惠券ID").append("【").append(StringUtils.join(voucherIdList, ",")).append("】").append("召回失败,失败原因为:").append(failReason).append(";");
            }
            couponRecallResultVo.setFailStr(stringBuilder.toString());
            couponRecallResultVo.setFailNum(failInfoMap.size());
        }
        return couponRecallResultVo;
    }

    @Override
    public CouponRecallResultVo recallVoucherInstance(String voucherInstanceIdStr, String username) {
        if (StringUtils.isEmpty(voucherInstanceIdStr)) {
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "请选择要召回的优惠券");
        }
        if (StringUtils.isEmpty(username)) {
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "获取操作人失败");
        }
        CouponRecallResultVo couponRecallResultVo = new CouponRecallResultVo();
        StringBuilder failReasonStringBuilder = new StringBuilder();

        List<String> voucherIdStrList = Lists.newArrayList(voucherInstanceIdStr.split(","));
        List<Long> voucherIdList = voucherIdStrList.stream().filter(NumberUtils::isDigits).map(Long::parseLong).collect(Collectors.toList());
        List<String> notDigitsList = voucherIdStrList.stream().filter(idStr -> !NumberUtils.isDigits(idStr)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(notDigitsList)) {
            failReasonStringBuilder.append("优惠券ID").append("【").append(StringUtils.join(notDigitsList, ",")).append("】").append("召回失败,失败原因为:").append("优惠券ID格式不正确").append(";");
        }
        if (CollectionUtils.isEmpty(voucherIdList)) {
            couponRecallResultVo.setSuccessNum(voucherIdList.size());
            couponRecallResultVo.setFailStr(failReasonStringBuilder.toString());
            return couponRecallResultVo;
        }

        ApiRPCResult<CouponRecallResultDTO> rpcResult = couponRecallApi.recallVoucherByInstanceId(voucherIdList, username);
        if (!rpcResult.isSuccess()) {
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, rpcResult.getErrMsg());
        }
        CouponRecallResultDTO resultData = rpcResult.getData();
        if (resultData == null){
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "召回失败");
        }
        couponRecallResultVo.setSuccessNum(resultData.getSuccessNum());
        Map<Long, String> failInfoMap = resultData.getFailInfoMap();
        if (MapUtils.isNotEmpty(failInfoMap)){
            List<CouponRecallFailParam> failParamList = Lists.newArrayList();
            for (Long voucherId : failInfoMap.keySet()) {
                String failReason = failInfoMap.get(voucherId);
                CouponRecallFailParam failParam = CouponRecallFailParam.builder()
                        .id(voucherId)
                        .failReason(failReason)
                        .build();
                failParamList.add(failParam);
            }
            Map<String, List<CouponRecallFailParam>> groupByFailReason = failParamList.stream().collect(Collectors.groupingBy(CouponRecallFailParam::getFailReason));
            StringBuilder stringBuilder = new StringBuilder();
            for (String failReason : groupByFailReason.keySet()) {
                List<CouponRecallFailParam> failParams = groupByFailReason.get(failReason);
                List<Long> idList = failParams.stream().map(CouponRecallFailParam::getId).collect(Collectors.toList());
                stringBuilder.append("优惠券ID").append("【").append(StringUtils.join(idList, ",")).append("】").append("召回失败,失败原因为:").append(failReason).append(";");
            }
            couponRecallResultVo.setFailStr(stringBuilder.toString());
            couponRecallResultVo.setFailNum(failInfoMap.size());
        }
        return couponRecallResultVo;
    }

    @Override
    public PageInfo<CouponInstanceVO> selectVoucherInstance(Long templateId, String phone,
                                                            String merchantName, Long merchantId,
                                                            Integer pageNum, Integer pageSize) {
        if (templateId == null) {
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "券模板ID不能为空");
        }
        if (StringUtils.isEmpty(phone) && StringUtils.isEmpty(merchantName) && merchantId == null) {
            return new PageInfo<>();
        }
        pageNum = pageNum == null || pageNum <= 0 ? 1 : pageNum;
        pageSize = pageSize == null || pageSize <= 0 ? 10 : pageSize;

        MerchantBussinessDto merchantBussinessDto = new MerchantBussinessDto();
        merchantBussinessDto.setMobile(phone);
        merchantBussinessDto.setRealName(merchantName);
        merchantBussinessDto.setId(merchantId);
        List<MerchantBussinessDto> merchantInfoList =
                merchantBussinessApi.findMerchantInfoList(merchantBussinessDto);
        if (CollectionUtils.isEmpty(merchantInfoList)) {
            return new PageInfo<>();
        }
        List<Long> merchantIdList = merchantInfoList.stream().map(MerchantBussinessDto::getId).collect(Collectors.toList());
        ShopCouponUserQueryParam queryParam = ShopCouponUserQueryParam.builder()
                .merchantIds(merchantIdList)
                .couponId(templateId)
                .build();
        ApiRPCResult<PageInfo<ShopCouponUserDTO>> apiRPCResult = shopCouponAdminApi.listShopUserCouponsForPopAdmin(queryParam,
                pageNum, pageSize);
        if (!apiRPCResult.isSuccess()) {
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, apiRPCResult.getErrMsg());
        }
        PageInfo<ShopCouponUserDTO> resultData = apiRPCResult.getData();
        if (resultData == null || CollectionUtils.isEmpty(resultData.getList())) {
            return new PageInfo<>();
        }
        List<ShopCouponUserDTO> shopCouponUserDTOS = resultData.getList();
        List<CouponInstanceVO> retList = shopCouponUserDTOS.stream().map(dto -> {
            CouponInstanceVO couponInstanceVO = new CouponInstanceVO();
            couponInstanceVO.setId(dto.getCouponUserId());
            couponInstanceVO.setMerchantId(dto.getMerchantId());
            couponInstanceVO.setPhone(dto.getMobile());
            couponInstanceVO.setMerchantName(dto.getMerchantName());
            couponInstanceVO.setCouponStatusStr(MarketingShopCouponStatusEnum.valueOfCustom(dto.getState()).getName());
            couponInstanceVO.setValidDate(getValidDate(dto.getStartTime(), dto.getEndTime()));
            return couponInstanceVO;
        }).collect(Collectors.toList());

        PageInfo<CouponInstanceVO> retPageInfo = new PageInfo<>();
        retPageInfo.setList(retList);
        retPageInfo.setPageNum(resultData.getPageNum());
        retPageInfo.setPageSize(resultData.getPageSize());
        retPageInfo.setTotal(resultData.getTotal());
        return retPageInfo;
    }

    private String getValidDate(Date stime, Date etime) {
        if (stime == null || etime == null) {
            return null;
        }
        return DateFormatUtils.format(stime, "yyyy-MM-dd HH:mm:ss") + "-" + DateFormatUtils.format(etime, "yyyy-MM-dd HH:mm:ss");
    }

}
