package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.erp.api.PopErpTaskApi;
import com.xyy.ec.pop.server.api.erpUtil.dto.PopCorpTaskConfigDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PopErpTaskRemote {
    @Reference
    private PopErpTaskApi popErpTaskApi;

    public boolean hasConfig(String orgId) {
        try {
            log.info("PopErpTaskRemote.getTaskConfig#orgId:{}", JSON.toJSONString(orgId));
            ApiRPCResult<PopCorpTaskConfigDto> result = popErpTaskApi.getPopCorpTaskConfig(orgId);
            log.info("PopErpTaskRemote.getTaskConfig#orgId:{} return {}", JSON.toJSONString(orgId), JSON.toJSONString(result));
            return result.isSuccess()&&result.getData()!=null;
        } catch (Exception e) {
            log.error("PopErpTaskRemote.getTaskConfig#orgId:{} 异常", JSON.toJSONString(orgId), e);
        }
        return false;
    }
}
