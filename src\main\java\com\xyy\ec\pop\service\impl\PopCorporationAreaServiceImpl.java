package com.xyy.ec.pop.service.impl;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.CorporationAreaConvertHelper;
import com.xyy.ec.pop.remote.CorporationAreaRemoteAdapter;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationAreaSaleTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationAreaDto;
import com.xyy.ec.pop.service.PopCorporationAreaService;
import com.xyy.ec.pop.vo.CorporationAreaInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 区域
 * <AUTHOR>
 */
@Service
public class PopCorporationAreaServiceImpl implements PopCorporationAreaService {
    @Autowired
    private CorporationAreaRemoteAdapter remoteAdapter;
    @Override
    public List<CorporationAreaInfoVo> getCorporationArea(Long cid) throws ServiceException {
        List<CorporationAreaDto> dtos  = remoteAdapter.getCorporationArea(cid);
        return CorporationAreaConvertHelper.convertToVo(dtos);
    }

    @Override
    public List<CorporationAreaInfoVo> getCorporationAreaByCids(List<Long> cids) throws ServiceException {
        List<CorporationAreaDto> dtos  = remoteAdapter.getCorporationAreaByCids(cids);
        return CorporationAreaConvertHelper.convertToVo(dtos);
    }

    @Override
    public void updateCorporationArea(String orgId, List<CorporationAreaInfoVo> drugAreas, List<CorporationAreaInfoVo> nonDrugAreas, Map<Integer, String> map, String username, Long userId) throws ServiceException {
        List<CorporationAreaDto> dtos = CorporationAreaConvertHelper.convertToDto(drugAreas,map, CorporationAreaSaleTypeEnum.DRUG.getCode());
        dtos.addAll(CorporationAreaConvertHelper.convertToDto(nonDrugAreas,map,CorporationAreaSaleTypeEnum.NON_DRUG.getCode()));
        //数据去重
        dtos = dtos.stream().collect(Collectors.toMap(item->item.getSaleType()+"_"+item.getProvId(),item->item,(item1,item2)->item2.getId()!=null?item2:item1)).values().stream().collect(Collectors.toList());
        remoteAdapter.saveCorporationArea(orgId,dtos,username,userId);


    }


}
