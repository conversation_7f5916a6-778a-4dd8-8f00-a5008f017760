package com.xyy.ec.pop.excel.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**在线支付入账单明细
* <AUTHOR>
* @date  2020/12/14 13:38
* @table
*/
@Data
@NoArgsConstructor
public class OnlinePayPopBillPaymentDetailExportVo implements Serializable {

    @Excel(name = "商户编号",width = 15)
    private String orgId;

    @Excel(name = "商户名称",width = 15)
    private String orgName;
    @Excel(name = "店铺名称",width = 15)
    private String name;

    @Excel(name = "入账单号",width = 15)
    private String flowNo;

    @Excel(name = "入账单生成时间",width = 18,format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "账单号",width = 15)
    private String billNo;
    /**
     * 佣金结算方式，1：非月结；2：月结
     */
    @Excel(name = "佣金结算方式",width = 15,replace={"非月结_1","月结_2","_null"})
    private Byte settlementType;

    @Excel(name = "商品金额",width = 15)
    private BigDecimal productMoney;

    @Excel(name = "运费",width = 15)
    private BigDecimal freightAmount;

    @Excel(name = "单据总金额(含运费)",width = 15)
    private BigDecimal totalMoney;

    @Excel(name = "店铺总优惠",width = 15)
    private BigDecimal shopTotalDiscount;

    @Excel(name = "平台总优惠",width = 15)
    private BigDecimal platformTotalDiscount;

    @Excel(name = "实付金额",width = 15)
    private BigDecimal money;

    @Excel(name = "佣金金额",width = 15)
    private BigDecimal hireMoney;

    @Excel(name = "应结算金额",width = 15)
    private BigDecimal statementTotalMoney;
    /**
     * 应收佣金
     */
    @Excel(name = "应收佣金",width = 15)
    private BigDecimal payableCommission;

    @Excel(name = "账单生成时间",width = 18,format = "yyyy-MM-dd HH:mm:ss")
    private Date billCreateTime;

    @Excel(name = "账单入账时间",width = 18,format = "yyyy-MM-dd HH:mm:ss")
    private Date billPaymentTime;

}