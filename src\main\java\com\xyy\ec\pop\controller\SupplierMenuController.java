package com.xyy.ec.pop.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.server.api.merchant.dto.PopSupplierMenuAdminQueryParam;
import com.xyy.ec.pop.server.api.seller.dto.PopSupplierMenuDto;
import com.xyy.ec.pop.service.SupplierMenuService;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import com.xyy.pop.framework.core.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.Date;
import java.util.Map;

/**
 * 商户端菜单管理
 * <AUTHOR>
 */
@Controller
@RequestMapping("suppliermenu")
@Api(tags = "商户端菜单管理类")
public class SupplierMenuController extends BaseController {

    private static final Logger LOG = LoggerFactory.getLogger(SupplierMenuController.class);

    @Autowired
    private SupplierMenuService supplierMenuService;

    /**
     * 菜单管理列表页
     */
    @RequestMapping(value = "/index", method = { RequestMethod.GET })
    @ApiOperation("菜单管理列表页")
    public String index(Model model) {
        return "suppliermenu/index";
    }

    @ResponseBody
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    @ApiOperation("菜单管理列表")
    public Response<PageInfo<PopSupplierMenuDto>> query(@RequestBody Map<String, Object> pms) {
        try {
            JSONObject j = new JSONObject(pms);
            PopSupplierMenuAdminQueryParam popSupplierMenuAdminQueryParam = new PopSupplierMenuAdminQueryParam();
            popSupplierMenuAdminQueryParam.setPageSize(j.getIntValue("limit"));
            popSupplierMenuAdminQueryParam.setPageNum(j.getIntValue("offset"));
            PageInfo<PopSupplierMenuDto> pageList = supplierMenuService.queryPageList(popSupplierMenuAdminQueryParam);
            return ResponseUtils.returnObjectSuccess(pageList);
        } catch (Exception e) {
            LOG.error("查询菜单管理列表信息异常", e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 跳转到新增页面
     */
    @RequestMapping(value = "/toAdd", method = {RequestMethod.GET })
    @ApiOperation("跳转到新增页面")
    public String index() {
        return "suppliermenu/add";
    }

    @ResponseBody
    @RequestMapping(value = "/queryRootMenu",method = RequestMethod.GET)
    @ApiOperation("查询一级菜单")
    @ApiImplicitParam(name = "menu",value = "菜单实体")
    public Response queryRootMenu(PopSupplierMenuDto menu) {
        try {
            menu.setParentMenuId(-1);
            return ResponseUtils.returnObjectSuccess(supplierMenuService.queryRootMenu(menu));
        } catch (ServiceException e) {
            LOG.error("查询一级菜单异常", e);
            return ResponseUtils.returnException(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/save", method = {RequestMethod.POST})
    @ApiOperation("保存菜单")
    @ApiImplicitParam(name = "menu",value = "菜单实体")
    public Response save(PopSupplierMenuDto menu) {
        try {
            SysUser sysUser = getUser();
            menu.setOperateBy(sysUser.getRealName());
            menu.setOperateTime(new Date());
            menu.setStatus(1);
            if(null == menu.getMenuId()){
                supplierMenuService.insert(menu);
            }else{
                supplierMenuService.updateBySelective(menu);
            }
            return ResponseUtils.returnSuccess("保存成功");
        } catch (ServiceException e) {
            LOG.error("保存异常！");
            return ResponseUtils.returnException(e);
        }
    }

    @RequestMapping(value = "/toUpdate",method = RequestMethod.GET)
    @ApiOperation("更新菜单")
    @ApiImplicitParam(name = "menuId",value = "菜单id")
    public ModelAndView toUpdate(Integer menuId) {
        try {
            ModelAndView modelAndView = new ModelAndView("suppliermenu/update");
            PopSupplierMenuDto menu = supplierMenuService.findById(menuId);
            modelAndView.addObject("menu",menu);
            return modelAndView;
        } catch (ServiceException e) {
            LOG.error(e.getMessage());
            return null;
        }
    }
}
