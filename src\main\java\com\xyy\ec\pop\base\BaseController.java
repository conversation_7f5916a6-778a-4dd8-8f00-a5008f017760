package com.xyy.ec.pop.base;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.redis.RedisConstants;
import com.xyy.ec.pop.redis.XyyJedisCluster;
import com.xyy.ec.pop.utils.*;
import com.xyy.ec.pop.utils.cookie.CookieTool;
import com.xyy.ec.pop.vo.zhongtai.PopProvinceVo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.beans.PropertyEditorSupport;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class BaseController {

	protected static final Logger LOGGER = LoggerFactory.getLogger(BaseController.class);
    private static final String DEFAULT_USER_NAME = "adminUser";
	@Autowired
	private XyyJedisCluster xyyJedisCluster;

	protected static final String RESULT_STATUS = "status";
	protected static final String RESULT_ERRORMSG = "errorMsg";
	protected static final String RESULT_ERRORCODE = "errorCode";
	protected static final String RESULT_MSG = "msg";
	protected static final String RESULT_SUCCESS = "success";
	protected static final String RESULT_FAILURE = "failure";

	public SysUser getUser(){
		RequestAttributes ra = RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = ((ServletRequestAttributes) ra).getRequest();
		Cookie cookie = CookieTool.getCookie(request, Constants.SESSION_ID);
		if (cookie != null) {
			try {
				String json = xyyJedisCluster.get(cookie.getValue());
				SysUser user = (SysUser) JSON.parseObject(json, SysUser.class);
				return user;
			} catch (Exception e) {
				LOGGER.error(
						"分布式缓存中没有sid=" + cookie.getValue() + "的用户"
								+ e.getMessage(), e);
			}

		}
		return null;
	}

    public String getUserName() {
        SysUser sysUser = getUser();
        if (sysUser == null) {
            return DEFAULT_USER_NAME;
        }
        if (!StringUtils.hasText(sysUser.getUsername())) {
            return !StringUtils.hasText(sysUser.getRealName()) ? DEFAULT_USER_NAME : sysUser.getRealName();
        }
        return sysUser.getUsername();
    }

	public String getSysUserName() {
		String userName = getUserName();
		return Objects.equals(userName, DEFAULT_USER_NAME) ? null : userName;
	}


	/**
	 * 
	 * 添加返回结果
	 * 
	 * @param name
	 *            返回对象的名称
	 * @param value
	 *            需要返回的对象
	 * @return
	 * <AUTHOR>
	 */
	protected Map<String, Object> addResult(String name, Object value) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_SUCCESS);
		responseData.put(name, value);
		return responseData;
	}

	protected Map<String, Object> addResult(String[] names, Object[] values) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_SUCCESS);
		for (int i = 0; i < names.length; i++) {
			responseData.put(names[i], values[i]);
		}
		return responseData;
	}

	protected Map<String, Object> addResult() {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_SUCCESS);
		return responseData;
	}

	protected Map<String, Object> addResult(String msg) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_SUCCESS);
		responseData.put(RESULT_MSG, msg);
		return responseData;
	}

	/**
	 * 
	 * 添加错误信息
	 * 
	 * @param errorMsg
	 *            错误信息
	 * @return
	 * <AUTHOR>
	 */
	protected Map<String, Object> addError(String errorMsg) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_FAILURE);
		responseData.put(RESULT_ERRORMSG, errorMsg);
		return responseData;
	}

	/**
	 * 添加错误信息
	 * 
	 * @param errorCode
	 *            错误编码
	 * @param errorMsg
	 *            错误信息
	 * @return
	 */
	protected Map<String, Object> addError(int errorCode, String errorMsg) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_FAILURE);
		responseData.put(RESULT_ERRORCODE, errorCode);
		responseData.put(RESULT_ERRORMSG, errorMsg);
		return responseData;
	}

	protected Map<String, Object> addError(String name, Object value) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_FAILURE);
		responseData.put(name, value);
		return responseData;
	}

	protected Map<String, Object> addError(String[] names, Object[] values) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_FAILURE);
		for (int i = 0; i < names.length; i++) {
			responseData.put(names[i], values[i]);
		}
		return responseData;
	}

	/**
	 * 拼接页面参数
	 * 
	 * @param request
	 * @return
	 * @throws UnsupportedEncodingException 
	 */
	protected String getRequestUrl(HttpServletRequest request){
		String url = "";
		String requestUri = request.getRequestURI();
		String queryString = request.getQueryString(); 
		String qs = StringUtil.removeParameter(queryString, "offset");
		if(requestUri.contains("/xyy-shop/")){
			requestUri = requestUri.replace("/xyy-shop/", "/");
		}
		if (StringUtil.isNotEmpty(qs)) {
			url = requestUri + "?" + EncodeUtil.urlDecode(qs,"UTF-8");
		} else {
			url = requestUri;
		}
		return url;
	}
	
	/**
     * 得到请求参数的字符串表现形式
     * 
     * @return
     */
	protected String toParameterString(HttpServletRequest request) {
		String URI = request.getRequestURI();
		Map<String, String[]> parameters = request.getParameterMap(); 
        StringBuilder sb = new StringBuilder(URI);
        if (CollectionUtil.isEmpty(parameters)) return sb.toString();
        sb.append("?");
        for (String key : parameters.keySet()) {
            String[] values = getStringArray(key,parameters);
            for (String value : values) {
            	if(sb.toString().contains(key+"=")){
            	}else{
            		sb.append(key).append("=").append(value).append("&");
            	}
                
            }
        }
        return sb.toString();
    }
	
	public String[] getStringArray(String key,Map<String,String[]> parameters) {
        List<String> values = new ArrayList<String>();
        String[] params = parameters.get(key);
        if (CollectionUtil.isEmpty(params)) return values.toArray(new String[] {});
        for (String param : params) {
            if (StringUtil.isEmpty(param)) continue;
            values.add(param);
        }
        return values.toArray(new String[] {});
    }
	
	@InitBinder
	protected void initBinder(ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(Integer.class, new IntegerEditor());
		binder.registerCustomEditor(Long.class, new LongEditor());
		binder.registerCustomEditor(Double.class, new DoubleEditor());
		binder.registerCustomEditor(Date.class, new DateEditor());
		binder.registerCustomEditor(String.class, new StringEditor());
	}

	class IntegerEditor extends PropertyEditorSupport {
		@Override
		public void setAsText(String text) throws IllegalArgumentException {
			if (!StringUtils.hasText(text)) {
				setValue(null);
			} else {
				// 这句话是最重要的，他的目的是通过传入参数的类型来匹配相应的databind
				setValue(Integer.parseInt(text));
			}
		}

		@Override
		public String getAsText() {

			return getValue().toString();
		}
	}

	class DoubleEditor extends PropertyEditorSupport {
		@Override
		public void setAsText(String text) throws IllegalArgumentException {
			if (!StringUtils.hasText(text)) {
				setValue(null);
			} else {
				// 这句话是最重要的，他的目的是通过传入参数的类型来匹配相应的databind
				setValue(Double.parseDouble(text));
			}
		}

		@Override
		public String getAsText() {

			return getValue().toString();
		}
	}

	class LongEditor extends PropertyEditorSupport {
		@Override
		public void setAsText(String text) throws IllegalArgumentException {
			if (!StringUtils.hasText(text)) {
				setValue(null);
			} else {
				// 这句话是最重要的，他的目的是通过传入参数的类型来匹配相应的databind
				setValue(Long.parseLong(text));
			}
		}

		@Override
		public String getAsText() {

			return getValue().toString();
		}
	}

	class DateEditor extends PropertyEditorSupport {
		@Override
		public void setAsText(String text) throws IllegalArgumentException {

			if (!StringUtils.hasText(text)) {
				setValue(null);
			} else {
				try {
					switch (text.length()) {
						case 4:
							setValue(DateUtil.string2Date(text, "yyyy"));
							break;
						case 7:
							setValue(DateUtil.string2Date(text, "yyyy-MM"));
							break;
						case 10:
							setValue(DateUtil.string2Date(text, "yyyy-MM-dd"));
							break;
						case 13:
							setValue(DateUtil.string2Date(text, "yyyy-MM-dd HH"));
							break;
						case 16:
							setValue(DateUtil.string2Date(text, "yyyy-MM-dd HH:mm"));
							break;
						case 19:
							setValue(DateUtil.string2Date(text, "yyyy-MM-dd HH:mm:ss"));
							break;
							default:
					}
				} catch (Exception e) {
					setValue(new Date(Long.parseLong(text)));
				}
			}
		}

		@Override
		public String getAsText() {

			return getValue().toString();
		}
	}

	class StringEditor extends PropertyEditorSupport {
		@Override
		public void setAsText(String text) throws IllegalArgumentException {
			if (!StringUtils.hasText(text)) {
				setValue(null);
			} else {
				text = text.trim();
				setValue(text);
			}
		}

		@Override
		public String getAsText() {
			return getValue().toString();
		}
	}

	protected List<Long> getProvIds(Long provId){
		if(Objects.nonNull(provId)){
			return Collections.singletonList(provId);
		}
		SysUser user = getUser();
		String redisKey = RedisConstants.XYY_POP_PROVINCE_KEYS + user.getOaId();
		String provinceStr = xyyJedisCluster.get(redisKey);
		if(StringUtils.isEmpty(provinceStr)){
			return Lists.newArrayList();
		}
		List<PopProvinceVo> list = JSON.parseArray(provinceStr, PopProvinceVo.class);
		return list.stream().map(PopProvinceVo::getProvId).collect(Collectors.toList());
	}

	protected List<String> getProvIds(String branchCode){
		if(Objects.nonNull(branchCode)){
			return Collections.singletonList(branchCode);
		}
		SysUser user = getUser();
		String redisKey = RedisConstants.XYY_POP_PROVINCE_KEYS + user.getOaId();
		String provinceStr = xyyJedisCluster.get(redisKey);
		if(StringUtils.isEmpty(provinceStr)){
			return Lists.newArrayList();
		}
		List<PopProvinceVo> list = JSON.parseArray(provinceStr, PopProvinceVo.class);
		return list.stream().map(PopProvinceVo::getProvId).map(String::valueOf).collect(Collectors.toList());
	}

	protected Map<Long,String> getProv(){
		SysUser user = getUser();
		String redisKey = RedisConstants.XYY_POP_PROVINCE_KEYS + user.getOaId();
		String provinceStr = xyyJedisCluster.get(redisKey);
		if(StringUtils.isEmpty(provinceStr)){
			return Maps.newHashMap();
		}
		List<PopProvinceVo> list = JSON.parseArray(provinceStr, PopProvinceVo.class);
		return list.stream().collect(Collectors.toMap(PopProvinceVo::getProvId, PopProvinceVo::getProv));
	}

	/**
	 * 校验是否有操作权限
	 * @param authorityUsers 英文逗号分割
	 */
	public void validAuthority(String authorityUsers,String module) throws ServiceException {
		if(StringUtils.isEmpty(authorityUsers)){
			throw new ServiceException("没有操作权限");
		}
		String user = getUser().getUsername();
		String[] users = authorityUsers.split(",");
		for(String u:users){
			if(user.equals(u)){
				return;
			}
		}
		log.warn("没有操作权限,user:{}, users:{},module：{}",user,authorityUsers,module);
		throw new ServiceException("没有操作权限");
	}
}
