package com.xyy.ec.pop.vo;


import lombok.Data;

import java.io.Serializable;

/**
 * @version v1
 * @Description 新品上报消息实体
 * <AUTHOR>
 */
@Data
public class MeProductResentVo implements Serializable {
    /**
     * 外部商品编码
     */
    private String outsideCode;
    /**
     * 标准库id
     */
    private String productId;

    /**
     * 发送方向 1：宜块钱 2：saas智鹿 3-实施上报 4-POP
     */
    private Byte sendType;
    /**
     * 状态值 0:成功 2:驳回 4:审核不通过
     */
    private Integer statusCode;

    /**
     * 审核原因
     */
    private Integer remark;

    /**
     * 时间戳
     */
    private String timeStamp;
}
