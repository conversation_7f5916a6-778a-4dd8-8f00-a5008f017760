package com.xyy.ec.pop.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 提现
 */
@Data
public class PopCashAdvanceDetailVo {
    /**
     * 商户编号
     */
    @Excel(name = "商户编号",orderNum = "0")
    private String orgId;

    /**
     * 商家名称
     */
    @Excel(name = "商家名称",orderNum = "0")
    private String orgName;

    /**
     * 商家名称
     */
    @Excel(name = "店铺名称",orderNum = "0")
    private String name;

    /**
     * 申请提现金额
     */
    @Excel(name = "申请提现金额",orderNum = "1")
    private BigDecimal applyAmount;

    /**
     * 提现手续费
     */
    @Excel(name = "手续费",orderNum = "2")
    private BigDecimal fee;

    /**
     * 实际到账金额
     */
    @Excel(name = "实际到账金额",orderNum = "3")
    private BigDecimal realityAmount;
    /**
     * 申请时间
     */
    private Date applyTime;
    @Excel(name = "提现申请时间",orderNum = "0")
    private String applyTimeStr;

    /**
     * 提现账户名
     */

    private String accountName;

    /**
     * 账号
     */
    private String accountNum;

    /**
     * 开户行
     */
    private String accountBank;
    /**
     * 提现单号
     */
    private String cashAdvanceNum;
    /**
     * 打款方式（1.线上打款，2线下打款）
     */
    private Byte paymentWay;

    /**
     * 打款状态（1.未打款，2.已打款）
     */
    private Byte paymentStatus;
    @Excel(name = "打款状态",orderNum = "4")
    private String paymentStatusName;

    /**
     * 备注/原因
     */
    @Excel(name = "备注/原因",orderNum = "5")
    private String reason;

    /**
     * 账单号
     */
    @Excel(name = "账单号",orderNum = "6")
    private String billNo;

    /**
     * 单据类型(1:订单 2:退款单）
     */
    private Byte businessType;
    @Excel(name = "单据类型",orderNum = "13")
    private String businessTypeName;

    /**
     * 订单或退款单号
     */
    @Excel(name = "单据号",orderNum = "12")
    private String businessNo;

    /** 药店名称 */
    @Excel(name = "客户名称",orderNum = "13")
    private String merchantName;

    private String sellerUserId;
    /**
     * 商品总金额
     */
    @Excel(name = "商品金额",orderNum = "14")
    private BigDecimal productMoney;

    /**
     * 单据总金额
     */
    @Excel(name = "单据总额（含运费）",orderNum = "16")
    private BigDecimal totalMoney;

    /**
     * 订单实际付款金额
     */
    @Excel(name = "实付金额",orderNum = "19")
    private BigDecimal money;

    /**
     * 运费
     */
    @Excel(name = "运费金额",orderNum = "15")
    private BigDecimal freightAmount;

    /**
     * 店铺优惠券金额
     */
    private BigDecimal couponShopAmount;

    /**
     * 店铺营销活动优惠金额
     */
    private BigDecimal marketingShopAmount;

    /**
     * 店铺总优惠金额
     */
    @Excel(name = "店铺总优惠",orderNum = "17")
    private BigDecimal shopTotalDiscount;

    /**
     * 平台优惠券金额
     */
    private BigDecimal couponPlatformAmount;

    /**
     * 平台营销活动优惠金额
     */
    private BigDecimal marketingPlatformAmount;

    /**
     * 平台总优惠金额
     */
    @Excel(name = "平台总优惠",orderNum = "18")
    private BigDecimal platformTotalDiscount;

    /**
     * 佣金
     */
    @Excel(name = "佣金金额",orderNum = "20")
    private BigDecimal hireMoney;
    /**
     * 应收佣金
     */
    @Excel(name = "应收佣金",orderNum = "22")
    private BigDecimal payableCommission;
    /**
     * 佣金结算方式，1：非月结；2：月结
     */
    private Byte settlementType;
    @Excel(name = "佣金结算方式",orderNum = "7")
    private String settlementTypeName;
    /**
     * 账单入账状态
     */
    private Byte billPaymentStatus;
    @Excel(name = "账单状态",orderNum = "8")
    private String billPaymentStatusName;
    /**
     * 账单入账时间
     */
    private Date billPaymentTime;
    @Excel(name = "入账时间",orderNum = "10")
    private String billPaymentTimeStr;
    /**
     * 处罚金额
     */
    private BigDecimal penaltyAmount;

    /**
     * 应结算金额
     */
    @Excel(name = "应结算金额",orderNum = "21")
    private BigDecimal statementTotalMoney;

    /**
     * 支付时间
     */
    private Date orderPayTime;
    @Excel(name = "支付时间",orderNum = "22")
    private String orderPayTimeStr;

    /**
     * 订单或退款单完成时间
     */
    private Date orderFinishTime;

    /**
     * 支付类型(1:在线支付  3:线下转账）
     */
    private Byte payType;

    /**
     * 订单结算状态 0-待结算 1-结算完成
     */
    private Byte orderSettlementStatus;

    /**
     * 订单结算时间
     */
    private Date orderSettlementTime;

    /**
     * 账单生成时间
     */
    private Date billCreateTime;
    @Excel(name = "生成时间",orderNum = "9")
    private String billCreateTimeStr;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 账单id 排序用
     */
    private long billId;

    /**
     * 应缴纳佣金
     */
    private BigDecimal deductedCommission;
}
