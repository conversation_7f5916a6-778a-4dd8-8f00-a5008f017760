package com.xyy.ec.pop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ShopStatusEnum {

    WAITING_ONLINE(1, "","待上线"),
    ONLINE(2,"上线","已上线"),
    OFFLINE(3,"下线","已下线"),
    CLOSED(4,"关店","已关闭") ;

    private int code;
    private String value;
    private String desc;

    public static String getShopStatusValue(Integer status){
        if(status == null){
            return "";
        }
        for (ShopStatusEnum anEnum : ShopStatusEnum.values()) {
            if(status == anEnum.getCode()){
                return anEnum.getValue();
            }
        }
        return "";
    }
}
