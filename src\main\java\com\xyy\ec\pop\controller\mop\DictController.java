package com.xyy.ec.pop.controller.mop;

import com.xyy.ec.pop.adapter.mop.MopDictAdapter;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.result.DictDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 字典控制器，用于处理字典相关的请求。
 */
@Slf4j
@RequestMapping("/mop/dict")
@RestController
public class DictController extends BaseController {

    @Autowired
    private MopDictAdapter mopDictAdapter;

    /**
     * 获取指定类型的字典数据列表。
     *
     * @param dictType 字典类型
     * @return 包含字典数据列表的响应对象
     */
    @GetMapping("/list")
    public ResponseVo<List<DictDataDTO>> list(String dictType) {
        return mopDictAdapter.listDictDataByType(dictType);
    }
    /**
     * 获取指定类型的字典数据列表。
     *
     * @param dictTypes 字典类型
     * @return 包含字典数据列表的响应对象
     */
    @GetMapping("/batch/list")
    public ResponseVo<Map<String,List<DictDataDTO>>> batchList(String dictTypes) {
        if (StringUtils.isEmpty(dictTypes)) {
            return ResponseVo.errRest("字典类型不能为空");
        }
        return mopDictAdapter.listDictDataByType(Arrays.asList(dictTypes.split(",")));
    }
}
