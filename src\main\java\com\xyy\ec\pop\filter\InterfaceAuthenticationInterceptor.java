package com.xyy.ec.pop.filter;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.ButtonAuth;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.redis.XyyJedisCluster;
import com.xyy.ec.pop.service.SystemService;
import com.xyy.ec.pop.utils.Constants;
import com.xyy.ec.pop.utils.cookie.CookieTool;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Component
public class InterfaceAuthenticationInterceptor extends HandlerInterceptorAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(InterfaceAuthenticationInterceptor.class);

    @Autowired
    private SystemService systemService;

    @Value("${pop.authentication.interfaces:}")
    private String btnUriStr;
    @Autowired
    private XyyJedisCluster xyyJedisCluster;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String uri = request.getRequestURI();
        LOGGER.info("InterfaceAuthenticationInterceptor.preHandle # param uri:{}, btnUriStr:{}, ", uri, btnUriStr);
        if (StringUtils.isBlank(btnUriStr)) {
            return true;//不做权限过滤
        }

        List<ButtonAuth> buttonAuths = JSON.parseArray(btnUriStr, ButtonAuth.class);
        ButtonAuth auth = buttonAuths.stream().filter(btn -> uri.equals(btn.getBtnUrl())).findAny().get();
        SysUser user = getUser();
        LOGGER.info("InterfaceAuthenticationInterceptor.preHandle用户鉴权info # param uri:{}, btnUriStr:{}, user:{}, auth:{}", uri,
                btnUriStr, JSON.toJSONString(user), JSON.toJSONString(auth));
        if (auth != null) {
            List<String> buttons = systemService.getButtons(user, auth.getBtnParentUrl());
            LOGGER.info("InterfaceAuthenticationInterceptor.preHandle用户鉴权info # param uri:{}, btnUriStr:{}, user:{}, auth:{},buttons:{}", uri,
                    btnUriStr, JSON.toJSONString(user), JSON.toJSONString(auth), JSON.toJSONString(buttons));
            if (CollectionUtils.isNotEmpty(buttons) && buttons.contains(auth.getBtnName())) {
                return true; //权限过滤
            }
        }
        LOGGER.info("InterfaceAuthenticationInterceptor.preHandle用户失败 # param uri:{}, btnUriStr:{}, user:{}", uri, btnUriStr, JSON.toJSONString(user));
        return false;
    }

    public SysUser getUser() {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) ra).getRequest();
        Cookie cookie = CookieTool.getCookie(request, Constants.SESSION_ID);
        if (cookie != null) {
            try {
                String json = xyyJedisCluster.get(cookie.getValue());
                SysUser user = (SysUser) JSON.parseObject(json, SysUser.class);
                return user;
            } catch (Exception e) {
                LOGGER.error(
                        "分布式缓存中没有sid=" + cookie.getValue() + "的用户"
                                + e.getMessage(), e);
            }

        }
        return null;
    }
}
