package com.xyy.ec.pop.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * tb_xyy_pop_commission_settle_set
 * <AUTHOR>
 */
@Data
public class PopCommissionSettlePo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 佣金结算流水编号
     */
    private String hireNo;

    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 商家名称
     */
    private String orgName;

    /**
     * 佣金
     */
    private BigDecimal hireMoney;

    /**
     * 冲抵平台优惠后的佣金
     */
    private BigDecimal deductedCommission;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 佣金收取月份，逗号分隔
     */
    private String hireMonths;

    /**
     * 付款期限：最后付款时间
     */
    private Date paymentTerm;

    /**
     * 确认收款时间
     */
    private Date paymentTime;

    /**
     * 付款凭证图片
     */
    private String paymentCertificate;

    /**
     * 付款凭证上传时间
     */
    private Date uploadTime;

    /**
     * 状态：0：待商业付款；1-待平台审核；2：审核通过；3：审核未通过；4：待结转;5:已结转
     */
    private Integer state;

    /**
     * 备注原因
     */
    private String remarks;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updator;

}