package com.xyy.ec.pop.utils;

import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON>yu on 2017/8/17.
 */
public class CopyUtil {
    public static<O,T> List<T> copyList(List<O> source, Class<T> target){
        List<T> result=new ArrayList<>();
        for(O o:source){
            Object t= null;
            try {
                t = target.newInstance();
                BeanUtils.copyProperties(o,t);
                result.add((T)t);
            } catch (InstantiationException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return result;
    }
}
