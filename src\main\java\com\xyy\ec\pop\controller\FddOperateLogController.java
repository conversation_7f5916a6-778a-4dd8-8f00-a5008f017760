package com.xyy.ec.pop.controller;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.server.api.fdd.api.FddOperateLogApi;
import com.xyy.ec.pop.server.api.fdd.dto.TbXyyPopFddEnterpriseAgreementDTO;
import com.xyy.ec.pop.server.api.fdd.dto.TbXyyPopFddOperateLogDTO;
import com.xyy.ec.pop.server.api.fdd.param.FddSignaturesParam;
import com.xyy.ec.pop.service.FddOperateLogService;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Objects;

@Slf4j
@RequestMapping("/platformServiceAgreement/log")
@Controller
public class FddOperateLogController  extends BaseController {


    @Autowired
    private FddOperateLogService fddOperateLogService;


    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public ResponseVo platformAgreementList(Integer relationId) {
        try{

            if (Objects.isNull(relationId)){
                return ResponseVo.errRest("必填参数不能为空");
            }

            List<TbXyyPopFddOperateLogDTO> res = fddOperateLogService.operateLogList(relationId);
            return ResponseVo.successResult(res);
        } catch (Exception e) {
            log.error("FddOperateLogController.list#query:{} 异常", JSON.toJSONString(relationId), e);
            return ResponseVo.errRest("查询异常");
        }
    }
}
