package com.xyy.ec.pop.controller;

import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.service.BondService;
import com.xyy.ec.pop.vo.BondVo;
import com.xyy.ec.pop.vo.ResponseVo;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/popBond")
@Api(tags = "保证金类")
public class PopBondController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PopBondController.class);

    @Autowired
    private BondService bondService;


    /**
     * 更新保证金
     * @param bond
     * @return
     */
    @PostMapping(value = "/saveBond")
    @ResponseBody
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> saveBond(@RequestBody BondVo bond){
        try{
            SysUser sysUser = getUser();
            String userName = StringUtils.isEmpty(sysUser.getUsername())?sysUser.getRealName():sysUser.getUsername();
            bondService.saveBond(bond,userName);
            return ResponseVo.successResult(true);
        }catch (ServiceException e){
            LOGGER.warn("设置保证金错误", e.getMessage());
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e){
            LOGGER.error("设置保证金异常", e);
            return ResponseVo.errRest("设置保证金异常");
        }

    }
}
