package com.xyy.ec.pop.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.xyy.ec.pop.service.BatchUpdateService;
import com.xyy.ec.pop.service.FastDfsUtilService;
import com.xyy.ec.pop.vo.BatchOverOrderVo;
import com.xyy.ec.pop.vo.BatchUpdateResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
public class BatchUpdateServiceImpl implements BatchUpdateService {
    @Autowired
    private FastDfsUtilService fastDfsUtilService;
    public BatchUpdateResultVo writeResult(int totalSize, List<BatchOverOrderVo> errors, String sheetName) {
        if(errors==null){
            errors = new ArrayList<>();
        }
        BatchUpdateResultVo resultVo = new BatchUpdateResultVo();
        resultVo.setError(errors.size());
        resultVo.setSuccess(totalSize - resultVo.getError());
        if(errors.size()>0){
            //将数据写入excel文件
            String fileUrl = fastDfsUtilService.writeDateToFastDfs(new ExportParams(null, sheetName, ExcelType.XSSF), errors.get(0).getClass(), errors);
            resultVo.setErrorFileUrl(fileUrl);
        }
        return resultVo;
    }
}
