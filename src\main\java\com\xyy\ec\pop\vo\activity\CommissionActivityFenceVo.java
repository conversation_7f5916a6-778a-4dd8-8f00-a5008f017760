package com.xyy.ec.pop.vo.activity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @TableName tb_xyy_pop_commission_activity_fence
 */
@Data
public class CommissionActivityFenceVo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 实付GMV(元)
     */
    private BigDecimal actualMonery;

    /**
     * 店铺动销客户数
     */
    private Integer custCnt;

    /**
     * 佣金折扣比例
     */
    private BigDecimal discountRatio;

    /**
     * 活动编码
     */
    private Long activityId;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

}