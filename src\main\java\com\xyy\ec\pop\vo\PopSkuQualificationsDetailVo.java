package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_xyy_pop_sku_qualifications_detail
 * <AUTHOR>
@Data
public class PopSkuQualificationsDetailVo implements Serializable {
    private Long id;

    /**
     * 资质名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 图片数量限制
     */
    private Byte maxImg;

    /**
     * 资质内容描述
     */
    private String remark;

    /**
     * 是否必填
     */
    private Boolean isNeed;

    /**
     * 证件号是否必填
     */
    private Boolean isNeedCode;

    /**
     * 开户银行及支行是否必填
     */
    private Boolean isNeedBank;

    /**
     * 证件有效期是否必填
     */
    private Boolean isNeedTime;

    /**
     * 结束时间是否可选长期:0没有长期，1有长期
     */
    private Boolean withLongTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改者
     */
    private String updator;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}