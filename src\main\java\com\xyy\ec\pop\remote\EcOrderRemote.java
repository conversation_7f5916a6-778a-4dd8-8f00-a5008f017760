package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.client.api.query.PromoDetailBusinessService;
import com.xyy.ec.marketing.client.dto.discount.OrderPromoDetail;
import com.xyy.ec.order.backend.order.api.OrderExportApi;
import com.xyy.ec.order.backend.pop.api.PopOrderApi;
import com.xyy.ec.order.backend.pop.api.PopOrderRefundApi;
import com.xyy.ec.order.backend.pop.dto.*;
import com.xyy.ec.order.backend.query.Sort;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.PopOrderBusinessApi;
import com.xyy.ec.order.business.dto.ConfirmReceiptDto;
import com.xyy.ec.order.business.dto.OrderDetailBusinessDto;
import com.xyy.ec.order.business.dto.pop.PopOrderFinishDto;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.helper.OrderHelper;
import com.xyy.ec.pop.utils.ConvertUtil;
import com.xyy.ec.pop.vo.OrderDetail;
import com.xyy.ec.pop.vo.RefundOrderDetailAdminVo;
import com.xyy.ec.pop.vo.RefundOrderDetailDto;
import com.xyy.ec.pop.vo.RefundOrderParamVo;
import com.xyy.ec.pop.vo.order.OrderAdminVo;
import com.xyy.ec.pop.vo.order.OrderDetailDiscountVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: Jincheng.Li
 * @Date: 2021/05/21/14:24
 * @Description:
 */
@Component
@Slf4j
public class EcOrderRemote {
    @Value("${deliver.timeout.hours}")
    private Integer timeoutHours;
    @Reference
    private PopOrderApi ecOrderApi;
    @Reference
    private PopOrderRefundApi popOrderRefundApi;
    @Autowired
    private RegionAdapter regionAdapter;
    @Reference
    private PopOrderBusinessApi popOrderBusinessApi;
    @Reference(retries = 3)
    private PromoDetailBusinessService promoDetailBusinessService;
    @Reference
    private OrderBusinessApi orderBusinessApi;
    @Reference
    private OrderExportApi orderExportApi;

    public Integer queryTimeoutOrderCount(List<Integer> statusList, Integer timeoutHours) {
        try {
            log.info("EcOrderRemote.queryTimeoutOrderCount#request statusList:{},timeoutHours:{}", statusList, timeoutHours);
            ApiRPCResult<Integer> apiRPCResult = ecOrderApi.getUnDeliveredOrderCount(null, statusList, timeoutHours);
            log.info("EcOrderRemote.queryTimeoutOrderCount#invoke ec response statusList:{},timeoutHours:{},apiRPCResult:{}", statusList, timeoutHours, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return 0;
            }
            Integer orderCount = apiRPCResult.getData();
            return orderCount == null ? 0 : orderCount;
        } catch (Exception e) {
            log.error("EcOrderRemote.queryTimeoutOrderCount#error request statusList:{},timeoutHours:{}", statusList, timeoutHours, e);
            return 0;
        }
    }

    public Page<OrderAdminVo> popOrderList(OrderAdminVo paramOrder, String sort, Integer pageNum, Integer pageSize) {
        PopOrderDto popOrderDto = OrderHelper.convertOrderToPopOrderDto(paramOrder);
        Sort sortObj = JSON.parseObject(sort, Sort.class);
        popOrderDto.setProvinceCode(paramOrder.getProvinceCode());
        log.info("#EcOrderApiRemote.orderList#info,入参:popOrderDto:{}", JSON.toJSON(popOrderDto));
        ApiRPCResult<com.xyy.ec.order.backend.query.Page<PopOrderDto>> apiRPCResult = ecOrderApi.queryPopOrderList(popOrderDto, pageSize, pageNum, sortObj);
        log.info("#EcOrderApiRemote.orderList#info,结果:apiRPCResult:{}", JSON.toJSON(apiRPCResult));
        if (apiRPCResult.isFail()) {
            log.warn("#EcOrderApiRemote.orderList#warn,参数:vo:{},pageNum:{},pageSize:{}", JSON.toJSON(paramOrder), pageNum, pageSize);
            return null;
        }
        com.xyy.ec.order.backend.query.Page<PopOrderDto> orderDtoPage = apiRPCResult.getData();
        //<省份code,省份名称>
        Map<Integer, String> provinceCodeNameMap = regionAdapter.getProvinceCodeNameMap();
        com.xyy.ec.pop.base.Page<OrderAdminVo> orderVoPage = OrderHelper.convertOrderDtoToOrder(orderDtoPage,provinceCodeNameMap);
        return orderVoPage;
    }

    public PageInfo<RefundOrderDetailAdminVo> queryRefundOrderPage(RefundOrderParamVo paramVo, int pageNum, int pageSize) {
        try {
            log.info("EcOrderRemote.queryRefundOrderPage#paramVo:{},pageNum:{},pageSize:{}", JSON.toJSONString(paramVo), JSON.toJSONString(pageNum), JSON.toJSONString(pageSize));
            PopOrderRefundDto popOrderRefundDto = OrderHelper.convertRefundOrderDTOToPopOrderRefundDto(paramVo);
            ApiRPCResult<com.xyy.ec.order.backend.query.Page<PopOrderRefundDto>> apiRPCResult = popOrderRefundApi.queryRefundOrderList(popOrderRefundDto, pageSize, pageNum);
            Map<Integer, String> provinceCodeNameMap = regionAdapter.getProvinceCodeNameMap();
            PageInfo<RefundOrderDetailAdminVo> refundOrderVoPageInfo = ConvertUtil.pageDto2Vo(apiRPCResult.getData(), dto -> {
                RefundOrderDetailAdminVo vo = OrderHelper.convetPopOrderRefundDtoToVo(dto);
                //设置区域省份名称
//                vo.setBranchName(branchs == null ? "" : branchs.get(dto.getBranchCode()));
                vo.setBranchName(provinceCodeNameMap.get(dto.getProvinceCode()));
                return vo;
            });
            log.info("EcOrderRemote.queryRefundOrderPage#paramVo:{},pageNum:{},pageSize:{} return {}", JSON.toJSONString(paramVo), JSON.toJSONString(pageNum), JSON.toJSONString(pageSize), JSON.toJSONString(refundOrderVoPageInfo));
            return refundOrderVoPageInfo;
        } catch (Exception e) {
            log.error("EcOrderRemote.queryRefundOrderPage#paramVo:{},pageNum:{},pageSize:{} 异常", JSON.toJSONString(paramVo), JSON.toJSONString(pageNum), JSON.toJSONString(pageSize), e);
            return PageInfo.of(new ArrayList<>());
        }
    }

    public PopOrderRefundExpressBusinessDto queryRefundExpressByOrderRefundId(Long orderRefundId) {
        try {
            log.info("EcRefundOrderRemote.queryRefundExpressByOrderRefundId#orderRefundId:{}", orderRefundId);
            ApiRPCResult<PopOrderRefundExpressBusinessDto> result = popOrderRefundApi.queryRefundExpressByOrderRefundId(orderRefundId);
            log.info("EcRefundOrderRemote.queryRefundExpressByOrderRefundId#orderRefundId:{},result:{}", orderRefundId, JSON.toJSONString(result));
            if (result == null || result.isFail()) {
                return new PopOrderRefundExpressBusinessDto();
            }
            if (Objects.isNull(result.getData())) {
                return new PopOrderRefundExpressBusinessDto();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("EcRefundOrderRemote.queryRefundExpressByOrderRefundId#orderRefundId:{}", orderRefundId, e);
            return new PopOrderRefundExpressBusinessDto();
        }
    }

    public PageInfo<RefundOrderDetailDto> queryRefundDetailPage(int pageNum, int pageSize, String refundOrderNo) {
        try {
            ApiRPCResult<com.xyy.ec.order.backend.query.Page<PopOrderRefundDetailDto>> apiRPCResult = this.popOrderRefundApi.queryRefundOrderDetailList(refundOrderNo, pageNum, pageSize);
            if (!apiRPCResult.isSuccess()) {
                return null;
            } else {
                PageInfo<RefundOrderDetailDto> refundOrderVoPageInfo = ConvertUtil.pageDto2Vo((com.xyy.ec.order.backend.query.Page)apiRPCResult.getData(), (dto) -> {
                    RefundOrderDetailDto vo = OrderHelper.convetPopOrderRefundDetailDtoToVo((PopOrderRefundDetailDto) dto);
                    return vo;
                });
                return refundOrderVoPageInfo;
            }
        } catch (Exception var6) {
            log.error("#EcOrderApiRemote.queryRefundDetailPage#error,参数:refundOrder:{},pageNum:{},pageSize:{}", new Object[]{refundOrderNo, pageNum, pageSize, var6});
            return null;
        }
    }

    public PageInfo<OrderDetail> getOrderDetails(String orderNo, Integer pageNum, Integer pageSize) {
        try {
            log.info("EcOrderRemote.getOrderDetails#orderNo:{},pageNum:{},pageSize:{}", orderNo, pageNum, pageSize);
            PageInfo<OrderDetailBusinessDto> ecPageOorderDetails = popOrderBusinessApi.getOrderDetailsByOrderNoNew(orderNo, pageNum, pageSize);
            log.info("EcOrderRemote.getOrderDetails#orderNo:{},pageNum:{},pageSize:{},orderDetails:{}", orderNo, pageNum, pageSize, JSON.toJSONString(ecPageOorderDetails));
            PageInfo<OrderDetail> pageInfo = ConvertUtil.pageInfoDto2Vo(ecPageOorderDetails, dto -> {
                OrderDetail vo = OrderHelper.convetOrderDetailDtoToVo(dto);
                return vo;
            });
            log.info("EcOrderRemote.getOrderDetails#orderNo:{},pageNum:{},pageSize:{},pageInfo:{}", orderNo, pageNum, pageSize, JSON.toJSONString(pageInfo));
            return pageInfo;
        } catch (Exception e) {
            log.info("EcOrderRemote.getOrderDetails#orderNo:{},pageNum:{},pageSize:{}", orderNo, pageNum, pageSize);
            return new PageInfo<>(Lists.newArrayList());
        }
    }
    public Map<Long, OrderDetailDiscountVo> getOrderPromoDetail(String orderNo) {
        try {
            log.info("OrderRefactorRemote.getOrderPromoDetail#orderNo:{}", orderNo);
            Map<Long, OrderDetailDiscountVo> result = Maps.newHashMap();
            if (StringUtils.isBlank(orderNo)) {
                return result;
            }
            List<OrderPromoDetail> allOrderPromoDetails = promoDetailBusinessService.mgetOrderPromoDetail(Lists.newArrayList(orderNo));
            log.info("OrderRefactorRemote.getOrderPromoDetail#orderNo:{},allOrderPromoDetails:{}", orderNo, JSON.toJSONString(allOrderPromoDetails));
            if (CollectionUtils.isEmpty(allOrderPromoDetails)) {
                return result;
            }
            //根据订单明细id汇总营销优惠
            Map<Long, List<OrderPromoDetail>> orderDetailIdPromosMap = allOrderPromoDetails.stream().collect(Collectors.groupingBy(OrderPromoDetail::getOrderDetailId));
            for (Long orderDetailId : orderDetailIdPromosMap.keySet()) {
                List<OrderPromoDetail> orderPromoDetails = orderDetailIdPromosMap.get(orderDetailId);
                OrderDetailDiscountVo orderDetailDiscountVo = OrderHelper.getPromoDetial(orderPromoDetails);
                if (orderDetailDiscountVo == null) {
                    continue;
                }
                result.put(orderDetailId, orderDetailDiscountVo);
            }
            log.info("OrderRefactorRemote.getOrderPromoDetail#orderNo:{},result:{}", orderNo, JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.info("OrderRefactorRemote.getOrderPromoDetail#orderNo:{}", orderNo, e);
            return Maps.newHashMap();
        }
    }

    public List<String> confirmOrderFinish(Set<PopOrderFinishDto> ecOrder) throws ServiceException {
        try {
            log.info("EcOrderRemote.confirmOrderFinish#ecOrder:{}", JSON.toJSONString(ecOrder));
            ApiRPCResult<List<PopOrderFinishDto>> result = orderBusinessApi.orderFinishForPop(ecOrder);
            log.info("EcOrderRemote.confirmOrderFinish#ecOrder:{} return {}", JSON.toJSONString(ecOrder), JSON.toJSONString(result));
            if (result != null && !result.isSuccess()) {
                throw new ServiceException(result == null ? "订单配送完成确认失败" : result.getErrMsg());
            }
            //检查失败订单，拼接显示文案
            List<PopOrderFinishDto> errData = result.getData();
            if (CollectionUtils.isNotEmpty(errData)) {
                return errData.stream().map(item -> item.getOrderNo()).collect(Collectors.toList());
            }
            return new ArrayList<>();
        } catch (ServiceException e) {
            log.warn("EcOrderRemote.confirmOrderFinish#ecOrder:{} 失败", JSON.toJSONString(ecOrder), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("EcOrderRemote.confirmOrderFinish#ecOrder:{} 异常", JSON.toJSONString(ecOrder), e);
            throw new ServiceException("订单配送完成确认失败");
        }
    }

    public Map<String,Integer> queryUnableFinishOrder(List<String> orders) throws ServiceException {
        try {
            log.info("EcOrderRemote.queryUnableFinishOrder#orders:{}", JSON.toJSONString(orders));
            ApiRPCResult<Map<String, Integer>> result = orderExportApi.queryUnableFinishOrder(orders);
            log.info("EcOrderRemote.queryUnableFinishOrder#orders:{} return {}", JSON.toJSONString(orders), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("EcOrderRemote.queryUnableFinishOrder#orders:{} 异常", JSON.toJSONString(orders), e);
        }
        throw new ServiceException("查询订单信息异常");
    }

    public LogisticsRemindDto queryLogisticsTrackCount() {
        try {
            log.info("EcOrderRemote.queryLogisticsTrackCount#");
            ApiRPCResult<LogisticsRemindDto> result = ecOrderApi.queryLogisticsRemind(null);
            log.info("EcOrderRemote.queryLogisticsTrackCount#: return {}", JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("EcOrderRemote.queryLogisticsTrackCount#: 异常", e);
        }
        return null;
    }

    public boolean confirmReceipt(ConfirmReceiptDto confirmReceiptDto){
        try {
            ApiRPCResult apiRPCResult = orderBusinessApi.confirmReceipt(confirmReceiptDto);
            log.info("EcOrderRemote.confirmReceipt#error request confirmReceipt:{} ,apiRPCResult : {}", JSONObject.toJSONString(confirmReceiptDto),JSONObject.toJSONString(apiRPCResult));
            if (apiRPCResult.isSuccess()){
                return true;
            }
        } catch (Exception e) {
            log.error("EcOrderRemote.confirmReceipt#error request confirmReceipt:{}", JSONObject.toJSONString(confirmReceiptDto), e);
        }

        return false;

    }


    public List<PopOrderRefundDto> queryRefundByOrderNo(String orderNo) {
        try {
            log.info("queryRefundByOrderNo:{}", orderNo);
            ApiRPCResult<List<PopOrderRefundDto>> result = popOrderRefundApi.queryRefundByOrderNo(orderNo);
            log.info("queryRefundByOrderNo:{},result:{}", orderNo, JSON.toJSONString(result));
            if (result == null || result.isFail() ||Objects.isNull(result.getData())) {
                return Lists.newArrayList();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("queryRefundByOrderNo:{}", orderNo, e);
            throw  new ServiceRuntimeException("查询退款单异常");
        }
    }

    public PopOrderDto queryOrderInfo(String orderNo) {
        try {
            log.info("queryOrderInfo:{}", orderNo);
            ApiRPCResult<PopOrderDto> result = ecOrderApi.queryOrderInfo(orderNo);
            log.info("queryOrderInfo:{},result:{}", orderNo, JSON.toJSONString(result));
            if (result == null || result.isFail() ||Objects.isNull(result.getData())) {
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("queryOrderInfo:{}", orderNo, e);
            throw  new ServiceRuntimeException("查询订单异常");
        }
    }
    public List<PopOrderDto> queryOrderBaseList(List<String> orderNos) {
        try {
            log.info("queryOrderBaseList:{}", orderNos);
            if (CollectionUtils.isEmpty(orderNos)) {
                return new ArrayList<>();
            }
            ApiRPCResult<List<PopOrderDto>>  result = ecOrderApi.queryOrderBaseList(orderNos);
            log.info("queryOrderBaseList:{},result:{}", orderNos, JSON.toJSONString(result));
            if (result == null || result.isFail() ||Objects.isNull(result.getData())) {
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("queryOrderInfo:{}", orderNos, e);
            throw  new ServiceRuntimeException("查询订单异常");
        }
    }
}
