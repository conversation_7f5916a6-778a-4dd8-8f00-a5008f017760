package com.xyy.ec.pop.utils;

import com.deepoove.poi.XWPFTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

/**
 * @Description 图片下载地址
 * <AUTHOR>
 * @Date 2021/1/27
 */
@Slf4j
@Component
public class WordDownloadUtils {
    @Value("${image.download.read.timeout:10000}")
    private Integer readTimeout;
    private static WordDownloadUtils wordDownloadUtils;

    @PostConstruct
    public void init() {
        wordDownloadUtils = this;
        wordDownloadUtils.readTimeout = this.readTimeout;
    }

    public static XWPFTemplate downloadWord(String url, Map<String,Object> map) {
        HttpURLConnection conn = null;
        InputStream inputStream = null;
        try {
            URL httpUrl = new URL(url);
            conn = (HttpURLConnection) httpUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setDoInput(true);
            conn.setDoOutput(true);
            conn.setUseCaches(false);
            conn.setConnectTimeout(3000);
            conn.setReadTimeout(wordDownloadUtils.readTimeout);
            // 连接指定的资源
            conn.connect();
            // 获取网络输入流
            inputStream = conn.getInputStream();
            return  XWPFTemplate.compile(inputStream).render(map);
        } catch (Exception e) {
            log.warn("wordDownloadUtils.downloadWord url:{} 异常", url, e);
            return null;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.warn("wordDownloadUtils.downloadWord url:{} 文件输入流关闭异常", url, e);
            }
        }
    }
}
