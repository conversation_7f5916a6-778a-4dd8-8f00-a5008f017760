//package com.xyy.ec.pop.controller;
//
//import com.alibaba.fastjson.JSONArray;
//import com.xyy.ec.pop.base.BaseController;
//import com.xyy.ec.pop.config.AvoidRepeatableCommit;
//import com.xyy.ec.pop.exception.PopAdminException;
//import com.xyy.ec.pop.server.api.Enum.EnjoyCommissionDiscountEnum;
//import com.xyy.ec.pop.service.CommissionDiscountConfigService;
//import com.xyy.ec.pop.vo.CommissionDiscountSetVo;
//import com.xyy.ec.pop.vo.ResponseVo;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.ResponseBody;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @Auther: lijincheng
// * @Date: 2021/09/13/15:41
// * @Description:
// */
//@Controller
//@Slf4j
//@Component
//@RequestMapping("/commission/config")
//public class CommissionDiscountConfigController extends BaseController {
//    @Autowired
//    private CommissionDiscountConfigService configService;
//
//    /**
//     *
//     * @param orgId
//     * @param config 佣金折扣配置json
//     * @param flag 0-不享受优惠，1-享受以下优惠
//     * @return
//     */
//    @RequestMapping(value = "/save", method = RequestMethod.POST)
//    @ResponseBody
//    @AvoidRepeatableCommit(timeout = 3)
//    public ResponseVo saveCommissionConfig(String orgId, String config, Integer flag) {
//        try {
//            log.info("CommissionDiscountConfigController.saveCommissionConfig orgId:{},config:{}", orgId, config);
//            if (StringUtils.isEmpty(orgId)) {
//                return ResponseVo.errRest("机构编码不能为空");
//            }
//            if (flag == EnjoyCommissionDiscountEnum.ENJOY_TRUE.getFlag() && StringUtils.isBlank(config)) {
//                return ResponseVo.errRest("佣金折扣不能为空");
//            }
//            Boolean aBoolean = configService.saveCommissionDiscountConfig(orgId, getUser().getUsername(), JSONArray.parseArray(config,CommissionDiscountSetVo.class),flag);
//            return aBoolean ? ResponseVo.successResult("保存佣金折扣配置成功") : ResponseVo.errRest("保存佣金折扣配置异常");
//        } catch (IllegalArgumentException i) {
//            log.error("CommissionDiscountConfigController.saveCommissionConfig error1 保存佣金折扣配置异常 orgId:{},config:{},flag:{}", orgId, config, flag, i);
//            return ResponseVo.errRest(i.getMessage());
//        } catch (PopAdminException ae) {
//            log.error("CommissionDiscountConfigController.saveCommissionConfig error2 保存佣金折扣配置异常 orgId:{},config:{},flag:{}", orgId, config, flag, ae);
//            return ResponseVo.errRest(ae.getMessage());
//        } catch (Exception e) {
//            log.error("CommissionDiscountConfigController.saveCommissionConfig error3 保存佣金折扣配置异常 orgId:{},config:{},flag:{}", orgId, config, flag, e);
//            return ResponseVo.errRest("保存失败");
//        }
//    }
//
//}
