package com.xyy.ec.pop.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description 商品佣金设置 列表数据
 */
@Data
public class ProductCommissionVo {
    /**
     * 机构id
     */
    private String orgId;
    /**
     * 商户名称
     */
    private String orgName;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 商品编码
     */
    private String barcode;
    /**
     * 展示名称
     */
    private String showName;
    /**
     * 标准库ID
     */
    private String standardProductId;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 规格
     */
    private String spec;
    /**
     * 商品状态
     */
    private Integer status;
    private String statusName;
    /**
     * 商品佣金比例
     */
    private BigDecimal commissionRatio;
    /**
     * 一级发布分类编码
     */
    private String businessFirstCategoryCode;
    /**
     * 一级发布分类名
     */
    private String businessFirstCategoryName;
    /**
     * 商品类型，参考 ActivityTypeEnum
     */
    private Integer activityType;
    /**
     * csuid
     */
    private Long csuid;
}
