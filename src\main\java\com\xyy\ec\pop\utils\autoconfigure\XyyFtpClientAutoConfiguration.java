package com.xyy.ec.pop.utils.autoconfigure;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * FtpClient连接池自动配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configurable
@ConditionalOnClass(value = {FTPClient.class, BasePooledObjectFactory.class, GenericObjectPool.class})
@ConditionalOnProperty(prefix = "xyy.ftp_client", name = "enabled", matchIfMissing = true, havingValue = "true")
@EnableConfigurationProperties(value = {XyyFtpClientProperties.class})
public class XyyFtpClientAutoConfiguration {

    @Bean
    public XyyFtpClientFactory getXyyFtpClientFactory(@Autowired XyyFtpClientProperties xyyFtpClientProperties) {
        return new XyyFtpClientFactory(xyyFtpClientProperties);
    }

    @Bean
    public XyyFtpClientTemplate getXyyFtpTemplate(@Autowired XyyFtpClientProperties xyyFtpClientProperties,
                                                  @Autowired XyyFtpClientFactory xyyFtpClientFactory) {
        return new XyyFtpClientTemplate(xyyFtpClientProperties, xyyFtpClientFactory);
    }

}
