package com.xyy.ec.pop.marketing.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.marketing.insight.params.MarketCustomerGroupMerchantQueryParam;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupDTO;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupMerchantInfoDTO;
import com.xyy.ec.pop.marketing.param.CustomerGroupQueryParam;
import com.xyy.ec.pop.marketing.vo.CustomerGroupVO;

import java.util.List;

public interface InsightService {

    /**
     * 分页查询人群信息
     *
     * @param customerGroupQueryParam
     * @return
     */
    PageInfo<CustomerGroupVO> pagingChosenGroups(CustomerGroupQueryParam customerGroupQueryParam);

    /**
     * 分页查询人群的药店信息
     *
     * @param queryParam
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageInfo<MarketCustomerGroupMerchantInfoDTO> pagingCustomerGroupMerchants(MarketCustomerGroupMerchantQueryParam queryParam,
                                                                              int pageNum, int pageSize);

    /**
     * 人群定义
     * @param marketCustomerGroupDTO
     * @return
     */
    List<List<String>> getContentBundleDescriptions(MarketCustomerGroupDTO marketCustomerGroupDTO);

}
