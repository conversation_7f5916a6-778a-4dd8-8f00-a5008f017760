package com.xyy.ec.pop.vo.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.ec.pop.base.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description 订单
 */
@Data
public class OrderAdminVo extends BaseEntity {
    //  运费金额
    private Double freightAmount;

    /* 商家留言 */
    private String merchantRemark;
    /**
     * 次日达
     */
    private  Integer nextDayDelivery;

    /** 签收时间 */
    private String arrivalTime;
    /**
     * 物流公司
     */
    private String logisticsWay;
    /**
     * 商户编号
     */
    private String corporationNo;
    /**
     * 店铺名称
     */
    private String corporationName;

    /**
     * 商户名称
     */
    private String companyName;

    /**
     * 机构id集合
     */
    private List<String> orgIdList;

    /**
     * 扩展信息 json
     */
    private String extraInfoJson;
    /** 订单信息 */
    private Long id;

    /**  */
    private String branchCode;

    /**
     * 区域名称
     */
    private String branchName;

    /** 机构ID */
    private String orgId;

    /** 商户编号 */
    private Long merchantId;

    /** 订单编号 */
    private String orderNo;

    /** 品种数量 */
    private Integer varietyNum;

    /** 收货人 */
    private String contactor;

    /** 收货人 */
    private String merchantErpCode;

    /** 开户状态 */
    private Byte accountStatus;

    /** 手机号 */
    private String mobile;

    /** 订单实付金额 */
    private BigDecimal money;

    /**
     * 现金实付
     */
    private BigDecimal cashPayAmount;

    /**
     * 购物金
     */
    private BigDecimal virtualGold;

    /** 支付类型(1:在线支付 2:货到付款 3:线下转账） */
    private Integer payType;

    /** 支付渠道(1-支付宝,2-微信,3-银联,7-电汇平台，8-电汇商业) */
    private Integer payChannel;

    /** 在线支付时间 */
    private Date payTime;

    /** 转账时间(争对线下转账) */
    private Date paymentTime;

    /** 发票信息 */
    private String billInfo;

    /** 发票类型 1:普通发票 2:专用发票 */
    private Integer billType;

    /** 订单状态：1-待配送，2-配送中，3-已配送，4-取消，5-已删除,6-已拆单,7-出库中,10-未支付,90-退款审核中,91-已退款,20-已送达,21-已拒签 */
    private Integer status;

    /** 审单流程状态：默认：0-尚未进入审单流程,1-审单中，2-开票中，3-发货中，9-审单流程结束 */
    private Integer checkStatus;

    /** 计划配送时间 */
    private Date deliveryTime;

    /** 实际配送时间 */
    private Date shipTime;

    /** 完成时间 */
    private Date finishTime;

    /** 总金额=实付金额+优惠金额 */
    private BigDecimal totalAmount;

    /** 优惠金额 */
    private BigDecimal discount;

    /** 父订单ID */
    private Long parentId;

    /** 是否可见 */
    private Integer visibled;
    public static final Integer STATUS_VISIBLED = 1;
    public static final Integer STATUS_NOTVISIBLED = 0;

    /** 紧急程度 0(默认:备注) 1:低  5:中  10:高 */
    private Integer urgencyDegree;

    /** 紧急程度描述 */
    private String urgencyInfo;

    /** 备注最后更新时间 */
    private Date urgencyUpdateTime;

    /** 订单原始支付金额(用于退款渠道和对账单作验证使用) */
    private BigDecimal sourcePayMoney;

    /** 省份编码 */
    private Integer provinceCode;

    /** 市区编码 */
    private Integer cityCode;

    /** 区域编码 */
    private Integer areaCode;

    /** 订单来源 0:手动添加 1:Android 2:IOS 3:H5 4:PC */
    private Integer orderSource;

    /** 总数量 */
    private Integer totalcount;

    /** 验证重复下单 */
    private String tranNo;

    /** 订单(签收/拒签)时间 */
    private Date deliveredTime;

    /** 申请退款次数 */
    private Integer refundCount;

    /** 销售工号 */
    private String salesJobNo;

    /** 销售姓名 */
    private String salesName;

    /** 销售员ID */
    private Long salesId;

    /** 配送方式: 0:未知 1:自营 2:第三方 3:其他 */
    private Integer shipType;

    /** 时空编号:SKPZxxxxxx */
    private String skCode;

    /** 集货区编码 */
    private String storeCode;

    /** 箱数 */
    private Integer boxes;

    /** 是否分配 0:未分配 1:已分配 */
    private Integer haveSchedule;

    /** 过单时间 */
    private Date passTime;

    /** 地址id */
    private Long addressId;

    /** 商品总数量 */
    private Integer productNum;

    /** 药店名称 */
    private String merchantName;

    /** 不返点商品总金额 */
    private BigDecimal noRebateTotalAmount;

    /** 不返点商品满减金额 */
    private BigDecimal noRebateDiscountAmount;

    /** 不返点商品优惠券金额 */
    private BigDecimal noRebateVoucherAmount;

    /** 返点商品总金额 */
    private BigDecimal rebateTotalAmount;

    /** 返点商品满减金额 */
    private BigDecimal rebateDiscountAmount;

    /** 返点商品优惠券金额 */
    private BigDecimal rebateVoucherAmount;

    /** 全场满减金额 */
    private BigDecimal wholeDiscountAmount;

    /** 全场优惠券金额 */
    private BigDecimal wholeVoucherAmount;

    /** 商户类型标识,暂提供连锁药店使用(3) */
    private Integer businessType;

    private String businessTypeName;

    /** 收货人地址 */
    private String address;

    /** 备注 */
    private String remark;

    /** 创建人 */
    private String creator;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updator;

    /** 修改时间 */
    private Date updateTime;

    /**发票文案**/
    private String invoinceText;

    /**是否显示查看发票按钮**/
    private Integer isShowInvoinceButton;

    /** 订单状态名称 */
    private String statusName;

    /** 物流单号 */
    private String trackingNo;

    /**
     * 可用额度
     */
    private BigDecimal usedLimit;

    /**
     * 是否展示支付声明  电汇商业   是否展示支付声明   1：展示   0不展示
     */
    private String showStatement = "0";



    /************************************************** 扩展字段 *************************************************/

    /** 是否可拆单还原:0 = 不可还原; 1 = 可还原 */
    private Integer separateFlag;

    /** 创建时间始 */
    private Date startCreateTime;

    /** 创建时间止 */
    private Date endCreateTime;

    /** 支付开始时间 */
    private Date startPayTime;

    /** 支付结束时间 */
    private Date endPayTime;

    /** 详细地址 */
    private String gspAddress;

    /** 地址状态: 0:默认无修改 1:用户已点击过弹窗 2:用户有修改过备注地址 3:用户备注地址已审过 4:用户备注地址没有审核通过,需要复审 */
    private Integer gspAuditState;

    /** 会员备注 */
    private String gspRemark;

    /** 活动返余额 */
    private BigDecimal rebateBalanceAmt;

    /**订单来源数组*/
    private Integer[] orderSourceArray;

    /** 支付方式名称 */
    private String payTypeName;

    /** 支付渠道名称 */
    private String payChannelName;

    //是否使用余额 默认为true
    private boolean useBalance = true;

    //是否有领取余额(0:未领取 1：已领取 2：不能领 )
    private Integer balanceStatus;

//    private List<ActivityPackage> packageList; //套餐列表

    /** 订单支付倒计时 */
    private String payEndTime;

    /** IOS使用的倒计时支付毫秒数 */
    private Long countDownNewTime;

    /** 倒计时相应的时间戳 毫秒数*/
    private Long countDownTime;

    //领取余额文案
    private String balanceText;

    //使用的余额金额
    private BigDecimal balanceAmount;
    private String balanceRemark;		//余额备注  --已领取余额100.0元/订单完成后预计可领取余额300.00元

    //申请退款文案
    private String refundText;

    /** 可以确认收货按钮 */
    private Integer canConfirmReceipt;
    private BigDecimal fullDivPrice;		//订单满减金额
    private BigDecimal voucherDivPrice;		//订单优惠券减免金额
    private BigDecimal rebate;				//订单返点金额

    /** 订单起送价格 */
    private BigDecimal startingPrice;
    /** 使用的优惠券 */
    private String voucherId;
    /** 选中的多个优惠券ID，用逗号分隔 */
    private String voucherIds;
//    private Voucher voucher;
    //参与优惠计算明细总金额
    private BigDecimal takeDiscountTotalAmt;
    /** app版本 */
    @JsonIgnore
    private Integer appVersion;
    //状态数组
    private Integer[] statuses;
    //退款审核状态数组
    private Integer[] auditStateArray;
    //根据药品名查询
    private String name;
    //是否首字母查询
    @JsonIgnore
    private Boolean isLettersQuery;


    // 区分查询订单详情列表时候的状态
    private Integer sceneType;

    //图片路径
    private String imageUrl;
    /** 优惠券优惠总金额 */
    private BigDecimal voucherDiscountAmount;
    /** 促销活动优惠总金额 */
    private BigDecimal promoDiscountAmount;

//    /** 订单优惠记录 */
//    @JsonIgnore
//    private List<OrderAdjust> orderAdjustList;

    /**为了不对原订单编号造成污染，所以先开一个字段*/
    //预生成订单号
    @JsonIgnore
    private String preOrderNo;

    //优惠类型
    private Integer discountType;
    //跳转类型 1 只有自营跳支付 2 包含第三方订单跳订单列表
    @JsonIgnore
    private Integer jumpType;
    /** 是否第三方厂家（0：否；1：是） */
    private Integer isThirdCompany;

    /** 有无退款文本 */
    private String hasRefundText;
    /** 查询条件 0 -无退款 1 - 有退款 */
    private Integer hasRefund;

    /**商户类型（业务类型）：1-个体药店，2-连锁药店（加盟），3-连锁药店（直营），4-诊所 */
    private Integer businessTypeSearch;

    /** 注册手机号 */
    private String loginMobile;

    /** 原总金额(秒杀/直降/特价/拼团按原价计算)*/
    private BigDecimal originalTotalAmount = BigDecimal.ZERO;

    /** 原总优惠金额(包含秒杀/直降/特价/拼团优惠金额)*/
    private BigDecimal originalTotalDiscount = BigDecimal.ZERO;

    /**
     * 订单状态集合字符串
     */
    private String statusList;
    /**
     * 未发货订单超时时间
     */
    private Integer timeoutHours;
    /**
     * 是否首单 1-是 2-否
     */
    private Integer firstOrderFlag;
    /**
     * 订单类型 7-拼团
     */
    private Integer orderType;
    //退款同步状态：0-未下发 1-已下发 2-下发失败
    private Integer orderSyncStatus;
    //失败 原因
    private String orderSyncErrorReason;
    //开始  订单完成时间
    private Date startFinishTime;
    //结束  订单完成时间
    private Date endFinishTime;

    /**
     * 省ID
     */
    private Long provId;


    /**
     * 省份名称
     */
    private String prov;

    /**
     * 省份id集合
     */
    private List<Long> provIds;
    //电汇凭证审核状态 0-待上传电汇凭证、1-待审核电汇凭证、2-审核通过
    private Integer evidenceVerifyStatus;
    //电汇图片列表
    private List<String> evidenceImages;

    private List<String> orderNos;
    /* 是否 是虚拟供应商 0:否，1:是 精准搜索*/
    private Integer isVirtualSupplier;
    /* 批量查询高毛 */
    private String isHighGrossStr;

    /* 是否入仓订单 精准搜索 0 否 1是 */
    private Integer isFbp;
    /**
     * 轨迹获取失败
     */
    private Boolean logisticsTrackFail;
    /**
     * 轨迹数据不合法
     */
    private Boolean logisticsTrackIllegal;

    /**
     * 是否随心拼订单 0否1是
     */
    private Integer isRandom;

    /**
     * 0-资质正常
     * 1-资质异常
     */
    private Integer exceptionFlag;
}
