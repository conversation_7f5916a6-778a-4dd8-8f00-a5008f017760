package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.address.api.BaseRegionBusinessApi;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.address.dto.XyyRegionParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @Date: 2021/3/12
 */
@Service
@Slf4j
public class BaseRegionRemote {

    @Reference(version = "1.0.0")
    BaseRegionBusinessApi baseRegionBusinessApi;

    /**
     * parentCodes、areaCodes 列表长度为0是会重置为null
     * @param params
     * @return
     */
    public List<XyyRegionBusinessDto> queryRegion(XyyRegionParams params) {
        log.info("BaseRegionRemote.queryRegion params:{}", JSON.toJSONString(params));
        try {
            List<XyyRegionBusinessDto> result = baseRegionBusinessApi.queryRegion(params);
            log.info("BaseRegionRemote.queryRegion apiRPCResult: [{}]", JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("BaseRegionRemote.queryRegion 系统异常!",e);
            return Lists.newArrayList();
        }
    }

    public XyyRegionBusinessDto getRegion(Integer areaCode) {
        try {
            XyyRegionParams params = new XyyRegionParams();
            params.setAreaCode(areaCode);
            XyyRegionBusinessDto xyyRegionBusinessDto = baseRegionBusinessApi.queryRegionByAreaCode(params);
            return xyyRegionBusinessDto;
        } catch (Exception e) {
            log.error("查询地区异常，areaCode={},e=", areaCode, e);
        }
        return null;
    }

    public List<XyyRegionBusinessDto> queryRegionByAreaCodeList(List<Integer> areaCodes) {
        try {
            XyyRegionParams xyyRegionParams = new XyyRegionParams();
            xyyRegionParams.setAreaCodes(areaCodes);
            return baseRegionBusinessApi.queryRegionByAreaCodeList(xyyRegionParams);
        } catch (Exception e) {
            log.error("查询地区列表异常，areaCodes={},e", JSON.toJSONString(areaCodes), e);
        }
        return Lists.newArrayList();
    }

    public Map<Integer,XyyRegionBusinessDto> getRegionMap(List<Integer> areaCodes) {
        List<List<Integer>> partition = Lists.partition(areaCodes, 100);
        Map<Integer,XyyRegionBusinessDto> result = Maps.newHashMap();
        for (List<Integer> integers : partition) {
            List<XyyRegionBusinessDto> regionList = queryRegionByAreaCodeList(integers);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(regionList)) {
                continue;
            }
            regionList.forEach(x -> result.put(x.getAreaCode(), x));
        }
        return result;
    }
}
