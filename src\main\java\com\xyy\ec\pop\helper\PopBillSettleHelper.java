package com.xyy.ec.pop.helper;

import com.google.common.collect.Lists;
import com.xyy.ec.pop.excel.entity.PopBillSettleExportVo;
import com.xyy.ec.pop.po.PopBillSettlePo;
import com.xyy.ec.pop.server.api.admin.dto.CommissionSettleSetDto;
import com.xyy.ec.pop.server.api.order.dto.PopBillSettleDto;
import com.xyy.ec.pop.server.api.order.enums.OrderPayTypeEnums;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.vo.AdjustiveBillSettleVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class PopBillSettleHelper {
    public static PopBillSettlePo convertPopBillSettleVo(PopBillSettleVo popBillSettleVo) {
        if (null == popBillSettleVo) {
            return null;
        }
        PopBillSettlePo popBillSettlePo = new PopBillSettlePo();
        popBillSettlePo.setId(popBillSettleVo.getId());
        popBillSettlePo.setOrgId(popBillSettleVo.getOrgId());
        popBillSettlePo.setOrgName(popBillSettleVo.getOrgName());
        popBillSettlePo.setName(popBillSettleVo.getName());
        popBillSettlePo.setBranchCode(popBillSettleVo.getBranchCode());
        popBillSettlePo.setAreaOrgId(popBillSettleVo.getAreaOrgId());
        popBillSettlePo.setBusinessType(popBillSettleVo.getBusinessType());
        popBillSettlePo.setBusinessNo(popBillSettleVo.getBusinessNo());
        popBillSettlePo.setMerchantName(popBillSettleVo.getMerchantName());
        popBillSettlePo.setProductMoney(popBillSettleVo.getProductMoney());
        popBillSettlePo.setTotalMoney(popBillSettleVo.getTotalMoney());
        popBillSettlePo.setMoney(popBillSettleVo.getMoney());
        popBillSettlePo.setFreightAmount(popBillSettleVo.getFreightAmount());
        popBillSettlePo.setCouponShopAmount(popBillSettleVo.getCouponShopAmount());
        popBillSettlePo.setMarketingShopAmount(popBillSettleVo.getMarketingShopAmount());
        popBillSettlePo.setShopTotalDiscount(popBillSettleVo.getShopTotalDiscount());
        popBillSettlePo.setCouponPlatformAmount(popBillSettleVo.getCouponPlatformAmount());
        popBillSettlePo.setMarketingPlatformAmount(popBillSettleVo.getMarketingPlatformAmount());
        popBillSettlePo.setPlatformTotalDiscount(popBillSettleVo.getPlatformTotalDiscount());
        popBillSettlePo.setHireMoney(popBillSettleVo.getHireMoney());
        popBillSettlePo.setPayableCommission(popBillSettleVo.getPayableCommission());
        popBillSettlePo.setSettlementType(popBillSettleVo.getSettlementType());
        popBillSettlePo.setPenaltyAmount(popBillSettleVo.getPenaltyAmount());
        popBillSettlePo.setStatementTotalMoney(popBillSettleVo.getStatementTotalMoney());
        popBillSettlePo.setOrderPayTime(popBillSettleVo.getOrderPayTime());
        popBillSettlePo.setOrderFinishTime(popBillSettleVo.getOrderFinishTime());
        popBillSettlePo.setPayType(popBillSettleVo.getPayType());
        popBillSettlePo.setOrderSettlementStatus(popBillSettleVo.getOrderSettlementStatus());
        popBillSettlePo.setOrderSettlementTime(popBillSettleVo.getOrderSettlementTime());
        popBillSettlePo.setBillCreateTime(popBillSettleVo.getBillCreateTime());
        popBillSettlePo.setBillCreateStatus(popBillSettleVo.getBillCreateStatus());
        popBillSettlePo.setCreateTime(popBillSettleVo.getCreateTime());
        popBillSettlePo.setUpdateTime(popBillSettleVo.getUpdateTime());
        popBillSettlePo.setStartOrderSettlementTime(popBillSettleVo.getStartOrderSettlementTime());
        popBillSettlePo.setEndOrderSettlementTime(popBillSettleVo.getEndOrderSettlementTime());
        popBillSettlePo.setStartOrderPayTime(popBillSettleVo.getStartOrderPayTime());
        popBillSettlePo.setEndOrderPayTime(popBillSettleVo.getEndOrderPayTime());
        popBillSettlePo.setOrgIds(popBillSettleVo.getOrgIds());
        return popBillSettlePo;
    }

    public static List<PopBillSettleExportVo> convertPopBillSettleExportVo(List<PopBillSettlePo> list) {
        List<PopBillSettleExportVo> popBillSettleExportVoList = new ArrayList<>();
        list.forEach(popBillSettlePo -> {
            PopBillSettleExportVo popBillSettleExportVo = new PopBillSettleExportVo();
            popBillSettleExportVo.setOrgId(popBillSettlePo.getOrgId());
            popBillSettleExportVo.setOrgName(popBillSettlePo.getOrgName());
            popBillSettleExportVo.setName(popBillSettlePo.getName());
            popBillSettleExportVo.setBusinessNo(popBillSettlePo.getBusinessNo());
            popBillSettleExportVo.setBusinessType(popBillSettlePo.getBusinessType());
            popBillSettleExportVo.setMerchantName(popBillSettlePo.getMerchantName());
            popBillSettleExportVo.setProductMoney(popBillSettlePo.getProductMoney());
            popBillSettleExportVo.setFreightAmount(popBillSettlePo.getFreightAmount());
            popBillSettleExportVo.setTotalMoney(popBillSettlePo.getTotalMoney());
            popBillSettleExportVo.setShopTotalDiscount(popBillSettlePo.getShopTotalDiscount());
            popBillSettleExportVo.setPlatformTotalDiscount(popBillSettlePo.getPlatformTotalDiscount());
            popBillSettleExportVo.setMoney(popBillSettlePo.getMoney());
            popBillSettleExportVo.setHireMoney(popBillSettlePo.getHireMoney());
            popBillSettleExportVo.setPayableCommission(popBillSettlePo.getPayableCommission());
            popBillSettleExportVo.setSettlementType(popBillSettlePo.getSettlementType());
            popBillSettleExportVo.setStatementTotalMoney(popBillSettlePo.getStatementTotalMoney());
            popBillSettleExportVo.setOrderSettlementStatus(popBillSettlePo.getOrderSettlementStatus());
            popBillSettleExportVo.setOrderPayTime(popBillSettlePo.getOrderPayTime());
            popBillSettleExportVo.setOrderSettlementTime(popBillSettlePo.getOrderSettlementTime());
            popBillSettleExportVoList.add(popBillSettleExportVo);
        });
        return popBillSettleExportVoList;
    }

    public static List<PopBillSettleDto> buildBillSettleDtos(List<AdjustiveBillSettleVo> vos, String userName, Map<String, CommissionSettleSetDto> orgIdSettleSetMap) {
        if (CollectionUtils.isEmpty(vos)) {
            return Lists.newArrayList();
        }
        return vos.stream().map(vo -> {
            PopBillSettleDto billSettleDto = new PopBillSettleDto();
            billSettleDto.setOrgId(vo.getOrgId());
            billSettleDto.setPayType(OrderPayTypeEnums.OFFLINE_PLATFORM.getType());
            Byte settlementType = orgIdSettleSetMap.get(vo.getOrgId()).getSettlementType();
            billSettleDto.setSettlementType(settlementType);
            if (settlementType == CommissionSettleTypeEnum.NOT_EVERY_MONTH.getCode()) {
                billSettleDto.setHireMoney(StringUtils.isBlank(vo.getHireMoney()) ? BigDecimal.ZERO : new BigDecimal(vo.getHireMoney()));
                billSettleDto.setPayableCommission(BigDecimal.ZERO);
            } else {
                billSettleDto.setPayableCommission(StringUtils.isBlank(vo.getHireMoney()) ? BigDecimal.ZERO : new BigDecimal(vo.getHireMoney()));
                billSettleDto.setHireMoney(BigDecimal.ZERO);
            }
            if (StringUtils.isBlank(vo.getPlatformTotalDiscount())) {
                billSettleDto.setPlatformTotalDiscount(BigDecimal.ZERO);
            } else {
                billSettleDto.setPlatformTotalDiscount(new BigDecimal(vo.getPlatformTotalDiscount()));
            }
            billSettleDto.setStatementTotalMoney(new BigDecimal(vo.getStatementTotalMoney()));
            billSettleDto.setDeductedCommission(new BigDecimal(vo.getDeductedCommission()));
            billSettleDto.setRemark(vo.getRemark());
            billSettleDto.setCreator(userName);
            return billSettleDto;
        }).collect(Collectors.toList());
    }
}
