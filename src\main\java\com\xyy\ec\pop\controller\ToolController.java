package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.base.ResultCodeEnum;
import com.xyy.ec.pop.base.ResultVO;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.dto.PopCorporationDto;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.redis.impl.RedisService;
import com.xyy.ec.pop.remote.*;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.product.api.FixProductUtilApi;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuAdminApi;
import com.xyy.ec.pop.server.api.product.api.task.ProductBaseInfoMonitorApi;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDetailDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.server.api.product.dto.SkuAdminPageQuery;
import com.xyy.ec.pop.server.api.product.enums.ActivityTypeEnum;
import com.xyy.ec.pop.server.api.product.enums.PopSkuStatus;
import com.xyy.ec.pop.server.api.seller.dto.PopSupplierMenuDto;
import com.xyy.ec.pop.server.api.tool.api.ConfigOrgApi;
import com.xyy.ec.pop.server.api.tool.api.ProductToolApi;
import com.xyy.ec.pop.server.api.tool.dto.ConfigOrgShowDto;
import com.xyy.ec.pop.server.api.tool.enmus.ConfigApolloKey;
import com.xyy.ec.pop.service.RedoRetryInfoService;
import com.xyy.ec.pop.service.ToolService;
import com.xyy.ec.pop.utils.JsonUtil;
import com.xyy.ec.pop.vo.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>工具类接口</p>
 *
 * <AUTHOR>
 * @Date: 2021/2/25
 */

@Slf4j
@Controller
@RequestMapping("/tool")
public class ToolController extends BaseController {

    @Autowired
    private ToolService toolService;
    @Reference
    private ConfigOrgApi configOrgApi;
    @Autowired
    private SupplierUserRemote supplierUserRemote;
    @Autowired
    private RedoRetryInfoService redoRetryInfoService;
    @Autowired
    private ToolRemote toolRemote;
    @Value("${product.tools.deleteSku.users}")
    private String deleteSkuUser;
    @Value("${product.tools.deleteSku.steps:50}")
    private Integer deleteSkuSteps;
    @Autowired
    private ProductSkuRemoteAdapter skuRemoteAdapter;
    @Autowired
    private EcSkuRemoteAdapter ecSkuRemoteAdapter;
    @Autowired
    private PopCorporationRemoteAdapter corporationRemoteAdapter;
    @Reference
    private ProductBaseInfoMonitorApi productBaseInfoMonitorApi;
    @Value("${auto.apply.invoice.date:202210}")
    private String autoApplyInvoiceDate;
    @Resource
    private RedisService redisService;
    @Reference
    private PopSkuAdminApi popSkuAdminApi;
    /**
     * 特殊扣保证金场景tool
     */
    @PostMapping("/deductFund")
    @ResponseBody
    public ResultVO<Boolean> deductFund(@RequestBody DeductFundToolVo deductFundToolVo) {
        log.info("deductFund param:{}", JsonUtil.toJson(deductFundToolVo));
        String msg = deductFundToolVo.validParam();
        if (StringUtils.isNotEmpty(msg)) {
            return ResultVO.createError(msg);
        }
        try {
            toolService.deductFund(deductFundToolVo);
            return ResultVO.createSuccess();
        } catch (Exception e) {
            log.error("updateCorporationInfo error", e);
            return ResultVO.createError(e.getMessage());
        }
    }
    /**
     * 企业结算主体信息修改
     */
    @PostMapping("/updateCorporationInfo")
    @ResponseBody
    public ResultVO<Boolean> updateCorporationInfo(@RequestBody PopCorporationDto popCorporationDto) {
        log.info("updateCorporationInfo param:{}", JsonUtil.toJson(popCorporationDto));
        boolean validParam = popCorporationDto.validParam();
        if (!validParam || StringUtils.isBlank(popCorporationDto.getBusinessIdType())) {
            return ResultVO.createError(ResultCodeEnum.PARAM_ERROR);
        }

        String lockKey = "updateCorporationInfo:popAdmin:orgId_" + popCorporationDto.getOrgId();
        if (!redisService.setNx(lockKey, StringUtils.EMPTY, 5)) {
            return ResultVO.createError(ResultCodeEnum.NOT_CONTINUOUS_CLICK);
        }

        try {
            trimPopCorporationDto(popCorporationDto);
            return ResultVO.createSuccess(toolService.updateCorporationInfo(popCorporationDto));
        } catch (Exception e) {
            log.error("updateCorporationInfo error", e);
            return ResultVO.createError(e.getMessage());
        }
    }

    /**
     * 线下转账白名单批量新增
     */
    @PostMapping("/updateOfflinePayData")
    @ResponseBody
    public ResultVO<Boolean> updateOfflinePayData(@RequestBody OfflinePayWhitelistVo offlinePayWhitelistVo) {
        log.info("updateOfflinePayData param:{}", JsonUtil.toJson(offlinePayWhitelistVo));
        if (StringUtils.isEmpty(offlinePayWhitelistVo.getOrgIds())) {
            return ResultVO.createError("商业编号不能为空");
        }
        try {
            return ResultVO.createSuccess(toolService.updateOfflinePayData(offlinePayWhitelistVo));
        } catch (Exception e) {
            log.error("updateOfflinePayData error", e);
            return ResultVO.createError(e.getMessage());
        }
    }

    private void trimPopCorporationDto(PopCorporationDto dto) {
        dto.setOrgId(dto.getOrgId().trim());
        dto.setCompanyName(dto.getCompanyName().trim());
        dto.setReprGlobalId(dto.getReprGlobalId().trim());
        dto.setReprName(dto.getReprName().trim());
    }

    @GetMapping(value = "/product/maintain")
    @ApiOperation("跳转到处理商品页面")
    public String index() {
        return "product/maintain";
    }

    /**
     * @param orgId
     * @return
     */
    @PostMapping("/product/deleteErpSkusByOrgId")
    @ResponseBody
    public ResultVO deleteErpSkusByOrgId(String orgId,Integer deleteType) {
        log.info("ToolController.deleteProductsByOrgId orgId:{}-deleteType:{}", orgId,deleteType);
        if (StringUtils.isBlank(orgId)) {
            return ResultVO.createError("orgId不能为空");
        }
        if (deleteType == null){
            return ResultVO.createError("删除类型不能为空");
        }
        try {
            boolean resutl = toolService.deleteErpSkusByOrgId(orgId,deleteType);
            if (resutl) {
                return ResultVO.createSuccess();
            } else {
                return ResultVO.createError("删除临时表商品失败");
            }
        } catch (Exception e) {
            log.error("ToolController.deleteProductsByOrgId orgId:{}", orgId, e);
            return ResultVO.createError("删除临时表商品失败");
        }
    }

    @GetMapping(value = "/configOrgIndex")
    @ApiOperation("查询配置的白名单商业")
    public String configOrgIndex() {
        return "tool/configOrg";
    }

    @GetMapping(value = "/configOrgPage")
    @ResponseBody
    public Page<ConfigOrgShowDto> configOrgPage(String key, int offset, int limit){
        try {
            log.info("ToolController.apolloConfigOrg#key:{},pageSize:{},pageNum:{}", key, offset, limit);
            ApiRPCResult<PageInfo<ConfigOrgShowDto>> rPCResult = configOrgApi.apolloConfigOrg(ConfigApolloKey.get(key), limit,offset/limit+1 );
            log.info("ToolController.apolloConfigOrg#key:{},pageSize:{},pageNum:{} return {}", key, offset, limit, JSON.toJSONString(rPCResult));
            if (rPCResult.isSuccess()) {
                Page page = new Page(offset,limit);
                page.setTotal(rPCResult.getData().getTotal());
                page.setRows(rPCResult.getData().getList());
                return page;
            }
        } catch (Exception e) {
            log.error("ToolController.apolloConfigOrg#key:{},pageSize:{},pageNum:{} 异常",key, offset, limit, e);
        }
        return new Page(offset,limit);
    }

    /**
     * 根据机构ID查询可下推的订单列表
     *
     * @param orgId
     * @return
     */
    @PostMapping("/order/listPushOrderByOrgId")
    @ResponseBody
    public ResultVO listPushOrderByOrgId(String orgId) {
        log.info("ToolController.listPushOrderByOrgId orgId:{}", orgId);
        if (StringUtils.isBlank(orgId)) {
            return ResultVO.createError("orgId不能为空");
        }
        try {
            return ResultVO.createSuccess(toolService.listPushOrderByOrgId(orgId));
        } catch (Exception e) {
            log.error("ToolController.listPushOrderByOrgId orgId:{}", orgId, e);
            return ResultVO.createError("查询下推订单列表失败");
        }
    }

    @RequestMapping("/pullDataInfo")
    public String pullDataInfo(){
        return "plannedOrderManagement/order/pullDataInfo";
    }

    /**
     * 修复pop出库中订单状态及刷新缓存
     * @return
     */
    @RequestMapping("/order/repairPopOrderStatus")
    public String repairPopOrderStatus(){
        return "tool/repairPopOrderStatus";
    }


    /**
     * 修复pop出库中订单状态及刷新缓存
     * @param orderNos
     * @return
     */
    @PostMapping("/order/startRepairPopOrderStatus")
    @ResponseBody
    public ResultVO startRepairPopOrderStatus(String orderNos) {
        if (StringUtils.isBlank(orderNos)) {
            return ResultVO.createError("orderNos不能为空");
        }
        List<String> orderNoList = Splitter.on(",").splitToList(orderNos);
        if(orderNoList.size()>50){
            return ResultVO.createError("最多只能选择50个订单");
        }
        try {
            orderNoList.forEach(orderNo->{
                toolService.startRepairPopOrderStatus(orderNo);
            });

            return ResultVO.createSuccess("刷新完成");
        } catch (Exception e) {
            log.error("ToolController.startRepairPopOrderStatus orderNo:{}", orderNos, e);
            return ResultVO.createError("修复pop出库中订单状态及刷新缓存失败");
        }
    }

    @RequestMapping("/downloadFilePage")
    public String downloadFilePage() {
        return "/erpTools/downloadFileList";
    }

    @RequestMapping("/searchAccountPage")
    public String searchAccountPage() {
        return "tool/searchAccount";
    }

    @RequestMapping("/searchAccount")
    @ResponseBody
    public ResponseVo searchAccount(String searchKey){
        if (StringUtils.isBlank(searchKey)){
            return ResponseVo.successResultNotData();
        }
        try {
            List<SupplierUserVo> supplierUserList = supplierUserRemote.getSupplierUserList(searchKey);
            log.info("ToolController.searchAccount list:{}",JSON.toJSONString(supplierUserList));
            return ResponseVo.successResult(supplierUserList);
        }catch (Exception e){
            log.error("ToolController.searchAccount error param:{}",searchKey,e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    @RequestMapping("/switchPaymentChannelPage")
    public String switchPaymentChannelPage() {
        return "tool/switchPaymentChannel";
    }

    @RequestMapping("/switchPaymentChannel")
    @ResponseBody
    public ResponseVo switchPaymentChannel(String orgIds) {
        log.info("ToolController.switchPaymentChannel#orgIds:{}", orgIds);
        try {
            if (StringUtils.isBlank(orgIds)) {
                return ResponseVo.errRest("请输入最少一个orgId");
            }
            List<String> orgIdList = Splitter.on(",").splitToList(orgIds);
            toolRemote.switchPaymentChannel(orgIdList);
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("ToolController.switchPaymentChannel error param:{}", orgIds, e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    @RequestMapping("/billSettleTaskPage")
    public String billSettleTaskPage() {
        return "tool/billSettleTask";
    }

    /**
     * 手动执行结算任务
     *
     * @return
     */
    @RequestMapping("/execSettleTask")
    @ResponseBody
    public ResponseVo execSettleTask() {
        log.info("ToolController.execSettleTask#start");
        try {
            toolRemote.execSettleTask();
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("ToolController.execSettleTask#error.", e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    /**
     * 手动执行账单任务
     *
     * @return
     */
    @RequestMapping("/execBillTask")
    @ResponseBody
    public ResponseVo execBillTask() {
        log.info("ToolController.execBillTask#start");
        try {
            toolRemote.execBillTask();
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("ToolController.execBillTask#error.", e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    /**
     * 手动执行入账单任务
     *
     * @return
     */
    @RequestMapping("/execBillPaymentTask")
    @ResponseBody
    public ResponseVo execBillPaymentTask() {
        log.info("ToolController.execBillPaymentTask#start");
        try {
            toolRemote.execBillPaymentTask();
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("ToolController.execBillPaymentTask#error.", e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    @RequestMapping("/retryPage")
    public String retryPage() {
        return "tool/retryPage";
    }

    @RequestMapping("/getRedoRetry")
    @ResponseBody
    public ResponseVo getRedoRetry(Long id){
        try {
            if (id == null){
                return ResponseVo.successResultNotData();
            }
            RedoRetryInfoVo retryTaskInfoVo = redoRetryInfoService.getRetryTaskInfoById(id);
            return ResponseVo.successResult(retryTaskInfoVo);
        } catch (ServiceException e) {
            log.error("ToolController.searchAccount error",e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    @RequestMapping("/editRedoRetry")
    @ResponseBody
    public ResponseVo editRedoRetry(Long id,Integer flag){
        try {
            if (id == null || flag == null){
                return ResponseVo.errRest("参数不能为空");
            }
            RedoRetryInfoVo retryTaskInfoVo = redoRetryInfoService.getRetryTaskInfoById(id);
            if (retryTaskInfoVo == null) {
                return ResponseVo.errRest("id为【"+id+"】的重试任务不存在");
            }
            RedoRetryInfoVo vo = new RedoRetryInfoVo();
            vo.setId(id);
            // 失败任务更新状态重试
            if (flag == 1){
                vo.setRetryCount(0);
                vo.setIstatus((byte)0);
            }
            //停止重试任务
            else {
                vo.setIstatus((byte) 1);
            }
            Boolean aBoolean = redoRetryInfoService.updateRetryTask(vo);
            return aBoolean?ResponseVo.successResultNotData():ResponseVo.errRest("更新状态失败");
        } catch (ServiceException e) {
            log.error("ToolController.searchAccount error",e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    @RequestMapping("/skuPage")
    public String skuPage() {
        return "tool/skuPage";
    }

    @RequestMapping("/getSkuByStandardId")
    @ResponseBody
    public ResponseVo getSkuByStandardId(@NotBlank(message = "请输入标准库Id") String standardId) {
        try {
            log.info("ToolController.getSkuByStandardId param:{}",standardId);
            if (StringUtils.isBlank(standardId)){
                return ResponseVo.errRest("请输入标准库Id");
            }
            Map<String, Object> productVo = toolService.getProductByStandardId(standardId);
            log.info("ToolController.getSkuByStandardId result:{}",JSON.toJSONString(productVo));
            if (productVo.isEmpty()) {
                return ResponseVo.successResultNotData();
            }
            return ResponseVo.successResult(JSON.toJSONString(productVo, SerializerFeature.WriteMapNullValue));
        }catch (Exception s){
            log.error("ToolController.getProductByStandardId error",s);
            return ResponseVo.errRest(s.getMessage());
        }
    }

    @RequestMapping("/editTaskSql")
    @ResponseBody
    public ResponseVo editTaskSql(String orgId, Integer taskId, String taskInfo) {
        try {
            log.info("ToolController.editTaskSql#orgId:{},taskId:{},taskInfo:{}", orgId, taskId, taskInfo);
            if (StringUtils.isBlank(orgId) || taskId == null || StringUtils.isBlank(taskInfo)) {
                return ResponseVo.errRest("orgId,taskId,taskInfo不能为空");
            }
            toolRemote.editTaskSql(orgId, taskId, taskInfo);
            return ResponseVo.successResultNotData();
        } catch (ServiceRuntimeException e) {
            log.error("ToolController.editTaskSql#error.orgId:{},taskId:{},taskInfo:{}", orgId, taskId, taskInfo, e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception s) {
            log.error("ToolController.editTaskSql#error.orgId:{},taskId:{},taskInfo:{}", orgId, taskId, taskInfo, s);
            return ResponseVo.errRest("修改任务sql报错");
        }
    }

    /**
     * 删除指定机构下商品
     * @return
     */
    @GetMapping(value = "/product/deleteSku")
    public String deleteSku() {
        return "product/deleteSku";
    }

    /**
     * @param orgId
     * @return
     */
    @PostMapping("/product/deleteSkuByOrgId")
    @ResponseBody
    public ResponseVo deleteSkuByOrgId(String orgId) {
        try {
            String user = getUser().getUsername();
            log.info("ToolController.deleteSkuByOrgId orgId:{},user:{}", orgId, user);
            if (StringUtils.isBlank(orgId)) {
                return ResponseVo.errRest("orgId不能为空");
            }
            CorporationDto corporationDto = corporationRemoteAdapter.queryByOrgId(orgId);
            if (corporationDto == null || StringUtils.isEmpty(corporationDto.getShopCode())) {
                return ResponseVo.errRest("机构不存在");
            }
            validAuthority(deleteSkuUser, "删除机构所有商品");
            SkuAdminPageQuery query = new SkuAdminPageQuery();
            query.setOrgIds(Arrays.asList(orgId));
            query.setPageSize(deleteSkuSteps);
            query.setPageNum(1);
            query.setEffectiveSku(true);
            List<Integer> beforeStatus = Arrays.asList(PopSkuStatus.SALE.getValue(), PopSkuStatus.UN_SHELF.getValue(), PopSkuStatus.STAY_ON_SHELF.getValue(),
                    PopSkuStatus.TO_AUDITED.getValue(), PopSkuStatus.AUDIT_FAILED.getValue());
            do {
                //只查有效数据，随着第一页面数据删除，第二页数据回成为第一页数据
                PageInfo<PopSkuDetailDto> pages = skuRemoteAdapter.adminQuery(query);
                if (CollectionUtils.isEmpty(pages.getList())) {
                    break;
                }
                //获取barcode前加一层过滤，避免底层sql修改造成的bug而删除其他商业商品
                List<String> barcodes = pages.getList().stream().filter(item->orgId.equals(item.getPopSku().getOrgId())&&item.getPopSku().getStatus()!=PopSkuStatus.DELETE.getValue()).map(item -> item.getPopSku().getBarcode()).collect(Collectors.toList());
                List<Long> csuIds = pages.getList().stream().filter(item->orgId.equals(item.getPopSku().getOrgId())&&item.getPopSku().getStatus()!=PopSkuStatus.DELETE.getValue()).map(item -> item.getPopSku().getCsuid()).collect(Collectors.toList());
                if(csuIds.size()!=pages.getList().size()){//发生了数据过滤，说明底层sql有问题
                    break;
                }
                deleteSkuByBarcodes(user, barcodes, csuIds, corporationDto, beforeStatus);
            } while (true);
            log.info("ToolController.deleteSkuByOrgId#orgId:{} return true", orgId);
            return ResponseVo.successResultNotData();
        } catch (ServiceException|ServiceRuntimeException e) {
            log.error("ToolController.deleteSkuByOrgId#orgId:{} 异常", orgId, e.getMessage());
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("ToolController.deleteSkuByOrgId#orgId:{} 异常", orgId, e);
            return ResponseVo.errRest("删除失败");
        }

    }

    /**
     * @param orgId
     * @return
     */
    @PostMapping("/product/deleteSkuByBarcodes")
    @ResponseBody
    public ResponseVo deleteSkuByBarcodes(String orgId,String barcodeStr) {
        try {
            String user = getUser().getUsername();
            log.info("ToolController.deleteSkuByBarcodes orgId:{},user:{}", orgId, user);
            if (StringUtils.isBlank(orgId)||StringUtils.isBlank(barcodeStr)) {
                return ResponseVo.errRest("参数不能为空");
            }
            List<String> barcodes = Arrays.asList(barcodeStr.split(","));
            if(barcodes.size()>50){
                return ResponseVo.errRest("一次最多删除50个商品");
            }
            CorporationDto corporationDto = corporationRemoteAdapter.queryByOrgId(orgId);
            if (corporationDto == null || StringUtils.isEmpty(corporationDto.getShopCode())) {
                return ResponseVo.errRest("机构不存在");
            }
            validAuthority(deleteSkuUser, "删除机构指定商品");
            List<Integer> beforeStatus = Arrays.asList(PopSkuStatus.SALE.getValue(), PopSkuStatus.UN_SHELF.getValue(), PopSkuStatus.STAY_ON_SHELF.getValue(),
                    PopSkuStatus.TO_AUDITED.getValue(), PopSkuStatus.AUDIT_FAILED.getValue());
            List<PopSkuDto> skus = skuRemoteAdapter.findSkuByBarCodes(barcodes);
            skus = skus.stream().filter(item->orgId.equals(item.getOrgId())&&item.getStatus()!=PopSkuStatus.DELETE.getValue()&&item.getActivityType()== ActivityTypeEnum.COMMON.getCode()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(skus)){
                log.info("ToolController.deleteSkuByBarcodes orgId:{},barcodeStr:{},user:{} 没有可删除商品", orgId,barcodeStr, user);
                return ResponseVo.successResultNotData();
            }
            List<Long> csuIds = skus.stream().map(item -> item.getCsuid()).collect(Collectors.toList());
            deleteSkuByBarcodes(user, skus.stream().map(item -> item.getBarcode()).collect(Collectors.toList()), csuIds, corporationDto, beforeStatus);
            log.info("ToolController.deleteSkuByOrgId#orgId:{} return true", orgId);
            return ResponseVo.successResultNotData();
        } catch (ServiceException|ServiceRuntimeException e) {
            log.error("ToolController.deleteSkuByOrgId#orgId:{} 异常", orgId, e.getMessage());
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("ToolController.deleteSkuByOrgId#orgId:{} 异常", orgId, e);
            return ResponseVo.errRest("删除失败");
        }

    }

    @Deprecated
    private void deleteSkuByBarcodes(String user, List<String> barcodes, List<Long> csuIds, CorporationDto corporationDto, List<Integer> beforeStatus) {
        boolean result = ecSkuRemoteAdapter.batchUpdateSkuStatusBySkuIdList(user, csuIds, PopSkuStatus.DELETE.getValue(), beforeStatus);
        if(!result){
            throw new ServiceRuntimeException("删除ec商品失败");
        }
        result = toolService.deleteLogicByBarcodes(barcodes,user);
        if(!result){
            throw new ServiceRuntimeException("删除pop商品失败");
        }
    }

    @PostMapping("/product/moveDeletedSku")
    @ResponseBody
    public ResponseVo moveDeletedSku() {
        try {
            log.info("ToolController.moveDeletedSku# 开始");
            toolService.moveDeletedSku();
            log.info("ToolController.moveDeletedSku# 结束 return ok"  );
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("ToolController.moveDeletedSku# 异常",e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    @PostMapping("/product/moveDeletedSkuOtherInfo")
    @ResponseBody
    public ResponseVo moveDeletedSkuOtherInfo(String orgId) {
        try {
            log.info("ToolController.moveDeletedSku# 开始");
            toolService.moveDeletedSkuOtherInfo(orgId);
            log.info("ToolController.moveDeletedSku# 结束 return ok"  );
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("ToolController.moveDeletedSku# 异常",e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    @PostMapping("/product/downNoStockSku")
    @ResponseBody
    public ResponseVo downNoStockSku(String orgId) {
        try {
            log.info("ToolController.downNoStockSku# 开始 org:{}",orgId);
            toolService.downNoStockSku(orgId);
            log.info("ToolController.downNoStockSku# 结束 return ok"  );
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("ToolController.downNoStockSku# 异常",e);
            return ResponseVo.errRest(e.getMessage());
        }
    }
    /**
     * 历史数据控销客户类型
     * @return
     */
    @PostMapping("/product/setControlUserType")
    @ResponseBody
    public ResponseVo setControlUserType(String orgIds) {
        try {
            log.info("ToolController.setControlUserType# 开始");
            List<String> orgs = StringUtils.isEmpty(orgIds)?null:Arrays.asList(orgIds.split(","));
            toolService.setControlUserType(orgs);
            log.info("ToolController.setControlUserType# 结束 return ok"  );
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("ToolController.setControlUserType# 异常",e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    @PostMapping("/product/monitorControlUserType")
    @ResponseBody
    public ResponseVo monitorControlUserType(String orgId) {
        try {
            log.info("ToolController.monitorControlUserType# orgId:{} 开始",orgId);
            ApiRPCResult<Boolean> result = productBaseInfoMonitorApi.monitorSkuControl(orgId);
            log.info("ToolController.monitorControlUserType# orgId:{} 结束 return {}",orgId,JSON.toJSONString(result)  );
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("ToolController.monitorControlUserType# 异常",e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    @RequestMapping("/billSettleFlushDataPage")
    public String billSettleFlushDataPage() {
        return "tool/billSettleFlushData";
    }

    @RequestMapping("/exeBillSettleData")
    @ResponseBody
    public ResponseVo exeBillSettleData() {
        try {
            log.info("ToolController.exeBillSettleData");
            toolRemote.exeBillSettleData();
            return ResponseVo.successResultNotData();
        } catch (ServiceRuntimeException e) {
            log.error("ToolController.exeBillSettleData#error.", e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception s) {
            log.error("ToolController.exeBillSettleData#error.", s);
            return ResponseVo.errRest("刷结算表数据出现异常");
        }
    }

    @RequestMapping("/addPopSkuSyncPage")
    public String addPopSkuSyncPage() {
        return "tool/addPopSkuSync";
    }

    @PostMapping("/addPopSkuSync")
    @ResponseBody
    public ResponseVo addPopSkuSync(String barcode,Integer jobType) {
        try {
            if (StringUtils.isEmpty(barcode) || Objects.isNull(jobType)){
                return ResponseVo.errRest("入参不能为空");
            }
            String user = getUser().getUsername();
            toolRemote.addPopSkuSync(barcode,jobType,user);
            return ResponseVo.successResultNotData();
        } catch (ServiceRuntimeException e) {
            log.error("ToolController.addPopSkuSync#error. barcode:{} ,jobType:{}",barcode,jobType, e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception s) {
            log.error("ToolController.addPopSkuSync#error. barcode:{} ,jobType:{}",barcode,jobType, s);
            return ResponseVo.errRest("新增同步sku表数据异常");
        }
    }


    @PostMapping("/addPopMerchantMenu")
    @ResponseBody
    public ResponseVo addPopMerchantMenu(@RequestBody PopSupplierMenuDto popSupplierMenuDto) {
        try {
            if (Objects.isNull(popSupplierMenuDto)){
                return ResponseVo.errRest("入参不能为空");
            }
            String user = getUser().getUsername();
            popSupplierMenuDto.setOperateBy(user);
            toolRemote.addPopMerchantMenu(popSupplierMenuDto);
            return ResponseVo.successResultNotData();
        } catch (ServiceRuntimeException e) {
            log.error("ToolController.addPopMerchantMenu#error. popSupplierMenuDto:{}", JSONObject.toJSONString(popSupplierMenuDto), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception s) {
            log.error("ToolController.addPopMerchantMenu#error. popSupplierMenuDto:{}",JSONObject.toJSONString(popSupplierMenuDto), s);
            return ResponseVo.errRest("新增卖家中心菜单异常");
        }
    }

    @GetMapping(value = "/rePush")
    public String rePush() {
        return "tool/rePush";
    }

    /**
     * @param orderNos
     * @return
     */
    @PostMapping("/rePushOrders")
    @ResponseBody
    public ResultVO rePushOrders(@RequestParam("orderNos") List<String> orderNos) {
        log.info("ToolController.rePushOrders orderNos:{}", JSON.toJSONString(orderNos));
        if (CollectionUtils.isEmpty(orderNos)) {
            return ResultVO.createError("订单号不能为空");
        }
        try {
            boolean resutl = toolService.updateOrderSynStatus(orderNos);
            if (resutl) {
                return ResultVO.createSuccess();
            } else {
                return ResultVO.createError("订单重新下发状态刷新失败");
            }
        } catch (Exception e) {
            log.info("ToolController.rePushOrders orderNos:{}", JSON.toJSONString(orderNos), e);
            return ResultVO.createError("订单重新下发状态刷新失败");
        }
    }

    /**
     * @param orgId
     * @return
     */
    @PostMapping("/rePushCustomers")
    @ResponseBody
    public ResultVO rePushCustomers(String orgId,@RequestParam("customerNames") List<String> customerNames) {
        log.info("ToolController.rePushCustomers orgId:{},customerNames:{}", orgId, JSON.toJSONString(customerNames));
        if (StringUtils.isBlank(orgId) || CollectionUtils.isEmpty(customerNames)) {
            return ResultVO.createError("orgId或者customerNames不能为空");
        }
        try {
            boolean resutl = toolService.updateCustomerSynStatus(orgId, customerNames);
            if (resutl) {
                return ResultVO.createSuccess();
            } else {
                return ResultVO.createError("客户重新下发状态刷新失败");
            }
        } catch (Exception e) {
            log.error("ToolController.rePushCustomers orgId:{},customerNames:{}", orgId,  JSON.toJSONString(customerNames), e);
            return ResultVO.createError("客户重新下发状态失败");
        }
    }


    @GetMapping(value = "/erpClient")
    public String erpClientInfo() {
        return "tool/erpClient";
    }
    /**
     * @param orgId
     * @return
     */
    @PostMapping("/delErpClientInfoByOrgId")
    @ResponseBody
    public ResultVO delErpClientInfoByOrgId(String orgId) {
        log.info("ToolController.delErpClientInfoByOrgId orgId:{}", orgId);
        if (StringUtils.isBlank(orgId) ) {
            return ResultVO.createError("orgId不能为空");
        }
        try {
            boolean resutl = toolService.delErpClientInfoByOrgId(orgId);
            if (resutl) {
                return ResultVO.createSuccess();
            } else {
                return ResultVO.createError("删除客户失败");
            }
        } catch (Exception e) {
            log.error("ToolController.delErpClientInfoByOrgId orgId:{}", orgId, e);
            return ResultVO.createError("删除客户失败");
        }
    }

    @GetMapping(value = "/autoApplyInvoicePage")
    public String autoApplyInvoicePage() {
        return "tool/autoApplyInvoice";
    }

    /**
     * @return
     */
    @PostMapping("/autoApplyInvoice")
    @ResponseBody
    public ResultVO autoApplyInvoice() {
        log.info("ToolController.autoApplyInvoice");
        try {
            boolean resutl = toolService.autoApplyInvoice(autoApplyInvoiceDate);
            if (resutl) {
                return ResultVO.createSuccess();
            } else {
                return ResultVO.createError("自动佣金开票失败");
            }
        } catch (PopAdminException e) {
            log.error("ToolController.autoApplyInvoice#自定义异常", e);
            return ResultVO.createError(e.getMessage());
        } catch (Exception e) {
            log.error("ToolController.autoApplyInvoice#未知异常", e);
            return ResultVO.createError("自动发起佣金开票异常");
        }
    }

    @Reference
    private FixProductUtilApi fixProductUtilApi;
    @RequestMapping("/refreshSkuStock")
    @ResponseBody
    public ResultVO refreshSkuStock(String skuIds){
        log.info("ToolController.refreshSkuStock param:{}",skuIds);
        if (StringUtils.isBlank(skuIds)){
            return ResultVO.createError("参数不能为空");
        }
        try {
            List<Long> skuIdList = Arrays.stream(skuIds.split(",")).map(Long::parseLong).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(skuIdList)){
                return ResultVO.createError("商品id不能为空");
            }
            ApiRPCResult apiRPCResult = fixProductUtilApi.fixProductStocks(skuIdList);
            return ResultVO.createSuccess(apiRPCResult);
        }catch (Exception e){
            log.error("ToolController.refreshSkuStock error",e);
            return ResultVO.createError("请求失败");
        }
    }

    @RequestMapping("/downPopSkuPage")
    public String downPopSkuPage() {
        return "tool/downPopSkuPage";
    }

    @RequestMapping("/fddSignUtil")
    public String fddSignUtil() {
        return "fdd/fddSignUtil";
    }

    @RequestMapping("/doDownPopSku")
    @ResponseBody
    public ResultVO doDownPopSku(@RequestParam("downType") Integer downType,@RequestParam("paramStr") String paramStr){
        if (downType == null){
            return ResultVO.createError("缺少参数");
        }
        if (downType != 3 && StringUtils.isBlank(paramStr)){
            return ResultVO.createError("缺少参数");
        }
        try {
            if (downType ==  1){
                ApiRPCResult apiRPCResult = fixProductUtilApi.downPopSkuByOrgId(paramStr);
                return ResultVO.createSuccess(apiRPCResult);
            }
            else if (downType ==  2){
                List<String> collect = Arrays.stream(paramStr.split(",")).distinct().collect(Collectors.toList());
                AtomicReference<Boolean> success = new AtomicReference<>(true);
                Lists.partition(collect,50).forEach(items->{
                    ApiRPCResult apiRPCResult = fixProductUtilApi.downPopSkuByBarcodeList(items);
                    if (apiRPCResult == null || apiRPCResult.isFail()){
                        success.set(false);
                    }
                    log.info("ToolController.doDownPopSku#downPopSkuByBarcodeList:{}",JSON.toJSONString(apiRPCResult));
                });
                return success.get() ? ResultVO.createSuccess() : ResultVO.createError("下架失败");
            }else {
                ApiRPCResult apiRPCResult = fixProductUtilApi.downPopSkuAll();
                return ResultVO.createSuccess(apiRPCResult);
            }
        }catch (Exception e){
            log.error("ToolController.doDownPopSku error",e);
            return ResultVO.createError("下架异常");
        }
    }

    @Autowired
    private PopSkuTaskRemote popSkuTaskRemote;

    @RequestMapping("/sku/delete")
    @ResponseBody
    public ResultVO deleteSku(String barcodes){
        if (StringUtils.isBlank(barcodes)){
            return ResultVO.createError("参数不能为空");
        }
        List<String> barcodeList = Arrays.stream(barcodes.split(",")).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(barcodeList)){
            return ResultVO.createError("参数不能为空");
        }
        return ResultVO.createSuccess(popSkuTaskRemote.deletePopSkus(barcodeList));
    }

    @RequestMapping("/sku/sync/task")
    @ResponseBody
    public ResultVO syncSkuTask(String barcodes){
        if (StringUtils.isBlank(barcodes)){
            return ResultVO.createError("参数不能为空");
        }
        List<String> barcodeList = Arrays.stream(barcodes.split(",")).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(barcodeList)){
            return ResultVO.createError("参数不能为空");
        }
        return ResultVO.createSuccess(popSkuTaskRemote.saveProductSyncTask(barcodeList));
    }

    @Reference(version = "1.0.0")
    private ProductToolApi productToolApi;

    @RequestMapping("/sku/down")
    @ResponseBody
    public ResultVO downSku(String csuIdStr){
        if (StringUtils.isBlank(csuIdStr)){
            return ResultVO.createError("参数不能为空");
        }
        log.info("ToolController.downSku param:{}:user:{}",csuIdStr,getUserName());
        List<Long> csuIdList = Arrays.stream(csuIdStr.split(",")).distinct().filter(StringUtils::isNotBlank).
                map(Long::parseLong).collect(Collectors.toList());
        ApiRPCResult<String> apiRPCResult = productToolApi.refreshProductStatus(csuIdList, getUserName(), PopSkuStatus.UN_SHELF.getValue());
        return ResultVO.createSuccess(apiRPCResult);

    }

    /**
     *  工具-商业中包装刷数页面
      * @return
     */
    @RequestMapping("/toUpdateSkuMediumPackagePage")
    public String updateSkuMediumPackagePage() {
        return "product/updateSkuMediumPackagePage";
    }

    @PostMapping("/updateSkuMediumPackage")
    @ResponseBody
    public ResultVO updateSkuMediumPackage(@RequestParam("updateType") Integer updateType,@RequestParam("orgId") String orgId,
                                           @RequestParam("barcodeStr")  String barCodes) {
        log.info("updateSkuMediumPackage updateType:{},orgId:{} barCodes:{}",updateType,orgId,barCodes);

        if (updateType == null){
            return ResultVO.createError("缺少参数");
        }
        if(updateType == 0 ){
            if(StringUtils.isBlank(orgId)){
                return ResultVO.createError("机构编码不能为空");
            }
        } else if (updateType == 1 ){
            if(StringUtils.isAnyBlank(orgId,barCodes)){
                return ResultVO.createError("机构编码，商品编码不能为空");
            }
        }
        ApiRPCResult result = ApiRPCResult.buildSuccess(true);
        //根据机构编码更新
        if(updateType == 0 ){
            log.info("ToolServiceImpl.updateSkuMediumPackage#orgId:{},barCodes:{}", orgId,barCodes);
            result = popSkuAdminApi.updateSkuMediumPackage(orgId, null);
        }else if (updateType == 1 ){
            result = popSkuAdminApi.updateSkuMediumPackage(orgId, barCodes);
        }

        return result.isSuccess() ? ResultVO.createSuccess(true) : ResultVO.createError(result.getErrMsg());

    }

}
