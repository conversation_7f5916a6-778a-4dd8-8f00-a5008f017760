package com.xyy.ec.pop.controller;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.remote.SkuSaleTimeRemote;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Description 商品销售时间
 */
@Controller
@RequestMapping("/saleTime")
@Slf4j
public class SaleTimeController extends BaseController {
    @Autowired
    private SkuSaleTimeRemote skuSaleTimeRemote;

    @GetMapping(value = "/index")
    public String index() {
        return "saleTime/index";
    }

    @PostMapping(value = "/reSetSkuTimeByConfig")
    @ResponseBody
    public ResponseVo reSetSkuTimeByConfig(Long id) {
        try {
            log.info("SaleTimeController.reSetSkuTimeByConfig#id:{}", id);
            if (id == null) {
                return ResponseVo.errRest("请输入配置id");
            }
            Boolean result = skuSaleTimeRemote.reSetSkuTimeByConfig(id, getUser().getRealName());
            log.info("SaleTimeController.reSetSkuTimeByConfig#id:{} return {}", id, result);
            return ResponseVo.successResult(result);
        }catch (XyyEcPopException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("SaleTimeController.reSetSkuTimeByConfig#id:{} 异常", id, e);
        }
        return ResponseVo.errRest("同步失败");
    }

    @PostMapping(value = "/reSetSkuTime")
    @ResponseBody
    public ResponseVo reSetSkuTime(@RequestBody Map<String, List<String>> map) {
        try {
            log.info("SaleTimeController.reSetSkuTime#map:{}", JSON.toJSONString(map));
            List<String> barcodes = map.get("barcode");
            if(CollectionUtils.isEmpty(barcodes)){
                return ResponseVo.errRest("请输入商品编码");
            }
            Boolean result = skuSaleTimeRemote.reSetSkuTime(barcodes);
            log.info("SaleTimeController.reSetSkuTime#map:{} return {}", JSON.toJSONString(map), JSON.toJSONString(result));
            return ResponseVo.successResult(result);
        }catch (XyyEcPopException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("SaleTimeController.reSetSkuTime#map:{} 异常", JSON.toJSONString(map), e);
        }
        return ResponseVo.errRest("同步失败");
    }
}
