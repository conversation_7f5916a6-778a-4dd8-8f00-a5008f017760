package com.xyy.ec.pop.vip.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.crm.operation.api.enums.BusinessTypeEnum;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.OrderNumMonthlyDto;
import com.xyy.ec.merchant.bussiness.dto.OrderNumMonthlyQueryDto;
import com.xyy.ec.merchant.bussiness.dto.vip.VipActivateUserQueryDto;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.redis.impl.RedisService;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.remote.MerchantRpcService;
import com.xyy.ec.pop.remote.impl.VipActiveUsersRemoteImpl;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.merchant.param.VipActiveUsersQueryParam;
import com.xyy.ec.pop.vip.service.VipActiveUsersService;
import com.xyy.ec.pop.vip.vo.VipActiveUserVo;
import com.xyy.ec.pop.vip.vo.VipActiveUsersQueryVo;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VipActiveUsersServiceImpl implements VipActiveUsersService {
    @Resource
    private RedisService redisService;
    @Autowired
    private DownloadRemote downloadRemote;
    @Resource
    private MerchantRpcService merchantRpcService;
    @Resource
    private VipActiveUsersRemoteImpl vipActiveUsersRemote;

    @Override
    public ResponseVo<Page<VipActiveUserVo>> pageVipActiveUsers(VipActiveUsersQueryVo vipActiveUsersQuery) {
        VipActivateUserQueryDto vipActivateUserQueryDto = new VipActivateUserQueryDto();
        if (NumberUtil.isNumber(vipActiveUsersQuery.getMerchantName())) {
            vipActivateUserQueryDto.setMerchantId(Long.valueOf(vipActiveUsersQuery.getMerchantName()));
        } else {
            vipActivateUserQueryDto.setMerchantName(vipActiveUsersQuery.getMerchantName());
        }
        vipActivateUserQueryDto.setPageNum(vipActiveUsersQuery.getPage());
        vipActivateUserQueryDto.setPageSize(vipActiveUsersQuery.getLimit());
        PageInfo<OrderNumMonthlyDto> pageInfo = vipActiveUsersRemote.pageMerchantReportDetail(vipActivateUserQueryDto);
        if (pageInfo != null) {
            Page<VipActiveUserVo> page = new Page<>(vipActiveUsersQuery.getPage(), vipActiveUsersQuery.getLimit());
            page.setTotal(pageInfo.getTotal());
            page.setRows(pageInfo.getList().stream().map(item -> {
                VipActiveUserVo vipActiveUserVo = new VipActiveUserVo();
                vipActiveUserVo.setMerchantName(item.getRealName());
                vipActiveUserVo.setMerchantType(item.getCustomerTypeStr());
                vipActiveUserVo.setProvinceName(item.getProvince());
                vipActiveUserVo.setAddress(item.getAddress());
                vipActiveUserVo.setMerchantId(item.getMerchantId());
                return vipActiveUserVo;
            }).collect(Collectors.toList()));

            return ResponseVo.successResult(page);
        }
        return ResponseVo.successResult(new Page<>(vipActiveUsersQuery.getPage(), vipActiveUsersQuery.getLimit()));
    }

    @Override
    public List<VipActiveUserVo> getVipActiveUsers(VipActiveUsersQueryVo vipActiveUsersQuery) {
        return Collections.emptyList();
    }

    @Override
    public ResponseVo<Boolean> exportActiveUsers(VipActiveUsersQueryVo vipActiveUsersQuery, SysUser user) {
        String redisCacheKey = "MPEAU_" + user.getId();
        boolean isHaveLock = acquireLock(redisCacheKey);
        if (!isHaveLock) {
            log.info("举报记录导出，分布式锁，标记={},isHaveLock={}", redisCacheKey, isHaveLock);
            throw new PopAdminException(XyyJsonResultCodeEnum.EXPORT_FREQUENCY_LIMIT, "您目前有正在导出的任务，请等待其完成后再进行导出！");
        }

        try {
            processExport(vipActiveUsersQuery, user);
        } finally {
            releaseLock(redisCacheKey);
        }

        return ResponseVo.successResult(true);
    }

    private boolean acquireLock(String redisCacheKey) {
        // 最多锁60秒
        return redisService.setNx(redisCacheKey, StringUtils.EMPTY, 60);
    }

    private void releaseLock(String redisCacheKey) {
        if (StringUtils.isNotEmpty(redisCacheKey)) {
            try {
                redisService.deleteKey(redisCacheKey);
            } catch (Exception e) {
                log.error("释放导出会员用户频次锁失败，redisCacheKey：{}", redisCacheKey, e);
            }
        }
    }

    private void processExport(VipActiveUsersQueryVo vipActiveUsersQuery, SysUser user) {
        VipActiveUsersQueryParam exportQueryParam = new VipActiveUsersQueryParam();
        if (StringUtils.isNotEmpty(vipActiveUsersQuery.getMerchantName())) {
            if (NumberUtil.isNumber(vipActiveUsersQuery.getMerchantName())) {
                exportQueryParam.setMerchantId(Long.valueOf(vipActiveUsersQuery.getMerchantName()));
            } else {
                exportQueryParam.setMerchantName(vipActiveUsersQuery.getMerchantName());
            }
        }
        DownloadFileContent content = DownloadFileContent.builder()
                .query(exportQueryParam)
                .operator(user.getEmail())
                .sysType(DownloadTaskSysTypeEnum.ADMIN)
                .businessType(DownloadTaskBusinessTypeEnum.VIP_ACTIVE_USER_LIST)
                .build();

        Boolean asyncResult = downloadRemote.saveTask(content);
        if (BooleanUtils.isNotTrue(asyncResult)) {
            log.error("导出失败，DownloadRemote.saveTask，content：{}", JSONObject.toJSONString(content));
            throw new PopAdminException(XyyJsonResultCodeEnum.FAIL, "导出失败，请稍后重试！");
        }
    }
}
