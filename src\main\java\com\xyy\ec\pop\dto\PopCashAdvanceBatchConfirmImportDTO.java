package com.xyy.ec.pop.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 商户提现批量确认导入DTO
 *
 * <AUTHOR>
 */
@Data
public class PopCashAdvanceBatchConfirmImportDTO implements IExcelModel, Serializable {

    private static final long serialVersionUID = -199881368025351256L;

    @Excel(name = "提现单号")
    private String cashAdvanceNum;

    private String errorMsg;

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }

    @Override
    public void setErrorMsg(String s) {
        this.errorMsg = s;
    }

}
