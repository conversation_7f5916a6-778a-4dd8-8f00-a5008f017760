package com.xyy.ec.pop.dao;

import com.xyy.ec.pop.po.PopBillPaymentDetailPo;
import com.xyy.ec.pop.po.PopBillPaymentPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface PopBillPaymentDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PopBillPaymentDetailPo record);

    int insertSelective(PopBillPaymentDetailPo record);

    PopBillPaymentDetailPo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PopBillPaymentDetailPo record);

    int updateByPrimaryKey(PopBillPaymentDetailPo record);

    List<PopBillPaymentDetailPo> queryPopBillPaymentDetail(@Param("popBillPayDetail") PopBillPaymentDetailPo popBillPaymentDetailPo, @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    Long queryPopBillPaymentDetailCount(@Param("list") List<String> flowNoList);

    List<PopBillPaymentDetailPo> queryPopBillPaymentDetailByFlowNoList(@Param("list") List<String> flowNoList);
}