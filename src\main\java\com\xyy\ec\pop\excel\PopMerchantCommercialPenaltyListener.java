package com.xyy.ec.pop.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.xyy.ec.pop.utils.StringUtil;
import com.xyy.ec.pop.vo.PopMerchantCommercialPenaltyVo;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 模板的读取类
 * 有个很重要的点 PopMerchantCommercialPenaltyListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
 * <AUTHOR>
 * @date 2024/9/2 17:20
 */
@Getter
public class PopMerchantCommercialPenaltyListener implements ReadListener<PopMerchantCommercialPenaltyVo> {
    List<PopMerchantCommercialPenaltyVo> vos=new ArrayList<>();

    @Override
    public void invoke(PopMerchantCommercialPenaltyVo vo, AnalysisContext analysisContext) {
        if (StringUtil.isBlank(vo.getPenaltyReason()) &&
                StringUtil.isBlank(vo.getDescription()) &&
                StringUtil.isBlank(vo.getOrderNo()) &&
                Objects.isNull(vo.getActualReceivedAmount()) &&
                StringUtil.isBlank(vo.getIsCompensateCustomer())
        ) {
            return;
        }
        vos.add(vo);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

}
