package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: Jincheng.Li
 * @Date: 2021/07/14/15:50
 * @Description:
 */
public enum PopAnnounceStatusEnum {

    ANNOUNCE_START(1,"启用"),
    ANNOUNCE_STOP(0,"停用");

    private Integer code;
    private String name;

    PopAnnounceStatusEnum(Integer code,String name){
        this.code = code;
        this.name = name;
    }

    private static Map<Integer, PopAnnounceStatusEnum> controlMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(PopAnnounceStatusEnum apEnum : PopAnnounceStatusEnum.values()) {
            controlMaps.put(apEnum.getCode(), apEnum);
            maps.put(apEnum.getCode(),apEnum.getName());
        }
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }
    public static String get(int id) {
        return controlMaps.get(id).getName();
    }

}
