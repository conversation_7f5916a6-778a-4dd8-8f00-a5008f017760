package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.backend.order.api.OrderApi;
import com.xyy.ec.order.backend.order.query.dto.OrderDto;
import com.xyy.ec.order.search.api.remote.enums.PopOrderStatusEnum;
import com.xyy.ec.order.search.api.remote.order.OrderSearchApi;
import com.xyy.ec.pop.dto.PopCorporationDto;
import com.xyy.ec.pop.enums.OptEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.remote.EcSkuRemoteAdapter;
import com.xyy.ec.pop.remote.MerchantCustomerTypeRemote;
import com.xyy.ec.pop.remote.ToolRemoteAdapter;
import com.xyy.ec.pop.server.api.erp.api.PopErpTaskApi;
import com.xyy.ec.pop.server.api.erp.dto.PopCorpClientManagerDto;
import com.xyy.ec.pop.server.api.erp.dto.ResultDto;
import com.xyy.ec.pop.server.api.erp.enums.TaskEnum;
import com.xyy.ec.pop.server.api.erp.param.TaskQueryParam;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.PopMerchantTotalFundAccountApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.merchant.param.MerchantAccountDeductionParam;
import com.xyy.ec.pop.server.api.order.api.PopOrderApi;
import com.xyy.ec.pop.server.api.order.dto.PopOrderDto;
import com.xyy.ec.pop.server.api.orgUserRelation.api.OrgUserRelationAfterApi;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuAdminApi;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.server.api.product.enums.PopSkuOperationLogType;
import com.xyy.ec.pop.server.api.product.enums.PopSkuStatus;
import com.xyy.ec.pop.server.api.seller.api.PopCommissionSettleApi;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleDto;
import com.xyy.ec.pop.server.api.tool.api.ToolApi;
import com.xyy.ec.pop.server.api.tool.dto.CorporationInfoDto;
import com.xyy.ec.pop.service.ToolService;
import com.xyy.ec.pop.utils.MD5;
import com.xyy.ec.pop.vo.DeductFundToolVo;
import com.xyy.ec.pop.vo.OfflinePayWhitelistVo;
import com.xyy.ec.shop.server.business.enums.ShopPatternEnum;
import com.xyy.me.product.general.api.dto.pictrue.PictrueInfoVo;
import com.xyy.me.product.general.api.dto.pictrue.PictureProResult;
import com.xyy.me.product.general.api.dto.product.GeneralProductDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ToolServiceImpl implements ToolService {
    @Reference
    private PopSkuAdminApi popSkuAdminApi;
    @Reference
    private PopErpTaskApi popErpTaskApi;
    @Reference(version = "1.0.0")
    private PopMerchantTotalFundAccountApi popMerchantTotalFundAccountApi;
    @Reference(version = "1.0.0")
    private PopCommissionSettleApi popCommissionSettleApi;
    @Reference
    private PopOrderApi popOrderApi;

    @Reference
    private OrderApi orderApi;

    @Reference
    private ToolApi toolApi;

    @Reference
    private OrgUserRelationAfterApi userRelationAfterApi;

    @Reference(version = "1.0.0")
    private OrderSearchApi orderSearchApi;

    @Autowired
    private ToolRemoteAdapter toolRemoteAdapter;
    @Autowired
    private MerchantCustomerTypeRemote merchantCustomerTypeRemote;
    @Autowired
    private EcSkuRemoteAdapter ecSkuRemoteAdapter;
    @Reference
    private CorporationAdminApi corporationAdminApi;
    /**
     * 商品移除到删除表，的批次间隔
     */
    @Value("${tool.product.moveToDelete.sleepTime:100}")
    private Long sleepTime;
    @Value("${tool.product.moveToDelete.batch:100}")
    private Integer batch;

    @Value("${offline.list.limit:400}")
    private Integer offlineListLimit;
    @Override
    public boolean deleteErpSkusByOrgId(String orgId,Integer deleteType) {
        log.info("ToolServiceImpl.deleteErpSkusByOrgId#orgId:{}", orgId);
        ApiRPCResult apiRPCResult = popSkuAdminApi.deleteErpSkusByOrgId(orgId,deleteType);
        log.info("ToolServiceImpl.deleteErpSkusByOrgId#orgId:{},apiRPCResult:{}", orgId, JSON.toJSONString(apiRPCResult));
        if (apiRPCResult == null || apiRPCResult.isFail()) {
            return false;
        }
        return true;
    }

    @Override
    public String listPushOrderByOrgId(String orgId) {
        ApiRPCResult<PopCorpClientManagerDto> corpManagerInfo = popErpTaskApi.getCorpManagerInfo(orgId);
        log.info("ToolServiceImpl.listPushOrderByOrgId#orgId:{},corpManagerInfo:{}", orgId, JSON.toJSONString(corpManagerInfo));
        if (corpManagerInfo == null || corpManagerInfo.isFail()) {
            return StringUtils.EMPTY;
        }
        PopCorpClientManagerDto popCorpClientManagerDto = corpManagerInfo.getData();
        log.info("##popCorpClientMaDto:{}", popCorpClientManagerDto);
        if (popCorpClientManagerDto == null) {
            return StringUtils.EMPTY;
        }
        String appId = popCorpClientManagerDto.getAppId();
        String secret = popCorpClientManagerDto.getAppSecret();
        TaskQueryParam taskQueryParam = new TaskQueryParam();
        taskQueryParam.setOrgId(orgId);
        taskQueryParam.setAppId(appId);
        taskQueryParam.setTaskId(TaskEnum.ORDER_TOTAL.getTaskId());
        Map<String, Object> innerMap = JSON.parseObject(JSON.toJSONString(taskQueryParam)).getInnerMap();
        taskQueryParam.setSign(MD5.encrypt(innerMap, secret));
        ApiRPCResult<ResultDto> apiRPCResult = popErpTaskApi.pullDataInfo(taskQueryParam);
        log.info("ToolServiceImpl.listPushOrderByOrgId#orgId:{},apiRPCResult:{}", orgId, JSON.toJSONString(apiRPCResult));
        if (apiRPCResult == null || apiRPCResult.isFail()) {
            log.warn("ToolServiceImpl.listPushOrderByOrgId#查询订单推送列表失败 orgId:{},apiRPCResult:{}", orgId, JSON.toJSONString(apiRPCResult));
            return StringUtils.EMPTY;
        }
        if (apiRPCResult.getData() == null) {
            log.info("back null");
            return null;
        }
        ResultDto data = apiRPCResult.getData();
        log.info("###ResultDto:{}", data);
        return data.getDataJson();
//        List<SellerOrderInfoDto> sellerOrderInfoDtos = JSON.parseArray(dataJson, SellerOrderInfoDto.class);
//        if (CollectionUtil.isEmpty(sellerOrderInfoDtos)) {
//            return Lists.newArrayList();
//        }
//        List<String> orderNos = sellerOrderInfoDtos.stream().map(SellerOrderInfoDto::getOrderNo).collect(Collectors.toList());

    }

    @Override
    public Map<String, Object> getProductByStandardId(String standardId) throws ServiceException {
        GeneralProductDto generalProduct = toolRemoteAdapter.getGeneralProduct(standardId);
        if (generalProduct == null) {
            return Maps.newHashMap();
        }
        Map<String, Object> productVo = getProductVo(generalProduct);
        List<PictureProResult> productImg = toolRemoteAdapter.getProductImg(standardId);
        if (CollectionUtils.isEmpty(productImg)) {
            return productVo;
        }

        List<PictrueInfoVo> pictureInfoList = productImg.get(0).getPictureInfoList();
        if (CollectionUtils.isEmpty(pictureInfoList)) {
            return productVo;
        }
        Map<Integer, String> imgTypeMap = new HashMap<>();
        imgTypeMap.put(0, "主图");
        imgTypeMap.put(1, "外包装");
        imgTypeMap.put(2, "说明书");
        Map<String, String> imgMap = Maps.newLinkedHashMap();

        Map<Integer, List<PictrueInfoVo>> picMap = pictureInfoList.stream().collect(Collectors.groupingBy(PictrueInfoVo::getPictureType));
        for (Map.Entry<Integer, List<PictrueInfoVo>> entry : picMap.entrySet()) {
            String typeName = imgTypeMap.get(entry.getKey());
            if (entry.getValue().size() == 1) {
                PictrueInfoVo vo = entry.getValue().get(0);
                imgMap.put("<a href=" + vo.getPictureUrl() + " target=_blank>" + typeName + "</a>", vo.getPictureUrl());
                continue;
            }
            int no = 1;
            for (PictrueInfoVo vo : entry.getValue()) {
                imgMap.put("<a href=" + vo.getPictureUrl() + " target=_blank>" + typeName + no + "</a>", vo.getPictureUrl());
                no += 1;
            }
        }
        productVo.put("图片地址", imgMap);
        return productVo;
    }

    @Override
    public boolean deleteLogicByBarcodes(List<String> barcodes, String user) {
        try {
            log.info("ToolServiceImpl.deleteLogicByBarcodes#barcodes:{},user:{}", JSON.toJSONString(barcodes), user);
            ApiRPCResult result = popSkuAdminApi.deleteLogicByBarcodes(barcodes, user);
            log.info("ToolServiceImpl.deleteLogicByBarcodes#barcodes:{},user:{} return {}", JSON.toJSONString(barcodes), user, JSON.toJSONString(result));
            return result.isSuccess();
        } catch (Exception e) {
            log.error("ToolServiceImpl.deleteLogicByBarcodes#barcodes:{},user:{} 异常", JSON.toJSONString(barcodes), user, e);
        }
        return false;
    }

    @Override
    public void moveDeletedSku() {
        Long currentId = 0l;
        List<Long> toDelete;
        try {
            do{
                ApiRPCResult<List<Long>> idResult = popSkuAdminApi.deletedIds(currentId, batch);
                log.info("移除商品 查询currentId:{},result:{}",currentId,JSON.toJSONString(idResult));
                if (idResult.isFail()) {
                    log.error("移除商品 查询失败");
                    return;
                }
                toDelete = idResult.getData();
                if(CollectionUtils.isEmpty(toDelete)){
                    log.info("移除商品 移除完毕，没有剩余可移除的商品");
                    return;
                }
                currentId = toDelete.get(toDelete.size()-1);
                ApiRPCResult<Boolean> moveResult = popSkuAdminApi.moveToDeleted(toDelete);
                log.info("移除商品 移除toDelete:{},result:{}",JSON.toJSONString(toDelete),JSON.toJSONString(moveResult));
                if(moveResult.isFail()){
                    log.error("移除商品 移除失败");
                    return;
                }
                log.info("移除商品 移除toDelete: 当前sleepTime:{}",sleepTime);
                Thread.sleep(sleepTime);
            }while (CollectionUtils.isNotEmpty(toDelete));
        } catch (Exception e) {
            log.error("移除商品 异常", e);
        }
    }

    @Override
    public boolean updateOrderSynStatus(List<String> orderNos) {
        try {
            log.info("ToolServiceImpl.updateOrderSynStatus#orderNos:{}", JSON.toJSONString(orderNos));
            ApiRPCResult<Boolean> apiRPCResult = popOrderApi.updateOrderSyncStatus(orderNos);
            log.info("ToolServiceImpl.updateOrderSynStatus#orderNos:{},apiRPCResult:{}", JSON.toJSONString(orderNos), JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return false;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("ToolServiceImpl.updateOrderSynStatus#orderNos:{}", JSON.toJSONString(orderNos), e);
            return false;
        }
    }

    @Override
    public boolean updateCustomerSynStatus(String orgId, List<String> customerNames) {
        try {
            log.info("ToolServiceImpl.updateCustomerSynStatus#orgId:{},customerNames:{}", orgId, JSON.toJSONString(customerNames));
            ApiRPCResult<Boolean> apiRPCResult = userRelationAfterApi.updateCustomerSyncStatus(orgId, customerNames);
            log.info("ToolServiceImpl.updateCustomerSynStatus##orgId:{},customerNames:{},apiRPCResult:{}", orgId,
                    JSON.toJSONString(customerNames), JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return false;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("ToolServiceImpl.updateCustomerSynStatus#orgId:{},customerNames:{}", orgId, JSON.toJSONString(customerNames), e);
            return false;
        }
    }

    @Override
    public void setControlUserType(List<String> orgs) {
        Long currentId = 0l;
        List<PopSkuDto> todo;
        String busTypes = merchantCustomerTypeRemote.getBusinessType().stream().map(item -> item.getId()).sorted().map(item->item.toString()).collect(Collectors.joining(","));
        log.info("重置客户类型#orgs:{} busTypes{}", JSON.toJSONString(orgs),busTypes);
        try {
            log.info("重置客户类型#orgs:{}", JSON.toJSONString(orgs));
            do {
                ApiRPCResult<List<PopSkuDto>> result = popSkuAdminApi.nextSkus(orgs, currentId, batch);
                log.info("重置客户类型 查询currentId:{},result:{}", currentId, JSON.toJSONString(orgs));
                if (result.isFail()) {
                    log.error("重置客户类型 查询失败");
                    return;
                }
                todo = result.getData();
                if (CollectionUtils.isEmpty(todo)) {
                    log.info("重置客户类型 设置完毕，没有剩余可移除的商品");
                    return;
                }
                currentId = todo.get(todo.size() - 1).getId();
                ApiRPCResult<Boolean> moveResult = popSkuAdminApi.setControlUserType(todo,busTypes);
                log.info("重置客户类型 设置todo:{},result:{}", JSON.toJSONString(todo), JSON.toJSONString(moveResult));
                if (moveResult.isFail()) {
                    log.error("重置客户类型 设置失败");
                    return;
                }
                log.info("重置客户类型 设置toDelete: 当前sleepTime:{}", sleepTime);
                Thread.sleep(sleepTime);
            } while (CollectionUtils.isNotEmpty(todo));
        } catch (Exception e) {
            log.error("重置客户类型#orgs:{} 异常", JSON.toJSONString(orgs), e);
        }
    }

    @Override
    public boolean delErpClientInfoByOrgId(String orgId) {
        try {
            log.info("ToolServiceImpl.delErpClientInfoByOrgId#orgId:{}", orgId, JSON.toJSONString(orgId));
            ApiRPCResult apiRPCResult = toolApi.delErpClientInfoByOrgId(orgId);
            log.info("ToolServiceImpl.delErpClientInfoByOrgId##orgId:{},apiRPCResult:{}", orgId, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("ToolServiceImpl.updateCustomerSynStatus#orgId:{}", orgId, e);
            return false;
        }
    }

    @Override
    public void moveDeletedSkuOtherInfo(String orgId) {
        Long currentId = 0l;
        List<Long> toDelete;
        try {
            do{
                ApiRPCResult<List<Long>> idResult = popSkuAdminApi.archivedSkuIds(orgId,currentId, batch);
                log.info("移除商品其他信息 查询currentId:{},result:{}",currentId,JSON.toJSONString(idResult));
                if (idResult.isFail()) {
                    log.error("移除商品其他信息 查询失败");
                    return;
                }
                toDelete = idResult.getData();
                if(CollectionUtils.isEmpty(toDelete)){
                    log.info("移除商品其他信息 移除完毕，没有剩余可移除的商品");
                    return;
                }
                currentId = toDelete.get(toDelete.size()-1);
                ApiRPCResult<Boolean> moveResult = popSkuAdminApi.moveOtherToArchived(toDelete);
                log.info("移除商品其他信息 移除toDelete:{},result:{}",JSON.toJSONString(toDelete),JSON.toJSONString(moveResult));
                if(moveResult.isFail()){
                    log.error("移除商品其他信息 移除失败");
                    return;
                }
                log.info("移除商品其他信息 移除toDelete: 当前sleepTime:{}",sleepTime);
                Thread.sleep(sleepTime);
            }while (CollectionUtils.isNotEmpty(toDelete));
        } catch (Exception e) {
            log.error("移除商品其他信息 异常", e);
        }
    }

    @Override
    public boolean autoApplyInvoice(String autoApplyInvoiceDate) {
        log.info("ToolServiceImpl.autoApplyInvoice#autoApplyInvoiceDate:{}", autoApplyInvoiceDate);
        return toolRemoteAdapter.autoApplyInvoice(autoApplyInvoiceDate);
    }

    @Override
    public void downNoStockSku(String orgId) throws InterruptedException {
        List<String> orgs;
        if(orgId!=null){
            orgs = Arrays.asList(orgId);
        }else {
            orgs = corporationAdminApi.allOrgId().getData();
        }
        log.info("售罄下架商品 机构总数：{}",orgs.size());
        byte AUTO_SHELF_WITH_STOCK_DOWN = 2;
        int count = 0;
        for(String org:orgs){
            count++;
            CorporationDto cor = corporationAdminApi.queryCorporation(org).getData();
            log.info("售罄下架商品 当前机构：{} {}/{}",org,count,orgs.size());
            if(Objects.equals(ShopPatternEnum.FBP.getCode(), cor.getShopPatternCode())){
                log.info("售罄下架商品 当前机构为FBP店铺");
                continue;
            }
            long current = 0l;
            while(true){
                List<PopSkuDto> skus = popSkuAdminApi.nextSkuInfos(org,current,batch);
                log.info("售罄下架商品 current:{},batch:{} result:{}",current,batch,skus.size());
                if(CollectionUtils.isEmpty(skus)){
                    break;
                }
                current = skus.get(skus.size()-1).getId();
                skus = skus.stream().filter(item->item.getStatus()== PopSkuStatus.SALE.getValue()&&item.getCsuid()!=null).collect(Collectors.toList());
                log.info("售罄下架商品 销售看商品{}",skus.size());
                if(CollectionUtils.isEmpty(skus)){
                    continue;
                }
                Map<Long, Integer> stockMap = ecSkuRemoteAdapter.getStockBySkuIdList(skus.stream().map(item -> item.getCsuid()).collect(Collectors.toList())).stream().collect(Collectors.toMap(item -> item.getCsuId(), item -> item.getAvailableQty()));

                List<PopSkuDto> downs = new ArrayList<>(skus.size());
                List<Long> csuids = new ArrayList<>(skus.size());
                for(PopSkuDto sku:skus){
                    Integer stock = stockMap.get(sku.getCsuid());
                    if(stock==null){//没有查到库存，错误数据
                        continue;
                    }
                    if(stock<=0){
                        sku.setStatus(PopSkuStatus.UN_SHELF.getValue());
                        sku.setAutoShelfWithStock(AUTO_SHELF_WITH_STOCK_DOWN);
                        downs.add(sku);
                        csuids.add(sku.getCsuid());
                    }
                }
                log.info("售罄下架商品 需要上架商品{}",csuids.size());
                if(csuids.size()==0){
                    continue;
                }
                //下架ec
                boolean ecResult = ecSkuRemoteAdapter.batchUpdateSkuStatusBySkuIdList("system",csuids,PopSkuStatus.UN_SHELF.getValue(),Arrays.asList(PopSkuStatus.SALE.getValue()));
                if(!ecResult){
                    throw new PopAdminException("更新ec状态失败");
                }
                log.info("售罄下架商品 下架ec成功");
                //下架pop
                boolean  popResult = popSkuAdminApi.stockDown(downs);
                if(!popResult){
                    throw new PopAdminException("更新POP状态失败");
                }
                log.info("售罄下架商品 下架pop成功");
                Thread.sleep(sleepTime);
            }
        }
    }

    /**
     * 订单状态修复（目前仅支持ec订单状态7 pop订单状态1）
     * 1:ec订单状态7 pop订单状态1  将pop订单状态修改成7
     * 2:刷新订单es缓存
     * @param orderNo
     * @return
     */
    @Override
    public String startRepairPopOrderStatus(String orderNo) {
        try {
            ApiRPCResult<OrderDto> apiRPCResult = orderApi.getByOrderNo(orderNo);
            ApiRPCResult<PopOrderDto> popOrderDtoApiRPCResult = popOrderApi.queryByOrderNo(orderNo);

            if (apiRPCResult.isFail() || popOrderDtoApiRPCResult.isFail()){
                return "服务异常，稍后重试";
            }
            OrderDto ecdData = apiRPCResult.getData();
            PopOrderDto popData = popOrderDtoApiRPCResult.getData();
            if (Objects.isNull(ecdData) || Objects.isNull(popData)){
                return "订单不存在";
            }
            if (ecdData.getStatus() == PopOrderStatusEnum.STORAGE.getCode() && popData.getStatus() == PopOrderStatusEnum.WAIT_SHIP.getCode()){
                PopOrderDto popOrderDto = popOrderDtoApiRPCResult.getData();
                popOrderDto.setStatus(PopOrderStatusEnum.STORAGE.getCode());
                popOrderDto.setUtime(LocalDateTime.now());
                ApiRPCResult<PopOrderDto> updatePopOrderDto = popOrderApi.updateByPrimaryKey(popOrderDto);
                if (updatePopOrderDto.isFail()){
                    return "修改pop订单状态失败，稍后重试";
                }
                return "修改pop订单状态为出库中成功";
            }
            orderSearchApi.syncOrderByOrderNo(Collections.singletonList(orderNo));
            return "刷新订单es成功";
        }catch (Exception e){
            return "服务异常，稍后重试";
        }
    }

    @Override
    public boolean updateCorporationInfo(PopCorporationDto popCorporationDto) {
        CorporationInfoDto dto = new CorporationInfoDto();
        BeanUtils.copyProperties(popCorporationDto, dto);
        log.info("ToolServiceImpl.updateCorporationInfo param:{}", JSON.toJSONString(dto));
        ApiRPCResult apiRPCResult = toolApi.updateCorporationInfo(dto);
        log.info("ToolServiceImpl.updateCorporationInfo apiRPCResult:{}", JSON.toJSONString(apiRPCResult));
        if (apiRPCResult == null) {
            throw new PopAdminException("ToolServiceImpl.updateCorporationInfo apiRPCResult is null");
        }
        if (apiRPCResult.isFail() || apiRPCResult.getData() == null) {
            throw new PopAdminException(apiRPCResult.getErrMsg());
        }
        return (boolean) apiRPCResult.getData();
    }


    @Override
    public Boolean updateOfflinePayData(OfflinePayWhitelistVo offlinePayWhitelistVo) {
        List<String> list = Arrays.asList(offlinePayWhitelistVo.getOrgIds().split(","));
        log.info("ToolServiceImpl.updateOfflinePayData param:{}", JSON.toJSONString(list));
        if (CollectionUtils.isNotEmpty(list) && list.size() > offlineListLimit) {
            throw new RuntimeException("商业编码梳理不能超过："+offlineListLimit);
        }
        ApiRPCResult apiRPCResult = null;
        if (Objects.equals(offlinePayWhitelistVo.getOpt(), OptEnum.INSERT.getId())) {
            apiRPCResult = toolApi.batchAddOfflinePayData(list);
        } else if (Objects.equals(offlinePayWhitelistVo.getOpt(), OptEnum.DEL.getId())) {
            apiRPCResult = toolApi.batchUpdateStatus(list);
        } else {
            throw new PopAdminException("ToolServiceImpl.updateOfflinePayData param opt illegal");
        }
        log.info("ToolServiceImpl.updateOfflinePayData apiRPCResult:{}", JSON.toJSONString(apiRPCResult));
        if (apiRPCResult == null) {
            throw new PopAdminException("ToolServiceImpl.updateOfflinePayData apiRPCResult is null");
        }
        if (apiRPCResult.isFail() || apiRPCResult.getData() == null) {
            throw new PopAdminException(apiRPCResult.getErrMsg());
        }
        return (boolean) apiRPCResult.getData();
    }

    /**
     * 临时工具
     * @param deductFundToolVo
     */
    @Override
    public void deductFund(DeductFundToolVo deductFundToolVo) {
        MerchantAccountDeductionParam param = new MerchantAccountDeductionParam();
        param.setOrgId(deductFundToolVo.getMntNo());
        param.setAmount(deductFundToolVo.getAmount());
        param.setDescription(deductFundToolVo.getFundRemarks());
        if (StringUtils.isEmpty(deductFundToolVo.getHireNo())) {
            param.setOrderNo(deductFundToolVo.getMntNo()+System.currentTimeMillis());
        } else {
            param.setOrderNo(deductFundToolVo.getHireNo());
        }
        if (StringUtils.isEmpty(deductFundToolVo.getHireNo())) {
            ApiRPCResult<Boolean> result = popMerchantTotalFundAccountApi.accountDeductionCommission(Arrays.asList(param));
            log.info("deductFund 扣保证金 param:{},result:{}",JSON.toJSONString(deductFundToolVo),JSON.toJSONString(result));
            if (result.isFail()) {
                throw new RuntimeException(result.getErrMsg());

            }
            return;
        }

        ApiRPCResult<PopCommissionSettleDto> rpcResult = popCommissionSettleApi.queryPopCommissionSettleByHireNo(deductFundToolVo.getHireNo());
        log.info("deductFund param:{},rpcResult:{}",JSON.toJSONString(deductFundToolVo),JSON.toJSONString(rpcResult));
        if (rpcResult.isFail()) {
            throw new RuntimeException("查询佣金缴纳记录失败");
        }
        PopCommissionSettleDto data = rpcResult.getData();
        if (deductFundToolVo.getAmount().compareTo(data.getActualHireMoney()) > 0) {
            throw new RuntimeException("扣保证金的金额不能大于佣金缴纳记录的金额，实际需缴纳佣金："+data.getActualHireMoney().toPlainString());
        }
        if (data.getState() == 3 || data.getState() == 5) {
            throw new RuntimeException("佣金缴纳记录当前状态是审核未通过/已结转不能修改状态");
        }
        ApiRPCResult<Boolean> result = popMerchantTotalFundAccountApi.accountDeductionCommission(Arrays.asList(param));
        log.info("deductFund 扣保证金 param:{},result:{}",JSON.toJSONString(deductFundToolVo),JSON.toJSONString(result));
        if (result.isFail()) {
            throw new RuntimeException(result.getErrMsg());

        }
        PopCommissionSettleDto commissionParam = new PopCommissionSettleDto();
//        commissionParam.setHireNo(deductFundToolVo.getHireNo());
        commissionParam.setState(2);
        commissionParam.setRemarks(deductFundToolVo.getHireNoRemarks());
        commissionParam.setId(data.getId());
        ApiRPCResult apiRPCResult = popCommissionSettleApi.updateCommissionSettleStatus(commissionParam);
        log.info("deductFund 修改佣金缴纳记录 param:{},result:{}",JSON.toJSONString(deductFundToolVo),JSON.toJSONString(apiRPCResult));
        if (result.isFail()) {
            throw new RuntimeException("修改佣金缴纳记录状态失败");

        }
    }

    private static Map<String, Object> getProductVo(GeneralProductDto dto) {

        Map<Integer, String> shadingAttrMap = Maps.newHashMap();
        shadingAttrMap.put(1, "外用");
        shadingAttrMap.put(2, "内服");
        shadingAttrMap.put(3, "注射剂");
        shadingAttrMap.put(4, "食品");
        shadingAttrMap.put(5, "保健食品");

        Map<Integer, String> validityUnitMap = Maps.newHashMap();
        validityUnitMap.put(1, "日");
        validityUnitMap.put(2, "月");
        validityUnitMap.put(3, "年");

        Map<Integer, String> preOperateStatusMap = Maps.newHashMap();
        preOperateStatusMap.put(1, "待预首营");
        preOperateStatusMap.put(2, "无需预首营");
        preOperateStatusMap.put(3, "已预首营");


        Map<String, Object> product = Maps.newLinkedHashMap();
        product.put("商品编码", dto.getBusinessCode());
        product.put("标准库id", dto.getProductId());
        product.put("商品名", dto.getBusinessName());
        product.put("商品名助记码", dto.getBusinessNameCode());
        product.put("通用名", dto.getGeneralName());
        product.put("通用名助记码", dto.getGeneralNameCode());
        product.put("别名", dto.getBusinessAliasText());
        product.put("别名助记码", dto.getBusinessAliasCodeText());
        product.put("规格/型号", dto.getSpec());
        product.put("包装单位名称", dto.getPackageUnitName());
        product.put("处方分类id", dto.getPrescriptionCategory());
        product.put("处方分类名称", dto.getPrescriptionCategoryName());
        product.put("小包装条码", dto.getSmallPackageCode());
        product.put("中包装条码", dto.getMediumPackageCode());
        product.put("件包装条码", dto.getPiecePackageCode());
        product.put("品牌/商标", dto.getBrand());
        product.put("有效期单位", validityUnitMap.get(dto.getValidityUnit().intValue()));
        product.put("受托生产厂家名称", dto.getEntrustedManufacturerName());
        product.put("建议零售价", dto.getSuggestedPrice());
        product.put("启用状态", dto.getBusinessDisableStatus() == 0 ? "停用" : dto.getBusinessDisableStatus() == 1 ? "启用" : "删除");
        product.put("SKU预首营状态", preOperateStatusMap.get(dto.getPreOperateStatus().intValue()));
        product.put("商品来源", dto.getBusinessSource());


        product.put("商品类型", dto.getProductType() == 0 ? "普通" : "赠品");
        product.put("商品大类名称", dto.getSpuCategoryName());
        product.put("批件规格", dto.getInstructionSpec());
        product.put("批准文号", dto.getApprovalNo());
        product.put("生产厂家名称", dto.getManufacturerName());
        product.put("生产厂家地址", dto.getProductionAddress());
        product.put("品牌企业", dto.getBrandEnterprise());
        product.put("剂型id", dto.getDosageForm());
        product.put("剂型名称", dto.getDosageFormName());
        product.put("所属经营范围多选id", dto.getBusinessScopeMulti());
        product.put("所属经营范围多选名称", dto.getBusinessScopeMultiName());
        product.put("存储条件名称", dto.getStorageCondName());
        product.put("存储属性", shadingAttrMap.get(dto.getShadingAttr().intValue()));
        product.put("贮藏", dto.getStorage());
        product.put("产地", dto.getOriginPlace());
        product.put("特殊属性名称", dto.getSpecialAttrName());
        product.put("一级分类", dto.getFirstCategory());
        product.put("一级分类名称", dto.getFirstCategoryName());
        product.put("二级分类", dto.getSecondCategory());
        product.put("二级分类名称", dto.getSecondCategoryName());
        product.put("三级分类", dto.getThirdCategory());
        product.put("三级分类名称", dto.getThirdCategoryName());
        product.put("四级分类", dto.getFourthCategory());
        product.put("四级分类名称", dto.getFourthCategoryName());
        product.put("五级分类", dto.getFiveCategory());
        product.put("五级分类名称", dto.getFiveCategoryName());
        product.put("六级分类", dto.getSixCategory());
        product.put("六级分类名称", dto.getSixCategoryName());
        product.put("分类编码", dto.getCategoryCode());
        product.put("税务分类编码", dto.getTaxCategoryCode());
        product.put("进项税率名称", dto.getInRateName());
        product.put("销项税率名称", dto.getOutRateName());
        product.put("上市许可人", dto.getMarketAuthor());
        product.put("商品数量", dto.getSkuCount());
        product.put("是否监管", dto.getWhetherSupervision() == 0 ? "否" : "是");
        product.put("商品本位码", dto.getStandardCodes());
        product.put("质量标准", dto.getQualityStandard());
        product.put("备注", dto.getRemark());
        product.put("品牌分类名称", dto.getBrandCategoryName());

        product.put("创建人所属机构", dto.getCreateInstitutionName());
        product.put("治疗症状", dto.getTreatSymptom());
        product.put("药品分类", dto.getPatentMedicineType() == 1 ? "中成药" : "西药");
        product.put("是否国产", dto.getMadeType() == 1 ? "国产" : "进口");
        product.put("用药人群", dto.getMedicationCrowd() == 1 ? "成人" : "儿童");
        product.put("用药方式名称", dto.getMedicationMethodName());
        product.put("用药频次名称", dto.getMedicationFrequencyName());
        product.put("每次复用数量", dto.getMedicationDosage());
        product.put("最小使用单位名称", dto.getMiniUnitName());
        product.put("成分", dto.getIngredient());
        product.put("适应症", dto.getIndication());
        product.put("不良反应", dto.getAdverseReaction());
        product.put("注意事项", dto.getPrecautions());
        product.put("禁忌", dto.getTaboos());
        product.put("是否处方药", dto.getPrescription() == 1 ? "是" : "否");
        product.put("是否含麻", dto.getMedicinesEphedrine() == 1 ? "是" : "否");
        return product;
    }
}
