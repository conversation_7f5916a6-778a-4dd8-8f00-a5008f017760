/**
 * @(#)Codec.java Copyright 2010 jointown, Inc. All rights reserved.
 */
package com.xyy.ec.pop.utils;

import com.google.common.base.Charsets;

/**
 * description
 * 
 * 
 * @version 1.0,2010-11-10
 */
public class Base64Utils {

    public static String encoding(String value) {
        return org.springframework.util.Base64Utils.encodeToString(value.getBytes(Charsets.UTF_8));
    }

    public static String decodeing(String encodingValue) {
        byte[] bs = org.springframework.util.Base64Utils.decodeFromString(encodingValue);
        return  new String(bs,Charsets.UTF_8);
    }

    /**
     * 字节转码为字符串
     * @Title: encode
     * @param binaryData
     * @return
     * String
     * <AUTHOR>
     * @date 2016-9-15 上午12:18:45
     */
    public static String encode(byte[] binaryData) {
        if (binaryData == null) {
            return null;
        }
        return org.springframework.util.Base64Utils.encodeToString(binaryData);
    }
}
