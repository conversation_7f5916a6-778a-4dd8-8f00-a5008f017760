package com.xyy.ec.pop.controller.mop;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.adapter.mop.AccountAdapter;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.dto.mop.AccountImportDTO;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.utils.easyexcel.EasyExcelUtil;
import com.xyy.ec.pop.utils.mop.MopDataFillerUtils;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.common.condition.CallingLogSelectCondition;
import com.xyy.pop.mop.api.remote.parameter.AccountAddOrUpdateParame;
import com.xyy.pop.mop.api.remote.parameter.AccountBatchImportParame;
import com.xyy.pop.mop.api.remote.parameter.MeSysUserParame;
import com.xyy.pop.mop.api.remote.parameter.query.AccountQueryParame;
import com.xyy.pop.mop.api.remote.result.AccountBasicDTO;
import com.xyy.pop.mop.api.remote.result.MeSysUserDto;
import com.xyy.scm.constant.entity.pagination.Paging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 账号控制器
 * 本控制器提供了账号的增删改查功能，通过调用适配器层的方法实现具体业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/mop/account")
@RestController
public class AccountController extends BaseController {

    @Resource
    private AccountAdapter accountAdapter;
    @Autowired
    private DownloadRemote downloadRemote;
    /**
     * 添加账号
     * 该方法用于新增账号信息
     *
     * @param parame 包含需要创建的账号详细信息的参数对象
     * @return 返回操作结果，包括是否成功创建账号
     */
    @PostMapping("/add")
    public ResponseVo<Boolean> add(@RequestBody AccountAddOrUpdateParame parame) {
        MopDataFillerUtils.fillData(getUser(),parame,false);
        return accountAdapter.addAccount(parame);
    }

    /**
     * 更新账号
     * 该方法用于更新已有的账号信息
     *
     * @param parame 包含需要更新的账号详细信息的参数对象
     * @return 返回操作结果，包括是否成功更新账号
     */
    @PostMapping("/update")
    public ResponseVo<Boolean> update(@RequestBody AccountAddOrUpdateParame parame) {
        MopDataFillerUtils.fillData(getUser(),parame,true);
        return accountAdapter.updateAccount(parame);
    }

    /**
     * 删除账号
     * 该方法用于删除指定的账号信息
     *
     * @param id 需要删除的账号的ID
     * @return 返回操作结果，包括是否成功删除账号
     */
    @GetMapping("/delete/{id}")
    public ResponseVo<Boolean> deleteAccount(@PathVariable Long id) {
        return accountAdapter.deleteAccount(id);
    }

    /**
     * 查询账号
     * 该方法用于查询指定ID的账号详细信息
     *
     * @param id 需要查询的账号的ID
     * @return 返回查询结果，包括账号的基本数据传输对象
     */
    @GetMapping("/{id}")
    public ResponseVo<AccountBasicDTO> findAccount(@PathVariable Long id) {
        return accountAdapter.findAccount(id);
    }

    /**
     * 分页查询账号
     * 该方法用于分页查询账号列表，以便处理大量数据时进行分页显示
     *
     * @param parame 包含分页查询条件的参数对象
     * @return 返回查询结果，包括分页的账号基本数据传输对象列表
     */
    @GetMapping("/list")
    public ResponseVo<Paging<AccountBasicDTO>> pageAccountsByPaging(AccountQueryParame parame) {
        return accountAdapter.pageAccountsByPaging(parame);
    }

    /**
     * 查询中台*
     * @param parame 包含分页查询条件的参数对象
     * @return 返回查询结果，包括分页的账号基本数据传输对象列表
     */
    @GetMapping("/sysuser")
    public ResponseVo<List<MeSysUserDto>> pageAccountsByPaging(MeSysUserParame parame) {
        return accountAdapter.getUserList(parame);
    }

    /**
     * 商业扣罚模版下载
     * @param response
     * @return
     */
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws IOException {
        EasyExcelUtil.writeExcel(response, "账号权限", new ArrayList<>(), AccountImportDTO.class,"权限");
    }
    /**
     * 批量导入账号
     * 该方法用于批量导入账号信息
     *
     * @param file 包含需要导入的账号信息列表
     * @return 返回操作结果，包括是否成功导入账号
     */
    @PostMapping("/batchImport")
    public ResponseVo<Boolean> batchImportAccounts(MultipartFile file) {
        try {
            List<AccountImportDTO> accountImportDTOS = EasyExcelUtil.readExcel(file.getInputStream(), AccountImportDTO.class);
            if (accountImportDTOS == null || accountImportDTOS.isEmpty() || accountImportDTOS.size()>1000) {
                return ResponseVo.errRest("数据不能为空或超过1000条");
            }
            AccountBatchImportParame batchImportParame = new AccountBatchImportParame();
            MopDataFillerUtils.fillData(getUser(),batchImportParame,false);
            return accountAdapter.batchImportAccounts(batchImportParame,accountImportDTOS);
        } catch (IOException e) {
            return ResponseVo.errRest("文件解析失败");
        }
    }

    /**
     * 导出列表
     *
     * @param query
     */
    @GetMapping(value = "/export")
    @ResponseBody
    public ResponseVo<Boolean> exportAccount(AccountQueryParame query) {
        try {
            log.info("exportAccount:{}", JSON.toJSONString(query));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.MOP_ACCOUNT_LIST_EXPORT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("exportAccount result:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        } catch (Exception e) {
            log.error("exportAccount error:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }
}
