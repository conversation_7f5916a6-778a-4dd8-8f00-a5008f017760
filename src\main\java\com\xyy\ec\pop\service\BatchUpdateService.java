package com.xyy.ec.pop.service;

import com.xyy.ec.pop.vo.BatchOverOrderVo;
import com.xyy.ec.pop.vo.BatchUpdateResultVo;

import java.util.List;

/**
 * @Description 批量更新服务
 */
public interface BatchUpdateService {
    /**
     * 根据错误信息，写错误文件，并上传到文件服务器
     * @param totalSize
     * @param errors
     * @param sheetName
     * @return
     */
    BatchUpdateResultVo writeResult(int totalSize, List<BatchOverOrderVo> errors, String sheetName);
}
