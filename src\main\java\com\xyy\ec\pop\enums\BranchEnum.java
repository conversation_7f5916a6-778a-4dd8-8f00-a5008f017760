package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Auther: WanKp
 * @Date: 2018/8/26 22:06
 **/
public enum BranchEnum {

    ALL_COUNTRY("XS000000","全国"),
    HUBEI_COUNTRY("XS420000","湖北子公司"),
    HUNAN_COUNTRY("XS430000","湖南子公司"),
    ANHUI_COUNTRY("XS340000","安徽子公司"),
    ZHEJIANG_COUNTRY("XS330000","浙江子公司"),
    SHANDONG_COUNTRY("XS370000","山东子公司"),
    FUJIAN_COUNTRY("XS350000","福建子公司"),
    HENAN_COUNTRY("XS410000","河南子公司"),
    CHONGQING_COUNTRY("XS500000","重庆子公司"),
    JIANGXI_COUNTRY("XS360000","江西子公司");


    private String key ;
    private  String value;

    BranchEnum(String key, String value){
        this.key = key;
        this.value = value;
    }

    private static Map<String, BranchEnum> enumMaps = new HashMap<>();
    public static Map<String,String> maps = new HashMap<>();
    static {
        for(BranchEnum e : BranchEnum.values()) {
            enumMaps.put(e.getKey(), e);
            maps.put(e.getKey(),e.getValue());
        }
    }

    public static String get(String key) {
        return enumMaps.get(key).getValue();
    }

    public String getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }

}
