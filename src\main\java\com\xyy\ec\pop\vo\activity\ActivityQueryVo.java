package com.xyy.ec.pop.vo.activity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @TableName tb_xyy_pop_commission_activity
 */
@Data
public class ActivityQueryVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 商户名称
     */
    private String orgName;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 活动状态 1 未开始 2进行中 3已结束
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createUser;

    private Integer pageNum;

    private Integer pageSize;

}