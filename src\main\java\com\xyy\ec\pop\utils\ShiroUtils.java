package com.xyy.ec.pop.utils;

import io.buji.pac4j.subject.Pac4jPrincipal;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;

public class ShiroUtils {

    /**
     * 获取中台账户名称
     *
     * @return
     */
    public static String getUsername() {
        Subject subject = SecurityUtils.getSubject();
        if (subject == null){
            return  null;
        }
        PrincipalCollection principalCollection = subject.getPrincipals();
        if (principalCollection == null || principalCollection.isEmpty()){
            return null;
        }
        Pac4jPrincipal pac4j = principalCollection.oneByType(Pac4jPrincipal.class);
        if (pac4j != null){
            return pac4j.getName();
        }
        return null;
    }


    /**
     * 解绑 subject
     */
    public static void unBindSubject() {
        ThreadContext.unbindSubject();
    }

}
