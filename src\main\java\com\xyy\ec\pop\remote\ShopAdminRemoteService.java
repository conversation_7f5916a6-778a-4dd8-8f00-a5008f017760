package com.xyy.ec.pop.remote;

import com.xyy.ec.shop.server.business.results.ShopCodeAndNameDTO;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface ShopAdminRemoteService {

    /**
     * 批量根据店铺编码查询店铺信息列表
     *
     * @param shopCodes
     * @return
     */
    Collection<ShopInfoDTO> queryShopInfoByShopCodes(Collection<String> shopCodes);

    /**
     * 根据店铺编码集合查询店铺后台名称列表
     *
     * @param shopCodes
     * @return
     */
    Collection<ShopCodeAndNameDTO> queryShopCodeAndNamesByShopCodes(Collection<String> shopCodes);

    /**
     * 根据店铺编码集合查询店铺后台名称列表
     *
     * @param shopCodes
     * @return
     */
    Collection<String> queryNameByShopCodes(Collection<String> shopCodes);

    /**
     * 根据店铺后台名称集合查询店铺编码列表
     *
     * @param names
     * @return
     */
    Collection<String> queryShopCodeByNames(Collection<String> names);

    /**
     * 根据区域code查询shopInfo
     * @param branchCodeList
     * @return
     */
    Map<String, ShopInfoDTO> queryECBranchShopCode(List<String> branchCodeList);

}
