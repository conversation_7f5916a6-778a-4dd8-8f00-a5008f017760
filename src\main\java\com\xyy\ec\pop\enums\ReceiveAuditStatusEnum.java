package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/10/7 10:17
 */
public enum ReceiveAuditStatusEnum {

    UNCHECK(0,"未审核"),
    ACCESS(1,"审核通过"),
    UNACCESS(2,"审核不通过");

    private  int id;
    private  String value;

    ReceiveAuditStatusEnum(int id, String value){
        this.id = id;
        this.value = value;
    }
    private static Map<Integer, ReceiveAuditStatusEnum> receiveAuditStatusMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(ReceiveAuditStatusEnum control : ReceiveAuditStatusEnum.values()) {
            receiveAuditStatusMaps.put(control.getId(), control);
            maps.put(control.getId(),control.getValue());
        }
    }
    public static String get(int id) {
        return receiveAuditStatusMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
