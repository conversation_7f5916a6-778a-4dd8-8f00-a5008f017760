package com.xyy.ec.pop.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.excel.entity.PopCommissionSettleExportDetailVo;
import com.xyy.ec.pop.excel.entity.PopCommissionSettleExportVo;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.PopCommissionSettleHelper;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.PopCommissionSettlementRemoteAdapter;
import com.xyy.ec.pop.server.api.order.dto.PopBillSettleDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleAdminQueryDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleBillDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleStatisticsDto;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleStatusEnum;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.service.ExportService;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.CommissionSettleVo;
import com.xyy.ec.pop.vo.CommissionsSettlementCollectVo;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 佣金结算记录管理
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/commissionSettle")
@Slf4j
@Api(tags = "佣金结算管理类")
public class CommissionSettleController extends BaseController {

    @Autowired
    private ExportService exportService;
    @Autowired
    private PopCommissionSettlementRemoteAdapter popCommissionSettlementRemoteAdapter;

    /**
     * 导出功能最大的查询数量（导出佣金结算单中的最大数量、导出对应账单的最大数量）
     */
    private static final Integer MAX_SIZE_QUERY= 20000;
    /**
     * 导出账单信息功能最大的结算单查询数量
     */
    private static final Integer MAX_SIZE_QUERY_SETTLE= 700;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Object list(PopCommissionSettleAdminQueryDto commissionSettlementQueryDTO, PageInfo pageInfo) {
        try {
            if (validateProv(commissionSettlementQueryDTO)) {
                return ResponseUtils.returnObjectSuccess(new PageInfo<>());
            }
            commissionSettlementQueryDTO.setSettlementType((byte) CommissionSettleTypeEnum.EVERY_MONTH.getCode());//默认查询月结数据
            PageInfo<PopCommissionSettleDto> result = popCommissionSettlementRemoteAdapter.queryPopCommissionSettleForPage(commissionSettlementQueryDTO,pageInfo);
            PageInfo<CommissionSettleVo> resultPage = new PageInfo<>();
            if(Objects.nonNull(result)&& CollectionUtils.isNotEmpty(result.getList())) {
                //转dto
                BeanUtils.copyProperties(result,resultPage);
                resultPage.setList(PopCommissionSettleHelper.commissionSettleDto2VoList(result.getList()));
            }
            return ResponseUtils.returnObjectSuccess(resultPage);
        } catch (ServiceException e) {
            log.error("查询统计佣金结算列表异常", e);
            return ResponseUtils.returnException(e);
        }
    }

    private boolean validateProv(PopCommissionSettleAdminQueryDto commissionSettlementQueryDTO) {
        List<Long> provIds = getProvIds(commissionSettlementQueryDTO.getProvId());
        if(CollectionUtils.isEmpty(provIds)){
            return true;
        }
        commissionSettlementQueryDTO.setProvIds(provIds);
        return false;
    }

    /**
     * 根据所搜条件查询获取
     * 待商业付款，审核未通过，待平台审核，逾期未完成，应收佣金金额合计
     * @param commissionSettlementQueryDTO
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/statistics", method = RequestMethod.GET)
    public Object statistics(PopCommissionSettleAdminQueryDto commissionSettlementQueryDTO) {
        try {
            if (validateProv(commissionSettlementQueryDTO)) {
                return ResponseUtils.returnObjectSuccess(new PopCommissionSettleStatisticsDto());
            }
            commissionSettlementQueryDTO.setSettlementType((byte) CommissionSettleTypeEnum.EVERY_MONTH.getCode());//默认查询月结数据
            PopCommissionSettleStatisticsDto dto = popCommissionSettlementRemoteAdapter.queryPopCommissionSettleStatistics(commissionSettlementQueryDTO);
            return ResponseUtils.returnObjectSuccess(dto);
        } catch (ServiceException e) {
            log.error("查询统计佣金结算统计数据异常", e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 确认收款
     * @param collectVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/confirmCollect", method = RequestMethod.POST)
    @AvoidRepeatableCommit(timeout = 3)
    public Object confirmCollect(@RequestBody CommissionsSettlementCollectVo collectVo) {
        try {
            if(Objects.isNull(collectVo)||CollectionUtils.isEmpty(collectVo.getIds())){
                return ResponseUtils.returnCommonException("入参错误");
            }
            if(Objects.equals(collectVo.getState(), CommissionSettleStatusEnum.UN_APPROVED.getStatus())){
                if(StringUtils.isEmpty(collectVo.getRemarks())){
                    return ResponseUtils.returnCommonException("审核不通过必须填写原因");
                }
            }
            SysUser sysUser = getUser();
            ApiRPCResult vo = popCommissionSettlementRemoteAdapter.commissionSettleConfirmCollect(collectVo, sysUser.getUsername());
            if(vo.isFail()){
                return ResponseUtils.returnCommonException(vo.getErrMsg());
            }
            return ResponseUtils.returnObjectSuccess("确认收款成功");
        } catch (ServiceException e) {
            log.error("确认收款异常", e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 根据id查询佣金结算详细信息
     * @return
     */
    @RequestMapping(value = "/commissionSettleDetail",method = RequestMethod.GET)
    @ResponseBody
    public Object commissionSettleDetail(String hireNo){
        try{
            if(StringUtils.isEmpty(hireNo)){
                return ResponseUtils.returnCommonException("佣金结算流水编号不能为空");
            }
            PopCommissionSettleDto dto = popCommissionSettlementRemoteAdapter.queryPopCommissionSettleByHireNo(hireNo);
            CommissionSettleVo vo = PopCommissionSettleHelper.commissionSettleDto2Vo(dto);
            return ResponseUtils.returnObjectSuccess(vo);
        }catch (ServiceException e){
            log.error("查询佣金结算详情信息异常",e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 查询佣金结算详细信息关联的账单明细，带分页
     * @param hireNo 佣金结算流水编号
     * @return
     */
    @RequestMapping(value = "/commissionSettleBillList",method = RequestMethod.GET)
    @ResponseBody
    public Object commissionSettleBillList(String hireNo, PageInfo pageInfo) {
        try{
            if(StringUtils.isEmpty(hireNo)){
                return ResponseUtils.returnCommonException("佣金结算流水编号不能为空");
            }
            PageInfo<PopBillSettleDto> pageInfoResult = popCommissionSettlementRemoteAdapter.queryCommissionSettleBillListByPage(hireNo, pageInfo);
            if (pageInfoResult != null) {
                pageInfoResult.getList().stream().forEach(item -> {
                    if (item.getSettlementType() == CommissionSettleTypeEnum.EVERY_MONTH.getCode()) {
                        item.setHireMoney(item.getPayableCommission());
                    }
                });
            }
            return ResponseUtils.returnObjectSuccess(pageInfoResult);
        }catch (ServiceException e){
            log.error("查询佣金结算的账单明细异常");
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 查询佣金结算列表导出的条数  超过20000条数据，前端给出提示
     * @param commissionSettlementQueryDTO
     * @return
     */
    @RequestMapping(value = "/queryExportCommissionSettleListCount")
    @ResponseBody
    public Object queryExportCommissionSettleListCount(PopCommissionSettleAdminQueryDto commissionSettlementQueryDTO){
        try{
            commissionSettlementQueryDTO.setSettlementType((byte) CommissionSettleTypeEnum.EVERY_MONTH.getCode());//默认查询月结数据
            Long countResult = popCommissionSettlementRemoteAdapter.countPopCommissionSettle(commissionSettlementQueryDTO);
            return ResponseUtils.returnObjectSuccess(countResult);
        }catch (Exception e){
            log.error("查询佣金结算列表导出的条数异常",e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 导出应收佣金
     *
     * @param res
     */
    @RequestMapping(value = "/exportList", method = RequestMethod.GET)
    public void exportList(PopCommissionSettleAdminQueryDto commissionSettlementQueryDTO, ModelMap modelMap, HttpServletRequest request, HttpServletResponse res) throws IOException, ServiceException {
        //获取应导出数据的数据量
        if(Objects.isNull(commissionSettlementQueryDTO)){
            commissionSettlementQueryDTO = new PopCommissionSettleAdminQueryDto();
        }
        commissionSettlementQueryDTO.setSettlementType((byte) CommissionSettleTypeEnum.EVERY_MONTH.getCode());//默认查询月结数据
        Long countResult = popCommissionSettlementRemoteAdapter.countPopCommissionSettle(commissionSettlementQueryDTO);
        if (Objects.isNull(countResult)) {
            log.error("导出应收佣金查询佣金结算列表数量失败");
            setResErrorMsg(res,"导出应收佣金查询佣金结算列表数量失败");
            return ;
        }
        if(countResult > MAX_SIZE_QUERY){
            log.error("导出应收佣金条数超过{}！",MAX_SIZE_QUERY);
            setResErrorMsg(res,"导出上限为"+MAX_SIZE_QUERY+"条，当前搜索结果导出数据为"+countResult+"条，超出导出上限");
            return ;
        }

        List<PopCommissionSettleDto> settleDtoList = Lists.newArrayList();
        int pageNum = 1;
        int limit = 500;
        while(true){
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageNum(pageNum);
            pageInfo.setPageSize(limit);
            //分页查询应收佣金信息，然后组装成大的list用来进行导出操作
            PageInfo<PopCommissionSettleDto> rpcResult = popCommissionSettlementRemoteAdapter.queryPopCommissionSettleForPage(commissionSettlementQueryDTO,pageInfo);
            if (Objects.isNull(rpcResult)) {
                log.error("获取应收佣金数据失败,queryDTO:{},pageNum:{}",JSONObject.toJSONString(commissionSettlementQueryDTO),pageNum);
                break;
            }
            if(CollectionUtils.isEmpty(rpcResult.getList())){
                log.error("导出应收佣金列表为空");
                break ;
            }
            settleDtoList.addAll(rpcResult.getList());
            //总页数
            int pageCount = rpcResult.getPages();
            if(pageNum >= pageCount || pageNum >= 40){
                break;
            }
            pageNum++;
        }
        //将应付佣金账单信息转换成对应的详情信息
        List<PopCommissionSettleExportVo> list = PopCommissionSettleHelper.convertSettleExportVo(settleDtoList);

        String filName = "应收佣金"+DateUtil.date2Str(new Date(),"yyyyMMdd");
        exportService.export(modelMap, request, res,
                list, PopCommissionSettleExportVo.class,
                filName, filName);
    }

    /**
     * 查询佣金结算明细的导出的条数  超过20000条数据，前端给出提示
     * @param commissionSettlementQueryDTO
     * @return
     */
    @RequestMapping(value = "/queryExportCommissionSettleBillListCount")
    @ResponseBody
    public Object queryExportCommissionSettleBillListCount(PopCommissionSettleAdminQueryDto commissionSettlementQueryDTO){
        try{
            commissionSettlementQueryDTO.setSettlementType((byte) CommissionSettleTypeEnum.EVERY_MONTH.getCode());//默认查询月结数据
            Long countResult = popCommissionSettlementRemoteAdapter.countPopCommissionSettle(commissionSettlementQueryDTO);
            if(Objects.isNull(countResult)){
                log.error("查询佣金结算明细的佣金结算记录的导出的条数为空,{}",JSONObject.toJSONString(commissionSettlementQueryDTO));
                return ResponseUtils.returnCommonException("查询佣金结算明细的佣金结算记录的导出的条数为空");
            }
            if(countResult>MAX_SIZE_QUERY_SETTLE){
                log.error("查询佣金结算明细的佣金结算记录的条数超过{}条，{}",MAX_SIZE_QUERY_SETTLE,JSONObject.toJSONString(commissionSettlementQueryDTO));
                return ResponseUtils.returnCommonException("导出上限为20000条，当前搜索佣金结算结果导出数据为"+MAX_SIZE_QUERY_SETTLE+"条，超出导出上限");
            }
            List<PopCommissionSettleDto> settleDtoList = popCommissionSettlementRemoteAdapter.queryPopCommissionSettleList(commissionSettlementQueryDTO);
            if(CollectionUtils.isEmpty(settleDtoList)){
                return ResponseUtils.returnCommonException("查询佣金结算明细的佣金结算记录的条数为空");
            }
            List<String> hireNoList = settleDtoList.stream().filter(dto->Objects.nonNull(dto)).map(dto->dto.getHireNo()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(hireNoList)){
                return ResponseUtils.returnCommonException("查询佣金结算明细的佣金结算记录的流水号为空");
            }
            if (hireNoList.size() > MAX_SIZE_QUERY) {
                log.error("根据流水号列表查询佣金结算的账单数据量超过{}！",MAX_SIZE_QUERY);
                return ResponseUtils.returnCommonException("导出上限为20000条，当前搜索结果导出数据为"+MAX_SIZE_QUERY_SETTLE+"条，超出导出上限");
            }
            Long settlementBillDetailtCount = 0L;
            List<List<String>> partition = Lists.partition(hireNoList, 200);
            for(List<String> hireNos : partition){
                Long settleBillDetailListCount = popCommissionSettlementRemoteAdapter.countPopCommissionSettleBill(hireNos);
                if(Objects.nonNull(settleBillDetailListCount)){
                    settlementBillDetailtCount = settlementBillDetailtCount + settleBillDetailListCount;
                }
            }
            if (settlementBillDetailtCount > MAX_SIZE_QUERY) {
                log.error("根据流水号列表查询佣金结算的账单数据量超过{}！",MAX_SIZE_QUERY);
                return ResponseUtils.returnCommonException("导出上限为20000条，当前搜索结果导出数据为"+MAX_SIZE_QUERY_SETTLE+"条，超出导出上限");
            }
            return ResponseUtils.returnObjectSuccess(settlementBillDetailtCount);
        }catch (Exception e){
            log.error("查询佣金结算明细的导出的条数异常",e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 导出应收佣金明细
     *
     * @param res
     */
    @RequestMapping(value = "/exportDetailList", method = RequestMethod.GET)
    public void exportDetailList(PopCommissionSettleAdminQueryDto commissionSettlementQueryDTO, ModelMap modelMap, HttpServletRequest request, HttpServletResponse res) throws IOException, ServiceException {
        //获取应导出数据的应用佣金的数据量
        if(Objects.isNull(commissionSettlementQueryDTO)){
            commissionSettlementQueryDTO = new PopCommissionSettleAdminQueryDto();
        }
        commissionSettlementQueryDTO.setSettlementType((byte) CommissionSettleTypeEnum.EVERY_MONTH.getCode());//默认查询月结数据
        Long countResult = popCommissionSettlementRemoteAdapter.countPopCommissionSettle(commissionSettlementQueryDTO);
        if (Objects.isNull(countResult)) {
            log.error("导出应收佣金明细查询佣金结算列表数量失败");
            setResErrorMsg(res,"导出应收佣金明细查询佣金结算列表数量失败");
            return ;
        }
        if(countResult > MAX_SIZE_QUERY_SETTLE){
            log.error("导出应收佣金明细所属的应收佣金的条数超过{}，可能对应的账单明细超过{}！",MAX_SIZE_QUERY_SETTLE,MAX_SIZE_QUERY);
            setResErrorMsg(res,"导出上限为"+MAX_SIZE_QUERY_SETTLE+"条，当前搜索结果导出数据为"+countResult+"条，超出导出上限");
            return ;
        }

        List<PopCommissionSettleDto> settleList = Lists.newArrayList();
        List<PopCommissionSettleBillDto> billDtoList = Lists.newArrayList();
        int pageNum = 1;
        int limit = 200;
        while(true){
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageNum(pageNum);
            pageInfo.setPageSize(limit);
            //分页查询应收佣金信息，然后组装成大的list用来进行导出操作
            PageInfo<PopCommissionSettleDto> rpcResult = popCommissionSettlementRemoteAdapter.queryPopCommissionSettleForPage(commissionSettlementQueryDTO,pageInfo);
            if (Objects.isNull(rpcResult)) {
                log.error("导出应收佣金明细查询数据失败,queryDTO:{},pageNum:{}",JSONObject.toJSONString(commissionSettlementQueryDTO),pageNum);
                break;
            }
            List<PopCommissionSettleDto> settleDtoList = rpcResult.getList();
            if(CollectionUtils.isEmpty(settleDtoList)){
                log.error("导出应收佣金明细查询佣金结算列表为空");
                break ;
            }
            settleList.addAll(settleDtoList);

            //获取应收佣金对应的流水号，用来获取对应的账单信息
            List<String> hireNoList = settleDtoList.stream().filter(dto->Objects.nonNull(dto)).map(dto->dto.getHireNo()).collect(Collectors.toList());
            //分页hireNo查询对应的账单列表
            List<PopCommissionSettleBillDto> settleBillDtoList = popCommissionSettlementRemoteAdapter.queryCommissionSettleBillListByHireNoList(hireNoList);
            if (CollectionUtils.isNotEmpty(settleBillDtoList)) {
                if (settleBillDtoList.size() > MAX_SIZE_QUERY) {
                    log.error("分页导出应收佣金的账单数据量超过{}！",MAX_SIZE_QUERY);
                    setResErrorMsg(res,"导出上限为"+MAX_SIZE_QUERY+"条，当前搜索结果导出数据为"+settleBillDtoList.size()+"条，超出导出上限");
                    return;
                }
                billDtoList.addAll(settleBillDtoList);
            }

            //总页数
            int pageCount = rpcResult.getPages();
            if(pageNum >= pageCount || pageNum >= 100){
                break;
            }
            pageNum++;
        }

        if(billDtoList.size()>MAX_SIZE_QUERY){
            log.error("导出应收佣金的账单总数据量超过{}！",MAX_SIZE_QUERY);
            setResErrorMsg(res,"导出上限为"+MAX_SIZE_QUERY+"条，当前搜索结果导出数据为"+billDtoList.size()+"条，超出导出上限");
            return;
        }
        //组装hireNo对应的settleDto的map
        Map<String,PopCommissionSettleDto> hireNoSettleDtoMap = settleList.stream().collect(Collectors.toMap(PopCommissionSettleDto::getHireNo, Function.identity()));

        //将应付佣金账单信息转换成对应的详情信息
        List<PopCommissionSettleExportDetailVo> list = PopCommissionSettleHelper.convertSettleDetailExportVo(billDtoList,hireNoSettleDtoMap);

        String filName = "应收佣金明细"+DateUtil.date2Str(new Date(),"yyyyMMdd");
        exportService.export(modelMap, request, res,
                list, PopCommissionSettleExportDetailVo.class,
                filName, filName);
    }


    private void setResErrorMsg(HttpServletResponse res,String errorMsg) throws IOException, ServiceException{
        res.setContentType("text/html;charset=UTF-8");
        res.setCharacterEncoding("UTF-8");
        PrintWriter writer = res.getWriter();
        writer.print(errorMsg);
    }
}
