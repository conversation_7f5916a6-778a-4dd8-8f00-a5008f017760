package com.xyy.ec.pop.adapter.mop;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.DepartmentRemote;
import com.xyy.pop.mop.api.remote.parameter.DepartmentParame;
import com.xyy.pop.mop.api.remote.result.DepartmentTreeDTO;
import com.xyy.scm.constant.foundation.TreeBean;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DepartmentAdapter implements MopBaseAdapter {

    @Reference(version = "1.0.0")
    private DepartmentRemote departmentRemote;

    public ResponseVo<Boolean> batchUpdateDepartments(List<DepartmentParame> departmentParams) {
        return to(()->departmentRemote.batchUpdateDepartments(departmentParams));
    }

    public ResponseVo<DepartmentTreeDTO> getDepartmentTree() {
        return to(()->departmentRemote.getDepartmentTree());
    }
}
