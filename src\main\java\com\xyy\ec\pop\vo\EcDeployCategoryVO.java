package com.xyy.ec.pop.vo;

import java.io.Serializable;

public class EcDeployCategoryVO implements Serializable {
    //分类id
    private Long id;
    //分类名称
    private String name;
    //父分类id
    private Long parentId;
    //所处级别
    private Integer level;
    //是否展示： 0-不展示，1-展示
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}

