package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.shop.api.ShopNoticeApi;
import com.xyy.ec.pop.server.api.shop.dto.PopCheckLogDto;
import com.xyy.ec.pop.service.RemoteService;
import com.xyy.ec.shop.server.business.api.ShopAdminApi;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class RemoteServiceImpl implements RemoteService {

    @Reference
    private ShopAdminApi shopAdminApi;

    @Reference
    private ShopNoticeApi shopNoticeApi;

    public List<ShopInfoDTO> queryInfoByShopCodes(List<String> shopCodes) {
        try {
            log.info("RemoteServiceImpl.queryInfoByShopCodes# params shopCodes:{}", JSON.toJSONString(shopCodes));
            if (CollectionUtils.isEmpty(shopCodes)) {
                return Lists.newArrayList();
            }

            ApiRPCResult<List<ShopInfoDTO>> rpcResult = shopAdminApi.queryInfoByShopCodes(shopCodes);
            log.info("RemoteServiceImpl.queryInfoByShopCodes# params shopCodes:{}, rpcResult:{}", JSON.toJSONString(shopCodes),
                    JSON.toJSONString(rpcResult));
            if (rpcResult == null || rpcResult.isFail()) {
                log.error("RemoteServiceImpl.queryInfoByShopCodes调用异常# params shopCodes:{}", JSON.toJSONString(shopCodes));
                return Lists.newArrayList();
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("RemoteServiceImpl.queryInfoByShopCodes调用异常# params shopCodes:{}", JSON.toJSONString(shopCodes), e);
            return Lists.newArrayList();
        }
    }

    public Boolean updateShopStatusByShopCode(String shopCode, Integer status) {
        try {
            log.info("RemoteServiceImpl.queryInfoByShopCodes# params shopCode:{}, status:{}",
                    JSON.toJSONString(shopCode), status);
            if (StringUtils.isEmpty(shopCode) || Objects.isNull(status)) {
                return Boolean.FALSE;
            }

            ApiRPCResult rpcResult = shopAdminApi.updateShopStatusByShopCode(shopCode, status);
            log.info("RemoteServiceImpl.queryInfoByShopCodes# params shopCode:{}, status:{}, rpcResult:{}",
                    JSON.toJSONString(shopCode), status, JSON.toJSONString(rpcResult));
            if (rpcResult == null || rpcResult.isFail()) {
                log.error("RemoteServiceImpl.queryInfoByShopCodes# params shopCode:{}, status:{}",
                        JSON.toJSONString(shopCode), status);
                return Boolean.FALSE;
            }
            return rpcResult.isSuccess();
        } catch (Exception e) {
            log.error("RemoteServiceImpl.queryInfoByShopCodes# params shopCode:{}, status:{}",
                    JSON.toJSONString(shopCode), status, e);
            return Boolean.FALSE;
        }
    }


    public Boolean savePopCheckLogDto(PopCheckLogDto dto) {
        try {
            log.info("RemoteServiceImpl.savePopCheckLogDto# params dto:{}", JSON.toJSONString(dto));
            if (Objects.isNull(dto)) {
                return Boolean.FALSE;
            }

            ApiRPCResult rpcResult = shopNoticeApi.saveLog(dto);
            log.info("RemoteServiceImpl.savePopCheckLogDto# params dto:{}, rpcResult:{}",
                    JSON.toJSONString(dto), JSON.toJSONString(rpcResult));
            if (rpcResult == null || rpcResult.isFail()) {
                log.error("RemoteServiceImpl.savePopCheckLogDto# params dto:{}", JSON.toJSONString(dto));
                return Boolean.FALSE;
            }
            return rpcResult.isSuccess();
        } catch (Exception e) {
            log.error("RemoteServiceImpl.savePopCheckLogDto# params dto:{}",
                    JSON.toJSONString(dto), e);
            return Boolean.FALSE;
        }
    }
}
