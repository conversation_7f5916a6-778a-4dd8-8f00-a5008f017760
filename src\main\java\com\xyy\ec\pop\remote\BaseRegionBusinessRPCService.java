package com.xyy.ec.pop.remote;

import com.xyy.address.api.BaseRegionBusinessApi;
import com.xyy.address.dto.XyyRegionBusinessDto;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * {@link BaseRegionBusinessApi} RPC Service
 *
 * <AUTHOR>
 */
public interface BaseRegionBusinessRPCService {

    /**
     * 获取省份地域列表
     *
     * @return
     */
    List<XyyRegionBusinessDto> listProvinceRegions();

    /**
     * 根据行政区域编码批量查询
     *
     * @param areaCodes
     * @return
     */
    List<XyyRegionBusinessDto> listRegionsByCodes(List<Integer> areaCodes);

    /**
     * 根据行政区域编码获取子级行政区域信息列表
     *
     * @param parentCode
     * @return
     */
    List<XyyRegionBusinessDto> listChildrenRegionsByParentCode(Integer parentCode);


    List<XyyRegionBusinessDto> queryRegionParam(Integer parentCode);

    List<XyyRegionBusinessDto> queryChildrenRegionParam(List<Integer> parentCodes);

    List<XyyRegionBusinessDto> queryParentToProvinceByAreaList(List<Integer> areaCodes);
}
