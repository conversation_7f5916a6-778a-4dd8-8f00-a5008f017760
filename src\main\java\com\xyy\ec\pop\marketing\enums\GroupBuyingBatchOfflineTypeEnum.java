package com.xyy.ec.pop.marketing.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 拼团批量下线类型枚举类。
 * 1：通过报名ID下线；2：通过活动ID下线
 *
 * <AUTHOR>
 */
@Getter
public enum GroupBuyingBatchOfflineTypeEnum {

    /**
     * 通过报名ID下线
     */
    BY_REPORT_ID(1, "通过报名ID下线"),

    /**
     * 通过活动ID下线
     */
    BY_ACT_ID(2, "通过活动ID下线"),
    ;

    private Integer type;
    private String name;

    GroupBuyingBatchOfflineTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * 自定义 valueOf()方法
     *
     * @param type
     * @return
     */
    public static GroupBuyingBatchOfflineTypeEnum valueOfCustom(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        for (GroupBuyingBatchOfflineTypeEnum anEnum : values()) {
            if (Objects.equals(anEnum.getType(), type)) {
                return anEnum;
            }
        }
        return null;
    }

}
