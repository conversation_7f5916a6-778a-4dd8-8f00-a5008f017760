package com.xyy.ec.pop.utils;

import java.io.*;
import java.util.*;

/**
 * 集合工具类
 * @ClassName: CollectionUtil 
 * <AUTHOR>
 * @date 2016-5-6 上午12:55:43
 */
public class CollectionUtil {
	/**
	 * 判断list集合为空
	 * @Title: isEmpty
	 * @param collection
	 * @return
	 * boolean
	 * <AUTHOR> 
	 * @date 2016-7-23 下午4:15:42
	 */
	public static boolean isEmpty(Collection<?> collection) {
		return ((collection == null) || (collection.isEmpty()));
	}
	
	/**
	 * 判断list集合不为空
	 * @Title: isNotEmpty
	 * @param collection
	 * @return
	 * boolean
	 * <AUTHOR> 
	 * @date 2017-1-12 下午11:46:24
	 */
	public static boolean isNotEmpty(Collection<?> collection) {
		return !isEmpty(collection);
	}
	/**
	 * 判断字典是否为空
	 * @Title: isEmpty
	 * @param map
	 * @return
	 * boolean
	 * <AUTHOR> 
	 * @date 2016-7-23 下午4:15:58
	 */
	public static boolean isEmpty(Map<?, ?> map) {
		return ((map == null) || (map.isEmpty()));
	}
	
	/**
	 * 判断字典不为空
	 * @Title: isNotEmpty
	 * @param map
	 * @return
	 * boolean
	 * <AUTHOR> 
	 * @date 2017-1-12 下午11:47:12
	 */
	public static boolean isNotEmpty(Map<?, ?> map) {
		return !isEmpty(map);
	}
	
	/**
	 * 判断数组为空
	 * @Title: isEmpty
	 * @param array
	 * @return
	 * boolean
	 * <AUTHOR> 
	 * @date 2017-1-12 下午11:52:29
	 */
	public static boolean isEmpty(Object[] array) {
		return array==null || array.length==0;
	}
	
	/**
	 * 判断数组不为空
	 * @Title: isNotEmpty
	 * @param array
	 * @return
	 * boolean
	 * <AUTHOR> 
	 * @date 2017-1-12 下午11:53:35
	 */
	public static boolean isNotEmpty(Object[] array) {
		return !isEmpty(array);
	}
	
	/**
	 * 用指定符号拼接list
	 * @Title: listToString
	 * @param list
	 * @param separator
	 * @return
	 * String
	 * <AUTHOR> 
	 * @date 2017-1-15 上午2:24:27
	 */
	public static String listToString(Collection<? extends Object> collection,String separator){
        if (CollectionUtil.isEmpty(collection)) {
            return null;
        }
        int length=collection.size();
        StringBuilder sb = new StringBuilder(length * 16);
        Iterator<?> iterator= collection.iterator();
        while(iterator.hasNext()){
            sb.append(separator).append(iterator.next());
        }
        if(sb.length()>0){
        	sb.deleteCharAt(0);
        }
        return sb.toString();
    }
	
	/**
	 * 用指定符号拼接数组
	 * @Title: listToString
	 * @param list
	 * @param separator
	 * @return
	 * String
	 * <AUTHOR> 
	 * @date 2017-1-15 上午2:24:27
	 */
	public static String arrayToString(Object[] array,String separator){
        if (CollectionUtil.isEmpty(array)) {
            return null;
        }
        int length=array.length;
        StringBuilder sb = new StringBuilder(length * 16);
        for (int i = 0; i < length; i++) {
            if (i > 0) {
            	sb.append(separator);
            }
            sb.append(array[i]);
        }
        return sb.toString();
    }
	/**
	 * List深度克隆
	 * @Title: deepClone
	 * @param list
	 * @return
	 * List<T>
	 * <AUTHOR> 
	 * @date 2017-4-27 下午10:34:27
	 */
	public static <T> List<T> deepClone(List<T> list){
	    List<T> dest = null;
        try {
			ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
			ObjectOutputStream out = new ObjectOutputStream(byteOut);
			out.writeObject(list);
			ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
			ObjectInputStream in = new ObjectInputStream(byteIn);
			dest = (List<T>) in.readObject();
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
	    return dest;
	}
	
	/**
	 * 分割集合
	 * @Title: splitList
	 * @param list
	 * @param len
	 * @return
	 * List<List<?>>
	 * <AUTHOR> 
	 * @date 2018年1月25日 下午2:49:32
	 */
	public static <T> List<List<T>> splitList(List<T> list, int len) {  
		if (isEmpty(list) || len < 1) {  
			return null;  
		}  
		  
		List<List<T>> result = new ArrayList<List<T>>();  
		  
		  
		int size = list.size();  
		int count = (size + len - 1) / len;  
		  
		  
		for (int i = 0; i < count; i++) {  
			List<T> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));  
			result.add(subList);  
		}
		return result;  
	}

	public static void main(String[] args) {
		List<Double> list=new ArrayList<Double>();
		list.add(1.1);
		list.add(3.1);
		list.add(2.1);
		list.add(8.1);
		list.add(7.1);
		list.add(9.1);
		list.add(5.1);
		System.out.println(splitList(list, 3));
	}
}
