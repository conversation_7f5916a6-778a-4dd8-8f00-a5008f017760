package com.xyy.ec.pop.excel.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 佣金结算记录 详情 包含对应的账单信息
 * 后台导出功能需要用的实体
 * tb_xyy_pop_commission_settle
 * <AUTHOR>
@Data
@NoArgsConstructor
public class PopCommissionSettleExportDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 佣金结算流水编号
     */
    private String hireNo;

    /**
     * 商户编号
     */
    @Excel(name = "商户编号",width = 15)
    private String orgId;

    /**
     * 商家名称
     */
    @Excel(name = "商户名称",width = 15)
    private String orgName;

    /**
     * 店铺名称
     */
    @Excel(name = "店铺名称",width = 15)
    private String name;

    /**
     * 佣金收取月份，逗号分隔
     */
    @Excel(name = "佣金收取月份",width = 20)
    private String hireMonths;

    /**
     * 佣金
     */
    @Excel(name = "应收佣金金额",width = 20)
    private BigDecimal hireMoney;

    /**
     * 创建时间
     */
    @Excel(name = "生成时间",width = 20,format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 付款期限：最后付款时间
     */
    @Excel(name = "最后付款时间",width = 20,format = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTerm;

    /**
     * 是否逾期
     */
    @Excel(name = "是否逾期",width = 10)
    private String overdue;

    /**
     * 确认收款时间
     */
    @Excel(name = "确认收款时间",width = 15,format = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    /**
     * 状态：0：待商业付款；1-待平台审核；2：审核通过；3：审核未通过；4：待结转;5:已结转
     */
    @Excel(name = "状态",width = 10)
    private String state;

    /**
     * 备注原因
     */
    @Excel(name = "备注/原因",width = 20)
    private String remarks;

    /**
     * 账单号
     */
    @Excel(name = "账单号",width = 15)
    private String billNo;

    /**
     * 商品总金额
     */
    @Excel(name = "商品金额",width = 15)
    private BigDecimal productMoney;

    /**
     * 运费
     */
    @Excel(name = "运费金额",width = 15)
    private BigDecimal freightAmount;

    /**
     * 单据总金额
     */
    @Excel(name = "单据总额（含运费）",width = 15)
    private BigDecimal totalMoney;

    /**
     * 店铺总优惠金额
     */
    @Excel(name = "店铺总优惠",width = 15)
    private BigDecimal shopTotalDiscount;

    /**
     * 平台总优惠金额
     */
    @Excel(name = "平台总优惠",width = 15)
    private BigDecimal platformTotalDiscount;

    /**
     * 订单实际付款金额
     */
    @Excel(name = "实付金额",width = 15)
    private BigDecimal money;

    /**
     * 佣金金额
     */
    @Excel(name = "佣金金额",width = 15)
    private BigDecimal billHireMoney;

    /**
     * 应结算金额
     */
    @Excel(name = "应结算金额",width = 15)
    private BigDecimal statementTotalMoney;

    /**
     * 应收佣金
     */
    @Excel(name = "应收佣金",width = 15)
    private BigDecimal payableCommission;

    /**
     * 账单生成时间
     */
    @Excel(name = "账单生成时间",width = 15,format = "yyyy-MM-dd HH:mm:ss")
    private Date billCreateTime;

    /**
     * 账单入账时间
     */
    @Excel(name = "入账时间",width = 15,format = "yyyy-MM-dd HH:mm:ss")
    private Date billPaymentTime;

}