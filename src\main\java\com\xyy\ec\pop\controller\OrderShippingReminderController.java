package com.xyy.ec.pop.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.exception.XyyEcOrderBizNoneCheckRTException;
import com.xyy.ec.order.backend.shippingReminder.dto.OrderShippingReminderDto;
import com.xyy.ec.order.backend.shippingReminder.dto.OrderShippingReminderHistoryBackendDto;
import com.xyy.ec.order.backend.shippingReminder.params.OrderShippingReminderAuditBatchParams;
import com.xyy.ec.order.backend.shippingReminder.params.OrderShippingReminderAuditParams;
import com.xyy.ec.order.backend.shippingReminder.params.OrderShippingReminderParams;
import com.xyy.ec.order.business.enums.orderShippingReminder.OrderShippingReminderState;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.remote.OrderShippingReminderRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.export.param.AfterSalesExportAdminParam;
import com.xyy.ec.pop.server.api.merchant.dto.MerchantTotalFundInfoDto;
import com.xyy.ec.pop.server.api.merchant.param.MerchantTotalFundInfoParam;
import com.xyy.ec.pop.vo.OrderShippingReminderAuditBatchVo;
import com.xyy.ec.pop.vo.OrderShippingReminderVo;
import com.xyy.ec.pop.vo.ResponseVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 催发货订单
 * <AUTHOR>
 * @date 2024/10/16 19:53
 */
@Slf4j
@RestController
@Api(tags = "催发货订单")
@RequestMapping("/order/shippingReminder")
public class OrderShippingReminderController extends BaseController {

    @Autowired
    private OrderShippingReminderRemote orderShippingReminderRemote;
    @Autowired
    private DownloadRemote downloadRemote;

    @GetMapping("/status")
    public ResponseVo<List<Map<String, String>>> statusList() {
        List<Map<String, String>> maps = orderShippingReminderRemote.queryStatusList();
        if (CollectionUtils.isEmpty(maps)){
            return ResponseVo.errRest("查询催发货状态枚举失败");
        }
        return ResponseVo.successResult(maps);
    }

    @PostMapping("/list")
    public ResponseVo<PageInfo<OrderShippingReminderDto>> list(@RequestBody OrderShippingReminderParams params) {
        PageInfo<OrderShippingReminderDto> pageInfo = orderShippingReminderRemote.queryShippingRemindersPage(params);
        if (pageInfo == null){
            return ResponseVo.errRest("查询催发货列表错误");
        }
        return ResponseVo.successResult(pageInfo);
    }

    @PostMapping("/loadStatusCount")
    public ResponseVo<List<Map<String, Number>>> loadStatusCount(@RequestBody OrderShippingReminderParams params) {
        params.setStatusList(Collections.singletonList(OrderShippingReminderState.APPEAL_PROCESSING.getState()));
        List<Map<String, Number>> statusCount = orderShippingReminderRemote.loadShippingReminderStatusCount(params);
        return ResponseVo.successResult(statusCount);
    }

    @GetMapping("/history")
    public ResponseVo<OrderShippingReminderHistoryBackendDto> history(Long reminderId) {
        try {
            OrderShippingReminderHistoryBackendDto orderShippingReminderHistoryBackendDto = orderShippingReminderRemote.queryHistory(reminderId);
            return ResponseVo.successResult(orderShippingReminderHistoryBackendDto);
        }catch (Exception e){
            return ResponseVo.errRest(e.getMessage());
        }
    }

    @PostMapping("/audit")
    public ResponseVo<OrderShippingReminderHistoryBackendDto> audit(@RequestBody OrderShippingReminderAuditParams orderShippingReminderAuditParams) {
        try {
            log.info("催发货审核请求参数:"+JSON.toJSONString(orderShippingReminderAuditParams));
            SysUser user = getUser();
            String operator = user.getRealName();
            Boolean auditResult = orderShippingReminderRemote.audit(orderShippingReminderAuditParams, operator);
            if (auditResult){
                return ResponseVo.successResultNotData();
            }else{
                return ResponseVo.errRest("审核失败");
            }
        }catch (XyyEcOrderBizNoneCheckRTException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e){
            return ResponseVo.errRest("审核失败");
        }
    }

    @GetMapping("/auditDetail")
    public ResponseVo<OrderShippingReminderHistoryBackendDto.CustomFields> auditDetail(Long reminderId) {
        try {
            OrderShippingReminderHistoryBackendDto.CustomFields customFields = orderShippingReminderRemote.queryAuditDetail(reminderId);
            return ResponseVo.successResult(customFields);
        }catch (Exception e){
            return ResponseVo.errRest(e.getMessage());
        }
    }

    @PostMapping("/auditBatch")
    public ResponseVo<String> auditBatch(@RequestBody OrderShippingReminderAuditBatchVo orderShippingReminderAuditBatchVo) {
        try {
            List<OrderShippingReminderVo> reminders = orderShippingReminderAuditBatchVo.getReminders();
            if (CollectionUtils.isEmpty(reminders)){
                return ResponseVo.errRest("请选择要审核的催发货单");
            }
            if (reminders.size()>50){
                return ResponseVo.errRest("一次最多审核50个催发货单");
            }
            SysUser user = getUser();
            String operator = user.getRealName();
            return orderShippingReminderRemote.auditBatch(orderShippingReminderAuditBatchVo, operator);
        } catch (Exception e) {
            return ResponseVo.errRest(e.getMessage());
        }
    }

    /**
     * 导出列表
     *
     * @param query
     */
    @PostMapping(value = "/export")
    @ResponseBody
    public ResponseVo<Boolean> exportShippingReminders(@RequestBody OrderShippingReminderParams query) {
        try {
            log.info("exportAfterSales:{}", JSON.toJSONString(query));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.ADMIN_SHIPMENT_REMINDER_ORDER)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("exportAfterSales result:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        } catch (Exception e) {
            log.error("exportAfterSales error:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }
}
