html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, menu, nav, output, ruby, section, summary, time, mark, audio, video{
    margin: 0;
    padding: 0;
    border: 0;
    text-decoration: none;
    font: normal;
    font-size: 12px;
    font-family: "Microsoft YaHei", "微软雅黑";
}

.top_input{
    height: 50px;
    padding-left: 20px;
}
.top_input input{
    border: 0;
    outline: none;
    margin-top: 5px;
}

.top_input_name{
    float: left;
    line-height: 50px;
}
.top_input_div{
    float: left;
    height: 30px;
    border: 1px solid #e3e3e3;
    border-radius: 5px;
    margin-top: 10px;
}
.top_input_button{
    width: 80px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color:#0784cb ;
    background: -webkit-linear-gradient(#0784cb, #0075a9); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(#0784cb, #0075a9); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#0784cb, #0075a9); /* Firefox 3.6 - 15 */
    background: linear-gradient(#0784cb, #0075a9); /* 鏍囧噯鐨勮娉� */
    border-radius: 5px;
    margin-top: 10px;
    margin-left: 17px;
    cursor: pointer;
    color: #fff;
    float: left;
}
.top_input_button:hover{
    background: -webkit-linear-gradient(#0075a9, #0784cb); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(#0075a9, #0784cb); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#0075a9, #0784cb); /* Firefox 3.6 - 15 */
    background: linear-gradient(#0075a9, #0784cb); /* 鏍囧噯鐨勮娉� */
}
.top_input_text{
    float: right;
    line-height: 50px;
    margin-right: 20px;
}
.top_input_text span{
    color: red;
}

.content_button{
    width: 98%;
    height: 50px;
    background-color: #F7F7F7;
    border: 1px solid #e3e3e3;
    margin: 0 auto;
}
.content_button_div{
    float: left;
    height: 50px;
    line-height: 50px;
    margin-left: 10px;
}
.content_button_right{
    float: right;
    line-height: 50px;
}