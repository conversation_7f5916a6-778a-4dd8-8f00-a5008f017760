package com.xyy.ec.pop.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.order.backend.pop.dto.PopOrderRefundExpressBusinessDto;
import com.xyy.ec.pop.vo.RefundOrderDetailAdminVo;
import com.xyy.ec.pop.vo.RefundOrderParamVo;

/**
 * @Description 退款单
 * <AUTHOR>
 */
public interface PopRefundOrderService {
    PageInfo<RefundOrderDetailAdminVo> queryRefundOrderPage(int pageNum, int limit, RefundOrderParamVo paramVo);

    PopOrderRefundExpressBusinessDto queryRefundExpressByOrderRefundId(Long orderRefundId);

    Boolean refundOrder(Long refundId,Integer operateType, String currentUser,String evidenceImages);

}
