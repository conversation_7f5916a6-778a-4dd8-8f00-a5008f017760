package com.xyy.ec.pop.adapter;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.order.business.api.OrderDeliveryBusinessApi;
import com.xyy.ec.order.business.dto.OrderDeliveryBusinessDto;
import com.xyy.ec.pop.adapter.dto.order.OrderDeliveryAdapterDto;
import com.xyy.ec.pop.adapter.helper.YbmOrderAdapterHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/21 20:35
* @table
*/
@Slf4j
@Component
public class OrderAdapter {

    @Reference
    private OrderDeliveryBusinessApi orderDeliveryBusinessApi;


    public List<OrderDeliveryAdapterDto> getOrderDeliveryMessageList(String orderNo){
        log.info("OrderAdapter getOrderDeliveryMessageList request orderNo:{}",orderNo);
        try {
            List<OrderDeliveryBusinessDto> orderDeliveryMessageList = orderDeliveryBusinessApi.getOrderDeliveryMessageList(orderNo);
            log.info("OrderAdapter getOrderDeliveryMessageList response orderNo:{} orderDeliveryMessageList:{}",orderNo, JSON.toJSONString(orderDeliveryMessageList));
            if(CollectionUtils.isEmpty(orderDeliveryMessageList)){
                return Lists.newArrayList();
            }
            return YbmOrderAdapterHelper.convertOrderDeliveryAdapterDto(orderDeliveryMessageList);
        } catch (Exception e) {
            log.error("OrderAdapter getOrderDeliveryMessageList error orderNo:{}",orderNo,e);
            return Lists.newArrayList();
        }
    }

}
