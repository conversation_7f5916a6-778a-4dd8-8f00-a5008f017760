package com.xyy.ec.pop.helper;

import com.google.common.collect.Lists;
import com.xyy.ec.order.search.api.remote.dto.OrderRefundSearchQueryDto;
import com.xyy.ec.order.search.api.remote.dto.OrderSearchQueryDto;
import com.xyy.ec.order.search.api.remote.enums.OrderRefundAuditStateEnum;
import com.xyy.ec.pop.server.api.order.enums.OrderExceptionTypeEnum;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.RefundOrderParamVo;
import com.xyy.ec.pop.vo.afterSales.AfterSaleQueryParamVo;
import com.xyy.ec.pop.vo.order.OrderAdminVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class OrderEsVoHelper {
    public static OrderRefundSearchQueryDto convertAfterSalesParam2SearchQueryDto(AfterSaleQueryParamVo param) {
        OrderRefundSearchQueryDto queryDto = new OrderRefundSearchQueryDto();
        queryDto.setOrgId(param.getOrgId());
        queryDto.setDocType(2);
        queryDto.setPopOrderId(StringUtils.isEmpty(param.getPopOrderId())?null:Long.valueOf(param.getPopOrderId()));
        queryDto.setMerchantName(StringUtils.isEmpty(param.getMerchantName())?null:param.getMerchantName());
        queryDto.setOrderNo(StringUtils.isEmpty(param.getOrderNo())?null:param.getOrderNo());
        queryDto.setMerchantErpCode(StringUtils.isEmpty(param.getCustomerErpCode())?null:param.getCustomerErpCode());
        queryDto.setStartRefundCreateTime(Objects.nonNull(param.getStartCreateTime()) ? new Date(param.getStartCreateTime()) : null);
        queryDto.setEndRefundCreateTime(Objects.nonNull(param.getEndCreateTime()) ? new Date(param.getEndCreateTime()) : null);
        if (param.getProvinceCode() != null) {
            queryDto.setCompanyProvinceCode(param.getProvinceCode());
        }
        queryDto.setRefundOrderNo(StringUtils.isEmpty(param.getAfterSalesNo())?null:param.getAfterSalesNo());
        //处理状态
        if (CollectionUtils.isNotEmpty(param.getAuditProcessStates())) {
            queryDto.setAuditProcessStates(param.getAuditProcessStates());
        }
        queryDto.setCompanyName(param.getCompanyName());
        queryDto.setAfterSalesType(param.getAfterSalesType());
        queryDto.setSubType(param.getSubType());
        queryDto.setSellerRemarks(StringUtils.isEmpty(param.getRemark())?null:param.getRemark());
//        queryDto.setse
//        if (StringUtils.isNotBlank(auditStateListJson)) {
//            List<Integer> auditStates = JSON.parseArray(auditStateListJson, Integer.class);
//            queryDto.setAuditProcessStates();
//        }
        queryDto.setPageNo(param.getPageNo());
        queryDto.setPageSize(param.getPageSize());

        return queryDto;
    }
    public static OrderSearchQueryDto convertOrderToOrderSearchQueryDto(OrderAdminVo param,Integer pageNum,Integer pageSize){
        OrderSearchQueryDto orderSearchQueryDto = new OrderSearchQueryDto();
        orderSearchQueryDto.setOrderNo(param.getOrderNo());
        orderSearchQueryDto.setOrgId(param.getCorporationNo());
        orderSearchQueryDto.setMobile(param.getMobile());
        orderSearchQueryDto.setCompanyName(param.getCompanyName());
        orderSearchQueryDto.setName(param.getCorporationName());
        orderSearchQueryDto.setStartCreateTime(param.getStartCreateTime());
        orderSearchQueryDto.setEndCreateTime(param.getEndCreateTime());
        orderSearchQueryDto.setOrderStatus(param.getStatus());
        orderSearchQueryDto.setProvinceCode(param.getProvinceCode());
//        orderSearchQueryDto.setMerchantIds(param.getMerchantIdList());
        orderSearchQueryDto.setCompanyProvinceCode(Objects.nonNull(param.getProvId())?param.getProvId().intValue():null);
        orderSearchQueryDto.setFirstOrderFlag(param.getFirstOrderFlag());
        orderSearchQueryDto.setOrderChannelSearch(param.getOrderType());
        orderSearchQueryDto.setPayChannel(param.getPayChannel());
        orderSearchQueryDto.setPayType(param.getPayType());
        orderSearchQueryDto.setEvidenceVerifyStatus(param.getEvidenceVerifyStatus());
        orderSearchQueryDto.setOrderSyncStatus(param.getOrderSyncStatus());
        orderSearchQueryDto.setStartPayTime(param.getStartPayTime());
        orderSearchQueryDto.setEndPayTime(param.getEndPayTime());
        orderSearchQueryDto.setStartFinishTime(param.getStartFinishTime());
        orderSearchQueryDto.setEndFinishTime(param.getEndFinishTime());
        orderSearchQueryDto.setMerchantName(param.getMerchantName());
        orderSearchQueryDto.setIsVirtualSupplier(param.getIsVirtualSupplier());
        orderSearchQueryDto.setBusinessTypeSearch(param.getBusinessTypeSearch());
        orderSearchQueryDto.setIsFbp(param.getIsFbp());
        orderSearchQueryDto.setIsRandom(param.getIsRandom());
        orderSearchQueryDto.setNextDayDelivery(param.getNextDayDelivery());
        /**
         * 物流轨迹状态  1查轨迹获取失败，2轨迹数据不合法
         */
        orderSearchQueryDto.setDeliveryStatus(param.getLogisticsTrackFail()!=null&&param.getLogisticsTrackFail()?Integer.valueOf(1):
                param.getLogisticsTrackIllegal()!=null&&param.getLogisticsTrackIllegal()?Integer.valueOf(2):null);
        if (StringUtils.isNotEmpty(param.getIsHighGrossStr())){
            String[] split = param.getIsHighGrossStr().split(",");
            List<Integer> isHighGrosses = Lists.newArrayList();
            for (String  s:split) {
                isHighGrosses.add(Integer.valueOf(s));
            }
            orderSearchQueryDto.setIsHighGrosses(isHighGrosses);
        }

        orderSearchQueryDto.setPageNo(pageNum);
        orderSearchQueryDto.setPageSize(pageSize);

        if (Objects.nonNull(param.getTimeoutHours())){
            orderSearchQueryDto.setEndPayTime(DateUtil.getDate(-param.getTimeoutHours(), Calendar.HOUR));
        }
        if(StringUtils.isNotEmpty(param.getBranchCode())){
            orderSearchQueryDto.setBranchCode(param.getBranchCode());
        }
        if (param.getExceptionFlag() == null){
            return orderSearchQueryDto;
        }
        if (param.getExceptionFlag() == 0){
            orderSearchQueryDto.setLicenseExceptions(Collections.singletonList(OrderExceptionTypeEnum.NONE_LICENSE_EXCEPTION.getType()));
        }
        if (param.getExceptionFlag() == 1){
            orderSearchQueryDto.setLicenseExceptions(com.google.common.collect.Lists.newArrayList(OrderExceptionTypeEnum.SYSTEM_LICENSE_EXCEPTION.getType(),OrderExceptionTypeEnum.SUPPLIER_LICENSE_EXCEPTION.getType()));
        }
        return orderSearchQueryDto;
    }

    public static OrderRefundSearchQueryDto convertRefundOrderToOrderRefundSearchQueryDto(RefundOrderParamVo param,int pageNum, int pageSize) {
        OrderRefundSearchQueryDto queryDto = new OrderRefundSearchQueryDto();
        queryDto.setOrgId(param.getCorporationNo());
        queryDto.setPayType(param.getPayType());
        if (Objects.equals(OrderRefundAuditStateEnum.PROCESS_SUCCESS.getValue(),param.getAuditState())){
            queryDto.setOrderRefundAuditStates(Arrays.asList(OrderRefundAuditStateEnum.WHIT_FINANCE.getValue(),OrderRefundAuditStateEnum.WHIT_FINANCE_PAY.getValue()));
        }else {
            queryDto.setOrderRefundAuditState(param.getAuditState());
        }
        queryDto.setCompanyProvinceCode(Objects.nonNull(param.getProvId())?param.getProvId().intValue():null);
        queryDto.setProvinceCode(param.getProvinceCode());
        queryDto.setCompanyName(param.getCompanyName());
        queryDto.setName(param.getCorporationName());
        queryDto.setRefundOrderNo(param.getRefundOrderNo());
        queryDto.setStartRefundCreateTime(param.getStartCreateTime());
        queryDto.setEndRefundCreateTime(param.getEndCreateTime());
        queryDto.setMerchantName(param.getMerchantName());
        queryDto.setStartOrderCreateTime(param.getStartTime());
        queryDto.setEndOrderCreateTime(param.getEndTime());
        queryDto.setRefundChannel(param.getRefundChannel());
        queryDto.setRefundReason(param.getRefundReason());
        queryDto.setPayChannel(param.getPayChannel());
        queryDto.setMerchantName(param.getMerchantName());
//        queryDto.setIsThirdCompany(1);
        queryDto.setIsThirdCompany(param.getIsThirdCompany());
        queryDto.setOrderNo(param.getOrderNo());
        queryDto.setStartRefundAuditTime(param.getStartRefundFinishTime());
        queryDto.setEndRefundAuditTime(param.getEndRefundFinishTime());
        queryDto.setIsFbp(param.getIsFbp());
        queryDto.setPayStatus(param.getPayStatus());
        queryDto.setPageNo(pageNum);
        queryDto.setPageSize(pageSize);
        return queryDto;
    }
}
