package com.xyy.ec.pop.utils;

import org.slf4j.helpers.FormattingTuple;
import org.slf4j.helpers.MessageFormatter;

import java.util.Arrays;
import java.util.List;

/**
 * 字符串工具类
 * @ClassName: StringUtil 
 * <AUTHOR>
 * @date 2016-5-22 下午8:40:12
 */
public class StringUtil {
	/**
	 * 是否为空
	 * @Title: isEmpty
	 * @param str
	 * @return
	 * boolean
	 * <AUTHOR> 
	 * @date 2016-5-22 下午8:40:21
	 */
	public static boolean isEmpty(String str){
        return str == null || str.length() == 0;
    }

	/**
	 * 判断Integer是否为空
	 */
	public static boolean isEmpty(Integer str){
		return str == null || str == 0;
	}
	/**
	 * 是否不为空
	 * @Title: isNotEmpty
	 * @param str
	 * @return
	 * boolean
	 * <AUTHOR> 
	 * @date 2016-5-22 下午8:40:30
	 */
	public static boolean isNotEmpty(String str){
        return !isEmpty(str);
    }
	/**
	 * 图片路径拼接
	 * @Title: ImgURL
	 * @param img
	 * @param path
	 * @return
	 * String
	 * <AUTHOR> 
	 * @date 2016-5-22 下午8:40:39
	 */
	public static String ImgURL(String img,String path){
		String retuenStr="";
		if(isNotEmpty(img)){
			retuenStr=(path+img);
		}
		return retuenStr;
	}
	
	/**
	 * 获取真实的文件名称
	 * @Title: realImgURL
	 * @param img
	 * @return
	 * String
	 * <AUTHOR> 
	 * @date 2016-8-17 下午1:57:32
	 */
	public static String realImgURL(String img,String path){
		String retuenStr="";
		if(isNotEmpty(img) && isNotEmpty(path)){
			retuenStr=img.substring(path.length());
		}
		return retuenStr;
	}
	/**
	 * 拆分多个图片返回图片对应的完整路径数组
	 * @Title: splitImgURL
	 * @param string
	 * @param path
	 * @return
	 * String[]
	 * <AUTHOR> 
	 * @date 2016-5-24 下午5:40:23
	 */
	public static String[] splitImgURL(String img,String path){
		String[] a = {};
		if(isNotEmpty(img)){
			a = img.split(",");
			if(isNotEmpty(path)){
				for (int i = 0; i < a.length; i++) {
					a[i]=path+a[i];
				}        	
			}			
		}
		return a;
	}
	
	/**
	 * String 分割
	 * @Title: splitStringToString
	 * @param string	需要分割的字符串
	 * @param split   分隔符
	 * @return
	 * String[]
	 * <AUTHOR> 
	 * @date 2016-7-17 下午10:42:47
	 */
	public static String[] splitStringToString(String string,String split){
		String[] a= {};
		if(isNotEmpty(string)){
			a = string.split(split);
		}
		return a;
	}
	/**
	 * String格式化成Integer数组
	 * @Title: splitStringToInt
	 * @param string	要分割的字符串
	 * @param split		分隔符
	 * @return
	 * Integer[]
	 * <AUTHOR> 
	 * @date 2016-7-17 下午10:44:48
	 */
	public static Integer[] splitStringToInt(String string,String split){
		String[] a = {};
		if(isNotEmpty(string)){
			a = string.split(split);
		}
		Integer[] i = new Integer[a.length];
		for (int j = 0; j < a.length; j++) {
			if(isNotEmpty(a[j])) {
				i[j]=Integer.valueOf(a[j]);
			}
		}
		return i;
	}
	
	/**
	 * String格式化成Double数组
	 * @Title: splitStringToDouble
	 * @param string	要分割的字符串
	 * @param split		分隔符
	 * @return
	 * Double[]
	 * <AUTHOR> 
	 * @date 2016-7-17 下午10:49:43
	 */
	public static Double[] splitStringToDouble(String string,String split){
		String[] a = {};
		if(isNotEmpty(string)){
			a = string.split(split);
		}
		Double[] d = new Double[a.length];
		for (int j = 0; j < a.length; j++) {
			if(isNotEmpty(a[j])) {
				d[j]=Double.valueOf(a[j]);
			}
		}
		return d;
	}
	
	/**
	 * 分割字符串转为Long
	 * @Title: splitStringToLong
	 * @param string
	 * @param split
	 * @return
	 * Long[]
	 * <AUTHOR> 
	 * @date 2017-1-14 下午10:58:11
	 */
	public static Long[] splitStringToLong(String string,String split){
		String[] a = {};
		if(isNotEmpty(string)){
			a = string.split(split);
		}
		Long[] d = new Long[a.length];
		for (int j = 0; j < a.length; j++) {
			if(isNotEmpty(a[j])) {
				d[j]=Long.valueOf(a[j]);
			}
		}
		return d;
	}
	
	/**
	 * 按指定符号分割字符串
	 * @Title: stringToList
	 * @param text
	 * @param character
	 * @return
	 * List<String>
	 * <AUTHOR> 
	 * @date 2017-1-13 上午12:21:22
	 */
    public static List<String> stringToList(String text,String character) {
        if (StringUtil.isNotEmpty(text)) {
            String[] split = text.split(character);
            return Arrays.asList(split);
        }
        return null;
    }
	
    /**
     * 判断两个字符串是否相等
     * @Title: equals
     * @param str1
     * @param str2
     * @return
     * boolean
     * <AUTHOR> 
     * @date 2017-1-15 下午9:56:01
     */
    public static boolean equals(String str1, String str2) {
        return str1 == null ? str2 == null : str1.equals(str2);
    }
    
    /**
     * 两个字符串不相等
     * @Title: noEquals
     * @param str1
     * @param str2
     * @return
     * boolean
     * <AUTHOR> 
     * @date 2017-1-15 下午9:58:40
     */
    public static boolean noEquals(String str1, String str2) {
        return !equals(str1,str2);
    }
    
    public static String removeParameter(String queryString, String parameter) {
        StringBuffer buffer = new StringBuffer();
        if (isEmpty(queryString) || isEmpty(parameter)) {
            return queryString;
        }

        String[] parameterPairs = queryString.split("&");
        if (CollectionUtil.isEmpty(parameterPairs)) {
            return "";
        }

        for (int i = 0; i < parameterPairs.length; i++) {
            String[] kvs = parameterPairs[i].split("=");
            if (CollectionUtil.isEmpty(kvs)) {
                continue;
            }
            if (kvs[0].equals(parameter)) continue;
            buffer.append(kvs[0]);
            if (kvs.length == 2) buffer.append("=").append(kvs[1]);
            buffer.append("&");
        }
        if (buffer.length() > 0) buffer.deleteCharAt(buffer.length() - 1);
        return buffer.toString();
    }
    
    /**
     * 判断某一个字符串中是否包含数组中的元素
     * @Title: isIn
     * @param str
     * @param array
     * @return
     * boolean
     * <AUTHOR> 
     * @date 2017-2-4 下午4:03:30
     */
    public static boolean isIn(String str,String[] array){
    	boolean flag=false;
    	if(StringUtil.isEmpty(str) || CollectionUtil.isEmpty(array)){
    		return flag;
    	}
    	for (String element : array) {
			if(str.indexOf(element)!=-1){
				flag=true;
				break;
			}
		}
    	return flag;
    }
    
    /**
     * 获取系统
     * @Title: line
     * @return
     * String
     * <AUTHOR> 
     * @date 2017-4-21 下午12:17:04
     */
    public static String line() {
        return System.getProperty("line.separator");
    }
    /**
     * 字符数组转换为hashcode
     * @Title: hashCodeOfStringArray
     * @param stringArray
     * @return
     * int
     * <AUTHOR> 
     * @date 2017-4-21 下午12:22:01
     */
    public static int hashCodeOfStringArray(String[] stringArray) {
        if (CollectionUtil.isEmpty(stringArray)) {
            return 0;
        }
        int hashCode = 17;
        for (int i = 0; i < stringArray.length; i++) {
            String value = stringArray[i];
            hashCode = hashCode * 31 + (value == null ? 0 : value.hashCode());
        }
        return hashCode;
    }
    
    /**
     * 字符串去掉所有空格
     * @param str
     * @return
     */
    public static String trimCharacterString(String str){
    	if(isEmpty(str)){
    		return null;
    	}
    	return str.replace(" ", "");
    }
    
    /**
     * 获取常量池字符串唯一值
     * @Title: buildLock
     * @param prefix
     * @param suffix
     * @return
     * String
     * <AUTHOR> 
     * @date 2017年8月29日 下午2:18:55
     */
    public static String buildLock(String prefix,Object suffix) {
    	StringBuilder sb = new StringBuilder();
    	sb.append(prefix);
    	sb.append(suffix);
    	return sb.toString().intern();
    }


	/**
	 * 判断是否空白
	 * @param str
	 * @return
	 */
	public static boolean isBlank(String str) {
		int strLen;
		if ((str == null) || ((strLen = str.length()) == 0))
			return true;
		for (int i = 0; i < strLen; i++) {
			if (!Character.isWhitespace(str.charAt(i))) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 判断是否不是空白
	 * @param str
	 * @return
	 */
	public static boolean isNotBlank(String str) {
		return !isBlank(str);
	}

	/**
	 * 判断多个字符串全部是否为空
	 * @param strings
	 * @return
	 */
	public static boolean isAllEmpty(String... strings) {
		if (strings == null) {
			return true;
		}
		for (String str : strings) {
			if (isNotEmpty(str)) {
				return false;
			}
		}
		return true;
	}

	/***
	 * 字符串格式化方法
	 * @param messagePattern
	 * @param args
	 * @return
	 */
	public static String format(String messagePattern,Object...args) {
		FormattingTuple formattingTuple = MessageFormatter.arrayFormat(messagePattern, args);
		return formattingTuple.getMessage();

	}

	public static void main(String[] args) {
		System.out.println(hashCodeOfStringArray(new String[]{"a","b","c"}));
		System.out.println(hashCodeOfStringArray(new String[]{"a"}));
		System.out.println(hashCodeOfStringArray(new String[]{"b"}));
		System.out.println(hashCodeOfStringArray(new String[]{"c"}));

	}
}
