package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.server.api.merchant.dto.PopSupplierUserDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCorporationDto;
import com.xyy.ec.pop.vo.SupplierUserVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/08/16/13:00
 * @Description:
 */
public class SupplierUserHelper {
    private static SupplierUserVo dto2vo(PopSupplierUserDto dto) {
        if (dto == null) {
            return null;
        }
        SupplierUserVo supplierUserVo = new SupplierUserVo();
        supplierUserVo.setOrgId(dto.getOrganizationId());
        supplierUserVo.setUserName(dto.getUserName());
        supplierUserVo.setUserPassword(dto.getPassword());
        return supplierUserVo;
    }

    public static List<SupplierUserVo> dtos2vos(List<PopSupplierUserDto> dtos, Map<String, PopCorporationDto> stringPopCorporationDtoMap) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Lists.newArrayList();
        }
        List<SupplierUserVo> vos = Lists.newArrayList();
        dtos.forEach(m -> {
            SupplierUserVo supplierUserVo = dto2vo(m);
            if (supplierUserVo != null) {
                PopCorporationDto popCorporationDto = stringPopCorporationDtoMap.get(m.getOrganizationId());
                supplierUserVo.setOrgName(popCorporationDto == null ? "" : popCorporationDto.getCompanyName());
                vos.add(supplierUserVo);
            }
        });
        return vos;
    }
}
