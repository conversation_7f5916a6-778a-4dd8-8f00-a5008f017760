package com.xyy.ec.pop.service.impl;

import com.xyy.ec.pop.remote.PopBankRemote;
import com.xyy.ec.pop.remote.PopCashAdvanceRemote;
import com.xyy.ec.pop.remote.PopCorporationToCorTransferRemote;
import com.xyy.ec.pop.server.api.bank.dto.BankAccountDto;
import com.xyy.ec.pop.server.api.merchant.api.enums.ToCorTransferToEnum;
import com.xyy.ec.pop.server.api.merchant.dto.PopCorporationToCorTransferDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceDto;
import com.xyy.ec.pop.service.PopCorporationToCorTransferService;
import com.xyy.ec.pop.utils.StringUtil;
import com.xyy.ec.pop.vo.PopCorporationToCorTransferVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class PopCorporationToCorTransferServiceImpl implements PopCorporationToCorTransferService {
    @Autowired
    private PopCorporationToCorTransferRemote toCorTransferRemote;
    @Autowired
    private PopBankRemote popBankRemote;
    @Autowired
    private PopCashAdvanceRemote popCashAdvanceRemote;
    @Override
    public PopCorporationToCorTransferVo getTransferVo(String orgId) {
        PopCorporationToCorTransferDto transferDto = toCorTransferRemote.getTransferWithDefaultBankInfo(orgId);
        if(transferDto==null){
            transferDto  = initTransferDto(orgId);
        }
        PopCorporationToCorTransferVo vo = convertToVo(transferDto);
        fillBankInfo(vo);
        return vo;
    }



    @Override
    public boolean updateToCorTransfer(PopCorporationToCorTransferVo transferVo, String username) {
        valid(transferVo);
        reSetValue(transferVo,username);
        PopCorporationToCorTransferDto dto = covertToDto(transferVo);
        return toCorTransferRemote.updateToCorTransfer(dto);
    }



    /**
     * 重置不可修改字段
     * @param vo
     * @param username
     */
    private void reSetValue(PopCorporationToCorTransferVo vo, String username) {
        vo.setCreateName(username);
        vo.setUpdateName(username);
        if(vo.getTransferTo()==ToCorTransferToEnum.CORPORATION.getCode()){
            return;
        }
        vo.setAccountBank(null);
        vo.setAccountName(null);
        vo.setAccountNum(null);
    }

    private void valid(PopCorporationToCorTransferVo vo) {
        Assert.hasText(vo.getOrgId(),"机构信息不能为空");
        ToCorTransferToEnum.valid(vo.getTransferTo());
        if(vo.getTransferTo()==ToCorTransferToEnum.PLATFORM.getCode()){
            return;
        }
        Assert.hasText(vo.getAccountBank(),"请输入商业开户银行信息");
        Assert.hasText(vo.getAccountSubBank(),"请输入商业开户银行对应的支行信息");
        Assert.hasText(vo.getAccountName(),"公司名称不能为空");
        if (vo.getAccountSubBank().length() > 300) {
            throw new IllegalArgumentException("商业开户银行对应的支行最多输入300字符");
        }
        Assert.hasText(vo.getAccountNum(),"银行账号不能为空");
    }

    /**
     * 根据富民开户信息返回开户行等信息
     * @param vo
     * @return
     */
    private void fillBankInfo(PopCorporationToCorTransferVo vo) {
        if(StringUtil.isNotEmpty(vo.getAccountName())){
            return;
        }
        BankAccountDto accountDto = popBankRemote.queryOpenedBankAccountByOrgId(vo.getOrgId());
        if(accountDto!=null){
            vo.setAccountName(accountDto.getRegisteredName());
            vo.setAccountNum(accountDto.getRegisteredBankAccount());
            vo.setAccountBank(accountDto.getBankName()+accountDto.getSubBankName());
            return;
        }
        PopCashAdvanceDto lastCashAdvance = popCashAdvanceRemote.getLastCashAdvance(vo.getOrgId());
        if(lastCashAdvance!=null){
            vo.setAccountName(lastCashAdvance.getAccountName());
            vo.setAccountNum(lastCashAdvance.getAccountNum());
            vo.setAccountBank(lastCashAdvance.getAccountBank());
        }
    }

    private PopCorporationToCorTransferDto initTransferDto(String orgId) {
        PopCorporationToCorTransferDto toCorTransferDto = new PopCorporationToCorTransferDto();
        toCorTransferDto.setOrgId(orgId);
        toCorTransferDto.setTransferTo(ToCorTransferToEnum.NONE.getCode());
        return toCorTransferDto;
    }

    private PopCorporationToCorTransferVo convertToVo(PopCorporationToCorTransferDto transferDto) {
        PopCorporationToCorTransferVo popCorporationToCorTransferVo = new PopCorporationToCorTransferVo();
        popCorporationToCorTransferVo.setId(transferDto.getId());
        popCorporationToCorTransferVo.setOrgId(transferDto.getOrgId());
        popCorporationToCorTransferVo.setTransferTo(transferDto.getTransferTo());
        popCorporationToCorTransferVo.setAccountName(transferDto.getAccountName());
        popCorporationToCorTransferVo.setAccountNum(transferDto.getAccountNum());
        popCorporationToCorTransferVo.setAccountBank(transferDto.getAccountBank());
        popCorporationToCorTransferVo.setAccountSubBank(transferDto.getAccountSubBank());
        popCorporationToCorTransferVo.setCreateTime(transferDto.getCreateTime());
        popCorporationToCorTransferVo.setCreateName(transferDto.getCreateName());
        popCorporationToCorTransferVo.setUpdateName(transferDto.getUpdateName());
        popCorporationToCorTransferVo.setUpdateTime(transferDto.getUpdateTime());
        return popCorporationToCorTransferVo;

    }

    private PopCorporationToCorTransferDto covertToDto(PopCorporationToCorTransferVo transferVo) {
        PopCorporationToCorTransferDto popCorporationToCorTransferDto = new PopCorporationToCorTransferDto();
        popCorporationToCorTransferDto.setId(transferVo.getId());
        popCorporationToCorTransferDto.setOrgId(transferVo.getOrgId());
        popCorporationToCorTransferDto.setTransferTo(transferVo.getTransferTo());
        popCorporationToCorTransferDto.setAccountName(transferVo.getAccountName());
        popCorporationToCorTransferDto.setAccountNum(transferVo.getAccountNum());
        popCorporationToCorTransferDto.setAccountBank(transferVo.getAccountBank());
        popCorporationToCorTransferDto.setAccountSubBank(transferVo.getAccountSubBank());
        popCorporationToCorTransferDto.setCreateName(transferVo.getCreateName());
        popCorporationToCorTransferDto.setUpdateName(transferVo.getUpdateName());
        return popCorporationToCorTransferDto;

    }
}
