package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/10/7 10:21
 */
public enum ReceiveAuditTypeEnum {

    PREPAYMENT(1,"预收款审核"),
    PAYMENT(2,"收款审核");

    private  int id;
    private  String value;

    ReceiveAuditTypeEnum(int id, String value){
        this.id = id;
        this.value = value;
    }
    private static Map<Integer, ReceiveAuditTypeEnum> receiveAuditTypeMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(ReceiveAuditTypeEnum control : ReceiveAuditTypeEnum.values()) {
            receiveAuditTypeMaps.put(control.getId(), control);
            maps.put(control.getId(),control.getValue());
        }
    }
    public static String get(int id) {
        return receiveAuditTypeMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
