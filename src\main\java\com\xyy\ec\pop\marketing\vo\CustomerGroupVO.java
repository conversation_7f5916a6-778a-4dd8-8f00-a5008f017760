package com.xyy.ec.pop.marketing.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/***
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomerGroupVO implements Serializable {

    private static final long serialVersionUID = -7849558407038537854L;

    private Long id;

    /**
     * 人群名称
     */
    private String tagName;

    /**
     * 人群定义
     *
     * @deprecated 过时。使用{@link #contentBundleDescriptions} 和 {@link #specifyUserDescription}代替。
     */
    @Deprecated
    private List<String> tagDef;

    /**
     * 人群内容的组描述列表。其中数组元素List 对应原来的tagDef。
     */
    private List<List<String>> contentBundleDescriptions;

    /**
     * 人群内容的指定用户描述
     */
    private String specifyUserDescription;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间字符串，格式yyyy-MM-dd HH:mm:ss
     */
    private String createTimeStr;
}
