package com.xyy.ec.pop.utils;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;

import static com.alibaba.com.caucho.hessian.io.BasicSerializer.STRING;
import static java.sql.Types.NUMERIC;
import static org.apache.poi.ss.usermodel.DataValidationConstraint.ValidationType.FORMULA;

/**
 * Excel辅助工具类
 * 
 * @ClassName: ExcelCommonUtil
 * <AUTHOR>
 * @date 2017-3-21 上午11:07:46
 */
public class ExcelCommonUtil {
	private static Logger LOGGER = LoggerFactory.getLogger(ExcelCommonUtil.class);

	/**
	 * 判断是否为老版本的excel
	 * 
	 * @Title: isExcel2003
	 * @param filePath
	 *            *.xls
	 * @return boolean
	 * <AUTHOR>
	 * @date 2017-3-21 上午11:05:19
	 */
	public static boolean isExcel2003(String filePath) {
		return StringUtil.isNotEmpty(filePath) && filePath.matches("^.+\\.(?i)(xls)$");
	}

	/**
	 * 判断是否为新版本的excel
	 * 
	 * @Title: isExcel2007
	 * @param filePath
	 *            *.xlsx
	 * @return boolean
	 * <AUTHOR>
	 * @date 2017-3-21 上午11:05:43
	 */
	public static boolean isExcel2007(String filePath) {
		return StringUtil.isNotEmpty(filePath) && filePath.matches("^.+\\.(?i)(xlsx)$");
	}

	/**
	 * 根据单元格获取内容
	 * 
	 * @Title: getCellFormatValue
	 * @param cell
	 * @return String
	 * <AUTHOR>
	 * @date 2017-3-10 下午10:19:04
	 */
	public static String getCellFormatValue(Cell cell) {
		String result = "";
		if (cell == null) {
			return result;
		}

//		switch (cell.getCellType()) {
	switch (cell.getCellTypeEnum()) {
		case STRING:
			result = cell.getRichStringCellValue().getString();
			break;
		case NUMERIC:
			if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
				Date date = cell.getDateCellValue();
				result = DateUtil.date2String(date, "yyyy-MM-dd");
			} else {
				BigDecimal cellValue = new BigDecimal(String.valueOf(cell.getNumericCellValue()));
				cellValue = cellValue.stripTrailingZeros();
				result = cellValue.toPlainString();
			}
			break;
		case FORMULA:
			result = String.valueOf(cell.getCellFormula());
			break;
		default:
			result = "";
			break;
		}
		return result;
	}
	
	
	/**
	 * 根据单元格获取内容
	 * 
	 * @Title: getCellFormatValue
	 * @param cell
	 * @return String
	 * <AUTHOR>
	 * @date 2017-3-10 下午10:19:04
	 */
	public static Object getCellValue(Cell cell,boolean isForceText) {
		Object result = null;
		if (cell == null) {
			return result;
		}
		//是否强制性走文本
		if(isForceText){
			cell.setCellType(CellType.STRING);
			return cell.getRichStringCellValue().getString();
		}

		//		switch (cell.getCellType()) {
		switch (cell.getCellTypeEnum()) {
		case STRING:
			result = cell.getRichStringCellValue().getString();
			break;
		case NUMERIC:
			if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
				Date date = cell.getDateCellValue();
				result = DateUtil.date2String(date, "yyyy-MM-dd");
			} else {
				result = new BigDecimal(String.valueOf(cell.getNumericCellValue()));
			}
			break;
		case FORMULA:
			result = String.valueOf(cell.getCellFormula());
			break;
		default:
			result = null;
			break;
		}
		return result;
	}
}
