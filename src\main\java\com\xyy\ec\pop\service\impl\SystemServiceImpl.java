package com.xyy.ec.pop.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.redis.RedisConstants;
import com.xyy.ec.pop.redis.XyyJedisCluster;
import com.xyy.ec.pop.service.SsoService;
import com.xyy.ec.pop.service.SystemService;
import com.xyy.ec.pop.utils.Constants;
import com.xyy.ec.pop.utils.StringUtil;
import com.xyy.ec.pop.utils.cookie.CookieTool;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.login.Menu;
import com.xyy.ec.pop.vo.login.SystemMenu;
import com.xyy.ec.pop.vo.login.UserInfo;
import com.xyy.ec.pop.vo.zhongtai.PopProvinceVo;
import com.xyy.ec.pop.vo.zhongtai.ZhongTaiResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: SystemServiceImpl
 * @Package com.xyy.xpop.admin.service.impl
 * @Description: 系统权限
 * @date 2020/5/28 17:25
 */
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Service
public class SystemServiceImpl implements SystemService {

    private final SsoService ssoService;

    private final HttpServletRequest request;

    private final HttpServletResponse response;

    private final XyyJedisCluster redisApi;

    @Value("${system.session.timeout:24}")
    private Integer systemSessionTime;

    @Value("#{'${pop.menu.list:}'.split(',')}")
    private List<String> menus;


    /**
     * 用户登录
     *
     * @param username
     * @param password
     * @return
     */
    @Override
    public ResponseVo login(String username, String password) {
        log.info("SystemServiceImpl.login,用户登录：username：{}，password：{}", username, password);
        try {

            ResponseVo<UserInfo> ssoResponse = ssoService.login(username, password);
            if (ssoResponse.isFail()) {
                log.warn("SystemServiceImpl.login,用户登录失败：username：{}，password：{},异常信息：{}", username, password, ssoResponse.getMessage());
                return ResponseVo.errRest(ssoResponse.getMessage());
            }
            UserInfo userInfo = ssoResponse.getData();
            SysUser sysUser = convertUserDto(userInfo);

            //设置用户菜单缓存
            redisApi.del(RedisConstants.XYY_POP_HERD_LEFT_KEYS + sysUser.getId());
            List<SystemMenu> menuList = initNavigationBar(sysUser);
            if (CollectionUtils.isEmpty(menuList)) {
                return ResponseVo.errRest("您无该系统访问权限，请联系管理员");
            }

            //登录成功
            String sid = request.getSession().getId();
            // 设置cookie的有效期
            CookieTool.setCookie(response, Constants.SESSION_ID, sid, Constants.OUT_TIME_FOREVER);

            // 用户放入redis缓存
            redisApi.setexHour(sid, systemSessionTime, JSON.toJSONString(sysUser));


            return ResponseVo.successResultNotData();

        } catch (Exception e) {
            log.warn("#SystemServiceImpl.login#error,参数:username：{}，password：{}", username, password, e);
            return ResponseVo.errRest("登录失败");
        }


    }

    /**
     * 初始化菜单并放入redis缓存
     *
     * @param loginUser 登录用户
     * @return
     */
    @Override
    public List<SystemMenu> initNavigationBar(SysUser loginUser) {
        List<Menu> menus = ssoService.getUserMenuByOaId(loginUser.getOaId(), loginUser.getToken());
        if (CollectionUtils.isEmpty(menus)) {
            logout();
            return null;
        }

        List<SystemMenu> listResources = convertMenus(menus);
        String jsonMenu = JSON.toJSONString(listResources);
        redisApi.setex(RedisConstants.XYY_POP_HERD_LEFT_KEYS + loginUser.getId(), RedisConstants.XYY_POP_HERD_LEFT_KEYS_TIME, jsonMenu);
        return listResources;
    }

    /**
     * 获取菜单权限
     *
     * @return
     */
    @Override
    public List<SystemMenu> getNavigationBar(SysUser sysUser) {
        List<SystemMenu> listResources = Lists.newArrayList();
        String redisKey = RedisConstants.XYY_POP_HERD_LEFT_KEYS + sysUser.getId();
        if (redisApi.get(redisKey) != null) {
            listResources = redisApi.get(redisKey, ArrayList.class);
        } else {
            listResources = initNavigationBar(sysUser);
        }
        return listResources;
    }

    /**
     * 退出登录
     */
    @Override
    public void logout() {
        Cookie cookie = CookieTool
                .getCookie(getRequest(), Constants.SESSION_ID);
        if (cookie != null) {
            String sid = cookie.getValue();
            if (!StringUtils.isEmpty(sid)) {
                redisApi.del(sid);
            }
        }
        CookieTool.clear(request, response, Constants.SESSION_ID);
    }

    @Override
    public boolean mockLocalLogin(UserInfo userInfo) {
        SysUser sysUser = convertUserDto(userInfo);

        //设置用户菜单缓存
        redisApi.del(RedisConstants.XYY_POP_HERD_LEFT_KEYS + sysUser.getId());
        List<SystemMenu> menuList = initNavigationBar(sysUser);
        if (CollectionUtils.isEmpty(menuList)) {
            return false;
        }

        //登录成功
        String sid = request.getSession().getId();
        // 设置cookie的有效期
        CookieTool.setCookie(response, Constants.SESSION_ID, sid, Constants.OUT_TIME_FOREVER);

        // 用户放入redis缓存
        redisApi.setexHour(sid, systemSessionTime, JSON.toJSONString(sysUser));
        return true;
    }

    @Override
    public List<PopProvinceVo> getProvincialAuthority(SysUser sysUser) {
        String redisKey = RedisConstants.XYY_POP_PROVINCE_KEYS + sysUser.getOaId();
        String provinceStr = redisApi.get(redisKey);
        List<PopProvinceVo> popProvinceVos;
        if (StringUtils.isNotBlank(provinceStr)) {
            popProvinceVos = JSON.parseArray(provinceStr, PopProvinceVo.class);
        } else {
            popProvinceVos = initProvinces(sysUser);
        }
        return popProvinceVos;
    }

    /**
     * 获取菜单按钮权限
     *
     * @param sysUser 用户
     * @param menuUrl 菜单id
     * @return 返回按钮list
     */
    @Override
    public List<String> getButtons(SysUser sysUser, String menuUrl) {
        String key = String.format(RedisConstants.XYY_POP_BUTTON_KEYS, sysUser.getOaId(), menuUrl);
        String buttonStr = redisApi.get(key);
        List<String> buttons;
        if (StringUtils.isNotBlank(buttonStr)) {
            buttons = JSON.parseArray(buttonStr, String.class);
        } else {
            initButtons(sysUser);
            buttonStr = redisApi.get(key);
            buttons = JSON.parseArray(buttonStr, String.class);
        }
        return buttons;
    }

    public void initButtons(SysUser sysUser) {
        List<ZhongTaiResource> resources = ssoService.initButtons(sysUser.getToken());
        if(CollectionUtils.isEmpty(resources)){
            return;
        }
        List<ZhongTaiResource> menuResources = resources.stream().filter(resource -> Objects.nonNull(resource) && menus.contains(resource.getUrl()) && resource.getResType() == 1)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(menuResources)){
            return;
        }
        Map<Integer, List<String>> buttonMap = resources.stream().filter(resource -> Objects.nonNull(resource) && resource.getResType() == 2)
                .collect(Collectors.groupingBy(ZhongTaiResource::getParent, Collectors.mapping(ZhongTaiResource::getTitle, Collectors.toList())));
        for (ZhongTaiResource resource : menuResources) {
            List<String> buttons = buttonMap.getOrDefault(resource.getId(), Lists.newArrayList());
            String key = String.format(RedisConstants.XYY_POP_BUTTON_KEYS, sysUser.getOaId(), resource.getUrl());
            redisApi.setex(key, RedisConstants.XYY_POP_HERD_LEFT_KEYS_TIME, JSON.toJSONString(buttons));
        }
    }

    public List<PopProvinceVo> initProvinces(SysUser sysUser) {
        List<PopProvinceVo> popProvinceVos = ssoService.getProvinces(sysUser.getToken());
        redisApi.setex(RedisConstants.XYY_POP_PROVINCE_KEYS + sysUser.getOaId(), RedisConstants.XYY_POP_HERD_LEFT_KEYS_TIME, JSON.toJSONString(popProvinceVos));
        return popProvinceVos;
    }

    public HttpServletRequest getRequest() {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) ra).getRequest();
        return request;
    }

    private SysUser convertUserDto(UserInfo userInfo) {
        if (null == userInfo) {
            return null;
        }
        SysUser sysUser = new SysUser();
        sysUser.setToken(userInfo.getToken());
        sysUser.setId(userInfo.getId());
        sysUser.setUsername(StringUtil.isEmpty(userInfo.getUsername()) ? userInfo.getRealName() : userInfo.getUsername());
        sysUser.setPassword(userInfo.getPassword());
        sysUser.setEmail(userInfo.getEmail());
        sysUser.setRealName(userInfo.getRealName());
        sysUser.setJobNumber(userInfo.getStaffNum());
        sysUser.setOaId(userInfo.getOaId());
        sysUser.setToken(userInfo.getToken());

        return sysUser;
    }

    private List<SystemMenu> convertMenus(List<Menu> menus) {
        if (CollectionUtils.isEmpty(menus)) {
            return null;
        }
        List<SystemMenu> systemMenus = new ArrayList<>(menus.size());
        for (Menu menu : menus) {
            SystemMenu systemMenu = convertMenu(menu);
            systemMenus.add(systemMenu);
        }
        return systemMenus;
    }

    private SystemMenu convertMenu(Menu menu) {
        if (null == menu) {
            return null;
        }
        SystemMenu systemMenu = new SystemMenu();
        systemMenu.setResourceId(menu.getId());
        systemMenu.setParentId(menu.getParent());
        systemMenu.setResourceName(menu.getTitle());
        systemMenu.setResourcePath(menu.getUrl());
        if (!CollectionUtils.isEmpty(menu.getChildren())) {
            systemMenu.setChildrenResources(convertMenus(menu.getChildren()));
        }

        return systemMenu;
    }
}
