package com.xyy.ec.pop.enums;


/**
 * 对于表tb_xyy_ka_should_receive_flow_log 中的type字段定义的枚举类
 * 类型：1--下单增加应收，2--取消订单减少应收，3--客户退款减少应收
 */
public enum ReceiveFlowLogTypeEnum{
    ADD_ORDER("ADD_ORDER",1),CANCEL_ORDER("CANCEL_ORDER",2),CANCEL_REFUND("CANCEL_REFUND",3);
    private String name;
    private Integer key;
    ReceiveFlowLogTypeEnum(String name, Integer key){
        this.name = name;
        this.key = key;
    }

    /**
     * 通过name取值key的方法
     * @param name
     * @return
     */
    public static Integer getKey(String name){
        for (ReceiveFlowLogTypeEnum receiveFlowLogTypeEnum:ReceiveFlowLogTypeEnum.values()){
            if (receiveFlowLogTypeEnum.getName().equals(name.trim())){
                return receiveFlowLogTypeEnum.key;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }
};
