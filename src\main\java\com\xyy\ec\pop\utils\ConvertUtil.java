package com.xyy.ec.pop.utils;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: ConvertUtil
 * @Package com.xyy.ec.pop.util
 * @Description: 转换工具类
 * @date 2020/7/16 11:56
 */
public class ConvertUtil {
    /**
     * Page 转PageInfo
     *
     * @param source  原分页数据
     * @param convert 实体转换函数
     * @param <VO>    转换结果 vo
     * @param <DTO>   dto
     * @return
     */
    public static <VO, DTO> PageInfo<VO> pageDto2Vo(com.xyy.ec.order.backend.query.Page<DTO> source,
                                                    Function<DTO, VO> convert) {
        if (source != null) {
            Page<VO> dest = new Page<>(source.getCurrentPage(), source.getLimit());
            dest.setTotal(source.getTotal() == null ? 0L : source.getTotal().longValue());
            PageInfo<VO> result = new PageInfo<>(dest);
            if (CollectionUtils.isNotEmpty(source.getRows())) {
                ArrayList<VO> voList = new ArrayList<>();
                for (DTO dto : source.getRows()) {
                    voList.add(convert.apply(dto));
                }
                result.setList(voList);
            }
            return result;
        }
        return null;
    }

    /**
     * PageInfo 转换
     *
     * @param source  原分页数据
     * @param convert 实体转换函数
     * @param <VO>    转换结果 vo
     * @param <DTO>   dto
     * @return
     */
    public static <VO, DTO> PageInfo<VO> pageInfoDto2Vo(PageInfo<DTO> source,
                                                        Function<DTO, VO> convert) {
        if (source != null) {
            Page<VO> dest = new Page<>(source.getPageNum(), source.getPageSize());
            dest.setTotal(source.getTotal());
            PageInfo<VO> result = new PageInfo<>(dest);
            if (source.getList() != null && !source.getList().isEmpty()) {
                ArrayList<VO> voList = new ArrayList<>();
                for (DTO dto : source.getList()) {
                    voList.add(convert.apply(dto));
                }
                result.setList(voList);
            }
            return result;
        }
        return null;
    }
}
