package com.xyy.ec.pop.base;


import com.xyy.ec.pop.exception.ServiceException;

import java.util.List;

/**
 * 业务接口类
 * @ClassName: Service 
 * <AUTHOR>
 * @date 2016-4-10 下午2:21:06 
 * @param <T>
 */
public interface Service<T,P>{

	
	/**
	 * 动态添加实体信息
	 * @Title: insertSelective
	 * @param entity
	 * @return
	 * @throws ServiceException
	 * int
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:21:17
	 */
	public abstract int insertSelective(T entity) throws ServiceException;

	/**
	 * 根据主键删除实体
	 * @Title: deleteByPrimaryKey
	 * @param id
	 * @return
	 * @throws ServiceException
	 * int
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:21:30
	 */
	public abstract int deleteByPrimaryKey(P id) throws ServiceException;
	
	/**
	 * 根据id批量删除实体
	 * @Title: batchDeleteByIds
	 * @param ids
	 * @return
	 * @throws ServiceException
	 * int
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:21:40
	 */
	public abstract int batchDeleteByIds(List<P> ids) throws ServiceException;
	
	/**
	 * 动态更新实体
	 * @Title: updateByPrimaryKeySelective
	 * @param entity
	 * @return
	 * @throws ServiceException
	 * int
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:21:56
	 */
	public abstract int updateByPrimaryKeySelective(T entity) throws ServiceException;

	/**
	 * 通过主键查询实体
	 * @Title: selectByPrimaryKey
	 * @param id
	 * @return
	 * @throws ServiceException
	 * T
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:22:06
	 */
	public abstract T selectByPrimaryKey(P id) throws ServiceException;

	/**
	 * 分页查询实体信息
	 * @Title: selectPagedList
	 * @param page
	 * @param entity
	 * @return
	 * @throws ServiceException
	 * PageList<T>
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:22:17
	 */
	public abstract Page<T> selectPageList(Page<T> page, T entity) throws ServiceException;
	
	/**
	 * 查询所有实体(不分页)
	 * @Title: selectList
	 * @param entity
	 * @return
	 * @throws ServiceException
	 * List<T>
	 * <AUTHOR> 
	 * @date 2016-4-10 下午2:22:27
	 */
	public abstract List<T> selectList(T entity) throws ServiceException;
	
	/**
	 * 查询总数量
	 * @Title: selectCount
	 * @param entity
	 * @return
	 * @throws ServiceException
	 * int
	 * <AUTHOR> 
	 * @date 2016-9-6 上午11:16:15
	 */
	public abstract int selectCount(T entity) throws ServiceException;
}
