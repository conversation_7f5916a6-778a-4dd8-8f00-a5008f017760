package com.xyy.ec.pop.controller.mop;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.adapter.mop.CallingAdapter;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.common.condition.CallingLogSelectCondition;
import com.xyy.pop.mop.api.common.condition.TargetSelectCondition;
import com.xyy.pop.mop.api.remote.parameter.CallingLogPageParameter;
import com.xyy.pop.mop.api.remote.result.CallingLogPageDTO;
import com.xyy.pop.mop.api.remote.result.TargetBasicDTO;
import com.xyy.scm.constant.entity.Result;
import com.xyy.scm.constant.entity.pagination.Paging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 商家运营-拜访/陪访
 */
@Slf4j
@RequestMapping("/mop/calling")
@RestController
public class CallingController extends BaseController {
    @Autowired
    CallingAdapter callingAdapter;
    @Autowired
    private DownloadRemote downloadRemote;
    @RequestMapping("/queryPageLogByParam")
    public ResponseVo queryPageLogByParam(@RequestBody CallingLogSelectCondition callingLogSelectCondition) {
        return callingAdapter.queryPageLogByParam(callingLogSelectCondition);
    }
    @RequestMapping("/queryEnum")
    public ResponseVo queryEnum() {
        return callingAdapter.queryEnum();
    }

    /**
     * Page查询
     * @param param
     * @return
     */

    @PostMapping("/page")
    public ResponseVo<Paging<CallingLogPageDTO>> queryCallingLogPageByParam(@RequestBody CallingLogPageParameter param) {
        param.setAccountId(getUser().getJobNumber());
        return  callingAdapter.queryCallingLogPageByParam(param);
    }

    /**
     * 导出列表
     *
     * @param query
     */
    @PostMapping(value = "/export")
    public ResponseVo<Boolean> exportShippingReminders(@RequestBody CallingLogPageParameter query) {
        try {
            log.info("exportAfterSales:{}", JSON.toJSONString(query));
            query.setAccountId(getUser().getJobNumber());
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.CALLINGLOG_MANAGEMENT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("exportAfterSales result:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        } catch (Exception e) {
            log.error("exportAfterSales error:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

}
