package com.xyy.ec.pop.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class ImpExcelDataDTO<T> implements Serializable {

    /**
     * 内容
     */
    private List<T> dataList;

    // 表头
    Map<Integer, String> headMaps;

}
