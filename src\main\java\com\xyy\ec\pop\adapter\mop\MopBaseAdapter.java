package com.xyy.ec.pop.adapter.mop;

import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.scm.constant.entity.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Supplier;

/**
 * 基础适配器接口，定义了将远程结果转换为响应对象的方法。
 * <AUTHOR>
 * @date 2024年12月06日18:08:56
 */
public interface MopBaseAdapter {
    /**
     * 日志记录器。
     */
    Logger log = LoggerFactory.getLogger(MopBaseAdapter.class);

    /**
     * 将远程结果转换为响应对象。
     *
     * @param <T>    泛型类型，表示结果的数据类型
     * @param result 提供远程调用结果的Supplier
     * @return 包含结果数据的响应对象
     */
    default <T> ResponseVo<T> to(Supplier<Result<T>> result) {
        try {
            Result<T> r = result.get();
            if (r.isSuccess()) {
                return ResponseVo.successResult(r.getResult());
            }
            return ResponseVo.errRest(r.getMsg());
        } catch (Exception e) {
            log.error("Error occurred in MopBaseAdapter.to:", e);
        }
        return ResponseVo.errRest("系统异常");
    }
}
