package com.xyy.ec.pop.utils.cos;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

/**
 * @author: yangpeng3
 * @Description:
 * @date: 2022/4/22 11:25
 */
@Component
public class CosUploadPopUtils {

    private static Logger logger = LoggerFactory.getLogger(CosUploadPopUtils.class);

    @Autowired(required = false)
    private CosUploadPopConfig cosUploadPopConfig;
    private static CosUploadPopUtils cosUploadPopUtils;
    private static COSClient cosClient;

    @PostConstruct
    public void init() {
        cosUploadPopUtils = this;
        if (!checkParam()) {
            return;
        }
        COSCredentials cred = new BasicCOSCredentials(cosUploadPopConfig.getSecretId(), cosUploadPopConfig.getSecretKey());
        Region region = new Region(cosUploadPopConfig.getRegion());
        ClientConfig clientConfig = new ClientConfig(region);
        clientConfig.setHttpProtocol(HttpProtocol.https);
        clientConfig.setMaxErrorRetry(cosUploadPopConfig.getRetryCount() == null ? 3 : cosUploadPopConfig.getRetryCount());
        cosClient = new COSClient(cred, clientConfig);
        if (cosClient == null) {
            throw new RuntimeException("cosClient is Null");
        }
    }

    @PreDestroy
    public void destroy() {
        if (cosClient != null) {
            cosClient.shutdown();
        }
    }

    private static boolean checkParam() {
        if (StringUtils.isBlank(cosUploadPopUtils.cosUploadPopConfig.getSecretId()) ||
                StringUtils.isBlank(cosUploadPopUtils.cosUploadPopConfig.getSecretKey()) ||
                StringUtils.isBlank(cosUploadPopUtils.cosUploadPopConfig.getBucketName()) ||
                StringUtils.isBlank(cosUploadPopUtils.cosUploadPopConfig.getDomain()) ||
                StringUtils.isBlank(cosUploadPopUtils.cosUploadPopConfig.getRegion()) ||
                StringUtils.isBlank(cosUploadPopUtils.cosUploadPopConfig.getDir())
        ) {
            return false;
        }

        return true;
    }

    public static String uploadAndGetFullPath(String filePath, String fileName) {
        if (!checkParam()) {
            throw new RuntimeException("文件上传配置信息缺失");
        }

        // 指定要上传的文件
        File localFile = new File(filePath);
        // 指定文件将要存放的存储桶
        String bucketName = cosUploadPopUtils.cosUploadPopConfig.getBucketName();
        String suffix = getFilenameSuffix(fileName);
        String key = cosUploadPopUtils.cosUploadPopConfig.getDir() + UUID.randomUUID().toString().replace("-", "") + suffix;
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, localFile);
        cosClient.putObject(putObjectRequest);
        return cosUploadPopUtils.cosUploadPopConfig.getDomain() + key;
    }

    public static String uploadAndGetFullPath(InputStream inputStream, String fileName) {
        if (!checkParam()) {
            throw new RuntimeException("文件上传配置信息缺失");
        }

        String bucketName = cosUploadPopUtils.cosUploadPopConfig.getBucketName();
        String suffix = getFilenameSuffix(fileName);
        String key = cosUploadPopUtils.cosUploadPopConfig.getDir() + UUID.randomUUID().toString().replace("-", "") + suffix;
        ObjectMetadata objectMetadata = new ObjectMetadata();
        try {
            objectMetadata.setContentLength(inputStream.available());
        } catch (IOException e) {
            logger.error("CosUploadPopUtils.uploadAndGetFullPath(java.io.InputStream, java.lang.String)#fileName:{}", fileName, e);
        }
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, inputStream, objectMetadata);
        cosClient.putObject(putObjectRequest);
        IOUtils.closeQuietly(inputStream, null);
        return cosUploadPopUtils.cosUploadPopConfig.getDomain() + key;
    }

    public static String uploadAndGetRelativePath(String filePath, String fileName) {
        if (!checkParam()) {
            throw new RuntimeException("文件上传配置信息缺失");
        }

        // 指定要上传的文件
        File localFile = new File(filePath);
        // 指定文件将要存放的存储桶
        String bucketName = cosUploadPopUtils.cosUploadPopConfig.getBucketName();
        String suffix = getFilenameSuffix(fileName);
        String key = cosUploadPopUtils.cosUploadPopConfig.getDir() + UUID.randomUUID().toString().replace("-", "") + suffix;
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, localFile);
        cosClient.putObject(putObjectRequest);
        return key;
    }

    public static String uploadAndGetRelativePath(InputStream inputStream, String fileName) {
        if (!checkParam()) {
            throw new RuntimeException("文件上传配置信息缺失");
        }

        String bucketName = cosUploadPopUtils.cosUploadPopConfig.getBucketName();
        String suffix = getFilenameSuffix(fileName);
        String key = cosUploadPopUtils.cosUploadPopConfig.getDir() + UUID.randomUUID().toString().replace("-", "") + suffix;
        ObjectMetadata objectMetadata = new ObjectMetadata();
        try {
            objectMetadata.setContentLength(inputStream.available());
        } catch (IOException e) {
            logger.error("CosUploadPopUtils.uploadAndGetRelativePath(java.io.InputStream, java.lang.String)#fileName:{}", fileName, e);
        }
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, inputStream, objectMetadata);
        cosClient.putObject(putObjectRequest);
        IOUtils.closeQuietly(inputStream, null);
        return key;
    }

    public static byte[] downloadFile(String filePath) {
        CosUploadPopConfig cosUploadPopConfig = cosUploadPopUtils.cosUploadPopConfig;
        String key = filePath.replace(cosUploadPopConfig.getDomain(), "");
        GetObjectRequest getObjectRequest = new GetObjectRequest(cosUploadPopConfig.getBucketName(), key);
        COSObject cosObject = cosClient.getObject(getObjectRequest);
        COSObjectInputStream cosObjectInput = cosObject.getObjectContent();

        try {
            return IOUtils.toByteArray(cosObjectInput);
        } catch (Exception e) {
            logger.error("下载文件失败：" + e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(cosObjectInput, null);
        }

        return null;
    }

    private static String getFilenameSuffix(String filename) {
        String suffix = "";
        if (StringUtils.isNotEmpty(filename)) {
            String separtor = System.lineSeparator();
            if (filename.contains("/")) {
                filename = filename.substring(filename.lastIndexOf(separtor) + 1);
            }
            if (filename.contains(".")) {
                suffix = filename.substring(filename.lastIndexOf("."));
            }
        }
        return suffix;
    }

}
