package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ms.promotion.business.api.pop.GroupBuyingForPopBusinessApi;
import com.xyy.ms.promotion.business.result.MarketingGroupBuyingBaseInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class PromotionRemote {

    @Reference
    private GroupBuyingForPopBusinessApi groupBuyingForPopBusinessApi;

    public List<MarketingGroupBuyingBaseInfoDTO> listMarketingGroupBuyingBaseInfo(List<Long> activityIds) {
        try {
            log.info("PromotionRemote.listMarketingGroupBuyingBaseInfo#activityIds:{}", JSON.toJSONString(activityIds));
            if (CollectionUtils.isEmpty(activityIds)) {
                return Lists.newArrayList();
            }
            ApiRPCResult<List<MarketingGroupBuyingBaseInfoDTO>> result = groupBuyingForPopBusinessApi.listMarketingGroupBuyingBaseInfo(activityIds);
            log.info("PromotionRemote.listMarketingGroupBuyingBaseInfo#activityIds:{},return:{}", JSON.toJSONString(activityIds), JSON.toJSONString(result));
            if (result == null || result.isFail()) {
                return Lists.newArrayList();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("PromotionRemote.listMarketingGroupBuyingBaseInfo#activityIds:{}", JSON.toJSONString(activityIds), e);
            return Lists.newArrayList();
        }
    }

}
