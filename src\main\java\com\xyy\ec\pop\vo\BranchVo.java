package com.xyy.ec.pop.vo;


public class BranchVo {

	/** ID */
    private Long id;

    /** 区域编码 */
    private String branchCode;

    /** 区域名称 */
    private String branchName;

    /** 区域所关联的行政区域，多个以逗号隔开 */
    private String relatedAreas;

    /** 备注 */
    private String beizhu;

    //行政区域编码
    private String administrativeArea;

    public BranchVo() {}
    public BranchVo(String branchCode, String branchName) {
		super();
		this.branchCode = branchCode;
		this.branchName = branchName;
	}

	/**
     * @return 返回数据库表tb_branch的id字段值
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id 对应数据库表tb_branch的id字段
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return 返回数据库表tb_branch的branch_code字段值
     */
    public String getBranchCode() {
        return branchCode;
    }

    /**
     * @param branchCode 对应数据库表tb_branch的branch_code字段
     */
    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    /**
     *
     * @return 返回数据库表tb_branch的branch_name字段值
     */
    public String getBranchName() {
        return branchName;
    }

    /**
     * @param branchName 对应数据库表tb_branch的branch_name字段
     */
    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    /**
     *
     * @return 返回数据库表tb_branch的related_areas字段值
     */
    public String getRelatedAreas() {
        return relatedAreas;
    }

    /**
     * @param relatedAreas 对应数据库表tb_branch的related_areas字段
     */
    public void setRelatedAreas(String relatedAreas) {
        this.relatedAreas = relatedAreas;
    }

    /**
     *
     * @return 返回数据库表tb_branch的beizhu字段值
     */
    public String getBeizhu() {
        return beizhu;
    }

    /**
     * @param beizhu 对应数据库表tb_branch的beizhu字段
     */
    public void setBeizhu(String beizhu) {
        this.beizhu = beizhu;
    }
    
	public String getAdministrativeArea() {
		return administrativeArea;
	}
	public void setAdministrativeArea(String administrativeArea) {
		this.administrativeArea = administrativeArea;
	}
    
    
}