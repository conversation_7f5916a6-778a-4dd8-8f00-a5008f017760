package com.xyy.ec.pop.erpUtil.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.erpUtil.service.PopErpUtilService;
import com.xyy.ec.pop.server.api.erpUtil.api.ErpPopCorporationUtilApi;
import com.xyy.ec.pop.server.api.erpUtil.api.ErpPopErpListApi;
import com.xyy.ec.pop.server.api.erpUtil.dto.*;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/03/11/11:42
 * @Description:
 */
@Slf4j
@Service
public class PopErpUtilServiceImpl implements PopErpUtilService {
    @Reference
    private ErpPopErpListApi popErpListApi;

    @Reference
    private ErpPopCorporationUtilApi corporationUtilApi;

    @Override
    public PageInfo<PopCorpTaskConfigDto> getCorpTaskConfigPage(PageInfo<PopCorpTaskConfigDto> pageInfo, List<String> orgIds, Integer status) {
        try {
            log.info("#PopErpUtilServiceImpl.getCorpTaskConfigPage#pageInfo:{},id:{},status:{}", pageInfo, orgIds, status);
            ApiRPCResult<PageInfo<PopCorpTaskConfigDto>> taskConfigPage = popErpListApi.getTaskConfigPage(pageInfo.getPageNum(), pageInfo.getPageSize(), orgIds, status);
            log.info("#PopErpUtilServiceImpl.getCorpTaskConfigPage#taskConfigPage:{}", taskConfigPage);
            if (taskConfigPage.isSuccess()) {
                return taskConfigPage.getData();
            }
            return null;
        } catch (Exception e) {
            log.error("#PopErpUtilServiceImpl.getCorpTaskConfigPage#异常", e);
            return null;
        }
    }

    @Override
    public PageInfo<PopClientMonitorDto> getClientMonitorPage(PageInfo<PopClientMonitorDto> pageInfo, List<String> orgIds) {
        try {
            log.info("#PopErpUtilServiceImpl.getClientMonitorPage#pageInfo:{},ids:{}", pageInfo, orgIds);
            ApiRPCResult<PageInfo<PopClientMonitorDto>> clientMonitorPage = popErpListApi.getClientMonitorPage(pageInfo.getPageNum(), pageInfo.getPageSize(), orgIds);
            log.info("#PopErpUtilServiceImpl.getClientMonitorPage#clientMonitorPage:{}", clientMonitorPage);
            if (clientMonitorPage.isSuccess()) {
                return clientMonitorPage.getData();
            }
            return null;
        } catch (Exception e) {
            log.error("#PopErpUtilServiceImpl.getClientMonitorPage#异常", e);
            return null;
        }
    }

    @Override
    public PageInfo<PopTaskRecordDto> getTaskRecordPage
            (PageInfo<PopTaskRecordDto> pageInfo, List<String> orgIds, Integer status) {
        try {
            log.info("#PopErpUtilServiceImpl.getTaskRecordPage#pageInfo:{},ids:{},status:{}", pageInfo, orgIds, status);
            ApiRPCResult<PageInfo<PopTaskRecordDto>> taskRecordPage = popErpListApi.getTaskRecordPage(pageInfo.getPageNum(), pageInfo.getPageSize(), orgIds, status);
            log.info("#PopErpUtilServiceImpl.getTaskRecordPage#taskRecordPage:{}", taskRecordPage);
            if (taskRecordPage.isSuccess()) {
                return taskRecordPage.getData();
            }
            return null;
        } catch (Exception e) {
            log.error("#PopErpUtilServiceImpl.getTaskRecordPage#PageInfo:{}异常", e);
            return null;
        }
    }

    @Override
    public PopCorporationDto getCorporationByName(String name) {
        try {
            log.info("#PopErpUtilServiceImpl.getCorporationByName#name:{}", name);
            ApiRPCResult<PopCorporationDto> corporationByName = corporationUtilApi.getCorporationByName(name);
            log.info("#PopErpUtilServiceImpl.getCorporationByName#corporationByName:{}", corporationByName);
            if (corporationByName.isSuccess()) {
                return corporationByName.getData();
            }
            return null;
        } catch (Exception e) {
            log.error("#PopErpUtilServiceImpl.getCorporationByName#name:{}异常", name, e);
            return null;
        }
    }

    @Override
    public PageInfo<PopErpSkuToolDto> getPopErpSkuList(PageInfo<PopErpSkuToolDto> pageInfo, ErpSkuToolParamDto
            erpSkuToolDto) {
        try {
            log.info("#PopErpUtilService.getPopErpSkuByOrgId#pageInfo:{},erpSkuToolDto:{}", pageInfo, erpSkuToolDto);
            ApiRPCResult<PageInfo<PopErpSkuToolDto>> popErpSkuPage = popErpListApi.getPopErpSkuPage(pageInfo.getPageNum(), pageInfo.getPageSize(), erpSkuToolDto);
            log.info("#PopErpUtilService.getPopErpSkuByOrgId#popErpSkuPage:{}", popErpSkuPage);
            if (popErpSkuPage.isSuccess()) {
                return popErpSkuPage.getData();
            }
            return null;
        } catch (Exception e) {
            log.error("#PopErpUtilService.getPopErpSkuByOrgId#异常", e);
            return null;
        }
    }

    @Override
    public List<PopCorporationDto> getCorpList(String name) {
        try {
            log.info("#PopErpUtilServiceImpl.getCorpList#name:{}", name);
            ApiRPCResult<List<PopCorporationDto>> corporationList = corporationUtilApi.getCorporationList(name);
            log.info("#PopErpUtilServiceImpl.getCorpList#corporationList:{}", corporationList);
            if (corporationList.isSuccess()) {
                return corporationList.getData();
            }
            return Lists.newArrayList();
        } catch (Exception e) {
            log.error("#PopErpUtilServiceImpl.getCorpList#name:{}异常", name, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public Boolean updateCorpTaskConfigStatus(List<Integer> ids, Integer status) {
        try {
            log.info("#PopErpUtilService.updateCorpTaskConfigStatus#ids:{},status:{}", ids, status);
            ApiRPCResult<Boolean> updateTaskConfigStatus = popErpListApi.updateTaskConfigStatus(ids, status);
            log.info("#PopErpUtilService.updateCorpTaskConfigStatus#popErpSkuPage:{}", JSON.toJSONString(updateTaskConfigStatus));
            if (updateTaskConfigStatus.isSuccess()) {
                return updateTaskConfigStatus.getData();
            }
            return false;
        } catch (Exception e) {
            log.error("#PopErpUtilService.updateCorpTaskConfigStatus#异常", e);
            return false;
        }
    }

    @Override
    public Boolean delCorpTaskConfig(String orgId,Integer taskGroup) {
        try {
            //加个判断
            if (StringUtils.isBlank(orgId) ||  Objects.isNull(taskGroup) ){
                return false;
            }
            log.info("#PopErpUtilService.delCorpTaskConfig#orgId:{},taskGroup:{}", orgId,taskGroup);
            ApiRPCResult<Boolean> delConfigStatus = popErpListApi.delTaskConfigStatus(orgId,taskGroup);
            log.info("#PopErpUtilService.delCorpTaskConfig#popErpSkuPage:{}", JSON.toJSONString(delConfigStatus));
            if (delConfigStatus.isSuccess()) {
                return delConfigStatus.getData();
            }
            return false;
        } catch (Exception e) {
            log.error("#PopErpUtilService.delCorpTaskConfig#异常", e);
            return false;
        }
    }


    @Override
    public List<PopCorporationDto> getCorpListByNameAndOrgId(String name, String orgId) {
        try {
            log.info("#PopErpUtilServiceImpl.getCorpListByNameAndOrgId#name:{},orgId:{}", name,orgId);
            ApiRPCResult<List<PopCorporationDto>> corporationList = corporationUtilApi.getCorpListByNameAndOrgId(name,orgId);
            log.info("#PopErpUtilServiceImpl.getCorpListByNameAndOrgId#corporationList:{}", corporationList);
            if (corporationList.isSuccess()) {
                return corporationList.getData();
            }
            return Lists.newArrayList();
        } catch (Exception e) {
            log.error("#PopErpUtilServiceImpl.getCorpListByNameAndOrgId#name:{},orgId:{}异常", name, orgId,e);
            return Lists.newArrayList();
        }
    }
}
