package com.xyy.ec.pop.utils;

/**
 * @description:
 * @author: xu
 * @time: 2019/12/25 9:51
 */
import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 〈一句话功能简述〉<br>
 * 〈request ip获取工具〉
 *
 * <AUTHOR>
 * @create 2018/10/26
 * @since 1.0.0
 */
public class IPUtils {
    IPUtils (){}

    /**
     * 获取本地ip地址
     *
     * @return
     */
    public static String getLocalIP() {
        InetAddress addr = null;
        try {
            addr = InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }

        byte[] ipAddr = null;
        if(addr != null) {
            ipAddr = addr.getAddress();
        }
        String ipAddrStr = "";
        if(ipAddr != null && ipAddr.length>0) {
            for (int i = 0; i < ipAddr.length; i++) {
                if (i > 0) {
                    ipAddrStr += ".";
                }
                ipAddrStr += ipAddr[i] & 0xFF;
            }
        }

        return ipAddrStr;
    }
}