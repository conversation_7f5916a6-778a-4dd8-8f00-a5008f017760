package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.api.admin.PopSupplierMenuAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.PopSupplierMenuAdminQueryParam;
import com.xyy.ec.pop.server.api.seller.dto.PopSupplierMenuDto;
import com.xyy.ec.pop.service.SupplierMenuService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SupplierMenuServiceImpl implements SupplierMenuService {


    @Reference
    private PopSupplierMenuAdminApi popSupplierMenuAdminApi;


    @Override
    public PageInfo<PopSupplierMenuDto> queryPageList(PopSupplierMenuAdminQueryParam popSupplierMenuAdminQueryParam){
        ApiRPCResult<PageInfo<PopSupplierMenuDto>> apiRPCResult = popSupplierMenuAdminApi.adminQueryByPage(popSupplierMenuAdminQueryParam.getPageNum(),popSupplierMenuAdminQueryParam.getPageSize());
        if (apiRPCResult.isSuccess()){
            return apiRPCResult.getData();
        }
        return new PageInfo<>();
    }
    @Override
    public List<PopSupplierMenuDto> queryRootMenu(PopSupplierMenuDto menu) throws ServiceException{
        ApiRPCResult<List<PopSupplierMenuDto>> apiRPCResult = popSupplierMenuAdminApi.selectByParentMenuId(menu.getParentMenuId());
        if (apiRPCResult.isSuccess()){
            return apiRPCResult.getData();
        }
        return new ArrayList<>();
    }

    @Override
    public void insert(PopSupplierMenuDto menu) throws ServiceException{
        popSupplierMenuAdminApi.saveMenu(menu);
    }

    @Override
    public PopSupplierMenuDto findById(Integer menuId) throws ServiceException {
        ApiRPCResult<PopSupplierMenuDto> apiRPCResult = popSupplierMenuAdminApi.selectById(menuId);
        if (apiRPCResult.isSuccess()){
            return apiRPCResult.getData();
        }
        return null;
    }

    @Override
    public void updateBySelective(PopSupplierMenuDto menu) {
        popSupplierMenuAdminApi.updateById(menu);
    }
}
