package com.xyy.ec.pop.constants;


/**
 * CodeMap code字段常量类
 * @ClassName: CodeMapEnum 
 * <AUTHOR>
 * @date 2017-2-10 下午2:15:14
 */
public class CodeMapConstants {
	public static final String ALIAS_STATUS = "alias_status";			//H5展示类型
	public static final String CART_ACTIVITY_IMG = "CART_ACTIVITY_IMG";	//购物车活动图片
	public static final String CART_NOTIFY_IMG = "CART_NOTIFY_IMG";		//购物车通知图片
	public static final String DRUG_CLASSIFICATION_CODE = "drug_classification_code";	//药物类型编码
	public static final String GLOBAL_ACTIVITY_CONF = "GLOBAL_ACTIVITY_CONF";	//全局活动配置
	public static final String ORDER_STATUS = "order_status";			//订单状态
	public static final String ORDER_PAY_TYPE = "order_pay_type";		//订单支付类型
	public static final String ORDER_PAY_CHANNEL = "order_pay_channel";	//订单支付渠道
	public static final String SETTLE_CONF = "SETTLE_CONF";				//支付配置
	public static final String WEIXINPAY_CODE = "weixinpay_code";		//微信支付code
	public static final String ALIPAY_CODE = "alipay_code";				//支付宝支付code
	public static final String UNIONPAY_CODE = "unionpay_code";			//银联支付code
	public static final String VISIT_REASON = "visit_reason";			//拜访原因
	public static final String ORDER_EXTEND_STATUS = "ORDER_EXTEND_STATUS"; //订单拓展表状态
	public static final String ACTIVITY_CUSTOM_MAP = "ACTIVITY_CUSTOM_MAP"; //pc端活动相关字典
	public static final String QUESTION_TYPE = "QUESTION_TYPE"; //问题单问题类型
	public static final String QUESTION_TYPE_INV = "QUESTION_TYPE_INV"; //发票单问题类型
	public static final String DISALLOW_ACCESS_RESOURCE = "DISALLOW_ACCESS_RESOURCE"; //禁止访问资源字典
	public static final String DISALLOW_ACCESS_TIME = "DISALLOW_ACCESS_TIME"; //禁止访问资源时间
	public static final String REFUND_SHIRO = "REFUND_SHIRO";//退款权限字典
	public static final String MERCHANT_INVOICE = "MERCHANT_INVOICE";//用户发票信息
	public static final String INVOICE_TEXT = "INVOICE";//发票文案
	public static final String TIME_OUT_ORDER_CONFIG = "TIME_OUT_ORDER_CONFIG";//订单自动确认收获时间
	
	public static final String INVOICE_EMAIL ="INVOICE_EMAIL";   //电子发票生成失败，发给邮件人
	
	public static final String PRELOADING_TERM ="PRELOADING_TERM";   //预加载项

	public static final String SKU_LIMITED_NUM = "SKU_LIMITED_NUM"; //商品限购数量

	public static final String ACTIVITY_PACKAGE_MARK = "ACTIVITY_PACKAGE_MARK"; //活动mark标识
	
	
	public static final String ALL_TO_SKU = "ALL_TO_SKU"; //跳转到全部页面

	public static final String SKU_PROMO_KEY = "SKU_PROMO_KEY"; //商品活动是否缓存分组
	public static final String SKU_PROMO_KEY_IS_ENABLE = "SKU_PROMO_KEY_IS_ENABLE"; //商品活动是否缓存开关
	public static final String SKU_PROMO_KEY_TIMES = "SKU_PROMO_KEY_TIMES";// 缓存时效
	public static final String SKU_PROMO_KEY_IS_ENABLE_RESULT = "1"; //商品活动是否缓存开关：1.开


	public static final String MERCHANT_ADDRESS_KEY = "MERCHANT_ADDRESS_KEY";//用户资质是否缓存分组
	public static final String MERCHANT_ADDRESS_KEY_IS_ENABLE = "MERCHANT_ADDRESS_KEY_IS_ENABLE"; //用户资质是否缓存开关
	public static final String MERCHANT_ADDRESS_KEY_TIMES = "MERCHANT_ADDRESS_KEY_TIMES";// 缓存时效
	public static final String MERCHANT_ADDRESS_KEY_IS_ENABLE_RESULT = "1"; //商品活动是否缓存开关：1.开


	public static final String SKU_PROMO_IS_SHOW = "SKU_PROMO_IS_SHOW"; //商品返利标签是否显示
	public static final String SKU_PROMO_SHOW_VALUE = "SKU_PROMO_SHOW_VALUE"; // 商品标签是否显示(0.不显示，1.显示)
	public static final String SKU_PROMO_SHOW_VALUE_RESULT = "1";
	
	//活动资质校验直接开始时间
	public static final String FIND_CLIENT_TIME = "find_client_time";


	public static final String CRM_OPEN_SEA_MAX_JR = "CRM_OPEN_SEA_MAX_JR"; //商品活动是否缓存分组
	public static final String OPEN_SEA_REASON = "open_sea_reason"; //公海释放原因
	
    //是否显示提现按钮的开关
	public static final String  SHOW_CASH = "SHOW_CASH";
	//资质提示相关信息
	public static final String  APTITUDE_CONFIG = "APTITUDE_CONFIG";
	
	/** 订单入库明细分享缓存有效时间 */
	public static final String ORDER_SHARE ="ORDER_SHARE";
	
	public static final String LICENSE_AUDIT_MESSAGE = "LICENSE_AUDIT_MESSAGE";
	
	public static final String SMS_CODE = "SMS_CODE";  //短信平台开关
	
	
	//推送消息跳转到h5的页面
	public static final String TO_MSG_CENTER = "TO_MSG_CENTER";
	
	
	//省域
	public static final String USE_PROVINCE_CODE = "USE_PROVINCE_CODE";

	//活动标签（商品名称前面的标）
	public static final String ACTIVITY_TAG = "ACTIVITY_TAG";
	//806活动标签展示
	public static final String TAG806 = "806TAG";
	//展示开始时间
	public static final String TAG_BEGIN_TIME = "tagBeginTime";
	//展示结束时间
	public static final String TAG_END_TIME = "tagEndTime";


	public static final String INDEX_STYLE_EXP = "INDEX_STYLE_EXP";

	public static final String MERCHANT_PROMO_SLEEP_FLAG = "MERCHANT_PROMO_SLEEP_FLAG";


}
