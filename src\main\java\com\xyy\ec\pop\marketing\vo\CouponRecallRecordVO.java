package com.xyy.ec.pop.marketing.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CouponRecallRecordVO implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 优惠券模板id
     */
    private Long couponTemplateId;

    /**
     * 优惠券模板名称
     */
    private String couponTemplateName;

    /**
     * 优惠券优惠信息
     */
    private String templateDiscountStr;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 召回数量
     */
    private Integer recallSuccessNum;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 备注
     */
    private String remark;
}
