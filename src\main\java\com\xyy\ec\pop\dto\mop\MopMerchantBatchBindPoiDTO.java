package com.xyy.ec.pop.dto.mop;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 商家运用-下载绑定poi 模版DTO
 *
 * @author: duHao
 * @since: 12:55 2024/12/4
 */
@Data
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL, fillForegroundColor = 10)
@HeadFontStyle(fontHeightInPoints = 12)
public class MopMerchantBatchBindPoiDTO implements Serializable {

    /**
     * poiId
     */
    @ExcelProperty(value = "poiId", index = 0)
    private Long poiId;

    /**
     * 商户编号
     */
    @ExcelProperty(value = "商户编号", index = 1)
    private String orgId;


}
