package com.xyy.ec.pop.helper;

import com.google.common.collect.Lists;
import com.xyy.ec.pop.server.api.product.dto.*;
import com.xyy.ec.pop.vo.*;
import com.xyy.ms.promotion.business.common.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lizhiwei
 * @description: 商品转换
 * create at:  2020/12/18  19:39
 */
public class PopSkuConvertHelper {

    public static ProductEcVo popSkuDetailDtoToProductEcVo(PopSkuDetailDto skuDetailDto) {
        if (skuDetailDto == null) {
            return null;
        }
        PopSkuDto popSku = skuDetailDto.getPopSku();
        if (popSku == null) {
            return null;
        }
        //专属信息
        List<PopSkuExclusiveDto> popSkuExclusives = skuDetailDto.getPopSkuExclusives();
        //商品说明书
        PopSkuInstructionDto popSkuInstruction = skuDetailDto.getPopSkuInstruction();
        //商品详情图片
        PopSkuInstructionImageDto popSkuInstructionImages = skuDetailDto.getPopSkuInstructionImages();
        //商品首营资质
        List<PopSkuQualificationDto> popSkuQualifications = skuDetailDto.getPopSkuQualifications();
        //商品分类佣金比例
        PopSkuCategoryDto popSkuCategoryDto = skuDetailDto.getPopSkuCategory();

        ProductEcVo productEcVo = new ProductEcVo();
//        productEcVo.setId();
        productEcVo.setPriceSyncErp(popSku.getPriceSyncErp());
        productEcVo.setStockSyncErp(popSku.getStockSyncErp());
        productEcVo.setStandardProductId(popSku.getStandardProductId());
        productEcVo.setCompanyName(popSku.getCompanyName());
        productEcVo.setOrgId(popSku.getOrgId());
        productEcVo.setShopCode(popSku.getShopCode());
        productEcVo.setApprovalNumber(popSku.getApprovalNumber());  //批准文号
//        productEcVo.setPrice();
        productEcVo.setPriceType("1");
        productEcVo.setManufacturer(popSku.getManufacturer());
        productEcVo.setAudited(popSku.getAudited());
        productEcVo.setBrand(popSku.getBrand());
        productEcVo.setSaleType(popSku.getSaleType());
//     productEcVo.setLimitedQty();
//        productEcVo.setBranchCode(); //TODO 确认 是否还有用

        productEcVo.setAvailableQty(popSku.getStock());//TODO 确认 是否还有用
//        productEcVo.setAvailableQtyType();

        productEcVo.setBarcode(popSku.getBarcode());
//        productEcVo.setBrand();
        productEcVo.setBz(popSku.getBz());
//        productEcVo.setSkuRelationCategory();
//        productEcVo.setSkuRelationCategoryName();
        productEcVo.setCategoryId(popSku.getCategoryId()==null?"":popSku.getCategoryId().toString()); //推荐分类
//        productEcVo.setCategoryName();
        productEcVo.setCode(popSku.getCode());
        productEcVo.setCommonName(popSku.getCommonName());
        //        productEcVo.setDescription(); //
        productEcVo.setDosageForm(popSku.getDosageForm()); //剂型
        productEcVo.setDrugClassification(popSku.getDrugClassification());
        productEcVo.setFob(popSku.getFob().toString());
        productEcVo.setChainPrice(popSku.getChainPrice()==null?null:popSku.getChainPrice().toPlainString());
//        productEcVo.setGrade();
//        productEcVo.setGrossMargin(popSku.get); //毛利率
        productEcVo.setImageUrl(popSku.getImageUrl());


//        productEcVo.setIsBasePrice();
        productEcVo.setIsFragileGoods(popSku.getIsFragileGoods() == null ? "0" : popSku.getIsFragileGoods().toString());
        productEcVo.setIsNew(popSku.getIsNew() == null ? "0" : popSku.getIsNew().toString());
        productEcVo.setIsSplit(popSku.getIsSplit() == null ? "0" : popSku.getIsSplit().toString());
        productEcVo.setMediumPackageNum(popSku.getMediumPackageNum().toString());
        productEcVo.setPieceLoading(popSku.getPieceLoading());
        productEcVo.setProductName(popSku.getProductName());
        productEcVo.setProductUnit(popSku.getProductUnit());
        if (popSku.getProductionDate() !=null){
            productEcVo.setProductionDate(DateUtils.date2String(popSku.getProductionDate(),"yyyy-MM-dd"));
        }

//        productEcVo.setRetailPrice(); //对比价
        productEcVo.setShelfLife(popSku.getShelfLife());
        productEcVo.setShowName(popSku.getShowName());
//        productEcVo.setSkuCategoryRelations(); //关联分类
//        productEcVo.setSkuInstructionImageList();
//        productEcVo.setSkuCategoryRelationsStr();
//

        String skuImgUrls = popSku.getImageListUrl();
        if (StringUtils.isNotBlank(skuImgUrls)) {
            productEcVo.setImagesListStr(popSku.getImageListUrl());
            List<String> imgList = Arrays.asList(skuImgUrls.split(","));
            productEcVo.setImagesList(imgList);
        }

        productEcVo.setSpec(popSku.getSpec());
        productEcVo.setStatus(popSku.getStatus());
        productEcVo.setStorageCondition(popSku.getStorageCondition());
        productEcVo.setSuggestPrice(popSku.getSuggestPrice()==null?"":popSku.getSuggestPrice().toString());
        if(popSku.getSuggestPrice()!=null){
            productEcVo.setSuggestPrice(popSku.getSuggestPrice().toString());
        }

        productEcVo.setTerm(popSku.getTerm());
        if(popSku.getUniformPrice()!=null){
            productEcVo.setUniformPrice(popSku.getUniformPrice().toString());
        }

//        productEcVo.setValidity();  //有效期至 无用
        productEcVo.setZjm(popSku.getZjm());
        productEcVo.setIsThirdCompany(1);
        productEcVo.setSkuCategory(popSku.getSkuCategory());  //经营分类id
        productEcVo.setSkuCategoryId(popSku.getSkuCategoryId()); //经营分类
        if(popSku.getMediumPackageNum()!=null){
            productEcVo.setMediumPackage(popSku.getMediumPackageNum().toString());
        }

//        productEcVo.setVideoUrl(); //视频url

//        productEcVo.setSkuPopExtendNewList();

//        productEcVo.setProducePopExtendVo();
        productEcVo.setOldestProDate(popSku.getOldestProDate());
        productEcVo.setNewProDate(popSku.getNewProDate());
        productEcVo.setNearEffect(popSku.getNearEffect());
        productEcVo.setFarEffect(popSku.getFarEffect());
//        productEcVo.setTotalAvailableQty();
        productEcVo.setCreateTime(popSku.getCreateTime());
        productEcVo.setIsInjection(popSku.getIsInjection()==null?"0":popSku.getIsInjection().toString());
        productEcVo.setIsPrescription(popSku.getIsPrescription()==null?"0":popSku.getIsPrescription().toString());
        if (popSkuCategoryDto !=null){
          productEcVo.setSource(popSkuCategoryDto.getSource());
          productEcVo.setErpFirstCategoryId(NumberUtils.toLong(popSkuCategoryDto.getBusinessFirstCategoryCode()));
          productEcVo.setErpSecondCategoryId(NumberUtils.toLong(popSkuCategoryDto.getBusinessSecondCategoryCode()));
          productEcVo.setErpThirdCategoryId(NumberUtils.toLong(popSkuCategoryDto.getBusinessThirdCategoryCode()));
          productEcVo.setErpFourthCategoryId(NumberUtils.toLong(popSkuCategoryDto.getBusinessFourthCategoryCode()));
            if (popSkuCategoryDto.getCommissionRatio() == null) {
                productEcVo.setCommissionRatioStr(StringUtils.EMPTY);
            } else {
                productEcVo.setCommissionRatioStr(popSkuCategoryDto.getCommissionRatio() .toString().concat("%"));
            }
          productEcVo.setCommissionRatioStr(popSkuCategoryDto.getCommissionRatio().toString());
//        productEcVo.setSkuPopExtendNewListStr();
//        productEcVo.setErpFirstCategoryName();
//        productEcVo.setErpSecondCategoryName();
//        productEcVo.setErpThirdCategoryName();
//        productEcVo.setErpFourthCategoryName();
        }

        // region 说明书信息
        if (popSkuInstruction != null) {
            productEcVo.setSubtitle(popSkuInstruction.getSubtitle());
            productEcVo.setAbstain(popSkuInstruction.getAbstain());
            productEcVo.setComponent(popSkuInstruction.getComponent()); //成分
            productEcVo.setConsiderations(popSkuInstruction.getConsiderations()); //注意事项
            productEcVo.setIndication(popSkuInstruction.getIndication());
            productEcVo.setInteraction(popSkuInstruction.getInteraction());
            productEcVo.setUsageAndDosage(popSkuInstruction.getUsageAndDosage());
            productEcVo.setUntowardEffect(popSkuInstruction.getUntowardEffect());
        }
        // endregion

        // region 商品详情图
        if(popSkuInstructionImages!=null){
            String instrutionImagesUrl = popSkuInstructionImages.getInstrutionImageUrl();
            if(StringUtils.isNotEmpty(instrutionImagesUrl)){
                productEcVo.setSkuInstructionImageListStr(popSkuInstructionImages.getInstrutionImageUrl());
                List<String> arrStr =Arrays.asList(instrutionImagesUrl.split(","));
                List<SkuInstructionImageVo> instructionImageVoList = arrStr.stream().map(s->{
                    SkuInstructionImageVo skuInstructionImageVo = new SkuInstructionImageVo();
                    skuInstructionImageVo.setBarcode(popSku.getBarcode());
                    skuInstructionImageVo.setInstrutionImageUrl(s);
                    return skuInstructionImageVo;
                }).collect(Collectors.toList());
                productEcVo.setSkuInstructionImageList(instructionImageVoList);
            }
        }
        // endregion

        // region 专属信息
        List<SkuPopExtend> skuPopExtendList = convertPopSkuExclusiveToPopExtendList(popSkuExclusives);
        productEcVo.setSkuPopExtendNewList(skuPopExtendList);

        ProducePopExtendVo skuPopExtend = new ProducePopExtendVo();
        skuPopExtend.setErpCode(popSku.getErpCode());
        productEcVo.setSkuPopExtend(skuPopExtend);
        // endregion

        //首营资质
        List<PopSkuQualificationVo> skuQualificationVoList =  convertPopSkuQualificationDtoToVoList(popSkuQualifications);
        productEcVo.setSkuQualificationVoList(skuQualificationVoList);
        //商圈id
        PopBusAreaProductRelationDto relationDto = skuDetailDto.getBusAreaProductRelation();
        if(relationDto!=null){
            productEcVo.setBusAreaId(relationDto.getBusAreaId());
        }
        PopBusAreaConifigDto busAreaConifigDto = skuDetailDto.getBusConfigDto();
        if(busAreaConifigDto!=null){
            productEcVo.setBusAreaConfigName(busAreaConifigDto.getBusAreaName());
        }
        //设置商品起购数量
        productEcVo.setMinPurchaseCount(popSku.getMinPurchaseCount());
        //设置商品限购信息
        productEcVo.setPopSkuPurchaseLimitDto(skuDetailDto.getPopSkuPurchaseLimitDto());
        productEcVo.setAvailableQty(popSku.getStock());
        return productEcVo;
    }

    public static SkuPopExtend convertPopSkuExclusiveToPopExtend(PopSkuExclusiveDto popSkuExclusiveDto){
        if (null == popSkuExclusiveDto){
            return null;
        }
        SkuPopExtend skuPopExtend =new SkuPopExtend();
//        skuPopExtend.setId(popSkuExclusiveDto.getId());
//        skuPopExtend.setOrgId();
//        skuPopExtend.setBranchCode();
//        skuPopExtend.setSkuId();
//        skuPopExtend.setMarketPrice();
        skuPopExtend.setCertificateId(popSkuExclusiveDto.getCertificateId());
        skuPopExtend.setUrl(popSkuExclusiveDto.getCertificateUrl());
        skuPopExtend.setType(popSkuExclusiveDto.getType().intValue());
//        skuPopExtend.setSalesArea();
//        skuPopExtend.setIsSalesOn();
//        skuPopExtend.setErpCode();
//        skuPopExtend.setSubtitle();
//        skuPopExtend.setOnlineTime();
//        skuPopExtend.setPurchaseMerchantType();
        skuPopExtend.setIsChoosed(popSkuExclusiveDto.getIsChoosed().intValue());
        return skuPopExtend;
    }
    public static List<SkuPopExtend> convertPopSkuExclusiveToPopExtendList(List<PopSkuExclusiveDto> popSkuExclusives) {
        if (CollectionUtils.isEmpty(popSkuExclusives)) {
            return Lists.newArrayList();
        }
        List<SkuPopExtend> skuPopExtendList = popSkuExclusives.stream().map(e->convertPopSkuExclusiveToPopExtend(e)).collect(Collectors.toList());
        return skuPopExtendList;
    }

    public static SkuInstructionImageVo convertInstructionImageDto2Vo(PopSkuInstructionImageDto instructionImageDto){
        if (null == instructionImageDto){
            return null;
        }
        SkuInstructionImageVo instructionImageVo = new SkuInstructionImageVo();
        instructionImageVo.setId(instructionImageDto.getId());
//        instructionImageVo.setSkuId();
        instructionImageVo.setBarcode(instructionImageDto.getBarcode());
        instructionImageVo.setInstrutionImageUrl(instructionImageDto.getInstrutionImageUrl());
        instructionImageVo.setCreateTime(instructionImageDto.getCreateTime());
        instructionImageVo.setCreator(instructionImageDto.getCreator());
        instructionImageVo.setUpdateTime(instructionImageDto.getUpdateTime());
        instructionImageVo.setUpdator(instructionImageDto.getUpdator());

        return instructionImageVo;
    }
    public static List<SkuInstructionImageVo> convertInstructionImageDto2VoList(List<PopSkuInstructionImageDto> instructionImageDtoList){
        if (CollectionUtils.isEmpty(instructionImageDtoList)){
            return Lists.newArrayList();
        }
        List<SkuInstructionImageVo> instructionImageVos = instructionImageDtoList.stream().map(dto->convertInstructionImageDto2Vo(dto))
                .collect(Collectors.toList());
        return instructionImageVos;
    }
    public static PopSkuQualificationVo convertPopSkuQualificationDtoToVo(PopSkuQualificationDto dto){
        if (null == dto){
            return null;
        }
        PopSkuQualificationVo vo = new PopSkuQualificationVo();
        vo.setId(dto.getId());
        vo.setBarcode(dto.getBarcode());
        vo.setName(dto.getName());
        vo.setCode(dto.getCode());
        vo.setStartDate(dto.getStartDate());
        vo.setEndDate(dto.getEndDate());
        vo.setUrl(dto.getUrl());
        vo.setRemarks(dto.getRemarks());
        vo.setState(dto.getState());
        vo.setDel(dto.getDel());
        vo.setCreateTime(dto.getCreateTime());
        vo.setCreateId(dto.getCreateId());
        vo.setCreateName(dto.getCreateName());
        vo.setUpdateTime(dto.getUpdateTime());
        vo.setUpdateId(dto.getUpdateId());
        vo.setUpdateName(dto.getUpdateName());
        vo.setQualificationsDetailId(dto.getQualificationsDetailId());
        return vo;
    }
    public static List<PopSkuQualificationVo> convertPopSkuQualificationDtoToVoList(List<PopSkuQualificationDto> dtoList){
        if (CollectionUtils.isEmpty(dtoList)){
            return Lists.newArrayList();
        }
        List<PopSkuQualificationVo> skuQualificationVoList = dtoList.stream().map(dto->convertPopSkuQualificationDtoToVo(dto))
                .collect(Collectors.toList());
        return skuQualificationVoList;
    }
}
