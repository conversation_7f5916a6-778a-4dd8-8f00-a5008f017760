package com.xyy.ec.pop.service.settle.impl;

import com.xyy.ec.pop.dao.PopBillPaymentMapper;
import com.xyy.ec.pop.po.PopBillPaymentPo;
import com.xyy.ec.pop.service.settle.PopBillPaymentService;
import com.xyy.ec.pop.vo.settle.PopBillPayStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillPayVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/1 10:47
* @table
*/
@Slf4j
@Service
public class PopBillPaymentServiceImpl implements PopBillPaymentService {

    @Autowired
    private PopBillPaymentMapper popBillPaymentMapper;

    @Override
    public int insert(PopBillPaymentPo record) {
        return popBillPaymentMapper.insert(record);
    }

    @Override
    public int insertSelective(PopBillPaymentPo record) {
        return popBillPaymentMapper.insertSelective(record);
    }

    @Override
    public PopBillPaymentPo selectByPrimaryKey(Long id) {
        return popBillPaymentMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(PopBillPaymentPo record) {
        return popBillPaymentMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PopBillPaymentPo record) {
        return popBillPaymentMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<PopBillPaymentPo> queryPopBillList(PopBillPayVo popBillPayVo, Integer pageNum, Integer pageSize) {
        return popBillPaymentMapper.queryPopBillList(popBillPayVo,pageNum,pageSize);
    }

    @Override
    public Long queryPopBillListCount(PopBillPayVo popBillPayVo) {
        return popBillPaymentMapper.queryPopBillListCount(popBillPayVo);
    }

    @Override
    public PopBillPayStatisVo queryPopBillPayStatis(PopBillPayVo popBillPayVo) {
        return popBillPaymentMapper.queryPopBillPayStatis(popBillPayVo);
    }

    @Override
    public PopBillPaymentPo selectByFlowNo(String flowNo) {
        return popBillPaymentMapper.selectByFlowNo(flowNo);
    }

    @Override
    public List<String> queryByOrderNos(PopBillPaymentPo popBillPaymentPo) {
        return popBillPaymentMapper.queryByOrderNos(popBillPaymentPo);
    }

    @Override
    public List<PopBillPaymentPo> queryPopBillPayByFlowNoList(List<String> flowNoList) {
        return popBillPaymentMapper.queryPopBillPayByFlowNoList(flowNoList);
    }

    @Override
    public void batchUpdateById(List<PopBillPaymentPo> popBillPaymentPos) {
        popBillPaymentMapper.batchUpdateById(popBillPaymentPos);
    }
}
