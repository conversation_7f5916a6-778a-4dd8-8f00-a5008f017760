package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.model.request.SettleCycleUpdateReq;
import com.xyy.ec.pop.remote.EcSystemApiRemote;
import com.xyy.ec.pop.remote.PopCommissionSettlementRemoteAdapter;
import com.xyy.ec.pop.server.api.Enum.ShopBusinessAttributeEnum;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationStateEnum;
import com.xyy.ec.pop.server.api.merchant.api.seller.CorporationApi;
import com.xyy.ec.pop.service.BusinessCategoryCommissionService;
import com.xyy.ec.pop.service.CorporationPriceTypeService;
import com.xyy.ec.pop.service.PopCorporationAreaService;
import com.xyy.ec.pop.service.PopCorporationService;
import com.xyy.ec.pop.service.PopCorporationToCorTransferService;
import com.xyy.ec.pop.vo.BusinessCategoryCommissionVo;
import com.xyy.ec.pop.vo.CommissionsSettlementSetVo;
import com.xyy.ec.pop.vo.CooperationInfoVo;
import com.xyy.ec.pop.vo.CorporationPriceTypeVo;
import com.xyy.ec.pop.vo.PopCorporationToCorTransferVo;
import com.xyy.ec.pop.vo.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 企业主信息
 * @version V2.0.1
 * <AUTHOR>
 * @date 2018年10月12日 下午3:44:28
 * @Description:
 */
@Api(tags = "企业信息")
@Controller
@RequestMapping(value = "/corporation")
public class CorporationController extends BaseController {
	private static final Logger LOG = LoggerFactory.getLogger(CorporationController.class);

	@Autowired
	private PopCorporationService popCorporationService;
	@Autowired
	private PopCorporationAreaService popCorporationAreaService;
    @Autowired
    private PopCommissionSettlementRemoteAdapter popCommissionSettlementRemoteAdapter;
    @Autowired
    private PopCorporationToCorTransferService popCorporationToCorTransferService;
	@Autowired
	private EcSystemApiRemote ecSystemApiRemote;

	@Autowired
	private CorporationPriceTypeService corporationSaleTypeService;
	@Autowired
	private BusinessCategoryCommissionService commissionService;

	@Reference(version = "1.0.0")
	private CorporationAdminApi corporationAdminApi;

	@Reference
	private CorporationApi corporationApi;

	/**
	 * ec正在售卖中的省份编码 110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,500000,510000,520000,530000,610000,650000,666666,777777
	 */
	@Value("#{'${ec.onSale.province}'.split(',')}")
	private List<Integer> ecOnSaleArea;
	//火星、水星区域
	@Value("#{'${ec.province.mars:666666,777777}'.split(',')}")
	private List<Integer> saleAreaMars;
	//展示水星、火星区域的orgId
	@Value("#{'${ec.province.mars.show}'.split(',')}")
	private List<String> saleAreaShowMars;
	@Value("${corporation.area.canUpdate:true}")
	private boolean canUpdateArea;

	//-----

	/**
	 * 更新售卖区域
	 * @param orgId
	 * @param cooperationInfo
	 * @return
	 */
	@ResponseBody
	@PostMapping(value = "/updateCorporationArea")
	@AvoidRepeatableCommit(timeout = 3)
	public ResponseVo<Boolean> updateCorporationArea(@RequestParam String orgId,@RequestBody CooperationInfoVo cooperationInfo){
		try {
			if(!canUpdateArea){
				return ResponseVo.errRest("当前不可修改售卖区域");
			}
			SysUser sysUser = getUser();
			String user = StringUtils.isEmpty(sysUser.getUsername())?sysUser.getRealName():sysUser.getUsername();
			LOG.error("更新经营区域orgId:{},sysUser:{},CooperationInfo:{}",orgId,user,JSON.toJSONString(cooperationInfo));
			if(CollectionUtils.isEmpty(cooperationInfo.getAreas())){
				return ResponseVo.errRest("至少勾选一个售卖区域");
			}
			cooperationInfo.getAreas().forEach(item->item.setId(null));
			//将区域设置到药品和非药上
			cooperationInfo.setDrugAreas(cooperationInfo.getAreas());
			cooperationInfo.setNonDrugAreas(cooperationInfo.getAreas());
			JSONArray areas = areas();
			if(areas==null){
				return ResponseVo.errRest("查询区域字段失败");
			}
			Map<Integer,String> map = new HashMap<>(areas.size());
			for(int i=0;i<areas.size();i++){
				JSONObject obj = areas.getJSONObject(i);
				map.put(obj.getIntValue("areaCode"),obj.getString("areaName"));
			}
			popCorporationAreaService.updateCorporationArea(orgId,cooperationInfo.getDrugAreas(),cooperationInfo.getNonDrugAreas(),map,user,sysUser.getId());
			LOG.error("更新经营区域orgId:{} 成功",orgId);
			return ResponseVo.successResult(true);
		}catch (ServiceException e){
			LOG.warn("更新经营区域orgId:{},CooperationInfo:{} 失败:{}",orgId,JSON.toJSONString(cooperationInfo), e.getMessage());
			return ResponseVo.errRest(e.getMessage());
		}catch (Exception e) {
			LOG.error("更新经营区域orgId:{},CooperationInfo:{}",orgId,JSON.toJSONString(cooperationInfo), e);
			return ResponseVo.errRest("查询合作信息异常");
		}
	}

	private JSONArray areas(){
		String str = ecSystemApiRemote.getNewDicAreaList("0");
		JSONObject jsonObject = JSON.parseObject(str);
		if("1".equals(String.valueOf(jsonObject.getString("code")))){
			JSONArray array = jsonObject.getJSONArray("result");
			if(CollectionUtils.isEmpty(ecOnSaleArea)){
				return array;
			}
			JSONArray jsonArray = new JSONArray();
			for(int i=0;i<array.size();i++){
				JSONObject obj = array.getJSONObject(i);
				if(ecOnSaleArea.contains(obj.getIntValue("areaCode"))){
					jsonArray.add(obj);
				}
			}
			return jsonArray;
		}
		return null;
	}

	/**
	 * 根据orgId查询合作信息
	 * @param orgId
	 * @return
	 */
	@ResponseBody
	@GetMapping(value = "/cooperationInfo")
	public ResponseVo<CooperationInfoVo> cooperationInfo(@RequestParam String orgId){
		try{
			CooperationInfoVo vo = popCorporationService.cooperationInfo(orgId);
			return ResponseVo.successResult(vo);
		}catch (ServiceException e){
			LOG.warn("查询店铺合作信息异常", e.getMessage());
			return ResponseVo.errRest(e.getMessage());
		}catch (Exception e) {
			LOG.error("查询店铺合作信息异常", e);
			return ResponseVo.errRest("查询合作信息异常");
		}

	}

	@ResponseBody
	@GetMapping(value = "/dicNewAreas")
	public ResponseVo<Object> dicNewAreas(String orgId){
		JSONArray areas = areas();
		if(areas!=null){
			if(!saleAreaShowMars.contains(orgId)){//不包含此机构
				JSONArray jsonArray = new JSONArray();
				for(int i=0;i<areas.size();i++){
					JSONObject obj = areas.getJSONObject(i);
					if(!saleAreaMars.contains(obj.getIntValue("areaCode"))){
						jsonArray.add(obj);
					}
				}
				areas = jsonArray;
			}
			return ResponseVo.successResult(areas);
		}
		return ResponseVo.errRest("查询区域字段失败");
	}

	/**
	 * 设置线下转账收款方
	 * @param transferVo
	 * @return
	 */
	@ResponseBody
	@PostMapping(value = "/updateToCorTransfer")
	@AvoidRepeatableCommit(timeout = 2)
	public ResponseVo<Boolean> updateToCorTransfer(@RequestBody PopCorporationToCorTransferVo transferVo) {
		try {
			LOG.info("CorporationController.updateToCorTransfer#transferVo:{}", JSON.toJSONString(transferVo));
			boolean result = popCorporationToCorTransferService.updateToCorTransfer(transferVo, getUser().getUsername());
			LOG.info("CorporationController.updateToCorTransfer#transferVo:{} return {}", JSON.toJSONString(transferVo), result);
			if(result){
				return ResponseVo.successResult(result);
			}
			return ResponseVo.errRest("更新失败");
		} catch (IllegalArgumentException e) {
			LOG.error("CorporationController.updateToCorTransfer#transferVo:{} 失败:{}", JSON.toJSONString(transferVo), e.getMessage());
			return ResponseVo.errRest(e.getMessage());
		} catch (Exception e) {
			LOG.error("CorporationController.updateToCorTransfer#transferVo:{} 异常", JSON.toJSONString(transferVo), e);
			return ResponseVo.errRest("更新失败");
		}
	}

	/**
	 * 设置佣金结算方式
	 * @param commissionsSettlementSetVo
	 * @return
	 */
	@ResponseBody
	@PostMapping(value = "/setCommissionsSettlementType")
	public ResponseVo<Boolean> setCommissionsSettlementType(@RequestBody CommissionsSettlementSetVo commissionsSettlementSetVo){
		try {
			if(Objects.isNull(commissionsSettlementSetVo)
					||Objects.isNull(commissionsSettlementSetVo.getOrgId())
					||Objects.isNull(commissionsSettlementSetVo.getSettlementType())){
				return ResponseVo.errRest("入参为空");
			}
			SysUser sysUser = getUser();
			boolean result = popCommissionSettlementRemoteAdapter.setCooperationCommissionsSettlementType(commissionsSettlementSetVo.getOrgId(),commissionsSettlementSetVo.getSettlementType(),sysUser.getUsername());
			LOG.info("设置佣金结算方式,vo:{} 结果:{}",JSONObject.toJSONString(commissionsSettlementSetVo),result);
			return ResponseVo.successResult(result);
		}catch (ServiceException e){
			LOG.warn("设置佣金结算方式失败,vo:{} 失败:{}",JSONObject.toJSONString(commissionsSettlementSetVo), e.getMessage());
			return ResponseVo.errRest(e.getMessage());
		}catch (Exception e) {
			LOG.error("设置佣金结算方式异常,vo:{}",JSONObject.toJSONString(commissionsSettlementSetVo), e);
			return ResponseVo.errRest("查询合作信息异常");
		}
	}

	/**
	 * 获取企业状态
	 * @return {@link Map}<{@link Integer}, {@link String}>：Map。
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年10月18日 下午12:01:49
	 */
	@ApiOperation(value = "企业状态及其中文描述", notes = "企业状态及其中文描述", response = Map.class)
	@ResponseBody
	@PostMapping(value = "/states")
	public Map<Integer, String> states() {
		CorporationStateEnum[] vs = CorporationStateEnum.values();
		Map<Integer, String> kv = new LinkedHashMap<Integer, String>(vs.length);
		for (int i = 0; i < vs.length; i++) {
			CorporationStateEnum e = vs[i];
			kv.put(e.getCode(), e.getName());
		}
		return kv;
	}

	@ResponseBody
	@PostMapping(value = "/updatePriceType")
	@AvoidRepeatableCommit(timeout = 2)
	public ResponseVo<Boolean> updatePriceType(@RequestBody CorporationPriceTypeVo typeVo) {
		try {
			LOG.info("CorporationController.updatePriceType#typeVo:{}", JSON.toJSONString(typeVo));
			boolean result = corporationSaleTypeService.updatePriceType(typeVo, getUser().getUsername());
			LOG.info("CorporationController.updatePriceType#typeVo:{} return {}", JSON.toJSONString(typeVo), result);
			if(result){
				return ResponseVo.successResult(result);
			}
			return ResponseVo.errRest("更新失败");
		} catch (ServiceException e) {
			LOG.error("CorporationController.updatePriceType#typeVo:{} 失败:{}", JSON.toJSONString(typeVo), e.getMessage());
			return ResponseVo.errRest(e.getMessage());
		} catch (Exception e) {
			LOG.error("CorporationController.updateSaleType#typeVo:{} 异常", JSON.toJSONString(typeVo), e);
			return ResponseVo.errRest("更新失败");
		}
	}

	/**
	 * 更新佣金比例
	 * @param orgId
	 * @param commissions
	 * @return
	 */
	@ResponseBody
	@PostMapping(value = "/updateCommissions")
	@AvoidRepeatableCommit(timeout = 3)
	public ResponseVo<Boolean> updateCommissions(@RequestParam String orgId,@RequestBody List<BusinessCategoryCommissionVo> commissions){
		try {
			SysUser sysUser = getUser();
			LOG.error("更新佣金比例orgId:{},sysUser:{},commissions:{}",orgId,sysUser,JSON.toJSONString(commissions));
			commissionService.updateCommissions(orgId,commissions,StringUtils.isEmpty(sysUser.getUsername())?sysUser.getRealName():sysUser.getUsername());
			LOG.error("更新佣金比例orgId:{} 成功",orgId);
			return ResponseVo.successResult(true);
		}catch (ServiceException e){
			LOG.warn("更新佣金比例失败orgId:{},commissions:{} 失败:{}",orgId,JSON.toJSONString(commissions), e.getMessage());
			return ResponseVo.errRest(e.getMessage());
		}catch (Exception e) {
			LOG.error("更新佣金比例异常orgId:{},commissions:{}",orgId,JSON.toJSONString(commissions), e);
			return ResponseVo.errRest("查询合作信息异常");
		}
	}

	@ResponseBody
	@PostMapping(value = "/updateSettleCycle")
	public ResponseVo<Boolean> updateSettleCycle(@RequestBody SettleCycleUpdateReq req){
		String userName = getUserName();
		if (StringUtils.isBlank(req.getOrgId())) {
			return ResponseVo.errRest("商户编号不为空");
		}
		popCorporationService.updateSettleCycle(req.getOrgId(), req.getSettleCycle(), req.getSettleValue(),userName);
		return ResponseVo.successResult(true);
	}

	/**
	 * 更新店铺经营属性
	 *
	 * @param orgId
	 * @param businessAttribute
	 * @return
	 * @see com.xyy.ec.pop.server.api.Enum.ShopBusinessAttributeEnum
	 */
	@ResponseBody
	@PostMapping(value = "/updateBusinessAttribute")
	@AvoidRepeatableCommit(timeout = 3)
	public ResponseVo<Boolean> updateBusinessAttribute(String orgId, Byte businessAttribute) {
		LOG.info("CorporationController.updateBusinessAttribute#orgId:{},businessAttribute:{}", orgId, businessAttribute);
		try {
			if (StringUtils.isBlank(orgId) || businessAttribute == null) {
				return ResponseVo.errRest("缺少必传参数");
			}
			if (!Objects.equals(businessAttribute, ShopBusinessAttributeEnum.ALL.getCode()) && !Objects.equals(businessAttribute, ShopBusinessAttributeEnum.NO_DRAG.getCode())
					&& !Objects.equals(businessAttribute, ShopBusinessAttributeEnum.CHINESE_DRAG.getCode()) && !Objects.equals(businessAttribute, ShopBusinessAttributeEnum.INSTRUMENT.getCode())) {
				return ResponseVo.errRest("店铺经营属性传值不对");
			}
			SysUser sysUser = getUser();
			String user = StringUtils.isBlank(sysUser.getUsername()) ? sysUser.getRealName() : sysUser.getUsername();
			popCorporationService.updateBusinessAttribute(orgId, businessAttribute, sysUser.getId(), user);
			return ResponseVo.successResult(true);
		} catch (PopAdminException e) {
			LOG.error("CorporationController.updateBusinessAttribute#自定义异常 orgId:{},businessAttribute:{}", orgId, businessAttribute, e);
			return ResponseVo.errRest(e.getMessage());
		} catch (Exception e) {
			LOG.error("CorporationController.updateBusinessAttribute#未知异常 orgId:{},businessAttribute:{}", orgId, businessAttribute, e);
			return ResponseVo.errRest("更新店铺经营属性异常");
		}
	}


	/**
	 * 更新商圈分类
	 * param orgId
	 * param shopCategory 1-全国 2-本省 3-多省
	 * @return
	 */
	@ResponseBody
	@PostMapping(value = "/updateShopCategory")
	@AvoidRepeatableCommit(timeout = 3)
	public ResponseVo<Boolean> updateShopCategory(String orgId, Integer shopCategory) {
		try {
			if (StringUtils.isBlank(orgId) || shopCategory == null) {
				return ResponseVo.errRest("缺少必传参数");
			}
			SysUser sysUser = getUser();
			String user = StringUtils.isBlank(sysUser.getUsername()) ? sysUser.getRealName() : sysUser.getUsername();
			popCorporationService.updateShopCategory(orgId, shopCategory, sysUser.getId(), user);
			return ResponseVo.successResult(true);
		}catch (Exception e){
			LOG.error("更新商圈分类异常orgId:{},shopCategory:{}",orgId,shopCategory, e);
			return ResponseVo.errRest("更新商圈分类异常");
		}
	}

}
