package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.QueryCorporationParam;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RequestMapping("/corporationExport/async")
@RestController
public class CorporationExportController extends BaseController {

    @Autowired
    private DownloadRemote downloadRemote;

    @ResponseBody
    @GetMapping(value = "/export")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> export(QueryCorporationParam queryDto) {
        List<Long> provIds = getProvIds(queryDto.getProvId());
        if(CollectionUtils.isEmpty(provIds)){
            return ResponseVo.errRest("导出失败，暂无导出数据");
        }
        log.info("CorporationExportController.export#queryDto:{}", JSON.toJSONString(queryDto));
        queryDto.setProvIds(provIds);
        try {
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.CORPORATION_AUTHENTICATION)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("CorporationExportController.export#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        }catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("CorporationExportController.export#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

}
