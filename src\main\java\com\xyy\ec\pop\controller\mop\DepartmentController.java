package com.xyy.ec.pop.controller.mop;

import com.xyy.ec.pop.adapter.mop.DepartmentAdapter;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.utils.mop.MopDataFillerUtils;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.parameter.DepartmentParame;
import com.xyy.pop.mop.api.remote.result.DepartmentTreeDTO;
import com.xyy.scm.constant.foundation.TreeBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@RequestMapping("/mop/dept")
@RestController
public class DepartmentController extends BaseController  {

    @Resource
    private DepartmentAdapter departmentAdapter;

    @PostMapping("/batch")
    public ResponseVo<Boolean> add(@RequestBody List<DepartmentParame> departmentParams) {
        for (DepartmentParame departmentParam : departmentParams) {
            MopDataFillerUtils.fillData(getUser(),departmentParam,false);
        }
        return departmentAdapter.batchUpdateDepartments(departmentParams);
    }


    @GetMapping("/tree")
    public ResponseVo<DepartmentTreeDTO> getDepartmentTree() {
        return departmentAdapter.getDepartmentTree();
    }
}
