package com.xyy.ec.pop.ecToPop;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * Ec与pop的店铺映射处理类（pop展示自营商品专用）
 */
@Slf4j
@Component
public class EcToPopCorporationService {
    @Value("${ec.to.pop.orgId.json:}")
    private String ecToPopOrgIdJson;

    /**
     * 是否包含ec店铺
     * @param isThirdCompany
     * @return
     */
    public boolean isContainsEc(Integer isThirdCompany){
        if(null != isThirdCompany && Objects.equals(isThirdCompany, 1)){
            return false;
        }
        return true;
    }
    /**
     * 获取ec-pop店铺orgId对应关系map （key：ecOrgId value:popOrgId）
     * @return
     */
    public Map<String, String> getEcToPopOrgIdMap(){
        if(log.isDebugEnabled()){
            log.debug("EcToPopCorporationService.getEcToPopOrgIdMap ecToPopOrgIdJson:{}", ecToPopOrgIdJson);
        }
        if(StringUtils.isEmpty(ecToPopOrgIdJson)){
            return Maps.newHashMap();
        }
        JSONArray jsonArray = JSONObject.parseArray(ecToPopOrgIdJson);
        if(null == jsonArray || jsonArray.size() == 0){
            return Maps.newHashMap();
        }
        Map<String, String> retMap = Maps.newHashMap();
        for(int i=0;i<jsonArray.size();i++){
            JSONObject jo = jsonArray.getJSONObject(i);
            String ecOrgId = jo.getString("ecOrgId");
            String popOrgId = jo.getString("popOrgId");
            retMap.put(ecOrgId, popOrgId);
        }
        if(log.isDebugEnabled()){
            log.debug("EcToPopCorporationService.getEcToPopOrgIdMap ecToPopOrgIdJson:{}, retMap:{}", ecToPopOrgIdJson, JSONObject.toJSONString(retMap));
        }
        return retMap;
    }

}
