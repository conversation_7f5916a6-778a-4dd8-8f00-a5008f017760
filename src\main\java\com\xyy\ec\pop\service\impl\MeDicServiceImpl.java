package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.product.api.PopSkuMeProductApi;
import com.xyy.ec.pop.service.MeDicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
@Slf4j
public class MeDicServiceImpl implements MeDicService {

    @Reference
    private PopSkuMeProductApi popSkuMeProductApi;


    public List<String> productUnitList() throws ServiceException {
        try {
            log.info("MeDicServiceImpl.productUnitList");
            ApiRPCResult<List<String>> result = popSkuMeProductApi.productUnit();
            log.info("MeDicServiceImpl.productUnitList return {}", JSON.toJSONString(result));
            if (result.isFail()) {
                throw new ServiceException("查询商品单位异常");
            }
            return result.getData();
        } catch (Exception e) {
            log.error("MeDicServiceImpl.productUnitList# 异常", e);
            throw new ServiceException("查询商品单位异常");
        }
    }
}
