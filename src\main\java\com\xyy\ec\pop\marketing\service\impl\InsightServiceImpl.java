package com.xyy.ec.pop.marketing.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.xyy.address.api.BaseRegionBusinessApi;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.address.dto.XyyRegionParams;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.insight.api.InsightChosenCustomerAdminApi;
import com.xyy.ec.marketing.insight.enums.MarketCustomerMerchantJoinTypeEnum;
import com.xyy.ec.marketing.insight.enums.MarketCustomerNewManTypeEnum;
import com.xyy.ec.marketing.insight.params.MarketCustomerGroupAdminQueryParam;
import com.xyy.ec.marketing.insight.params.MarketCustomerGroupMerchantQueryParam;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupConditionAreaDTO;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupContentBundleDTO;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupDTO;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupMerchantInfoDTO;
import com.xyy.ec.merchant.bussiness.enums.BusinessTypeEnum;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.marketing.dto.InsightLabelDTO;
import com.xyy.ec.pop.marketing.helpers.CustomerGroupQueryParamHelper;
import com.xyy.ec.pop.marketing.helpers.MarketCustomerGroupAdminQueryParamHelper;
import com.xyy.ec.pop.marketing.param.CustomerGroupQueryParam;
import com.xyy.ec.pop.marketing.service.InsightService;
import com.xyy.ec.pop.marketing.vo.CustomerGroupVO;
import com.xyy.ec.pop.remote.LabelQueryRPCService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InsightServiceImpl implements InsightService {

    private static final Joiner COMMA_JOINER = Joiner.on(",");

    @Reference(version = "1.0.0")
    private BaseRegionBusinessApi baseRegionBusinessApi;

    @Reference(version = "1.0.0")
    private InsightChosenCustomerAdminApi insightChosenCustomerAdminApi;

    @Autowired
    private LabelQueryRPCService labelQueryRPCService;

    @Override
    public PageInfo<CustomerGroupVO> pagingChosenGroups(CustomerGroupQueryParam customerGroupQueryParam) {
        Boolean isSuccess = CustomerGroupQueryParamHelper.validate(customerGroupQueryParam);
        if (BooleanUtils.isNotTrue(isSuccess)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        Integer pageNum = customerGroupQueryParam.getPageNum();
        Integer pageSize = customerGroupQueryParam.getPageSize();
        MarketCustomerGroupAdminQueryParam marketCustomerGroupAdminQueryParam = MarketCustomerGroupAdminQueryParamHelper.create(customerGroupQueryParam);
        ApiRPCResult<PageInfo<MarketCustomerGroupDTO>> apiRPCResult = insightChosenCustomerAdminApi.listChosenCustomer(marketCustomerGroupAdminQueryParam, pageNum, pageSize);
        if (!apiRPCResult.isSuccess()) {
            String message = MessageFormat.format("【选人】分页查询人群信息异常，入参，marketCustomerGroupAdminQueryParam：{0}，pageNum：{1}，pageSize：{2}",
                    JSONObject.toJSONString(marketCustomerGroupAdminQueryParam), String.valueOf(pageNum), String.valueOf(pageSize));
            throw new PopAdminException(message, XyyJsonResultCodeEnum.FAIL, apiRPCResult.getMsg());
        }
        PageInfo<CustomerGroupVO> result = new PageInfo<>(Lists.newArrayList());
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setTotal(0L);
        result.setPages(0);
        PageInfo<MarketCustomerGroupDTO> pageInfo = apiRPCResult.getData();
        List<MarketCustomerGroupDTO> marketCustomerGroupDTOS = pageInfo.getList();
        if (CollectionUtils.isEmpty(marketCustomerGroupDTOS)) {
            return result;
        }
        List<CustomerGroupVO> customerGroupVOS = marketCustomerGroupDTOS.stream()
                .map(this::createCustomerGroupVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setTotal(pageInfo.getTotal());
        result.setPages(pageInfo.getPages());
        result.setList(customerGroupVOS);
        return result;
    }

    @Override
    public PageInfo<MarketCustomerGroupMerchantInfoDTO> pagingCustomerGroupMerchants(MarketCustomerGroupMerchantQueryParam queryParam,
                                                                                     int pageNum, int pageSize) {
        ApiRPCResult<PageInfo<MarketCustomerGroupMerchantInfoDTO>> apiRPCResult = insightChosenCustomerAdminApi
                .pagingListCustomerGroupMerchantInfos(queryParam, pageNum, pageSize);
        if (!apiRPCResult.isSuccess()) {
            String message = MessageFormat.format("分页查询人群的药店信息失败，入参，queryParam：{0}，pageNum：{1}，pageSize：{2}",
                    JSONObject.toJSONString(queryParam), String.valueOf(pageNum), String.valueOf(pageSize));
            throw new PopAdminException(message, XyyJsonResultCodeEnum.FAIL, apiRPCResult.getMsg());
        }
        return apiRPCResult.getData();
    }

    private CustomerGroupVO createCustomerGroupVO(MarketCustomerGroupDTO marketCustomerGroupDTO) {
        if (Objects.isNull(marketCustomerGroupDTO)) {
            return null;
        }
        String createTimeStr = Objects.nonNull(marketCustomerGroupDTO.getCreateTime())
                ? DateFormatUtils.format(marketCustomerGroupDTO.getCreateTime(), "yyyy-MM-dd HH:mm:ss") : "";
        CustomerGroupVO customerGroupVO = CustomerGroupVO.builder()
                .id(marketCustomerGroupDTO.getId())
                .tagName(marketCustomerGroupDTO.getGroupName())
                .createTime(marketCustomerGroupDTO.getCreateTime())
                .createTimeStr(createTimeStr)
                .contentBundleDescriptions(this.getContentBundleDescriptions(marketCustomerGroupDTO))
                .specifyUserDescription(this.getSpecifyUserDescription(marketCustomerGroupDTO))
                .build();
        return customerGroupVO;
    }

    /**
     * 组描述
     *
     * @param marketCustomerGroupDTO
     * @return
     */
    @Override
    public List<List<String>> getContentBundleDescriptions(MarketCustomerGroupDTO marketCustomerGroupDTO) {
        if (Objects.isNull(marketCustomerGroupDTO)) {
            return Lists.newArrayList();
        }
        List<MarketCustomerGroupContentBundleDTO> contentBundles = marketCustomerGroupDTO.getContentBundles();
        if (CollectionUtils.isEmpty(contentBundles)) {
            return Lists.newArrayList();
        }
        List<List<String>> result = Lists.newArrayListWithExpectedSize(16);
        try {
            List<String> bundleDescription;
            List<MarketCustomerGroupConditionAreaDTO> conditionAreas;
            String areaDef;
            List<Integer> merchantTypes;
            String merchantTypeDef;
            Integer newManType;
            List<String> newManAssignShopCodes;
            List<Long> tagIds;
            for (MarketCustomerGroupContentBundleDTO contentBundle : contentBundles) {
                bundleDescription = Lists.newArrayListWithExpectedSize(16);
                conditionAreas = contentBundle.getConditionAreas();
                areaDef = this.turnAreaForDef(conditionAreas);
                if (StringUtils.isNotEmpty(areaDef)) {
                    bundleDescription.add(areaDef);
                }
                merchantTypes = contentBundle.getMerchantTypes();
                merchantTypeDef = this.turnMerchantTypeDef(merchantTypes);
                if (StringUtils.isNotEmpty(merchantTypeDef)) {
                    bundleDescription.add(merchantTypeDef);
                }
                newManType = contentBundle.getNewManType();
                newManAssignShopCodes = contentBundle.getNewManAssignShopCodes();
                tagIds = contentBundle.getTagIds();
                String tagDef = this.turnTagDef(newManType, newManAssignShopCodes, tagIds);
                if (StringUtils.isNotEmpty(tagDef)) {
                    bundleDescription.add(tagDef);
                }
                if (CollectionUtils.isNotEmpty(bundleDescription)) {
                    result.add(bundleDescription);
                }
            }
        } catch (Exception e) {
            log.error("【营销】获取人群内容的组描述列表异常, marketCustomerGroupDTO：{}", JSONObject.toJSONString(marketCustomerGroupDTO), e);
        }
        return result;
    }

    /**
     * 指定用户的描述
     *
     * @param marketCustomerGroupDTO
     * @return
     */
    private String getSpecifyUserDescription(MarketCustomerGroupDTO marketCustomerGroupDTO) {
        if (Objects.isNull(marketCustomerGroupDTO)) {
            return null;
        }
        List<Long> merchantIds = marketCustomerGroupDTO.getMerchantIds();
        if (CollectionUtils.isEmpty(merchantIds)) {
            return null;
        }
        Integer merchantJoinType = marketCustomerGroupDTO.getMerchantJoinType();
        // 与UI一致
        String joinTypeShowName = "";
        if (Objects.equals(merchantJoinType, MarketCustomerMerchantJoinTypeEnum.JOIN.getType())) {
            joinTypeShowName = "参与";
        } else if (Objects.equals(merchantJoinType, MarketCustomerMerchantJoinTypeEnum.NOT_JOIN.getType())) {
            joinTypeShowName = "排除";
        }
        StringBuilder result = new StringBuilder();
        if (StringUtils.isNotEmpty(joinTypeShowName)) {
            result.append("指定ID选人：指定客户").append(joinTypeShowName);
        } else {
            result.append("指定ID选人");
        }
        result.append("；");
        return result.toString();
    }

    /**
     * 标签人群定义
     *
     * @param newManType
     * @param newManAssignShopCodes
     * @param tagIds
     * @return
     */
    private String turnTagDef(Integer newManType, List<String> newManAssignShopCodes, List<Long> tagIds) {
        if (Objects.isNull(newManType) && CollectionUtils.isEmpty(tagIds)) {
            return null;
        }
        // 标签人群定义
        List<String> tagStrList = Lists.newArrayListWithExpectedSize(16);
        if (Objects.nonNull(newManType)) {
            MarketCustomerNewManTypeEnum newManTypeEnum = MarketCustomerNewManTypeEnum.valueOfCustom(newManType);
            if (Objects.equals(MarketCustomerNewManTypeEnum.PLAT_FORM, newManTypeEnum)) {
                tagStrList.add("平台新人");
            } else if (Objects.equals(MarketCustomerNewManTypeEnum.SHOP, newManTypeEnum)) {
                newManAssignShopCodes = Objects.isNull(newManAssignShopCodes) ? Lists.newArrayList() : newManAssignShopCodes;
                tagStrList.add("店铺新人[" + COMMA_JOINER.join(newManAssignShopCodes) + "]");
            } else {
                tagStrList.add("新人标签（" + newManType + "）");
            }
        }
        if (CollectionUtils.isNotEmpty(tagIds)) {
            List<InsightLabelDTO> insightLabelDTOS = labelQueryRPCService.listLabelByIds(tagIds);
            if (CollectionUtils.isEmpty(insightLabelDTOS)) {
                tagStrList.add("标签[" + COMMA_JOINER.join(tagIds) + "]");
            } else {
                for (InsightLabelDTO insightLabelDTO : insightLabelDTOS) {
                    tagStrList.add(insightLabelDTO.getName());
                }
            }
        }
        StringBuilder stringBuilder = new StringBuilder("用户标签：");
        stringBuilder.append(String.join("、", tagStrList)).append("；");
        return stringBuilder.toString();
    }

    /**
     * 地区人群定义
     *
     * @param conditionAreas
     * @return
     */
    private String turnAreaForDef(List<MarketCustomerGroupConditionAreaDTO> conditionAreas) {
        List<String> defs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(conditionAreas)) {
            List<Integer> areaCodes =
                    conditionAreas.stream().filter(obj -> obj != null).map(obj -> obj.getAreaCode()).collect(Collectors.toList());
            XyyRegionParams xyyRegionParams = new XyyRegionParams();
            xyyRegionParams.setAreaCodes(areaCodes);
            List<XyyRegionBusinessDto> xyyRegionBusinessDtos = baseRegionBusinessApi.queryRegionByAreaCodeList(xyyRegionParams);
            if (CollectionUtils.isNotEmpty(xyyRegionBusinessDtos)) {
                List<String> collect = xyyRegionBusinessDtos.stream().filter(obj -> obj != null)
                        .map(obj -> obj.getAreaName()).collect(Collectors.toList());
                defs.addAll(collect);
            }
        }
        if (CollectionUtils.isNotEmpty(defs)) {
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append("地域：");
            for (int i = 0; i < defs.size(); i++) {
                stringBuffer.append(defs.get(i));
                if (i < defs.size() - 1) {
                    stringBuffer.append("、");
                }
            }
            stringBuffer.append("；");
            return stringBuffer.toString();
        }
        return null;
    }

    /**
     * 类型人群定义
     *
     * @param merchantTypes
     * @return
     */
    private String turnMerchantTypeDef(List<Integer> merchantTypes) {
        if (CollectionUtils.isNotEmpty(merchantTypes)) {
            List<String> collect = merchantTypes.stream().filter(obj -> obj != null)
                    .map(obj -> String.valueOf(BusinessTypeEnum.maps.get(obj))).collect(Collectors.toList());
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append("客户类型：");
            for (int i = 0; i < collect.size(); i++) {
                stringBuffer.append(collect.get(i));
                if (i < collect.size() - 1) {
                    stringBuffer.append("、");
                }
            }
            stringBuffer.append("；");
            return stringBuffer.toString();
        }
        return null;
    }

}
