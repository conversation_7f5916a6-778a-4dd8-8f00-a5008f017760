package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.retry.api.RedoRetryInfoApi;
import com.xyy.ec.pop.server.api.retry.dto.RedoRetryInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/09/18/10:39
 * @Description:
 */
@Component
@Slf4j
public class RedoRetryInfoRemote {
    @Reference
    private RedoRetryInfoApi redoRetryInfoApi;

    public RedoRetryInfoDto getRetryTaskInfoById(Long id) throws ServiceException {
        try {
            log.info("RedoRetryInfoRemote.getRetryTaskInfoById request id:{}", id);
            ApiRPCResult<RedoRetryInfoDto> redoRetryInfoDtoApiRPCResult = redoRetryInfoApi.getRedoRetryById(id);
            log.info("RedoRetryInfoRemote.getRetryTaskInfoById response result:{}", JSON.toJSONString(redoRetryInfoDtoApiRPCResult));
            if (redoRetryInfoDtoApiRPCResult == null || redoRetryInfoDtoApiRPCResult.isFail()) {
                throw new ServiceException("查询任务重试记录Api失败");
            }
            return redoRetryInfoDtoApiRPCResult.getData();
        } catch (ServiceException s) {
            log.error("RedoRetryInfoRemote.getRetryTaskInfoById error ", s);
            throw s;
        } catch (Exception e) {
            log.error("RedoRetryInfoRemote.getRetryTaskInfoById error ", e);
            throw new ServiceException("查询异常");
        }
    }

    public Boolean updateRetryTask(RedoRetryInfoDto retryInfoDto) throws ServiceException {
        try {
            log.info("RedoRetryInfoRemote.updateRetryTask request retryInfoDto:{}", JSON.toJSONString(retryInfoDto));
            ApiRPCResult<Boolean> BooleanApiRPCResult = redoRetryInfoApi.updateRedoRetry(retryInfoDto);
            log.info("RedoRetryInfoRemote.updateRetryTask response result:{}", JSON.toJSONString(BooleanApiRPCResult));
            if (BooleanApiRPCResult == null || BooleanApiRPCResult.isFail()) {
                throw new ServiceException("调用更新任务重试记录Api失败");
            }
            return BooleanApiRPCResult.getData();
        } catch (ServiceException s) {
            log.error("RedoRetryInfoRemote.updateRetryTask error ", s);
            throw s;
        } catch (Exception e) {
            log.error("RedoRetryInfoRemote.updateRetryTask error ", e);
            throw new ServiceException("更新异常");
        }
    }
}
