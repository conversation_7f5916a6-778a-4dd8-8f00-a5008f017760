package com.xyy.ec.pop.erpUtil.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.server.api.erpUtil.dto.*;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/03/11/11:41
 * @Description: 任务配置数据/任务监控数据/任务执行记录
 */
public interface PopErpUtilService {
    /**
     * 获取任务配置数据
     * @param pageInfo
     * @param orgIds
     * @param status
     * @returnsearchKey
     */
    PageInfo<PopCorpTaskConfigDto> getCorpTaskConfigPage(PageInfo<PopCorpTaskConfigDto> pageInfo, List<String> orgIds, Integer status);

    /**
     * 获取监控数据
     * @param pageInfo
     * @param orgIds
     * @return
     */
    PageInfo<PopClientMonitorDto> getClientMonitorPage(PageInfo<PopClientMonitorDto> pageInfo, List<String> orgIds);

    /**
     * 获取任务执行记录数据
     * @param pageInfo
     * @param orgIds
     * @param status
     * @return
     */
    PageInfo<PopTaskRecordDto> getTaskRecordPage(PageInfo<PopTaskRecordDto> pageInfo, List<String> orgIds, Integer status);

    /**
     * 通过名字获取企业信息
     * @param name
     * @return
     */
    PopCorporationDto getCorporationByName(String name);

    /**
     * 商品表
     * @return
     */
    PageInfo<PopErpSkuToolDto> getPopErpSkuList(PageInfo<PopErpSkuToolDto> pageInfo,ErpSkuToolParamDto erpSkuToolDto);

    List<PopCorporationDto> getCorpList(String name);

    Boolean updateCorpTaskConfigStatus(List<Integer> ids,Integer status);
    Boolean delCorpTaskConfig(String orgId,Integer taskGroup);

    List<PopCorporationDto> getCorpListByNameAndOrgId(String name, String orgId);


}
