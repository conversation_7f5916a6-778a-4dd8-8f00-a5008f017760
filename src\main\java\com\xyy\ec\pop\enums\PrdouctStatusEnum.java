package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

public enum PrdouctStatusEnum {
        SALES(1, "销售中"),
        SOLD_OUT(2, "已售罄"),
        PREFERENTIAL(3, "特惠中"),
        OFF(4, "下架"),
        SECOND_KILL(5, "秒杀"),
        ON_SHELF(6, "待上架"),
        INPUTTED(7, "已录入"),
        TO_BE_AUDITED(8, "待审核"),
        AUDITED_AILED(9, "审核未通过");
        private int status;
        private String name;

        private PrdouctStatusEnum(int status, String name) {
            this.status = status;
            this.name = name;
        }
    private static Map<Integer, PrdouctStatusEnum> controlMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(PrdouctStatusEnum apEnum : PrdouctStatusEnum.values()) {
            controlMaps.put(apEnum.getStatus(), apEnum);
            maps.put(apEnum.getStatus(),apEnum.getName());
        }
    }
    public static String get(int id) {
        return controlMaps.get(id).getName();
    }

        public int getStatus() {
            return this.status;
        }

        public String getName() {
            return this.name;
        }
}
