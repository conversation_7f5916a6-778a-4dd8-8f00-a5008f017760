package com.xyy.ec.pop.service.impl;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.CorporationSaleTypeHelper;
import com.xyy.ec.pop.remote.CorporationPriceTypeRemote;
import com.xyy.ec.pop.server.api.merchant.api.enums.CorporationPriceTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.PopCorporationPriceTypeDto;
import com.xyy.ec.pop.service.CorporationPriceTypeService;
import com.xyy.ec.pop.vo.CorporationPriceTypeVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CorporationPriceTypeServiceImpl implements CorporationPriceTypeService {
    @Autowired
    private CorporationPriceTypeRemote remote;
    @Override
    public CorporationPriceTypeVo getPriceType(String orgId) {
        return CorporationSaleTypeHelper.convertToSaleVo(remote.queryByOrgId(orgId),orgId);
    }

    @Override
    public boolean updatePriceType(CorporationPriceTypeVo vo, String username) throws ServiceException {
        if(StringUtils.isEmpty(vo.getOrgId())||vo.getPriceType()==null||
                CorporationPriceTypeEnum.getByCode(vo.getPriceType())==null){
            throw new ServiceException("缺少必要参数");
        }
        PopCorporationPriceTypeDto dto = CorporationSaleTypeHelper.convertToPriceDto(vo,username);
        return remote.updateByOrgId(dto);
    }
}
