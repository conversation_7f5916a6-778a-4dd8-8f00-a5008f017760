package com.xyy.ec.pop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.config.XyyPopBaseConfig;
import com.xyy.ec.pop.remote.ProductSkuRemoteAdapter;
import com.xyy.ec.pop.remote.SkuMeRemote;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDetailDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.service.ProductReportingService;
import com.xyy.me.product.common.beans.ResponseInfo;
import com.xyy.me.product.general.api.dto.present.GeneralProductPresentDto;
import com.xyy.me.product.general.api.newer.facade.product.present.NewerProductPresentGenApi;
import com.xyy.me.product.general.api.newer.vo.present.GeneralProductPresentNewVo;
import com.xyy.me.product.general.api.vo.correction.GeneralProductCorrectMediaVo;
import com.xyy.me.product.general.api.vo.product.GeneralMatchProduct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 新品上报接口
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductReportingServiceImpl implements ProductReportingService {
    @Autowired
    private ProductSkuRemoteAdapter productSkuRemoteAdapter;
    @Autowired
    private XyyPopBaseConfig popBaseConfig;
    @Reference(version = "1.0.0",registry = "me")
    private NewerProductPresentGenApi newerProductPresentGenApi;
    @Autowired
    private SkuMeRemote skuMeRemote;
    @Value("${pop-base.bigDescImgUrlPrefix}")
    private String  bigDescImgUrlPrefix;;
    @Value("${product.default.picture}")
    private String defaultPic;
    private byte source = 4;
    private String outSideStart = "POP_";
    @Override
    public String report(String barcode,String userName) {
        PopSkuDetailDto sku = productSkuRemoteAdapter.getDetailByBarcode(barcode);
        if(sku==null|| StringUtils.isNotEmpty(sku.getPopSku().getStandardProductId())){
            log.warn("新品上报没有查询到商品信息:skuId:{}",barcode);
            return null;
        }
//        //先进行一次匹配
//        GeneralMatchProduct product = convertToGeneralMatchProduct(sku.getPopSku());
//        GeneralMatchProductDto dto = meProductApiRemote.getGeneralMatchProduct(product);
//        if(dto!=null&&product.getBusinessCode().equals(dto.getOldBusinessCode())&&StringUtils.isNotEmpty(dto.getProductId())){
//            return dto.getProductId();
//        }
        // 新品上报
        GeneralProductPresentNewVo presentVo=convertToPresentNewVo(sku,userName,popBaseConfig.getBigImgUrlPrefix(),bigDescImgUrlPrefix);
        List<GeneralProductPresentDto> list = report(presentVo);
        if(list==null){
            log.warn("新品上报失败:barcode:{}",barcode);
            return null;
        }
        String standId = getStandId(list,sku.getPopSku());
        return standId;
    }

    private String getStandId(List<GeneralProductPresentDto> list, PopSkuDto sku) {
        //中台承诺只会返回一个
        if(list.size()>1){
            log.error("新品上报返回了多个值。popSkuDto:{},中台数据{}",JSON.toJSONString(sku),JSON.toJSONString(list));
            //todo 微信告警
            return null;
        }else if(list.size()==1){
            return list.get(0).getProductId();
        }
        return null;
    }

    public GeneralMatchProduct convertToGeneralMatchProduct(PopSkuDto dto) {
        GeneralMatchProduct product =new GeneralMatchProduct();
        product.setBusinessCode(UUID.randomUUID().toString());
        product.setGeneralName(dto.getCommonName());
        product.setApprovalNo(dto.getApprovalNumber()==null?"-":dto.getApprovalNumber());
        product.setManufacturerName(dto.getManufacturer());
        product.setSpec(dto.getSpec());
        product.setSmallPackageCode(dto.getCode()==null?"0":dto.getCode());
        return product;
    }

    public GeneralProductPresentNewVo convertToPresentNewVo(PopSkuDetailDto popProductDetailDto, String userName,String bigImgUrlPrefix, String bigDescImgUrlPrefix) {
        PopSkuDto popSkuDto=popProductDetailDto.getPopSku();
        GeneralProductPresentNewVo vo = new GeneralProductPresentNewVo();
        vo.setTraceId(outSideStart+ popSkuDto.getBarcode()+ "_"+ DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        vo.setSource(source);
        vo.setCreateUser(userName);
        Integer cate = popProductDetailDto.getPopSkuCategory()==null?null:NumberUtils.toInt(popProductDetailDto.getPopSkuCategory().getBusinessFirstCategoryCode());
        Integer spuCategory = skuMeRemote.getSpuCategory(cate, popSkuDto.getApprovalNumber());
        vo.setSpuCategory(spuCategory==null?4:spuCategory);
        vo.setGeneralName(popSkuDto.getCommonName());
        vo.setApprovalNo(popSkuDto.getApprovalNumber());
        vo.setManufacturerName(popSkuDto.getManufacturer());
        vo.setOriginPlace(popSkuDto.getProducer());
        vo.setSpec(popSkuDto.getSpec());
        vo.setSmallPackageCode(popSkuDto.getCode());
        vo.setDosageFormName(popSkuDto.getDosageForm());
        vo.setPackageUnitName(popSkuDto.getProductUnit());
        vo.setPrescriptionCategory(popSkuDto.getDrugClassification());
        String term = popSkuDto.getTerm()!=null&&popSkuDto.getTerm().endsWith("月")?popSkuDto.getTerm().substring(0,popSkuDto.getTerm().length()-1):popSkuDto.getTerm();
        vo.setValidity(NumberUtils.toShort(term));
        List<GeneralProductCorrectMediaVo> medias = popProductDetailDto.getPopSkuQualifications().stream().map(item->{
            GeneralProductCorrectMediaVo mediaVo = new GeneralProductCorrectMediaVo();
            mediaVo.setMediaType((byte)1);
            mediaVo.setMediaUrl(item.getUrl());

            return mediaVo;
        }).collect(Collectors.toList());
        vo.setApprovalImgList(medias);
        vo.setProductName(popSkuDto.getProductName());
        vo.setBusinessScopeMulti(popSkuDto.getSkuCategoryId()+"");
        vo.setOutsideCode(vo.getTraceId());
        //外包装图片
        List<String> images = getImageUrl(bigImgUrlPrefix,popSkuDto.getImageUrl());
        images.addAll(getImageUrl(bigImgUrlPrefix,popSkuDto.getImageListUrl()));
        List<GeneralProductCorrectMediaVo> outPackageImgList = images.stream().map(item->{
            GeneralProductCorrectMediaVo mediaVo = new GeneralProductCorrectMediaVo();
            mediaVo.setMediaType((byte)2);
            mediaVo.setMediaUrl(item);
            return mediaVo;
        }).collect(Collectors.toList());
        vo.setOutPackageImgList(outPackageImgList);
        //说明书图片
        List<GeneralProductCorrectMediaVo> directionImgList = getImageUrl(bigDescImgUrlPrefix,popProductDetailDto.getPopSkuInstructionImages()==null?null:popProductDetailDto.getPopSkuInstructionImages().getInstrutionImageUrl()).stream().map(item->{
            GeneralProductCorrectMediaVo mediaVo = new GeneralProductCorrectMediaVo();
            mediaVo.setMediaType((byte)3);
            mediaVo.setMediaUrl(item);
            return mediaVo;
        }).collect(Collectors.toList());
        vo.setDirectionImgList(directionImgList);
        return vo;
    }

    private List<String> getImageUrl(String prefix, String imageUrl) {
        if(StringUtils.isEmpty(imageUrl)){
            return new ArrayList<String>();
        }
        return Arrays.asList(imageUrl.split(",")).stream().filter(item->StringUtils.isNotEmpty(item)&&!defaultPic.equals(item)).map(item->prefix+item).collect(Collectors.toList());
    }
    /**
     * 新品上报
     * @param presentVo
     * @return
     */
    public List<GeneralProductPresentDto> report(GeneralProductPresentNewVo presentVo) {
        try{
            log.info("MeProductApiRemote.report#presentVo:{}", JSON.toJSONString(presentVo));
            if (StringUtils.isBlank(presentVo.getApprovalNo())){
                presentVo.setApprovalNo("-");
            }
            ResponseInfo<List<GeneralProductPresentDto>> res = newerProductPresentGenApi.addProductPresent(presentVo);
            log.info("MeProductApiRemote.report#presentVo:{} return {}",JSON.toJSONString(presentVo),JSON.toJSONString(res));
            if(res.isSuccess()){
                return res.getData()==null?new ArrayList<>(0):res.getData();
            }
            log.warn("新品上报异常：traceId：{},返回值{}",presentVo.getTraceId(),JSON.toJSONString(res));
            return null;
        }catch (Exception e){
            log.error("MeProductApiRemote.report#presentVo:{} return {}",JSON.toJSONString(presentVo),e);
            log.warn("新品上报异常：traceId：{},异常：{}",presentVo.getTraceId(),e.getMessage());
            return null;
        }
    }
}
