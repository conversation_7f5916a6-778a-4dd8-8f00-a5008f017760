package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAreaAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationSettleCycleApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationSettleCycle;
import com.xyy.ec.pop.vo.CorporationSettleCycleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 封装dubbo接口，获取商户 结算周期信息
 */
@Slf4j
@Component
public class CorporationSettleCycleAdapter {
    @Reference
    private CorporationSettleCycleApi corporationSettleCycleApi;

    public CorporationSettleCycle findSettleCycle(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return null;
        }
        try {
            log.info("findSettleCycle request:{}", orgId);
            ApiRPCResult<CorporationSettleCycle> result = corporationSettleCycleApi.findSettleCycle(orgId);
            log.info("findSettleCycle response:{}", JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("findSettleCycle error:{}", e.getMessage(), e);
            return null;
        }
    }

    public void updateSettleCycle(String orgId, Integer settleCycle, Integer settleValue, String operator) {
        try {
            corporationSettleCycleApi.updateSettleCycle(orgId, settleCycle, settleValue, operator);
        } catch (Exception e) {
            log.error("CorporationSettleCycleAdapter.updateSettleCycle error:{}", e.getMessage(), e);
            throw new PopAdminException("更新失败");
        }
    }
}
