<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.ec.pop.dao.PopBillMapper">
  <resultMap id="BaseResultMap" type="com.xyy.ec.pop.po.PopBillPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="product_money" jdbcType="DECIMAL" property="productMoney" />
    <result column="total_money" jdbcType="DECIMAL" property="totalMoney" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="freight_amount" jdbcType="DECIMAL" property="freightAmount" />
    <result column="coupon_shop_amount" jdbcType="DECIMAL" property="couponShopAmount" />
    <result column="marketing_shop_amount" jdbcType="DECIMAL" property="marketingShopAmount" />
    <result column="shop_total_discount" jdbcType="DECIMAL" property="shopTotalDiscount" />
    <result column="coupon_platform_amount" jdbcType="DECIMAL" property="couponPlatformAmount" />
    <result column="marketing_platform_amount" jdbcType="DECIMAL" property="marketingPlatformAmount" />
    <result column="platform_total_discount" jdbcType="DECIMAL" property="platformTotalDiscount" />
    <result column="hire_money" jdbcType="DECIMAL" property="hireMoney" />
    <result column="payable_commission" jdbcType="DECIMAL" property="payableCommission" />
    <result column="deducted_commission" jdbcType="DECIMAL" property="deductedCommission" />
    <result column="deducted" jdbcType="TINYINT" property="deducted" />
    <result column="settlement_type" jdbcType="TINYINT" property="settlementType" />
    <result column="penalty_amount" jdbcType="DECIMAL" property="penaltyAmount" />
    <result column="statement_total_money" jdbcType="DECIMAL" property="statementTotalMoney" />
    <result column="pay_type" jdbcType="TINYINT" property="payType" />
    <result column="bill_create_time" jdbcType="TIMESTAMP" property="billCreateTime" />
    <result column="bill_payment_status" jdbcType="TINYINT" property="billPaymentStatus" />
    <result column="bill_payment_time" jdbcType="TIMESTAMP" property="billPaymentTime" />
    <result column="remit_status" jdbcType="TINYINT" property="remitStatus" />
    <result column="remit_time" jdbcType="TIMESTAMP" property="remitTime" />
    <result column="invoice_status" jdbcType="TINYINT" property="invoiceStatus" />
    <result column="invoice_time" jdbcType="DATE" property="invoiceTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bill_share_status" jdbcType="TINYINT" property="billShareStatus" />
    <result column="bill_share_time" jdbcType="TIMESTAMP" property="billShareTime" />
    <result column="bill_share_result" jdbcType="VARCHAR" property="billShareResult" />
    <result column="payment_channel" jdbcType="INTEGER" property="paymentChannel" />
    <result column="prov_id" property="provId"/>
    <result column="prov" property="prov"/>
    <result column="actual_commission_money" jdbcType="DECIMAL" property="actualCommissionMoney"/>
    <result column="commission_discount_money" jdbcType="DECIMAL" property="commissionDiscountMoney"/>
    <result column="cash_pay_amount" jdbcType="DECIMAL" property="cashPayAmount" />
    <result column="virtual_gold" jdbcType="DECIMAL" property="virtualGold" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_id, org_name, bill_no, product_money, total_money, money, freight_amount, 
    coupon_shop_amount, marketing_shop_amount, shop_total_discount, coupon_platform_amount, 
    marketing_platform_amount, platform_total_discount, hire_money,payable_commission,deducted_commission,deducted,settlement_type, penalty_amount, statement_total_money,
    pay_type, bill_create_time, bill_payment_status, bill_payment_time, remit_status, 
    remit_time,invoice_status,invoice_time, create_time, update_time,
    bill_share_status, bill_share_time, bill_share_result, payment_channel, prov_id, prov,
    actual_commission_money, commission_discount_money, cash_pay_amount, virtual_gold
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_xyy_pop_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tb_xyy_pop_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xyy.ec.pop.po.PopBillPo" useGeneratedKeys="true">
    insert into tb_xyy_pop_bill (org_id, org_name, bill_no, 
      product_money, total_money, money, 
      freight_amount, coupon_shop_amount, marketing_shop_amount, 
      shop_total_discount, coupon_platform_amount, 
      marketing_platform_amount, platform_total_discount, 
      hire_money,payable_commission,settlement_type, penalty_amount, statement_total_money,
      pay_type, bill_create_time, bill_payment_status, 
      bill_payment_time, remit_status, remit_time, remit_time,invoice_status,invoice_time,
      create_time, update_time)
    values (#{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, #{billNo,jdbcType=VARCHAR}, 
      #{productMoney,jdbcType=DECIMAL}, #{totalMoney,jdbcType=DECIMAL}, #{money,jdbcType=DECIMAL}, 
      #{freightAmount,jdbcType=DECIMAL}, #{couponShopAmount,jdbcType=DECIMAL}, #{marketingShopAmount,jdbcType=DECIMAL}, 
      #{shopTotalDiscount,jdbcType=DECIMAL}, #{couponPlatformAmount,jdbcType=DECIMAL}, 
      #{marketingPlatformAmount,jdbcType=DECIMAL}, #{platformTotalDiscount,jdbcType=DECIMAL}, 
      #{hireMoney,jdbcType=DECIMAL},#{payableCommission,jdbcType=DECIMAL},#{settlementType,jdbcType=TINYINT},  #{penaltyAmount,jdbcType=DECIMAL}, #{statementTotalMoney,jdbcType=DECIMAL},
      #{payType,jdbcType=TINYINT}, #{billCreateTime,jdbcType=TIMESTAMP}, #{billPaymentStatus,jdbcType=TINYINT}, 
      #{billPaymentTime,jdbcType=TIMESTAMP}, #{remitStatus,jdbcType=TINYINT}, #{remitTime,jdbcType=TIMESTAMP}, 
      #{invoiceStatus,jdbcType=TINYINT}, #{invoiceTime,jdbcType=TIMESTAMP},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xyy.ec.pop.po.PopBillPo" useGeneratedKeys="true">
    insert into tb_xyy_pop_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="productMoney != null">invoice_status
        product_money,
      </if>
      <if test="totalMoney != null">
        total_money,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="freightAmount != null">
        freight_amount,
      </if>
      <if test="couponShopAmount != null">
        coupon_shop_amount,
      </if>
      <if test="marketingShopAmount != null">
        marketing_shop_amount,
      </if>
      <if test="shopTotalDiscount != null">
        shop_total_discount,
      </if>
      <if test="couponPlatformAmount != null">
        coupon_platform_amount,
      </if>
      <if test="marketingPlatformAmount != null">
        marketing_platform_amount,
      </if>
      <if test="platformTotalDiscount != null">
        platform_total_discount,
      </if>
      <if test="hireMoney != null">
        hire_money,
      </if>
      <if test="payableCommission != null">
        payable_commission,
      </if>
      <if test="settlementType != null">
        settlement_type,
      </if>
      <if test="penaltyAmount != null">
        penalty_amount,
      </if>
      <if test="statementTotalMoney != null">
        statement_total_money,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="billCreateTime != null">
        bill_create_time,
      </if>
      <if test="billPaymentStatus != null">
        bill_payment_status,
      </if>
      <if test="billPaymentTime != null">
        bill_payment_time,
      </if>
      <if test="remitStatus != null">
        remit_status,
      </if>
      <if test="remitTime != null">
        remit_time,
      </if>
      <if test="invoiceStatus != null">
        invoice_status,
      </if>
      <if test="invoiceTime != null">
        invoice_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="productMoney != null">
        #{productMoney,jdbcType=DECIMAL},
      </if>
      <if test="totalMoney != null">
        #{totalMoney,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="freightAmount != null">
        #{freightAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponShopAmount != null">
        #{couponShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingShopAmount != null">
        #{marketingShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="shopTotalDiscount != null">
        #{shopTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="couponPlatformAmount != null">
        #{couponPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingPlatformAmount != null">
        #{marketingPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="platformTotalDiscount != null">
        #{platformTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="hireMoney != null">
        #{hireMoney,jdbcType=DECIMAL},
      </if>
      <if test="payableCommission != null">
        #{payableCommission,jdbcType=DECIMAL},
      </if>
      <if test="settlementType  != null">
        #{settlementType ,jdbcType=TINYINT},
      </if>
      <if test="penaltyAmount != null">
        #{penaltyAmount,jdbcType=DECIMAL},
      </if>
      <if test="statementTotalMoney != null">
        #{statementTotalMoney,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=TINYINT},
      </if>
      <if test="billCreateTime != null">
        #{billCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billPaymentStatus != null">
        #{billPaymentStatus,jdbcType=TINYINT},
      </if>
      <if test="billPaymentTime != null">
        #{billPaymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remitStatus != null">
        #{remitStatus,jdbcType=TINYINT},
      </if>
      <if test="remitTime != null">
        #{remitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceTime != null">
        #{invoiceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xyy.ec.pop.po.PopBillPo">
    update tb_xyy_pop_bill
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="productMoney != null">
        product_money = #{productMoney,jdbcType=DECIMAL},
      </if>
      <if test="totalMoney != null">
        total_money = #{totalMoney,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="freightAmount != null">
        freight_amount = #{freightAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponShopAmount != null">
        coupon_shop_amount = #{couponShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingShopAmount != null">
        marketing_shop_amount = #{marketingShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="shopTotalDiscount != null">
        shop_total_discount = #{shopTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="couponPlatformAmount != null">
        coupon_platform_amount = #{couponPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingPlatformAmount != null">
        marketing_platform_amount = #{marketingPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="platformTotalDiscount != null">
        platform_total_discount = #{platformTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="hireMoney != null">
        hire_money = #{hireMoney,jdbcType=DECIMAL},
      </if>
      <if test="payableCommission != null">
        payable_commission =  #{payableCommission,jdbcType=DECIMAL},
      </if>
      <if test="settlementType  != null">
        settlement_type = #{settlementType ,jdbcType=TINYINT},
      </if>
      <if test="penaltyAmount != null">
        penalty_amount = #{penaltyAmount,jdbcType=DECIMAL},
      </if>
      <if test="statementTotalMoney != null">
        statement_total_money = #{statementTotalMoney,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=TINYINT},
      </if>
      <if test="billCreateTime != null">
        bill_create_time = #{billCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billPaymentStatus != null">
        bill_payment_status = #{billPaymentStatus,jdbcType=TINYINT},
      </if>
      <if test="billPaymentTime != null">
        bill_payment_time = #{billPaymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remitStatus != null">
        remit_status = #{remitStatus,jdbcType=TINYINT},
      </if>
      <if test="remitTime != null">
        remit_time = #{remitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceStatus != null">
        invoice_status = #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceTime != null">
        invoice_time = #{invoiceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xyy.ec.pop.po.PopBillPo">
    update tb_xyy_pop_bill
    set org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      bill_no = #{billNo,jdbcType=VARCHAR},
      product_money = #{productMoney,jdbcType=DECIMAL},
      total_money = #{totalMoney,jdbcType=DECIMAL},
      money = #{money,jdbcType=DECIMAL},
      freight_amount = #{freightAmount,jdbcType=DECIMAL},
      coupon_shop_amount = #{couponShopAmount,jdbcType=DECIMAL},
      marketing_shop_amount = #{marketingShopAmount,jdbcType=DECIMAL},
      shop_total_discount = #{shopTotalDiscount,jdbcType=DECIMAL},
      coupon_platform_amount = #{couponPlatformAmount,jdbcType=DECIMAL},
      marketing_platform_amount = #{marketingPlatformAmount,jdbcType=DECIMAL},
      platform_total_discount = #{platformTotalDiscount,jdbcType=DECIMAL},
      hire_money = #{hireMoney,jdbcType=DECIMAL},
      payable_commission =  #{payableCommission,jdbcType=DECIMAL},
      settlement_type = #{settlementType ,jdbcType=TINYINT},
      penalty_amount = #{penaltyAmount,jdbcType=DECIMAL},
      statement_total_money = #{statementTotalMoney,jdbcType=DECIMAL},
      pay_type = #{payType,jdbcType=TINYINT},
      bill_create_time = #{billCreateTime,jdbcType=TIMESTAMP},
      bill_payment_status = #{billPaymentStatus,jdbcType=TINYINT},
      bill_payment_time = #{billPaymentTime,jdbcType=TIMESTAMP},
      remit_status = #{remitStatus,jdbcType=TINYINT},
      remit_time = #{remitTime,jdbcType=TIMESTAMP},
      invoice_status = #{invoiceStatus,jdbcType=TINYINT},
      invoice_time = #{invoiceTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryPopBillList" resultMap="BaseResultMap" parameterType="com.xyy.ec.pop.vo.settle.PopBillVo">
    SELECT
    <include refid="Base_Column_List"/>
    FROM tb_xyy_pop_bill
    <where>
      <if test="popBill.payType != null">
        AND pay_type = #{popBill.payType,jdbcType=TINYINT}
      </if>
      <if test="popBill.payTypes != null and popBill.payTypes.size() >0 ">
        AND pay_type in
        <foreach collection="popBill.payTypes" item="payType" open="(" close=")" separator=",">
          #{payType}
        </foreach>
      </if>
      <if test="null != popBill.orgId and '' != popBill.orgId">
        AND org_id = #{popBill.orgId,jdbcType=VARCHAR}
      </if>
      <if test="null != popBill.orgName and '' != popBill.orgName">
        AND org_name like CONCAT('%',#{popBill.orgName},'%')
      </if>
      <if test="null != popBill.billNo and '' != popBill.billNo">
        AND bill_no = #{popBill.billNo,jdbcType=VARCHAR}
      </if>
      <if test="null != popBill.invoiceStatus and popBill.invoiceStatus!=2">
        AND invoice_status = #{popBill.invoiceStatus,jdbcType=TINYINT}
        AND bill_no not in (select bill_no
          from tb_xyy_pop_commission_settle cs
          inner join tb_xyy_pop_commission_settle_detail csd on cs.hire_no = csd.hire_no
          where
          hire_money = 0)
      </if>
      <if test="null != popBill.billPaymentStatus">
        AND bill_payment_status = #{popBill.billPaymentStatus,jdbcType=TINYINT}
      </if>
      <if test="null != popBill.remitStatus">
        AND remit_status = #{popBill.remitStatus,jdbcType=TINYINT}
      </if>
      <if test="popBill.startRemitTime != null">
        AND remit_time >= #{popBill.startRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endRemitTime != null">
        AND remit_time &lt;= #{popBill.endRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.startCreateTime != null">
        AND bill_create_time >= #{popBill.startCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endCreateTime != null">
        AND bill_create_time &lt;= #{popBill.endCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.startInvoiceTime != null">
        AND invoice_time >= #{popBill.startInvoiceTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endInvoiceTime != null">
        AND invoice_time &lt;= #{popBill.endInvoiceTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.startBillPaymentTime != null">
        AND bill_payment_time >= #{popBill.startBillPaymentTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endBillPaymentTime != null">
        AND bill_payment_time &lt;= #{popBill.endBillPaymentTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.settlementType != null and popBill.settlementType != 0">
        AND settlement_type = #{popBill.settlementType, jdbcType=TINYINT}
      </if>
      <if test="popBill.orgIds != null and popBill.orgIds.size() &gt; 0">
        AND org_id in
        <foreach collection="popBill.orgIds" item="orgId" open="(" close=")" separator=",">
          #{orgId}
        </foreach>
      </if>
      <if test="null != popBill.invoiceStatus and popBill.invoiceStatus==2">
          and bill_no in (select bill_no
          from tb_xyy_pop_commission_settle cs
          inner join tb_xyy_pop_commission_settle_detail csd on cs.hire_no = csd.hire_no
          where hire_money = 0)
      </if>
      <if test="popBill.billShareStatus != null ">
        and bill_share_status = #{popBill.billShareStatus}
      </if>
      <if test="popBill.paymentChannel != null ">
        and payment_channel = #{popBill.paymentChannel}
      </if>
      <if test="popBill.provIds != null and popBill.provIds.size() != 0">
        AND prov_id in
        <foreach collection="popBill.provIds" item="provId" open="(" separator="," close=")">
          #{provId}
        </foreach>
      </if>
      <if test="popBill.deducted != null">
        AND deducted = #{popBill.deducted,jdbcType=TINYINT}
      </if>
      <if test="popBill.startBillShareTime != null">
        AND bill_share_time >= #{popBill.startBillShareTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endBillShareTime != null">
        AND bill_share_time &lt;= #{popBill.endBillShareTime, jdbcType=TIMESTAMP}
      </if>
    </where>
      order by create_time desc,id desc
    <if test="pageNum != null and pageSize != null">
      limit #{pageNum, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
    </if>
  </select>
  <select id="queryPopBillListCount" resultType="java.lang.Long"  parameterType="com.xyy.ec.pop.vo.settle.PopBillVo">
    select count(id) from tb_xyy_pop_bill
    <where>
      <if test="popBill.payType != null">
        AND pay_type = #{popBill.payType,jdbcType=TINYINT}
      </if>
      <if test="popBill.payTypes != null and popBill.payTypes.size() >0 ">
        AND pay_type in
        <foreach collection="popBill.payTypes" item="payType" open="(" close=")" separator=",">
          #{payType}
        </foreach>
      </if>
      <if test="null != popBill.orgId and '' != popBill.orgId">
        AND org_id = #{popBill.orgId,jdbcType=VARCHAR}
      </if>
      <if test="null != popBill.orgName and '' != popBill.orgName">
        AND org_name like CONCAT('%',#{popBill.orgName},'%')
      </if>
      <if test="null != popBill.billNo and '' != popBill.billNo">
        AND bill_no = #{popBill.billNo,jdbcType=VARCHAR}
      </if>
      <if test="null != popBill.invoiceStatus and popBill.invoiceStatus!=2">
        AND invoice_status = #{popBill.invoiceStatus,jdbcType=TINYINT}
          AND bill_no not in (select bill_no
          from tb_xyy_pop_commission_settle cs
          inner join tb_xyy_pop_commission_settle_detail csd on cs.hire_no = csd.hire_no
          where
          hire_money = 0)
      </if>
      <if test="null != popBill.billPaymentStatus">
        AND bill_payment_status = #{popBill.billPaymentStatus,jdbcType=TINYINT}
      </if>
      <if test="null != popBill.remitStatus">
        AND remit_status = #{popBill.remitStatus,jdbcType=TINYINT}
      </if>
      <if test="popBill.startRemitTime != null">
        AND remit_time >= #{popBill.startRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endRemitTime != null">
        AND remit_time &lt;= #{popBill.endRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.startCreateTime != null">
        AND bill_create_time >= #{popBill.startCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endCreateTime != null">
        AND bill_create_time &lt;= #{popBill.endCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.startInvoiceTime != null">
        AND invoice_time >= #{popBill.startInvoiceTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endInvoiceTime != null">
        AND invoice_time &lt;= #{popBill.endInvoiceTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.startBillPaymentTime != null">
        AND bill_payment_time >= #{popBill.startBillPaymentTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endBillPaymentTime != null">
        AND bill_payment_time &lt;= #{popBill.endBillPaymentTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.settlementType != null and popBill.settlementType != 0">
        AND settlement_type = #{popBill.settlementType, jdbcType=TINYINT}
      </if>
      <if test="popBill.orgIds != null and popBill.orgIds.size() &gt; 0">
        AND org_id in
        <foreach collection="popBill.orgIds" item="orgId" open="(" close=")" separator=",">
          #{orgId}
        </foreach>
      </if>
        <if test="null != popBill.invoiceStatus and popBill.invoiceStatus==2">
            and bill_no in (select bill_no
            from tb_xyy_pop_commission_settle cs
            inner join tb_xyy_pop_commission_settle_detail csd on cs.hire_no = csd.hire_no
            where hire_money = 0)
        </if>
      <if test="popBill.billShareStatus != null ">
        and bill_share_status = #{popBill.billShareStatus}
      </if>
      <if test="popBill.paymentChannel != null ">
        and payment_channel = #{popBill.paymentChannel}
      </if>
      <if test="popBill.provIds != null and popBill.provIds.size() != 0">
        AND prov_id in
        <foreach collection="popBill.provIds" item="provId" open="(" separator="," close=")">
          #{provId}
        </foreach>
      </if>
      <if test="popBill.deducted != null">
        AND deducted = #{popBill.deducted,jdbcType=TINYINT}
      </if>
      <if test="popBill.startBillShareTime != null">
        AND bill_share_time >= #{popBill.startBillShareTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endBillShareTime != null">
        AND bill_share_time &lt;= #{popBill.endBillShareTime, jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>

  <select id="queryPopBillStatis" resultType="com.xyy.ec.pop.vo.settle.PopBillStatisVo"  parameterType="com.xyy.ec.pop.vo.settle.PopBillVo">
    select SUM(CASE WHEN settlement_type = 1 THEN hire_money ELSE payable_commission END) hireMoneyTotal,SUM(statement_total_money) statementTotalMoneyTotal,
           SUM(actual_commission_money) actualCommissionMoneyTotal,SUM(commission_discount_money) commissionDiscountMoneyTotal,
           SUM(deducted_commission) deductedCommissionTotal
    from tb_xyy_pop_bill
    WHERE 1=1
    <if test="popBill.payType != null">
      AND pay_type = #{popBill.payType,jdbcType=TINYINT}
    </if>
    <if test="popBill.payTypes != null and popBill.payTypes.size() >0 ">
      AND pay_type in
      <foreach collection="popBill.payTypes" item="payType" open="(" close=")" separator=",">
        #{payType}
      </foreach>
    </if>
      <if test="null != popBill.orgId and '' != popBill.orgId">
        AND org_id = #{popBill.orgId,jdbcType=VARCHAR}
      </if>
      <if test="null != popBill.orgName and '' != popBill.orgName">
        AND org_name like CONCAT('%',#{popBill.orgName},'%')
      </if>
      <if test="null != popBill.billNo and '' != popBill.billNo">
        AND bill_no = #{popBill.billNo,jdbcType=VARCHAR}
      </if>
      <if test="null != popBill.invoiceStatus and popBill.invoiceStatus!=2">
        AND invoice_status = #{popBill.invoiceStatus,jdbcType=TINYINT}
          AND bill_no not in (select bill_no
          from tb_xyy_pop_commission_settle cs
          inner join tb_xyy_pop_commission_settle_detail csd on cs.hire_no = csd.hire_no
          where
          hire_money = 0)
      </if>
      <if test="null != popBill.billPaymentStatus">
        AND bill_payment_status = #{popBill.billPaymentStatus,jdbcType=TINYINT}
      </if>
    <if test="null != popBill.remitStatus">
      AND remit_status = #{popBill.remitStatus,jdbcType=TINYINT}
    </if>
    <if test="popBill.startRemitTime != null">
      AND remit_time >= #{popBill.startRemitTime, jdbcType=TIMESTAMP}
    </if>
    <if test="popBill.endRemitTime != null">
      AND remit_time &lt;= #{popBill.endRemitTime, jdbcType=TIMESTAMP}
    </if>
      <if test="popBill.startCreateTime != null">
        AND bill_create_time >= #{popBill.startCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endCreateTime != null">
        AND bill_create_time &lt;= #{popBill.endCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.startInvoiceTime != null">
        AND invoice_time >= #{popBill.startInvoiceTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endInvoiceTime != null">
        AND invoice_time &lt;= #{popBill.endInvoiceTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.startBillPaymentTime != null">
        AND bill_payment_time >= #{popBill.startBillPaymentTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endBillPaymentTime != null">
        AND bill_payment_time &lt;= #{popBill.endBillPaymentTime, jdbcType=TIMESTAMP}
      </if>
    <if test="popBill.settlementType != null and popBill.settlementType != 0">
      AND settlement_type = #{popBill.settlementType, jdbcType=TINYINT}
    </if>
      <if test="null != popBill.invoiceStatus and popBill.invoiceStatus==2">
          and bill_no in (select bill_no
          from tb_xyy_pop_commission_settle cs
          inner join tb_xyy_pop_commission_settle_detail csd on cs.hire_no = csd.hire_no
          where hire_money = 0)
      </if>
    <if test="popBill.orgIds != null and popBill.orgIds.size() &gt; 0">
      AND org_id in
      <foreach collection="popBill.orgIds" item="orgId" open="(" close=")" separator=",">
        #{orgId}
      </foreach>
    </if>
    <if test="popBill.provIds != null and popBill.provIds.size() != 0">
      AND prov_id in
      <foreach collection="popBill.provIds" item="provId" open="(" separator="," close=")">
        #{provId}
      </foreach>
    </if>
    <if test="popBill.deducted != null">
      AND deducted = #{popBill.deducted,jdbcType=TINYINT}
    </if>
    <if test="popBill.startBillShareTime != null">
      AND bill_share_time >= #{popBill.startBillShareTime, jdbcType=TIMESTAMP}
    </if>
    <if test="popBill.endBillShareTime != null">
      AND bill_share_time &lt;= #{popBill.endBillShareTime, jdbcType=TIMESTAMP}
    </if>
  </select>
  <select id="selectByBillNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_xyy_pop_bill
    where bill_no = #{billNo}
  </select>
  <select id="queryByOrderNos" resultType="java.lang.String">
    SELECT bill_no as billNo
    FROM tb_xyy_pop_bill
    <where>
      <if test="null != popBill.payType">
        AND pay_type = #{popBill.payType,jdbcType=TINYINT}
      </if>
      <if test="null != popBill.orgId and '' != popBill.orgId">
        AND org_id = #{popBill.orgId,jdbcType=VARCHAR}
      </if>
      <if test="null != popBill.orgName and '' != popBill.orgName">
        AND org_name like CONCAT('%',#{popBill.orgName},'%')
      </if>
      <if test="null != popBill.billNo and '' != popBill.billNo">
        AND bill_no = #{popBill.billNo,jdbcType=VARCHAR}
      </if>
      <if test="null != popBill.invoiceStatus and popBill.invoiceStatus!=2">
        AND invoice_status = #{popBill.invoiceStatus,jdbcType=TINYINT}
          AND bill_no not in (select bill_no
          from tb_xyy_pop_commission_settle cs
          inner join tb_xyy_pop_commission_settle_detail csd on cs.hire_no = csd.hire_no
          where
          hire_money = 0)
      </if>
      <if test="null != popBill.billPaymentStatus">
        AND bill_payment_status = #{popBill.billPaymentStatus,jdbcType=TINYINT}
      </if>
      <if test="null != popBill.remitStatus">
        AND remit_status = #{popBill.remitStatus,jdbcType=TINYINT}
      </if>
      <if test="popBill.startRemitTime != null">
        AND remit_time >= #{popBill.startRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endRemitTime != null">
        AND remit_time &lt;= #{popBill.endRemitTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.startCreateTime != null">
        AND bill_create_time >= #{popBill.startCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endCreateTime != null">
        AND bill_create_time &lt;= #{popBill.endCreateTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.startInvoiceTime != null">
        AND invoice_time >= #{popBill.startInvoiceTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endInvoiceTime != null">
        AND invoice_time &lt;= #{popBill.endInvoiceTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.startBillPaymentTime != null">
        AND bill_payment_time >= #{popBill.startBillPaymentTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.endBillPaymentTime != null">
        AND bill_payment_time &lt;= #{popBill.endBillPaymentTime, jdbcType=TIMESTAMP}
      </if>
      <if test="popBill.settlementType != null and popBill.settlementType != 0">
        AND settlement_type = #{popBill.settlementType, jdbcType=TINYINT}
      </if>
        <if test="null != popBill.invoiceStatus and popBill.invoiceStatus==2">
            and bill_no in (select bill_no
            from tb_xyy_pop_commission_settle cs
            inner join tb_xyy_pop_commission_settle_detail csd on cs.hire_no = csd.hire_no
            where hire_money = 0)
        </if>
      <if test="popBill.orgIds != null and popBill.orgIds.size() &gt; 0">
        AND org_id in
        <foreach collection="popBill.orgIds" item="orgId" open="(" close=")" separator=",">
          #{orgId}
        </foreach>
      </if>
      <if test="popBill.deducted != null">
        AND deducted = #{popBill.deducted, jdbcType=TINYINT}
      </if>
    </where>
  </select>
  <select id="queryPopBillByBillNoList" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_xyy_pop_bill
    where bill_no in
    <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
  <update id="batchUpdateById">
    <if test="list != null and list.size() &gt; 0">
      <foreach collection="list" item="bill" separator=";">
        update tb_xyy_pop_bill
        <set>
          <if test="bill.invoiceStatus != null">
            invoice_status = #{bill.invoiceStatus,jdbcType=TINYINT},
          </if>
          <if test="bill.invoiceTime != null">
            invoice_time = #{bill.invoiceTime,jdbcType=TIMESTAMP},
          </if>
          <if test="bill.updateTime != null">
            update_time = #{bill.updateTime,jdbcType=TIMESTAMP},
          </if>
        </set>
        where id = #{bill.id,jdbcType=BIGINT}
      </foreach>
    </if>
  </update>
</mapper>