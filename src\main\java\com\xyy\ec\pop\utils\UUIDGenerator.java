/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.xyy.ec.pop.utils;

import java.util.UUID;

/**
 * UUID生成工具类 
 * ClassName: UUIDGenerator<br/>
 * date: 2015-9-21 下午3:06:22 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.7
 */
public class UUIDGenerator {

    /**
     * 生成32位编码
     *
     * @return string
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString().trim().replace("-", "");
    }

    /**
     * 获得指定数目的UUID
     *
     * @param number int 需要获得的UUID数量
     * @return String[] UUID数组
     */
    public static String[] generateUUID(int number) {
        if (number < 1) {
            return null;
        }
        String[] ss = new String[number];
        for (int i = 0; i < number; i++) {
            ss[i] = generateUUID();
        }
        return ss;
    }

    public static void main(String[] args) {
        String[] ss = generateUUID(10);
        for (int i = 0; i < ss.length; i++) {
            System.out.println(ss[i]);
        }
    }

}
