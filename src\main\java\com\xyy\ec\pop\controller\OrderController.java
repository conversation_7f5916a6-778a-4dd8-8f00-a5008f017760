package com.xyy.ec.pop.controller;

import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.enums.BusinessTypeEnum;
import com.xyy.ec.order.backend.framework.enums.OrderPayChannelEnum;
import com.xyy.ec.order.backend.order.query.dto.OrderDto;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderDetailBusinessApi;
import com.xyy.ec.order.business.api.OrderTransactionSnapshotBusinessApi;
import com.xyy.ec.order.business.dto.OrderDetailBusinessDto;
import com.xyy.ec.order.business.dto.OrderTransactionSnapshotBusinessDto;
import com.xyy.ec.order.business.dto.ProductTag;
import com.xyy.ec.order.business.dto.afterSales.ReissueCredentialInfo;
import com.xyy.ec.order.business.enums.pay.PayChannelEnum;
import com.xyy.ec.order.business.utils.DateUtil;
import com.xyy.ec.order.search.api.remote.dto.OrderSearchQueryDto;
import com.xyy.ec.order.search.api.remote.result.SearchResultDto;
import com.xyy.ec.order.search.api.remote.order.OrderSearchApi;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.base.ResultMessage;
import com.xyy.ec.pop.config.OssConfig;
import com.xyy.ec.pop.config.XyyConfig;
import com.xyy.ec.pop.constants.Constants;
import com.xyy.ec.pop.ecToPop.EcToPopCorporationService;
import com.xyy.ec.pop.enums.BranchEnum;
import com.xyy.ec.pop.enums.OrderEnum;
import com.xyy.ec.pop.excel.verify.OrderOverOrderExcelVerifyHandler;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.OrderEsVoHelper;
import com.xyy.ec.pop.helper.OrgUserRelationHelper;
import com.xyy.ec.pop.marketing.remote.MerchantRemoteAdapter;
import com.xyy.ec.pop.model.*;
import com.xyy.ec.pop.remote.*;
import com.xyy.ec.pop.server.api.erp.api.PopErpSkuAdminApi;
import com.xyy.ec.pop.server.api.merchant.api.admin.PopOrderConsignmentApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.merchant.dto.OrgUserRelationDto;
import com.xyy.ec.pop.server.api.order.api.PopOrderApi;
import com.xyy.ec.pop.server.api.order.dto.*;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuAdminApi;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuCategoryAdminApi;
import com.xyy.ec.pop.server.api.product.dto.PopSkuCategoryDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuInstructionDto;
import com.xyy.ec.pop.server.api.seller.api.PopReceivingReportApi;
import com.xyy.ec.pop.server.api.util.NullUtils;
import com.xyy.ec.pop.service.*;
import com.xyy.ec.pop.service.domains.OrderDomainService;
import com.xyy.ec.pop.utils.BatchUpdateForExcelUtils;
import com.xyy.ec.pop.utils.CollectionUtil;
import com.xyy.ec.pop.utils.ExceImportWarpUtil;
import com.xyy.ec.pop.vo.*;
import com.xyy.ec.pop.vo.OrderDetail;
import com.xyy.ec.pop.vo.order.*;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuDTO;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import com.xyy.ec.product.business.dto.SkuImagesVideos;
import com.xyy.ec.product.business.dto.TagDTO;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import com.xyy.ec.system.business.api.CodeitemBusinessApi;
import com.xyy.ec.system.business.dto.CodeitemBusinessDto;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xyy.ec.order.business.config.PopOrderStatus.WAITBUYERPAY;

/**
 * <AUTHOR>
 * @description
 * @date 2018/10/20 下午3:47
 */
@Slf4j
@RequestMapping("/order")
@Controller
public class OrderController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderController.class);

    private static final Map<Integer, String> statusMapping = new HashMap<>();

    static {
        statusMapping.put(0, "尚未进入审单流程");
        statusMapping.put(1, "已支付");
        statusMapping.put(2, "配送中");
        statusMapping.put(3, "已完成");
        statusMapping.put(4, "取消");
        statusMapping.put(5, "删除");
        statusMapping.put(6, "已拆单");
        statusMapping.put(7, "出库中");
        statusMapping.put(9, "审单流程结束");
        statusMapping.put(10, "未支付");
        statusMapping.put(11, "已支付");
        statusMapping.put(90, "退款审核中");
        statusMapping.put(91, "已退款");
        statusMapping.put(20, "已送达");
        statusMapping.put(21, "拒签");
    }

    @Autowired
    private XyyConfig xyyConfig;
    @Reference
    private PopOrderApi popOrderApi;
    @Reference
    private PopOrderConsignmentApi popOrderConsignmentApi;
    @Autowired
    private BranchService branchService;
    @Reference
    private PopReceivingReportApi popReceivingReportApi;
    @Autowired
    private OrderDomainService orderDomainService;

    @Value("${deliver.timeout.hours}")
    private Integer timeoutHours;
    @Reference
    private PopErpSkuAdminApi popErpSkuAdminApi;
    @Reference
    private PopSkuAdminApi popSkuAdminApi;
    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;
    @Autowired
    private EcOrderRemote ecOrderRemote;
    @Reference(version = "1.0.0")
    private OrderTransactionSnapshotBusinessApi orderTransactionSnapshotBusinessApi;
    @Reference(version = "1.0.0")
    private OrderDetailBusinessApi orderDetailBusinessApi;
    @Reference(version = "1.0.0", timeout = 3000)
    private CodeitemBusinessApi codeitemService;
    @Autowired
    private EcOrderEsRemote ecOrderEsRemote;
    //订单es 开关 0 开 1关
    @Value("${order.orderes.switch}")
    private String orderEsSwitch;
    /**
     * 批量配送文件大小
     */
    @Value("${order.batchOver.fileSize:1}")
    private int batchOverFileSize;
    /**
     * 批量配送完成订单数
     */
    @Value("${order.batchOver.rowSize:1000}")
    private int batchOverRowSize;
    /**
     * 一次批量处理数
     */
    @Value("${order.batchOver.stepSize:100}")
    private int batchOverStepSize;
    /**
     * 判断商品为医疗器械的一级分类
     */
    @Value("${product.instrument.category}")
    public String instrumentCategory;

    @Reference
    private OrderSearchApi orderSearchApi;
    @Autowired
    private CorporationRemote corporationRemote;
    @Autowired
    private OssConfig ossConfig;

    @Reference
    private PopSkuCategoryAdminApi popSkuCategoryAdminApi;
    @Autowired
    private MerchantRemoteAdapter merchantRemoteAdapter;
    @Autowired
    private ShopAdminRemoteService shopAdminRemoteService;
    @Autowired
    private EcToPopCorporationService ecToPopCorporationService;
    @Autowired
    private ProductSkuRemoteAdapter productSkuRemoteAdapter;

    @Reference
    private OrderBusinessApi orderBusinessApi;
    @RequestMapping(value = "/index", method = RequestMethod.GET)
    public ModelAndView list() {
        ModelAndView orderModel = new ModelAndView("plannedOrderManagement/order/chainOrderList");
        orderModel.addObject(Constants.STATUS, OrderEnum.OrderStatus.maps);

        //获取区域列表
        List<BranchVo> branchVos = branchService.getAllProvinces();
        orderModel.addObject("branchList", branchVos);
        orderModel.addObject("businessTypes", BusinessTypeEnum.values());

        return orderModel;
    }


    /* 订单数据加载 */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public Object orders(OrderAdminVo paramOrder,  Page page, Sort sort) {
        try {
            if (validateProv(paramOrder)) {
                Page<OrderAdminVo> orderVoPage = new Page<>();
                orderVoPage.setTotal(0l);
                orderVoPage.setOffset(1);
                orderVoPage.setLimit(page.getLimit());
                orderVoPage.setPageCount(0);
                orderVoPage.setCurrentPage(1);
                orderVoPage.setRows(Collections.EMPTY_LIST);
                return ResponseUtils.returnObjectSuccess(orderVoPage);
            }
            if (!Objects.equals("0", orderEsSwitch)) {
                handleOrderParam(paramOrder);
            }
            if (page == null) {
                page = new Page();
            }
            int limit = page.getLimit();
            int pageNum = page.getOffset() / limit + 1;
            if(null == paramOrder.getIsThirdCompany()){
                paramOrder.setIsThirdCompany(1);
            }
            Page<OrderAdminVo> orderPage = handleOrderRequest(paramOrder, JSON.toJSONString(sort), pageNum, limit);
            return ResponseUtils.returnObjectSuccess(orderPage);
        } catch (Exception e) {
            log.error("查询订单列表失败！paramOrder:{}",JSONObject.toJSONString(paramOrder), e);
            return ResponseUtils.returnCommonException(e.getMessage());
        }
    }

    @RequestMapping("/exception/detail")
    @ResponseBody
    public Object queryExceptionDetail(String orderNo){
        if (NullUtils.hasBlank(orderNo)){
            return ResponseUtils.returnCommonException("参数不完整");
        }
        try {
            List<OrderExceptionVo> orderExceptionVos = orderDomainService.queryOrderException(orderNo);
            return ResponseUtils.returnObjectSuccess(orderExceptionVos);
        }catch (Exception e){
            log.error("查询订单异常信息失败！orderNo:{}",orderNo, e);
            return ResponseUtils.returnCommonException(e.getMessage());
        }
    }
    private boolean validateProv(OrderAdminVo paramOrder) {
        List<Long> provIds = getProvIds(paramOrder.getProvId());
        if (CollectionUtils.isEmpty(provIds)) {
            return true;
        }
        paramOrder.setProvIds(provIds);
        return false;
    }

    @Value("${tracingCodeTime:2025-04-17 00:00:00}")
    private String tracingCodeTime;

    /* 订单详情页 */
    @RequestMapping(value = "orderManagement/detail/{orderNo}", method = RequestMethod.GET)
    public String orderDetail(@PathVariable("orderNo") String orderNo, Model model) {
        OrderAdminVo paramOrder = new OrderAdminVo();
        paramOrder.setOrderNo(orderNo);
        try {
            Page<OrderAdminVo> orderPage = handleOrderRequest(paramOrder, null, 0, 1);
            OrderAdminVo order = orderPage.getRows().get(0);
            ApiRPCResult<List<OrderTrackInfoDto>> apiRPCResult = popOrderApi.queryOrderTrackInfoList(orderNo);
            if (apiRPCResult.isSuccess() && !CollectionUtils.isEmpty(apiRPCResult.getData())) {
                OrderTrackInfoDto orderTrackInfoDto = apiRPCResult.getData().get(0);
                order.setTrackingNo(orderTrackInfoDto.getTrackingNo());
            }
            if (order.getPayChannel() != null) {
                OrderPayChannelEnum payChannelObj = OrderPayChannelEnum.get(order.getPayChannel());
                order.setPayChannelName(payChannelObj != null ? payChannelObj.getName() : "");
                //Apollo 上线时间
                if (PayChannelEnum.DHSY.getValue() == order.getPayChannel().intValue() && DateUtil.differentDays(DateUtil.string2Date(this.tracingCodeTime, DateUtil.PATTERN_STANDARD), order.getCreateTime()) > 0) {
                    order.setShowStatement("1");
                }
            }
            model.addAttribute("order", order);
        } catch (Exception e) {
            log.error("查询订单列表失败！", e);
        }
        return "plannedOrderManagement/order/orderDetail";
    }


    /* 订单商品列表 YBM20181023155041100009 */
    /*  /order/orderManagement/orders/' + $("#hidden_orderNo").html() + '/details */
    @RequestMapping(value = "/orderManagement/orders/details", method = RequestMethod.GET)
    @ResponseBody
    public Object orderProductDetail(String orderNo, Integer offset, Integer limit) {
        limit = limit == null ? 10 : limit;
        offset = offset == null ? 0 : offset;
        Integer pageNum = (offset + limit) / limit;
        Page page = new Page();
        PageInfo<OrderDetail> pageInfo = ecOrderRemote.getOrderDetails(orderNo, pageNum, limit);
        //查询发货明细信息
        List<OrderDetail> orderDetailList = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(orderDetailList)) {
            List<Long> orderDetailIds = orderDetailList.stream().map(OrderDetail::getId).collect(Collectors.toList());
            ApiRPCResult<List<PopOrderConsignmentDetailDto>> listApiRPCResult1 = popOrderConsignmentApi.queryOrderConsignmentDetailList(orderNo, orderDetailIds);
            if (listApiRPCResult1.isSuccess() && CollectionUtils.isNotEmpty(listApiRPCResult1.getData())) {
                List<PopOrderConsignmentDetailDto> orderConsignmentDetails = listApiRPCResult1.getData();
                Map<Long, List<PopOrderConsignmentDetailDto>> groupMap = orderConsignmentDetails.stream().collect(Collectors.groupingBy(PopOrderConsignmentDetailDto::getOrderDetailId));
                for (OrderDetail orderDetail : orderDetailList) {
                    List<PopOrderConsignmentDetailDto> orderConsignmentDetailList = groupMap.get(orderDetail.getId());
                    if (CollectionUtils.isEmpty(orderConsignmentDetailList)) {
                        continue;
                    }
                    Integer batchNumTotal = orderConsignmentDetailList.stream().mapToInt(m -> m.getBatchConsNum()).sum();
                    BigDecimal batchPriceTotal = BigDecimal.ZERO;
                    List<BigDecimal> batchPriceSet = orderConsignmentDetailList.stream().map(m -> m.getBatchPrice()).collect(Collectors.toList());
                    for (BigDecimal batchPrice : batchPriceSet) {
                        batchPriceTotal = batchPriceTotal.add(batchPrice);
                    }
                    orderDetail.setBatchConsNumTotal(batchNumTotal);
                    orderDetail.setBatchPriceTotal(batchPriceTotal);
                }
            }
            //活动信息
            getDisCount(orderNo, orderDetailList);

            orderDetailList = giftSort(orderDetailList);

            List<Long> csuIds = orderDetailList.stream().map(OrderDetail::getSkuId).collect(Collectors.toList());
            Map<Long, PopSkuDto> popSkuDtoMap = new HashMap<>();

            ApiRPCResult<List<PopSkuDto>> apiRPCResult = popSkuAdminApi.findByCsuIdsWithDeleted(csuIds);
            if (apiRPCResult.isSuccess()) {
                List<PopSkuDto> popSkuDtos = apiRPCResult.getData();
                popSkuDtoMap = popSkuDtos.stream().collect(Collectors.toMap(PopSkuDto::getCsuid, Function.identity(), (o1, o2) -> o1));
            }

            Map<String, PopSkuInstructionDto> popSkuInstructionDtoMap = new HashMap<>();

            List<String> barcodes = orderDetailList.stream().map(OrderDetail::getBarcode).collect(Collectors.toList());
            ApiRPCResult<List<PopSkuInstructionDto>> popSkuInstructionDtoApi = popSkuAdminApi.findSkuInstructionByBarcodes(barcodes);
            if (popSkuInstructionDtoApi.isSuccess()){
                List<PopSkuInstructionDto> skuInstructionDtos = popSkuInstructionDtoApi.getData();
                popSkuInstructionDtoMap = skuInstructionDtos.stream().collect(Collectors.toMap(PopSkuInstructionDto::getBarcode, Function.identity(), (o1, o2) -> o1));
            }

            Map<String, PopSkuCategoryDto> popSkuCategoryDtoMap = new HashMap<>();
            ApiRPCResult<List<PopSkuCategoryDto>> listApiRPCResult = popSkuCategoryAdminApi.categoryByBarcodes(barcodes);
            if (listApiRPCResult.isSuccess()){
                List<PopSkuCategoryDto> popSkuCategoryDtos = listApiRPCResult.getData();
                popSkuCategoryDtoMap = popSkuCategoryDtos.stream().collect(Collectors.toMap(PopSkuCategoryDto::getBarcode, Function.identity(), (o1, o2) -> o1));
            }

            for (OrderDetail orderDetail : orderDetailList) {
                PopSkuDto popSkuDto = popSkuDtoMap.get(orderDetail.getSkuId());
                PopSkuCategoryDto popSkuCategoryDto = popSkuCategoryDtoMap.get(orderDetail.getBarcode());
                if (Objects.nonNull(popSkuDto)) {
                    orderDetail.setErpCode(popSkuDto.getErpCode());
                    orderDetail.setCommonName(popSkuDto.getCommonName());
                    orderDetail.setDosageForm(popSkuDto.getDosageForm());
                    if (Objects.nonNull(popSkuCategoryDto) && Objects.equals(instrumentCategory,popSkuCategoryDto.getBusinessFirstCategoryCode())) {
                        orderDetail.setApprovalNumber(popSkuDto.getApprovalNumber());
                    }
                }
                PopSkuInstructionDto popSkuInstructionDto = popSkuInstructionDtoMap.get(orderDetail.getBarcode());
                if (Objects.nonNull(popSkuInstructionDto)){
                    orderDetail.setManufacturingLicenseNo(popSkuInstructionDto.getManufacturingLicenseNo());
                    orderDetail.setMarketAuthor(popSkuInstructionDto.getMarketAuthor());
                }
            }
            //填充自营商品信息
            fillECSkuInfo(orderDetailList);

        }
        Integer currentPage = pageInfo.getPageNum();
        Integer limits = pageInfo.getPageSize();
        Integer off = 0;
        if (currentPage >= 1 && limits > 0) {
            off = (currentPage - 1) * limits;
        }
        page.setTotal(pageInfo.getTotal());
        page.setOffset(off);
        page.setRows(orderDetailList);
        page.setCurrentPage(currentPage);
        page.setLimit(limits);
        page.setPageCount(pageInfo.getPages());
        // 追加发货数量
        //appendExt(jsonArray, orderNo);
        return page;
    }

    private void fillECSkuInfo(List<OrderDetail> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        //查找没有erpCode 和生产许可证的数据（两个都没有的 去ec查询）
        List<Long> ecSkuIds = list.stream()
                .filter(dto -> StringUtils.isEmpty(dto.getErpCode()) && StringUtils.isEmpty(dto.getApprovalNumber()) && StringUtils.isEmpty(dto.getManufacturingLicenseNo()))
                .map(OrderDetail::getSkuId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ecSkuIds)){
            return;
        }
        List<CsuDTO> ecProductBySkuIds = productSkuRemoteAdapter.findECProductBySkuIds(ecSkuIds);
        //商品map
        Map<Long, CsuDTO> csuDTOMap = ecProductBySkuIds.stream()
                .collect(Collectors.toMap(dto -> dto.getId(), Function.identity(), (v1, v2)->v1));
        if(MapUtils.isEmpty(csuDTOMap)){
            return;
        }
        for(OrderDetail detail : list){
            if(StringUtils.isNotEmpty(detail.getErpCode())
                    || StringUtils.isNotEmpty(detail.getApprovalNumber())
                    || StringUtils.isNotEmpty(detail.getManufacturingLicenseNo())){
                continue;
            }
            Long skuId = detail.getSkuId();
            CsuDTO csuDTO = csuDTOMap.get(skuId);
            if(null != csuDTO){
                detail.setErpCode(csuDTO.getErpCode());
                detail.setApprovalNumber(csuDTO.getApprovalNumber());
                detail.setManufacturingLicenseNo(csuDTO.getManufacturingLicenseNo());
                detail.setMarketAuthor(csuDTO.getMarketAuthor());
                detail.setCommonName(csuDTO.getCommonName());
                detail.setDosageForm(csuDTO.getDosageForm());
            }
        }


    }

    private List<OrderDetail> giftSort(List<OrderDetail> orderDetailList) {
        log.info("9528-orderDetailList-1:{}",JSON.toJSONString(orderDetailList));

        List<OrderDetail> orderDetailsNoGiftId = orderDetailList.stream()
                .filter(orderDetail -> orderDetail.getExtraGiftId()==null)
                .collect(Collectors.toList());
           log.info("9528-orderDetailsNoGiftId:{}",JSON.toJSONString(orderDetailsNoGiftId));
        orderDetailList = orderDetailList.stream()
                .filter(orderDetail -> orderDetail.getExtraGiftId()!=null)
                .collect(Collectors.toList());

        log.info("9528-orderDetailList:{}",JSON.toJSONString(orderDetailList));
        List<OrderDetail> resultOrderDetails = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(orderDetailList)) {
        List<OrderDetail> giftOrderDetails = orderDetailList.stream()
                .filter(orderDetail -> Objects.equals(1, orderDetail.getExtraGift()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(giftOrderDetails)){
            return orderDetailList;
        }
        log.info("9528-giftOrderDetails:{}",JSON.toJSONString(giftOrderDetails));

        List<OrderDetail> unGiftOrderDetails = orderDetailList.stream()
                .filter(orderDetail -> !Objects.equals(1, orderDetail.getExtraGift()))
                .collect(Collectors.toList());
        log.info("9528-unGiftOrderDetails:{}",JSON.toJSONString(unGiftOrderDetails));

        Map<Long, List<OrderDetail>> giftOrderMapDetails= giftOrderDetails.stream().collect(Collectors.groupingBy( z->  z.getExtraGiftId()));
        if(CollectionUtils.isNotEmpty(unGiftOrderDetails)) {
            Map<Long, List<OrderDetail>> unGiftOrderMapDetails = unGiftOrderDetails.stream().collect(Collectors.groupingBy(z -> z.getExtraGiftId()));
            unGiftOrderMapDetails.forEach((giftId, unGiftOrderDetailsItem) -> {
                resultOrderDetails.addAll(unGiftOrderDetailsItem);
                List<OrderDetail> giftOrderDetailsItem = giftOrderMapDetails.get(giftId);
                if (CollectionUtil.isNotEmpty(giftOrderDetailsItem)) {
                    resultOrderDetails.addAll(giftOrderDetailsItem);
                }
            });
        }}
        log.info("9528-orderDetailsNoGiftId:{}",JSON.toJSONString(orderDetailsNoGiftId));
        resultOrderDetails.addAll(orderDetailsNoGiftId);

        return resultOrderDetails;
    }


    private void getDisCount(String orderNo, List<OrderDetail> orderDetailList) {
        try {
            log.info("OrderController.getDisCount参数:orderNo {}, orderDetailList:{}", orderNo, JSON.toJSONString(orderDetailList));
            //计算店铺活动优惠和平台活动优惠
            BigDecimal orderTotalDiscount = orderDetailList.stream().map(o -> o.getOriginalTotalDiscount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            //商品明细的订单上优惠金额
            Map<Long, BigDecimal> orderDetailDisForPopDtoMap = orderDetailList.stream().collect(Collectors.toMap(OrderDetail::getId, OrderDetail::getOriginalTotalDiscount));
            Map<Long, PromoDiscountDetailDto> promoDistcountMap = new HashMap<>();
            if (orderTotalDiscount.compareTo(BigDecimal.ZERO) > 0) {
                promoDistcountMap = orderDomainService.getPromoDiscountDetail(orderNo, orderTotalDiscount, orderDetailDisForPopDtoMap);
            }
            if (MapUtils.isNotEmpty(promoDistcountMap)) {
                for (OrderDetail orderDetail : orderDetailList) {
                    PromoDiscountDetailDto detailDto = promoDistcountMap.get(orderDetail.getId());
                    if (Objects.isNull(detailDto)) {
                        continue;
                    }
                    orderDetail.setShopPromoDiscount(detailDto.getShopPromoDiscount());
                    orderDetail.setPlatformPromoDiscount(detailDto.getPlatformPromoDiscount());
                    orderDetail.setShopVoucherAmount(detailDto.getCrossShopVoucherAmount());
                    orderDetail.setCrossPlatformVoucherAmount(detailDto.getCrossPlatformVoucherAmount());
                }
            }
            //活动提示信息
            Map<Long, OrderDetailDiscountVo> orderPromoDetail = ecOrderRemote.getOrderPromoDetail(orderNo);
            for (OrderDetail orderDetail : orderDetailList) {
                OrderDetailDiscountVo orderDetailDiscountVo = orderPromoDetail.get(orderDetail.getId());
                if (orderDetailDiscountVo != null) {
                    orderDetail.setPlatformDiscountMsgList(orderDetailDiscountVo.getPlatformDiscountMsgList());
                }
            }
        } catch (Exception e) {
            log.error("OrderController.getDisCount异常:orderNo {}, orderDetailList:{}", orderNo, JSON.toJSONString(orderDetailList), e);
        }
    }


    /* 修改订单状态为待审核 */
    @RequestMapping(value = "orderManagement/order/status", method = RequestMethod.POST)
    @ResponseBody
    public Object updateOrderStatus(String orderNo, Integer status, String orgId,Integer payType) {
        SysUser user = getUser();
        String operator = user.getRealName();
        try {
            OrderDto orderDto = orderDomainService.getOrderByOrderNo(orderNo);
            if (orderDto == null) {
                throw new XyyEcPopException("订单不存在");
            }
            int orderStatus = orderDto.getStatus();
            if (orderStatus != WAITBUYERPAY.getId()) {
                throw new XyyEcPopException("订单状态有变化，请刷新操作");
            }
            if (orderDto.getPayChannel() != 7) {
                throw new XyyEcPopException("非电汇平台订单不能审核");
            }
            boolean b = orderDomainService.orderColl(orderNo, status, operator, orgId);
//            if (b) {
//                //写入日志
//                boolean saved = orderDomainService.saveOperateLog(orderNo,  user.getUsername()+user.getRealName(), user.getUsername(), );
//            }
        } catch (XyyEcPopException e) {
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            LOGGER.error(" encounter exception {} , e {}", e.getMessage(), e);
            return ResponseVo.errRest("查询失败");
        }
        return ResponseVo.successResult(Boolean.TRUE);


    }

    /* 处理orgId 参数 关键字搜索 传入参数 corporationNo */
    private void handleOrderParam(OrderAdminVo paramOrder) {
        String corporationNo = paramOrder.getCorporationNo();
        String corporationName = paramOrder.getCompanyName();
        String name = paramOrder.getCorporationName();
        paramOrder.setOrgId(corporationNo);

        if (StringUtils.isNotEmpty(name) || StringUtils.isNotEmpty(corporationName)) {
            List<String> orgIds = popCorporationRemoteAdapter.getOrgIdByName(name, corporationName);
            if (CollectionUtil.isNotEmpty(orgIds)) {
                if (Objects.isNull(paramOrder.getOrgIdList())) {
                    paramOrder.setOrgIdList(orgIds);
                } else {
                    paramOrder.getOrgIdList().addAll(orgIds);
                }
            } else {
                orgIds.add("-1");
                paramOrder.setOrgIdList(orgIds);
            }

        }
        //第三方公司
        paramOrder.setIsThirdCompany(ProductEnumDTO.ThirdCompany.IS_THIRD.getId());
        if (StringUtils.isNotBlank(paramOrder.getStatusList())) {
            paramOrder.setTimeoutHours(timeoutHours);
        }
    }


    /* 处理请求 */
    private Page<OrderAdminVo> handleOrderRequest(OrderAdminVo paramOrder, String sort, Integer pageNum, Integer limit) {
        List<OrderAdminVo> rows;
        Long totalCount;
        if (Objects.equals("0", orderEsSwitch)) {
            //转换es的查询对象
            OrderSearchQueryDto orderSearchQueryDto = OrderEsVoHelper.convertOrderToOrderSearchQueryDto(paramOrder, pageNum, limit);
            orderSearchQueryDto.setIsThirdCompany(paramOrder.getIsThirdCompany());
            SearchResultDto<String> searchResultDto = ecOrderEsRemote.orderSearch(orderSearchQueryDto);

            OrderAdminVo query = new OrderAdminVo();
            if (Objects.nonNull(searchResultDto) && CollectionUtils.isNotEmpty(searchResultDto.getResultList())) {
                query.setOrderNos(searchResultDto.getResultList());
                query.setIsThirdCompany(null == paramOrder.getIsThirdCompany() ? -1 : paramOrder.getIsThirdCompany());
                Page<OrderAdminVo> orderPage = ecOrderRemote.popOrderList(query, sort, 1, limit);
                log.info("9528-OrderController.handleOrderRequest 结果orderPage {}", JSON.toJSONString(orderPage));
                rows = orderPage.getRows();
                totalCount = searchResultDto.getTotalCount();
            } else {
                rows = Lists.newArrayList();
                totalCount = 0L;
            }
        } else {
            Page<OrderAdminVo> orderPage = ecOrderRemote.popOrderList(paramOrder, sort, pageNum, limit);
            log.info("OrderController.handleOrderRequest 结果orderPage {}", JSON.toJSONString(orderPage));
            rows = orderPage.getRows();
            totalCount = orderPage.getTotal();
        }

        Page<OrderAdminVo> page = new Page<>();
        page.setTotal(totalCount);
        page.setLimit(limit);
        page.setOffset(pageNum);

        if (CollectionUtil.isNotEmpty(rows)) {
            if(Objects.equals(paramOrder.getIsThirdCompany(), 1)){
                fillPopShopInfo(rows);
            }else{
                fillEcShopInfo(rows);
            }
            page.setRows(rows);
        } else {
            page.setRows(Collections.emptyList());
            if (page.getTotal() == null) {
                page.setTotal(NumberUtils.LONG_ZERO);
            }
        }

        return page;
    }

    private void fillEcShopInfo(List<OrderAdminVo> rows){
        List<String> branchCodes = rows.stream().map(OrderAdminVo::getBranchCode).distinct().collect(Collectors.toList());
//        Map<String, ShopInfoDTO> branchCodeShopInfoMap = Maps.newHashMap();
//        if(CollectionUtils.isNotEmpty(branchCodes)){
//            //查询区域信息
//            branchCodeShopInfoMap = shopAdminRemoteService.queryECBranchShopCode(branchCodes);
//        }
        List<Long> merchantIds = rows.stream().map(OrderAdminVo::getMerchantId).collect(Collectors.toList());
        //添加自营用户信息
        Map<Long, OrgUserRelationDto> orgUserRelationDtoMap = getEcOrgUserRelationMap(merchantIds);

        List<String> orgIds = rows.stream().map(OrderAdminVo::getOrgId).distinct().collect(Collectors.toList());
        Map<String, String> ecToPopOrgIdMap = ecToPopCorporationService.getEcToPopOrgIdMap();
        if(MapUtils.isNotEmpty(ecToPopOrgIdMap)){
            orgIds.addAll(ecToPopOrgIdMap.values());
        }

        List<CorporationDto> corporationList = corporationRemote.queryCorpBaseByOrgIds(orgIds);
        Map<String, CorporationDto> orgIdMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(corporationList)) {
            orgIdMap = corporationList.stream().distinct().collect(Collectors.toMap(CorporationDto::getOrgId, e -> e, (key1, key2) -> key2, LinkedHashMap::new));
        }
        for (OrderAdminVo order : rows) {
            String orgId = order.getOrgId();//areaOrgId or orgId
            String branchCode = order.getBranchCode();
//            ShopInfoDTO shopInfo = branchCodeShopInfoMap.get(branchCode);
            order.setCorporationNo(orgId);
            com.xyy.ec.pop.marketing.enums.BranchEnum branch = com.xyy.ec.pop.marketing.enums.BranchEnum.match(branchCode);
            if(null == branch){
                branch = com.xyy.ec.pop.marketing.enums.BranchEnum.ALL_COUNTRY;
            }
//            if (null != shopInfo) {
//                order.setCorporationName(branch.getValue());
//                order.setCompanyName(shopInfo.getName());
//            }
            CorporationDto corInfo = orgIdMap.get(orgId);
            if(MapUtils.isNotEmpty(ecToPopOrgIdMap) && StringUtils.isNotEmpty(ecToPopOrgIdMap.get(orgId))){
                String popOrgId = ecToPopOrgIdMap.get(orgId);
                corInfo = orgIdMap.get(popOrgId);
            }
            if (null != corInfo) {//说明订单对象中传来的是 OrgId
                order.setCorporationName(corInfo.getName());
                order.setCorporationNo(corInfo.getOrgId());
                order.setCompanyName(corInfo.getCompanyName());
            }
            if(StringUtils.isNotEmpty(order.getCorporationNo())
                    && StringUtils.isNotEmpty(orgId)
                    && !Objects.equals(order.getCorporationNo(), orgId)){
                String nOrgId = order.getCorporationNo() + "(" + orgId + ")";
                order.setCorporationNo(nOrgId);
            }
            if (order.getProvId() != null) {
                order.setProv(branch.getProvinceName());
            }
            OrgUserRelationDto orgUserRelationDto = orgUserRelationDtoMap.get(order.getMerchantId());

            if (Objects.nonNull(orgUserRelationDto)) {
                order.setMerchantErpCode(orgUserRelationDto.getSellerUserId());
                order.setAccountStatus(orgUserRelationDto.getAccountStatus());
            }
        }


    }

    /**
     * 自营用户信息
     * @param merchantIds
     * @return
     */
    private Map<Long, OrgUserRelationDto> getEcOrgUserRelationMap(List<Long> merchantIds){
        if(log.isDebugEnabled()){
            log.debug("OrderV2ServiceImpl.listOrder findMerchantAndMerchantIdList start, merchantIds:{}", JSONObject.toJSONString(merchantIds));
        }
        //自营设置 直接查询用户信息
        Map<Long, MerchantBussinessDto> merchants = merchantRemoteAdapter.findMerchantAndMerchantIdList(merchantIds);
        Map<Long, OrgUserRelationDto>  ecOrgUserMap = OrgUserRelationHelper.convertMerchantToOrgUserMap(merchants);
        if(log.isDebugEnabled()){
            log.debug("OrderV2ServiceImpl.listOrder findMerchantAndMerchantIdList start, ecOrgUserMap:{}, merchantIds:{}", JSONObject.toJSONString(ecOrgUserMap), JSONObject.toJSONString(merchantIds));
        }

        return ecOrgUserMap;
    }

    /**
     * 填充店铺信息
     * @param rows
     */
    private void fillPopShopInfo(List<OrderAdminVo> rows){
        List<String> orderNoList = rows.stream().map(OrderAdminVo::getOrderNo).collect(Collectors.toList());
        //areaOrgId or orgId
        List<String> orgIds = rows.stream().map(OrderAdminVo::getOrgId).collect(Collectors.toList());

        List<Long> merchantIds = rows.stream().map(OrderAdminVo::getMerchantId).collect(Collectors.toList());

        ApiRPCResult<List<OrderTrackInfoDto>> apiRPCResult = popOrderApi.queryOrderTrackInfoListByOrderNos(orderNoList);
        List<OrderTrackInfoDto> orderTrackInfoDtos = new ArrayList<>();
        if (apiRPCResult.isSuccess()) {
            orderTrackInfoDtos = apiRPCResult.getData();
        }

        List<CorporationDto> corporationList = corporationRemote.queryCorpBaseByOrgIds(orgIds);
        List<OrgUserRelationDto> orgUserRelationDtos = orderDomainService.selectOrgUserRelation(merchantIds);
        List<PopOrderDto> popOrderDtos = orderDomainService.queryPopOrdersByOrderNoList(orderNoList);

        //key areaOrgId
        Map<String, CorporationDto> orgIdMap = new HashMap<>();
        Map<String, OrgUserRelationDto> orgUserRelationDtoMap = new HashMap<>();
        Map<String, PopOrderDto> popOrderDtoMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(corporationList)) {
            orgIdMap = corporationList.stream().distinct().collect(Collectors.toMap(CorporationDto::getOrgId, e -> e, (key1, key2) -> key2, LinkedHashMap::new));
        }

        if (CollectionUtil.isNotEmpty(orgUserRelationDtos)) {
            orgUserRelationDtoMap = orgUserRelationDtos.stream().filter(Objects::nonNull).collect(Collectors.toMap(o -> o.getMerchantId() + o.getOrgId(), Function.identity(), (o1, o2) -> o1));
        }

        if (CollectionUtils.isNotEmpty(popOrderDtos)) {
            popOrderDtoMap = popOrderDtos.stream().filter(Objects::nonNull).collect(Collectors.toMap(PopOrderDto::getOrderNo, Function.identity(), (o1, o2) -> o1));
        }

        Map<String, OrderTrackInfoDto> orderTrackInfoMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(orderTrackInfoDtos)) {
            orderTrackInfoMap = orderTrackInfoDtos.stream().collect(Collectors.toMap(OrderTrackInfoDto::getOrderNo, Function.identity(), (k1, k2) -> k2));
        }

        Map<Long, String> map = getProv();
        for (OrderAdminVo order : rows) {

            String orgId = order.getOrgId();//areaOrgId or orgId
            String orderNo = order.getOrderNo();
            CorporationDto corInfo = orgIdMap.get(orgId);
            if (null != corInfo) {//说明订单对象中传来的是 OrgId
                order.setCorporationName(corInfo.getName());
                order.setCorporationNo(corInfo.getOrgId());
                order.setCompanyName(corInfo.getCompanyName());
            }

            OrgUserRelationDto orgUserRelationDto = orgUserRelationDtoMap.get(order.getMerchantId() + orgId);

            if (Objects.nonNull(orgUserRelationDto)) {
                order.setMerchantErpCode(orgUserRelationDto.getSellerUserId());
                order.setAccountStatus(orgUserRelationDto.getAccountStatus());
            }

            PopOrderDto popOrderDto = popOrderDtoMap.get(orderNo);

            if (Objects.nonNull(popOrderDto)) {
                order.setOrderSyncStatus(popOrderDto.getOrderSyncStatus());
                order.setOrderSyncErrorReason(popOrderDto.getErrorReason());
                if (Objects.equals(order.getStatus(), OrderEnum.OrderStatus.OUTBOUND.getId()) && !Objects.equals(1, order.getIsFbp())) {
                    order.setStatus(popOrderDto.getStatus());
                }
            }

            if (order.getProvId() != null) {
                order.setProv(map.getOrDefault(order.getProvId(), StringUtils.EMPTY));
            }

            OrderTrackInfoDto orderTrackInfo = orderTrackInfoMap.get(orderNo);
            if (null == orderTrackInfo) {
                continue;
            }
            if (StringUtils.isNotEmpty(orderTrackInfo.getLogiCompany())) {
                order.setLogisticsWay(orderTrackInfo.getLogiCompany());
            } else if (StringUtils.isEmpty(orderTrackInfo.getLogiCompany()) && StringUtils.isNotBlank(orderTrackInfo.getLogisticsWay())) {
                order.setLogisticsWay(xyyConfig.getLogisticsNameByCode(orderTrackInfo.getLogisticsWay()));
            }
        }
    }

    /**
     * 订单操作日志
     */
    @RequestMapping(value = "/operationLog", method = RequestMethod.GET)
    public String checkCerficateLog(String orderNo, Model model) {
        List<OrderStatusRecordsVo> operationLogs = orderDomainService.queryOperationLogByOrderNo(orderNo);
        model.addAttribute("operateList", operationLogs);
        return "plannedOrderManagement/order/operatLog";
    }

    /**
     * 关联资质异常弹窗页面
     */
    @RequestMapping(value = "/exceptionLog", method = RequestMethod.GET)
    public String checkExceptionLog(String orderNo, Model model) {
        return "plannedOrderManagement/order/exceptionLog";
    }

    /**
     * 订单客服备注
     */
    @RequestMapping(value = "/orderServiceUrgency", method = RequestMethod.GET)
    public String orderServiceUrgency(String orderNo, Model model) {
        OrderUrgencyVo orderUrgencyVo = queryOrderServiceUrgency(orderNo);
        model.addAttribute("order", orderUrgencyVo);
        return "plannedOrderManagement/order/serviceUrgency";
    }

    /**
     * 更新订单紧急状态和订单备注信息
     *
     * @param id            订单id
     * @param urgencyDegree 订单紧急状态
     * @param urgencyInfo   订单备注信息
     * @return 操作结果
     */
    @RequestMapping("updateOrderUrgencyInfo")
    @ResponseBody
    public Object updateOrderUrgencyInfo(Long id, String orderNo, int urgencyDegree, String urgencyInfo) {
        OrderDto order = orderDomainService.getOrderByOrderNo(orderNo);
        if (null == order) {
            return this.addError("查询不到此订单");
        }
        if (id.longValue() != order.getId().longValue()) {
            return this.addError("订单id错误");
        }
        SysUser user = getUser();
        String operator = user.getRealName();
        urgencyInfo = StringUtils.isEmpty(urgencyInfo) ? "" : urgencyInfo;
        boolean updated = orderDomainService.updateOrderUrgencyInfo(id, urgencyDegree, urgencyInfo, operator);
        String msg;
        if (updated) {
            //更新客服备注信息到收款报表
            popReceivingReportApi.updateReceivingReportRemark(orderNo,urgencyInfo);
            msg = "更新成功";
        } else {
            msg = "更新失败";
        }
        return this.addResult(msg);
    }

    private OrderUrgencyVo queryOrderServiceUrgency(String orderNo) {
        OrderUrgencyVo orderUrgencyVo = new OrderUrgencyVo();
        OrderDto orderDto = orderDomainService.getOrderByOrderNo(orderNo);
        if (orderDto != null) {
            orderUrgencyVo.setId(orderDto.getId());
            orderUrgencyVo.setOrderNo(orderDto.getOrderNo());
            orderUrgencyVo.setUrgencyDegree(orderDto.getUrgencyDegree());
            orderUrgencyVo.setUrgencyInfo(orderDto.getUrgencyInfo());
            orderUrgencyVo.setUrgencyUpdateTime(orderDto.getUrgencyUpdateTime());
        }
        return orderUrgencyVo;
    }

    /**
     * 获取物流发货信息
     *
     * @param orderNo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getOrderDelivery", method = RequestMethod.GET)
    public ResultMessage getOrderDeliveryByOrderNo(String orderNo) {
        try {
            if (StringUtils.isEmpty(orderNo)) {
                ResultMessage.createError("orderNo不能为空");
            }
            List<OrderDeliveryVo> orderDeliveryList = orderDomainService.getOrderDeliveryList(orderNo);
            return ResultMessage.createSuccess(orderDeliveryList);
        } catch (Exception e) {
            LOGGER.error("获取物流发货信息异常 orderNo:{}", orderNo, e);
            return ResultMessage.createError(e.getMessage());
        }
    }

    @RequestMapping("/getInvoice")
    @ResponseBody
    public ResponseVo getInvoice(String orderNo) {
        try {
            if (StringUtils.isBlank(orderNo)) {
                return ResponseVo.errRest("缺少重要参数");
            }
            List<String> restList = orderDomainService.getOrderInvoice(orderNo);
            return ResponseVo.successResult(restList);
        } catch (Exception e) {
            log.error("#order  getInvoice#Error#", e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    /**
     * 查询超时未发货订单数
     *
     * @return
     */
    @GetMapping(value = "/queryTimeoutOrderCount")
    @ResponseBody
    public ResponseVo<Integer> queryTimeoutOrderCount() {
        try {
            log.info("OrderController.queryTimeoutOrderCount#");
            return ResponseVo.successResult(orderDomainService.queryTimeoutOrderCount());
        } catch (Exception e) {
            log.error("OrderController.queryTimeoutOrderCount#查询超时未发货订单数,error.", e);
            return ResponseVo.errRest("查询超时未发货订单数失败");
        }
    }

    /**
     * 查询超时未发货订单数
     *
     * @return
     */
    @GetMapping(value = "/queryLogisticsTrackCount")
    @ResponseBody
    public ResponseVo<LogisticsRemindVo> queryLogisticsTrackCount() {
        try {
            log.info("OrderController.queryLogisticsTrackCount#");
            LogisticsRemindVo vo = orderDomainService.queryLogisticsTrackCount();
            if(vo==null){
                ResponseVo.errRest("查询物流异常信息失败");
            }
            return ResponseVo.successResult(vo);
        } catch (Exception e) {
            log.error("OrderController.queryLogisticsTrackCount#物流异常信息,error.", e);
            return ResponseVo.errRest("查询物流异常信息失败");
        }
    }

    @RequestMapping("getSalesMessage")
    @ResponseBody
    public Object getSalesMessage(@RequestParam(value = "saleId") Long saleId) {
        return orderDomainService.getSalesMessage(saleId);
    }

    /**
     * 初始化订单数据到es
     *
     * @return
     */
    @GetMapping(value = "/initOrderEs")
    public Object initOrderEs() {
        try {
            orderSearchApi.initOrderEs();
            return ResponseVo.successResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("OrderControllerV2.initOrderEs#订单初始化异常", e);
            return ResponseVo.errRest("订单初始化异常");
        }
    }

    /**
     * 初始化退款单数据到es
     *
     * @return
     */
    @GetMapping(value = "/initOrderRefundEs")
    public Object initOrderRefundEs() {
        try {
            orderSearchApi.initOrderRefundEs();
            return ResponseVo.successResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("OrderControllerV2.initOrderEs#订单初始化异常", e);
            return ResponseVo.errRest("订单初始化异常");
        }
    }

    /**
     * 同步订单两天内的数据到es
     *
     * @return
     */
    @GetMapping(value = "/syncOrderEs")
    public Object syncOrderEs() {
        try {
            orderSearchApi.syncOrder();
            return ResponseVo.successResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("OrderControllerV2.initOrderEs#订单初始化异常", e);
            return ResponseVo.errRest("订单初始化异常");
        }
    }


    /**
     * 同步pop订单的数据到es
     *
     * @return
     */
    @GetMapping(value = "/initPopOrder")
    public Object initPopOrder() {
        try {
            orderSearchApi.initPopOrder();
            return ResponseVo.successResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("OrderControllerV2.initPopOrder#订单初始化异常", e);
            return ResponseVo.errRest("订单初始化异常");
        }
    }

    /**
     * 查看订单快照
     *
     * @return
     */
    @GetMapping(value = "/getOrderSnapshot")
    public String getOrderSnapshot(@RequestParam(value = "orderNo") String orderNo,
                                   @RequestParam(value = "skuId") Long skuId,
                                   Model model) {
        try {


            OrderTransactionSnapshotBusinessDto query = new OrderTransactionSnapshotBusinessDto();
            query.setOrderNo(orderNo);
            query.setSkuId(skuId);
            List<OrderTransactionSnapshotBusinessDto> orderTransactionSnapshotBusinessDtos = orderTransactionSnapshotBusinessApi.selectList(query);
            OrderTranscationSnapshotAppVo orderTranscationSnapshotAppDto = new OrderTranscationSnapshotAppVo();

            if (CollectionUtils.isNotEmpty(orderTransactionSnapshotBusinessDtos)) {
                OrderTransactionSnapshotBusinessDto dto = orderTransactionSnapshotBusinessDtos.get(0);
                OrderTransactionSnapshotBusinessDto orderTransactionSnapshotBusinessDto = orderTransactionSnapshotBusinessApi.selectById(dto.getId());
                BeanUtils.copyProperties(orderTransactionSnapshotBusinessDto, orderTranscationSnapshotAppDto);
                //处理商品促销标签列表
                List<TagDTO> cxTagList = new ArrayList<>();
                if (StringUtils.isNotEmpty(orderTransactionSnapshotBusinessDto.getCxTagStr())) {
                    cxTagList = JSONArray.parseArray(orderTransactionSnapshotBusinessDto.getCxTagStr(), TagDTO.class);
                }
                orderTranscationSnapshotAppDto.setCxTagList(cxTagList);//商品促销标签集合

                //存储视频图片集合
                List<SkuImagesVideos> skuImagesVideos = new ArrayList<>();
                //获取商品视频跳转链接
                String videoUrl = orderTransactionSnapshotBusinessDto.getVideoUrl();
                if (StringUtils.isNotEmpty(videoUrl)) {
                    SkuImagesVideos imagesVideos = new SkuImagesVideos();
                    imagesVideos.setType(2);
                    imagesVideos.setVideoUrl(videoUrl);
                    skuImagesVideos.add(imagesVideos);
                }
                //定义一个存储商品所有图片（主图和附图）的集合
                List<String> imagesListArry = new ArrayList<>();
                //获取附图（是以逗号分隔的字符串）
                String imagesList = orderTransactionSnapshotBusinessDto.getImageListUrl();
                //获取附图集合
                String[] arryImagesList = StringUtils.split(imagesList, ",");
                if (StringUtils.isNotEmpty(orderTransactionSnapshotBusinessDto.getImageUrl())) {
                    SkuImagesVideos imagesVideos = new SkuImagesVideos();
                    //存储主图到图片集合
                    imagesListArry.add(orderTransactionSnapshotBusinessDto.getImageUrl());
                    imagesVideos.setType(1);
                    imagesVideos.setImageUrl(orderTransactionSnapshotBusinessDto.getImageUrl());
                    skuImagesVideos.add(imagesVideos);
                }
                //处理附图集合
                for (String skuImages : arryImagesList) {
                    if (StringUtils.isNotEmpty(skuImages)) {
                        SkuImagesVideos imagesVideos = new SkuImagesVideos();
                        //存储附图到图片集合
                        imagesListArry.add(skuImages.trim());
                        imagesVideos.setType(1);
                        imagesVideos.setImageUrl(skuImages.trim());
                        skuImagesVideos.add(imagesVideos);
                    }
                }
                //存储所有商品图片
                orderTranscationSnapshotAppDto.setImagesList(imagesListArry);
                //存储图片和视频集合
                orderTranscationSnapshotAppDto.setImagesVideosList(skuImagesVideos);
                //处方分类图片地址
                Map<String, String> drugClassificationImageMap = findCodeMap("drug_classification_code", orderTransactionSnapshotBusinessDto.getBranchCode());

                /* 更新对应的药品分类的image信息(甲类OTC，乙类OTC，处方药的图片) */
                if (MapUtils.isNotEmpty(drugClassificationImageMap)) {
                    orderTranscationSnapshotAppDto.setDrugClassificationImage(drugClassificationImageMap.get(orderTransactionSnapshotBusinessDto.getDrugClassification() + ""));
                    if (NumberUtils.toInt(orderTransactionSnapshotBusinessDto.getDrugClassification() + "", 0) == 0) {
                        orderTranscationSnapshotAppDto.setDrugClassificationImage("");
                    }
                }

                if (orderTransactionSnapshotBusinessDto.getDrugClassification() != null) {
                    switch (orderTransactionSnapshotBusinessDto.getDrugClassification()) {
                        case 1:
                            orderTranscationSnapshotAppDto.setDrugClassificationText("甲类OTC");
                            break;
                        case 2:
                            orderTranscationSnapshotAppDto.setDrugClassificationText("乙类OTC");
                            break;
                        case 3:
                            orderTranscationSnapshotAppDto.setDrugClassificationText("RX处方药");
                            break;
                        default:
                            orderTranscationSnapshotAppDto.setDrugClassificationText("其他");
                            break;
                    }
                }

                if (orderTransactionSnapshotBusinessDto.getCategoryFirstId() != null && orderTransactionSnapshotBusinessDto.getCategoryFirstId() == 68) {
                    orderTranscationSnapshotAppDto.setShelfLifeText("保质期");
                } else {
                    orderTranscationSnapshotAppDto.setShelfLifeText("有效期");
                }
                //商品描述
                if (StringUtils.isEmpty(orderTransactionSnapshotBusinessDto.getDescription())) {
                    orderTranscationSnapshotAppDto.setDescription("");
                }
                if (orderTransactionSnapshotBusinessDto.getPackageId() == null) {
                    orderTranscationSnapshotAppDto.setPackageId(0L);
                    orderTranscationSnapshotAppDto.setPackageCount(0);
                }
                if (orderTransactionSnapshotBusinessDto.getBalanceFlag() == null) {
                    orderTranscationSnapshotAppDto.setBalanceFlag(0);
                    orderTranscationSnapshotAppDto.setBalancePercent(BigDecimal.ZERO);
                }
                if (orderTransactionSnapshotBusinessDto.getPreferentialAmount() == null) {
                    orderTranscationSnapshotAppDto.setPreferentialAmount(BigDecimal.ZERO);
                }
                if (orderTransactionSnapshotBusinessDto.getPreferentialCount() == null) {
                    orderTranscationSnapshotAppDto.setPreferentialCount(0);
                }
                //处理近效期临期以及特价、直降、秒杀活动温馨提示
                if (org.apache.commons.lang.StringUtils.isNotBlank(orderTranscationSnapshotAppDto.getTagJsonStr())) {
                    List<ProductTag> tagList = JSONArray.parseArray(orderTranscationSnapshotAppDto.getTagJsonStr(), ProductTag.class);
                    tagList.stream().filter(e -> e.getValue() != null && (e.getValue() == 1 || e.getValue() == 2)).map(e -> {
                        orderTranscationSnapshotAppDto.setNearEffectiveFlag(e.getValue());
                        return e;
                    }).collect(Collectors.toList());
                }
                //查询订单明细，获取活动及效期
                OrderDetailBusinessDto orderDetailBusinessDto = new OrderDetailBusinessDto();
                orderDetailBusinessDto.setOrderNo(orderTranscationSnapshotAppDto.getOrderNo());
                if (orderTranscationSnapshotAppDto.getOrderDetailId() != null) {
                    orderDetailBusinessDto.setId(orderTranscationSnapshotAppDto.getOrderDetailId());
                }
                orderDetailBusinessDto.setSkuId(orderTranscationSnapshotAppDto.getSkuId());
                List<OrderDetailBusinessDto> orderDetails = orderDetailBusinessApi.selectList(orderDetailBusinessDto);
                OrderDetailBusinessDto matchDetail = orderDetails.stream().findAny().orElse(new OrderDetailBusinessDto());
                if (matchDetail.getType() != null && (matchDetail.getType() == OrderEnum.OrderDetailConstant.TYPE_PROMOTION.getId() ||
                        matchDetail.getType() == OrderEnum.OrderDetailConstant.TYPE_SECKILL.getId() ||
                        matchDetail.getType() == OrderEnum.OrderDetailConstant.TYPE_PLUMMET.getId())) {
                    orderTranscationSnapshotAppDto.setActivityType(99);
                }
            }
            model.addAttribute("fastDfsUrl", xyyConfig.getFastHostUrl());
            model.addAttribute("showUrl", xyyConfig.getCdnConfig().getShowUrl());
            model.addAttribute("orderTranscationSnapshotAppDto", orderTranscationSnapshotAppDto);
        } catch (Exception e) {
            log.error("OrderControllerV2.getOrderSnapshot#查询订单快照异常", e);
        }
        return "plannedOrderManagement/order/orderSnapshot";
    }

    /**
     * 通过字典名和地区名获取对应的字典Map String key String value数据
     *
     * @param codeMapName 字典名
     * @param branchCode  非字典名
     * @return 对应的key字典
     */
    public Map<String, String> findCodeMap(String codeMapName, String branchCode) throws Exception {
        Map<String, String> codeMap = new HashMap<>();
        if (StringUtils.isEmpty(codeMapName)) {
            return codeMap;
        }
        List<CodeitemBusinessDto> codeitemMap = codeitemService.selectByCodemapRTList(codeMapName, branchCode);
        if (codeitemMap != null && codeitemMap.size() > 0) {
            codeitemMap.stream().forEach(codeitemBusinessDto -> {
                codeMap.put(codeitemBusinessDto.getCode(), codeitemBusinessDto.getName());
            });
        }
        return codeMap;
    }


    /**
     * 批量完成
     *
     * @return
     */
    @GetMapping(value = "/batchOverOrderView")
    public String batchOverOrderView(Model model) {
        model.addAttribute("fileHost", ossConfig.getDownloadPath());
        model.addAttribute("fileSize", batchOverFileSize);
        model.addAttribute("rowSize", batchOverRowSize);
        return "plannedOrderManagement/order/batchOverOrderView";
    }

    @PostMapping(value = "/batchOverOrderFromExcel")
    @ResponseBody
    public ResponseVo<BatchUpdateResultVo> batchOverOrderFromExcel(@RequestParam("file") MultipartFile file) {
        String user = getUser().getRealName();
        try {
            log.info("OrderController.batchUpdateInfoFromExcel#user:{}", user);
            ImportParams importParams = new ImportParams();
            importParams.setVerifyHandler(new OrderOverOrderExcelVerifyHandler());
            List<BatchOverOrderVo> vos = ExceImportWarpUtil.importExcel(file, BatchOverOrderVo.class, importParams, batchOverFileSize, batchOverRowSize, Arrays.asList("订单号"));
            vos.forEach(item->item.setErrorMsg(null));
            BatchUpdateResultVo resultVo = orderDomainService.batchOverOrderFromExcel(user,vos,batchOverStepSize);
            BatchUpdateForExcelUtils.setErrorName(resultVo,file);
            log.info("OrderController.batchUpdateInfoFromExcel#user:{} return {}", user, JSON.toJSONString(resultVo));
            return ResponseVo.successResult(resultVo);
        } catch (ServiceException e) {
            log.warn("OrderController.batchUpdateInfoFromExcel#user:{} 失败", user, e.getMessage());
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("OrderController.batchUpdateInfoFromExcel#user:{} 异常", user, e);
            return ResponseVo.errRest("更新失败");
        }
    }
    @GetMapping("/toOrderConsignment")
    public String toOrderConsignment(@RequestParam(value = "orderNo") String orderNo,Model model){
        model.addAttribute("orderNo",orderNo);
        return "plannedOrderManagement/order/orderConsignment";
    }

    @GetMapping("/getOrderConsignment")
    @ResponseBody
    public Object getOrderConsignment(@Param("orderNo") String orderNo, @Param("offset")Integer offset,@Param("limit")Integer limit){
        PageInfo<PopOrderConsignmentDetailDto> pageInfo = orderDomainService.getPopOrderConsignmentPage(orderNo, offset, limit);
        Page page = new Page();
        Integer currentPage = pageInfo.getPageNum();
        Integer limits = pageInfo.getPageSize();
        Integer off = 0;
        if (currentPage >= 1 && limits > 0) {
            off = (currentPage - 1) * limits;
        }
        page.setTotal(pageInfo.getTotal());
        page.setOffset(off);
        page.setRows(pageInfo.getList());
        page.setCurrentPage(currentPage);
        page.setLimit(limits);
        page.setPageCount(pageInfo.getPages());
        // 追加发货数量
        //appendExt(jsonArray, orderNo);
        return page;
    }

    @ResponseBody
    @RequestMapping(value = "/queryOrderCredential", method = RequestMethod.GET)
    public Object queryOrderCredential(String orderNo) {
        try {
            LOGGER.info("queryOrderCredential request orderNo:{}", orderNo);
            ApiRPCResult<ReissueCredentialInfo> apiRPCResult = orderBusinessApi.queryOrderCredential(orderNo);
            LOGGER.info("queryOrderCredential response orderNo:{} apiRPCResult:{}", orderNo, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return this.addError("数据为空") ;

            }
            return this.addResult("data",apiRPCResult.getData()) ;
        } catch (Exception e) {
            log.error("EcOrderRemote.getOrderByOrderNo error orderNo:{}", orderNo, e);
            return this.addError("数据异常") ;
        }
    }
}
