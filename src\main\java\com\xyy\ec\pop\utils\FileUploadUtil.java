/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.xyy.ec.pop.utils;


import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.config.XyyConfig;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.awt.image.ConvolveOp;
import java.awt.image.Kernel;
import java.io.*;
import java.util.*;
import java.util.List;

/**
 * 文件上传工具类 ClassName: FileUploadUtil <br/>
 * date: 2016-1-7 上午10:06:22 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.7
 */
public class FileUploadUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileUploadUtil.class);

    //上传成功
    private static final String SUCCESS_CODE = "success";

    //上传失败
    private static final String FAILURE_CODE = "failure";

    //图片上传路径（根目录原图，min目录缩略图）
    private static final String[] IMG_PATHS = {"", "min/"};

    //缩略图宽度
    private static final int MIN_IMG_WIDTH = 320;

    private static final int MIN_IMG_HEIGHT = 320;

    /**
     * 文件上传
     *
     * @param request 请求request
     * @param uploadPath 上传文件目录
     * @param object PDNConfig或CDNConfig
     * @param targetFileName 上传文件名称
     * @param localTempPath 本地临时文件夹
     * @return
     */
    public static Map<String, Object> fileUpload(HttpServletRequest request, String uploadPath, XyyConfig.CdnConfig object, String targetFileName, String localTempPath) {
        String tempPath = null;
        tempPath = "/tmp" + File.separator + "temp" + File.separator;
        File tempFile = new File(tempPath);
        if(!tempFile.exists()){
            LOGGER.info("-------------------temp is not exists -----------------------------");
            tempFile.mkdirs();
            LOGGER.info("-------------------temp is create -----------------------------");
        }
       
        LOGGER.info("tempPath="+tempPath);
        Map<String, Object> retMap = null;
        Long fileSizes = null;
        // 创建一个通用的多部分解析器
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                request.getSession().getServletContext());
        // 判断 request 是否有文件上传,即多部分请求
        if (multipartResolver.isMultipart(request)) {
            retMap = new HashMap<String, Object>();
            // 转换成多部分request
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            // 取得request中的所有文件名
            Iterator<String> iter = multiRequest.getFileNames();
            List<Map<String, Object>> preFileList = new ArrayList<Map<String, Object>>();
            // 记录上传过程起始时的时间，用来计算上传时间
            int pre = (int) System.currentTimeMillis();
            String newFileName = null;
            List<String> fileNameList = new ArrayList<String>();
            while (iter.hasNext()) {
                // 取得上传文件
                List<MultipartFile> files = multiRequest.getFiles(iter.next());
                if (!CollectionUtils.isEmpty(files)) {
                    for (MultipartFile file : files) {
                        if (file != null) {
                            // 取得当前上传文件的文件名称
                            String myFileName = file.getOriginalFilename();
                            // 如果名称不为“”,说明该文件存在，否则说明该文件不存在
                            if (!StringUtils.isEmpty(myFileName)) {
                                System.out.println(myFileName);
                                // 重命名上传后的文件名
                                // String fileName= file.getOriginalFilename();
                                // 获取图片的扩展名
                                String extensionName = myFileName
                                        .substring(myFileName.lastIndexOf(".") + 1);
                                // 文件名（扩展名之前的名字）
                                String fileName = myFileName.substring(0,
                                        myFileName.lastIndexOf("."));
                                // 新的图片文件名 = 获取时间戳+"."图片扩展名
                                // String newFileName = fileName +
                                // String.valueOf(System.nanoTime())
                                // + "." + extensionName;
                                //是否指定上传文件名
                                if (StringUtils.isEmpty(targetFileName)) {
                                    newFileName = UUID.randomUUID()
                                            .toString() + "." + extensionName;
                                } else {
                                    newFileName = targetFileName.concat(".").concat(extensionName);
                                }
                                Map<String, Object> preFileMap = new HashMap<String, Object>();
                                preFileMap.put("fileName", newFileName);// 上传到FTP服务器文件名字
                                preFileMap.put("fileSize", (file.getSize() / 1024));//文件大小(单位：KB)
                                fileSizes = file.getSize() / 1024;
                                LOGGER.info("+++++++++++++++++++++++++++"+file.getSize());
                                fileNameList.add(newFileName);
                                try {
                                    preFileMap.put("fileInputStream",
                                            file.getInputStream());// 上传输入文件流
                                } catch (IOException ex) {
                                    LOGGER.error("文件上传异常:" + ex);
                                    retMap.put("status", FAILURE_CODE);
//									result = "文件上传异常" + ex;
                                }
                                preFileList.add(preFileMap);
                            }
                        }
                    }
                }
            }
            boolean isUploadSuccess = false;

            try {
                if (true) {
                    isUploadSuccess = uploadFile(object.getCdnHostname(),
                            Integer.parseInt(object.getCdnPort()), object.getCdnUsername(),
                            object.getCdnPassword(), object.getCdnUploadPath()
                                    + uploadPath, preFileList, tempPath);

                }

                // 记录上传该文件后的时间
                int finaltime = (int) System.currentTimeMillis();
                LOGGER.info("上传文件耗时：" + (finaltime - pre));
                LOGGER.info("上传文件耗时：" + (finaltime - pre));

                if (isUploadSuccess) {
                    if(LOGGER.isDebugEnabled()){
                        LOGGER.debug("上传成功");
                    }
                    retMap.put("status", SUCCESS_CODE);
                    retMap.put("fileName", fileNameList);
                    retMap.put("fileSize", fileSizes);
                } else {
                    
                    retMap.put("status", FAILURE_CODE);
                }
            } catch (Exception e) {
                
                retMap.put("status", FAILURE_CODE);
            }
        }
        return retMap;
    }



    
    /**
     * 图片文件上传
     *
     * @param request 请求request
     * @param uploadPath 上传文件目录
     * @param object PDNConfig或CDNConfig
     * @param targetFileName 上传文件名称
     * @param localTempPath 本地临时文件夹
     * @return
     * <AUTHOR>
     */
    public static Map<String, Object> imageUpload(String imgType, HttpServletRequest request, String uploadPath, XyyConfig.CdnConfig object, String targetFileName, String localTempPath) throws IOException {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String tempPath = null;
        if (StringUtils.isEmpty(localTempPath)) {
            tempPath = getWebRootAbsolutePath() + File.separator + "temp" + File.separator;
        } else {
            tempPath = localTempPath + File.separator + "temp" + File.separator;
        }
        // 原图属性枚举
        ImgConstant.UpLoadImgAttr big = null;
        // 缩略图属性枚举
        ImgConstant.UpLoadImgAttr small = null;
        // 商品原图
        if ("1".equals(imgType)) {
            big = ImgConstant.UpLoadImgAttr.shangpinyuantu_big;
            small = ImgConstant.UpLoadImgAttr.shangpinyuantu_small;
        } else if ("2".equals(imgType)){ // 商品说明图
            big = ImgConstant.UpLoadImgAttr.shangpinshuomingshu;
            small = ImgConstant.UpLoadImgAttr.shangpinshuomingshu;
        }
       
        LOGGER.info("tempPath="+tempPath);
        Long fileSizes = null;
        // 创建一个通用的多部分解析器
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                request.getSession().getServletContext());
        // 判断 request 是否有文件上传,即多部分请求
        if (multipartResolver.isMultipart(request)) {

            // 转换成多部分request
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            // 取得request中的所有文件名
            Iterator<String> iter = multiRequest.getFileNames();
            List<Map<String, Object>> preFileList = new ArrayList<Map<String, Object>>();
            // 记录上传过程起始时的时间，用来计算上传时间
            int pre = (int) System.currentTimeMillis();
            String newFileName = null;
            List<String> fileNameList = new ArrayList<String>();
            while (iter.hasNext()) {
                // 取得上传文件
                List<MultipartFile> files = multiRequest.getFiles(iter.next());
                if (!CollectionUtils.isEmpty(files)) {
                    for (MultipartFile file : files) {
                        // 验证图片尺寸
                        BufferedImage src = ImageIO.read(file.getInputStream());
                        // 判断是否限宽
                        if (big.isCheckWidth()) {
                            // 验证宽度
                            if (src.getWidth(null) < big.getWidth()) {
                                retMap.put("status", FAILURE_CODE);
                                retMap.put("msg", "上传图片尺寸不符合要求，请重新上传");
                                return retMap;
                            }
                        }
                        //  判断是否限高
                        if (big.isCheckHeight()) {
                            // 验证高度
                            if (src.getHeight(null) < big.getHeight()) {
                                retMap.put("status", FAILURE_CODE);
                                retMap.put("msg", "上传图片尺寸不符合要求，请重新上传");
                                return retMap;
                            }
                        }
                        // 取得当前上传文件的文件名称
                        String myFileName = file.getOriginalFilename();
                        // 如果名称不为“”,说明该文件存在，否则说明该文件不存在
                        if (!StringUtils.isEmpty(myFileName)) {
                            // 获取图片的扩展名
                            String extensionName = myFileName.substring(myFileName.lastIndexOf(".") + 1);
                            //是否指定上传文件名
                            if (!StringUtils.isEmpty(targetFileName)) {
                                newFileName = targetFileName + "_" + RandomUtil.getAuthCode(8) + "." + extensionName;
                                LOGGER.info("---------------------------"+newFileName);
                            }else{
                                newFileName = UUID.randomUUID().toString() + "." + extensionName;
                                LOGGER.info("---------------------------"+newFileName);
                            }
                            Map<String, Object> preFileMap = new HashMap<String, Object>();
                            preFileMap.put("fileName", newFileName);// 上传到FTP服务器文件名字
                            preFileMap.put("fileSize", (file.getSize() / 1024));//文件大小(单位：KB)
                            fileSizes = file.getSize() / 1024;
                            LOGGER.info("+++++++++++++++++++++++++++"+file.getSize());
                            fileNameList.add(newFileName);
                            preFileMap.put("fileInputStream", file.getInputStream());// 上传输入文件流
                            preFileMap.put("bufferedImage", src);
                            preFileList.add(preFileMap);
                        }
                    }
                }
            }
            boolean isUploadSuccess = false;

            isUploadSuccess = uploadFile(big, small, object.getCdnHostname(), Integer.parseInt(object.getCdnPort()), object.getCdnUsername(), object.getCdnPassword(), object.getCdnUploadPath() + uploadPath, preFileList, tempPath);

            // 记录上传该文件后的时间
            int finaltime = (int) System.currentTimeMillis();
            LOGGER.info("上传文件耗时：" + (finaltime - pre));
            if (isUploadSuccess) {
                LOGGER.debug("上传成功");
                retMap.put("status", SUCCESS_CODE);
                retMap.put("fileName", fileNameList);
                retMap.put("fileSize", fileSizes);
            } else {
                
                retMap.put("status", FAILURE_CODE);
            }
        }
        return retMap;
    }
    

    /**
     * 上传文件（可供Action/Controller层使用）
     *
     * @param hostname FTP服务器地址
     * @param port FTP服务器端口号
     * @param username FTP登录帐号
     * @param password FTP登录密码
     * @param pathname FTP服务器保存目录
     * @return
     */
    private static boolean uploadFile(ImgConstant.UpLoadImgAttr big, ImgConstant.UpLoadImgAttr small, String hostname, int port,
                                      String username, String password, String pathname,
                                      List<Map<String, Object>> preFileList, String tempPath) {
        boolean flag = false;
        FTPClient ftpClient = new FTPClient();
        ftpClient.setControlEncoding("UTF-8");
        try {
            // 连接FTP服务器
            ftpClient.connect(hostname, port);
            // 登录FTP服务器
            ftpClient.login(username, password);
            // 是否成功登录FTP服务器
            int replyCode = ftpClient.getReplyCode();

            LOGGER.info("replyCode:"+replyCode);
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                return flag;
            }

            LOGGER.info("上传文件路径pathname:"+pathname);

            ftpClient.enterLocalActiveMode();// 主动模式
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
//            LOGGER.info("preFileList:"+ JSON.toJSONString(preFileList));
            if (!CollectionUtils.isEmpty(preFileList)) {
                for (Map<String, Object> map : preFileList) {
                    String fileName = map.get("fileName").toString();
                    InputStream inputStream = (InputStream) map.get("fileInputStream");
                    // 获取图片尺寸
                    BufferedImage src = (BufferedImage)map.get("bufferedImage");

                    // 上传图片的临时文件
                    StringTokenizer tmptokenizer = new StringTokenizer(fileName, ".");
                    String tmpFileName = tmptokenizer.nextToken().concat("-tmp.").concat(tmptokenizer.nextToken());
                    File tmpFile = new File(tempPath + tmpFileName);
                    inputstreamtofile(inputStream, tmpFile);
                    File outFile = null;
                    for (int i = 0; i < IMG_PATHS.length; i++) {
                        ftpClient.changeWorkingDirectory("/");
                        mkdirs(ftpClient, pathname + IMG_PATHS[i]);

                        if (i == 0) {//原图
                            // 待上传文件
                            outFile = new File(tempPath + fileName);
                            if (big.isDengbiYs()) {// 已宽为参照，等比压缩
                                ImgUtils.proportionZoom(tmpFile, NumberUtils.toDouble(src.getWidth() + "") / NumberUtils.toDouble(big.getWidth() + ""), outFile);
                            } else {// 定宽定高压缩
                                ImgUtils.sizeZoom(tmpFile, big.getWidth(), big.getHeight(), big.isDengbiYs(), outFile);
                            }

                            ftpClient.storeFile(fileName, new FileInputStream(outFile));
                        } else {//缩略图
                            StringTokenizer tokenizer = new StringTokenizer(fileName, ".");
                            String minFileName = tokenizer.nextToken().concat("-min.").concat(tokenizer.nextToken());
                            // 待上传文件
                            outFile = new File(tempPath + minFileName);
                            ImageScale(tmpFile, outFile);

                            if (big.isDengbiYs()) {// 已宽为参照，等比压缩
                                ImgUtils.proportionZoom(tmpFile, NumberUtils.toDouble(src.getWidth() + "") / NumberUtils.toDouble(small.getWidth() + ""), outFile);
                            } else {// 定宽定高压缩
                                ImgUtils.sizeZoom(tmpFile, small.getWidth(), small.getHeight(), small.isDengbiYs(), outFile);
                            }
                            ftpClient.storeFile(fileName, new FileInputStream(outFile));
                        }
                    }
                }
            }
            delAllFile(tempPath);
            ftpClient.logout();
            flag = true;
        } catch (IOException ex) {
            LOGGER.error("文件上传异常：" + ex);
        } catch (Exception ex) {
            LOGGER.error("文件上传异常：" + ex);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (Exception e) {
                    LOGGER.error("文件上传异常：" + e);
                }
            }
        }
        return flag;
    }

    /**
     * 视频上传
     *
     * @param request 请求request
     * @param uploadPath 上传文件目录
     * @param object PDNConfig或CDNConfig
     * @param targetFileName 上传文件名称
     * @param localTempPath 本地临时文件夹
     * @return
     */
    public static Map<String, Object> videoUpload(HttpServletRequest request, String uploadPath, XyyConfig.CdnConfig object, String targetFileName, String localTempPath) {
        String tempPath = null;
        if (StringUtils.isEmpty(localTempPath)) {
            tempPath = getWebRootAbsolutePath() + File.separator + "temp" + File.separator;
        } else {
            tempPath = localTempPath + File.separator + "temp" + File.separator;
        }
       
        LOGGER.info("tempPath="+tempPath);
        Map<String, Object> retMap = null;
        Long fileSizes = null;
        // 创建一个通用的多部分解析器
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                request.getSession().getServletContext());
        // 判断 request 是否有文件上传,即多部分请求
        if (multipartResolver.isMultipart(request)) {
            retMap = new HashMap<>();
            // 转换成多部分request
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            // 取得request中的所有文件名
            Iterator<String> iter = multiRequest.getFileNames();
            List<Map<String, Object>> preFileList = new ArrayList<>();
            // 记录上传过程起始时的时间，用来计算上传时间
            int pre = (int) System.currentTimeMillis();
            String newFileName = null;
            List<String> fileNameList = new ArrayList<>();
            while (iter.hasNext()) {
                // 取得上传文件
                List<MultipartFile> files = multiRequest.getFiles(iter.next());
                if (!CollectionUtils.isEmpty(files)) {
                    for (MultipartFile file : files) {
                        if (file != null) {
                            // 取得当前上传文件的文件名称
                            String myFileName = file.getOriginalFilename();
                            LOGGER.info("~~~~~~~~~~当前上传的文件名："+myFileName+"~~~~~~~~~~");
                            // 如果名称不为“”,说明该文件存在，否则说明该文件不存在
                            if (!StringUtils.isEmpty(myFileName)) {
                                // 重命名上传后的文件名
                                // String fileName= file.getOriginalFilename();
                                // 获取图片的扩展名
                                String extensionName = myFileName
                                        .substring(myFileName.lastIndexOf(".") + 1);
                                //是否指定上传文件名
                                if (StringUtils.isEmpty(targetFileName)) {
                                    newFileName = UUID.randomUUID()
                                            .toString() + "." + extensionName;
                                    LOGGER.info("@@@@@@@@@@@@上传的新的文件名："+newFileName+"@@@@@@@@@@@@@@@@");
                                } else {
                                    newFileName = targetFileName.concat(".").concat(extensionName);
                                }
                                Map<String, Object> preFileMap = new HashMap<String, Object>();
                                preFileMap.put("fileName", newFileName);// 上传到FTP服务器文件名字
                                preFileMap.put("fileSize", (file.getSize() / 1024));//文件大小(单位：KB)
                                fileSizes = file.getSize() / 1024;
                                fileNameList.add(newFileName);
                                try {
                                    preFileMap.put("fileInputStream",
                                            file.getInputStream());// 上传输入文件流
                                } catch (IOException ex) {
                                    LOGGER.error("文件上传异常:" + ex);
                                    retMap.put("status", FAILURE_CODE);
//									result = "文件上传异常" + ex;
                                }
                                preFileList.add(preFileMap);
                            }
                        }
                    }
                }
            }
            boolean isUploadSuccess = false;

            try {
                isUploadSuccess = uploadVideo(object.getCdnHostname(),
                        Integer.parseInt(object.getCdnPort()), object.getCdnUsername(),
                        object.getCdnPassword(), object.getCdnUploadPath()
                                    + uploadPath, preFileList, tempPath);

                // 记录上传该文件后的时间
                int finaltime = (int) System.currentTimeMillis();
                LOGGER.info("上传文件耗时：" + (finaltime - pre));

                LOGGER.info("----是否上传成功："+isUploadSuccess+"-----");
                if (isUploadSuccess) {
                    if(LOGGER.isDebugEnabled()){
                        LOGGER.debug("上传成功");
                    }
                    retMap.put("status", SUCCESS_CODE);
                    retMap.put("fileName", fileNameList);
                    retMap.put("fileSize", fileSizes);
                    LOGGER.info("返回数据："+retMap);
                } else {
                    
                    retMap.put("status", FAILURE_CODE);
                }
            } catch (Exception e) {
                
                retMap.put("status", FAILURE_CODE);
            }
        }
        return retMap;
    }
    /**
     * 上传单个简单文件
     * @param xyyConfig
     * @param inputStream
     * @param uploadPath
     * @return
     */
    public static boolean uploadFile(XyyConfig xyyConfig,InputStream inputStream,String uploadPath,String fileName){
        LOGGER.info("uploadFile cdnConfig:{},uploadPath:{},fileName:{}", JSON.toJSON(xyyConfig.getCdnConfig()),uploadPath,fileName);
        FTPClient ftpClient = new FTPClient();
        ftpClient.setControlEncoding("UTF-8");
        boolean flag = false;
        try {
            // 连接FTP服务器
            ftpClient.connect(xyyConfig.getCdnConfig().getCdnHostname(), NumberUtils.toInt(xyyConfig.getCdnConfig().getCdnPort()));
            // 登录FTP服务器
            ftpClient.login(xyyConfig.getCdnConfig().getCdnUsername(), xyyConfig.getCdnConfig().getCdnPassword());
            // 是否成功登录FTP服务器
            int replyCode = ftpClient.getReplyCode();

            ftpClient.enterLocalActiveMode();// 主动模式
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);

            if (!FTPReply.isPositiveCompletion(replyCode)) {
                return flag;
            }
            mkdirs(ftpClient, uploadPath);
            ftpClient.storeFile(fileName, inputStream);

            ftpClient.logout();
            flag = true;
        }catch (Exception e){
            LOGGER.error("uploadFile",e);
            throw new XyyEcPopException("文件上传到ftp失败");
        }finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (Exception e) {
                    LOGGER.error("文件上传异常", e);
                }
            }
            try {
                inputStream.close();
            } catch (IOException e) {
                LOGGER.error("关闭文件流异常", e);
            }
        }
        return flag;
    }
    /**
     * 上传单个简单文件
     * @param xyyConfig
     * @param file
     * @param uploadPath
     * @return
     */
    public static boolean uploadFile(XyyConfig xyyConfig,File file,String uploadPath){
        LOGGER.info("uploadFile cdnConfig:{},uploadPath:{},fileName:{}", JSON.toJSON(xyyConfig.getCdnConfig()),uploadPath,file.getName());
        FTPClient ftpClient = new FTPClient();
        ftpClient.setControlEncoding("UTF-8");
        boolean flag = false;
        try {
            // 连接FTP服务器
            ftpClient.connect(xyyConfig.getCdnConfig().getCdnHostname(), NumberUtils.toInt(xyyConfig.getCdnConfig().getCdnPort()));
            // 登录FTP服务器
            ftpClient.login(xyyConfig.getCdnConfig().getCdnUsername(), xyyConfig.getCdnConfig().getCdnPassword());
            // 是否成功登录FTP服务器
            int replyCode = ftpClient.getReplyCode();

            ftpClient.enterLocalActiveMode();// 主动模式
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);

            if (!FTPReply.isPositiveCompletion(replyCode)) {
                return flag;
            }
            mkdirs(ftpClient, uploadPath);
            ftpClient.storeFile(file.getName(), new FileInputStream(file));

            file.delete();
            ftpClient.logout();
            flag = true;
        }catch (Exception e){
            LOGGER.error("uploadFile",e);
            throw new XyyEcPopException("文件上传到ftp失败");
        }finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (Exception e) {
                    LOGGER.error("文件上传异常", e);
                }
            }
        }
        return flag;
    }

    /**
     * 上传文件（可供Action/Controller层使用）
     *
     * @param hostname FTP服务器地址
     * @param port FTP服务器端口号
     * @param username FTP登录帐号
     * @param password FTP登录密码
     * @param pathname FTP服务器保存目录
     * @return
     */
    private static boolean uploadFile(String hostname, int port,
            String username, String password, String pathname,
            List<Map<String, Object>> preFileList, String tempPath) {
        LOGGER.info("@@@@hostname:"+hostname+"@@@@");
        LOGGER.info("@@@@port:"+port+"@@@@");
        LOGGER.info("@@@@username:"+username+"@@@@");
        LOGGER.info("@@@@pathname:"+pathname+"@@@@");
        LOGGER.info("@@@@tempPath:"+tempPath+"@@@@");
        boolean flag = false;
        FTPClient ftpClient = new FTPClient();
        ftpClient.setControlEncoding("UTF-8");
        try {
            // 连接FTP服务器
            ftpClient.connect(hostname, port);
            // 登录FTP服务器
            ftpClient.login(username, password);
            // 是否成功登录FTP服务器
            int replyCode = ftpClient.getReplyCode();

            if (!FTPReply.isPositiveCompletion(replyCode)) {
                return flag;
            }

            LOGGER.info("================ ftl is connect ==============================");
            ftpClient.enterLocalActiveMode();// 主动模式
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            if (!CollectionUtils.isEmpty(preFileList)) {
                for (Map<String, Object> map : preFileList) {
                    String fileName = map.get("fileName").toString();
                    InputStream inputStream = (InputStream) map.get("fileInputStream");
                    File originalFile = new File(tempPath + fileName);
                    inputstreamtofile(inputStream, originalFile);
                    File outFile = null;
                    for (int i = 0; i < IMG_PATHS.length; i++) {
                        ftpClient.changeWorkingDirectory("/");
                        mkdirs(ftpClient, pathname + IMG_PATHS[i]);
                        if (i == 0) {//原图
                            ftpClient.storeFile(fileName, new FileInputStream(originalFile));
                        } else {//缩略图
                            StringTokenizer tokenizer = new StringTokenizer(fileName, ".");
                            String minFileName = tokenizer.nextToken().concat("-min.").concat(tokenizer.nextToken());
                            outFile = new File(tempPath + minFileName);
                            ImageScale(originalFile, outFile);
                            ftpClient.storeFile(fileName, new FileInputStream(outFile));
                        }
                    }
                }
            }
            delAllFile(tempPath);
            ftpClient.logout();
            flag = true;
        } catch (IOException ex) {
            ex.printStackTrace();
            System.out.println("文件上传异常：" + ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("文件上传异常：" + ex);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (Exception e) {
                    e.printStackTrace();
                    System.out.println("文件上传异常：" + e);
                }
            }
        }
        return flag;
    }
    
    /**
     * 简单的单个文件上传
     * @Title: uploadFile
     * @param hostname
     * @param port
     * @param username
     * @param password
     * @param pathname
     * @param dataMap
     * @param tempPath
     * @return
     * boolean
     * <AUTHOR> 
     * @date 2018年3月8日 下午4:51:17
     */
    private static boolean uploadFile(String hostname, int port,
            String username, String password, String pathname,
           Map<String, Object> dataMap, String tempPath) {
        LOGGER.info("@@@@hostname:"+hostname+"@@@@");
        LOGGER.info("@@@@port:"+port+"@@@@");
        LOGGER.info("@@@@username:"+username+"@@@@");
        LOGGER.info("@@@@pathname:"+pathname+"@@@@");
        LOGGER.info("@@@@tempPath:"+tempPath+"@@@@");
        boolean flag = false;
        FTPClient ftpClient = new FTPClient();
        ftpClient.setControlEncoding("UTF-8");
        try {
            // 连接FTP服务器
            ftpClient.connect(hostname, port);
            // 登录FTP服务器
            ftpClient.login(username, password);
            // 是否成功登录FTP服务器
            int replyCode = ftpClient.getReplyCode();

            if (!FTPReply.isPositiveCompletion(replyCode)) {
                return flag;
            }

            ftpClient.enterLocalActiveMode();// 主动模式
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            String fileName = dataMap.get("fileName").toString();
            InputStream inputStream = (InputStream) dataMap.get("fileInputStream");
            File originalFile = new File(tempPath + fileName);
            inputstreamtofile(inputStream, originalFile);
            ftpClient.changeWorkingDirectory("/");
            mkdirs(ftpClient, pathname);
            ftpClient.storeFile(fileName, new FileInputStream(originalFile));
            
            delAllFile(tempPath);
            ftpClient.logout();
            flag = true;
        } catch (IOException ex) {
            LOGGER.error("文件上传异常：" + ex);
        } catch (Exception ex) {
            LOGGER.error("文件上传异常：" + ex);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (Exception e) {
                    LOGGER.error("文件上传异常：" + e);
                }
            }
        }
        return flag;
    }

    
    /**
     * 上传视频（可供Action/Controller层使用）
     *
     * @param hostname FTP服务器地址
     * @param port FTP服务器端口号
     * @param username FTP登录帐号
     * @param password FTP登录密码
     * @param pathname FTP服务器保存目录
     * @return
     */
    private static boolean uploadVideo(String hostname, int port,
                                      String username, String password, String pathname,
                                      List<Map<String, Object>> preFileList, String tempPath) {
        boolean flag = false;
        FTPClient ftpClient = new FTPClient();
        ftpClient.setControlEncoding("UTF-8");
        try {
            // 连接FTP服务器
            ftpClient.connect(hostname, port);
            // 登录FTP服务器
            ftpClient.login(username, password);
            // 是否成功登录FTP服务器
            int replyCode = ftpClient.getReplyCode();

            if (!FTPReply.isPositiveCompletion(replyCode)) {
                return flag;
            }

            ftpClient.enterLocalActiveMode();// 主动模式
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            if (!CollectionUtils.isEmpty(preFileList)) {
                for (Map<String, Object> map : preFileList) {
                    String fileName = map.get("fileName").toString();
                    LOGGER.info("$$$$$$$$$$$$即将上传的上传的文件名："+fileName+"$$$$$$$$$$$$$$$$$$");
                    InputStream inputStream = (InputStream) map
                            .get("fileInputStream");
                    File originalFile = new File(tempPath + fileName);
                    inputstreamtofile(inputStream, originalFile);
                    ftpClient.changeWorkingDirectory("/");
                    mkdirs(ftpClient, pathname);
                    LOGGER.info("ftpClient信息："+ftpClient.toString());
                    boolean bl = ftpClient.storeFile(fileName, new FileInputStream(originalFile));
                    LOGGER.info("上传后返回信息："+bl);
                }
            }
            delAllFile(tempPath);
            ftpClient.logout();
            flag = true;
        } catch (IOException ex) {
            LOGGER.error("文件上传异常：" + ex);
        } catch (Exception ex) {
            LOGGER.error("文件上传异常：" + ex);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (Exception e) {
                    LOGGER.error("文件上传异常：" + e);
                }
            }
        }
        return flag;
    }

    private static void mkdirs(FTPClient client, String dir) throws Exception {
        if (dir == null) {
            return;
        }
        dir = dir.replace("//", "/");
        String[] dirs = dir.split("/");
        for (int i = 0; i < dirs.length; i++) {
            dir = dirs[i];
            if (!StringUtils.isEmpty(dir)) {
                if (!isExist(dir)) {
                    client.makeDirectory(dir);
                }
                client.changeWorkingDirectory(dir);
            }
        }
    }

    private static boolean isExist(String path) {
        File file = new File(File.separator + path);
        // 判断文件夹是否存在,如果不存在则创建文件夹
        if (!file.exists()) {
            return false;
        }
        return true;
    }


    private static void inputstreamtofile(InputStream ins, File file) throws IOException {
        LOGGER.info("---------begin inputstreamtoFile ------------------------" + file.getPath() +" =============");
        try(OutputStream os = new FileOutputStream(file);){
            LOGGER.info("-232--------------------" + os.toString());
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            ins.close();
        }catch (Exception se){
            LOGGER.error(se.getMessage());
        }

    }

    /**
     * 获取WEBROOT绝对路径
     *
     * @return
     */
    private static String getWebRootAbsolutePath() {
        ClassLoader classLoader = Thread.currentThread()
                .getContextClassLoader();
        if (classLoader == null) {
            classLoader = ClassLoader.getSystemClassLoader();
        }
        java.net.URL url = classLoader.getResource("");
        String ROOT_CLASS_PATH = url.getPath() + "/";
        File rootFile = new File(ROOT_CLASS_PATH);
        String WEB_INFO_DIRECTORY_PATH = rootFile.getParent() + "/";
        File webInfoDir = new File(WEB_INFO_DIRECTORY_PATH);
        String SERVLET_CONTEXT_PATH = webInfoDir.getParent() + "/";
        System.out.println("----getWebRootAbsolutePath------");
        return SERVLET_CONTEXT_PATH;
    }

    // 删除指定文件夹下所有文件
    private static boolean delAllFile(String path) {
        boolean flag = false;
        File file = new File(path);
        if (!file.exists()) {
            return flag;
        }
        if (!file.isDirectory()) {
            return flag;
        }
        String[] tempList = file.list();
        File temp = null;
        for (int i = 0; i < tempList.length; i++) {
            if (path.endsWith(File.separator)) {
                temp = new File(path + tempList[i]);
            } else {
                temp = new File(path + File.separator + tempList[i]);
            }
            if (temp.isFile()) {
                temp.delete();
            }
            if (temp.isDirectory()) {
                delAllFile(path + "/" + tempList[i]);// 先删除文件夹里面的文件
//                delFolder(path + "/" + tempList[i]);// 再删除空文件夹
                flag = true;
            }
        }
        return flag;
    }

    private static void ImageScale(File sourceFile, File targetFile) throws IOException {
        Image image = ImageIO.read(sourceFile);
        int imageWidth = image.getWidth(null);
        int imageHeight = image.getHeight(null);
        float scale = getRatio(imageWidth, imageHeight, MIN_IMG_WIDTH, MIN_IMG_HEIGHT);
        imageWidth = (int) (scale * imageWidth);
        imageHeight = (int) (scale * imageHeight);

        image = image.getScaledInstance(imageWidth, imageHeight, Image.SCALE_AREA_AVERAGING);
        // Make a BufferedImage from the Image.
        BufferedImage mBufferedImage = new BufferedImage(imageWidth, imageHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2 = mBufferedImage.createGraphics();

        g2.drawImage(image, 0, 0, imageWidth, imageHeight, Color.white, null);
        g2.dispose();

        float[] kernelData2 = {
                -0.125f, -0.125f, -0.125f,
                -0.125f, 2, -0.125f,
                -0.125f, -0.125f, -0.125f};
        Kernel kernel = new Kernel(3, 3, kernelData2);
        ConvolveOp cOp = new ConvolveOp(kernel, ConvolveOp.EDGE_NO_OP, null);
        mBufferedImage = cOp.filter(mBufferedImage, null);
        FileOutputStream out = new FileOutputStream(targetFile);
        ImageIO.write(mBufferedImage, "jpeg", out);
        out.close();
    }

    private static float getRatio(int width, int height, int maxWidth, int maxHeight) {
        float Ratio = 1.0f;
        float widthRatio;
        float heightRatio;
        widthRatio = (float) maxWidth / width;
        heightRatio = (float) maxHeight / height;
        if (widthRatio < 1.0 || heightRatio < 1.0) {
            Ratio = widthRatio <= heightRatio ? widthRatio : heightRatio;
        }
        return Ratio;
    }


    /**
     * 文件上传
     *
     * @param request 请求request
     * @param uploadPath 上传文件目录
     * @param object PDNConfig或CDNConfig
     * @param targetFileName 上传文件名称
     * @param localTempPath 本地临时文件夹
     * @param fileSize 用于控制文件上传大小
     * @return
     */
    public static Map<String, Object> fileUploadMax(HttpServletRequest request, String uploadPath, XyyConfig.CdnConfig object, String targetFileName, String localTempPath, Long fileSize) {
        String tempPath = null;
//        if (StringUtils.isEmpty(localTempPath)) {
//            tempPath = getWebRootAbsolutePath() + File.separator + "temp" + File.separator;
//        } else {
//            tempPath = localTempPath + File.separator + "temp" + File.separator;
//        }
        tempPath = "/tmp" + File.separator + "temp" + File.separator;
        File tempFile = new File(tempPath);
        if(!tempFile.exists()){
            LOGGER.info("-------------------temp is not exists -----------------------------");
            tempFile.mkdirs();
            LOGGER.info("-------------------temp is create -----------------------------");
        }
       
        LOGGER.info("tempPath="+tempPath);
        Map<String, Object> retMap = null;
        Long fileSizes = null;
        // 创建一个通用的多部分解析器
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                request.getSession().getServletContext());
        // 判断 request 是否有文件上传,即多部分请求
        if (multipartResolver.isMultipart(request)) {
            retMap = new HashMap<String, Object>();
            // 转换成多部分request
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            // 取得request中的所有文件名
            Iterator<String> iter = multiRequest.getFileNames();
            List<Map<String, Object>> preFileList = new ArrayList<Map<String, Object>>();
            // 记录上传过程起始时的时间，用来计算上传时间
            int pre = (int) System.currentTimeMillis();
            String newFileName = null;
            List<String> fileNameList = new ArrayList<String>();
            while (iter.hasNext()) {
                // 取得上传文件
                List<MultipartFile> files = multiRequest.getFiles(iter.next());
                if (!CollectionUtils.isEmpty(files)) {
                    for (MultipartFile file : files) {
                        if (file != null) {
                            // 取得当前上传文件的文件名称
                            String myFileName = file.getOriginalFilename();
                            if(fileSize!=null){
                                 long lon=file.getSize();
                                if(file.getSize()>fileSize){
                                    retMap.put("status", "max");
                                    return retMap;
                                }
                            }
                            // 如果名称不为“”,说明该文件存在，否则说明该文件不存在
                            if (!StringUtils.isEmpty(myFileName)) {
                                System.out.println(myFileName);
                                // 重命名上传后的文件名
                                // String fileName= file.getOriginalFilename();
                                // 获取图片的扩展名
                                String extensionName = myFileName
                                        .substring(myFileName.lastIndexOf(".") + 1);
                                // 文件名（扩展名之前的名字）
                                String fileName = myFileName.substring(0,
                                        myFileName.lastIndexOf("."));
                                // 新的图片文件名 = 获取时间戳+"."图片扩展名
                                // String newFileName = fileName +
                                // String.valueOf(System.nanoTime())
                                // + "." + extensionName;
                                //是否指定上传文件名
                                if (StringUtils.isEmpty(targetFileName)) {
                                    newFileName = UUID.randomUUID()
                                            .toString() + "." + extensionName;
                                } else {
                                    newFileName = targetFileName.concat(".").concat(extensionName);
                                }
                                Map<String, Object> preFileMap = new HashMap<String, Object>();
                                preFileMap.put("fileName", newFileName);// 上传到FTP服务器文件名字
                                preFileMap.put("fileSize", (file.getSize() / 1024));//文件大小(单位：KB)
                                fileSizes = file.getSize() / 1024;
                                LOGGER.info("+++++++++++++++++++++++++++"+file.getSize());
                                fileNameList.add(newFileName);
                                try {
                                    preFileMap.put("fileInputStream",
                                            file.getInputStream());// 上传输入文件流
                                } catch (IOException ex) {
                                    LOGGER.error("文件上传异常:" + ex);
                                    retMap.put("status", FAILURE_CODE);
//									result = "文件上传异常" + ex;
                                }
                                preFileList.add(preFileMap);
                            }
                        }
                    }
                }
            }
            boolean isUploadSuccess = false;

            try {
                if (true) {
                    isUploadSuccess = uploadFile(object.getCdnHostname(),
                            Integer.parseInt(object.getCdnPort()), object.getCdnUsername(),
                            object.getCdnPassword(), object.getCdnUploadPath()
                                    + uploadPath, preFileList, tempPath);

                }

                // 记录上传该文件后的时间
                int finaltime = (int) System.currentTimeMillis();
                LOGGER.info("上传文件耗时：" + (finaltime - pre));
                LOGGER.info("上传文件耗时：" + (finaltime - pre));

                if (isUploadSuccess) {
                    if(LOGGER.isDebugEnabled()){
                        LOGGER.debug("上传成功");
                    }
                    retMap.put("status", SUCCESS_CODE);
                    retMap.put("fileName", fileNameList);
                    retMap.put("fileSize", fileSizes);
                } else {
                    
                    retMap.put("status", FAILURE_CODE);
                }
            } catch (Exception e) {
                
                retMap.put("status", FAILURE_CODE);
            }
        }
        return retMap;
    }

    /**
     * 图片文件上传
     * 无图片格式校验
     * @param request 请求request
     * @param uploadPath 上传文件目录
     * @param object PDNConfig或CDNConfig
     * @param targetFileName 上传文件名称
     * @param localTempPath 本地临时文件夹
     * @return
     * <AUTHOR>
     */
    public static Map<String, Object> uploadThirdImg(String imgType, HttpServletRequest request, String uploadPath, XyyConfig.CdnConfig object, String targetFileName, String localTempPath) throws IOException {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String tempPath = null;
        if (StringUtils.isEmpty(localTempPath)) {
            tempPath = getWebRootAbsolutePath() + File.separator + "temp" + File.separator;
        } else {
            tempPath = localTempPath + File.separator + "temp" + File.separator;
        }
        // 原图属性枚举
        ImgConstant.UpLoadImgAttr big = null;
        // 缩略图属性枚举
        ImgConstant.UpLoadImgAttr small = null;
        // 商品原图
        if ("1".equals(imgType)) {
            big = ImgConstant.UpLoadImgAttr.shangpinyuantu_big;
            small = ImgConstant.UpLoadImgAttr.shangpinyuantu_small;
        } else if ("2".equals(imgType)){ // 商品说明图
            big = ImgConstant.UpLoadImgAttr.shangpinshuomingshu;
            small = ImgConstant.UpLoadImgAttr.shangpinshuomingshu;
        }
       
        LOGGER.info("tempPath="+tempPath);
        Long fileSizes = null;
        // 创建一个通用的多部分解析器
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                request.getSession().getServletContext());
        // 判断 request 是否有文件上传,即多部分请求
        if (multipartResolver.isMultipart(request)) {
            // 转换成多部分request
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            // 取得request中的所有文件名
            Iterator<String> iter = multiRequest.getFileNames();
            List<Map<String, Object>> preFileList = new ArrayList<Map<String, Object>>();
            // 记录上传过程起始时的时间，用来计算上传时间
            int pre = (int) System.currentTimeMillis();
            String newFileName = null;
            List<String> fileNameList = new ArrayList<String>();
            while (iter.hasNext()) {
                // 取得上传文件
                List<MultipartFile> files = multiRequest.getFiles(iter.next());
                if (!CollectionUtils.isEmpty(files)) {
                    for (MultipartFile file : files) {
                        // 验证图片尺寸
                        BufferedImage src = ImageIO.read(file.getInputStream());
                        // 取得当前上传文件的文件名称
                        String myFileName = file.getOriginalFilename();
                        // 如果名称不为“”,说明该文件存在，否则说明该文件不存在
                        if (!StringUtils.isEmpty(myFileName)) {
                            // 获取图片的扩展名
                            String extensionName = myFileName.substring(myFileName.lastIndexOf(".") + 1);
                            //是否指定上传文件名
                            if (!StringUtils.isEmpty(targetFileName)) {
                                newFileName = targetFileName + "_" + RandomUtil.getAuthCode(8) + "." + extensionName;
                                LOGGER.info("---------------------------"+newFileName);
                            }else{
                                newFileName = UUID.randomUUID().toString() + "." + extensionName;
                                LOGGER.info("---------------------------"+newFileName);
                            }
                            Map<String, Object> preFileMap = new HashMap<String, Object>();
                            preFileMap.put("fileName", newFileName);// 上传到FTP服务器文件名字
                            preFileMap.put("fileSize", (file.getSize() / 1024));//文件大小(单位：KB)
                            fileSizes = file.getSize() / 1024;
                            LOGGER.info("+++++++++++++++++++++++++++"+file.getSize());
                            fileNameList.add(newFileName);
                            preFileMap.put("fileInputStream", file.getInputStream());// 上传输入文件流
                            preFileMap.put("bufferedImage", src);
                            preFileList.add(preFileMap);
                        }
                    }
                }
            }
            boolean isUploadSuccess = false;
            isUploadSuccess = uploadFile(big, small, object.getCdnHostname(), Integer.parseInt(object.getCdnPort()), object.getCdnUsername(), object.getCdnPassword(), object.getCdnUploadPath() + uploadPath, preFileList, tempPath);
            // 记录上传该文件后的时间
            int finaltime = (int) System.currentTimeMillis();
            LOGGER.info("上传文件耗时：" + (finaltime - pre));
            if (isUploadSuccess) {
                LOGGER.debug("上传成功");
                retMap.put("status", SUCCESS_CODE);
                retMap.put("fileName", fileNameList);
                retMap.put("fileSize", fileSizes);
            } else {
                
                retMap.put("status", FAILURE_CODE);
            }
        }
        return retMap;
    }

}
