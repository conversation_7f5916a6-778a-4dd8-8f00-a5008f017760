package com.xyy.ec.pop.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.ProtectSkuConvertHelper;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.ProtectSkuRemoteAdapter;
import com.xyy.ec.pop.server.api.product.dto.ProtectSkuExcelDto;
import com.xyy.ec.pop.service.ProtectSkuService;
import com.xyy.ec.pop.vo.ProtectSkuExcelVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 保护品种
 * <AUTHOR>
 */
@Service
public class ProtectSkuServiceImpl implements ProtectSkuService {
    @Autowired
    private ProtectSkuRemoteAdapter protectSkuRemoteAdapter;
    @Override
    public void update(String path, String fileName, List<Long> ids, SysUser user) throws ServiceException {
        ProtectSkuExcelDto excelDto = new ProtectSkuExcelDto();
        excelDto.setFileName(fileName);
        excelDto.setFilePath(path);
        protectSkuRemoteAdapter.update(excelDto,ids,user.getUsername(),user.getId());
    }

    public PageInfo<ProtectSkuExcelVo> page(Integer pageNum, Integer pageSize) throws ServiceException {
        PageInfo<ProtectSkuExcelDto> page =  protectSkuRemoteAdapter.page(pageNum,pageSize);
        return convertToVo(page);
    }

    private PageInfo<ProtectSkuExcelVo> convertToVo(PageInfo<ProtectSkuExcelDto> page) {
        PageInfo<ProtectSkuExcelVo>  pageInfo = new PageInfo();
        BeanUtils.copyProperties(page,pageInfo);
        List<ProtectSkuExcelVo> list = page.getList().stream().map(item->{
            ProtectSkuExcelVo vo = new ProtectSkuExcelVo();
            vo.setId(item.getId());
            vo.setFileName(item.getFileName());
            vo.setFilePath(item.getFilePath());
            vo.setCreateTime(item.getCreateTime());
            vo.setCreateName(item.getCreateName());

            return vo;
        }).collect(Collectors.toList());
        pageInfo.setList(list);
        return pageInfo;
    }

}
