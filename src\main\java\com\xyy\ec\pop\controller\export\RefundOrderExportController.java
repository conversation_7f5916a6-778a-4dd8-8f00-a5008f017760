package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.export.param.RefundOrderExportAdminParam;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/07/30
 */
@Slf4j
@RequestMapping("/refundOrderExport/async")
@RestController
public class RefundOrderExportController extends BaseController {
    @Autowired
    private DownloadRemote downloadRemote;

    /**
     * 导出退款单数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportRefundOrder")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportRefundOrder(RefundOrderExportAdminParam queryDto) {
        log.info("OrderExportController.exportRefundOrder#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(provIds);
            //第三方公司
            queryDto.setIsThirdCompany(ProductEnumDTO.ThirdCompany.IS_THIRD.getId());
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.REFUND_ORDER)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("OrderExportController.exportRefundOrder#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        }catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("OrderExportController.exportRefundOrder#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

}
