package com.xyy.ec.pop.service.settle.impl;

import com.xyy.ec.pop.dao.PopCommissionSettleMapper;
import com.xyy.ec.pop.service.settle.PopCommissionSettleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class PopCommissionSettleServiceImpl implements PopCommissionSettleService {
    @Autowired
    private PopCommissionSettleMapper popCommissionSettleMapper;

    @Override
    public List<String> queryBillNoListByForHireMoneyZero(List<String> orgIds) {
        log.info("PopCommissionSettleServiceImpl.queryBillNoListByForHireMoneyZero#orgIds:{}", orgIds);
        if (CollectionUtils.isEmpty(orgIds)) {
            return Lists.newArrayList();
        }
        List<String> billNoList = popCommissionSettleMapper.queryBillNoListByForHireMoneyZero(orgIds);
        log.info("PopCommissionSettleServiceImpl.queryBillNoListByForHireMoneyZero#orgIds:{},billNoList:{}", orgIds, billNoList);
        return billNoList;
    }
}
