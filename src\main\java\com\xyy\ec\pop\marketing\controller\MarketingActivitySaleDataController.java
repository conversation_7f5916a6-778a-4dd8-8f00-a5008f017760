package com.xyy.ec.pop.marketing.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.marketing.elephant.params.MarketingActivitySaleDataExportPagingQueryParam;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.marketing.service.MarketingActivitySaleDataService;
import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataStatisticsInfoVO;
import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataStatusCountInfoVO;
import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataVO;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.model.XyyJsonResult;
import com.xyy.ms.promotion.business.params.*;
import com.xyy.ms.promotion.business.result.MarketingActivitySaleDataSearchInfoDTO;
import com.xyy.ms.promotion.business.result.MarketingActivitySaleDataSummaryInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.keyvalue.DefaultKeyValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 活动销售数据
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/marketing/activity/saleData")
public class MarketingActivitySaleDataController extends BaseController {

    @Autowired
    private MarketingActivitySaleDataService marketingActivitySaleDataService;

    /**
     * 查询省份列表
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/listProvinces")
    public XyyJsonResult listProvinces() {
        SysUser user = super.getUser();
        try {
            List<DefaultKeyValue<Integer, String>> provinces = marketingActivitySaleDataService.listProvinces();
            return XyyJsonResult.createSuccess().addResult("list", provinces);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("查询省份列表失败，当前用户名：{}，msg：{}", user.getUsername(), e.getMsg(), e);
            }
            return XyyJsonResult.create(XyyJsonResultCodeEnum.FAIL, e.getMsg());
        } catch (Exception e) {
            log.error("查询省份列表失败，当前用户名：{}", user.getUsername(), e);
            return XyyJsonResult.createFailure();
        }
    }

    /**
     * 查询订单状态列表
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/listOrderStatus")
    public XyyJsonResult listOrderStatus() {
        SysUser user = super.getUser();
        try {
            List<DefaultKeyValue<Integer, String>> orderStatusList = marketingActivitySaleDataService.listOrderStatus();
            return XyyJsonResult.createSuccess().addResult("list", orderStatusList);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("查询订单状态列表失败，当前用户名：{}，queryParam：{}，msg：{}", user.getUsername(), e.getMsg(), e);
            }
            return XyyJsonResult.create(XyyJsonResultCodeEnum.FAIL, e.getMsg());
        } catch (Exception e) {
            log.error("查询订单状态列表失败，当前用户名：{}", user.getUsername(), e);
            return XyyJsonResult.createFailure();
        }
    }

    /**
     * 获取活动销售数据概要信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/getSummaryInfo")
    public XyyJsonResult getSummaryInfo(MarketingActivitySaleDataSummaryInfoQueryParam queryParam) {
        SysUser user = super.getUser();
        try {
            MarketingActivitySaleDataSummaryInfoDTO summaryInfo = marketingActivitySaleDataService.getSummaryInfo(queryParam);
            return XyyJsonResult.createSuccess().addResult("summaryInfo", summaryInfo);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("获取活动销售数据概要信息失败，当前用户名：{}，queryParam：{}，msg：{}", user.getUsername(), e.getMsg(), e);
            }
            return XyyJsonResult.create(XyyJsonResultCodeEnum.FAIL, e.getMsg());
        } catch (Exception e) {
            log.error("获取活动销售数据概要信息失败，当前用户名：{}", user.getUsername(), e);
            return XyyJsonResult.createFailure();
        }
    }

    /**
     * 获取活动销售数据查询信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/getSearchInfo")
    public XyyJsonResult getSearchInfo(MarketingActivitySaleDataSearchInfoQueryParam queryParam) {
        SysUser user = super.getUser();
        try {
            MarketingActivitySaleDataSearchInfoDTO searchInfo = marketingActivitySaleDataService.getSearchInfo(queryParam);
            return XyyJsonResult.createSuccess().addResult("searchInfo", searchInfo);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("获取活动销售数据查询信息失败，当前用户名：{}，queryParam：{}，msg：{}", user.getUsername(), e.getMsg(), e);
            }
            return XyyJsonResult.create(XyyJsonResultCodeEnum.FAIL, e.getMsg());
        } catch (Exception e) {
            log.error("获取活动销售数据查询信息失败，当前用户名：{}", user.getUsername(), e);
            return XyyJsonResult.createFailure();
        }
    }

    /**
     * 根据条件分页查询销售数据
     *
     * @param queryParam
     * @return
     */
    @ResponseBody
    @RequestMapping("/paging")
    public XyyJsonResult paging(@RequestBody MarketingActivitySaleDataListQueryParam queryParam) {
        SysUser user = super.getUser();
        try {
            PageInfo<MarketingActivitySaleDataVO> pageInfo = marketingActivitySaleDataService.paging(queryParam);
            return XyyJsonResult.createSuccess().addResult("pageInfo", pageInfo);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("根据条件分页查询销售数据失败，当前用户名：{}，queryParam：{}，msg：{}",
                        user.getUsername(), JSONObject.toJSONString(queryParam), e.getMsg(), e);
            }
            return XyyJsonResult.create(XyyJsonResultCodeEnum.FAIL, e.getMsg());
        } catch (Exception e) {
            log.error("根据条件分页查询销售数据失败，当前用户名：{}，queryParam：{}",
                    user.getUsername(), JSONObject.toJSONString(queryParam), e);
            return XyyJsonResult.createFailure();
        }
    }

    /**
     * 根据条件查询销售统计数据
     *
     * @param queryParam
     * @return
     */
    @ResponseBody
    @RequestMapping("/getStatisticsInfo")
    public XyyJsonResult getStatisticsInfo(@RequestBody MarketingActivitySaleDataStatisticsQueryParam queryParam) {
        SysUser user = super.getUser();
        try {
            MarketingActivitySaleDataStatisticsInfoVO statisticsInfo = marketingActivitySaleDataService.getStatisticsInfo(queryParam);
            return XyyJsonResult.createSuccess().addResult("statisticsInfo", statisticsInfo);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("根据条件查询销售统计数据失败，当前用户名：{}，queryParam：{}，msg：{}",
                        user.getUsername(), JSONObject.toJSONString(queryParam), e.getMsg(), e);
            }
            return XyyJsonResult.create(XyyJsonResultCodeEnum.FAIL, e.getMsg());
        } catch (Exception e) {
            log.error("根据条件查询销售统计数据失败，当前用户名：{}，queryParam：{}",
                    user.getUsername(), JSONObject.toJSONString(queryParam), e);
            return XyyJsonResult.createFailure();
        }
    }

    /**
     * 根据条件查询状态及数量
     *
     * @param queryParam
     * @return
     */
    @ResponseBody
    @RequestMapping("/getStatusCountInfo")
    public XyyJsonResult getStatusCountInfo(@RequestBody MarketingActivitySaleDataOrderStatusCountQueryParam queryParam) {
        SysUser user = super.getUser();
        try {
            MarketingActivitySaleDataStatusCountInfoVO statusCountInfo = marketingActivitySaleDataService.getStatusCountInfo(queryParam);
            return XyyJsonResult.createSuccess().addResult("statusCountInfo", statusCountInfo);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("根据条件查询状态及数量失败，当前用户名：{}，queryParam：{}，msg：{}",
                        user.getUsername(), JSONObject.toJSONString(queryParam), e.getMsg(), e);
            }
            return XyyJsonResult.create(XyyJsonResultCodeEnum.FAIL, e.getMsg());
        } catch (Exception e) {
            log.error("根据条件查询状态及数量失败，当前用户名：{}，queryParam：{}",
                    user.getUsername(), JSONObject.toJSONString(queryParam), e);
            return XyyJsonResult.createFailure();
        }
    }

    @RequestMapping("/export")
    @ResponseBody
    public XyyJsonResult export(@RequestBody MarketingActivitySaleDataExportPagingQueryParam queryParam) {
        SysUser user = super.getUser();
        try {
            if (log.isDebugEnabled()) {
                log.debug("export 查询，参数：{}", JSONObject.toJSONString(queryParam));
            }
            marketingActivitySaleDataService.asyncExport(user, queryParam);
            return XyyJsonResult.createSuccess().msg("文件生成中，请到 文件下载中心 页面进行查看和下载");
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("导出失败，当前用户名：{}，exportParam：{}，msg：{}",
                        user.getUsername(), JSONObject.toJSONString(queryParam), e.getMsg(), e);
            }
            return XyyJsonResult.create(XyyJsonResultCodeEnum.FAIL, e.getMsg());
        } catch (Exception e) {
            log.error("导出失败，当前用户名：{}，exportParam：{}",
                    user.getUsername(), JSONObject.toJSONString(queryParam), e);
            return XyyJsonResult.createFailure();
        }
    }

}
