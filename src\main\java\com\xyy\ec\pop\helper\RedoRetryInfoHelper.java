package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.server.api.retry.dto.RedoRetryInfoDto;
import com.xyy.ec.pop.vo.RedoRetryInfoVo;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/09/18/10:53
 * @Description:
 */
public class RedoRetryInfoHelper {
    public static RedoRetryInfoVo covertDtoToVo(RedoRetryInfoDto dto) {
        if (dto == null) {
            return null;
        }
        RedoRetryInfoVo redoRetryInfoVo = new RedoRetryInfoVo();
        redoRetryInfoVo.setId(dto.getId());
        redoRetryInfoVo.setRetryJson(dto.getRetryJson());
        redoRetryInfoVo.setRetryCount(dto.getRetryCount());
        redoRetryInfoVo.setStatus(dto.getStatus());
        redoRetryInfoVo.setIstatus(dto.getIstatus());
        redoRetryInfoVo.setCreateTime(dto.getCreateTime());
        redoRetryInfoVo.setCreateBy(dto.getCreateBy());
        redoRetryInfoVo.setLastModifyTime(dto.getLastModifyTime());
        redoRetryInfoVo.setLastModifyBy(dto.getLastModifyBy());
        return redoRetryInfoVo;
    }

    public static RedoRetryInfoDto covertVoToDto(RedoRetryInfoVo vo) {
        if (vo == null) {
            return null;
        }
        RedoRetryInfoDto redoRetryInfoDto = new RedoRetryInfoDto();
        redoRetryInfoDto.setId(vo.getId());
        redoRetryInfoDto.setRetryJson(vo.getRetryJson());
        redoRetryInfoDto.setRetryCount(vo.getRetryCount());
        redoRetryInfoDto.setStatus(vo.getStatus());
        redoRetryInfoDto.setIstatus(vo.getIstatus());
        redoRetryInfoDto.setCreateTime(vo.getCreateTime());
        redoRetryInfoDto.setCreateBy(vo.getCreateBy());
        redoRetryInfoDto.setLastModifyTime(vo.getLastModifyTime());
        redoRetryInfoDto.setLastModifyBy(vo.getLastModifyBy());
        return redoRetryInfoDto;
    }
}
