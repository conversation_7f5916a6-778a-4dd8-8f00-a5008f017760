package com.xyy.ec.pop.report.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.ProductBatchUpdateConfig;
import com.xyy.ec.pop.dto.ImpExcelDataDTO;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.redis.impl.RedisService;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.report.Vo.*;
import com.xyy.ec.pop.report.param.MerchantReportBatchAuditImportParam;
import com.xyy.ec.pop.report.service.MerchantReportService;
import com.xyy.ec.pop.service.ExcelReadService;
import com.xyy.ec.pop.vo.BatchUpdateResultVo;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Controller
@RequestMapping(value = "/report")
public class MerchantReportController extends BaseController {
    @Resource
    private RedisService redisService;
    @Autowired
    private DownloadRemote downloadRemote;
    @Resource
    private ExcelReadService excelReadService;
    @Resource
    private MerchantReportService merchantReportService;
    @Autowired
    private ProductBatchUpdateConfig productBatchUpdateConfig;

    @Value("${report.audit.download.count.limit:1000}")
    private Integer downloadCountLimit;

    public final static String commonTip = "当前上传文件与模版不一致，请重新选择文件上传";

    /**
     * 分页查询举报记录列表
     *
     * @param reportRecordQueryVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/pageReportRecords", method = {RequestMethod.GET})
    public ResponseVo<PageInfo<MerchantRecordsVo>> pageReportRecords(ReportRecordQueryVo reportRecordQueryVo) {
        return merchantReportService.pageReportRecords(reportRecordQueryVo);
    }

    @ResponseBody
    @RequestMapping(value = "/pageReportDetail", method = {RequestMethod.GET})
    public ResponseVo<PageInfo<MerchantDetailsVo>> pageReportDetails(ReportDetailQueryVo reportDetailQueryVo) {
        return merchantReportService.pageReportDetails(reportDetailQueryVo);
    }

    @ResponseBody
    @RequestMapping(value = "/queryReportLogs", method = {RequestMethod.GET})
    public ResponseVo<PageInfo<ReportLogVo>> queryReportLogs(ReportLogQueryVo reportLogQueryVo) {
        return merchantReportService.queryReportLogs(reportLogQueryVo.getMerchantId(), reportLogQueryVo.getPage(), reportLogQueryVo.getLimit());
    }

    /**
     * 审核
     *
     * @param auditVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/audit", method = {RequestMethod.POST})
    public ResponseVo<String> audit(AuditVo auditVo) {
        if (auditVo == null) {
            return ResponseVo.errRest("参数错误");
        }
        if (auditVo.getAuditStatus() == null) {
            return ResponseVo.errRest("审核状态不能为空");
        }
        if (auditVo.getMerchantId() == null) {
            return ResponseVo.errRest("店铺id不能为空");
        }
        return merchantReportService.audit(auditVo, super.getUserName());
    }

    @PostMapping(value = "/batchAudit")
    @ResponseBody
    public ResponseVo<BatchUpdateResultVo> batchAudit(@RequestParam("file") MultipartFile file) {
        BatchUpdateResultVo batchUpdateResult = new BatchUpdateResultVo();
        List<String> titleList = Lists.newArrayList("药店编码", "审核结果", "原因");
        try {
            //导入文件
            ImpExcelDataDTO<MerchantReportBatchAuditImportParam> impExcelDataDTO = excelReadService.readExcel(file, MerchantReportBatchAuditImportParam.class);

            //校验文件
            validateFile(file, productBatchUpdateConfig, (new ArrayList<>(impExcelDataDTO.getHeadMaps().values())), titleList);

            //校验参数
            validateParameters(impExcelDataDTO.getDataList());

            //审核
            return merchantReportService.batchAudit(impExcelDataDTO.getDataList(), super.getUserName(), file);
        } catch (PopAdminException e) {
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("批量审核举报失败，e=", e);
            return ResponseVo.errRest("批量审核举报失败");
        }
    }

    public void validateParameters(List<MerchantReportBatchAuditImportParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            throw new PopAdminException("没有需要导入的数据");
        }
        if (params.size() > downloadCountLimit) {
            throw new PopAdminException("当前上传文件超过" + downloadCountLimit + "条，请分批上传");
        }
    }

    public void validateFile(MultipartFile file, ProductBatchUpdateConfig productBatchUpdateConfig,
                             List<String> actTitles, List<String> titles) {
        checkFileSize(file, productBatchUpdateConfig);
        checkFileName(file);
        checkTitles(actTitles, titles);
    }

    private void checkFileSize(MultipartFile file, ProductBatchUpdateConfig productBatchUpdateConfig) {
        if (file.isEmpty() || file.getSize() > productBatchUpdateConfig.getMaxFileSize() * 3 * (1024 * 1024)) {
            throw new PopAdminException("文件大小不能超过" + productBatchUpdateConfig.getMaxFileSize() * 3 + "M");
        }
    }

    private void checkFileName(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (org.apache.commons.lang.StringUtils.isEmpty(fileName) ||
                (!fileName.toLowerCase().endsWith(".xls") && !fileName.toLowerCase().endsWith(".xlsx"))) {
            throw new PopAdminException(commonTip);
        }
    }

    private void checkTitles(List<String> actualTitles, List<String> expectedTitles) {
        if (!CollectionUtils.isEmpty(expectedTitles)) {
            if (CollectionUtils.isEmpty(actualTitles) ||
                    !new HashSet<>(actualTitles).containsAll(expectedTitles) ||
                    !new HashSet<>(expectedTitles).containsAll(actualTitles)) {
                List<String> missingColumns = new ArrayList<>(expectedTitles);
                List<String> extraColumns = new ArrayList<>(actualTitles);
                missingColumns.removeAll(actualTitles);
                extraColumns.removeAll(expectedTitles);
                log.info("当前导入模板缺少列：{}, 多余列：{}", JSON.toJSONString(missingColumns), JSON.toJSONString(extraColumns));
                throw new PopAdminException(commonTip);
            }
        }
    }

    @RequestMapping(value = "/exportReportRecords", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseVo<Boolean> exportReportRecords(ReportRecordQueryVo reportRecordQueryVo) {
        try {
            merchantReportService.exportReportRecord(reportRecordQueryVo, super.getUser());
        } catch (Exception e) {
            log.error("导出举报记录失败，e=", e);
            return ResponseVo.errRest("导出举报记录失败");
        }
        return ResponseVo.successResult(true);
    }
}
