package com.xyy.ec.pop.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.server.api.shopcourse.api.PopShopCourseCategoryApi;
import com.xyy.ec.pop.server.api.shopcourse.dto.PopShopCourseCategoryDto;
import com.xyy.ec.pop.server.api.shopcourse.dto.PopShopCourseCategoryParam;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Objects;

@Controller
@RequestMapping("/shopCourseCategory")
@Slf4j
public class PopShopCourseCategoryController extends BaseController {


    @Reference
    private PopShopCourseCategoryApi popShopCourseCategoryApi;



    @PostMapping("/query")
    @ResponseBody
    public ResponseVo<Object> query(@RequestBody PopShopCourseCategoryParam queryParamDto){
        try {
            if (Objects.isNull(queryParamDto) || Objects.isNull(queryParamDto.getPageSize()) || Objects.isNull(queryParamDto.getPageNum()) ){
                return ResponseVo.errRest("分页不能为空");
            }
            PageInfo<PopShopCourseCategoryDto> query = popShopCourseCategoryApi.query(queryParamDto);
            return ResponseVo.successResult(query);
        } catch (Exception e) {
            log.error("PopShopCourseCategoryController selectPageList error",e);
            return ResponseVo.errRest("系统错误");
        }
    }



    @PostMapping("/save")
    @ResponseBody
    public ResponseVo<Object> save(@RequestBody PopShopCourseCategoryParam param){
        try {
            log.info("PopShopCourseCategoryController save request ,queryParamDto :{}", JSONObject.toJSONString(param));
            SysUser user = getUser();
            if (user == null){
                return ResponseVo.errRest("请登录");
            }
            param.setUser(user.getUsername());
            String save = popShopCourseCategoryApi.save(param);
            if (StringUtils.isNotBlank(save)){
                return ResponseVo.errRest(save);
            }
            return ResponseVo.successResult("操作成功");
        } catch (Exception e) {
            log.error("PopShopCourseCategoryController save error",e);
            return ResponseVo.errRest("系统错误");
        }
    }

    @PostMapping("/delete")
    @ResponseBody
    public ResponseVo<Object> delete(@RequestBody PopShopCourseCategoryParam param){
        try {
            log.info("PopShopCourseCategoryController delete request ,queryParamDto :{}", JSONObject.toJSONString(param));
            SysUser user = getUser();
            if (user == null){
                return ResponseVo.errRest("请登录");
            }
            String save = popShopCourseCategoryApi.delete(param.getId(),user.getUsername());
            if (StringUtils.isNotBlank(save)){
                return ResponseVo.errRest(save);
            }
            return ResponseVo.successResult("操作成功");
        } catch (Exception e) {
            log.error("PopShopCourseCategoryController delete error",e);
            return ResponseVo.errRest("系统错误");
        }
    }
}
