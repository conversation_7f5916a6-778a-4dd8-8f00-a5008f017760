package com.xyy.ec.pop.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @project_name: xyy-ec-pop-admin
 * @Auther: Jincheng.Li
 * @Date: 2021/04/14/15:28
 * @Description:
 */
@Data
public class ErpSkuBatchUpdateVo {
    @Excel(name="*机构编码",width=20)
    private String orgId;
    @Excel(name = "*商品ERP编码",width = 20)
    private String erpCode;
    @Excel(name = "商品条码",width = 20)
    private String code;
    @Excel(name="通用名称",width=20)
    private String commonName;
    @Excel(name="商品名称",width=20)
    private String productName;
    @Excel(name = "批准文号",width = 30)
    private String approvalNumber;
    @Excel(name = "生产厂家",width = 30)
    private String manufacturer;
    @Excel(name = "规格",width = 20)
    private String spec;

    @Excel(name="失败原因",width=20)
    protected String errorMsg;
    /**
     * 表明否已经导入错误了
     */
    private boolean failed;
}
