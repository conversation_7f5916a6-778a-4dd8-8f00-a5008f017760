package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
public class OrderShippingReminderVo implements Serializable {

    /**
     * 催发货id
     */
    private Long reminderId;

    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 商户
     * 商户编码
     * 商户名称
     * 店铺名称
     */
    private String merchantSearch;

    /**
     * 客户
     * 客户名称
     * 客户erp编码
     */
    private String customerSearch;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 商品编号
     */
    private String orgId;

    /**
     * 状态 10 催发货待处理  20 催发货已处理  21 催发货逾期处理 22 客户发起售后，自动撤销 23 客户撤销 30 催发货申诉中 31 催发货申诉成立 32 催发货申诉失败
     */
    private Integer status;

    /**
     * 批量状态
     */
    private List<Integer> statusList;

    /**
     * 支付类型(1:在线支付 2:货到付款 3:线下转账  4:银行授信支付）
     */
    private Integer payType;

    /**
     * 商户注册省份ID
     */
    private Long merchantProvId;
    /**
     * 催发货时间 开始时间
     */
    private Date reminderTimeStart;
    /**
     * 催发货时间 结束时间
     */
    private Date reminderTimeEnd;
    /**
     * 当前页
     */
    private Integer pageNo;
    /**
     * 每页条
     */
    private Integer pageSize;
}
