package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.SupplierUserHelper;
import com.xyy.ec.pop.server.api.merchant.api.seller.SupplierUserApi;
import com.xyy.ec.pop.server.api.merchant.dto.PopSupplierUserDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCorporationDto;
import com.xyy.ec.pop.vo.SupplierUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/08/16/12:57
 * @Description:
 */
@Slf4j
@Component
public class SupplierUserRemote {
    @Reference
    private SupplierUserApi supplierUserApi;
    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;

    public List<SupplierUserVo> getSupplierUserList(String keyword) throws Exception {
        try {
            if (StringUtils.isBlank(keyword)) {
                return Lists.newArrayList();
            }
            log.info("SupplierUserRemote.getSupplierUserList request param:{}",keyword);
            ApiRPCResult<List<PopSupplierUserDto>> userListByKeyWord = supplierUserApi.getPopSupplierUserListByKeyWord(keyword);
            log.info("SupplierUserRemote.getSupplierUserList response param:{}",keyword);
            if (userListByKeyWord == null || userListByKeyWord.isFail()){
                throw new ServiceException("查询失败");
            }
            List<PopSupplierUserDto> data = userListByKeyWord.getData();
            if (CollectionUtils.isEmpty(data)){
                return Lists.newArrayList();
            }
            List<String> collect = data.stream().map(PopSupplierUserDto::getOrganizationId).collect(Collectors.toList());
            Map<String, PopCorporationDto> stringPopCorporationDtoMap = popCorporationRemoteAdapter.queryPopCorporationDtoMap(collect);
            return SupplierUserHelper.dtos2vos(data,stringPopCorporationDtoMap);
        }catch (Exception e){
            log.error("SupplierUserRemote.getSupplierUserList error param:{}",keyword,e);
            throw new Exception(e.getMessage());
        }
    }
}
