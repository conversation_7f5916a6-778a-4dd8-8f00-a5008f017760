package com.xyy.ec.pop.marketing.dto;

import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class GroupBuyingBatchOfflineResultDTO implements Serializable {

    /**
     * Excel行数限制标识
     */
    private Boolean excelRowNumLimitFlag;
    /**
     * Excel行数限制失败信息
     */
    private String excelRowNumLimitFailureMsg;

    /**
     * 成功数量
     */
    private Integer successNum;

    /**
     * 失败数量
     */
    private Integer failureNum;

    /**
     * 失败Excel文件下载地址
     */
    private String failureExcelFileDownloadUrl;

}
