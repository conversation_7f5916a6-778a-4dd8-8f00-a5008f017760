/**
 * Copyright (C), 2015-2019,  武汉小药药医药科技有限公司
 * FileName: popCodeMapController
 * Author:   danshiyu
 * Date:     2019/6/14 13:45
 * Description: pop字典管理接口
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.constants.Constants;
import com.xyy.ec.pop.server.api.seller.api.PopCodeAdminApi;
import com.xyy.ec.pop.server.api.seller.dto.PopCodeMapDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCodeitemDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.Collections;
import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈pop字典管理接口〉
 *
 * <AUTHOR>
 * @create 2019/6/14
 * @since 1.0.0
 */
@Controller
@RequestMapping("popCodeMapManagement")
@Slf4j
@Api(tags = "pop字典管理类")
public class PopCodeMapController extends BaseController {

    @Reference
    private PopCodeAdminApi popCodeAdminApi;

    /**
     * pop字典管理主界面
     * @return
     */
    @ApiOperation("pop字典管理主界面")
    @RequestMapping(value = "/index",method = RequestMethod.GET)
    public String index(){
        return "codeManagement/index";
    }

    /**
     * 跳轉到'维护数据字典'界面
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/view/update",method = RequestMethod.GET)
    @ApiOperation("跳轉到'维护数据字典'界面")
    public ModelAndView codeMapViewUpdate(@ApiParam(name = "id",value = "字典id") Long id){
        ModelAndView modelAndView = new ModelAndView("codeManagement/update");
        ApiRPCResult<PopCodeMapDto> apiRPCResult = popCodeAdminApi.selectPopCodeMapById(id);
        modelAndView.addObject(Constants.CODERMAP, apiRPCResult.getData());
        return modelAndView;
    }

    /**
     * 跳轉到'修改数据字典'界面
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/view/codeMapEdit",method = RequestMethod.GET)
    @ApiOperation("跳轉到'修改数据字典'界面")
    public ModelAndView codeMapViewEdit(@ApiParam(name = "id",value = "字典id")Long id){
        ModelAndView modelAndView = new ModelAndView("codeManagement/edit");
        if(id != null){
            ApiRPCResult<PopCodeMapDto> apiRPCResult = popCodeAdminApi.selectPopCodeMapById(id);
            modelAndView.addObject(Constants.CODERMAP, apiRPCResult.getData());
        }
        return modelAndView;
    }

    /**
     * 跳轉到'修改数据字典项'界面
     * @throws Exception
     */
    @RequestMapping(value = "/view/codeItemEdit",method = RequestMethod.GET)
    @ApiOperation("跳轉到'修改维护数据字典项'界面")
    public ModelAndView codeItemViewEdit(@ApiParam(name = "id",value = "字典id")Long id, @ApiParam(name = "codeMap",value = "字典集codeMap")String codeMap){
        ModelAndView modelAndView = new ModelAndView("codeManagement/edit-codeitem");
        ApiRPCResult<PopCodeitemDto> apiRPCResult = popCodeAdminApi.selectPopItemById(id);
        modelAndView.addObject("codeItem", apiRPCResult.getData());
        modelAndView.addObject(Constants.CODERMAP, codeMap);
        return modelAndView;
    }

    /**
     * 保存字典集
     * @param popCodeMap
     * @return
     */
    @RequestMapping(value = "/saveCodeMap",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("保存字典集")
    public Object saveCodeMap(@ApiParam(name = "popCodeMap",value = "字典实体")PopCodeMapDto popCodeMap){
        try {
            if(popCodeMap.getId() == null) {
                popCodeAdminApi.savePopCodeMap(popCodeMap);
            }else {
                popCodeAdminApi.updatePopCodeMapById(popCodeMap);
            }
            return this.addResult("字典集保存成功");
        }catch (Exception e){
            log.error("saveCodeMap",e);
            return this.addError(e.getMessage());
        }
    }

    /**
     * 删除字典集
     * @param id
     * @return
     */
    @RequestMapping(value = "/deleteCodeMap",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("删除字典集")
    public Object deleteCodeMap(@ApiParam(name = "id",value = "字典id")Long id){
        try {
            popCodeAdminApi.deleteById(id);
            return this.addResult("字典集删除成功");
        }catch (Exception e){
            log.error("deleteCodeMap",e);
            return this.addError(e.getMessage());
        }
    }

    /**
     * 列表查询字典集
     * @param page
     * @param popCodeMap
     * @return
     */
    @RequestMapping(value = "/listCodeMap",method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("列表查询字典集")
    public Object listCodeMap(@ApiParam(name = "page",value = "字典分页查询") Page<PopCodeMapDto> page, @ApiParam(name = "popCodeMap",value = "字典实体") PopCodeMapDto popCodeMap){
        try {
            ApiRPCResult<PageInfo<PopCodeMapDto>> apiRPCResult = popCodeAdminApi.findPageByParams(page.getOffset(), page.getLimit(), popCodeMap);
            return apiRPCResult.getData();
        }catch (Exception e){
            log.error("listCodeMap",e);
            return Collections.EMPTY_LIST;
        }
    }

    /**
     * 保存字典子项
     * @param popCodeItem
     * @return
     */
    @RequestMapping(value = "/saveCodeItem",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("保存字典子项")
    public Object saveCodeItem(@ApiParam(name = "popCodeItem",value = "字典子项实体")PopCodeitemDto popCodeItem){
        try {
            if(popCodeItem.getId() == null) {
                popCodeAdminApi.savePopCodeItem(popCodeItem);
            }else {
                popCodeAdminApi.updatePopCodeItemId(popCodeItem);
            }
            return this.addResult("字典子项保存成功");
        }catch (Exception e){
            log.error("saveCodeItem",e);
            return this.addError(e.getMessage());
        }
    }

    /**
     * 根据字典code查询全部字典子项
     * @param codeMap
     * @return
     */
    @RequestMapping(value = "/listCodeItem",method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("根据字典code查询全部字典子项")
    public Object listCodeItem(@ApiParam(name = "codeMap",value = "字典codeMap")String codeMap){
        try {
            ApiRPCResult<List<PopCodeitemDto>> apiRPCResult = popCodeAdminApi.selectPopCodeitemDtoByCodeMap(codeMap);
            return apiRPCResult.getData();
        }catch (Exception e){
            log.error("listCodeItem",e);
            return Collections.EMPTY_LIST;
        }
    }

    /**
     * 删除字典子项
     * @param id
     * @return
     */
    @RequestMapping(value = "/deleteCodeItem",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("删除字典子项")
    public Object deleteCodeItem(@ApiParam(name = "id",value = "字典子项id") Long id){
        try {
            popCodeAdminApi.deletePopCodeItemById(id);
            return this.addResult("字典子项删除成功");
        }catch (Exception e){
            log.error("deleteCodeItem",e);
            return this.addError(e.getMessage());
        }
    }
}