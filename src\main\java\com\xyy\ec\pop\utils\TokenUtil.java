/*
 * Copyright (C) 2010 The MobileSecurePay Project
 * All right reserved.
 * author: <EMAIL>
 */

package com.xyy.ec.pop.utils;


/**
 * 
 * <AUTHOR>
 *
 */
public final class TokenUtil {
	
	private static final String SPLIT = "|";
	
	private static final String TO_SPLIT = "\\|";
	
    /**
     * 
     * @param key
     * @return
     */
    public static String generateTokenByKey(String key){
    	String uuid = UUIDGenerator.generateUUID();
    	uuid = uuid.replaceAll(TO_SPLIT, "");
    	uuid = uuid + SPLIT + key;
    	return Base64Utils.encoding(uuid);
    }
   
    
    /**
     * 根据分隔符得到token
     * @param token
     * @return
     */
    public static String decodeTokenByKey(String token){
    	String values = Base64Utils.decodeing(token);
    	String[] value = values.split(TO_SPLIT);
    	return (value == null || value.length == 0) ? null : value[1];  
    }
    
    
    public static void main(String[] args) throws Exception {
    	String uuid = UUIDGenerator.generateUUID();
    	uuid = uuid.replaceAll(TO_SPLIT, "");
    	System.out.print(uuid);
		//System.out.print(decodeTokenByKey(token));
	}
}
