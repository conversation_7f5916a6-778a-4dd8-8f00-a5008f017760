package com.xyy.ec.pop.service.base;

import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.xyy.ec.pop.base.Mapper;
import com.xyy.ec.pop.exception.ServiceException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.exceptions.TooManyResultsException;

import java.lang.reflect.Field;
import java.util.Map;

/**
 * 通用Service基础操作
 * @param <T>：Po；
 * @version V2.0.1
 * <AUTHOR>
 * @date 2018年9月19日 上午11:42:25
 * @Description:
 */
public interface Service<T> {
	/**
	 * <p>重写：持久层子接口实例</p>
	 * @return {@link Mapper}<{@link T}, {@link Object}>：持久层继承{@link Mapper}接口的子接口实例。
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午3:28:09
	 * @Description: 此get方法，用于获取Spring注入的持久层实例，以调用对应的*Mapper.xml。
	 */
	Mapper<T, Object> getMapper();

	/**
	 * 统计结果
	 * @param params {@link Object}：查询参数，Bean或Map类型；
	 * @return {@link Map}<{@link String}, {@link Object}>：结果集。
	 * @throws ServiceException
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午3:51:38
	 * @Description:
	 */
	default Map<String, Object> selectCount(Object params) throws ServiceException, IllegalAccessException {
		Page<Map<String, Object>> sums = this.selectCounts(params);
		if (CollectionUtils.isEmpty(sums))
			return null;
		int sz = sums.size();
		if (sz == 1)
			return sums.get(0);
		throw new TooManyResultsException("Expected one (or null) result, but found: " + sz);
	}
	/**
	 * 统计结果集
	 * @param params {@link Object}：查询参数，Bean或Map类型；
	 * @return {@link Page}<{@link Map}<{@link String}, {@link Object}>>：结果集。
	 * @throws ServiceException
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午3:51:38
	 * @Description:
	 */
	default Page<Map<String, Object>> selectCounts(Object params) throws ServiceException, IllegalAccessException {
		Page<T> page = null;
		return this.selectCounts(params, page);
	}
	/**
	 * 统计结果集
	 * @param params {@link Object}：查询参数，Bean或Map类型；
	 * @param page {@link Page<{@link T}>}：分页；
	 * @return {@link Page}<{@link Map}<{@link String}, {@link Object}>>：结果集。
	 * @throws ServiceException
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午3:51:38
	 * @Description:
	 */
	default Page<Map<String, Object>> selectCounts(Object params, Page<T> page) throws ServiceException, IllegalAccessException {
		if (page != null)
			PageMethod.startPage(page.getPageNum(), page.getPageSize());
		this.order(params);
		return this.getMapper().selectCounts(params);
	}
	/**
	 * 查询Bean
	 * @param params {@link Object}：查询参数，Bean或Map类型；
	 * @return {@link T}：结果。
	 * @throws ServiceException
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午4:30:50
	 * @Description: 
	 */
	default T query(Object params) throws ServiceException, IllegalAccessException {
		Page<T> page = null;
		Page<T> beans = this.select(params, page);
		if (CollectionUtils.isEmpty(beans))
			return null;
		int sz = beans.size();
		if (sz == 1)
			return beans.get(0);
		throw new TooManyResultsException("Expected one (or null) result, but found: " + sz);
	}
	/**
	 * 查询Bean集合
	 * @param params {@link Object}：查询参数，Bean或Map类型；
	 * @param page {@link Page}<{@link T}>：分页；
	 * @return {@link Page}<{@link T}>：分页结果集。
	 * @throws ServiceException
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午5:01:42
	 * @Description:
	 */
	default Page<T> select(Object params, Page<T> page) throws ServiceException, IllegalAccessException {
		if (page != null)
			PageMethod.startPage(page.getPageNum(), page.getPageSize());
		this.order(params);
		return this.getMapper().select(params);
	}
	/**
	 * 修改Bean
	 * @param bean {@link T}：Bean；
	 * @return {@link Integer}：被更新条数。
	 * @throws ServiceException
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午4:33:41
	 * @Description: 
	 */
	default int update(T bean) throws ServiceException {
		return this.getMapper().update(bean);
	}
	/**
	 * （逻辑）删除
	 * @param bean {@link T}：Bean；
	 * @return {@link Integer}：被“删除”条数。
	 * @throws ServiceException
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午4:37:20
	 * @Description: 
	 */
	default int delete(T bean) throws ServiceException {
		return this.getMapper().del(bean);
	}
	/**
	 * 新增Bean
	 * @param bean {@link T}：Bean；
	 * @return {@link Integer}：新增条数。
	 * @throws ServiceException
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午4:51:39
	 * @Description: 
	 */
	default int save(T bean) throws ServiceException {
		return this.getMapper().save(bean);
	}

	/**
	 * 统计结果集
	 * @param params {@link Object}：查询参数，Bean或Map类型；
	 * @param page {@link com.xyy.ec.pop.base.Page<{@link T}>}：分页；
	 * @return {@link com.xyy.ec.pop.base.Page}<{@link Map}<{@link String}, {@link Object}>>：结果集。
	 * @throws ServiceException
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午3:51:38
	 * @Description:
	 */
	default com.xyy.ec.pop.base.Page<Map<String, Object>> selectCounts(Object params, com.xyy.ec.pop.base.Page<Map<String, Object>> page) throws ServiceException, IllegalAccessException {
		if (page != null)
			PageMethod.startPage(page.getOffset(), page.getLimit());
		this.order(params);
		if(null == page){
			page = new com.xyy.ec.pop.base.Page<>();
		}
		Page<Map<String, Object>> rs = this.selectCounts(params);
		page.setPageCount(rs.getPages());
		page.setOffset(rs.getPageNum());
		page.setLimit(rs.getPageSize());
		page.setTotal(rs.getTotal());
		page.setRows(rs);
		return page;
	}
	/**
	 * 查询Bean集合
	 * @param params {@link Object}：查询参数，Bean或Map类型；
	 * @param page {@link com.xyy.ec.pop.base.Page}<{@link T}>：分页；
	 * @return {@link com.xyy.ec.pop.base.Page}<{@link T}>：分页结果集。
	 * @throws ServiceException
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午5:01:42
	 * @Description:
	 */
	default com.xyy.ec.pop.base.Page<T> select(Object params, com.xyy.ec.pop.base.Page<T> page) throws ServiceException, IllegalAccessException {
		if (page != null)
			PageMethod.startPage(page.getOffset(), page.getLimit());
		this.order(params);
		if(null == page){
			page = new com.xyy.ec.pop.base.Page<>();
		}
		Page<T> rs = this.getMapper().select(params);
		page.setPageCount(rs.getPages());
		page.setOffset(rs.getPageNum());
		page.setLimit(rs.getPageSize());
		page.setTotal(rs.getTotal());
		page.setRows(rs);
		return page;
	}

	/**
	 * 设置排序
	 * @param params {@link Object}：Map类型，须携带key = "ORDER"；
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年9月19日 下午8:14:47
	 * @Description:
	 */
	default void order(Object params) throws IllegalAccessException {
		if (params instanceof Map) {
			@SuppressWarnings("unchecked")
			Object order = ((Map<String, Object>) params).get("ORDER");
			if (order != null)
				PageMethod.orderBy(order.toString());
		} else {
			Class<? extends Object> c = params.getClass();
			Field[] fs = c.getDeclaredFields();
			for (int i = 0; i < fs.length; i++) {
				Field f = fs[i];
				if ("ORDER".equals(f.getName())) {
					try {
						f.setAccessible(true);
						Object order = f.get(params);
						if (order != null)
							PageMethod.orderBy(order.toString());
					} catch (IllegalArgumentException | IllegalAccessException e) {
						throw e;
					}
					break;
				}
			}
		}
	}
}
