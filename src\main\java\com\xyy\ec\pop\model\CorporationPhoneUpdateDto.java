package com.xyy.ec.pop.model;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
public class CorporationPhoneUpdateDto implements Serializable {

    /**
     * 机构ID
     */
    @NotBlank(message = "orgId不能为空")
    private String orgId;

    /**
     * 旧手机号
     */
    @NotBlank(message = "原手机号不能为空")
    @Length(max = 11, message = "商业账号仅支持最多11位数字")
    @Pattern(regexp = "^[0-9]*$", message = "商业账号仅支持最多11位数字")
    private String oldPhone;

    /**
     * 新手机号
     */
    @NotBlank(message = "新手机号不能为空")
    @Length(max = 11, message = "商业账号仅支持最多11位数字")
    @Pattern(regexp = "^[0-9]*$", message = "商业账号仅支持最多11位数字")
    private String newPhone;

}
