package com.xyy.ec.pop.remote;

import com.xyy.ec.label.server.business.api.LabelQueryApi;
import com.xyy.ec.pop.marketing.dto.InsightLabelDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * {@link LabelQueryApi}
 *
 * <AUTHOR>
 */
public interface LabelQueryRPCService {

    /**
     * 获取全部的标签列表
     *
     * @return
     */
    List<InsightLabelDTO> listAllLabels();

    /**
     * 批量根据标签ID获取标签信息
     *
     * @param labelIds
     * @return
     */
    List<InsightLabelDTO> listLabelByIds(List<Long> labelIds);

    /**
     * 校验标签是否有效存在
     *
     * @param labelIds
     * @return
     */
    Set<Long> checkLabelIds(Set<Long> labelIds);

    /**
     * 查询用户在标签中的标签ID列表
     *
     * @param merchantId
     * @param labelIds
     * @return
     */
    Set<Long> checkUserIsInLabelIds(Long merchantId, Set<Long> labelIds);

    /**
     * 查询用户在标签中的标签ID列表
     *
     * @param merchantIds
     * @param labelIds
     * @return
     */
    Map<Long, Set<Long>> checkUsersIsInLabelIds(Set<Long> merchantIds, Set<Long> labelIds);

}
