package com.xyy.ec.pop.marketing.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.marketing.insight.enums.MarketCustomerChannelTypeEnum;
import com.xyy.ec.marketing.insight.params.MarketCustomerGroupAdminQueryParam;
import com.xyy.ec.pop.marketing.param.CustomerGroupQueryParam;

import java.util.Objects;

/**
 * {@link MarketCustomerGroupAdminQueryParam} 帮助类
 *
 * <AUTHOR>
 */
public class MarketCustomerGroupAdminQueryParamHelper {

    public static MarketCustomerGroupAdminQueryParam create(CustomerGroupQueryParam customerGroupQueryParam) {
        if (Objects.isNull(customerGroupQueryParam)) {
            return null;
        }
        MarketCustomerGroupAdminQueryParam marketCustomerGroupAdminQueryParam = MarketCustomerGroupAdminQueryParam.builder()
                .id(customerGroupQueryParam.getId())
                // 在这里，channelType不起作用，但需要填写。
                .channelType(MarketCustomerChannelTypeEnum.SHOP.getType())
                .channelTypes(Lists.newArrayList(MarketCustomerChannelTypeEnum.EC.getType(),
                        MarketCustomerChannelTypeEnum.EC_MULTI_REGION.getType(),
                        MarketCustomerChannelTypeEnum.SHOP.getType()))
                .name(customerGroupQueryParam.getGroupName())
                .createStartTime(customerGroupQueryParam.getCreateStartTime())
                .createEndTime(customerGroupQueryParam.getCreateEndTime())
                .isMultiBundle(customerGroupQueryParam.getIsMultiBundle())
                .build();
        return marketCustomerGroupAdminQueryParam;
    }

}
