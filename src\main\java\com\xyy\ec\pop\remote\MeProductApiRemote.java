package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.me.product.common.beans.ResponseInfo;
import com.xyy.me.product.general.api.dto.pictrue.PictrueCondition;
import com.xyy.me.product.general.api.dto.pictrue.PictureProResult;
import com.xyy.me.product.general.api.dto.present.GeneralProductPresentDto;
import com.xyy.me.product.general.api.dto.product.GeneralMatchProductDto;
import com.xyy.me.product.general.api.dto.product.GeneralProductDto;
import com.xyy.me.product.general.api.dto.standardlib.request.GeneralRequestForBusinessScope;
import com.xyy.me.product.general.api.dto.standardlib.response.GeneralResponse;
import com.xyy.me.product.general.api.dto.standardlib.response.StandardLibResponseDataForBusinessScope;
import com.xyy.me.product.general.api.exceptions.GeneralProductException;
import com.xyy.me.product.general.api.facade.picture.ProductPictureFacade;
import com.xyy.me.product.general.api.facade.product.present.ProductPresentGenApi;
import com.xyy.me.product.general.api.facade.product.sku.SkuGenReadApi;
import com.xyy.me.product.general.api.facade.standardlib.StandardLibGeneralFacade;
import com.xyy.me.product.general.api.vo.present.GeneralProductPresentVo;
import com.xyy.me.product.general.api.vo.product.GeneralMatchProduct;
import com.xyy.me.product.general.api.vo.product.GeneralMatchProductVo;
import com.xyy.me.product.general.api.vo.product.GeneralPageProductVo;
import com.xyy.me.product.general.api.vo.product.GeneralProductVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: MeProductApiRemote
 * @Package com.xyy.ec.pop.service.remote
 * @Description: 中台商品标准库
 * @date 2020/7/16 15:48
 */
@Slf4j
@Component
public class MeProductApiRemote {
    @Reference(version = "1.0.0",registry = "me")
    private SkuGenReadApi skuGenReadApi;
    @Reference(version = "1.0.0",registry = "me")
    private ProductPresentGenApi productPresentGenApi;
    @Reference(version = "1.0.0",registry = "me")
    private StandardLibGeneralFacade standardLibGeneralFacade;
    @Reference(version = "1.0.0", registry = "me")
    private ProductPictureFacade productPictureFacade;
    /**
     * 调用商品中台搜索接口，来源字段值
     */
    private final static Integer  STANDARD_LIB_GEN_READ_SOURCE=3;

    public PageInfo<GeneralProductDto> pageQueryGeneralProduct(GeneralPageProductVo vo){
        log.info("#MeProductApiRemote.pageQueryGeneralProduct#info,参数:vo:{}", vo);
        try {
            ResponseInfo<PageInfo<GeneralProductDto>> responseInfo = skuGenReadApi.pageQueryGeneralProduct(vo);
            log.info("#MeProductApiRemote.pageQueryGeneralProduct#info,参数:vo:{}，返回:{}", vo, JSON.toJSONString(responseInfo));
            if(responseInfo.isFailure()){
                log.warn("#MeProductApiRemote.pageQueryGeneralProduct#warn,参数:vo:{}", vo);
                return null;
            }
            return responseInfo.getData();
        } catch (Exception e) {
            log.error("#MeProductApiRemote.pageQueryGeneralProduct#error,参数:vo:{}", vo, e);
            return null;
        }
    }

    public GeneralProductDto getGeneralProduct(String standardSkuId) {
        log.info("#MeProductApiRemote.getGeneralProduct#info,参数:id:{}", standardSkuId);
        try {
            GeneralProductVo vo = new GeneralProductVo();
            vo.setProductId(standardSkuId);
            ResponseInfo<GeneralProductDto> responseInfo = skuGenReadApi.getGeneralProduct(vo);
            log.info("#MeProductApiRemote.getGeneralProduct#info,参数:id:{}，返回:{}", vo, JSON.toJSONString(responseInfo));
            if(responseInfo.isFailure()){
                log.warn("#MeProductApiRemote.getGeneralProduct#warn,参数:id:{}", standardSkuId);
                return null;
            }
            return responseInfo.getData();
        } catch (Exception e) {
            log.error("#MeProductApiRemote.getGeneralProduct#error,参数:id:{}", standardSkuId, e);
            return null;
        }
    }

    public List<GeneralProductDto> getGeneralProduct(List<String> standardSkuId) {
        log.info("#MeProductApiRemote.getGeneralProductList#info,参数:standardSkuId:{}", JSON.toJSONString(standardSkuId));
        try {
            GeneralProductVo vo = new GeneralProductVo();
            vo.setProductIdList(standardSkuId);
            ResponseInfo<List<GeneralProductDto>> responseInfo = skuGenReadApi.getGeneralProductList(vo);
            log.info("#MeProductApiRemote.getGeneralProductList#info,参数:standardSkuId:{}，返回:{}", vo, JSON.toJSONString(responseInfo));
            if(responseInfo.isFailure()){
                log.warn("#MeProductApiRemote.getGeneralProductList#warn,参数:standardSkuId:{}", JSON.toJSONString(standardSkuId));
                return null;
            }
            return responseInfo.getData();
        } catch (Exception e) {
            log.error("#MeProductApiRemote.getGeneralProduct#error,参数:standardSkuId:{}", JSON.toJSONString(standardSkuId), e);
            return null;
        }
    }

    /**
     * 根据5要素匹配中台商品
     * @param product
     * @return
     */
    public GeneralMatchProductDto getGeneralMatchProduct(GeneralMatchProduct product){
        List<GeneralMatchProductDto> list = getGeneralMatchProduct(Lists.newArrayList(product));
        if(list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
     * 根据5要素匹配中台商品
     * @param productList
     * @return
     */
    public List<GeneralMatchProductDto> getGeneralMatchProduct(List<GeneralMatchProduct> productList){
        GeneralMatchProductVo productVo = new GeneralMatchProductVo();
        productVo.setTraceId("pop_"+ UUID.randomUUID().toString());
        productVo.setSource(STANDARD_LIB_GEN_READ_SOURCE);
        productVo.setProductList(productList);
        try {
            log.info("skuGenReadApi.skuGenReadApi(productVo:{}) ", JSON.toJSONString(productVo));
             ResponseInfo<List<GeneralMatchProductDto>> responseInfo = skuGenReadApi.getGeneralMatchProduct(productVo);
            log.info("skuGenReadApi.skuGenReadApi(productVo:{}) return {}", productVo.getTraceId(),JSON.toJSONString(responseInfo));
            List<GeneralMatchProductDto> resList = responseInfo.getData();
            if(CollectionUtils.isEmpty(resList)){
                return new ArrayList<>(0);
            }
            return resList;
        } catch (Exception e) {
            log.error("skuGenReadApi.skuGenReadApi(productVo:{}) 调用出错", JSON.toJSONString(productVo), e);
            return new ArrayList<>();
        }
    }

    /**
     * 新品上报
     * @param presentVo
     * @return
     */
    public List<GeneralProductPresentDto> report(GeneralProductPresentVo presentVo) {
        try{
            log.info("MeProductApiRemote.report#presentVo:{}",JSON.toJSONString(presentVo));
            ResponseInfo<List<GeneralProductPresentDto>> res = productPresentGenApi.addProductPresent(presentVo);
            log.info("MeProductApiRemote.report#presentVo:{} return {}",JSON.toJSONString(presentVo),JSON.toJSONString(res));
            if(res.isSuccess()){
                return res.getData()==null?new ArrayList<>(0):res.getData();
            }
            return null;
        }catch (Exception e){
            log.error("MeProductApiRemote.report#presentVo:{} return {}",JSON.toJSONString(presentVo),e);
            return null;
        }
    }


    public GeneralResponse<StandardLibResponseDataForBusinessScope> getStandardLibInfo(GeneralRequestForBusinessScope generalRequest) {
        try {
            log.info("standardLibGeneralFacade.getStandardLibInfo 搜索条件：{}", JSON.toJSONString(generalRequest));
            GeneralResponse<StandardLibResponseDataForBusinessScope> re = standardLibGeneralFacade.getStandardLibInfo(generalRequest);
            log.info("standardLibGeneralFacade.getStandardLibInfo 返回值：{}", JSON.toJSONString(re));
            return re;
        } catch (GeneralProductException e) {
            log.error("standardLibGeneralFacade.getStandardLibInfo(request:{}) 出现异常", JSON.toJSONString(generalRequest), e);
            return null;
        } catch (Exception e) {
            log.error("standardLibGeneralFacade.getStandardLibInfo(request:{}) 出现异常", JSON.toJSONString(generalRequest), e);
            return null;
        }
    }

    public List<PictureProResult> findProPicture(String... productIds) {
        if(productIds.length==0){
            return new ArrayList<>();
        }
        try {
            PictrueCondition pictrueCondition = new PictrueCondition();
            pictrueCondition.setProductIds(Arrays.asList(productIds));
            log.info("#MiddlegroundSkuInfoRemote.findProPicture#pictrueCondition:{}",JSON.toJSONString(pictrueCondition));
            com.xyy.me.product.general.api.common.ResponseInfo<List<PictureProResult>> proPicture = productPictureFacade.findProPicture(pictrueCondition);
            log.info("#MiddlegroundSkuInfoRemote.getGeneralProductList#rest:{}", JSON.toJSONString(proPicture));
            if (proPicture.isFailure()) {
                log.error("#MiddlegroundSkuInfoRemote.getGeneralProductList#rest:{}", JSON.toJSONString(proPicture));
                return new ArrayList<>();
            }
            return proPicture.getData();
        }catch (Exception e){
            log.error("#MiddlegroundSkuInfoRemote.getGeneralProductList 异常", e);
            return new ArrayList<>();
        }
    }
}
