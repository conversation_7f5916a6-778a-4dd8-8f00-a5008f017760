package com.xyy.ec.pop.service.impl;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.RedoRetryInfoHelper;
import com.xyy.ec.pop.remote.RedoRetryInfoRemote;
import com.xyy.ec.pop.server.api.retry.dto.RedoRetryInfoDto;
import com.xyy.ec.pop.service.RedoRetryInfoService;
import com.xyy.ec.pop.vo.RedoRetryInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/09/18/10:52
 * @Description:
 */
@Service
public class RedoRetryInfoServiceImpl implements RedoRetryInfoService {
    @Autowired
    private RedoRetryInfoRemote retryInfoRemote;
    @Override
    public RedoRetryInfoVo getRetryTaskInfoById(Long id) throws ServiceException {
        RedoRetryInfoDto retryTaskInfoDto = retryInfoRemote.getRetryTaskInfoById(id);
        return RedoRetryInfoHelper.covertDtoToVo(retryTaskInfoDto);
    }

    @Override
    public Boolean updateRetryTask(RedoRetryInfoVo retryInfo) throws ServiceException {
        RedoRetryInfoDto dto = RedoRetryInfoHelper.covertVoToDto(retryInfo);
        return retryInfoRemote.updateRetryTask(dto);
    }
}
