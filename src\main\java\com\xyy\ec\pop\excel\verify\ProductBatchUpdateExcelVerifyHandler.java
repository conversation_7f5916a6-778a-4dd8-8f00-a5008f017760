package com.xyy.ec.pop.excel.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.xyy.ec.pop.vo.SkuBatchUpdateVo;
import org.apache.commons.lang3.StringUtils;

/**
 * @version v1
 * @Description 商品导入过滤空行
 * <AUTHOR>
 */
public class ProductBatchUpdateExcelVerifyHandler implements IExcelVerifyHandler<SkuBatchUpdateVo> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(SkuBatchUpdateVo vo) {
        if(StringUtils.isNotBlank(vo.getBarcode())){
            return new ExcelVerifyHandlerResult(true);
        }
        return new ExcelVerifyHandlerResult(false);
    }
}
