package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.server.api.erp.dto.PopErpSkuAdminBatchUpdateDto;
import com.xyy.ec.pop.server.api.product.dto.PopErpSkuDto;
import com.xyy.ec.pop.vo.ErpSkuBatchUpdateVo;

/**
 * Created with IntelliJ IDEA.
 *
 * @project_name: xyy-ec-pop-service
 * @Auther: Jincheng.Li
 * @Date: 2021/04/15/10:03
 * @Description:
 */
public class PopErpSkuBatchUpdateConvertHelper {
    public static PopErpSkuAdminBatchUpdateDto convertVoToDto(ErpSkuBatchUpdateVo vo){
        PopErpSkuAdminBatchUpdateDto popErpSkuAdminBatchUpdateDto = new PopErpSkuAdminBatchUpdateDto();
        popErpSkuAdminBatchUpdateDto.setOrgId(vo.getOrgId());
        popErpSkuAdminBatchUpdateDto.setErpCode(vo.getErpCode());
        popErpSkuAdminBatchUpdateDto.setCode(vo.getCode());
        popErpSkuAdminBatchUpdateDto.setProductName(vo.getProductName());
        popErpSkuAdminBatchUpdateDto.setCommonName(vo.getCommonName());
        popErpSkuAdminBatchUpdateDto.setManufacturer(vo.getManufacturer());
        popErpSkuAdminBatchUpdateDto.setApprovalNumber(vo.getApprovalNumber());
        popErpSkuAdminBatchUpdateDto.setSpec(vo.getSpec());
        popErpSkuAdminBatchUpdateDto.setErrorMsg(vo.getErrorMsg());
        popErpSkuAdminBatchUpdateDto.setFailed(vo.isFailed());
        return popErpSkuAdminBatchUpdateDto;
    }

    public static ErpSkuBatchUpdateVo covertErpSkuDtoToUpdateVo(PopErpSkuDto popErpSkuDto) {
        ErpSkuBatchUpdateVo updateVo = new ErpSkuBatchUpdateVo();
        updateVo.setErpCode(popErpSkuDto.getErpCode());
        updateVo.setOrgId(popErpSkuDto.getOrgId());
        return updateVo;
    }

    public static ErpSkuBatchUpdateVo covertErpSkuAdminBatchDtoToDto(PopErpSkuAdminBatchUpdateDto dto){
        ErpSkuBatchUpdateVo vo = new ErpSkuBatchUpdateVo();
        vo.setOrgId(dto.getOrgId());
        vo.setErpCode(dto.getErpCode());
        vo.setErrorMsg(dto.getErrorMsg());
        vo.setFailed(dto.isFailed());
        return vo;
    }
}
