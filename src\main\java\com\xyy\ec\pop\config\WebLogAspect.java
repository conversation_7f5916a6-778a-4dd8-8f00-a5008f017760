package com.xyy.ec.pop.config;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * controller日志切面
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class WebLogAspect {

    /**
     * 换行符
     */
    private static final String LINE_SEPARATOR = System.lineSeparator();

    /**
     * 切点
     */
    @Pointcut("execution(* com.xyy.ec.pop.controller.CorporationController.*(..))")
    public void pointCut() {
    }

    /**
     * 环绕
     *
     * @param proceedingJoinPoint proceedingJoinPoint
     * @return Object
     * @throws Throwable throwable
     */
    @Around("pointCut()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        // 开始打印请求日志
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        // 打印请求 url
        log.info("URL            : {}", request.getRequestURL().toString());
        // 打印请求入参
        log.info("Request Args   : {}", JSON.toJSONString(Arrays.stream(proceedingJoinPoint.getArgs())
                .filter(item -> !(item instanceof HttpServletRequest))
                .filter(item -> !(item instanceof HttpServletResponse))
                .collect(Collectors.toList())));

        Object result;
        try {
            result = proceedingJoinPoint.proceed();
            // 打印出参？
        } catch (Exception e) {
            // todo 判断出参不是ResponseVo，直接抛异常 or beforeBodyWrite
            // 打印异常
            log.error("Error Message  : {}", e.getMessage(), e);
            if (e instanceof PopAdminException) {
                result = ResponseVo.errRest(((PopAdminException) e).getCode(), ((PopAdminException) e).getMsg());
            } else if (e instanceof IllegalAccessException) {
                result = ResponseVo.errRest(e.getMessage());
            } else {
                result = ResponseVo.errRest("未知错误，请联系研发");
            }
            log.error("Error Message  : {}", e.getMessage()+result.toString(), e);
        }
        return result;
    }

}
