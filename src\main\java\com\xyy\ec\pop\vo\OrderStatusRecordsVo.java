package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 2021/05/25
 */
@Data
public class OrderStatusRecordsVo {
    //订单日志 日志列表页不展示发送 bd按钮
    public static final Integer ORDER_LOG_TYPE = 1;
    //订单备注  日志列表页展示发送 bd按钮
    public static final Integer REMARK_TYPE = 2;
    /**
     * 包裹数
     */
    private Integer packagesNum;
    /**
     * 审核结果
     */
    private Integer auditResult;
    /**
     * 支付类型
     */
    private Integer payType;
    /**
     * 支付类型名称
     */
    private String payTypeName;
    /**
     * 支付金额
     */
    private BigDecimal payMoney;
    /**
     * 取消原因
     */
    private String cancelReason;
    /**
     * 操作时间
     */
    private Date operationTime;
    /**
     * 操作内容
     */
    private String operationContent;
    /**
     * 状态
     */
    private Integer orderStatus;

    private String orderStatusName;
    /**
     * 支付类型(1-支付宝,2-微信,3-银联,4-小药白条，7-电汇平台，8-电汇商业)
     */
    private Integer payChannel;
    /**
     *电汇凭证图片地址
     */
    private List<String> evidenceUrlList;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 日志类型 1订单日志 2商家备注
     */
    private Integer logType;

    //对客户是否可见： 1可见 0 不可见
    private Integer isVisible;

    private Boolean exceptionFlag = false;

    private String exceptionMsgJson;
    @Data
    public static class OrderExceptionLog implements Serializable {
        private static final long serialVersionUID = 2567461429302858206L;
        private String exceptionTxt;
        private List<String> exceptionDetailList;
        private String operateTime;
    }
}
