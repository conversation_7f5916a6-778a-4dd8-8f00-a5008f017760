package com.xyy.ec.pop.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.dto.ImpExcelDataDTO;
import com.xyy.ec.pop.dto.PopCashAdvanceBatchConfirmImportDTO;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.helper.PopCashAdvanceHelper;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.remote.PopCashAdvanceRemote;
import com.xyy.ec.pop.remote.PopCorporationRemoteAdapter;
import com.xyy.ec.pop.server.api.Enum.SaasShareProfitChannelEnum;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceDetailDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceLogDto;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.server.api.seller.enums.PopCashAdvanceStatusEnum;
import com.xyy.ec.pop.server.api.seller.param.CashAdvanceDetailQuery;
import com.xyy.ec.pop.server.api.seller.param.CashAdvanceQuery;
import com.xyy.ec.pop.service.ExcelReadService;
import com.xyy.ec.pop.service.admin.AdminCashAdvanceService;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.utils.ExceImportWarpUtil;
import com.xyy.ec.pop.utils.PageInfoUtils;
import com.xyy.ec.pop.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;

/**
 * 商家提现管理
 */
@RequestMapping("/popCashAdvanceManageV2")
@Slf4j
@RestController
public class CashAdvanceManageV2Controller extends BaseController {
    @Autowired
    private AdminCashAdvanceService adminCashAdvanceService;
    @Autowired
    private PopCashAdvanceRemote popCashAdvanceRemote;
    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;
    @Autowired
    private DownloadRemote downloadRemote;
    @Autowired
    private ExcelReadService excelReadService;
    @Value("${cashAdvance.auth.reason.maxLength:50}")
    private int maxReason;
    @Value("${cashAdvance.export.maxRows:20000}")
    private int maxExport;
    @Value("${cashAdvance.batchConfirm.count.limit:1000}")
    private Integer batchConfirmCountLimit;

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseVo<PageInfo> list(CashAdvanceQuery query) {
        try{
            log.info("SettlementController.list#query:{}", JSON.toJSONString(query));
            boolean b = (keywordToOrgIds(query) && CollectionUtils.isEmpty(query.getOrgIds()))
                    || CollectionUtils.isEmpty(getProvIds(query.getProvId()));
            if(b){
                return ResponseVo.successResult(new PageInfo());
            }
            query.setProvIds(getProvIds(query.getProvId()));
            PageInfo<PopCashAdvanceDto> page = popCashAdvanceRemote.queryCashAdvancePage(query);
            Optional.ofNullable(page.getList()).orElseGet(ArrayList::new).stream()
                    .forEach(t -> t.setReason(StringUtils.isEmpty(t.getReason()) && StringUtils.isNotEmpty(t.getFailureReason())
                            ? t.getFailureReason() : t.getReason()));
            log.info("SettlementController.list#query:{}, return {}", JSON.toJSONString(query), JSON.toJSONString(page));
            return ResponseVo.successResult(page);
        } catch (Exception e) {
            log.error("SettlementController.list#query:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("查询异常");
        }
    }

    private boolean keywordToOrgIds(CashAdvanceQuery query) {
        Set<String> orgIds = new HashSet<>();
        if (Objects.nonNull(query.getOrgId())) {
            orgIds.add(query.getOrgId());
        }

        if (StringUtils.isNotEmpty(query.getName()) || StringUtils.isNotEmpty(query.getCompanyName())) {
            if (StringUtils.isNotEmpty(query.getName()) || StringUtils.isNotEmpty(query.getCompanyName())) {
                orgIds.addAll(popCorporationRemoteAdapter.getOrgIdByName(query.getName(), query.getCompanyName()));
            }

            query.setOrgIds(new ArrayList<>(orgIds));
            return true;
        }
        return false;
    }

    @RequestMapping(value = "/sumAmount",method = RequestMethod.GET)
    public ResponseVo<BigDecimal> sumAmount(CashAdvanceQuery query) {
        try {
            log.info("CashAdvanceManageController.sumAmount#query:{}", JSON.toJSONString(query));
            if(keywordToOrgIds(query)&&CollectionUtils.isEmpty(query.getOrgIds()) || CollectionUtils.isEmpty(getProvIds(query.getProvId()))){
                return ResponseVo.successResult(BigDecimal.ZERO);
            }
            query.setProvIds(getProvIds(query.getProvId()));
            BigDecimal summation = popCashAdvanceRemote.sumAmount(query);
            log.info("CashAdvanceManageController.sumAmount#query:{} ,return {}", JSON.toJSONString(query), summation);
            return ResponseVo.successResult(summation);
        } catch (Exception e) {
            log.error("CashAdvanceManageController.sumAmount#query:{}, 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("查询异常");
        }
    }


    @GetMapping(value = "/cashAdvanceInfo")
    public ResponseVo cashAdvanceInfo(String cashAdvanceNum){
        try {
            log.info("CashAdvanceManageController.cashAdvanceInfo#cashAdvanceNum:{}", cashAdvanceNum);
            if (StringUtils.isBlank(cashAdvanceNum)) {
                return ResponseVo.errRest("请输入提现单号");
            }
            PopCashAdvanceDto dto = popCashAdvanceRemote.findByCashAdvanceNum(cashAdvanceNum);
            log.info("CashAdvanceManageController.cashAdvanceInfo#cashAdvanceNum:{} return {}", cashAdvanceNum, JSON.toJSONString(dto));
            if(dto==null){
                return ResponseVo.errRest("没有查询到提现单信息");
            }
            return ResponseVo.successResult(dto);
        } catch (Exception e) {
            log.error("CashAdvanceManageController.cashAdvanceInfo#cashAdvanceNum:{} 异常", cashAdvanceNum, e);
            return ResponseVo.errRest("查询提现单失败");
        }
    }

    @GetMapping(value = "/cashAdvanceDetail")
    public ResponseVo cashAdvanceDetail(CashAdvanceDetailQuery query){
        try {
            log.info("CashAdvanceManageController.cashAdvanceInfo#query:{}", JSON.toJSONString(query));
            PageInfo<PopCashAdvanceDetailDto> page = popCashAdvanceRemote.queryCashAdvanceDetailPage(query);
            log.info("CashAdvanceManageController.cashAdvanceInfo#query:{} return {}", JSON.toJSONString(query), JSON.toJSONString(page));
            List<PopCashAdvanceDetailVo> popCashAdvanceDetailVos = PopCashAdvanceHelper.convertToVos(page.getList());
            if (!org.springframework.util.CollectionUtils.isEmpty(popCashAdvanceDetailVos)) {
                popCashAdvanceDetailVos.stream().forEach(item -> {
                    if (item.getSettlementType() != null && item.getSettlementType() == CommissionSettleTypeEnum.EVERY_MONTH.getCode()) {
                        item.setHireMoney(item.getPayableCommission());
                    }
                });
            }
            PageInfo<PopCashAdvanceDetailVo> pageInfo = PageInfoUtils.pageInfo(popCashAdvanceDetailVos, (int) page.getTotal(), page.getPageNum(), page.getPageSize());
            return ResponseVo.successResult(pageInfo);
        }catch (ServiceException e){
            log.error("CashAdvanceManageController.cashAdvanceInfo#query:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("CashAdvanceManageController.cashAdvanceInfo#query:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("查询异常");
        }
    }

    @RequestMapping(value = "/async/downloadExcel",method = RequestMethod.GET)
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> downloadExcel(CashAdvanceQuery query){
        try {
            List<Long> provIds = getProvIds(query.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            query.setProvIds(provIds);
            log.info("CashAdvanceManageController.downloadExcel#query:{}", JSON.toJSONString(query));
            keywordToOrgIds(query);
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .businessType(DownloadTaskBusinessTypeEnum.CASH_ADVANCE)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("CashAdvanceManageController.downloadExcel#query:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("CashAdvanceManageController.downloadExcel#query:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    @RequestMapping(value = "/async/exportDetail",method = RequestMethod.GET)
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportDetail(CashAdvanceQuery query){
        try {
            log.info("CashAdvanceManageController.exportDetail#query:{}", JSON.toJSONString(query));
            //校验日期
//            validQueryParam(query);
            //校验机构
            keywordToOrgIds(query);

            query.setProvIds(getProvIds(query.getProvId()));
            PageInfo<PopCashAdvanceDto> page = popCashAdvanceRemote.queryCashAdvancePage(query);
            if (page.getTotal() != 1) {
                if ((CollectionUtils.isEmpty(query.getOrgIds()) && StringUtils.isEmpty(query.getOrgId()))
                        || (query.getOrgIds() != null && query.getOrgIds().size() > 1)) {
                    return ResponseVo.errRest("只允许导出一个商业在一个自然月内的提现明细，请修改查询条件进行查询并导出");
                }
            }
            List<Long> provIds = getProvIds(query.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            query.setProvIds(provIds);
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .businessType(DownloadTaskBusinessTypeEnum.CASH_ADVANCE_DETAIL)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("CashAdvanceManageController.exportDetail#query:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("CashAdvanceManageController.exportDetail#query:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }


    public int compareStr(String str1,String str2){
        if(str1==null&&str2==null) return 0;
        if(str1==null) return -1;
        if(str2==null) return 1;
        return str1.compareTo(str2);
    }

    private void validQueryParam(CashAdvanceQuery query) throws ServiceException {
        Calendar start = DateUtil.string2Calendar(query.getApplyTimeStart(), DateUtil.PATTERN_DATE);
        if(start==null){
            throw new ServiceException("只允许导出一个商业在一个自然月内的提现明细，请修改申请日期进行查询并导出");
        }
        Calendar end = DateUtil.string2Calendar(query.getApplyTimeEnd(),DateUtil.PATTERN_DATE);
        if(end == null){
            end = Calendar.getInstance();
        }
        //跨年或者跨月了
        if(start.get(Calendar.YEAR)!=end.get(Calendar.YEAR)||start.get(Calendar.MONTH)!=end.get(Calendar.MONTH)){
            throw new ServiceException("只允许导出一个商业在一个自然月内的提现明细，请修改申请日期进行查询并导出");
        }
        if(end.before(start)){
            throw new ServiceException("结束时间不能早于开始时间");
        }
    }

    @ResponseBody
    @PostMapping(value = "/confirmPayment")
    @AvoidRepeatableCommit(timeout=3)
    public ResponseVo confirmPayment(@RequestBody CashAdvancePayParamVo payParamVo){
        try {
            log.info("CashAdvanceManageController.confirmPayment#payParamVo:{}", JSON.toJSONString(payParamVo));
            if (CollectionUtils.isEmpty(payParamVo.getIds())) {
                return ResponseVo.errRest("请勾选要操作的提现记录");
            }
            if (PopCashAdvanceStatusEnum.SUCCESS.getStatus() == payParamVo.getAuditStatus()) {
                return auditOk(payParamVo);
            } else if (PopCashAdvanceStatusEnum.FAIL.getStatus() == payParamVo.getAuditStatus()) {
                return auditFail(payParamVo);
            }
            return ResponseVo.errRest("错误的审核状态");
        }catch (ServiceException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("CashAdvanceManageController.confirmPayment#payParamVo:{} 异常", JSON.toJSONString(payParamVo), e);
            return ResponseVo.errRest("审核失败");
        }
    }

    private ResponseVo auditFail(CashAdvancePayParamVo payParamVo) throws ServiceException {
        if(StringUtils.isEmpty(payParamVo.getReason())){
            return ResponseVo.errRest("请填写驳回原因");
        }
        if(payParamVo.getReason().length()>maxReason){
            return ResponseVo.errRest("驳回原因，限"+maxReason+"个字");
        }
        boolean success = popCashAdvanceRemote.auditFail(payParamVo.getIds(),getUser().getRealName(),payParamVo.getReason());
        if (success){
            return new ResponseVo(ResponseVo.CODE_SUCCESS,"驳回成功");
        } else {
            return ResponseVo.errRest("操作失败,请稍后重试！");
        }
    }

    /**
     * 审核通过
     * @param payParamVo
     * @return
     */
    private ResponseVo auditOk(CashAdvancePayParamVo payParamVo) throws ServiceException {
        Date makeMoneyTime = DateUtil.string2Date(payParamVo.getMakeMoneyTime(),DateUtil.PATTERN_STANDARD);
        if(makeMoneyTime==null){
            return ResponseVo.errRest("请填写打款时间");
        }
        boolean success =  adminCashAdvanceService.confirmPayment(payParamVo.getIds(),getUser().getRealName(),makeMoneyTime);
        if (success){
            return new ResponseVo(ResponseVo.CODE_SUCCESS,"确认打款成功");
        } else {
            return ResponseVo.errRest("操作失败,请稍后重试！");
        }
    }


    @RequestMapping(value = "accountLog",method = RequestMethod.GET)
    public ResponseVo accountLog(int accountId){
        try {
            log.info("CashAdvanceManageController.accountLog#accountI:{}", accountId);
            PopCashAdvanceDto popCashAdvance = adminCashAdvanceService.queryById(accountId);
            if (popCashAdvance == null) {
                return ResponseVo.errRest("不存在此提现记录");
            }
            List<CashAdvanceLogVo> list = new ArrayList<>();

            if (popCashAdvance.getPaymentStatus().intValue() == PopCashAdvanceStatusEnum.SUCCESS.getStatus() ||
                    popCashAdvance.getPaymentStatus().intValue() == PopCashAdvanceStatusEnum.FAIL.getStatus() ||
                    popCashAdvance.getPaymentStatus().intValue() == PopCashAdvanceStatusEnum.REEXCHANGE.getStatus()) {
                if (SaasShareProfitChannelEnum.FUMIN_CHANNEL.getDesc().equals(popCashAdvance.getUpdateBy()) || SaasShareProfitChannelEnum.PINGAN_CHANNEL.getDesc().equals(popCashAdvance.getUpdateBy())) {
                    CashAdvanceLogVo vo = new CashAdvanceLogVo();
                    vo.setAction(popCashAdvance.getPaymentStatus().intValue() == PopCashAdvanceStatusEnum.SUCCESS.getStatus() ? "打款成功" : PopCashAdvanceStatusEnum.getText(popCashAdvance.getPaymentStatus().intValue()));
                    vo.setAuditor(popCashAdvance.getUpdateBy());
                    vo.setActionTime(DateUtil.date2String(popCashAdvance.getApplyTime(), DateUtil.PATTERN_STANDARD));
                    vo.setReason(popCashAdvance.getReason());

                    list.add(vo);
                }
            }
            List<PopCashAdvanceLogDto> cashAdvanceLogs = popCashAdvanceRemote.getCashAdvanceLog(Long.valueOf(popCashAdvance.getId()));
            if (CollectionUtils.isNotEmpty(cashAdvanceLogs)){
                for (PopCashAdvanceLogDto popCashAdvanceLogDto:cashAdvanceLogs) {
                    CashAdvanceLogVo vo = new CashAdvanceLogVo();
                    vo.setAction(popCashAdvanceLogDto.getRemarks());
                    vo.setAuditor(popCashAdvanceLogDto.getCreateName());
                    vo.setActionTime(DateUtil.date2String(popCashAdvanceLogDto.getCreateTime(), DateUtil.PATTERN_STANDARD));
                    list.add(vo);
                }
            }
            log.info("CashAdvanceManageController.accountLog#accountI:{} return {}", accountId, JSON.toJSONString(list));
            return ResponseVo.successResult(list);

        } catch (Exception e) {
            log.error("CashAdvanceManageController.accountLog#accountI:{} 异常", accountId, e);
            return ResponseVo.errRest("查询异常");
        }
    }

    @PostMapping(value = "/batchConfirm")
    @ResponseBody
    @AvoidRepeatableCommit(timeout=3)
    public ResponseVo<BatchUpdateResultVo> batchConfirm(@RequestParam("file") MultipartFile file) {
        try {
            // 导入文件
            ImpExcelDataDTO<PopCashAdvanceBatchConfirmImportDTO> impExcelDataDTO = excelReadService.readExcel(file, PopCashAdvanceBatchConfirmImportDTO.class);

            // 校验文件
            checkFile(file, (new ArrayList<>(impExcelDataDTO.getHeadMaps().values())), Arrays.asList("提现单号（必填项）"));

            // 校验数据
            validateParameters(impExcelDataDTO.getDataList());

            // 批量确认
            return adminCashAdvanceService.batchConfirm(impExcelDataDTO.getDataList(), super.getUserName(), file);
        } catch (PopAdminException e) {
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("商户提现批量确认失败", e);
            return ResponseVo.errRest("批量确认失败");
        }
    }

    private void checkFile(MultipartFile file, List<String> actualTitles, List<String> expectedTitles) {
        if (file.isEmpty() || file.getSize() > 3 * (1024 * 1024)) {
            throw new PopAdminException("文件大小不能超过3M");
        }

        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName) ||
                (!fileName.toLowerCase().endsWith(".xls") && !fileName.toLowerCase().endsWith(".xlsx"))) {
            throw new PopAdminException(ExceImportWarpUtil.commonTip);
        }

        if (CollectionUtils.isEmpty(expectedTitles)) {
            return;
        }
        if (CollectionUtils.isEmpty(actualTitles) || !new HashSet<>(actualTitles).containsAll(expectedTitles) ||
                !new HashSet<>(expectedTitles).containsAll(actualTitles)) {
            List<String> missingColumns = new ArrayList<>(expectedTitles);
            List<String> extraColumns = new ArrayList<>(actualTitles);
            missingColumns.removeAll(actualTitles);
            extraColumns.removeAll(expectedTitles);
            log.info("当前导入模板缺少列：{}, 多余列：{}", JSON.toJSONString(missingColumns), JSON.toJSONString(extraColumns));
            throw new PopAdminException(ExceImportWarpUtil.commonTip);
        }
    }

    public void validateParameters(List<PopCashAdvanceBatchConfirmImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new PopAdminException("没有需要确认的数据");
        }
        if (list.size() > batchConfirmCountLimit) {
            throw new PopAdminException("当前上传文件超过" + batchConfirmCountLimit + "条，请分批上传");
        }
    }

    @ResponseBody
    @PostMapping(value = "/confirmPaymentNew")
    @AvoidRepeatableCommit(timeout=3)
    public ResponseVo confirmPaymentNew(@RequestBody CashAdvancePayParamVo payParamVo){
        try {
            log.info("CashAdvanceManageController.confirmPayment#payParamVo:{}", JSON.toJSONString(payParamVo));
            if (CollectionUtils.isEmpty(payParamVo.getIds())) {
                return ResponseVo.errRest("请勾选要操作的提现记录");
            }
            boolean success =  adminCashAdvanceService.confirmPayment(payParamVo.getIds(), getUser().getRealName());
            if (success) {
                return new ResponseVo(ResponseVo.CODE_SUCCESS,"确认打款成功");
            } else {
                return ResponseVo.errRest("操作失败,请稍后重试！");
            }
        }catch (ServiceException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("CashAdvanceManageController.confirmPayment#payParamVo:{} 异常", JSON.toJSONString(payParamVo), e);
            return ResponseVo.errRest("审核失败");
        }
    }

}
