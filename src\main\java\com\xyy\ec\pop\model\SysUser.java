package com.xyy.ec.pop.model;

import com.xyy.ec.pop.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 系统用户
 * 
 * <AUTHOR>
 */
@ApiModel("系统用户实体")
public class SysUser extends BaseEntity{
    private static final long serialVersionUID = 1L;

    // 主键
    @ApiModelProperty("用户主键")
    private Long id;

    // 用户名
    @ApiModelProperty("用户名")
    private String username;

    // 密码
    @ApiModelProperty("密码")
    private String password;

    // 邮箱地址
    @ApiModelProperty("邮箱地址")
    private String email;

    // 电话
    @ApiModelProperty("电话")
    private String phone;

    // 创建时间
    @ApiModelProperty("创建时间")
    private Date createtime;

    // 更新时间
    @ApiModelProperty("更新时间")
    private Date updatetime;

    // 状态
    @ApiModelProperty("状态")
    private Integer status;
    
    // 是否是超级管理员 0:否 1:是
    @ApiModelProperty("是否是超级管理员 0:否 1:是")
    private Integer isSuper;

    @ApiModelProperty("部门Id")
    private Long departmentId;

    @ApiModelProperty("用户真实名称")
    private String realName;

    @ApiModelProperty("用户角色Id")
    private String roleIds;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("工号")
    private String jobNumber;
    //中台Token
    private String token;

    private String oaId;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getOaId() {
        return oaId;
    }

    public void setOaId(String oaId) {
        this.oaId = oaId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username == null ? null : username.trim();
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(String roleIds) {
        this.roleIds = roleIds;
    }

    
    public String getDepartment() {
        return department;
    }

    
    public void setDepartment(String department) {
        this.department = department;
    }

	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	public Integer getIsSuper() {
		return isSuper;
	}

	public void setIsSuper(Integer isSuper) {
		this.isSuper = isSuper;
	}

}
