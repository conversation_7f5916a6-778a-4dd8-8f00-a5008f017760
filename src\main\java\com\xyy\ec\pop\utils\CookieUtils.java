/**
 * @(#)CookieUtils.java Copyright 2010 jointown, Inc. All rights reserved.
 */

package com.xyy.ec.pop.utils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * description
 * 
 * 
 */
public class CookieUtils {

    public static final String VIEW_HISTORY_COOKIE_KEY = "client_view_history";
    public static final int    COOKIE_AGE              = 30 * 24 * 60 * 60;
    public static final String COOKIE_PATH             = "/";

    private CookieUtils(){
    }

    /**
     * 得到当前request请求的所有cookie
     * 
     * 
     * @return cookie数组
     */
    public static Cookie[] getCookies() {
        HttpServletRequest request = WebContext.currentRequest();
        return request == null ? null : request.getCookies();
    }

    /**
     * 根据cookie名字取得cookie
     * 
     * 
     * @param name cookie名字
     * @return cookie
     */
    public static Cookie getCookie(String name) {
        Cookie[] cookies = getCookies();
        if (CollectionUtil.isNotEmpty(cookies)) {
            for (Cookie cookie : cookies) {
                String cookName = cookie.getName();
                if (cookName != null && cookName.equals(name)) {
                    return cookie;
                }
            }
        }
        return null;
    }

    /**
     * 将cookie写入客户端
     * 
     *
     * @param cookie
     */
    public static void writeCookie(Cookie cookie) {
        if (cookie == null) return;
        HttpServletResponse response = WebContext.currentResponse();
        HttpServletRequest request = WebContext.currentRequest();
        if (request != null) {
            String host = request.getHeader("Host");
            if (ConfigUtils.WEBSITE.equals(host)) cookie.setDomain("." + ConfigUtils.DOMAIN);
        }
        if (response != null) {
            response.addCookie(cookie);
        }
    }

    public static void removeCookie(String cookieName, String path) {
        HttpServletRequest request = WebContext.currentRequest();
        Cookie[] cookies = request.getCookies();
        if (CollectionUtil.isEmpty(cookies)) return;

        for (int i = 0; i < cookies.length; i++) {
            Cookie cookie = cookies[i];
            if (cookie.getName().equals(cookieName)) {
                cookie.setMaxAge(0);
                cookie.setPath(path);
                String host = request.getHeader("Host");
                if (ConfigUtils.WEBSITE.equals(host)) cookie.setDomain("." + ConfigUtils.DOMAIN);
                HttpServletResponse response = WebContext.currentResponse();
                if(response != null) {
                	response.addCookie(cookie);
                }
                break;
            }
        }
    }
}
