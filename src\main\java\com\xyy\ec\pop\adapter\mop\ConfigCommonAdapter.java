package com.xyy.ec.pop.adapter.mop;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.ConfigRemote;
import com.xyy.pop.mop.api.remote.parameter.ConfigAddOrUpdateParameter;
import com.xyy.pop.mop.api.remote.result.ConfigBasicDTO;
import com.xyy.scm.constant.entity.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Component
public class ConfigCommonAdapter implements MopBaseAdapter {
    @Reference(version = "1.0.0")
    ConfigRemote configRemote;

    /**
     * 直接返回json字符串
     *
     * @param configBasicDTO
     * @return
     * @throws Exception
     */
    public ConfigBasicDTO queryConfigJson(ConfigBasicDTO configBasicDTO){
        Result<List<ConfigBasicDTO>> listResult = configRemote.queryConfigByType(configBasicDTO);
        if (listResult.isFailure()){
            log.info("queryConfigJson失败!:{}",listResult.getMsg());
            return new ConfigBasicDTO();
        }
        List<ConfigBasicDTO> result = listResult.getResult();
        if (CollectionUtils.isEmpty(result)){
            log.info("未查询到数据!:{}",configBasicDTO);
            return new ConfigBasicDTO();
        }
        return result.get(0);
    }

    /**
     * 将json自动转换成dto再传回来
     *
     * @param configBasicDTO
     * @param clazz
     * @return
     */
    public ResponseVo<T> queryConfigDto(ConfigBasicDTO configBasicDTO, Class<T> clazz){
        return to(()->configRemote.getConfigByNameOrId(configBasicDTO, clazz));
    }

    /**
     * 根据配置名称自动插入或更新，因为配置类的名称唯一
     *
     * @param param
     * @return
     */
    public ResponseVo insertOrUpdateConfig(ConfigAddOrUpdateParameter param){
        return to(()->configRemote.addOrUpdateConfig(param));
    }

    public ResponseVo configDetail(String configName) {
        return to(()->configRemote.configDetail(configName));
    }
}
