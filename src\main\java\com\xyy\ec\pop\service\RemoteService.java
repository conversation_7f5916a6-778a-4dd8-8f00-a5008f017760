package com.xyy.ec.pop.service;

import com.xyy.ec.pop.server.api.shop.dto.PopCheckLogDto;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;

import java.util.List;

public interface RemoteService {

    public List<ShopInfoDTO> queryInfoByShopCodes(List<String> shopCodes);

    public Boolean updateShopStatusByShopCode(String shopCode, Integer status);

    public Boolean savePopCheckLogDto(PopCheckLogDto dto);
}
