package com.xyy.ec.pop.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.PopErpSkuBatchUpdateConvertHelper;
import com.xyy.ec.pop.remote.*;
import com.xyy.ec.pop.service.FastDfsUtilService;
import com.xyy.ec.pop.service.PopErpSkuBatchUpdateService;
import com.xyy.ec.pop.vo.BatchUpdateResultVo;
import com.xyy.ec.pop.vo.ErpSkuBatchUpdateVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @project_name: xyy-ec-pop-service
 * @Auther: Jincheng.Li
 * @Date: 2021/04/19/14:58
 * @Description:
 */
@Slf4j
@Service
public class PopErpSkuBatchUpdateServiceImpl implements PopErpSkuBatchUpdateService {

    @Autowired
    private FastDfsUtilService fastDfsUtilService;


    @Autowired
    private PopErpSkuBatchUpdateRemoteAdapter popErpSkuBatchUpdateRemoteAdapter;

    /**
     * 批量修改erp商品
     * @param user 操作人
     * @param vos 导入修改信息
     * @return
     * @throws ServiceException
     * <AUTHOR>
     */

    @Override
    public BatchUpdateResultVo batchUpdateErpSku(String user, List<ErpSkuBatchUpdateVo> vos) throws ServiceException {

        //记录原始文件：包含错误原因，方便后期分析
        String fileUrl = fastDfsUtilService.writeDateToFastDfs(new ExportParams(null, "批量导入的ERP商品-" + user, ExcelType.XSSF), ErpSkuBatchUpdateVo.class, new ArrayList<>(vos));
        if (StringUtils.isEmpty(fileUrl)) {
            throw new ServiceException("导入商品失败：记录原始文件异常");
        }

        //过滤可以更新的商品
        List<ErpSkuBatchUpdateVo> okVos = vos.stream().filter(item -> !item.isFailed()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(okVos)) {
            BatchUpdateResultVo resultVo = batchUpdateErpSkuResultVoBase(vos.size(), vos);
            log.info("批量更新ERP商品信息：没有可更新ERP商品:user:{},result:{}", user, JSON.toJSONString(resultVo));
            return resultVo;
        }

        List<ErpSkuBatchUpdateVo> notExists = popErpSkuBatchUpdateRemoteAdapter.batchUpdateErpSku(user, okVos.stream().map(PopErpSkuBatchUpdateConvertHelper::convertVoToDto).collect(Collectors.toList()), fileUrl);

        Map<String, String> notExistOrgIdAndErpCodeMap = Maps.newHashMap();
        notExists.forEach(m -> {
            notExistOrgIdAndErpCodeMap.put(m.getOrgId() +"-"+ m.getErpCode(), m.getErrorMsg());
        });

        log.info("批量更新商品信息，成功更新：{}个", okVos.size() - notExists.size());
        vos.forEach(m -> {
            String mKey = m.getOrgId() +"-"+ m.getErpCode();
            String mValue = notExistOrgIdAndErpCodeMap.get(mKey);
            if (StringUtils.isNotEmpty(mValue)) {
                m.setFailed(true);
                m.setErrorMsg(mValue);
            }
        });

        log.info("#错误记录查看##errorErpSku:{}",vos);
        //错误信息写入文件
        List<ErpSkuBatchUpdateVo> errorsVos = vos.stream().filter(ErpSkuBatchUpdateVo::isFailed).collect(Collectors.toList());
        BatchUpdateResultVo resultVo = batchUpdateErpSkuResultVoBase(vos.size(), errorsVos);
        log.info("批量更新ERP商品信息：更新结果:user:{},result:{}", user, JSON.toJSONString(resultVo));
        return resultVo;
    }

    /**
     * 记录失败的erp商品文件
     *
     * @param totalSize
     * @param errorVos 修改失败的记录
     * @return
     */
    private BatchUpdateResultVo batchUpdateErpSkuResultVoBase(int totalSize, List<ErpSkuBatchUpdateVo> errorVos) {
        BatchUpdateResultVo resultVo = new BatchUpdateResultVo();
        resultVo.setError(errorVos.size());
        String uuid = UUID.randomUUID().toString();
        resultVo.setErrorFileUrl(uuid);
        resultVo.setSuccess(totalSize - resultVo.getError());
        //将数据写入excel文件
        String fileUrl = fastDfsUtilService.writeDateToFastDfs(new ExportParams(null, "导入失败ERP商品", ExcelType.XSSF), ErpSkuBatchUpdateVo.class, new ArrayList<>(errorVos));
        resultVo.setErrorFileUrl(fileUrl);
        return resultVo;
    }

}
