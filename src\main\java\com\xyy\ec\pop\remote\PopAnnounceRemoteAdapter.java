package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.PopAnnounceHelper;
import com.xyy.ec.pop.server.api.announce.api.AnnounceApi;
import com.xyy.ec.pop.server.api.announce.dto.PopAnnounceDto;
import com.xyy.ec.pop.server.api.announce.dto.PopAnnounceQueryParamDto;
import com.xyy.ec.pop.vo.PopAnnounceQueryParamVo;
import com.xyy.ec.pop.vo.PopAnnounceVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: Jincheng.Li
 * @Date: 2021/07/14/14:36
 * @Description:
 */
@Component
@Slf4j
public class PopAnnounceRemoteAdapter {
    @Reference
    private AnnounceApi announceApi;

    /**
     * 分页查询公告列表
     *
     * @param paramVo
     * @return
     */
    public PageInfo<PopAnnounceVo> getPageList(PopAnnounceQueryParamVo paramVo) throws Exception {
        try {
            log.info("PopAnnounceRemoteAdapter.getPageList request param:{}", JSON.toJSONString(paramVo));
            PopAnnounceQueryParamDto popAnnounceQueryParamDto = PopAnnounceHelper.covertQueryParamVoToDto(paramVo);
            ApiRPCResult<PageInfo<PopAnnounceDto>> pageListPop = announceApi.getPageListPop(popAnnounceQueryParamDto);
            log.info("PopAnnounceRemoteAdapter.getPageList response result:{}", JSON.toJSONString(pageListPop));
            if (pageListPop != null && pageListPop.isSuccess()) {
                return PopAnnounceHelper.covertPage(pageListPop.getData());
            }
            throw new ServiceException("查询系统公告失败");
        } catch (Exception e) {
            log.error("PopAnnounceRemoteAdapter.getPageList error 分页查询广告失败", e);
            throw new ServiceException("查询系统公告失败");
        }
    }

    /**
     * 获取当前启用公告数量
     *
     * @return
     */
    public Integer getUsingAnnounce() {
        try {
            log.info("PopAnnounceRemoteAdapter.getUsingAnnounce request");
            ApiRPCResult<List<PopAnnounceDto>> usingAnnounce = announceApi.getUsingAnnounce();
            log.info("PopAnnounceRemoteAdapter.getUsingAnnounce response result:{}", JSON.toJSONString(usingAnnounce));
            if (usingAnnounce == null || usingAnnounce.isFail()) {
                return -1;
            }
            return usingAnnounce.getData().size();
        } catch (Exception e) {
            log.error("PopAnnounceRemoteAdapter.getUsingAnnounce error", e);
            return -1;
        }
    }

    /**
     * 保存公告信息
     *
     * @param popAnnounceVo
     * @param user
     * @return
     */
    public Boolean savePopAnnounce(PopAnnounceVo popAnnounceVo, String user) {
        try {
            log.info("PopAnnounceRemoteAdapter.savePopAnnounce param:{}", JSON.toJSONString(popAnnounceVo));
            PopAnnounceDto dto = PopAnnounceHelper.covertVoToDto(popAnnounceVo, user);
            log.info("PopAnnounceRemoteAdapter.savePopAnnounce request param:{}", JSON.toJSONString(dto));
            ApiRPCResult<Boolean> apiRPCResult = announceApi.saveAnnounce(dto);
            log.info("PopAnnounceRemoteAdapter.savePopAnnounce response result:{}", JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return false;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.info("PopAnnounceRemoteAdapter.savePopAnnounce error", e);
            return false;
        }
    }

    public PopAnnounceVo getDetail(Integer id) throws Exception{
        try {
            log.info("PopAnnounceRemoteAdapter.getDetail request param:{}", id);
            ApiRPCResult<PopAnnounceDto> announceDetail = announceApi.getAnnounceDetail(id);
            log.info("PopAnnounceRemoteAdapter.getDetail response result:{}", JSON.toJSONString(announceDetail));
            if (announceDetail == null || announceDetail.isFail()) {
                throw new ServiceException("查询公告详情失败");
            }
            return PopAnnounceHelper.covertDtoToVo(announceDetail.getData());
        } catch (Exception e) {
            log.error("PopAnnounceRemoteAdapter.getDetail error", e);
            throw new ServiceException("查询公告详情失败");
        }
    }

    public Boolean deleteAnnounceById(Integer id, String user) {
        try {
            log.info("PopAnnounceRemoteAdapter.deleteAnnounceById request param id:{},user:{}", id, user);
            ApiRPCResult<Boolean> apiRPCResult = announceApi.deleteAnnounce(id, user);
            log.info("PopAnnounceRemoteAdapter.deleteAnnounceById response id:{},user:{},result:{}", id, user, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return false;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("PopAnnounceRemoteAdapter.deleteAnnounceById error id:{},user:{}", id, user, e);
            return false;
        }
    }

    public Boolean stopUsing(Integer id, Integer status,Byte oldStatus, String user) {
        try {
            log.info("PopAnnounceRemoteAdapter.stopUsing param id:{},status:{},oldStatus:{},user:{}", id, status,oldStatus, user);
            ApiRPCResult<Boolean> apiRPCResult = announceApi.stopUsingAnnounce(id, status,oldStatus, user);
            log.info("PopAnnounceRemoteAdapter.stopUsing response id:{},status:{},user:{},result:{}", id, status, user, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return false;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("PopAnnounceRemoteAdapter.stopUsing error id:{},status:{},user:{}", id, status, user, e);
            return false;
        }
    }

    public Boolean closeBanner(Integer id,String user){
        try {
            log.info("PopAnnounceRemoteAdapter.closeBanner param id:{},user:{}", id, user);
            ApiRPCResult<Boolean> apiRPCResult = announceApi.closeBanner(id, user);
            log.info("PopAnnounceRemoteAdapter.closeBanner response result:{}", JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return false;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("PopAnnounceRemoteAdapter.stopUsing error", e);
            return false;
        }
    }

}
