package com.xyy.ec.pop.utils;

public interface ImgConstant {


    enum UpLoadImgAttr implements ImgConstant{
        /** 商品原图-大图*/
        shangpinyuantu_big(800, true, 800, true, false),
        /** 商品原图-小图 */
        shangpinyuantu_small(320, false, 320, false, false),
        /** 商品说明图-大图 */
        shangpinshuomingshu(1280, true, 1280, false, true);


        private int width;
        private boolean isCheckWidth;
        private int height;
        private boolean isCheckHeight;

        /** 是否按照上传比例等比压缩 */
        private boolean dengbiYs;

        private UpLoadImgAttr(){}

        private UpLoadImgAttr(int width, boolean isCheckWidth, int height, boolean isCheckHeight, boolean dengbiYs){
            this.width = width;
            this.isCheckWidth = isCheckWidth;
            this.height = height;
            this.isCheckHeight = isCheckHeight;
            this.dengbiYs = dengbiYs;
        }

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public boolean isCheckWidth() {
            return isCheckWidth;
        }

        public void setCheckWidth(boolean checkWidth) {
            isCheckWidth = checkWidth;
        }

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public boolean isCheckHeight() {
            return isCheckHeight;
        }

        public void setCheckHeight(boolean checkHeight) {
            isCheckHeight = checkHeight;
        }

        public boolean isDengbiYs() {
            return dengbiYs;
        }

        public void setDengbiYs(boolean dengbiYs) {
            this.dengbiYs = dengbiYs;
        }
    }
}
