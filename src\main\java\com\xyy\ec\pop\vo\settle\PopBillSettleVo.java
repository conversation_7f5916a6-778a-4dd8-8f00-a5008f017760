package com.xyy.ec.pop.vo.settle;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @date  2020/12/4 10:24
* @table
*/
@Data
public class PopBillSettleVo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 商家名称
     */
    private String orgName;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 区域编码
     */
    private String branchCode;

    /**
     * 域orgId(EC传的orgId)
     */
    private String areaOrgId;

    /**
     * 单据类型(1:订单 2:退款单）
     */
    private Byte businessType;

    /**
     * 订单或退款单号
     */
    private String businessNo;
    /**
     * 客户名称
     */
    private String merchantName;

    /**
     * 商品总金额
     */
    private BigDecimal productMoney;

    /**
     * 单据总金额
     */
    private BigDecimal totalMoney;

    /**
     * 订单实际付款金额
     */
    private BigDecimal money;

    /**
     * 运费
     */
    private BigDecimal freightAmount;

    /**
     * 店铺优惠券金额
     */
    private BigDecimal couponShopAmount;

    /**
     * 店铺营销活动优惠金额
     */
    private BigDecimal marketingShopAmount;

    /**
     * 店铺总优惠金额
     */
    private BigDecimal shopTotalDiscount;

    /**
     * 平台优惠券金额
     */
    private BigDecimal couponPlatformAmount;

    /**
     * 平台营销活动优惠金额
     */
    private BigDecimal marketingPlatformAmount;

    /**
     * 平台总优惠金额
     */
    private BigDecimal platformTotalDiscount;

    /**
     * 佣金
     */
    private BigDecimal hireMoney;
    /**
     * 应收佣金
     */
    private BigDecimal payableCommission;
    /**
     * 佣金结算方式，1：非月结；2：月结
     */
    private Byte settlementType;
    /**
     * 处罚金额
     */
    private BigDecimal penaltyAmount;

    /**
     * 应结算金额
     */
    private BigDecimal statementTotalMoney;

    /**
     * 支付时间
     */
    private Date orderPayTime;

    /**
     * 订单或退款单完成时间
     */
    private Date orderFinishTime;

    /**
     * 支付类型(1:在线支付  3:线下转账）
     */
    private Byte payType;
    /**
     * 多个支付类型
     */
    private List<Byte> payTypes;
    /**
     * 订单结算状态 0-待结算 1-结算完成
     */
    private Byte orderSettlementStatus;

    /**
     * 订单结算时间
     */
    private Date orderSettlementTime;

    /**
     * 账单生成时间
     */
    private Date billCreateTime;

    /**
     * 账单生成状态： 0：未生成，1:已生成
     */
    private Byte billCreateStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 结算开始时间
     */
    private Date startOrderSettlementTime;
    /**
     * 结算结束时间
     */
    private Date endOrderSettlementTime;
    /**
     * 支付开始时间
     */
    private Date startOrderPayTime;
    /**
     * 支付结束时间
     */
    private Date endOrderPayTime;

    private List<String> orgIds;

    private Long provId;

    private List<Long> provIds;

    /**
     * 补贴冲抵佣金 0-否 1-是
     */
    private Byte deducted;

    /**
     * 是否参与佣金折扣:0-不参与,1-参与
     */
    private Integer commissionCalcFlag;

    /**
     * 结算方式:1:直连支付  3:平安支付
     */
    private Integer paymentChannel;
}
