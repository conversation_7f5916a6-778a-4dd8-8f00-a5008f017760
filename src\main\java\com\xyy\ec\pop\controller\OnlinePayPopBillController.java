package com.xyy.ec.pop.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.excel.entity.OnlinePayPopBillExportVo;
import com.xyy.ec.pop.excel.style.SettleExcelExportStyler;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.helper.PopBillHelper;
import com.xyy.ec.pop.po.PopBillDetailPo;
import com.xyy.ec.pop.po.PopBillPo;
import com.xyy.ec.pop.server.api.order.enums.OrderPayTypeEnums;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.service.settle.PopBillService;
import com.xyy.ec.pop.service.settle.impl.domain.PopBillDomainService;
import com.xyy.ec.pop.utils.CollectionUtil;
import com.xyy.ec.pop.utils.EncodeUtil;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.settle.PopBillStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillVo;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**在线支付账单
* <AUTHOR>
* @date  2020/12/2 18:24
* @table
*/
@Slf4j
@RequestMapping("/onlinePayPopBill")
@Controller
public class OnlinePayPopBillController extends BaseController{

    @Autowired
    private PopBillService popBillService;
    @Autowired
    private PopBillDomainService popBillDomainService;



    /**
     * 列表查询
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Object list(PopBillVo popBillVo, PageInfo pageInfo) {
        try {
            if (validateProv(popBillVo)) {
                return ResponseUtils.returnObjectSuccess(new PageInfo<>());
            }
            popBillVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            PageInfo<PopBillPo> popBillPoPageInfo = popBillDomainService.queryPopBillList(popBillVo, pageInfo);
            return ResponseUtils.returnObjectSuccess(popBillPoPageInfo);
        } catch (ServiceRuntimeException e) {
            log.error("查询在线支付账单列表异常", e);
            return ResponseUtils.returnException(e);
        }
    }

    private boolean validateProv(PopBillVo popBillVo) {
        List<Long> provIds = getProvIds(popBillVo.getProvId());
        if(CollectionUtils.isEmpty(provIds)){
            return true;
        }
        popBillVo.setProvIds(provIds);
        return false;
    }


    /**
     * 列表根据搜索条件统计佣金金额，应结算金额
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryPopBillPayStatis", method = RequestMethod.GET)
    public Object queryPopBillPayStatis(PopBillVo popBillVo) {
        try {
            if (validateProv(popBillVo)) {
                return ResponseUtils.returnObjectSuccess(new PopBillStatisVo());
            }
            popBillVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            PopBillStatisVo popBillPo = popBillDomainService.queryPopBillPayStatis(popBillVo);
            return ResponseUtils.returnObjectSuccess(popBillPo);
        } catch (ServiceRuntimeException e) {
            log.error("查询统计在线支付账单异常", e);
            return ResponseUtils.returnException(e);
        }
    }


    /**
     * 查询列表在线支付账单导出的条数  超过5000条数据，前端给出提示
     * @param popBillVo
     * @return
     */
    @RequestMapping(value = "/queryExprotBillListCount")
    @ResponseBody
    public Object queryExprotBillListCount(PopBillVo popBillVo){
        try{
            popBillVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            Long count = popBillDomainService.queryPopBillListCount(popBillVo);
            return ResponseUtils.returnObjectSuccess(count);
        }catch (Exception e){
            log.error("查询列表在线支付账单导出的条数异常",e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 导出在线支付账单
     * @param popBillVo
     * @param request
     * @param res
     */
    @RequestMapping(value = "/exportBillPaymemtList", method = RequestMethod.GET)
    public void exportBillPaymemtList(PopBillVo popBillVo, HttpServletRequest request, HttpServletResponse res){
        try{
            popBillVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            Workbook workbook = null;
            ExportParams params = new ExportParams();
            params.setSheetName("账单");
            params.setType(ExcelType.XSSF);
            params.setColor(IndexedColors.BLUE_GREY.index);
            params.setFreezeCol(2);
            params.setStyle(SettleExcelExportStyler.class);
            int pageNum = 1;
            int limit = 500;
            while(true){
                PageInfo pageInfo = new PageInfo();
                pageInfo.setPageNum(pageNum);
                pageInfo.setPageSize(limit);
                PageInfo<PopBillPo> resultPage = popBillDomainService.queryPopBillList(popBillVo, pageInfo);
                List<OnlinePayPopBillExportVo> onlinePayPopBillExportVoList = PopBillHelper.convetOnlinePayPopBillExportVo(resultPage.getList());
                int pageCount = resultPage.getPages();
                workbook = ExcelExportUtil.exportBigExcel(params, OnlinePayPopBillExportVo.class,onlinePayPopBillExportVoList);
                if(pageNum >= pageCount || pageNum >= 2000){
                    break;
                }
                pageNum++;
            }
            ExcelExportUtil.closeExportBigExcel();
            ServletOutputStream out = res.getOutputStream();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fileName = URLEncoder.encode("账单-", EncodeUtil.DEFAULT_URL_ENCODING) + sdf.format(new Date());	//对中文进行URL编码，防止乱码
            res.setHeader("Content-type", "application/x-xls; charset=" + EncodeUtil.DEFAULT_URL_ENCODING);
            res.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xlsx");
            res.setContentType("application/msexcel");
            if(null != workbook){
                workbook.write(out);
            }
        }catch (Exception e) {
            log.error("运营后台导出在线在线支付账单列表导出异常",e);
        }
    }

    /**
     * 根据帐单号查询账单
     * @return
     */
    @RequestMapping(value = "/billPayment",method = RequestMethod.GET)
    @ResponseBody
    public Object billPayment(String billNo){
        try{
            if(StringUtils.isEmpty(billNo)){
                return ResponseUtils.returnCommonException("账单号不能为空");
            }
            PopBillPo popBillPo = popBillDomainService.selectByBillNo(billNo);
            //合并佣金字段
            if (popBillPo != null) {
                if (popBillPo.getSettlementType() == CommissionSettleTypeEnum.EVERY_MONTH.getCode()) {
                    popBillPo.setHireMoney(popBillPo.getPayableCommission());
                }
            }
            return ResponseUtils.returnObjectSuccess(popBillPo);
        }catch (Exception e){
            log.error("根据入帐单好查询账单异常",e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 查询账单明细，带分页
     * @param billNo
     * @return
     */
    @RequestMapping(value = "/billDetail",method = RequestMethod.GET)
    @ResponseBody
    public Object billDetail(String billNo, PageInfo pageInfo) {
        try{
            if(StringUtils.isEmpty(billNo)){
               return ResponseUtils.returnCommonException("账单号不能为空");
            }
            PageInfo<PopBillDetailPo> popBillDetailPoPageInfo = popBillDomainService.queryPopBillDetail(billNo, pageInfo);
            return ResponseUtils.returnObjectSuccess(popBillDetailPoPageInfo);
        }catch (Exception e){
            log.error("查询账单明细异常");
            return ResponseUtils.returnException(e);
        }

    }

    /**
     * 查询列表账单明细导出的条数  超过5000条数据，前端给出提示
     * @param popBillVo
     * @return
     */
    @RequestMapping(value = "/queryExprotBillDetailCount")
    @ResponseBody
    public Object queryExprotBillDetailCount(PopBillVo popBillVo){
        try{
            popBillVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            Long billDetailCount = popBillDomainService.queryExprotBillDetailCount(popBillVo);
            return ResponseUtils.returnObjectSuccess(billDetailCount);
        }catch (Exception e){
            log.error("查询列表账单导出的条数异常",e);
            return ResponseUtils.returnException(e);
        }
    }

    /**
     * 导出在线支付账单明细
     * @param popBillVo
     * @param request
     * @param res
     */
    @RequestMapping(value = "/exportBillPaymemtDetailList", method = RequestMethod.GET)
    public void exportBillPaymemtDetailList(PopBillVo popBillVo, HttpServletRequest request, HttpServletResponse res){
        try{
            popBillVo.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            popBillDomainService.olinePayExportBillPaymemtDetailList(popBillVo,res);
        }catch (Exception e) {
            log.error("运营后台导出在线支付账单列表导出异常",e);
        }
    }

    /**
     * 确认开票
     * @param popBillVo
     * @return
     */
    @RequestMapping(value = "/confirmationMakeInvoice",method = RequestMethod.POST)
    @ResponseBody
    public Object confirmationMakeInvoice(PopBillVo popBillVo){
        try{
            if(CollectionUtils.isEmpty(popBillVo.getBillNoList()) || null == popBillVo.getInvoiceTime()){
               return ResponseUtils.returnCommonException("参数不能为空");
            }
            List<PopBillPo> popBillPos = popBillService.queryPopBillByBillNoList(popBillVo.getBillNoList());
            if(CollectionUtil.isEmpty(popBillPos)){
                return null;
            }
            boolean flag = popBillPos.stream().anyMatch(p ->p.getInvoiceStatus() == 1);
            if(flag){
                return ResponseUtils.returnCommonException("已开票账单不能重复确认，请检查已勾选的账单开票状态");
            }
            popBillDomainService.updateBatchPopBill(popBillPos,popBillVo);
            return ResponseUtils.returnObjectSuccess(true);
        }catch (Exception e){
            log.error("确认开票异常",e);
            return ResponseUtils.returnException(e);
        }
    }

}
