package com.xyy.ec.pop.controller;

import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.constants.Constants;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.service.SystemService;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.login.SystemMenu;
import com.xyy.ec.pop.vo.zhongtai.PopProvinceVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Objects;

/**
 * 系统主页
 * <AUTHOR>
 */
@ApiIgnore
@Controller
public class IndexController extends BaseController {
	private static final Logger LOGGER = LoggerFactory.getLogger(IndexController.class);

    @Autowired
    private SystemService systemService;
    /**
     * 后台管理系统-主页
     * @return 
     */
    @GetMapping(value={"/index",""})
    public ModelAndView index(){
        return new ModelAndView("index");
    }

    @GetMapping(value = "/header")
    public ModelAndView header(){
        ModelAndView mav = new ModelAndView("header");
        try {
            SysUser sysUser = getUser();
            //中台权限菜单
            List<SystemMenu> list= systemService.getNavigationBar(sysUser);
            //初始化省份
            systemService.initProvinces(sysUser);
            //初始化按钮
            systemService.initButtons(sysUser);
            mav.addObject(Constants.RESOURCE, list);
        } catch (Exception e) {
        	LOGGER.error("头部加载异常",e);
        }
        return mav;
        
    }
    
    @GetMapping(value = "/left")
    public ModelAndView left(){
        ModelAndView mav = new ModelAndView("left");
        try {
            SysUser sysUser = getUser();
            //中台权限菜单
            List<SystemMenu> list= systemService.getNavigationBar(sysUser);
            mav.addObject(Constants.RESOURCE, list);
        } catch (Exception e) {
        	LOGGER.error("左侧加载异常",e);
        }
        return mav;
    }

    /**
     * 商业注册省份
     */
    @RequestMapping(value = "/businessProvinceList",method = RequestMethod.GET)
    @ResponseBody
    public ResponseVo businessProvinceList(){
        try {
            return ResponseVo.successResult(systemService.getProvincialAuthority(getUser()));
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("#CommonController.businessProvinceList#error", e);
            return ResponseVo.errRest("查询商业注册省份失败");
        }
    }

    /**
     * 菜单按钮
     */
    @RequestMapping(value = "/getButtons",method = RequestMethod.GET)
    @ResponseBody
    public ResponseVo getButtons(String menuUrl){
        try {
            if(StringUtils.isBlank(menuUrl)){
                return ResponseVo.errRest("菜单url不能为空");
            }
            return ResponseVo.successResult(systemService.getButtons(getUser(), menuUrl));
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("#CommonController.businessProvinceList#error", e);
            return ResponseVo.errRest("查询菜单按钮失败");
        }
    }
}
