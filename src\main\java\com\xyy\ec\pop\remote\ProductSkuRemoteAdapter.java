package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.client.api.MarketingQueryApi;
import com.xyy.ec.marketing.client.dto.ActBaseDTO;
import com.xyy.ec.marketing.client.dto.param.ActBaseQueryDTO;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.product.api.PopSkuApi;
import com.xyy.ec.pop.server.api.product.api.PopSkuMeProductApi;
import com.xyy.ec.pop.server.api.product.api.PopSkuQualificationApi;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuAdminApi;
import com.xyy.ec.pop.server.api.product.dto.PopSkuAdminBatchUpdateDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuAuditParamDTO;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDetailDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuQualificationDto;
import com.xyy.ec.pop.server.api.product.dto.SkuAdminPageQuery;
import com.xyy.ec.pop.server.api.product.dto.SyncPopSkuInfoDto;
import com.xyy.ec.pop.server.api.product.dto.SystemDictionaryDto;
import com.xyy.ec.pop.vo.EcpSkuActiveVo;
import com.xyy.ec.pop.vo.SyncPopSkuInfoRspVo;
import com.xyy.ec.pop.vo.SyncPopSkuInfoVo;
import com.xyy.ec.product.back.end.ecp.csu.api.CsuForOtherSystemApi;
import com.xyy.ec.product.back.end.ecp.csu.api.EcCsuBackEndApi;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuDTO;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuQuery;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.result.EcSkuInstructionDto;
import com.xyy.me.product.common.util.BeanMapper;
import com.xyy.me.product.common.util.JsonMapper;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @version v1
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductSkuRemoteAdapter {
    @Reference
    private PopSkuApi popSkuApi;
    @Reference
    private PopSkuQualificationApi popSkuQualificationApi;
    @Reference
    private PopSkuAdminApi popSkuAdminApi;
    //5分钟超时，如果要拷贝的商品比较多，时间会比较长
    @Reference(timeout = 300000)
    private PopSkuMeProductApi popSkuMeProductApi;
    @Reference
    private CsuForOtherSystemApi csuForOtherSystemApi;
    @Reference
    private MarketingQueryApi marketingQueryApi;
    @Value("${request.productSku.csuIds.num.max:200}")
    private Integer csuIdsNumMax;
    @Reference
    private EcCsuBackEndApi ecCsuBackEndApi;

    /**
     * 同步停用信息(上线后刷存量数据使用)
     *
     * @param syncPopSkuInfoVo 停用信息
     * @return 失败返回原纪录+异常原因,成功返回null
     */
    public SyncPopSkuInfoRspVo syncPopSkuInfo(SyncPopSkuInfoVo syncPopSkuInfoVo) {
        log.info("同步商品信息,入参:{}", JsonMapper.toJson(syncPopSkuInfoVo));
        try {
            SyncPopSkuInfoDto syncPopSkuInfoDto = BeanMapper.map(syncPopSkuInfoVo, SyncPopSkuInfoDto.class);
            popSkuApi.syncPopSkuInfo(syncPopSkuInfoDto);
        } catch (Exception e) {
            log.info("同步商品信息,同步异常,异常原因:{}", e.getMessage(), e);
            SyncPopSkuInfoRspVo syncPopSkuInfoRspVo = BeanMapper.map(syncPopSkuInfoVo, SyncPopSkuInfoRspVo.class);
            syncPopSkuInfoRspVo.setErrorMsg(e.getMessage());
            return syncPopSkuInfoRspVo;
        }
        return null;
    }

    public PopSkuDto getSku(Long skuId) {
        try {
            log.info("ProductSkuRemoteAdapter.getSku#skuId:{}", skuId);
            ApiRPCResult<PopSkuDto> result = popSkuApi.getSku(skuId);
            log.info("ProductSkuRemoteAdapter.getSku#skuId:{} return:{}", skuId, JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            return null;
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.getSku#skuId:{} 异常", skuId, e);
            return null;
        }
    }

    public Map<Long, PopSkuDto> getPopSkuByCsuidList(List<Long> csuIds) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("ProductSkuRemoteAdapter.getPopSkuByCsuidList#csuIds:{}", csuIds);
            }
            Map<Long, PopSkuDto> retMap = Maps.newHashMap();
            List<List<Long>> csuIdsList = Lists.partition(csuIds, 200);
            for (List<Long> csuIdList : csuIdsList) {
                ApiRPCResult<List<PopSkuDto>> result = popSkuApi.findByCsuIds(csuIdList);
                if (!result.isSuccess()) {
                    log.error("ProductSkuRemoteAdapter.getPopSkuByCsuidList#csuIds:{} return:{}", csuIds, JSON.toJSONString(result));
                    continue;
                }
                List<PopSkuDto> skuDtoList = result.getData();
                if (CollectionUtils.isEmpty(skuDtoList)) {
                    continue;
                }
                Map<Long, PopSkuDto> skuMap = skuDtoList.stream().collect(Collectors.toMap(PopSkuDto::getCsuid, Function.identity(), (o1, o2) -> o1));
                retMap.putAll(skuMap);
            }
            if (log.isDebugEnabled()) {
                log.debug("ProductSkuRemoteAdapter.getPopSkuByCsuidList#csuIds:{} return:{}", csuIds, JSON.toJSONString(retMap));
            }
            return retMap;
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.getSku#csuIds:{} 异常", csuIds, e);
            return null;
        }
    }

    public List<PopSkuQualificationDto> selectByBarcode(String barcode) {
        try {
            log.info("popSkuQualificationApi.selectByBarcode#barcode:{}", barcode);
            ApiRPCResult<List<PopSkuQualificationDto>> result = popSkuQualificationApi.selectByBarcode(barcode);
            log.info("popSkuQualificationApi.selectByBarcode#barcode:{} return:{}", barcode, JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("popSkuQualificationApi.selectByBarcode#barcode:{} 异常", barcode, e);
            return new ArrayList<>();
        }
    }


    public void updateStandardIdAndAuthReason(PopSkuDto dto) {
        try {
            log.info("ProductSkuRemoteAdapter.updateStandardIdAndAuthReason#dto:{}", JSON.toJSONString(dto));
            ApiRPCResult<Boolean> result = popSkuApi.updateStandardIdAndAuthReason(dto);
            log.info("ProductSkuRemoteAdapter.updateStandardIdAndAuthReason#dto:{} return {}", JSON.toJSONString(dto), JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.updateStandardIdAndAuthReason#dto:{} 异常", JSON.toJSONString(dto), e);
        }
    }

    public Map<Long, List<Long>> protectedAreaSkus(Long current, int maxBatch) {
        try {
            log.info("ProductSkuRemoteAdapter.protectedAreaSkus#current:{},maxBatch:{}", current, maxBatch);
            ApiRPCResult<Map<Long, List<Long>>> result = popSkuApi.protectedAreaSkus(current, maxBatch);
            log.info("ProductSkuRemoteAdapter.protectedAreaSkus#current:{},maxBatch:{}", current, maxBatch);
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.protectedAreaSkus#current:{},maxBatch:{} 异常", current, maxBatch, e);
            return null;
        }
    }

    public boolean unShelf(List<Long> skuIds) {
        try {
            log.info("ProductSkuRemoteAdapter.unShelf#skuIds:{}", JSON.toJSONString(skuIds));
            ApiRPCResult<Boolean> result = popSkuApi.unShelf(skuIds);
            log.info("ProductSkuRemoteAdapter.unShelf#skuIds:{} return {}", JSON.toJSONString(skuIds), JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : false;
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.unShelf#skuIds:{} 异常", JSON.toJSONString(skuIds), e);
            return false;
        }
    }

    public List<PopSkuDto> querySyncSku(Integer cId, int saleType, Long current, int maxBatch) {
        try {
            log.info("ProductSkuRemoteAdapter.querySyncSku#cId:{},code:{},current:{},maxBatch:{}", cId, saleType, current, maxBatch);
            ApiRPCResult<List<PopSkuDto>> result = popSkuApi.querySyncSku(cId, saleType, current, maxBatch);
            log.info("ProductSkuRemoteAdapter.querySyncSku#cId:{},code:{},current:{},maxBatch:{} return {}", cId, saleType, current, maxBatch, JSON.toJSONString(result));
            return result.isFail() ? null : result.getData();
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.querySyncSku#cId:{},code:{},current:{},maxBatch:{} 异常", cId, saleType, current, maxBatch, e);
            return null;
        }
    }

    public boolean batchDeleteArea(String userName, List<String> branchs, List<String> barcodes) {
        try {
            log.info("ProductSkuRemoteAdapter.batchDeleteArea#userName:{},branchs:{},barcodes:{},maxBatch:{}", userName, JSON.toJSONString(branchs), JSON.toJSONString(barcodes));
            ApiRPCResult<Boolean> result = popSkuApi.batchDeleteArea(userName, branchs, barcodes);
            log.info("ProductSkuRemoteAdapter.batchDeleteArea#userName:{},branchs:{},barcodes:{},maxBatch:{} return {}", userName, JSON.toJSONString(branchs), JSON.toJSONString(barcodes), JSON.toJSONString(result));
            return result.isFail() ? false : result.getData();
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.batchDeleteArea#userName:{},branchs:{},barcodes:{},maxBatch:{} 异常", userName, JSON.toJSONString(branchs), JSON.toJSONString(barcodes), e);
            return false;
        }
    }


    public PopSkuDetailDto getDetailByBarcode(String orgId, String barcode) {
        try {
            log.info("ProductSkuRemoteAdapter.getDetailByBarcode#orgId:{},barcode:{}", orgId, barcode);
            ApiRPCResult<PopSkuDetailDto> result = popSkuApi.getSkuDetail(orgId, barcode);
            log.info("ProductSkuRemoteAdapter.getDetailByBarcode#orgId:{},barcode:{} return {}", orgId, barcode, JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.getDetailByBarcode#orgId:{},barcode:{} 异常", orgId, barcode, e);
            return null;
        }

    }

    public PopSkuDetailDto getDetailByBarcode(String barcode) {
        try {
            log.info("ProductSkuRemoteAdapter.getDetailByBarcode#barcode:{}", barcode);
            ApiRPCResult<PopSkuDetailDto> result = popSkuApi.getDetailByBarcode(barcode);
            log.info("ProductSkuRemoteAdapter.getDetailByBarcode#barcode:{} return {}", barcode, JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.getDetailByBarcode#barcode:{} 异常", barcode, e);
            return null;
        }

    }

    public PopSkuDto getSkuByBarcode(String barcode) {
        try {
            log.info("ProductSkuRemoteAdapter.getSkuByBarcode#barcode", barcode);
            ApiRPCResult<List<PopSkuDto>> result = popSkuApi.findByBarCode(Arrays.asList(barcode));
            log.info("ProductSkuRemoteAdapter.getSkuByBarcode#barcode return {}", barcode, JSON.toJSONString(result));
            if (result.isSuccess() && result.getData().size() == 1) {
                return result.getData().get(0);
            }
            return null;
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.getSkuByBarcode#barcode 异常", barcode, e);
            return null;
        }

    }

    public PopSkuDetailDto getSkuDetailByBarcodeWithArchive(String barcode) {
        try {
            log.info("ProductSkuRemoteAdapter.getSkuDetailByBarcode#barcode", barcode);
            ApiRPCResult<PopSkuDetailDto> result = popSkuApi.getSkuDetailByBarcodeWithArchive(null, barcode);
            log.info("ProductSkuRemoteAdapter.getSkuDetailByBarcode#barcode return {}", barcode, JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            return null;
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.getSkuDetailByBarcode#barcode 异常", barcode, e);
            return null;
        }

    }

    public PageInfo<PopSkuDetailDto> adminQuery(SkuAdminPageQuery pageQuery) {
        try {
            log.info("ProductSkuRemoteAdapter.adminQuery#pageQuery:{}", JSON.toJSONString(pageQuery));
            ApiRPCResult<PageInfo<PopSkuDetailDto>> result = popSkuApi.adminQuery(pageQuery);
            log.info("ProductSkuRemoteAdapter.adminQuery#pageQuery:{} return {}", JSON.toJSONString(pageQuery), JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : new PageInfo(new ArrayList());
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.adminQuery#pageQuery:{} 异常", JSON.toJSONString(pageQuery), e);
            return new PageInfo(new ArrayList());
        }
    }

    /**
     * 批量更新商品信息
     *
     * @param user    更新人
     * @param skus    商品信息
     * @param fileUrl 原始文件地址
     * @return
     */
    public List<String> batchUpdate(String user, List<PopSkuAdminBatchUpdateDto> skus, String fileUrl) throws ServiceException {
        try {
            log.info("ProductSkuRemoteAdapter.batchUpdate#user:{},skus:{},fileUrl:{}", user, JSON.toJSONString(skus), fileUrl);
            ApiRPCResult<List<String>> result = popSkuAdminApi.batchUpdateForAdmin(user, skus, fileUrl);
            log.info("ProductSkuRemoteAdapter.batchUpdate#user:{},skus:{},fileUrl:{} return {}", user, JSON.toJSONString(skus), fileUrl, JSON.toJSONString(result));
            if (result.isFail()) {
                throw new ServiceException("批量更新商品信息失败");
            }
            return result.getData();
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.batchUpdate#user:{},skus:{},fileUrl:{} 异常", user, JSON.toJSONString(skus), fileUrl, e);
            throw new ServiceException("批量更新商品信息失败");
        }
    }

    public boolean auditSku(PopSkuAuditParamDTO popSkuAuditParamDTO) {
        try {
            log.info("ProductSkuRemoteAdapter.auditSku#popSkuAuditParamDTO:{}", JSON.toJSONString(popSkuAuditParamDTO));
            ApiRPCResult result = popSkuAdminApi.auditSku(popSkuAuditParamDTO);
            log.info("ProductSkuRemoteAdapter.auditSku#popSkuAuditParamDTO:{} return {}", JSON.toJSONString(popSkuAuditParamDTO), JSON.toJSONString(result));
            return result.isSuccess();
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.auditSku#popSkuAuditParamDTO:{} 异常", JSON.toJSONString(popSkuAuditParamDTO), e);
            return false;
        }

    }

    public int synProductFromMe(String userName, List<String> barcodes, List<String> standardIds, List<String> fields) throws ServiceException {
        try {
            log.info("ProductSkuRemoteAdapter.synProductFromMe#userName:{},barcodes:{},standardIds:{},fields:{}", JSON.toJSONString(userName), JSON.toJSONString(barcodes), JSON.toJSONString(standardIds), JSON.toJSONString(fields));
            ApiRPCResult<Integer> result = popSkuMeProductApi.synProductFromMe(userName, barcodes, standardIds, fields);
            log.info("ProductSkuRemoteAdapter.synProductFromMe#userName:{},barcodes:{},standardIds:{},fields:{} return {}", JSON.toJSONString(userName), JSON.toJSONString(barcodes), JSON.toJSONString(standardIds), JSON.toJSONString(fields), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            throw new ServiceException(result.getErrMsg());
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.synProductFromMe#userName:{},barcodes:{},standardIds:{},fields:{} 异常", JSON.toJSONString(userName), JSON.toJSONString(barcodes), JSON.toJSONString(standardIds), JSON.toJSONString(fields), e);
            throw new ServiceException(e.getMessage());
        }

    }


    /**
     * 根据barCode查询商品
     *
     * @param barCodes
     * @param csuIds
     * @param corporationDto
     * @return
     */
    public List<CsuDTO> findProductByBarCodes(List<String> barCodes, List<Long> csuIds, CorporationDto corporationDto) throws ServiceException {
        try {
            log.info("ProductSkuRemoteAdapter.findProductByBarCode barCode:{}", JSON.toJSONString(barCodes));

            List<CsuDTO> csuDtos = new ArrayList<>();
            List<List<String>> lists = Lists.partition(barCodes, 200);
            for (List<String> list : lists) {
                CsuQuery csuQuery = new CsuQuery();
//                csuQuery.setBarcodes(list);
                csuQuery.setSkuIdList(csuIds);
                csuQuery.setIsThirdCompany(1);
                if (StringUtils.isBlank(corporationDto.getProductOwnerId())) {
                    csuQuery.setProductOwnerId("0");
                } else {
                    csuQuery.setProductOwnerId(corporationDto.getProductOwnerId());
                }
                csuQuery.setOffset(1);
                csuQuery.setLimit(list.size());
                PageInfo<CsuDTO> pageInfo = csuForOtherSystemApi.queryPageCsuList(csuQuery);
                log.info("ProductSkuRemoteAdapter.findProductByBarCode csuQuery:{} result:{}", JSON.toJSONString(csuQuery), JSON.toJSONString(pageInfo));
                if (pageInfo == null) {
                    throw new ServiceException("ProductSkuRemoteAdapter.findProductByBarCode.queryPageCsuList调用异常");
                }
                if (CollectionUtils.isEmpty(pageInfo.getList())) {
                    continue;
                }
                csuDtos.addAll(pageInfo.getList());
            }
            return csuDtos;
        } catch (Exception e) {
            log.error("ProductSkuRemote.findProductByBarCodes 异常", e);
            throw new XyyEcPopException("查询商品参加异常");
        }
    }


    public List<EcpSkuActiveVo> getActivityCsuIds(List<Long> csuIds) {
        List<ActBaseDTO> actBaseDTOS = new ArrayList<>();
        List<List<Long>> lists = Lists.partition(csuIds, csuIdsNumMax);
        for (List<Long> list : lists) {
            ActBaseQueryDTO queryDTO = new ActBaseQueryDTO();
            queryDTO.setCsuIds(list);
            log.info("ProductSkuRemote.findProductActivity queryDTO:{}", JSON.toJSONString(queryDTO));
            ApiRPCResult<List<ActBaseDTO>> rpcResult = marketingQueryApi.queryActBaseForBack(queryDTO);
            log.info("ProductSkuRemote.findProductActivity result:{}", JSON.toJSONString(rpcResult));
            //参加拼团或者特价获得的sku
            if (rpcResult == null || rpcResult.isFail()) {
                throw new XyyEcPopException("查询商品参加拼团异常");
            }

            if (CollectionUtils.isEmpty(rpcResult.getData())) {
                continue;
            }
            actBaseDTOS.addAll(rpcResult.getData());
        }
        if (CollectionUtils.isEmpty(actBaseDTOS)) {
            return new ArrayList<>();
        }
        List<EcpSkuActiveVo> result = new ArrayList<>(csuIds.size());
        for (ActBaseDTO dto : actBaseDTOS) {
            if (!filterbyActivity(dto)) {
                continue;
            }
            List<EcpSkuActiveVo> acts = dto.getCsuIds().stream().map(id -> new EcpSkuActiveVo(id, dto.getActivityType())).collect(Collectors.toList());
            result.addAll(acts);
        }
        return result;
    }

    //过滤特价和拼团活动，而且在指定时间
    public Boolean filterbyActivity(ActBaseDTO dto) {
        //拼团、特价
        boolean boolActivity = dto.getActivityType().equals(MarketingEnum.PING_TUAN) || dto.getActivityType().equals(MarketingEnum.TE_JIA);
        if (boolActivity) {
            return true;
        }
        return false;
    }

    public List<PopSkuDto> findSkuByBarCodes(List<String> barcodes) throws ServiceException {
        try {
            log.info("ProductSkuRemoteAdapter.findSkuByBarCodes#barcodes:{}", JSON.toJSONString(barcodes));
            ApiRPCResult<List<PopSkuDto>> result = popSkuApi.findByBarCode(barcodes);
            log.info("ProductSkuRemoteAdapter.findSkuByBarCodes#barcodes:{} return {}", JSON.toJSONString(barcodes), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            throw new ServiceException(result.getErrMsg());
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.findSkuByBarCodes#barcodes:{} 异常", JSON.toJSONString(barcodes), e);
            throw e instanceof ServiceException ? (ServiceException) e : new ServiceException("查询商品信息失败");
        }
    }

    public List<Long> findGroupSkuCsuIdsByOriginalBarcode(String barcode) {
        log.info("ProductSkuRemoteAdapter.findGroupSkuCsuIdsByOriginalBarcode#barcode:{}", barcode);
        ApiRPCResult<List<Long>> result = popSkuApi.findGroupSkuCsuIdsByOriginalBarcode(barcode);
        log.info("ProductSkuRemoteAdapter.findGroupSkuCsuIdsByOriginalBarcode#barcode:{} return {}", barcode, JSON.toJSONString(result));
        if (result.isSuccess()) {
            return result.getData();
        }
        throw new ServiceRuntimeException(result.getErrMsg());
    }

    public Map<String, Integer> getErpCodeCountMap(List<String> erpCodes) {
        try {
            log.info("ProductSkuRemoteAdapter.getErpCodeCountMap#erpCodes:{}", erpCodes);
            ApiRPCResult<Map<String, Integer>> result = popSkuApi.getErpCodeCountMap(erpCodes);
            log.info("ProductSkuRemoteAdapter.getErpCodeCountMap#erpCodes:{} result {}", erpCodes, JSON.toJSONString(result));
            if (result == null || result.isFail()) {
                return Maps.newHashMap();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.getErpCodeCountMap#error.erpCodes:{}", erpCodes, e);
            return Maps.newHashMap();
        }
    }

    public List<SystemDictionaryDto> selectAllSystemDictionaryDto() {
        try {
            ApiRPCResult<List<SystemDictionaryDto>> apiRPCResult = popSkuApi.selectAllSystemDictionaryDto();
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return new ArrayList<>();
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.selectAllSystemDictionaryDto:{}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询自营商品信息列表
     *
     * @param skuIds
     * @return
     */
    public List<CsuDTO> findECProductBySkuIds(List<Long> skuIds) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("ProductSkuRemoteAdapter.findECProductBySkuIds skuIds:{}", JSON.toJSONString(skuIds));
            }
            List<CsuDTO> csuDtos = new ArrayList<>();
            List<List<Long>> lists = Lists.partition(skuIds, 200);
            for (List<Long> list : lists) {
                CsuQuery csuQuery = new CsuQuery();
                csuQuery.setSkuIdList(list);
                csuQuery.setLimit(9999);
                csuQuery.setIsThirdCompany(0);
                csuQuery.setOffset(0);
                csuQuery.setIsNeedSaleArea(false);
                PageInfo<CsuDTO> pageInfo = csuForOtherSystemApi.queryPageCsuList(csuQuery);
                if (log.isDebugEnabled()) {
                    log.debug("ProductSkuRemoteAdapter.findECProductBySkuIds csuQuery:{} result:{}", JSON.toJSONString(csuQuery), JSON.toJSONString(pageInfo));
                }
                if (pageInfo == null) {
                    throw new ServiceException("ProductSkuRemoteAdapter.findECProductBySkuIds调用异常");
                }
                if (CollectionUtils.isEmpty(pageInfo.getList())) {
                    continue;
                }
                csuDtos.addAll(pageInfo.getList());
            }
            return csuDtos;
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.findECProductBySkuIds 异常, skuIds :{}, e:{}", JSON.toJSONString(skuIds), e);
            return Lists.newArrayList();
        }
    }

    public List<EcSkuInstructionDto> getEcInstructionList(List<Long> skuIds) {
        if (log.isDebugEnabled()) {
            log.debug("ProductSkuRemoteAdapter.getEcInstructionList skuIds:{}", JSON.toJSONString(skuIds));
        }
        List<EcSkuInstructionDto> retList = Lists.newArrayList();
        try {
            ApiRPCResult<List<EcSkuInstructionDto>> instructionResult = ecCsuBackEndApi.getInstructionExtendList(skuIds);
            if (log.isDebugEnabled()) {
                log.debug("ProductSkuRemoteAdapter.getEcInstructionList skuIds:{} instructionResult:{}", JSON.toJSONString(skuIds), JSON.toJSONString(instructionResult));
            }
            if (instructionResult.isSuccess()) {
                retList.addAll(instructionResult.getData());
            }
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.getEcInstructionList 异常, skuIds :{}, e:{}", JSON.toJSONString(skuIds), e);
            return Lists.newArrayList();
        }
        return retList;
    }

    public List<String> queryOriginBarcodes(List<String> barcodes) {
        if (CollectionUtils.isEmpty(barcodes)) {
            return Lists.newArrayList();
        }
        try {
            ApiRPCResult<List<String>> listApiRPCResult = popSkuApi.queryOriginBarcodes(barcodes);
            if (listApiRPCResult == null || listApiRPCResult.isFail()) {
                return Lists.newArrayList();
            }
            return listApiRPCResult.getData();
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.queryOriginBarcodes 异常, barcodes :{}, e", JSON.toJSONString(barcodes), e);
            return Lists.newArrayList();
        }
    }
}
