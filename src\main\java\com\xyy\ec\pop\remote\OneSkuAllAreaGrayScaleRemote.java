package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.api.OneSkuAllAreaGrayScaleApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/19
 */
@Service
@Slf4j
public class OneSkuAllAreaGrayScaleRemote {
    @Reference
    private OneSkuAllAreaGrayScaleApi oneSkuAllAreaGrayScaleApi;

    public Boolean inGray(String orgId) throws ServiceException {
        try {
            log.info("OneSkuAllAreaGrayScaleRemote.inGray#orgId:{}", orgId);
            ApiRPCResult<Boolean> result = oneSkuAllAreaGrayScaleApi.inGray(orgId);
            log.info("OneSkuAllAreaGrayScaleRemote.inGray#orgId:{} return {}", orgId, JSON.toJSONString(result));
            if(result.isSuccess()){
                return result.getData();
            }
            throw new ServiceException("查询灰度状态异常");
        } catch (Exception e) {
            log.error("OneSkuAllAreaGrayScaleRemote.inGray#orgId:{} 异常", orgId, e);
            throw new ServiceException("查询灰度状态异常");
        }
    }
}
