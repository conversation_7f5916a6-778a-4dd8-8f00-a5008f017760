package com.xyy.ec.pop.helper;

import com.google.common.collect.Lists;
import com.xyy.ec.label.server.business.dto.EcpLabelDTO;
import com.xyy.ec.pop.marketing.dto.InsightLabelDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * {@link InsightLabelDTO} 帮助类
 *
 * <AUTHOR>
 */
public class InsightLabelDTOHelper {

    /**
     * 创建。
     * 将{@link EcpLabelDTO} 转换为 {@link InsightLabelDTO}
     *
     * @param ecpLabelDTO
     * @return
     */
    public static InsightLabelDTO create(EcpLabelDTO ecpLabelDTO) {
        if (ecpLabelDTO == null) {
            return null;
        }
        InsightLabelDTO insightLabelDTO = InsightLabelDTO.builder()
                .id(ecpLabelDTO.getId())
                .name(ecpLabelDTO.getLabelName())
                .build();
        return insightLabelDTO;
    }

    /**
     * 创建。
     * 将{@link EcpLabelDTO}集合 转换为 {@link InsightLabelDTO}集合
     *
     * @param ecpLabelDTOS
     * @return
     */
    public static List<InsightLabelDTO> creates(List<EcpLabelDTO> ecpLabelDTOS) {
        if (CollectionUtils.isEmpty(ecpLabelDTOS)) {
            return Lists.newArrayList();
        }
        return ecpLabelDTOS.stream().filter(Objects::nonNull)
                .map(item -> InsightLabelDTOHelper.create(item))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }
}
