package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.BillSettleRemote;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.export.param.BillExportAdminParam;
import com.xyy.ec.pop.server.api.export.param.BillSettleDetailExportAdminParam;
import com.xyy.ec.pop.server.api.export.param.BillSettleExportAdminParam;
import com.xyy.ec.pop.server.api.order.enums.OrderPayTypeEnums;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/07/28
 */
@Slf4j
@RequestMapping("/billSettleExport/async")
@RestController
public class BillSettleExportController extends BaseController {
    @Autowired
    private DownloadRemote downloadRemote;

    @Resource
    private BillSettleRemote billSettleRemote;

    /**
     * 导出线下转账账单数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportOfflineBill")
    @AvoidRepeatableCommit(timeout = 3)
    @Deprecated
    public ResponseVo<Boolean> exportOfflineBill(BillExportAdminParam queryDto) {
        log.info("BillExportController.exportOfflineBill#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(provIds);
            queryDto.setPayTypes(OrderPayTypeEnums.getOfflineTypes(queryDto.getPayType()));
            queryDto.setPayType(null);
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.BILL_OFFLINE)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("BillExportController.exportOfflineBill#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        }catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillExportController.exportOfflineBill#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出线下转账账单明细数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportOfflineBillDetail")
    @AvoidRepeatableCommit(timeout = 3)
    @Deprecated
    public ResponseVo<Boolean> exportOfflineBillDetail(BillExportAdminParam queryDto) {
        log.info("BillExportController.exportOfflineBillDetail#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(provIds);
            queryDto.setPayTypes(OrderPayTypeEnums.getOfflineTypes(queryDto.getPayType()));
            queryDto.setPayType(null);
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.BILL_OFFLINE_DETAIL)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("BillExportController.exportOfflineBillDetail#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("BillExportController.exportOfflineBillDetail#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出在线支付账单数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportOnlineBill")
    @AvoidRepeatableCommit(timeout = 3)
    @Deprecated
    public ResponseVo<Boolean> exportOnlineBill(BillExportAdminParam queryDto) {
        log.info("BillExportController.exportOnlineBill#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(getProvIds(queryDto.getProvId()));
            queryDto.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.BILL_ONLINE)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("BillExportController.exportOnlineBill#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        }catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillExportController.exportOnlineBill#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出在线支付账单明细数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportOnlineBillDetail")
    @AvoidRepeatableCommit(timeout = 3)
    @Deprecated
    public ResponseVo<Boolean> exportOnlineBillDetail(BillExportAdminParam queryDto) {
        log.info("BillExportController.exportOnlineBillDetail#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(getProvIds(queryDto.getProvId()));
            queryDto.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.BILL_ONLINE_DETAIL)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("BillExportController.exportOnlineBillDetail#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("BillExportController.exportOnlineBillDetail#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出线下转账结算单数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportOfflineBillSettle")
    @AvoidRepeatableCommit(timeout = 3)
    @Deprecated
    public ResponseVo<Boolean> exportOfflineBillSettle(BillSettleExportAdminParam queryDto) {
        log.info("BillExportController.exportOfflineBillSettle#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(provIds);
            queryDto.setPayTypes(OrderPayTypeEnums.getOfflineTypes(queryDto.getPayType()));
            queryDto.setPayType(null);
//            queryDto.setOrderSettlementStatus((byte) OrderSettleStatusEnum.DONE.getStatus());  //只查询已结算
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.OFFLINE_SETTLEMENT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("BillExportController.exportOfflineBillSettle#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        }catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillExportController.exportOfflineBillSettle#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出在线支付结算单数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportOnlineBillSettle")
    @AvoidRepeatableCommit(timeout = 3)
    @Deprecated
    public ResponseVo<Boolean> exportOnlineBillSettle(BillSettleExportAdminParam queryDto) {
        log.info("BillExportController.exportOnlineBillSettle#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(provIds);
            queryDto.setPayType(Integer.valueOf(OrderPayTypeEnums.ONLINE.getType()).byteValue());
//            queryDto.setOrderSettlementStatus((byte) OrderSettleStatusEnum.DONE.getStatus());  //只查询已结算
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.ONLINE_SETTLEMENT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("BillExportController.exportOnlineBillSettle#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("BillExportController.exportOnlineBillSettle#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出结算单数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportBillSettle")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportBillSettle(BillSettleExportAdminParam queryDto) {
        log.info("BillExportController.exportBillSettle#queryDto:{}", JSON.toJSONString(queryDto));
        try {
//            List<Long> provIds = getProvIds(queryDto.getProvId());
//            if (CollectionUtils.isEmpty(provIds)) {
//                return ResponseVo.errRest("导出失败，暂无导出数据");
//            }
//            queryDto.setProvIds(provIds);
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.SETTLEMENT)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("BillExportController.exportBillSettle#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e) {
            log.error("BillExportController.exportBillSettle#queryDto:{} 自定义异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillExportController.exportBillSettle#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 导出结算单明细数据
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/exportBillSettleDetail")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportBillSettleDetail(BillSettleDetailExportAdminParam queryDto) {
        log.info("BillExportController.exportBillSettle#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if (CollectionUtils.isEmpty(provIds)) {
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(provIds);
            String errorMsg = billSettleRemote.exportBillSettleAdminDetail(queryDto, getUser().getEmail());
            if (StringUtils.isNotEmpty(errorMsg)) {
                return ResponseVo.errRest(errorMsg);
            } else {
                return ResponseVo.successResult(true);
            }
        } catch (Exception e) {
            log.error("BillExportController.exportBillSettle#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请稍后重试");
        }
    }

}
