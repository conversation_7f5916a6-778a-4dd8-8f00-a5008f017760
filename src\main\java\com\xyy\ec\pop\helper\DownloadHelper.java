package com.xyy.ec.pop.helper;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.pop.server.api.Enum.ExportStatusEnum;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileDto;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.vo.DownloadFileToolVo;
import com.xyy.ec.pop.vo.DownloadFileVo;
import com.xyy.ms.promotion.business.common.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/06/16
 * @table
 */
public class DownloadHelper {

    public static PageInfo<DownloadFileVo> buildVoPageInfo(PageInfo<DownloadFileDto> dtoPageInfo) {
        if (dtoPageInfo == null) {
            return new PageInfo();
        }
        PageInfo<DownloadFileVo> voPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(dtoPageInfo, voPageInfo);
        voPageInfo.setList(dtos2Vos(dtoPageInfo.getList()));
        return voPageInfo;
    }

    private static List<DownloadFileVo> dtos2Vos(List<DownloadFileDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Lists.newArrayList();
        }
        return dtos.stream().map(item -> dto2Vo(item)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static DownloadFileVo dto2Vo(DownloadFileDto dto) {
        if (dto == null) {
            return null;
        }
        DownloadFileVo downloadFileVo = new DownloadFileVo();
        downloadFileVo.setId(dto.getId());
        downloadFileVo.setDownloadFileName(dto.getDownloadFileName());
        downloadFileVo.setDownloadSpeed(dto.getDownloadSpeed());
        downloadFileVo.setStatus(dto.getStatus());
        downloadFileVo.setStatusDesc(ExportStatusEnum.getNameByCode(dto.getStatus()));
        downloadFileVo.setFileUrl(dto.getFileUrl());
        downloadFileVo.setFailReason(dto.getFailReason());
        downloadFileVo.setCreateTime(dto.getCreateTime());
        downloadFileVo.setCreateBy(dto.getCreateBy());
        downloadFileVo.setOrgId(dto.getOrgId());
        return downloadFileVo;
    }

    public static PageInfo<DownloadFileToolVo> buildToolVoPageInfo(PageInfo<DownloadFileDto> dtoPageInfo) {
        if (dtoPageInfo == null) {
            return new PageInfo();
        }
        PageInfo<DownloadFileToolVo> voPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(dtoPageInfo, voPageInfo);
        List<DownloadTaskBusinessTypeEnum> enums = Arrays.asList(DownloadTaskBusinessTypeEnum.values());
        Map<Integer, String> collect = enums.stream().collect(Collectors.toMap(DownloadTaskBusinessTypeEnum::getType, DownloadTaskBusinessTypeEnum::getDesc, (k1, k2) -> k1));
        List<DownloadFileToolVo> collect1 = dtoPageInfo.getList().stream().map(m -> dtos2VoTool(m, collect)).collect(Collectors.toList());
        voPageInfo.setList(collect1);
        return voPageInfo;
    }

    public static DownloadFileToolVo dtos2VoTool(DownloadFileDto dto, Map<Integer, String> collect) {
        if (dto == null) {
            return null;
        }
        DownloadFileToolVo downloadFileToolVo = new DownloadFileToolVo();
        downloadFileToolVo.setId(dto.getId());
        downloadFileToolVo.setOrgId(StringUtils.isBlank(dto.getOrgId())?"":dto.getOrgId());
        downloadFileToolVo.setName(dto.getDownloadFileName());
        downloadFileToolVo.setSys(dto.getSysType() == 1 ? DownloadTaskSysTypeEnum.SELLER.getDesc() : DownloadTaskSysTypeEnum.ADMIN.getDesc());
        downloadFileToolVo.setBusiness(collect.get(dto.getBusinessType()));
        downloadFileToolVo.setStatus(dto.getStatus().intValue());
        downloadFileToolVo.setReason(StringUtils.isBlank(dto.getFailReason())?"":dto.getFailReason());
        downloadFileToolVo.setCreateTime(DateUtils.date2String(dto.getCreateTime(),null));
        downloadFileToolVo.setUser(dto.getCreateBy());
        downloadFileToolVo.setStatusDesc(getStatusMap().get(dto.getStatus().intValue()));
        return downloadFileToolVo;
    }

    private static Map<Integer, String> getStatusMap(){
        Map<Integer, String> statusMap = Maps.newHashMap();
        statusMap.put(1,"新建");
        statusMap.put(2,"生成中");
        statusMap.put(3,"完成");
        statusMap.put(4,"失败");
        return statusMap;
    }
}
