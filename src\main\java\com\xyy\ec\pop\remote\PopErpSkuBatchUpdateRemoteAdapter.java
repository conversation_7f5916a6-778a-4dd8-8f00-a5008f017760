package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.PopErpSkuBatchUpdateConvertHelper;
import com.xyy.ec.pop.server.api.erp.api.PopErpSkuAdminApi;
import com.xyy.ec.pop.server.api.erp.dto.PopErpSkuAdminBatchUpdateDto;
import com.xyy.ec.pop.vo.ErpSkuBatchUpdateVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @project_name: xyy-ec-pop-service
 * @Auther: Jincheng.Li
 * @Date: 2021/04/15/10:12
 * @Description:
 */
@Slf4j
@Service
public class PopErpSkuBatchUpdateRemoteAdapter {

    @Reference
    private PopErpSkuAdminApi popErpSkuAdminApi;
    /**
     * 批量处理erp商品信息
     * @param user 更新人
     * @param collect erp商品信息
     * @param fileUrl 文件路径
     * @return
     */
    public List<ErpSkuBatchUpdateVo> batchUpdateErpSku(String user, List<PopErpSkuAdminBatchUpdateDto> collect, String fileUrl) throws ServiceException {
        try {
            log.info("ProductSkuRemoteAdapter.batchUpdateErpSku#user:{},erpSkus:{},fileUrl:{}", user, JSON.toJSONString(collect), fileUrl);
            ApiRPCResult<List<PopErpSkuAdminBatchUpdateDto>> result = popErpSkuAdminApi.batchUpdateErpSkuForAdmin(user,collect,fileUrl);
            log.info("ProductSkuRemoteAdapter.batchUpdateErpSku#user:{},erpSkus:{},fileUrl:{} return {}",user, JSON.toJSONString(collect), fileUrl, JSON.toJSONString(result));
            if(result.isFail()){
                throw new ServiceException("批量更新Erp商品信息失败");
            }
            return result.getData().stream().map(PopErpSkuBatchUpdateConvertHelper::covertErpSkuAdminBatchDtoToDto).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("ProductSkuRemoteAdapter.batchUpdateErpSku#user:{},erpSkus:{},fileUrl:{} 异常", user, JSON.toJSONString(collect), fileUrl, e);
            throw new ServiceException("批量更新Erp商品信息失败");
        }
    }
}
