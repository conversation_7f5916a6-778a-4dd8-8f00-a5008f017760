package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.model.SkuOperationLog;
import com.xyy.ec.pop.server.api.product.dto.PopSkuOperationLogDto;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/28
 */
public class PopSkuOperationLogHelper {
    public static List<SkuOperationLog> convertToVo(List<PopSkuOperationLogDto> list) {
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>(0);
        }
        return list.stream().map(item->convertToVo(item)).collect(Collectors.toList());
    }

    public static SkuOperationLog convertToVo(PopSkuOperationLogDto dto) {
        SkuOperationLog log = new SkuOperationLog();
        log.setId(dto.getId());
        log.setType(dto.getType());
        log.setTypeName(dto.getTypeName());
        log.setRemark(dto.getRemark());
        log.setCreateTime(dto.getCreateTime());
        log.setCreateId(dto.getCreateId());
        log.setCreateName(dto.getCreateName());

        return log;
    }
}
