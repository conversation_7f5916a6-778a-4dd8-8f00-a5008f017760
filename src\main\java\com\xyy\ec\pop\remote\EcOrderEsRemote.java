package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.search.api.remote.dto.AfterSalesEsDto;
import com.xyy.ec.order.search.api.remote.dto.OrderRefundSearchQueryDto;
import com.xyy.ec.order.search.api.remote.dto.OrderRefundSearchQueryPlusDto;
import com.xyy.ec.order.search.api.remote.dto.OrderSearchQueryDto;
import com.xyy.ec.order.search.api.remote.order.OrderSearchApi;
import com.xyy.ec.order.search.api.remote.result.SearchResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单es搜索
 */
@Component
@Slf4j
public class EcOrderEsRemote {

    @Reference(version = "1.0.0")
    private OrderSearchApi orderSearchApi;

    public SearchResultDto<String> orderSearch(OrderSearchQueryDto orderSearchQueryDto) {
        try {
            log.info("EcOrderEsRemote.orderSearch request orderSearchQueryDto:{}", JSONObject.toJSONString(orderSearchQueryDto));
            ApiRPCResult<SearchResultDto<String>> apiRPCResult = orderSearchApi.orderSearch(orderSearchQueryDto);
            log.info("EcOrderEsRemote.orderSearch response  apiRPCResult:{}", JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return null;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("EcOrderEsRemote.orderSearch error orderSearchQueryDto:{}", JSONObject.toJSONString(orderSearchQueryDto), e);
            return null;
        }
    }

    public SearchResultDto<String> refundOrderSearch(OrderRefundSearchQueryDto orderRefundSearchQueryDto) {
        try {
            log.info("EcOrderEsRemote.refundOrderSearch request orderRefundSearchQueryDto:{}", JSONObject.toJSONString(orderRefundSearchQueryDto));
            ApiRPCResult<SearchResultDto<String>> apiRPCResult = orderSearchApi.refundOrderSearch(orderRefundSearchQueryDto);
            log.info("EcOrderEsRemote.refundOrderSearch response  apiRPCResult:{}", JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return null;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("EcOrderEsRemote.refundOrderSearch error orderRefundSearchQueryDto:{}", JSONObject.toJSONString(orderRefundSearchQueryDto), e);
            return null;
        }
    }
    public SearchResultDto<AfterSalesEsDto> afterSalesOrderSearch(OrderRefundSearchQueryDto orderRefundSearchQueryDto) {
        try {
            log.info("EcOrderEsRemote.refundOrderSearch request orderRefundSearchQueryDto:{}", JSONObject.toJSONString(orderRefundSearchQueryDto));
            ApiRPCResult<SearchResultDto<AfterSalesEsDto>> apiRPCResult = orderSearchApi.afterSalesOrderSearch(orderRefundSearchQueryDto);
            log.info("EcOrderEsRemote.refundOrderSearch response  apiRPCResult:{}", JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return null;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("EcOrderEsRemote.refundOrderSearch error orderRefundSearchQueryDto:{}", JSONObject.toJSONString(orderRefundSearchQueryDto), e);
            return null;
        }
    };

    public SearchResultDto<String> refundOrderSearchPlus(OrderRefundSearchQueryPlusDto orderRefundSearchQueryPlusDto) {
        try {
            log.info("EcOrderEsRemote.refundOrderSearchPlus request orderRefundSearchQueryPlusDto:{}", JSONObject.toJSONString(orderRefundSearchQueryPlusDto));
            ApiRPCResult<SearchResultDto<String>> apiRPCResult = orderSearchApi.refundOrderSearchPlus(orderRefundSearchQueryPlusDto);
            log.info("EcOrderEsRemote.refundOrderSearch response  apiRPCResult:{}", JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return null;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("EcOrderEsRemote.refundOrderSearchPlus error orderRefundSearchQueryPlusDto:{}", JSONObject.toJSONString(orderRefundSearchQueryPlusDto), e);
            return null;
        }
    }
}
