/**
 * Copyright (C), 2015-2019,  武汉小药药医药科技有限公司
 * FileName: FastDfsConfig
 * Author:   dell
 * Date:     2019/3/25 19:21
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.xyy.ec.pop.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2019/3/25
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "fastdfs")
public class FastDfsConfig {
    /** 请求地址 */
    private String host;
    /**
     * 最大连接数 default 8.
     */
    private int maxStorageConnection;

    private int connectTimeOut;

    private int netWorkTimeout;

    private String charSet;

    private boolean stealToken;

    private String secretKey;

    private int httpPort;

    private String trackerServers;

}