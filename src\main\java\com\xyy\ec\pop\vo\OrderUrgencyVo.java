package com.xyy.ec.pop.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: OrderUrgencyVo
 * @Package com.xyy.ec.pop.vo
 * @Description: 订单紧急情况备注
 * @date 2020/7/16 21:35
 */
@Data
public class OrderUrgencyVo {

    private Long id;
    /** 订单编号 */
    private String orderNo;
    /** 紧急程度 0(默认:备注) 1:低  5:中  10:高 */
    private Integer urgencyDegree;
    /** 紧急程度描述 */
    private String urgencyInfo;
    /** 备注最后更新时间 */
    private Date urgencyUpdateTime;

}
