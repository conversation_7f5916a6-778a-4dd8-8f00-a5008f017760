package com.xyy.ec.pop.marketing.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @author: WanKp
 * @date: 2018/8/26 22:06
 **/
public enum BranchEnum {

    ALL_COUNTRY("XS000000", 0, "全国","全国"),
    HUBEI_COUNTRY("XS420000", 420000, "湖北子公司", "湖北"),
    HUNAN_COUNTRY("XS430000", 430000, "湖南子公司", "湖南"),
    ANHUI_COUNTRY("XS340000", 340000, "安徽子公司","安徽"),
    ZHEJIANG_COUNTRY("XS330000", 330000, "浙江子公司", "浙江"),
    SHANDONG_COUNTRY("XS370000", 370000, "山东子公司","山东"),
    FUJIAN_COUNTRY("XS350000", 350000, "福建子公司","福建"),
    HENAN_COUNTRY("XS410000", 410000, "河南子公司","河南"),
    CHONGQING_COUNTRY("XS500000", 500000, "重庆子公司","重庆"),
    JIANGXI_COUNTRY("XS360000", 360000, "江西子公司","江西"),
    SHANXI_COUNTRY("XS140001", 140000, "山西子公司","山西"),
    BEIJING_COUNTRY("XS110000", 110000, "北京子公司","北京"),
    SICHUAN_COUNTRY("XS510000", 510000, "四川子公司","四川"),
    YUNNAN_COUNTRY("XS530000", 530000, "云南子公司","云南"),
    HEBEI_COUNTRY("XS130000", 130000, "河北子公司","河北"),
    SHANGHAI_COUNTRY("XS310000", 310000, "上海子公司","上海"),
    JILIN_COUNTRY("XS220000", 220000, "吉林子公司","吉林"),
    GUIZHOU_COUNTRY("XS520000", 520000, "贵州子公司","贵州"),
    GUANGZHOU_COUNTRY("XS440000", 440000, "广州子公司","广州"),
    GUANGXI_COUNTRY("XS450000", 450000, "广西子公司","广西"),
    HEILONGJIANG_COUNTRY("XS230000", 230000, "黑龙江子公司","黑龙江"),
    NEIMENGGU_COUNTRY("XS150000", 150000, "内蒙古子公司","内蒙古"),
    JIANGSU_COUNTRY("XS320000", 320000, "江苏子公司","江苏"),
    SHUIXING_COUNTRY("XS666666", 666666, "水星域","水星"),
    HUOXING_COUNTRY("XS777777", 777777, "火星域","火星"),
    TIANJIN_COUNTRY("XS120000", 120000, "天津子公司","天津"),
    SX_COUNTRY("XS610000", 610000, "陕西子公司","陕西"),
    LIAONING_COUNTRY("XS210000", 210000, "辽宁子公司","辽宁"),
    XIANGGANG_COUNTRY("XS810000", 810000, "香港子公司","香港"),
    XINJIANG_COUNTRY("XS650000", 650000, "新疆子公司","新疆"),
    GANSU_COUNTRY("XS620000", 620000, "甘肃子公司","甘肃"),
    HAINAN_COUNTRY("XS460000", 460000, "海南子公司","海南"),
    XIZANG_COUNTRY("XS540000", 540000, "西藏子公司","西藏"),
    QINGHAI_COUNTRY("XS630000", 630000, "青海子公司","青海"),
    NINGXIA_COUNTRY("XS640000", 640000, "宁夏子公司","宁夏"),
    ;

    private String key ;
    private Integer provinceCode;
    private  String value;
    private  String provinceName;

    BranchEnum(String key, Integer provinceCode, String value, String provinceName) {
        this.key = key;
        this.provinceCode = provinceCode;
        this.value = value;
        this.provinceName = provinceName;
    }

    private static Map<String, BranchEnum> enumMaps = new HashMap<>();
    public static Map<String,String> maps = new HashMap<>();
    public static Map<Integer, BranchEnum> provinceEnumMap = new HashMap<>();

    static {
        for(BranchEnum e : BranchEnum.values()) {
            enumMaps.put(e.getKey(), e);
            maps.put(e.getKey(),e.getValue());
            provinceEnumMap.put(e.getProvinceCode(), e);
        }
    }

    public static String get(String key) {
        return enumMaps.get(key).getValue();
    }

    public String getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }

    public Integer getProvinceCode() {
        return provinceCode;
    }

    public String getProvinceName() {
        return provinceName;
    }


    public static BranchEnum match(String key) {
        return enumMaps.get(key);
    }
}
