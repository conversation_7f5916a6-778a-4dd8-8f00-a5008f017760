package com.xyy.ec.pop.report.Vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class MerchantRecordsVo implements Serializable {
    private static final long serialVersionUID = -1284651709429279815L;
    /**
     * 药店id
     */
    private Long merchantId;
    /**
     * 药店名称
     */
    private String merchantName;
    /**
     * 药店类型
     */
    private Integer merchantType;
    /**
     * 省code
     */
    private Integer provinceCode;
    /**
     * 省份
     */
    private String provinceName;
    /**
     * 地址
     */
    private String address;

    /**
     * 累计举报次数
     */
    private Integer totalReports;
    /**
     * 累计举报商家数
     */
    private Integer uniqueMerchants;
    /**
     * 举报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstReportTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastReportTime;
    /**
     * 举报审核状态：0-待审核, 1-审核通过, -1-核驳回，2-移除
     */
    private Integer auditStatus;
    private String auditStatusStr;

    /**
     * 客户类型
     */
    private Integer customerType;
    private String customerTypeStr;
}
