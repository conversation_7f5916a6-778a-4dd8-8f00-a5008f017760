package com.xyy.ec.pop.controller;

import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AdjustiveBillSettleConfig;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.excel.verify.AdjustiveBillSettleExcelVerifyHandler;
import com.xyy.ec.pop.excel.verify.AdjustiveBillSettleValid;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.po.PopBillSettleDetailPo;
import com.xyy.ec.pop.po.PopBillSettlePo;
import com.xyy.ec.pop.remote.CommissionSettleSetRemote;
import com.xyy.ec.pop.remote.CorporationRemote;
import com.xyy.ec.pop.server.api.admin.dto.AdjustiveBillSettleDto;
import com.xyy.ec.pop.server.api.admin.dto.CommissionSettleSetDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import com.xyy.ec.pop.server.api.order.dto.PopBillSettleDto;
import com.xyy.ec.pop.service.settle.PopBillSettleService;
import com.xyy.ec.pop.service.settle.impl.domain.PopBillSettleDomainService;
import com.xyy.ec.pop.utils.ExceImportWarpUtil;
import com.xyy.ec.pop.utils.FileNameUtils;
import com.xyy.ec.pop.utils.cos.CosUploadPopUtils;
import com.xyy.ec.pop.vo.AdjustiveBillSettleResultVo;
import com.xyy.ec.pop.vo.AdjustiveBillSettleVo;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.activity.UpdateBatchResultVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleDetailVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleStatisVo;
import com.xyy.ec.pop.vo.settle.PopBillSettleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 结算单
 *
 * <AUTHOR>
 * @date 2022/8/15 10:27
 * @table
 */
@Slf4j
@RequestMapping("/billSettle")
@Controller
public class BillSettleController extends BaseController {
    @Autowired
    private PopBillSettleService popBillSettleService;
    @Autowired
    private PopBillSettleDomainService popBillSettleDomainService;
    @Autowired
    private AdjustiveBillSettleConfig adjustiveBillSettleConfig;
    @Autowired
    private CorporationRemote corporationRemote;
    @Autowired
    private CommissionSettleSetRemote commissionSettleSetRemote;


    /**
     * 列表查询
     *
     * @param popBillSettleVo
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/list")
    public ResponseVo<PageInfo<PopBillSettlePo>> list(PopBillSettleVo popBillSettleVo, PageInfo pageInfo) {
        try {
            if (validateProv(popBillSettleVo)) {
                return ResponseVo.successResult(new PageInfo<>());
            }
            PageInfo<PopBillSettlePo> pageInfoPop = popBillSettleDomainService.queryPopBillSettleList(popBillSettleVo, pageInfo);
            return ResponseVo.successResult(pageInfoPop);
        } catch (ServiceRuntimeException e) {
            log.error("查询结算单列表异常", e);
            return ResponseVo.errRest("查询结算单列表失败");
        }
    }

    private boolean validateProv(PopBillSettleVo popBillSettleVo) {
        List<Long> provIds = getProvIds(popBillSettleVo.getProvId());
        if (CollectionUtils.isEmpty(provIds)) {
            return true;
        }
        popBillSettleVo.setProvIds(provIds);
        return false;
    }

    /**
     * 结算单分页查看明细
     */
    @ResponseBody
    @GetMapping(value = "/detail")
    public ResponseVo<PageInfo<PopBillSettleDetailVo>> billSettleDetail(@RequestParam("businessNo")String businessNo, @RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize) {
        try {
            if (StringUtils.isBlank(businessNo) || Objects.isNull(pageNum) || Objects.isNull(pageSize)) {
                return ResponseVo.errRest("查询参数错误");
            }
            PageInfo<PopBillSettleDetailVo> pageInfoPop = popBillSettleDomainService.queryPopBillSettleDetailList(businessNo, pageNum, pageSize);
            return ResponseVo.successResult(pageInfoPop);
        } catch (ServiceRuntimeException e) {
            log.error("查询结算单明细异常", e);
            return ResponseVo.errRest("查询结算单明细失败");
        }
    }
    /**
     * 列表根据搜索条件统计佣金金额，应结算金额
     *
     * @param popBillSettleVo
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/queryBillSettleStatistic")
    public ResponseVo<PopBillSettleStatisVo> queryBillSettleStatistic(PopBillSettleVo popBillSettleVo) {
        try {
            if (validateProv(popBillSettleVo)) {
                PopBillSettleStatisVo popBillSettleStatisVo = new PopBillSettleStatisVo();
                return ResponseVo.successResult(popBillSettleStatisVo);
            }
            PopBillSettleStatisVo popBillPaymentPo = popBillSettleDomainService.queryPopBillSettleStatis(popBillSettleVo);
            if(popBillPaymentPo == null){
                popBillPaymentPo = new PopBillSettleStatisVo();
            }
            return ResponseVo.successResult(popBillPaymentPo);
        } catch (ServiceRuntimeException e) {
            log.error("查询结算单统计金额异常", e);
            return ResponseVo.errRest("查询结算单统计金额失败");
        }
    }

    /**
     * 查询列表结算单导出的条数  超过5000条数据，前端给出提示
     *
     * @param popBillSettleVo
     * @return
     */
    @GetMapping(value = "/queryBillSettleExportCount")
    @ResponseBody
    public ResponseVo<Long> queryBillSettleExportCount(PopBillSettleVo popBillSettleVo) {
        try {
//            popBillSettleVo.setPayType(Integer.valueOf(OrderPayTypeEnum.ONLINE.getType()).byteValue());
//            popBillSettleVo.setOrderSettlementStatus((byte)OrderSettleStatusEnum.DONE.getStatus());  //只查询已结算
            Long count = popBillSettleDomainService.queryPopBillListCount(popBillSettleVo);
            return ResponseVo.successResult(count);
        } catch (Exception e) {
            log.error("查询列表结算单导出的条数异常", e);
            return ResponseVo.errRest("查询结算单导出数量失败");
        }
    }

    @PostMapping(value = "/batchImportAdjustiveBillSettle")
    @ResponseBody
    public ResponseVo<AdjustiveBillSettleResultVo> batchImportAdjustiveBillSettle(@RequestParam("file") MultipartFile file) {
        String user = getUser().getId() + getUser().getUsername();
        try {
            if (StringUtils.isBlank(user)) {
                return ResponseVo.errRest("未知用户，请重新登录后重试");
            }
            ImportParams importParams = new ImportParams();
            importParams.setVerifyHandler(new AdjustiveBillSettleExcelVerifyHandler());
            List<AdjustiveBillSettleVo> vos = ExceImportWarpUtil.importExcel(file, AdjustiveBillSettleVo.class, importParams, adjustiveBillSettleConfig.getMaxFileSize(), adjustiveBillSettleConfig.getMaxRows(), adjustiveBillSettleConfig.getTitles());
            AdjustiveBillSettleValid.trim(vos);
            List<String> orgIds = vos.stream().map(AdjustiveBillSettleVo::getOrgId).distinct().collect(Collectors.toList());
            List<CorporationDto> corporationDtos = corporationRemote.queryCorpBaseByOrgIds(orgIds);
            //查询有效的orgIds
            List<String> orgIdsInDB = corporationDtos.stream().map(CorporationDto::getOrgId).collect(Collectors.toList());
            //查询商家佣金结算方式
            List<CommissionSettleSetDto> commissionSettleSetDtos = commissionSettleSetRemote.listCommissionSettleSet(orgIds);
            //<orgId,佣金结算方式对象>
            Map<String, CommissionSettleSetDto> orgIdSettleSetMap = commissionSettleSetDtos.stream().collect(Collectors.toMap(item -> item.getOrgId(), Function.identity()));
            AdjustiveBillSettleValid.valid(vos, orgIdsInDB, orgIdSettleSetMap);


            AdjustiveBillSettleResultVo resultVo = popBillSettleService.batchImportAdjustiveBillSettle(vos, user, orgIdSettleSetMap);
            if (resultVo != null && resultVo.getErrorCount() > 0) {
                resultVo.setErrorFileName(FileNameUtils.getErrorFileName(file));
                String downLoadUrl = FileNameUtils.getErrorFileDownUrl(resultVo.getErrorFileUrl(), resultVo.getErrorFileName());
                resultVo.setErrorFileUrl(downLoadUrl);
            }
            return ResponseVo.successResult(resultVo);
        } catch (ServiceException e) {
            log.error("BillSettleController.batchImportAdjustiveBillSettle,user:{}, 失败", user, e);
            return ResponseVo.errRest(StringUtils.isEmpty(e.getMessage()) ? "系统异常" : e.getMessage());
        } catch (Exception e) {
            log.error("BillSettleController.batchImportAdjustiveBillSettle,user:{}, 出现异常", user, e);
            return ResponseVo.errRest("调账单导入异常");
        }
    }

    /**
     * 批量更新调账单结算状态和附件
     *
     * @param params
     * @return
     */
    @PostMapping(value = "/batchUpdateAdjustiveBillSettle")
    @ResponseBody
    public ResponseVo batchUpdateAdjustiveBillSettle(@RequestBody List<AdjustiveBillSettleDto> params) {
        log.info("BillSettleController.batchUpdateAdjustiveBillSettle#params:{}", JSON.toJSONString(params));
        String user = getUser().getUsername();
        try {
            if (CollectionUtils.isEmpty(params)) {
                return ResponseVo.errRest("参数为空，请检查数据");
            }
            List<AdjustiveBillSettleDto> errorList = params.stream().filter(f -> f.getId() == null || StringUtils.isBlank(f.getAttachmentUrl())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errorList)) {
                return ResponseVo.errRest("请添加附件后重试");
            }
            popBillSettleService.batchUpdateAdjustiveBillSettle(params);
            return ResponseVo.successResult(null);
        } catch (ServiceRuntimeException e) {
            log.error("BillSettleController.batchUpdateAdjustiveBillSettle#error. params:{}, 自定义异常", JSON.toJSONString(params), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillSettleController.batchUpdateAdjustiveBillSettle#error. params:{}, 出现异常", JSON.toJSONString(params), e);
            return ResponseVo.errRest("批量更新调整单失败");
        }
    }

    /**
     * 查询导入的调账单列表
     *
     * @return
     */
    @GetMapping(value = "/listImportAdjustiveBillSettle")
    @ResponseBody
    public ResponseVo<List<PopBillSettleDto>> listImportAdjustiveBillSettle() {
        log.info("BillSettleController.listImportAdjustiveBillSettle#");
        String userName = getUser().getId() + getUser().getUsername();
        try {
            if (StringUtils.isBlank(userName)) {
                return ResponseVo.errRest("未知用户，请重新登录后重试");
            }
            List<PopBillSettleDto> result = popBillSettleService.listImportAdjustiveBillSettle(userName);
            return ResponseVo.successResult(result);
        } catch (ServiceRuntimeException e) {
            log.error("BillSettleController.listImportAdjustiveBillSettle#error. userName:{}, 自定义异常", userName, e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillSettleController.listImportAdjustiveBillSettle#error. userName:{}, 出现异常", userName, e);
            return ResponseVo.errRest("查询导入的调账单列表异常");
        }
    }

    /**
     * 批量移除导入调账单
     *
     * @return
     */
    @PostMapping(value = "/deleteAdjustiveBillSettleByIds")
    @ResponseBody
    public ResponseVo deleteAdjustiveBillSettleByIds(@RequestBody List<String> ids) {
        log.info("BillSettleController.deleteAdjustiveBillSettleByIds#ids:{}", JSON.toJSONString(ids));
        String userName = getUser().getId() + getUser().getUsername();
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return ResponseVo.errRest("参数不能为空");
            }
            if (StringUtils.isBlank(userName)) {
                return ResponseVo.errRest("未知用户，请重新登录后重试");
            }
            Boolean result = popBillSettleService.deleteAdjustiveBillSettleByIds(ids, userName);
            if (result != null && result) {
                return ResponseVo.successResult(null);
            }
            return ResponseVo.errRest("批量移除导入调账单失败");
        } catch (ServiceRuntimeException e) {
            log.error("BillSettleController.deleteAdjustiveBillSettleByIds#自定义异常. ids:{}, userName:{}", JSON.toJSONString(ids), userName, e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("BillSettleController.deleteAdjustiveBillSettleByIds#未知异常. ids:{}, userName:{}", JSON.toJSONString(ids), userName, e);
            return ResponseVo.errRest("批量移除导入调账单异常");
        }
    }

    /**
     * 更新是否参与佣金折扣
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "/updateCommissionCalcFlag")
    @ResponseBody
    public ResponseVo updateCommissionCalcFlag(@RequestBody PopBillSettleVo vo) {
        log.info("updateCommissionCalcFlag#params:{}", JSON.toJSONString(vo));
        String userName = getUser().getId() + getUser().getUsername();
        try {
            if (vo.getCommissionCalcFlag() == null || vo.getId() == null) {
                return ResponseVo.errRest("参数CommissionCalcFlag或id为空，请检查数据");
            }
            return popBillSettleService.updateCommissionCalcFlag(vo,userName);
        } catch (ServiceRuntimeException e) {
            log.error("updateCommissionCalcFlag#error. params:{}, 自定义异常", JSON.toJSONString(vo), e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("updateCommissionCalcFlag#error. params:{}, 出现异常", JSON.toJSONString(vo), e);
            return ResponseVo.errRest("更新失败");
        }
    }

    @PostMapping(value = "/updateBatchCommissionCalcFlag")
    @ResponseBody
    @AvoidRepeatableCommit
    public ResponseVo<UpdateBatchResultVo> updateBatchCommissionCalcFlag(@RequestParam("file") MultipartFile file) {
        String userName = getUser().getId() + getUser().getUsername();
        try {
            return popBillSettleService.updateBatchCommissionCalcFlag(file,userName);
        } catch (ServiceRuntimeException e) {
            log.error("updateBatchCommissionCalcFlag, 自定义异常", e);
            return ResponseVo.errRest(e.getMessage());
        } catch (Exception e) {
            log.error("updateBatchCommissionCalcFlag, 出现异常", e);
            return ResponseVo.errRest("更新失败");
        }
    }

    @GetMapping(value = "/downloadCommissionCalcFlag")
    @ResponseBody
    public void downloadCommissionCalcFlag(HttpServletResponse response) {
        try {
            popBillSettleService.downloadCommissionCalcFlag(response);
        } catch (Exception e) {
            log.error("downloadCommissionCalcFlag 下载模板异常", e);
        }
    }
}
