package com.xyy.ec.pop.enums;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/2/1.
 */
public interface PlannedOrderItemEnum {

    enum PlannedOderSelect implements PlannedOrderItemEnum{
        IS_SELECT(1,"被选中"),
        IS_UNSELECT(0,"未被选中");

        private int id;
        private String value;

        PlannedOderSelect(int id,String value){
            this.id = id;
            this.value = value;
        }
        public int getId(){
            return id;
        }
        public String getValue(){
            return value;
        }
    }

    enum PlannedOrderMustQTY implements PlannedOrderItemEnum{
        MUST_QTY(1000,"最大购买数量");

        private int id;
        private String value;

        PlannedOrderMustQTY(int id,String value){
            this.id = id;
            this.value = value;
        }
        public int getId(){
            return id;
        }
        public String getValue(){
            return value;
        }
    }
}
