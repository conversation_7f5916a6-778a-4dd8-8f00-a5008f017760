/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.xyy.ec.pop.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * bootstrap-tablePage辅助类 ClassName: Page <br/>
 * date: 2015-9-21 下午3:06:22 <br/>
 * 
 * <AUTHOR>
 * @version
 * @param <T>
 * @since JDK 1.7
 */
@ApiModel
public final class Page<T> implements Serializable {

	private static final long serialVersionUID = 8091007641441739356L;

	private final static ThreadLocal<Page<? extends Object>> page = new ThreadLocal<Page<? extends Object>>();

	@ApiModelProperty(value = "", name = "total", notes = "分页数据总数")
	// 总记录数
	private Long total;
	@ApiModelProperty(value = "", name = "offset", notes = "分页查询的起始码")
	// 页码
	private Integer offset = 0;
	@ApiModelProperty(value = "", name = "limit", notes = "分页查询的每页条数")
	// 每页显示条数
	private Integer limit = 10;
	@ApiModelProperty(value = "", name = "rows", notes = "分页查询返回的数据list")
	// 具体业务数据
	private List<T> rows;
	@ApiModelProperty(value = "", name = "pageCount", notes = "分页查询的总页数")
	// 总页数
	private Integer pageCount = 0;

	@ApiModelProperty(value = "", name = "pageNum", notes = "分页查询的起始码")
	// 页码
	private Integer pageNum = 1;
	@ApiModelProperty(value = "", name = "pageSize", notes = "分页查询的每页条数")
	// 每页显示条数
	private Integer pageSize = 10;

	/**
	 * 当前页
	 */
	@ApiModelProperty(value = "", name = "currentPage", notes = "分页查询的当前页")
	private Integer currentPage = 0;

	@ApiModelProperty(value = "", name = "currentPage", hidden = true)
	private Map<String, Object> requestParameters;

	@ApiModelProperty(value = "", name = "currentPage", hidden = true)
	private String requestUrl;

	//排序字段
	private String property;
	//排序类型
	private String direction;

	// 默认构造函数
	public Page() {
		page.set(this);
	}

	/**
	 * 获取当前线程ThreadLocal变量绑定的pageBean
	 */
	public static Page<?> get() {
		return null != page.get() ? page.get() : new Page<Object>();
	}

	/**
	 * 清除当前线程ThreadLocal变量
	 */
	public static void clear() {
		page.remove();
	}

	public Page(Integer offset, Integer limit) {
		this.setOffset(offset);
		this.setLimit(limit);
	}

	public Long getTotal() {
		return total;
	}

	public void setTotal(Long total) {
		this.total = total;
		if(null == total){
			total = 0L;
		}
		this.pageCount = total.intValue() / limit
				+ ((total.intValue() % limit == 0) ? 0 : 1);
		if (currentPage > pageCount)
			currentPage = pageCount;
		if (currentPage <= 0)
			currentPage = 1;
	}

	public Integer getOffset() {
		return offset;
	}

	public void setOffset(Integer offset) {
		this.offset = offset < 0 ? 0 : offset;
	}

	public Integer getLimit() {
		return limit;
	}

	public void setLimit(Integer limit) {
		this.limit = limit <= 0 ? 10 : limit;
	}

	public List<T> getRows() {
		return rows;
	}

	public void setRows(List<T> rows) {
		this.rows = rows;
	}

	public Integer getPageCount() {
		return pageCount;
	}

	public void setPageCount(Integer pageCount) {
		this.pageCount = pageCount;

	}

	public Integer getCurrentPage() {
		if (offset <= 0) {
			return currentPage;
		} else {
			return offset;
		}

	}

	public void setCurrentPage(Integer currentPage) {
		this.currentPage = currentPage;
	}

	/**
	 * 是否可以到第一页
	 * 
	 * @return
	 */
	public boolean canGoFirst() {
		return (this.currentPage > 1);
	}

	public Map<String, Object> getRequestParameters() {
		return requestParameters;
	}

	public void setRequestParameters(Map<String, Object> requestParameters) {
		this.requestParameters = requestParameters;
	}

	public String getRequestUrl() {
		return requestUrl;
	}

	public void setRequestUrl(String requestUrl) {
		this.requestUrl = requestUrl;
	}

	public String getProperty() {
		return property;
	}

	public void setProperty(String property) {
		this.property = property;
	}

	public String getDirection() {
		return direction;
	}

	public void setDirection(String direction) {
		this.direction = direction;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}
