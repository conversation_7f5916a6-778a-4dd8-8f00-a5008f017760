package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Title: 订单相关常量
 * @Description:
 * @date 2018/1/6 16:05
 */
public interface OrderEnum {


    enum OrderSeparateFlag implements OrderEnum {

        SEPARATETRUE(1, "可拆单"),
        SEPARATEFALSE(0, "不可拆单");

        private int id;
        private String value;

        OrderSeparateFlag(int id, String value) {
            this.id = id;
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public int getId() {
            return id;
        }
    }
   
    enum OrderMiddleStatus implements OrderEnum{
    	  STATUS_FOR_THE_SHIPPING(1,"待配送"),      //待配送

    	    /** 配送中 */
    	     STATUS_IN_THE_DISTRIBUTION(2,"配送中"),  //配送中

    	    /** 已完成 */
    	    STATUS_HAS_BEEN_SHIPPED(3,"已完成"),      //已完成

    	    /** 取消 */
    	    STATUS_CANCEL(4,"取消"),               //取消

    	    /** 删除 */
    	    STATUS_DELETE(5,"删除"),                //删除

    	    /** 拆单 */
    	    STATUS_SEPARATE(6,"拆单"),              //拆单

    	    /** 出库中 */
    	    STATUS_IN_THE_ERP(7,"出库中");               //出库中
    	    
    	    private int id;
            private String value;

            OrderMiddleStatus(int id, String value) {
                this.id = id;
                this.value = value;
            }

            public static Map<Integer, String> maps = new HashMap<>();

            static {
                for (OrderMiddleStatus e : OrderMiddleStatus.values()) {
                    maps.put(e.getId(), e.getValue());
                }
            }

            public String getValue() {
                return value;
            }

            public int getId() {
                return id;
            }
    }
    /**
     * 订单状态
     */
    enum OrderStatus implements OrderEnum {
        CHECK_STATUS_NONE(0, "尚未进入审单流程"),
        PENDING(1, "已支付"),
        SHIPPING(2, "配送中"),
        FINISH(3, "已完成"),
        INVALID(4, "取消"),
        DELETE(5, "删除"),
        SEPARATE(6, "已拆单"),
        OUTBOUND(7, "出库中"),
        CHECK_STATUS_FINISH(9, "审单流程结束"),
        WAITBUYERPAY(10, "未支付"),
        STATUS_TRADE_SUCCESS(11, "已支付"),
        DELIVERED(20, "已送达"),
        REJECTION(21, "已拒签"),
        REFUNDPENDING(90, "退款审核中"),
        REFUNDCOMPLETE(91, "已退款");

        private int id;
        private String value;

        OrderStatus(int id, String value) {
            this.id = id;
            this.value = value;
        }

        public static Map<Integer, String> maps = new HashMap<>();

        static {
            for (OrderStatus orderStatus : OrderStatus.values()) {
                maps.put(orderStatus.getId(), orderStatus.getValue());
            }
        }

        public static String getValueById(Integer status) {
            return maps.get(status);
        }

        public String getValue() {
            return value;
        }

        public int getId() {
            return id;
        }
    }

	enum OrderPayTag implements OrderEnum {

		PAY_AFTER(1, "支付成功后标志"),
		CONFIRM_RECEIPT(2, "确认收获标志");

		private int id;
		private String value;

		OrderPayTag(int id, String value) {
			this.id = id;
			this.value = value;
		}


		public static OrderPayTag findByValue(int code) {
			switch (code) {
				case 1:
					return PAY_AFTER;
				case 2:
					return CONFIRM_RECEIPT;
				default:
					return null;
			}
		}

		public String getValue() {
			return value;
		}

		public int getId() {
			return id;
		}
	}



    /**
     * 订单支付类型
     */
    enum OrderPayType implements OrderEnum {

        ONLINEPAYMENT(1, "在线支付"),
        CASHONDELIVERY(2, "货到付款"),
        OFFLINETRANSFER(3, "线下转账");

        private int id;
        private String value;

        OrderPayType(int id, String value) {
            this.id = id;
            this.value = value;
        }

        public static Map<Integer, String> maps = new HashMap<>();

        static {
            for (OrderPayType orderPayType : OrderPayType.values()) {
                maps.put(orderPayType.getId(), orderPayType.getValue());
            }
        }

		public static OrderPayType findByValue(int code) {
			switch (code) {
				case 1:
					return ONLINEPAYMENT;
				case 2:
					return CASHONDELIVERY;
				case 3:
					return OFFLINETRANSFER;
				default:
					return null;
			}
		}

        public String getValue() {
            return value;
        }

        public int getId() {
            return id;
        }
    }

    /**
     * 支付渠道(1:支付宝 2:微信 3:银联)
     */
    enum OrderPayChannelType implements OrderEnum {

        ALIPAY(1, "支付宝"),
        WXPAY(2, "微信"),
        UNIONPAY(3, "银联");

        private int id;
        private String value;

        OrderPayChannelType(int id, String value) {
            this.id = id;
            this.value = value;
        }

        public static Map<Integer, String> maps = new HashMap<>();

        static {
            for (OrderPayChannelType orderPayChannelType : OrderPayChannelType.values()) {
                maps.put(orderPayChannelType.getId(), orderPayChannelType.getValue());
            }
        }
        
        public static String get(int id) {
            return maps.get(id);
        }


        public String getValue() {
            return value;
        }

        public int getId() {
            return id;
        }
    }

    enum OrderRefundConstant implements OrderEnum {

        AUDIT_WAIT(0, "等待审核"),
        AUDIT_PASS(1, "审核通过"),
        AUDIT_NO_PASS(-1, "审核不通过"),
        APP_PUSH(1, "客户发起退款"),
        WEBSERVICE_PUSH(2, "客服介入申请退款"),
        DRIVER_PUSH(3, "司机发起申请退款");

        private int id;
        private String value;

        OrderRefundConstant(int id, String value) {
            this.id = id;
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public int getId() {
            return id;
        }
    }



    enum OrderDetailConstant implements OrderEnum {

        STATUS_FOR_THE_SHIPPING(0, "待配送"),
        STATUS_IN_THE_DISTRIBUTION(1, "配送中"),
        STATUS_HAVE_THE_GOODS(2, "已完成"),
        STATUS_HAVE_TO_RETURN(3, "已退货"),
        STATUS_CANCEL(4, "取消"),
        STATUS_DELETE(5, "删除"),
        TYPE_NORMAL(1, "原价"),
        TYPE_PROMOTION(2, "特惠价格"),
        TYPE_SECKILL(3, "秒杀价格"),
        TYPE_FIXED_PRICE(5, "一口价价格"),
        TYPE_PLUMMET(6, "直降"),
        TYPE_GROUP_PURCHASE(8, "拼团");



        private int id;
        private String value;

        OrderDetailConstant(int id, String value) {
            this.id = id;
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public int getId() {
            return id;
        }
    }
    

}

