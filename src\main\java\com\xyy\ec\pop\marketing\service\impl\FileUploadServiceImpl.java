package com.xyy.ec.pop.marketing.service.impl;

import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.marketing.constants.MarketingConstants;
import com.xyy.ec.pop.marketing.service.FileUploadService;
import com.xyy.ec.pop.utils.autoconfigure.XyyFtpClientTemplate;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.UUID;

@Service
public class FileUploadServiceImpl implements FileUploadService {

    @Autowired
    private XyyFtpClientTemplate xyyFtpClientTemplate;

    @Override
    public String uploadFile(MultipartFile multipartFile, String module) {
        InputStream inputStream;
        try {
            inputStream = multipartFile.getInputStream();
            String originalFilename = multipartFile.getOriginalFilename();
            String extension = FilenameUtils.getExtension(originalFilename);
            String filename = UUID.randomUUID().toString() + (StringUtils.isNotBlank(extension) ? "." + extension : "");
            String ftpAbsoluteDir = MarketingConstants.FTP_ABSOLUTE_DIR_MARKETING + (StringUtils.isNotBlank(module) ? "/" + module : "");
            return xyyFtpClientTemplate.uploadFile(inputStream, ftpAbsoluteDir, filename);
        } catch (PopAdminException e) {
            throw e;
        } catch (Exception e) {
            throw new PopAdminException("上传文件失败。", e, XyyJsonResultCodeEnum.FTP_UPLOAD_FILE_FAILURE);
        }
    }

    @Override
    public String uploadFile(InputStream inputStream, String filename, String module) {
        try {
            String ftpAbsoluteDir = MarketingConstants.FTP_ABSOLUTE_DIR_MARKETING + (StringUtils.isNotBlank(module) ? "/" + module : "");
            return xyyFtpClientTemplate.uploadFile(inputStream, ftpAbsoluteDir, filename);
        } catch (PopAdminException e) {
            throw e;
        } catch (Exception e) {
            throw new PopAdminException("上传文件失败。", e, XyyJsonResultCodeEnum.FTP_UPLOAD_FILE_FAILURE);
        }
    }

    @Override
    public FTPFile[] listFiles(String ftpAbsoluteDir) {
        try {
            return xyyFtpClientTemplate.listFiles(ftpAbsoluteDir);
        } catch (PopAdminException e) {
            throw e;
        } catch (Exception e) {
            throw new PopAdminException("获取文件或目录列表失败。", e, XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public boolean deleteFile(String pathname) {
        try {
            return xyyFtpClientTemplate.deleteFile(pathname);
        } catch (PopAdminException e) {
            throw e;
        } catch (Exception e) {
            throw new PopAdminException("删除文件失败。", e, XyyJsonResultCodeEnum.FAIL);
        }
    }


    @Override
    public boolean removeDirectory(String pathname) {
        try {
            return xyyFtpClientTemplate.removeDirectory(pathname);
        } catch (PopAdminException e) {
            throw e;
        } catch (Exception e) {
            throw new PopAdminException("删除文件失败。", e, XyyJsonResultCodeEnum.FAIL);
        }
    }

}
