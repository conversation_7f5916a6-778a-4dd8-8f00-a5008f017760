package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.server.api.merchant.api.enums.QualificationsCategoryEnum;
import com.xyy.ec.pop.vo.QualificationsCategoryRelationVo;
import com.xyy.ec.pop.vo.QualificationsCategoryVo;
import com.xyy.ec.pop.vo.QualificationsDetailVo;
import com.xyy.pop.framework.core.exception.XyyEcPopException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 校验工具
 * <AUTHOR>
 */
public class PopQualificationsConfigValidHelper {
    /**
     * 运维基本不使用，只做简单逻辑判断
     * @param relationVoList
     * @param corporationType
     */
    public static void checkBeforeSave(List<QualificationsCategoryRelationVo> relationVoList, byte corporationType) {
        if(CollectionUtils.isEmpty(relationVoList)){
            throw new XyyEcPopException("资质配置不能为空");
        }
        long invalidCount = relationVoList.stream().filter(relationVo -> (null == relationVo.getQualificationsCategory() && CollectionUtils.isNotEmpty(relationVo.getBusinessCategoryIdList()))
                || (null != relationVo.getQualificationsCategory() && CollectionUtils.isEmpty(relationVo.getBusinessCategoryIdList())))
                .count();
        if(invalidCount > 0){
            throw new XyyEcPopException("数据存在缺失,请检查");
        }

        //检查公共配置
        checkCommonQualificationsConfig(relationVoList,corporationType);
        //检查经营资质
        checkBusinessQualificationsConfig(relationVoList,corporationType);
    }

    private static void checkBusinessQualificationsConfig(List<QualificationsCategoryRelationVo> relationVoList, byte corporationType) {
        List<QualificationsCategoryRelationVo> commonList =  relationVoList.stream().filter(item->item.getQualificationsCategory()!=null&&CollectionUtils.isNotEmpty(item.getBusinessCategoryIdList())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(commonList)){
            return;
        }
        for(QualificationsCategoryRelationVo relationVo:commonList){
            if(StringUtils.isEmpty(relationVo.getQualificationsCategory().getName())){
                throw new XyyEcPopException("资质名称不能为空");
            }
            if(CollectionUtils.isEmpty(relationVo.getBusinessCategoryIdList())){
                throw new XyyEcPopException(relationVo.getQualificationsCategory().getName()+"关联经营范围不能为空");
            }
            checkQualificationsDetails(relationVo.getQualificationsDetailList(),relationVo.getQualificationsCategory().getName());
            relationVo.getQualificationsCategory().setCorporationType(corporationType);
            relationVo.getQualificationsCategory().setType(QualificationsCategoryEnum.QUALIFICATIONS.getCode());
        }
    }

    private static void checkCommonQualificationsConfig(List<QualificationsCategoryRelationVo> relationVoList, byte corporationType) {
        List<QualificationsCategoryRelationVo> commonList =  relationVoList.stream().filter(item->item.getQualificationsCategory()==null&&CollectionUtils.isNotEmpty(item.getQualificationsDetailList())&&item.getBusinessCategoryIdList()==null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(commonList)){
            throw new XyyEcPopException("企业公共资质不能为空");
        }
        if(commonList.size()>1){
            throw new XyyEcPopException("企业公共资质只能有一个");
        }
        checkQualificationsDetails(commonList.get(0).getQualificationsDetailList(),"企业公共资质");
        QualificationsCategoryVo vo = new QualificationsCategoryVo();
        vo.setName("公共资质");
        vo.setCorporationType(corporationType);
        vo.setType(QualificationsCategoryEnum.COMMON.getCode());
        commonList.get(0).setQualificationsCategory(vo);
  }

    private static void checkQualificationsDetails(List<QualificationsDetailVo> detailList, String name) {
        if(CollectionUtils.isEmpty(detailList)){
            throw new XyyEcPopException(name+"详情不能为空");
        }
        for(QualificationsDetailVo vo:detailList){
            if(StringUtils.isEmpty(vo.getName())){
                throw new XyyEcPopException(name+"名称不能为空");
            }
            if(StringUtils.isEmpty(vo.getCode())){
                throw new XyyEcPopException(vo.getName()+"编码不能为空");
            }
            if(vo.getMaxImg()==null||vo.getMaxImg()<=0){
                throw new XyyEcPopException(vo.getName()+"最大图片数不正确");
            }
        }
    }

}
