package com.xyy.ec.pop.excel.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class SettlementExportVo implements Serializable {
    private static final long serialVersionUID = -7234760341816842649L;
    private Integer id;

    /**
     * 商户编号
     */
    @Excel(name = "商户编号",width = 15)
    private String orgId;

    /**
     * 商户编号
     */
    @Excel(name = "商户名称",width = 15)
    private String orgName;

    /**
     * 订单号
     */
    @Excel(name = "订单号",width = 15)
    private String orderNo;

    /**
     * 订单总金额
     */
    @Excel(name = "订单总金额",width = 15)
    private BigDecimal totalMoney;

    /**
     * 订单实付金额
     */
    @Excel(name = "订单实付金额",width = 15)
    private BigDecimal money;

    /**
     * 店铺优惠券金额
     */
    @Excel(name = "店铺优惠券金额",width = 15)
    private BigDecimal shopDiscountMoney;

    /**
     * ec平台优惠券金额
     */
    @Excel(name = "平台优惠券金额",width = 15)
    private BigDecimal platformDiscountMoney;

    /**
     * 运费
     */
    @Excel(name = "运费金额",width = 15)
    private BigDecimal freightAmount;

    /**
     * 退款金额
     */
    @Excel(name = "退款金额",width = 15)
    private BigDecimal refundTotalMoney;

    /**
     * 佣金
     */
    @Excel(name = "佣金金额",width = 15)
    private BigDecimal hireMoney;

    /**
     * 应结算金额
     */
    @Excel(name = "应结算金额",width = 15)
    private BigDecimal statementTotalMoney;

    /**
     * 订单创建时间
     */
    @Excel(name = "下单时间",width = 15,format = "yyyy-MM-dd HH:mm:ss")
    private Date orderCreateTime;

    /**
     * 订单状态 1-待配送，2-配送中，3-已配送，4-取消，5-已删除,6-已拆单,7-出库中,10-未支付,90-退款审核中,91-已退款,20-已送达,21-已拒签
     */
    @Excel(name = "订单状态",width = 15)
    private String orderStatusName;

    /**
     * 退款状态 0-无退款 1-退款进行中 2-已完成
     */
    @Excel(name = "退款状态",width = 15,replace = {"无退款_0", "退款进行中_1", "已完成_2" })
    private Byte refundStatus;

    /**
     * 确认收货时间
     */
    @Excel(name = "确认收货时间",width = 15,format = "yyyy-MM-dd HH:mm:ss")
    private Date confirmReceiveTime;

    /**
     * 支付类型(1:在线支付 2:货到付款 3:线下转账）
     */
    @Excel(name = "支付类型",width = 15,replace = {"在线支付_1", "货到付款_2", "线下转账_3" })
    private Byte payType;

//    /**
//     * 创建时间
//     */
//    @Excel(name = "结算单生成时间",width = 15,format = "yyyy-MM-dd HH:mm:ss")
//    private Date createTime;

    /**
     * 结算时间
     */
    @Excel(name = "结算时间",width = 15,format = "yyyy-MM-dd HH:mm:ss")
    private Date settlementTime;

//    /**
//     * 结算状态 0-待结算 1-结算完成
//     */
//    @Excel(name = "结算状态",width = 15,replace = {"待结算_0", "结算完成_1","待商户申请结算_2" })
//    private Byte settlementStatus;
    /**
     * 开票状态
     */
    @Excel(name = "开票状态",width = 15,replace = {"未开票_0", "已开票_1"})
    private Byte billingStatus;
    /**
     * 开票时间
     */
    @Excel(name = "开票时间",width = 15,format = "yyyy-MM-dd")
    private Date billingTime;
}
