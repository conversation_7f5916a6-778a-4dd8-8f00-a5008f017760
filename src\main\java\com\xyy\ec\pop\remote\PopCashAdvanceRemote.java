package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.seller.api.PopCashAdvanceApi;
import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceDetailDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceLogDto;
import com.xyy.ec.pop.server.api.seller.param.CashAdvanceDetailQuery;
import com.xyy.ec.pop.server.api.seller.param.CashAdvanceQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description 提现远程接口
 * <AUTHOR>
 * @Date 2021/6/29
 */
@Slf4j
@Component
public class PopCashAdvanceRemote {

    @Reference
    private PopCashAdvanceApi popCashAdvanceApi;

    public PageInfo<PopCashAdvanceDto> queryCashAdvancePage(CashAdvanceQuery cashAdvanceQuery) throws ServiceException {
        try {
            log.info("PopCashAdvanceRemote.queryCashAdvancePage#cashAdvanceQuery:{}", JSON.toJSONString(cashAdvanceQuery));
            ApiRPCResult<PageInfo<PopCashAdvanceDto>> result = popCashAdvanceApi.queryCashAdvancePage(cashAdvanceQuery);
            log.info("PopCashAdvanceRemote.queryCashAdvancePage#cashAdvanceQuery:{} return {}", JSON.toJSONString(cashAdvanceQuery), JSON.toJSONString(result));
            if (result.isSuccess()) return result.getData();
            throw new ServiceException("查询提现记录失败");
        } catch (Exception e) {
            log.error("PopCashAdvanceRemote.queryCashAdvancePage#cashAdvanceQuery:{} 异常", JSON.toJSONString(cashAdvanceQuery), e);
            throw new ServiceException("查询提现记录失败");
        }
    }

    public BigDecimal sumAmount(CashAdvanceQuery query) throws ServiceException {
        try {
            log.info("PopCashAdvanceRemote.sumAmount#query:{}", JSON.toJSONString(query));
            ApiRPCResult<BigDecimal> result = popCashAdvanceApi.sumAmount(query);
            log.info("PopCashAdvanceRemote.sumAmount#query:{} return {}", JSON.toJSONString(query), JSON.toJSONString(result));
            if (result.isSuccess()) return result.getData();
            throw new ServiceException("查询提现记录失败");
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("PopCashAdvanceRemote.sumAmount#query:{} 异常", JSON.toJSONString(query), e);
            throw new ServiceException("查询提现记录失败");
        }
    }

    public PopCashAdvanceDto findByCashAdvanceNum(String cashAdvanceNum) {
        try {
            log.info("PopCashAdvanceRemote.findByCashAdvanceNum#cashAdvanceNum:{}", cashAdvanceNum);
            ApiRPCResult<PopCashAdvanceDto> result = popCashAdvanceApi.findByCashAdvanceNum(cashAdvanceNum,null);
            log.info("PopCashAdvanceRemote.findByCashAdvanceNum#cashAdvanceNum:{} return {}", cashAdvanceNum, JSON.toJSONString(result));
            return result.getData();
        } catch (Exception e) {
            log.error("PopCashAdvanceRemote.findByCashAdvanceNum#cashAdvanceNum:{}, 异常", cashAdvanceNum, e);
            return null;
        }
    }

    public PageInfo<PopCashAdvanceDetailDto> queryCashAdvanceDetailPage(CashAdvanceDetailQuery query) throws ServiceException {
        try {
            log.info("PopCashAdvanceRemote.queryCashAdvanceDetailPage#query:{}", JSON.toJSONString(query));
            ApiRPCResult<PageInfo<PopCashAdvanceDetailDto>> result = popCashAdvanceApi.queryCashAdvanceDetailPage(query);
            log.info("PopCashAdvanceRemote.queryCashAdvanceDetailPage#query:{} return {}", JSON.toJSONString(query), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            throw new ServiceException(result.getErrMsg());
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("PopCashAdvanceRemote.queryCashAdvanceDetailPage#query:{} 异常", JSON.toJSONString(query), e);
            throw new ServiceException("查询提现明细失败");
        }
    }

    public PageInfo<PopCashAdvanceDetailDto> queryCashAdvanceDetailExportPage(CashAdvanceQuery query) throws ServiceException {
        try {
            log.info("PopCashAdvanceRemote.queryCashAdvanceDetailExportPage#query:{}", JSON.toJSONString(query));
            ApiRPCResult<PageInfo<PopCashAdvanceDetailDto>> result = popCashAdvanceApi.queryCashAdvanceDetailExportPage(query);
            log.info("PopCashAdvanceRemote.queryCashAdvanceDetailExportPage#query:{} return {}", JSON.toJSONString(query), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            throw new ServiceException(result.getErrMsg());
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("PopCashAdvanceRemote.queryCashAdvanceDetailExportPage#query:{} 异常", JSON.toJSONString(query), e);
            throw new ServiceException("查询提现明细失败");
        }
    }

    public boolean auditFail(List<Integer> ids, String updateBy, String reason) throws ServiceException {
        try {
            log.info("PopCashAdvanceRemote.auditFail#ids:{},updateBy:{},reason:{}", JSON.toJSONString(ids), updateBy, reason);
            ApiRPCResult result = popCashAdvanceApi.auditFail(ids, updateBy, reason);
            log.info("PopCashAdvanceRemote.auditFail#ids:{},updateBy:{},reason:{} return {}", JSON.toJSONString(ids), updateBy, reason, JSON.toJSONString(result));
            if(result.isSuccess()){
                return true;
            }
            throw new ServiceException(result.getErrMsg());
        }catch (ServiceException e){
            throw e;
        }catch (Exception e) {
            log.error("PopCashAdvanceRemote.auditFail#ids:{},updateBy:{},reason:{} 异常", JSON.toJSONString(ids), updateBy, reason, e);
            return false;
        }
    }

    public PopCashAdvanceDto getLastCashAdvance(String orgId){
        try {
            log.info("PopCashAdvanceRemote.getLastCashAdvance#orgId:{}", orgId);
            ApiRPCResult<PopCashAdvanceDto> lastCashAdvance = popCashAdvanceApi.getLastCashAdvance(orgId);
            log.info("PopCashAdvanceRemote.getLastCashAdvance#orgId:{} return {}", orgId, JSON.toJSONString(lastCashAdvance));
            if (lastCashAdvance.isSuccess()) {
                return lastCashAdvance.getData();
            }
        } catch (Exception e) {
            log.error("PopCashAdvanceRemote.getLastCashAdvance#orgId:{} 异常", orgId, e);
        }
        return null;
    }

    public List<PopCashAdvanceLogDto> getCashAdvanceLog(Long cashAdvanceId){
        try {
            ApiRPCResult<List<PopCashAdvanceLogDto>> apiRPCResult = popCashAdvanceApi.getCashAdvanceLog(cashAdvanceId);
            log.info("PopCashAdvanceRemote.getCashAdvanceLog#cashAdvanceId:{} return {}", cashAdvanceId, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult.isSuccess()) {
                return apiRPCResult.getData();
            }
        } catch (Exception e) {
            log.error("PopCashAdvanceRemote.getCashAdvanceLog#cashAdvanceId:{} 异常", cashAdvanceId, e);
        }
        return null;
    }
}
