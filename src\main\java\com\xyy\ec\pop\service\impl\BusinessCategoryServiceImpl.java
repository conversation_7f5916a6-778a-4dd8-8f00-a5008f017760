package com.xyy.ec.pop.service.impl;

import com.xyy.ec.pop.remote.BusinessCategoryRemoteAdapter;
import com.xyy.ec.pop.server.api.merchant.dto.BusinessScopeDto;
import com.xyy.ec.pop.service.BusinessCategoryService;
import com.xyy.ec.pop.vo.EcErpCategoryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @version v1
 * @Description 查询经营类目
 * <AUTHOR>
 */
@Service
@Slf4j
public class BusinessCategoryServiceImpl implements BusinessCategoryService {
    @Autowired
    private BusinessCategoryRemoteAdapter businessCategoryRemoteAdapter;
    @Override
    public List<EcErpCategoryVo> getValidBusinessScope() {
        List<BusinessScopeDto> businessScope= businessCategoryRemoteAdapter.getBusinessScope();
        List<EcErpCategoryVo> list = convertToCategoryVo(businessScope);
        return list;
    }

    private List<EcErpCategoryVo> convertToCategoryVo(List<BusinessScopeDto> businessScope) {
        if(CollectionUtils.isEmpty(businessScope)){
            return new ArrayList<>(0);
        }
        List<EcErpCategoryVo> list = new ArrayList<>(businessScope.size());
        for(BusinessScopeDto dto:businessScope){
            EcErpCategoryVo vo = new EcErpCategoryVo();
            vo.setId(dto.getId());
            vo.setLevel(dto.getLevelNode().longValue());
            vo.setPid(dto.getParentId().intValue()<0?"0":dto.getParentId().toString());
            vo.setName(dto.getDictName());
            vo.setIsValid((byte)1);
            vo.setIsShow("1");
            vo.setSonCategoryList(convertToCategoryVo(dto.getChildDictionaryList()));

            list.add(vo);
        }
        return list;
    }
}
