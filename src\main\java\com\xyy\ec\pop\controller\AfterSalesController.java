package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesSellerParamDto;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.XyyConfig;
import com.xyy.ec.pop.config.XyyPopBaseConfig;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.remote.EcOrderRemote;
import com.xyy.ec.pop.remote.PopCorporationRemoteAdapter;
import com.xyy.ec.pop.remote.ProductSkuRemoteAdapter;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.export.param.AfterSalesExportAdminParam;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuAdminApi;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuCategoryAdminApi;
import com.xyy.ec.pop.service.AfterSalesServiceV2;
import com.xyy.ec.pop.service.BranchService;
import com.xyy.ec.pop.service.PopRefundOrderService;
import com.xyy.ec.pop.service.domains.OrderDomainService;
import com.xyy.ec.pop.vo.*;
import com.xyy.ec.pop.vo.afterSales.AfterSaleQueryParamVo;
import com.xyy.ec.pop.vo.afterSales.AfterSalesLogisticsVo;
import com.xyy.ec.pop.vo.afterSales.AfterSalesVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 退款单管理
 * <AUTHOR>
 */
@Controller
@Slf4j
@RequestMapping("/afterSale")
public class AfterSalesController extends BaseController {
    @Autowired
    private AfterSalesServiceV2 afterSaleServiceV2;
    @Autowired
    private DownloadRemote downloadRemote;



    /**
     * 售后列表
     *
     * @param param
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseVo<PageInfo<AfterSalesVo>> queryPage(@RequestBody AfterSaleQueryParamVo param) {
        try {
            log.info("queryPage#param:{}", JSON.toJSONString(param));
            if (param == null || param.getPageNo() == null || param.getPageNo() < 1 || param.getPageSize() == null || param.getPageSize() < 1) {
                return ResponseVo.errRest("请检查分页参数");
            }

            PageInfo<AfterSalesVo> info = afterSaleServiceV2.queryPage(param);
            return ResponseVo.successResult(info);
        } catch (Exception e) {
            log.error("queryRefundOrderPage#error. param:{}", JSON.toJSONString(param), e);
            return ResponseVo.errRest("查询退款单列表失败");
        }
    }

    /**
     * 导出列表
     *
     * @param query
     */
    @PostMapping(value = "/export")
    @ResponseBody
    public ResponseVo<Boolean> exportAfterSales(@RequestBody AfterSalesExportAdminParam query) {
        try {
            log.info("exportAfterSales:{}", JSON.toJSONString(query));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(query)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.ADMIN_AFTER_SALES_ORDER)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("exportAfterSales result:{} return {}", JSON.toJSONString(query), b);
            return ResponseVo.successResult(b);
        } catch (Exception e) {
            log.error("exportAfterSales error:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 售后详情
     *
     * @param afterSalesNo
     * @return
     */
    @GetMapping(value = "/detail")
    @ResponseBody
    public ResponseVo<Map<String,Object>> afterSalesDetail(String afterSalesNo) {
        try {
            if (StringUtils.isBlank(afterSalesNo)) {
                return ResponseVo.errRest("退款单编号不能为空");
            }
            log.info("afterSalesDetail#refundOrderNo:{}", afterSalesNo);
            Map<String,Object> afterSalesDetailVo = afterSaleServiceV2.queryAfterSalesDetail(afterSalesNo);
            log.info("afterSalesDetail#refundOrderNo:{},result:{}", afterSalesNo, JSON.toJSONString(afterSalesDetailVo));
            return ResponseVo.successResult(afterSalesDetailVo);
        } catch (Exception e) {
            log.error("afterSalesDetail#error. afterSalesNo:{}", afterSalesNo, e);
            return ResponseVo.errRest("查询详情失败");
        }
    }

    /**
     * 售后退款根据状态查询对应的数量
     *
     * @return
     */
    @PostMapping("/queryStatusCount")
    @ResponseBody
    public ResponseVo<Map<String,Integer>> queryStatusCount(@RequestBody AfterSaleQueryParamVo param) {
        try {
            log.info("queryStatusCount:{}", JSON.toJSONString(param));
//            CorporationDto corporationDto = corporationRemote.queryCorpBaseByOrgId(getCurrentOrgId());
            Map<String, Integer> result = afterSaleServiceV2.queryAfterSalesStatusCount(param);
            log.info("queryStatusCount:result:{}",JSON.toJSONString(result));
            return ResponseVo.successResult(result);
        } catch (Exception e) {
            log.error("queryStatusCount:{}", JSON.toJSONString(param), e);
            return ResponseVo.errRest("查询售后状态数量失败");
        }
    }

    /**
     * 保存卖家备注
     *
     * @param param
     * @return
     */
    @PostMapping("/saveSellerRemark")
    @ResponseBody
    public ResponseVo saveSellerRemark(@RequestBody AfterSalesSellerParamDto param) {
        try {
            log.info("saveSellerRemark:{}", JSON.toJSONString(param));
            afterSaleServiceV2.saveSellerRemark(param);
            return ResponseVo.successResult(null);
        } catch (Exception e) {
            log.error("saveSellerRemark:{}", JSON.toJSONString(param), e);
            return ResponseVo.errRest("保存失败");
        }
    }
    /**
     * 查看退货物流
     *
     * @return
     */
    @PostMapping("/queryLogistics")
    @ResponseBody
    public ResponseVo<AfterSalesLogisticsVo> queryLogistics(@RequestBody AfterSaleQueryParamVo param) {
        try {
            log.info("queryLogistics:{}", param.getAfterSalesNo());
            final AfterSalesLogisticsVo logisticsVo = afterSaleServiceV2.queryLogistics(param.getAfterSalesNo());
            return ResponseVo.successResult(logisticsVo);
        } catch (Exception e) {
            log.error("queryLogistics:{}", param.getAfterSalesNo(), e);
            return ResponseVo.errRest("查询失败");
        }
    }


}
