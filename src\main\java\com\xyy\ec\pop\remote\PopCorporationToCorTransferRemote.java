package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.merchant.api.admin.ToCorTransferApi;
import com.xyy.ec.pop.server.api.merchant.dto.PopCorporationToCorTransferDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description 对公打款
 * <AUTHOR>
 * @Date 2021/10/12
 */
@Component
@Slf4j
public class PopCorporationToCorTransferRemote {
    @Reference
    private ToCorTransferApi corTransferApi;

    public PopCorporationToCorTransferDto getTransfer(String orgId) {
        try {
            log.info("PopCorporationToCorTransferRemote.getTransfer#orgId:{}", JSON.toJSONString(orgId));

            ApiRPCResult<PopCorporationToCorTransferDto> result = corTransferApi.getTransfer(orgId);
            log.info("PopCorporationToCorTransferRemote.getTransfer#orgId:{} return {}", JSON.toJSONString(orgId), JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("PopCorporationToCorTransferRemote.getTransfer#orgId:{} 异常", JSON.toJSONString(orgId), e);
            return null;
        }
    }
    public PopCorporationToCorTransferDto getTransferWithDefaultBankInfo(String orgId) {
        try {
            log.info("PopCorporationToCorTransferRemote.getTransferWithDefaultBankInfo#orgId:{}", JSON.toJSONString(orgId));

            ApiRPCResult<PopCorporationToCorTransferDto> result = corTransferApi.getTransferWithDefaultBankInfo(orgId);
            log.info("PopCorporationToCorTransferRemote.getTransferWithDefaultBankInfo#orgId:{} return {}", JSON.toJSONString(orgId), JSON.toJSONString(result));
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.error("PopCorporationToCorTransferRemote.getTransferWithDefaultBankInfo#orgId:{} 异常", JSON.toJSONString(orgId), e);
            return null;
        }
    }

    public boolean updateToCorTransfer(PopCorporationToCorTransferDto dto) {
        try {
            log.info("PopCorporationToCorTransferRemote.updateToCorTransfer#dto:{}", JSON.toJSONString(dto));
            ApiRPCResult<Boolean> result = corTransferApi.updateToCorTransfer(dto);
            log.info("PopCorporationToCorTransferRemote.updateToCorTransfer#dto:{} return {}", JSON.toJSONString(dto), JSON.toJSONString(result));
            return result.isSuccess();
        } catch (Exception e) {
            log.error("PopCorporationToCorTransferRemote.updateToCorTransfer#dto:{} 异常", JSON.toJSONString(dto), e);
            return false;
        }
    }
}
