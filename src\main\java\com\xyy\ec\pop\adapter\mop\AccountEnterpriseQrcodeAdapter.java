package com.xyy.ec.pop.adapter.mop;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.remote.AccountEnterpriseQrcodeRemote;
import com.xyy.pop.mop.api.remote.result.AccountEnterpriseQrcodeBasicDTO;
import com.xyy.scm.constant.entity.pagination.Paging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AccountEnterpriseQrcodeAdapter implements MopBaseAdapter {
    @Reference(version = "1.0.0")
    private AccountEnterpriseQrcodeRemote accountEnterpriseQrcodeRemote;

    public ResponseVo<Paging<AccountEnterpriseQrcodeBasicDTO>> listAccountEnterpriseQrcode(AccountEnterpriseQrcodeBasicDTO qrcodeBasicDTO) {
        return to(()-> accountEnterpriseQrcodeRemote.listAccountEnterpriseQrcode(qrcodeBasicDTO));
    }
}
