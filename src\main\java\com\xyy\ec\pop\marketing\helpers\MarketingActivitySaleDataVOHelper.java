package com.xyy.ec.pop.marketing.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.order.business.config.OrderEnum;
import com.xyy.ec.pop.marketing.vo.MarketingActivitySaleDataVO;
import com.xyy.ec.pop.utils.MobileDesensitizationUtils;
import com.xyy.ms.promotion.business.result.MarketingActivitySaleDataDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class MarketingActivitySaleDataVOHelper {

    public static MarketingActivitySaleDataVO create(MarketingActivitySaleDataDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        MarketingActivitySaleDataVO vo = MarketingActivitySaleDataVO.builder()
                .csuId(dto.getCsuId())
                .originalCsuBarcode(dto.getOriginalCsuBarcode())
                .csuOriginalPrice(dto.getCsuOriginalPrice())
                .buyUnitPrice(dto.getBuyUnitPrice())
                .buyNum(dto.getBuyNum())
                .refundNum(dto.getRefundNum())
                .receiptNum(dto.getReceiptNum())
                .totalAmount(dto.getTotalAmount())
                .discountAmount(dto.getDiscountAmount())
                .actualAmount(dto.getActualAmount())
                .orderNo(dto.getOrderNo())
                .merchantId(dto.getMerchantId())
                .provinceCode(dto.getProvinceCode())
                .consignee(dto.getConsignee())
                .consigneeMobile(dto.getConsigneeMobile())
                .consigneeAddress(dto.getConsigneeAddress())
                .orderCreateTime(dto.getOrderCreateTime())
                .orderStatus(dto.getOrderStatus())
                .build();

        // 手机号脱敏处理
        String consigneeMobile = vo.getConsigneeMobile();
        consigneeMobile = MobileDesensitizationUtils.desensitizeMobilePhoneNumber(consigneeMobile);
        vo.setConsigneeMobile(consigneeMobile);

        Date orderCreateTime = vo.getOrderCreateTime();
        String orderCreateTimeStr = "";
        if (Objects.nonNull(orderCreateTime)) {
            orderCreateTimeStr = DateFormatUtils.format(orderCreateTime, "yyyy-MM-dd HH:mm:ss");
        }
        vo.setOrderCreateTimeStr(orderCreateTimeStr);

        Integer orderStatus = vo.getOrderStatus();
        String orderStatusName = "";
        if (Objects.nonNull(orderStatus)) {
            for (OrderEnum.OrderStatus orderStatusEnum : OrderEnum.OrderStatus.values()) {
                if (Objects.equals(orderStatusEnum.getId(), orderStatus)) {
                    orderStatusName = orderStatusEnum.getValue();
                }
            }
        }
        vo.setOrderStatusName(orderStatusName);

        return vo;
    }

    public static List<MarketingActivitySaleDataVO> creates(List<MarketingActivitySaleDataDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Lists.newArrayList();
        }
        return dtoList.stream()
                .map(MarketingActivitySaleDataVOHelper::create)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

}
