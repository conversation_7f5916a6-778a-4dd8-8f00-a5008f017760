package com.xyy.ec.pop.controller;

import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.remote.ProductSkuRemoteAdapter;
import com.xyy.ec.pop.vo.ResponseVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/inner/query")
public class InnerQueryController extends BaseController {
    @Autowired
    private ProductSkuRemoteAdapter productSkuRemoteAdapter;

    @RequestMapping("/queryOriginBarcodes")
    @ResponseBody
    public Object queryOriginBarcodes(String barcodes) {
        if (StringUtils.isBlank(barcodes)){
            return ResponseVo.errRest("参数不能为空");
        }
        try {
            return ResponseVo.successResult(productSkuRemoteAdapter.queryOriginBarcodes(Arrays.stream(barcodes.split(",")).distinct().map(String::trim).filter(StringUtils::isNotBlank).collect(Collectors.toList())));
        }catch (Exception e){
            return ResponseVo.errRest("查询失败");
        }
    }
}
