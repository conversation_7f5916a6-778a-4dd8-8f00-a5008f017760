package com.xyy.ec.pop.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.remote.ShopAdminRemoteService;
import com.xyy.ec.shop.server.business.api.ShopAdminApi;
import com.xyy.ec.shop.server.business.enums.ShopPatternEnum;
import com.xyy.ec.shop.server.business.results.ShopCodeAndNameDTO;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.*;

@Service
public class ShopAdminRemoteServiceImpl implements ShopAdminRemoteService {

    /**
     * 默认分页大小：根据店铺编码集合查询店铺后台名称列表
     */
    private static final Integer DEFAULT_PAGE_SIZE_QUERY_SHOP_NAME_BY_SHOP_CODES = 200;

    /**
     * 默认分页大小：根据店铺后台名称集合查询店铺编码列表
     */
    private static final Integer DEFAULT_PAGE_SIZE_QUERY_SHOP_CODE_BY_NAMES = 200;

    /**
     * 最大分页大小：根据店铺编码列表批量查询店铺信息
     */
    private static final Integer MAX_SIZE_QUERY_SHOP_INFO_BY_SHOP_CODES = 200;

    @Reference(version = "1.0.0")
    private ShopAdminApi shopAdminApi;

    @Override
    public Collection<ShopInfoDTO> queryShopInfoByShopCodes(Collection<String> shopCodes) {
        if (CollectionUtils.isEmpty(shopCodes)) {
            return Lists.newArrayList();
        }
        HashSet<String> tempSet = Sets.newHashSet(shopCodes);
        tempSet.remove(null);
        tempSet.remove("");
        List<String> shopCodeList = Lists.newArrayList(tempSet);
        if (CollectionUtils.isEmpty(shopCodeList)) {
            return Lists.newArrayList();
        }
        Collection<ShopInfoDTO> result = Lists.newArrayListWithExpectedSize(shopCodeList.size());
        List<List<String>> shopCodeListPartitions = Lists.partition(shopCodeList, MAX_SIZE_QUERY_SHOP_INFO_BY_SHOP_CODES);
        ApiRPCResult<List<ShopInfoDTO>> apiRPCResult;
        List<ShopInfoDTO> shopInfoDTOS;
        for (List<String> shopCodeListPartition : shopCodeListPartitions) {
            apiRPCResult = shopAdminApi.queryInfoByShopCodes(shopCodeListPartition);
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
                String message = MessageFormat.format("根据店铺编码集合查询店铺信息列表失败，入参，shopCodeListPartition：{0}，出参：{1}",
                        JSONArray.toJSONString(shopCodeListPartition), JSONObject.toJSONString(apiRPCResult));
                throw new PopAdminException(message, XyyJsonResultCodeEnum.SHOP_REMOTE_ERROR);
            }
            shopInfoDTOS = apiRPCResult.getData();
            if (CollectionUtils.isNotEmpty(shopInfoDTOS)) {
                result.addAll(shopInfoDTOS);
            }
        }
        return result;
    }

    @Override
    public Collection<ShopCodeAndNameDTO> queryShopCodeAndNamesByShopCodes(Collection<String> shopCodes) {
        if (CollectionUtils.isEmpty(shopCodes)) {
            return Lists.newArrayList();
        }
        HashSet<String> tempSet = Sets.newHashSet(shopCodes);
        tempSet.remove(null);
        tempSet.remove("");
        List<String> shopCodeList = Lists.newArrayList(tempSet);
        if (CollectionUtils.isEmpty(shopCodeList)) {
            return Lists.newArrayList();
        }
        List<ShopCodeAndNameDTO> result = Lists.newArrayListWithExpectedSize(shopCodeList.size());
        List<List<String>> shopCodeListPartitions = Lists.partition(shopCodeList, DEFAULT_PAGE_SIZE_QUERY_SHOP_NAME_BY_SHOP_CODES);
        ApiRPCResult<Collection<ShopCodeAndNameDTO>> apiRPCResult;
        Collection<ShopCodeAndNameDTO> shopCodeAndNameDTOS;
        for (List<String> shopCodeListPartition : shopCodeListPartitions) {
            apiRPCResult = shopAdminApi.queryShopCodeAndNameByShopCodes(shopCodeListPartition);
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
                String message = MessageFormat.format("入参，shopCodeListPartition：{0}，出参：{1}",
                        JSONArray.toJSONString(shopCodeListPartition), JSONObject.toJSONString(apiRPCResult));
                throw new PopAdminException(message, XyyJsonResultCodeEnum.SHOP_REMOTE_ERROR);
            }
            shopCodeAndNameDTOS = apiRPCResult.getData();
            if (CollectionUtils.isNotEmpty(shopCodeAndNameDTOS)) {
                result.addAll(shopCodeAndNameDTOS);
            }
        }
        return result;
    }

    @Override
    public Collection<String> queryNameByShopCodes(Collection<String> shopCodes) {
        if (CollectionUtils.isEmpty(shopCodes)) {
            return Lists.newArrayList();
        }
        Set<String> tempSet = Sets.newHashSet(shopCodes);
        tempSet.remove(null);
        tempSet.remove("");
        List<String> shopCodeList = Lists.newArrayList(tempSet);
        if (CollectionUtils.isEmpty(shopCodeList)) {
            return Lists.newArrayList();
        }
        Collection<String> result = Lists.newArrayListWithExpectedSize(shopCodeList.size());
        List<List<String>> shopCodeListPartitions = Lists.partition(shopCodeList, DEFAULT_PAGE_SIZE_QUERY_SHOP_NAME_BY_SHOP_CODES);
        ApiRPCResult<Collection<ShopCodeAndNameDTO>> apiRPCResult;
        Collection<ShopCodeAndNameDTO> shopCodeAndNameDTOS;
        for (List<String> shopCodeListPartition : shopCodeListPartitions) {
            apiRPCResult = shopAdminApi.queryShopCodeAndNameByShopCodes(shopCodeListPartition);
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
                String message = MessageFormat.format("入参，shopCodeListPartition：{0}，出参：{1}",
                        JSONArray.toJSONString(shopCodeListPartition), JSONObject.toJSONString(apiRPCResult));
                throw new PopAdminException(message, XyyJsonResultCodeEnum.SHOP_REMOTE_ERROR);
            }
            shopCodeAndNameDTOS = apiRPCResult.getData();
            if (CollectionUtils.isNotEmpty(shopCodeAndNameDTOS)) {
                shopCodeAndNameDTOS.stream()
                        .filter(item -> item != null && StringUtils.isNotEmpty(item.getName()))
                        .forEach(item -> result.add(item.getName()));
            }
        }
        return result;
    }

    @Override
    public Collection<String> queryShopCodeByNames(Collection<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return Lists.newArrayList();
        }
        Set<String> tempSet = Sets.newHashSet(names);
        tempSet.remove(null);
        tempSet.remove("");
        List<String> nameList = Lists.newArrayList(tempSet);
        if (CollectionUtils.isEmpty(nameList)) {
            return Lists.newArrayList();
        }
        Collection<String> result = Lists.newArrayListWithExpectedSize(nameList.size());
        List<List<String>> nameListPartitions = Lists.partition(nameList, DEFAULT_PAGE_SIZE_QUERY_SHOP_CODE_BY_NAMES);
        ApiRPCResult<Collection<ShopCodeAndNameDTO>> apiRPCResult;
        Collection<ShopCodeAndNameDTO> shopCodeAndNameDTOS;
        for (List<String> nameListPartition : nameListPartitions) {
            apiRPCResult = shopAdminApi.queryShopCodeAndNameByNames(nameListPartition);
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
                String message = MessageFormat.format("入参，nameListPartition：{0}，出参：{1}",
                        JSONArray.toJSONString(nameListPartition), JSONObject.toJSONString(apiRPCResult));
                throw new PopAdminException(message, XyyJsonResultCodeEnum.SHOP_REMOTE_ERROR);
            }
            shopCodeAndNameDTOS = apiRPCResult.getData();
            if (CollectionUtils.isNotEmpty(shopCodeAndNameDTOS)) {
                shopCodeAndNameDTOS.stream()
                        .filter(item -> item != null && StringUtils.isNotEmpty(item.getShopCode()))
                        .forEach(item -> result.add(item.getShopCode()));
            }
        }
        return result;
    }

    @Override
    public Map<String, ShopInfoDTO> queryECBranchShopCode(List<String> branchCodeList) {
        Map<String,ShopInfoDTO> map = Maps.newHashMap();
        if (CollectionUtils.isEmpty(branchCodeList)) {
            return map;
        }
        ApiRPCResult<List<ShopInfoDTO>> listShopInfoDtoByBranchCodes = shopAdminApi.getListShopInfoDtoByBranchCodes(branchCodeList, "1", ShopPatternEnum.YBM.getCode());
        if (CollectionUtils.isEmpty(listShopInfoDtoByBranchCodes.getData())) {
            return map;
        }
        listShopInfoDtoByBranchCodes.getData().stream().forEach(shopInfoDTO -> map.put(shopInfoDTO.getBranchCode(),shopInfoDTO));
        return map;
    }
}
