package com.xyy.ec.pop.marketing.helpers;

import com.google.common.collect.Sets;
import com.xyy.ec.pop.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.marketing.param.GroupBuyingListExportParam;
import com.xyy.ms.promotion.business.common.config.IsTrueEnum;
import com.xyy.ms.promotion.business.common.constants.ActivityReportEnum;
import com.xyy.ms.promotion.business.enums.MarketingActivityCreateSourceEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Objects;
import java.util.Set;

/**
 * {@link GroupBuyingListExportParam} 帮助类
 *
 * <AUTHOR>
 */
public class GroupBuyingListExportParamHelper {

    /**
     * 若有必要去除字符串前后空串
     *
     * @param queryParam
     */
    public static void trimIfNecessary(GroupBuyingListExportParam queryParam) {
        if (Objects.isNull(queryParam)) {
            return;
        }
        if (StringUtils.isNotEmpty(queryParam.getOrgId())) {
            queryParam.setOrgId(queryParam.getOrgId().trim());
        }
        if (StringUtils.isNotEmpty(queryParam.getOrgName())) {
            queryParam.setOrgName(queryParam.getOrgName().trim());
        }
        if (StringUtils.isNotEmpty(queryParam.getActIdOrReportId())) {
            queryParam.setActIdOrReportId(queryParam.getActIdOrReportId().trim());
        }
        if (StringUtils.isNotEmpty(queryParam.getShopName())) {
            queryParam.setShopName(queryParam.getShopName().trim());
        }
        if (StringUtils.isNotEmpty(queryParam.getActIdOrReportId())) {
            queryParam.setActIdOrReportId(queryParam.getActIdOrReportId().trim());
        }
        if (StringUtils.isNotEmpty(queryParam.getCsuId())) {
            queryParam.setCsuId(queryParam.getCsuId().trim());
        }
        if (StringUtils.isNotEmpty(queryParam.getBarcode())) {
            queryParam.setBarcode(queryParam.getBarcode().trim());
        }
        if (StringUtils.isNotEmpty(queryParam.getProductCode())) {
            queryParam.setProductCode(queryParam.getProductCode().trim());
        }
        if (StringUtils.isNotEmpty(queryParam.getProductName())) {
            queryParam.setProductName(queryParam.getProductName().trim());
        }
        if (StringUtils.isNotEmpty(queryParam.getTheme())) {
            queryParam.setTheme(queryParam.getTheme().trim());
        }
        if (StringUtils.isNotEmpty(queryParam.getTopics())) {
            queryParam.setTopics(queryParam.getTopics().trim());
        }
    }

    /**
     * 校验
     *
     * @param queryParam
     * @return
     */
    public static Boolean validate(GroupBuyingListExportParam queryParam) {
        if (Objects.isNull(queryParam)) {
            String msg = "参数非法";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (Objects.isNull(queryParam.getActStartTime())) {
            String msg = "请选择活动开始时间";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (Objects.isNull(queryParam.getActEndTime())) {
            String msg = "请选择活动截止时间";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (!queryParam.getActEndTime().after(queryParam.getActStartTime())) {
            String msg = "活动截止时间须晚于开始时间";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (DateUtils.addMonths(queryParam.getActStartTime(), 3).before(queryParam.getActEndTime())) {
            String msg = "活动时间间隔最大选择3个月";
            throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (StringUtils.isNotEmpty(queryParam.getActIdOrReportId())) {
            if (!NumberUtils.isDigits(queryParam.getActIdOrReportId())) {
                String msg = "活动id参数非法";
                throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }
        }
        if (StringUtils.isNotEmpty(queryParam.getCsuId())) {
            if (!NumberUtils.isDigits(queryParam.getCsuId())) {
                String msg = "csuid参数非法";
                throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }
        }
        if (Objects.nonNull(queryParam.getSourceType())) {
            MarketingActivityCreateSourceEnum marketingActivityCreateSourceEnum = MarketingActivityCreateSourceEnum
                    .valueOfCustom(queryParam.getSourceType());
            if (Objects.isNull(marketingActivityCreateSourceEnum)) {
                String msg = "活动来源参数非法";
                throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }
        }
        if (Objects.nonNull(queryParam.getStatus())) {
            if (!Objects.equals(queryParam.getStatus(), 7)) {
                ActivityReportEnum.ActReportIndexStatusEnum statusEnum = ActivityReportEnum.ActReportIndexStatusEnum
                        .getByType(queryParam.getStatus());
                if (Objects.isNull(statusEnum)) {
                    String msg = "状态参数非法";
                    throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
                }
            }
        }
        Set<Integer> yesNoValueSet = Sets.newHashSet(0, 1);
        if (Objects.nonNull(queryParam.getIsPlatformSubsidy())) {
            if (!yesNoValueSet.contains(queryParam.getIsPlatformSubsidy())) {
                String msg = "是否是平台补贴参数非法";
                throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }
        }
        if (Objects.nonNull(queryParam.getIsVirtualShop())) {
            if (!yesNoValueSet.contains(queryParam.getIsVirtualShop())) {
                String msg = "是否是虚拟供应商参数非法";
                throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }
        }
        if (Objects.nonNull(queryParam.getStepPriceStatus())) {
            if (!Objects.equals(IsTrueEnum.YES.getType(), queryParam.getStepPriceStatus())
                    && !Objects.equals(IsTrueEnum.NO.getType(), queryParam.getStepPriceStatus())) {
                String msg = "是否阶梯价参数非法";
                throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }
        }
        if (Objects.nonNull(queryParam.getIsOrder())) {
            if (!Objects.equals(IsTrueEnum.YES.getType(), queryParam.getIsOrder())
                    && !Objects.equals(IsTrueEnum.NO.getType(), queryParam.getIsOrder())) {
                String msg = "是否成团参数非法";
                throw new PopAdminException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }
        }
        return true;
    }

}
