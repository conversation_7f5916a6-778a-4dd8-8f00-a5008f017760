package com.xyy.ec.pop.utils;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;

import java.util.List;

public class PageInfoUtils {
    public  static <T> PageInfo<T> pageInfo(List<T> detailPos, int total, int pageNum, int pageSize) {
        if(pageSize<1) pageSize = 1;
        PageInfo pageInfo = new PageInfo(detailPos);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages(total/pageSize+(total%pageSize==0?0:1));
        return pageInfo;
    }

    public static <T> PageInfo<T> pack(List<T> list, Page<T> page) {
        PageInfo<T> pageInfo = new PageInfo<>(list);
        if (page != null) {
            pageInfo.setPages(page.getPages());
            pageInfo.setTotal(pageInfo.getTotal());
        }
        return pageInfo;
    }

    public static <T> PageInfo<T> pack(List<T> list, PageInfo page) {
        PageInfo<T> pageInfo = new PageInfo<>(list);
        if (page != null) {
            pageInfo.setPages(page.getPages());
            pageInfo.setTotal(pageInfo.getTotal());
        }
        return pageInfo;
    }
}
