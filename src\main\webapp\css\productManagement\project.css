html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, menu, nav, output, ruby, section, summary, time, mark, audio, video{
    margin: 0;
    padding: 0;
    border: 0;
    text-decoration: none;
    font: normal;
    font-size: 12px;
    font-family: "Microsoft YaHei", "微软雅黑";
}

.red{
    color: red;
}
.blue {
    color: #089AD6;
}

.green {
    color: #0EAF12;
}

.green2 {
    color: #67A462;
}

.orange {
    color: #FF5000;
}
input,textarea,select{
    color: #555;
    background-color: #fff;
    background-image: none;
    padding-left: 6px;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    -webkit-transition: border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
}
input:focus,textarea:focus,select:focus{
    border: 1px solid  #0590CD;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
}
input:focus,textarea:focus{
    background-color:#ebf8ff;
}
.xmlb_list_left{
    float: left;
    width: 100%;
    height: 600px;
    border: 1px solid #e9e9e9;
}
.xmlb_list_left div{

}
.xmlb_list_left_title{
    width: 100%;
    height: 50px;
    line-height: 50px;
}
.xmlb_list_left_title img{
    margin-left: 30px;

}
.xmlb_list_left_header{
    width: 95%;
    height: 40px;
    line-height: 40px;
    margin: 0 auto;
    background-color: #F7F7F7;
    border: 1px solid #e9e9e9;
}
.xmlb_list_left_table_up{
    width:95%;
    width:calc(95% + 2px);
    margin: 0 auto;
    margin-top: 10px;
    height: 40px;
}
.xmlb_list_left_table_up table{
    width:100% ;
    margin: 0 auto;
    border: 1px solid #e9e9e9;
    float: left;
}
.xmlb_list_left_table_up table tr th{
    text-align: center;
    background-color: #F0F0F0;
    height: 40px;
    line-height: 40px;
    border: 1px solid #e9e9e9;
    background: linear-gradient(#fefefe, #ededed);
}
.xmlb_list_left_table_up table tr th:nth-child(1){
    width: 10%;
}
.xmlb_list_left_table_up table tr th:nth-child(2){
    width: 30%;
}
.xmlb_list_left_table_up table tr th:nth-child(3){
    width: 20%;
}
.xmlb_list_left_table_up table tr th:nth-child(4){
    width: 40%;
}
.xmlb_list_left_table_down{
    width:95%;
    width:calc(95% + 2px);
    margin: 0 auto;
    height: 450px;
    background-color: #FCFCFC;
    overflow: hidden;
    overflow-x:scroll ;
}
.xmlb_list_left_table_down table{
    width:100%;
    margin: 0 auto;
    border: 1px solid #e9e9e9;
}
.xmlb_list_left_table_down table tr td{
    text-align: center;
    height: 40px;
    line-height: 40px;
    border: 1px solid #f1f1f1;
}
.xmlb_list_left_table_down table tr td:nth-child(1){
    width: 10%;
}
.xmlb_list_left_table_down table tr td:nth-child(2){
    width: 30%;
}
.xmlb_list_left_table_down table tr td:nth-child(3){
    width: 20%;
}
.xmlb_list_left_table_down table tr td:nth-child(4){
    width: 40%;
}
.xmlb_list_left_table_down tr:hover td{
    background-color: #EBF8FF;
    border-bottom: 1px solid #ACD2E5;
    border-top: 1px solid #ACD2E5;
}
.tr_active{
    background-color: #FAFAFA;
}
.xmlb_list_right{
    /*float: right;*/
    width: 48%;
    left: 50%;
    width: calc(50%- 4px);
    height: 600px;
    border: 1px solid #e9e9e9;
    display: none;
    position: absolute;
    background-color: #fff;
}
.xmlb_dlxq{
    width: 100%;
    padding-bottom: 20px;
    border: 1px solid #DFDFDF;
    background-color: #F8F8F8;
}
.xmlb_dlxq_title{
    text-align: center;
    font-size: 18px;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #DFDFDF;
}
.xmlb_dlxq_header{
    text-align: center;
    height: 40px;
    line-height: 40px;
    color: #8C8C8C;
}
.xmlb_dlxq_center{
    width: 980px;
    margin: 0 auto;
    border: 1px solid #DFDFDF;
    background-color: #fff;
}
.xmlb_dlxq_center table tr td{
    height: 40px;
    line-height: 20px;
}
.xmlb_dlxq_center table tr td:nth-child(1){
    width: 13%;
    text-indent: 2em;
}
.xmlb_dlxq_center hr{
    border: 1px solid #DFDFDF;
}
.xmlb_dlxq_center_button{
    width: 96%;
    margin: 0 auto;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    border: 1px solid #DFDFDF;
    background-color:#F8F8F8 ;
}
.xmlb_dlxq_center_button div{
    float: left;
}
.xmlb_dlxq_center_button div:nth-child(1){
    margin-top: 5px;
}
.dlzj_box {
    width: 630px;
    padding-bottom: 10px;
    border-radius: 5px;
    overflow: hidden;
    font-size: 12px;
    box-shadow: 0px 0px 15px #666;

}
.dlzj_box_title {
    width: 100%;
    background-color: #0690CE;
    color: #fff;
    text-align: center;
    height: 36px;
    line-height: 36px;
}
.dlzj_box_title span{
    float: right;
    margin-right: 10px;
    cursor: pointer;
}
.dlzj_box_button{
    width: 200px;
    margin: 0 auto;
    margin-top: 10px;
}
.dlzj_box_button div{
    height: 30px;
    line-height: 30px;
    width: 90px;
    text-align: center;
    float: left;
    cursor: pointer;
    border-radius: 5px;
}
.dlzj_box_button_right{
    background-color: #E9E9E9;
    border: 1px solid #ccc;margin-left: 10px;
    background: linear-gradient(#E9E9E9, #ccc); /* 标准的语法 */
}
.dlzj_box_button_right:hover{
    background: linear-gradient(#ccc, #E9E9E9); /* 标准的语法 */
}
.dlzj_box_button_left{
    background-color: #0178B1;
    border: 1px solid #0178B1;

    color: #fff;
    background: linear-gradient(#0784cb, #0075a9); /* 标准的语法 */
}
.dlzj_box_button_left:hover{
    background: linear-gradient(#0075a9, #0784cb); /* 标准的语法 */
}
.dlzj_box_center{
    width: 95%;
    margin: 0 auto;
    border: 1px solid #F0F0F0;
    margin-top: 10px;
    overflow: hidden;
    /*overflow-y: scroll;*/
    font-size: 14px;
    padding-bottom: 10px;
}
.dlzj_box_center table tr td{
    /**height: 40px;*/
    padding-top: 5px;

}
.dlzj_box_center table tr td:nth-child(1){
    vertical-align:top;
    padding-left: 10px;
}
.dlzj_box_center table tr td:nth-child(2){
    width: 80%;
}
.dlzj_box_center table tr td:nth-child(2) img{
    width: 60px;
    margin-left: 5px;
    border: 1px solid #ccc;
}
.dlzj_box_center table tr td:nth-child(2) img:nth-child(1){
    margin-left: 0;
}
.dlzj_box_center table tr:nth-child(3) td:nth-child(2){
    vertical-align:top;

}
.dlzj_box_center table tr:nth-child(8) td:nth-child(2){
    vertical-align:top;
    line-height: 15px;
}
.dlzj_box_center table tr:nth-child(13) td:nth-child(2){
    vertical-align:top;
    line-height: 15px;
}
.dlzj_box_center table tr:nth-child(18) td:nth-child(2){
    vertical-align:top;
    line-height: 15px;
}
.dlzj_box_center table tr:nth-child(2) td:nth-child(2) input{
    width: 250px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.dlzj_box_center table textarea{
    width: 440px;
    height: 120px;
    resize: none;
    border: 1px solid #D5D5D5;
}

.dlzj_box_center p{
    height: 24px;
    line-height: 24px;
  
}

.dlzj_box_center p label{
    height: 24px;
    line-height: 24px;
}
.zlzj_box {
    width: 630px;
    padding-bottom: 10px;
    border-radius: 5px;
    overflow: hidden;
    font-size: 12px;
    box-shadow: 0px 0px 15px #666;

}
.zlzj_box_title {
    width: 100%;
    background-color: #0690CE;
    color: #fff;
    text-align: center;
    height: 36px;
    line-height: 36px;
}
.zlzj_box_title span{
    float: right;
    margin-right: 10px;
    cursor: pointer;
}
.zlzj_box_button{
    width: 200px;
    margin: 0 auto;
    margin-top: 10px;
}
.zlzj_box_button div{
    height: 30px;
    line-height: 30px;
    width: 90px;
    text-align: center;
    float: left;
    cursor: pointer;
    border-radius: 5px;
}
.zlzj_box_button_right{
    background-color: #E9E9E9;
    border: 1px solid #ccc;
    margin-left: 10px;
    background: linear-gradient(#E9E9E9, #ccc); /* 标准的语法 */
}
.zlzj_box_button_right:hover{
    background: linear-gradient(#ccc, #E9E9E9); /* 标准的语法 */
}
.zlzj_box_button_left{
    background-color: #0178B1;
    border: 1px solid #0178B1;

    color: #fff;
    background: linear-gradient(#0784cb, #0075a9); /* 标准的语法 */
}
.zlzj_box_button_left:hover{
    background: linear-gradient(#0075a9, #0784cb); /* 标准的语法 */
}
.zlzj_box_center{
    width: 95%;
    margin: 0 auto;
    border: 1px solid #F0F0F0;
    margin-top: 10px;
    height: 600px;
    overflow: hidden;
    overflow-y: scroll;
    font-size: 14px;
    padding-bottom: 10px;
}
.zlzj_box_center table tr td{
    height: 40px;
    padding-top: 5px;
}
.zlzj_box_center table tr td:nth-child(1){
    vertical-align:top;
    padding-left: 10px;
}
.zlzj_box_center table tr td:nth-child(2){
    width: 80%;
}
.zlzj_box_center table tr td:nth-child(2) img{
    width: 60px;
    margin-left: 5px;
    border: 1px solid #ccc;
}
.zlzj_box_center table tr td:nth-child(2) img:nth-child(1){
    margin-left: 0;
}
.zlzj_box_center table tr:nth-child(4) td:nth-child(2){
    vertical-align:top;
    /*line-height: 15px;*/

}

.zlzj_box_center table tr:nth-child(3) td:nth-child(2) input{
    width: 250px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlzj_box_center table tr:nth-child(4) td:nth-child(2) input{
    width: 50px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlzj_box_center table tr:nth-child(5) td:nth-child(2) input{
    width: 50px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlzj_box_center table tr:nth-child(6) td:nth-child(2) input{
    width: 50px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlzj_box_center table tr:nth-child(7) td:nth-child(2) input{
    width: 50px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlzj_box_center table tr:nth-child(17) td:nth-child(2) select{
    width: 250px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlzj_box_center table tr:nth-child(18) td:nth-child(2) select{
    width: 250px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlzj_box_center table tr:nth-child(19) td:nth-child(2) select{
    width: 250px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlzj_box_center table textarea{
    width: 440px;
    height: 120px;
    resize: none;
    border: 1px solid #D5D5D5;
}
.zlbj_box {
    width: 630px;
    padding-bottom: 10px;
    border-radius: 5px;
    overflow: hidden;
    font-size: 12px;
    box-shadow: 0px 0px 15px #666;

}
.zlbj_box_title {
    width: 100%;
    background-color: #0690CE;
    color: #fff;
    text-align: center;
    height: 36px;
    line-height: 36px;
}
.zlbj_box_title span{
    float: right;
    margin-right: 10px;
    cursor: pointer;
}
.zlbj_box_button{
    width: 200px;
    margin: 0 auto;
    margin-top: 10px;
}
.zlbj_box_button div{
    height: 30px;
    line-height: 30px;
    width: 90px;
    text-align: center;
    float: left;
    cursor: pointer;
    border-radius: 5px;
}
.zlbj_box_button_right{
    background-color: #E9E9E9;
    border: 1px solid #ccc;margin-left: 10px;
    background: linear-gradient(#E9E9E9, #ccc); /* 标准的语法 */
}
.zlbj_box_button_right:hover{
    background: linear-gradient(#ccc, #E9E9E9); /* 标准的语法 */
}
.zlbj_box_button_left{
    background-color: #0178B1;
    border: 1px solid #0178B1;

    color: #fff;
    background: linear-gradient(#0784cb, #0075a9); /* 标准的语法 */
}
.zlbj_box_button_left:hover{
    background: linear-gradient(#0075a9, #0784cb); /* 标准的语法 */
}
.zlbj_box_center{
    width: 95%;
    margin: 0 auto;
    border: 1px solid #F0F0F0;
    margin-top: 10px;
    height: 600px;
    overflow: hidden;
    overflow-y: scroll;
    font-size: 14px;
    padding-bottom: 10px;
}
.zlbj_box_center table tr td{
    height: 40px;
    line-height: 40px;
}
.zlbj_box_center table tr td:nth-child(1){
    vertical-align:top;
    padding-left: 10px;
}
.zlbj_box_center table tr td:nth-child(2){
    width: 80%;
}
.zlbj_box_center table tr td:nth-child(2) img{
    width: 60px;
    margin-left: 5px;
    border: 1px solid #ccc;
}
.zlbj_box_center table tr td:nth-child(2) img:nth-child(1){
    margin-left: 0;
}
.zlbj_box_center table tr:nth-child(4) td:nth-child(2){
    vertical-align:top;
    /*line-height: 15px;*/

}

.zlbj_box_center table tr:nth-child(3) td:nth-child(2) input{
    width: 250px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlbj_box_center table tr:nth-child(4) td:nth-child(2) input{
    width: 50px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlbj_box_center table tr:nth-child(5) td:nth-child(2) input{
    width: 50px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlbj_box_center table tr:nth-child(6) td:nth-child(2) input{
    width: 50px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlbj_box_center table tr:nth-child(7) td:nth-child(2) input{
    width: 50px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlbj_box_center table tr:nth-child(15) td:nth-child(2) select{
    width: 250px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlbj_box_center table tr:nth-child(16) td:nth-child(2) select{
    width: 250px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlbj_box_center table textarea{
    width: 440px;
    height: 120px;
    resize: none;
    border: 1px solid #D5D5D5;
}
.dj_box {
    width: 440px;
    padding-bottom: 10px;
    border-radius: 5px;
    overflow: hidden;
    font-size: 12px;
    box-shadow: 0px 0px 15px #666;

}
.dj_box_title {
    width: 100%;
    background-color: #0690CE;
    color: #fff;
    text-align: center;
    height: 36px;
    line-height: 36px;
}
.dj_box_title span{
    float: right;
    margin-right: 10px;
    cursor: pointer;
}
.dj_box_button{
    width: 200px;
    margin: 0 auto;
    margin-top: 10px;
}
.dj_box_button div{
    height: 30px;
    line-height: 30px;
    width: 90px;
    text-align: center;
    float: left;
    cursor: pointer;
    border-radius: 5px;
}
.dj_box_button_right{
    background-color: #E9E9E9;
    border: 1px solid #ccc;margin-left: 10px;
    background: linear-gradient(#E9E9E9, #ccc); /* 标准的语法 */
}
.dj_box_button_right:hover{
    background: linear-gradient(#ccc, #E9E9E9); /* 标准的语法 */
}
.dj_box_button_left{
    background-color: #0178B1;
    border: 1px solid #0178B1;

    color: #fff;
    background: linear-gradient(#0784cb, #0075a9); /* 标准的语法 */
}
.dj_box_button_left:hover{
    background: linear-gradient(#0075a9, #0784cb); /* 标准的语法 */
}
.dj_box_center{
    width: 95%;
    margin: 0 auto;
    border: 1px solid #F0F0F0;
    margin-top: 10px;

    font-size: 14px;
    padding-bottom: 10px;
}
.dj_box_center table{
    padding-left: 20px;
}
.dj_box_center table tr td{
    height: 40px;
    line-height: 40px;
}
.dj_box_header{
    text-align: center;
    height: 70px;
    line-height: 70px;
    font-weight: bold;
}
.jd_box {
    width: 440px;
    padding-bottom: 10px;
    border-radius: 5px;
    overflow: hidden;
    font-size: 12px;
    box-shadow: 0px 0px 15px #666;

}
.jd_box_title {
    width: 100%;
    background-color: #0690CE;
    color: #fff;
    text-align: center;
    height: 36px;
    line-height: 36px;
}
.jd_box_title span{
    float: right;
    margin-right: 10px;
    cursor: pointer;
}
.jd_box_button{
    width: 200px;
    margin: 0 auto;
    margin-top: 10px;
}
.jd_box_button div{
    height: 30px;
    line-height: 30px;
    width: 90px;
    text-align: center;
    float: left;
    cursor: pointer;
    border-radius: 5px;
}
.jd_box_button_right{
    background-color: #E9E9E9;
    border: 1px solid #ccc;margin-left: 10px;
    background: linear-gradient(#E9E9E9, #ccc); /* 标准的语法 */
}
.jd_box_button_right:hover{
    background: linear-gradient(#ccc, #E9E9E9); /* 标准的语法 */
}
.jd_box_button_left{
    background-color: #0178B1;
    border: 1px solid #0178B1;

    color: #fff;
    background: linear-gradient(#0784cb, #0075a9); /* 标准的语法 */
}
.jd_box_button_left:hover{
    background: linear-gradient(#0075a9, #0784cb); /* 标准的语法 */
}
.jd_box_center{
    width: 95%;
    margin: 0 auto;
    border: 1px solid #F0F0F0;
    margin-top: 10px;

    font-size: 14px;
    padding-bottom: 10px;
}
.jd_box_center table{
    padding-left: 20px;
}
.jd_box_center table tr td{
    height: 40px;
    line-height: 40px;
}
.jd_box_header{
    text-align: center;
    height: 70px;
    line-height: 70px;
    font-weight: bold;
}
.image_div{
    position: relative;
    min-height: 40px;
}


.zlzj_box_center p{
    height: 24px;
    line-height: 24px;
  
}

.zlzj_box_center p label{
    height: 24px;
    line-height: 24px;
}

.image_input{
    opacity:0;
    filter:alpha(opacity=0);
    height: 95px;
    width: 100px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
}
.zlzj_box_center table tr:nth-child(8) td:nth-child(2) input{
    width: 50px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlzj_box_center table tr:nth-child(9) td:nth-child(2) input{
    width: 50px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}
.zlzj_box_center table tr:nth-child(10) td:nth-child(2) input{
    width: 50px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D5D5D5;
}