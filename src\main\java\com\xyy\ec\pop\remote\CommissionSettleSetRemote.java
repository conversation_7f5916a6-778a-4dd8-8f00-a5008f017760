package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.admin.api.PopCommissionSettleSetApi;
import com.xyy.ec.pop.server.api.admin.dto.CommissionActivitySettleDto;
import com.xyy.ec.pop.server.api.admin.dto.CommissionSettleSetDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description 佣金结算设置
 * <AUTHOR>
 */
@Component
@Slf4j
public class CommissionSettleSetRemote {
    @Reference
    private PopCommissionSettleSetApi popCommissionSettleApi;

    public List<CommissionSettleSetDto> listCommissionSettleSet(List<String> orgIds) {
        try {
            log.info("CommissionSettleSetRemote.listCommissionSettleSet#orgIds：{}", JSON.toJSONString(orgIds));
            ApiRPCResult<List<CommissionSettleSetDto>> result = popCommissionSettleApi.listCommissionSettleSet(orgIds);
            log.info("CommissionSettleSetRemote.listCommissionSettleSet#orgIds：{}，result：{}", JSON.toJSONString(orgIds), JSONObject.toJSONString(result));
            if (result == null || result.isFail()) {
                return Lists.newArrayList();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("CommissionSettleSetRemote.listCommissionSettleSet#error. orgIds：{}", JSON.toJSONString(orgIds), e);
            return Lists.newArrayList();
        }
    }

    public PageInfo<CommissionActivitySettleDto> listCorporationByPage(CommissionActivitySettleDto commissionActivitySettleDto) {
        try {
            log.info("CommissionSettleSetRemote.listCorporationByPage#commissionActivitySettleDto：{}", JSON.toJSONString(commissionActivitySettleDto));
            ApiRPCResult<PageInfo<CommissionActivitySettleDto>> result = popCommissionSettleApi.listCorporationByPage(commissionActivitySettleDto);
            log.info("CommissionSettleSetRemote.listCorporationByPage#commissionActivitySettleDto：{}，result：{}", JSON.toJSONString(commissionActivitySettleDto), JSONObject.toJSONString(result));
            return result.isSuccess() ? result.getData() : new PageInfo<>();
        } catch (Exception e) {
            log.error("#CommissionSettleSetRemote.listCorporationByPage#commissionActivitySettleDto:{}", commissionActivitySettleDto, e);
            return new PageInfo<>();
        }
    }
}
