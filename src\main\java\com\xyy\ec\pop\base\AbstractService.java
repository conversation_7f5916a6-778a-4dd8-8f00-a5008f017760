package com.xyy.ec.pop.base;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.exception.ServiceException;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 业务实现类
 * @ClassName: AbstractService 
 * <AUTHOR>
 * @date 2016-4-10 下午2:26:47 
 * @param <T>
 */
public abstract class AbstractService<T,P> implements Service<T,P> {

	protected abstract Mapper<T,P> getMapper();

	/**
	 * 动态添加实体信息
	 * @param entity
	 * @return
	 * @throws ServiceException
	 * <AUTHOR>
	 * @date 2016-4-10 下午2:26:58
	 */

	@Override
	@Transactional
	public int insertSelective(T entity) throws ServiceException {
		return getMapper().insertSelective(entity);
	}

	/**
	 * 根据主键删除实体信息
	 * @param id
	 * @return
	 * @throws ServiceException
	 * <AUTHOR>
	 * @date 2016-4-10 下午2:28:32
	 */

	@Override
	@Transactional
	public int deleteByPrimaryKey(P id) throws ServiceException {
		return getMapper().deleteByPrimaryKey(id);
	}

	/**
	 * 批量删除实体信息
	 * @param ids
	 * @return
	 * @throws ServiceException
	 * <AUTHOR>
	 * @date 2016-4-10 下午2:28:49
	 */
	@Override
	@Transactional
	public int batchDeleteByIds(List<P> ids) throws ServiceException {
		return getMapper().batchDeleteByIds(ids);
	}

	/**
	 * 动态更新实体信息
	 * @param entity
	 * @return
	 * @throws ServiceException
	 * @see com.xwan.framework.generic.Service#updateByPrimaryKeySelective(Object)
	 * <AUTHOR>
	 * @date 2016-4-10 下午2:29:15
	 */
	@Override
	@Transactional
	public int updateByPrimaryKeySelective(T entity) throws ServiceException {
		return getMapper().updateByPrimaryKeySelective(entity);
	}

	/**
	 * 根据主键查询实体
	 * @param id
	 * @return
	 * @throws ServiceException
	 * @see com.xwan.framework.generic.Service#selectByPrimaryKey(Integer)
	 * <AUTHOR>
	 * @date 2016-4-10 下午2:29:28
	 */
	@Override
	public T selectByPrimaryKey(P id) throws ServiceException {
		return getMapper().selectByPrimaryKey(id);
	}

	/**
	 * 分页查询(Mybatis插件)
	 * @param page
	 * @param entity
	 * @return
	 * @throws ServiceException
	 * <AUTHOR>
	 * @date 2016-4-10 下午2:30:05
	 */
	@Override
	public Page<T> selectPageList(Page<T> page, T entity) throws ServiceException {
		Page<T> pageResult = new Page<T>();
		PageHelper.startPage(page.getOffset()+1, page.getLimit());
		List<T> list = getMapper().selectList(entity);
	    //用PageInfo对结果进行包装
	    PageInfo<T> pageInfo = new PageInfo<T>(list);
	    pageResult.setTotal(pageInfo.getTotal());
        pageResult.setRows(pageInfo.getList());
		return pageResult;
	}

	/**
	 * 查询所有实体,不分页
	 * @Title: selectAll
	 * @param entity
	 * @return
	 * @throws ServiceException
	 * List<T>
	 * <AUTHOR>
	 * @date 2016-4-10 下午2:31:01
	 */
	@Override
	public List<T> selectList(T entity) throws ServiceException {
		return getMapper().selectList(entity);
	}

	/**
	 * 查询总数量
	 * @param entity
	 * @return
	 * @throws ServiceException
	 * @see com.xwan.framework.generic.Service#selectCount(Object)
	 * <AUTHOR>
	 * @date 2016-9-6 上午11:16:51
	 */
	@Override
	public int selectCount(T entity) throws ServiceException {
		return getMapper().selectCount(entity);
	}
}
