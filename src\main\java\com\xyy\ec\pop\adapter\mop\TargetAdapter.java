package com.xyy.ec.pop.adapter.mop;


import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pop.dto.mop.TargetManagerExcelDTO;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.common.condition.TargetSelectCondition;
import com.xyy.pop.mop.api.remote.parameter.TargetBatchImportParameter;
import com.xyy.pop.mop.api.remote.parameter.TargetImportParameter;
import com.xyy.pop.mop.api.remote.result.TargetBasicDTO;
import com.xyy.scm.constant.entity.pagination.Paging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class TargetAdapter implements MopBaseAdapter{
    @Reference(version = "1.0.0")
    com.xyy.pop.mop.api.remote.TargetRemote targetRemoteApi;
    public ResponseVo importExcel(List<TargetBasicDTO> targetBasicDTOS) {
        return to(()->targetRemoteApi.insertOrUpdateBatch(targetBasicDTOS));
    }
    public ResponseVo<Paging<TargetBasicDTO>> queryPageTargetByParam(TargetSelectCondition targetSelectCondition) {
        return to(()->targetRemoteApi.queryPageLogByParam(targetSelectCondition));
    }
    public ResponseVo<List<TargetBasicDTO>> queryTargetByParam(TargetSelectCondition targetSelectCondition) {
        return to(()->targetRemoteApi.queryTargetByParam(targetSelectCondition));
    }
    public ResponseVo update(TargetBasicDTO targetBasicDTO) {
        return to(()-> targetRemoteApi.update(targetBasicDTO));
    }
    public ResponseVo deleteTarget(TargetBasicDTO param) {
        return to(()-> targetRemoteApi.deleteTarget(param));
    }

    public ResponseVo batchImportTarget(TargetBatchImportParameter param, List<TargetManagerExcelDTO> excelDTOList) {
        List<TargetImportParameter> importList = new ArrayList<>();
        for (TargetManagerExcelDTO excelDTO : excelDTOList) {
            TargetImportParameter importParam = new TargetImportParameter();
            BeanUtils.copyProperties(excelDTO, importParam);
            importParam.setTargetMonthStr(excelDTO.getTargetMonth());

            importList.add(importParam);
        }
        param.setTargetImportParameterList(importList);
        return to(() -> targetRemoteApi.batchImportTarget(param));
    }
}
