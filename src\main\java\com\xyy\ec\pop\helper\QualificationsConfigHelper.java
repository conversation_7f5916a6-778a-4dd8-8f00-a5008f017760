package com.xyy.ec.pop.helper;

import com.xyy.ec.pop.server.api.Enum.QualificationsConfigEnum;
import com.xyy.ec.pop.server.api.merchant.dto.QualificationsCategoryDto;
import com.xyy.ec.pop.server.api.merchant.dto.QualificationsDetailDto;
import com.xyy.ec.pop.vo.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 资质配置 转换工具
 * <AUTHOR>
 */
public class QualificationsConfigHelper {
    public static QualificationsCategoryRelationVo convertToCategoryRelationVo(QualificationsCategoryDto dto) {
        if (dto == null) {
            return null;
        }
        QualificationsCategoryVo categoryVo = new QualificationsCategoryVo();
        categoryVo.setParentId(dto.getParentId());
        categoryVo.setCorporationType(dto.getCorporationType());
        categoryVo.setId(dto.getId());
        categoryVo.setName(dto.getName());
        categoryVo.setType(dto.getType());

        QualificationsCategoryRelationVo relationVo = new QualificationsCategoryRelationVo();
        relationVo.setQualificationsCategory(categoryVo);
        relationVo.setQualificationsDetailList(convertToDetailVo(dto.getDetails()));
        relationVo.setBusinessCategoryIdList(dto.getBusinessCategoryIdList());
        return relationVo;
    }

    private static List<QualificationsDetailVo> convertToDetailVo(List<QualificationsDetailDto> details) {
        if (CollectionUtils.isEmpty(details)) {
            return new ArrayList<>(0);
        }
        return details.stream().map(item -> convertToDetailVo(item)).collect(Collectors.toList());
    }

    private static QualificationsDetailVo convertToDetailVo(QualificationsDetailDto dto) {

        QualificationsDetailVo detailVo = new QualificationsDetailVo();
        detailVo.setId(dto.getId());
        detailVo.setName(dto.getName());
        detailVo.setRemark(dto.getRemark());
        detailVo.setIsNeedCode(dto.getIsNeedCode() ? QualificationsConfigEnum.IsNeedEnum.MUST.getFlag() : QualificationsConfigEnum.IsNeedEnum.NONE.getFlag());
        detailVo.setIsNeed(dto.getIsNeed() ? QualificationsConfigEnum.IsNeedEnum.MUST.getFlag() : QualificationsConfigEnum.IsNeedEnum.NONE.getFlag());
        detailVo.setIsNeedTime(dto.getIsNeedTime() ? QualificationsConfigEnum.IsNeedEnum.MUST.getFlag() : QualificationsConfigEnum.IsNeedEnum.NONE.getFlag());
        detailVo.setIsNeedBank(dto.getIsNeedBank() ? QualificationsConfigEnum.IsNeedEnum.MUST.getFlag() : QualificationsConfigEnum.IsNeedEnum.NONE.getFlag());
        detailVo.setIsNeedSubBank(dto.getIsNeedSubBank() ? QualificationsConfigEnum.IsNeedEnum.MUST.getFlag() : QualificationsConfigEnum.IsNeedEnum.NONE.getFlag());
        detailVo.setWithLongTime(dto.getWithLongTime()? QualificationsConfigEnum.IsNeedEnum.MUST.getFlag() : QualificationsConfigEnum.IsNeedEnum.NONE.getFlag());
        detailVo.setCode(dto.getCode());
        detailVo.setCorporationType(dto.getCorporationType());
        detailVo.setLegalPersonName(dto.getLegalPersonName()?QualificationsConfigEnum.IsNeedEnum.MUST.getFlag() : QualificationsConfigEnum.IsNeedEnum.NONE.getFlag());
        detailVo.setMaxImg(dto.getMaxImg());
        detailVo.setPersonNameShow(dto.isPersonNameShow());
        detailVo.setNeedCodeShow(dto.isNeedCodeShow());
        detailVo.setNeedBankShow(dto.isNeedBankShow());
        detailVo.setNeedSubBankShow(dto.isNeedSubBankShow());
        detailVo.setNeedTimeEndShow(dto.isNeedTimeEndShow());
        detailVo.setPersonNameShowName(dto.getPersonNameShowName());
        detailVo.setNeedCodeShowName(dto.getNeedCodeShowName());
        detailVo.setNeedBankShowName(dto.getNeedBankShowName());
        detailVo.setNeedSubBankShowName(dto.getNeedSubBankShowName());
        detailVo.setNeedTimeShowName(dto.getNeedTimeShowName());
        detailVo.setIsShow(dto.getIsShow());
        detailVo.setIsNeedGainTime(dto.getIsNeedGainTime());
        return detailVo;

    }

    public static List<QualificationsCategoryRelationVo> convertToCategoryRelationVos(List<QualificationsCategoryDto> categories) {
        if (CollectionUtils.isEmpty(categories)) {
            return new ArrayList<>(0);
        }
        return categories.stream().map(QualificationsConfigHelper::convertToCategoryRelationVo).collect(Collectors.toList());
    }

    public static List<QualificationsCategoryDto> convertToCategoryDto(List<QualificationsCategoryRelationVo> relationVoList) {
        return relationVoList.stream().map(item->convertToCategoryDto(item)).collect(Collectors.toList());
    }
    public static QualificationsCategoryDto convertToCategoryDto(QualificationsCategoryRelationVo vo) {
        QualificationsCategoryDto dto = new QualificationsCategoryDto();
        QualificationsCategoryVo catVo = vo.getQualificationsCategory();
        dto.setId(catVo.getId());
        dto.setName(catVo.getName());
        dto.setParentId(catVo.getParentId());
        dto.setCorporationType(catVo.getCorporationType());
        dto.setBusinessCategoryIdList(vo.getBusinessCategoryIdList());
        dto.setType(catVo.getType());
        dto.setDetails(convertToCategoryRelationDtos(vo.getQualificationsDetailList()));

        return dto;
    }

    private static List<QualificationsDetailDto> convertToCategoryRelationDtos(List<QualificationsDetailVo> popQualificationsDetailList) {
        if(CollectionUtils.isEmpty(popQualificationsDetailList)){
            return new ArrayList<>(0);
        }
        return popQualificationsDetailList.stream().map(item->convertToCategoryRelationDto(item)).collect(Collectors.toList());
    }

    private static QualificationsDetailDto convertToCategoryRelationDto(QualificationsDetailVo item) {
        QualificationsDetailDto dto = new QualificationsDetailDto();
        dto.setId(item.getId());
        dto.setName(item.getName());
        dto.setRemark(item.getRemark());
        dto.setCode(item.getCode());
        dto.setCorporationType(item.getCorporationType());
        dto.setMaxImg(item.getMaxImg());
        dto.setLegalPersonName(getNeed(item.getLegalPersonName()));
        dto.setIsNeed(getNeed(item.getIsNeed()));
        dto.setIsNeedCode(getNeed(item.getIsNeedCode()));
        dto.setIsNeedBank(getNeed(item.getIsNeedBank()));
        dto.setIsNeedSubBank(getNeed(item.getIsNeedSubBank()));
        dto.setIsNeedTime(getNeed(item.getIsNeedTime()));
        dto.setWithLongTime(getNeed(item.getWithLongTime()));
        dto.setPersonNameShow(item.isPersonNameShow());
        dto.setNeedCodeShow(item.isNeedCodeShow());
        dto.setNeedBankShow(item.isNeedBankShow());
        dto.setNeedSubBankShow(item.isNeedSubBankShow());
        dto.setNeedTimeEndShow(item.isNeedTimeEndShow());
        dto.setPersonNameShowName(item.getPersonNameShowName());
        dto.setNeedCodeShowName(item.getNeedCodeShowName());
        dto.setNeedBankShowName(item.getNeedBankShowName());
        dto.setNeedSubBankShowName(item.getNeedSubBankShowName());
        dto.setNeedTimeShowName(item.getNeedTimeShowName());
        dto.setIsShow(item.getIsShow());
        dto.setIsNeedGainTime(item.getIsNeedGainTime());
        return dto;
    }

    private static Boolean getNeed(Byte b) {
        return b!=null&& QualificationsConfigEnum.IsNeedEnum.MUST.getFlag()==b.byteValue();
    }
}
