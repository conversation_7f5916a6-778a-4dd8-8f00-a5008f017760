package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/09/18/10:50
 * @Description:
 */
@Data
public class RedoRetryInfoVo implements Serializable {
    private static final long serialVersionUID = -1173722894992726364L;
    private Long id;

    private String retryJson;

    private Integer retryCount;

    private Byte status;

    private Byte istatus;

    private Date createTime;

    private String createBy;

    private Date lastModifyTime;

    private String lastModifyBy;
}
