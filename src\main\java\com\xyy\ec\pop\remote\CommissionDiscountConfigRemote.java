package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.commission.api.CommissionDiscountConfigApi;
import com.xyy.ec.pop.server.api.commission.dto.CommissionDiscountConfigDto;
import com.xyy.ec.pop.vo.CommissionDiscountSetVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/09/13/15:17
 * @Description:
 */
@Component
@Slf4j
public class CommissionDiscountConfigRemote {
    @Reference
    private CommissionDiscountConfigApi commissionDiscountConfigApi;

    /**
     * 保存佣金折扣配置
     *
     * @return
     */
    public Boolean saveCommissionDiscountConfig(CommissionDiscountConfigDto dto) {
        try {
            log.info("CommissionDiscountConfigRemote.saveCommissionDiscountConfig request param:{}", JSON.toJSONString(dto));
            ApiRPCResult<Boolean> booleanApiRPCResult = commissionDiscountConfigApi.saveCommissionDiscountConfig(dto);
            log.info("CommissionDiscountConfigRemote.saveCommissionDiscountConfig response param:{}", JSON.toJSONString(booleanApiRPCResult));
            if (booleanApiRPCResult != null && booleanApiRPCResult.isSuccess()){
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        }catch (Exception e) {
            log.error("CommissionDiscountConfigRemote.saveCommissionDiscountConfig error param:{}",JSON.toJSONString(dto),e);
            return Boolean.FALSE;
        }
    }

    /**
     * 查看佣金折扣配置
     *
     * @param orgId
     * @return
     */
    public CommissionDiscountConfigDto getCommissionDiscountConfigByOrgId(String orgId) throws Exception {
        try {
            log.info("CommissionDiscountConfigRemote.getCommissionDiscountConfigByOrgId request param:{}",orgId);
            ApiRPCResult<CommissionDiscountConfigDto> commissionDiscountConfigResult = commissionDiscountConfigApi.getCommissionDiscountConfigByOrgId(orgId);
            log.info("CommissionDiscountConfigRemote.getCommissionDiscountConfigByOrgId response param:{}",JSON.toJSONString(commissionDiscountConfigResult));
            if (commissionDiscountConfigResult == null || commissionDiscountConfigResult.isFail()){
                throw new Exception("查询佣金配置失败");
            }
            return commissionDiscountConfigResult.getData();
        }catch (Exception e) {
            log.error("CommissionDiscountConfigRemote.getCommissionDiscountConfigByOrgId error param:{}",orgId,e);
            throw e;
        }
    }
}
