package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.server.api.merchant.api.seller.SupplierRoleApi;
import com.xyy.ec.pop.server.api.merchant.api.seller.SupplierUserApi;
import com.xyy.ec.pop.server.api.seller.api.PopBillApi;
import com.xyy.ec.pop.server.api.seller.api.PopBillPaymentApi;
import com.xyy.ec.pop.server.api.seller.api.PopBillSettlementApi;
import com.xyy.ec.pop.server.api.seller.dto.PopSupplierMenuDto;
import com.xyy.ec.pop.server.api.tool.api.ToolApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Auther: shiyujie
 * @Date: 2021/09/06/12:57
 * @Description:
 */
@Slf4j
@Component
public class ToolRemote {
    @Reference
    private ToolApi toolApi;
    @Reference
    private PopBillSettlementApi popBillSettlementApi;
    @Reference
    private PopBillApi popBillApi;
    @Reference
    private PopBillPaymentApi billPaymentApi;
    @Reference
    private SupplierRoleApi supplierRoleApi;


    public void switchPaymentChannel(List<String> orgIdList) {
        try {
            if (CollectionUtils.isEmpty(orgIdList)) {
                return;
            }
            log.info("ToolRemote.switchPaymentChannel#request param:{}", orgIdList);
            ApiRPCResult apiRPCResult = toolApi.switchPaymentChannel(orgIdList);
            log.info("ToolRemote.switchPaymentChannel# response param:{}", orgIdList);
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                throw new ServiceException("富民支付通道切换失败");
            }
        } catch (Exception e) {
            log.error("ToolRemote.switchPaymentChannel#error param:{}", orgIdList, e);
        }
    }

    public void execSettleTask() {
        ApiRPCResult<List<String>> orgIdsRpc = popBillSettlementApi.selectWaitSettleOrgIdsList();
        log.info("ToolRemote.execSettleTask#orgIdsRpc:{}", JSON.toJSONString(orgIdsRpc));
        if (orgIdsRpc.isFail()) {
            log.warn("ToolRemote.execSettleTask:RPC请求失败，请求接口：popBilSettlementApi.selectWaitSettleOrgIdsList，返回：{}", JSON.toJSONString(orgIdsRpc));
            return;
        }
        List<String> orgIds = orgIdsRpc.getData();
        if (CollectionUtils.isEmpty(orgIds)) {
            log.info("无待处理结算单");
            return;
        }
        //逐个商家循环，后期可改为根据分片处理
        for (String orgId : orgIds) {
            if (StringUtils.isBlank(orgId)) {
                log.info("ToolRemote.execSettleTask:orgId为空，orgId：{}", orgId);
                continue;
            }
            dealForOrg(orgId);
        }
    }

    private void dealForOrg(String orgId) {
        try {
            ApiRPCResult<Integer> dealRpcResult = popBillSettlementApi.dealSettlementsForOrg(orgId);
            if (dealRpcResult.isFail() || dealRpcResult.getData() > 0) {
                //处理失败
                log.error("ToolRemote.dealForOrg:处理失败，orgId：{}，RPC返回：{}", orgId, JSON.toJSONString(dealRpcResult));
            }
        } catch (Exception e) {
            log.error("ToolRemote.dealForOrg#error,orgId:{}", orgId, e);
        }
    }

    public void execBillTask() {
        popBillApi.createBillFromSettle(LocalDateTime.now());
    }

    public void execBillPaymentTask() {
        billPaymentApi.createBillPaymentFromBill(LocalDateTime.now());
    }

    public void editTaskSql(String orgId, Integer taskId, String taskInfo) {
        log.info("ToolRemote.editTaskSql#orgId:{},taskId:{},taskInfo:{}", orgId, taskId, taskInfo);
        ApiRPCResult apiRPCResult = toolApi.editTaskSql(orgId, taskId, taskInfo);
        log.info("ToolRemote.editTaskSql#orgId:{},taskId:{},taskInfo:{},apiRPCResult:{}", orgId, taskId, taskInfo, JSON.toJSONString(apiRPCResult));
        if (apiRPCResult.isFail()) {
            //更新任务sql失败
            throw new ServiceRuntimeException(apiRPCResult.getErrMsg());
        }
    }

    public void exeBillSettleData() {
        log.info("ToolRemote.exeBillSettleData start");
        ApiRPCResult apiRPCResult = toolApi.exeBillSettleData();
        log.info("ToolRemote.exeBillSettleData end");
        if (apiRPCResult.isFail()) {
            throw new ServiceRuntimeException(apiRPCResult.getErrMsg());
        }
    }

    public void addPopSkuSync(String barcode, Integer jobType,String userName) {
        ApiRPCResult apiRPCResult = toolApi.addPopSkuSync(barcode,jobType,userName);
        log.info("ToolRemote.addPopSkuSync request: barcode{} jobType:{},result:{}",barcode,jobType, JSONObject.toJSONString(apiRPCResult));
        if (apiRPCResult.isFail()) {
            throw new ServiceRuntimeException(apiRPCResult.getErrMsg());
        }
    }

    public void addPopMerchantMenu(PopSupplierMenuDto popSupplierMenuDto) {
        ApiRPCResult<Boolean> apiRPCResult =supplierRoleApi.addSupplierMenuDto(popSupplierMenuDto);
        if (apiRPCResult.isFail()) {
            throw new ServiceRuntimeException(apiRPCResult.getErrMsg());
        }
    }
}
