package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.core.util.CollectionUtil;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.helper.PopErpSkuBatchUpdateConvertHelper;
import com.xyy.ec.pop.server.api.erp.dto.PopErpSkuAdminBatchUpdateDto;
import com.xyy.ec.pop.server.api.merchant.api.admin.PopMerchantTotalFundAccountApi;
import com.xyy.ec.pop.server.api.merchant.dto.MerchantBatchFailDeductionDto;
import com.xyy.ec.pop.server.api.merchant.dto.MerchantTotalFundInfoDto;
import com.xyy.ec.pop.server.api.merchant.param.*;
import com.xyy.ec.pop.vo.ErpSkuBatchUpdateVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商家总资金账户相关接口
 * <AUTHOR>
 * @date 2024/9/2 10:43
 */
@Component
@Slf4j
public class PopMerchantTotalFundAccountRemoteAdapter {

    @Reference
    private PopMerchantTotalFundAccountApi popMerchantTotalFundAccountApi;


    /**
     * 获取资金账户信息
     * @return
     */
    public MerchantTotalFundInfoDto getAccountInfo(MerchantTotalFundInfoParam param){
        try {
            log.info("PopMerchantTotalFundAccountRemoteAdapter.getAccountInfo-start:{}", JSON.toJSONString(param));
            ApiRPCResult<MerchantTotalFundInfoDto> accountInfo = popMerchantTotalFundAccountApi.getAccountInfo(param);
            log.info("PopMerchantTotalFundAccountRemoteAdapter.getAccountInfo-end:{}",JSON.toJSONString(accountInfo));
            if(accountInfo.isFail()){
                return null;
            }
            return accountInfo.getData();
        } catch (Exception e) {
            log.error("popMerchantTotalFundAccountApi.getAccountInfo#user:{}",param);
        }
        return null;
    }

    /**
     * 资金扣罚接口
     * @return
     */
    public MerchantBatchFailDeductionDto batchDeduction(MerchantBatchDeductionParam  params){
        try {
            log.info("PopMerchantTotalFundAccountRemoteAdapter.batchDeduction-start:{}", JSON.toJSONString(params));
            if (Objects.isNull(params)) {
                return null;
            }
            ApiRPCResult<MerchantBatchFailDeductionDto> accountInfo = popMerchantTotalFundAccountApi.batchDeduction(params);
            log.info("PopMerchantTotalFundAccountRemoteAdapter.batchDeduction-end:{}",JSON.toJSONString(accountInfo));
            if(Objects.isNull(accountInfo)){
                return null;
            }
            return accountInfo.getData();
        } catch (Exception e) {
            log.error("popMerchantTotalFundAccountApi.batchDeduction#:{}",params);
        }
        return null;
    }

    /**
     * 资金扣罚接口
     * @return
     */
    public ApiRPCResult<Boolean>  accountDeduction(MerchantAccountDeductionParam params){
        try {
            log.info("PopMerchantTotalFundAccountRemoteAdapter.accountDeduction-start:{}", JSON.toJSONString(params));
            if (params==null) {
                return null;
            }
            ApiRPCResult<Boolean> accountInfo = popMerchantTotalFundAccountApi.accountDeduction(params);
            log.info("PopMerchantTotalFundAccountRemoteAdapter.accountDeduction-end:{}",JSON.toJSONString(accountInfo));
            return accountInfo;
        } catch (Exception e) {
            log.error("popMerchantTotalFundAccountApi.accountDeduction#:{}",params);
        }
        return null;
    }
    /**
     * 重试扣罚
     * @return
     */
    public ApiRPCResult<Boolean> retryDeduction(MerchantRetryDeductionParam params){
        try {
            log.info("PopMerchantTotalFundAccountRemoteAdapter.retryDeduction-start:{}", JSON.toJSONString(params));
            if (params==null) {
                return null;
            }
            ApiRPCResult<Boolean> accountInfo = popMerchantTotalFundAccountApi.retryDeduction(params);
            log.info("PopMerchantTotalFundAccountRemoteAdapter.retryDeduction-end:{}",JSON.toJSONString(accountInfo));
            return accountInfo;
        } catch (Exception e) {
            log.error("popMerchantTotalFundAccountApi.retryDeduction#:{}",params);
        }
        return null;
    }
}
