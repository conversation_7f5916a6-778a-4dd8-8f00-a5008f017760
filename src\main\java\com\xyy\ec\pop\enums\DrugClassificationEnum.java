package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

public enum DrugClassificationEnum {
        AC_OTC(1, "甲类OTC"),
        BC_OTC(2, "乙类OTC"),
        RX(3, "RX处方药");
        private int status;
        private String name;

        private DrugClassificationEnum(int status, String name) {
            this.status = status;
            this.name = name;
        }

        public int getStatus() {
            return this.status;
        }

        public String getName() {
            return this.name;
        }

    private static Map<Integer, DrugClassificationEnum> controlMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(DrugClassificationEnum apEnum : DrugClassificationEnum.values()) {
            controlMaps.put(apEnum.getStatus(), apEnum);
            maps.put(apEnum.getStatus(),apEnum.getName());
        }
    }
    public static String get(int id) {
        return controlMaps.get(id).getName();
    }

}
