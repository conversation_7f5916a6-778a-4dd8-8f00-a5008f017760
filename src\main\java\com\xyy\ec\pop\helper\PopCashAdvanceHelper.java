package com.xyy.ec.pop.helper;


import com.xyy.ec.pop.server.api.seller.dto.PopCashAdvanceDetailDto;
import com.xyy.ec.pop.server.api.seller.enums.BillPaymentStatusEnum;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.server.api.seller.enums.PopBillBusinessTypeEnum;
import com.xyy.ec.pop.server.api.seller.enums.PopCashAdvanceStatusEnum;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.PopCashAdvanceDetailVo;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class PopCashAdvanceHelper {
    public static List<PopCashAdvanceDetailVo> convertToVos(List<PopCashAdvanceDetailDto> list) {
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>(0);
        }
        return list.stream().map(PopCashAdvanceHelper::convertToVo).collect(Collectors.toList());
    }

    public static PopCashAdvanceDetailVo convertToVo(PopCashAdvanceDetailDto dto) {
        PopCashAdvanceDetailVo vo = new PopCashAdvanceDetailVo();
        vo.setOrgId(dto.getOrgId());
        vo.setOrgName(dto.getOrgName());
        vo.setName(dto.getName());
        vo.setApplyAmount(dto.getApplyAmount());
        vo.setFee(dto.getFee());
        vo.setRealityAmount(dto.getRealityAmount());
        vo.setApplyTime(dto.getApplyTime());
        vo.setApplyTimeStr(DateUtil.date2Str(dto.getApplyTime(),DateUtil.PATTERN_STANDARD));
        vo.setAccountName(dto.getAccountName());
        vo.setAccountNum(dto.getAccountNum());
        vo.setAccountBank(dto.getAccountBank());
        vo.setCashAdvanceNum(dto.getCashAdvanceNum());
        vo.setPaymentWay(dto.getPaymentWay());
        vo.setPaymentStatus(dto.getPaymentStatus());
        vo.setPaymentStatusName(PopCashAdvanceStatusEnum.getText(dto.getPaymentStatus()));
        vo.setReason(dto.getReason());
        vo.setBillNo(dto.getBillNo());
        vo.setBusinessType(dto.getBusinessType());
        vo.setBusinessTypeName(PopBillBusinessTypeEnum.getText(dto.getBusinessType()));
        vo.setBusinessNo(dto.getBusinessNo());
        vo.setMerchantName(dto.getMerchantName());
        vo.setProductMoney(dto.getProductMoney());
        vo.setTotalMoney(dto.getTotalMoney());
        vo.setMoney(dto.getMoney());
        vo.setFreightAmount(dto.getFreightAmount());
        vo.setCouponShopAmount(dto.getCouponShopAmount());
        vo.setMarketingShopAmount(dto.getMarketingShopAmount());
        vo.setShopTotalDiscount(dto.getShopTotalDiscount());
        vo.setCouponPlatformAmount(dto.getCouponPlatformAmount());
        vo.setMarketingPlatformAmount(dto.getMarketingPlatformAmount());
        vo.setPlatformTotalDiscount(dto.getPlatformTotalDiscount());
        vo.setHireMoney(dto.getHireMoney());
        vo.setPayableCommission(dto.getPayableCommission());
        vo.setSettlementType(dto.getSettlementType());
        vo.setSettlementTypeName(CommissionSettleTypeEnum.getText(dto.getSettlementType()));
        vo.setBillPaymentStatus(dto.getBillPaymentStatus());
        vo.setBillPaymentStatusName(BillPaymentStatusEnum.getText(dto.getBillPaymentStatus()));
        vo.setBillPaymentTime(dto.getBillPaymentTime());
        vo.setBillPaymentTimeStr(DateUtil.date2Str(dto.getBillPaymentTime(),DateUtil.PATTERN_STANDARD));
        vo.setPenaltyAmount(dto.getPenaltyAmount());
        vo.setStatementTotalMoney(dto.getStatementTotalMoney());
        vo.setOrderPayTime(dto.getOrderPayTime());
        vo.setOrderPayTimeStr(DateUtil.date2Str(dto.getOrderPayTime(),DateUtil.PATTERN_STANDARD));
        vo.setOrderFinishTime(dto.getOrderFinishTime());
        vo.setPayType(dto.getPayType());
        vo.setOrderSettlementStatus(dto.getOrderSettlementStatus());
        vo.setOrderSettlementTime(dto.getOrderSettlementTime());
        vo.setBillCreateTime(dto.getBillCreateTime());
        vo.setBillCreateTimeStr(DateUtil.date2Str(dto.getBillCreateTime(),DateUtil.PATTERN_STANDARD));
        vo.setCreateTime(dto.getCreateTime());
        vo.setBillId(dto.getBillId());
        vo.setSellerUserId(dto.getSellerUserId());
        vo.setDeductedCommission(dto.getDeductedCommission());
        return vo;
    }
}
