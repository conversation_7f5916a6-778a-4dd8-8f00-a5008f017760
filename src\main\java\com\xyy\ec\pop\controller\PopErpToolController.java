package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.excel.metadata.NullObject;
import com.alibaba.fastjson.JSONArray;
import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.erpUtil.service.PopErpUtilService;
import com.xyy.ec.pop.server.api.erpUtil.dto.ErpSkuToolParamDto;
import com.xyy.ec.pop.server.api.erpUtil.dto.PopClientMonitorDto;
import com.xyy.ec.pop.server.api.erpUtil.dto.PopCorpTaskConfigDto;
import com.xyy.ec.pop.server.api.erpUtil.dto.PopCorporationDto;
import com.xyy.ec.pop.server.api.erpUtil.dto.PopErpSkuToolDto;
import com.xyy.ec.pop.server.api.erpUtil.dto.PopTaskRecordDto;
import com.xyy.ec.pop.server.api.product.api.FixProductUtilApi;
import com.xyy.ec.pop.server.api.product.api.PopSkuApi;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/03/11/13:20
 * @Description: 任务配置数据/任务监控数据/任务执行记录 controller
 */
@Slf4j
@Controller
@RequestMapping("/tool")
@Component
public class PopErpToolController {
    @Autowired
    private PopErpUtilService popErpUtilService;
    @Reference
    private PopSkuApi popSkuApi;


    @RequestMapping("/taskRecordPage")
    public String taskRecordPage(ModelAndView modelAndView) {
        return "/erpTools/popTaskRecord";
    }

    @RequestMapping("/corpTaskConfigPage")
    public String corpTaskConfigPage(ModelAndView modelAndView) {
        return "/erpTools/popCorpTaskConfig";
    }

    @RequestMapping("/clientMonitorPage")
    public String clientMonitorPage(ModelAndView modelAndView) {
        return "/erpTools/popClientMonitor";
    }

    @RequestMapping("/popErpSkuPage")
    public String popErpSkuPage() {
        return "/erpTools/popErpSku";
    }

    @RequestMapping("/brushProductIdPage")
    public String brushProductPage() {
        return "/erpTools/refrenshProductId";
    }

    @RequestMapping("/getCorporation")
    @ResponseBody
    public ResponseVo getCorporation(String name) {
        try {
            log.info("#PopErpToolController.getCorporation#name:{}", name);
            return Optional.ofNullable(name).map(n -> {
                PopCorporationDto corporationByName = popErpUtilService.getCorporationByName(name);
                return ResponseVo.successResult(corporationByName);
            }).orElseGet(() -> ResponseVo.errRest("查询失败"));
        } catch (Exception e) {
            log.info("#查询企业失败#", e);
            return ResponseVo.errRest("查询失败");
        }
    }

    @RequestMapping("/getCorporationList")
    @ResponseBody
    public ResponseVo getCorporationList(String name) {
        try {
            log.info("#PopErpToolController.getCorporation#name:{}", name);
            return Optional.ofNullable(name).map(n -> {
                List<PopCorporationDto> corpList = popErpUtilService.getCorpList(name);
                return ResponseVo.successResult(corpList);
            }).orElseGet(() -> ResponseVo.errRest("查询失败"));
        } catch (Exception e) {
            log.info("#查询企业失败#", e);
            return ResponseVo.errRest("查询失败");
        }
    }

    @RequestMapping("/corpTaskConfigData")
    @ResponseBody
    public ResponseVo getPopCorpTaskConfig(PageInfo pageInfo, String orgName, Integer status, String orgId) {
        try {
            List<String> orgIdList = getOrgIdListByNameAndOrgId(orgName, orgId);
            if ((StringUtils.isNotBlank(orgName) || StringUtils.isNotBlank(orgId)) && CollectionUtils.isEmpty(orgIdList)) {
                return ResponseVo.successResultNotData();
            }
            log.info("PopErpToolController.getPopCorpTaskConfig#pageInfo:{},orgIds:{},status:{}", pageInfo, orgIdList, status);
            PageInfo<PopCorpTaskConfigDto> corpTaskConfigPage = popErpUtilService.getCorpTaskConfigPage(pageInfo, orgIdList, status);
            return ResponseVo.successResult(corpTaskConfigPage);
        } catch (Exception e) {
            log.error("#查询任务配置失败#", e);
            return ResponseVo.errRest("查询任务配置失败");
        }
    }

    @RequestMapping("/clientMonitorData")
    @ResponseBody
    public ResponseVo getClientMonitor(PageInfo pageInfo, String orgName) {
        try {
            List<String> orgIdList = getOrgIdList(orgName);
            if (StringUtils.isNotBlank(orgName) && CollectionUtils.isEmpty(orgIdList)) {
                return ResponseVo.successResultNotData();
            }
            log.info("PopErpToolController.getClientMonitor#pageInfo:{},orgId:{}", pageInfo, orgIdList);
            PageInfo<PopClientMonitorDto> clientMonitorPage = popErpUtilService.getClientMonitorPage(pageInfo, orgIdList);
            return ResponseVo.successResult(clientMonitorPage);
        } catch (Exception e) {
            log.error("#查询任务监控记录失败#", e);
            return ResponseVo.errRest("查询任务监控记录失败");
        }
    }

    @RequestMapping("/taskRecordData")
    @ResponseBody
    public ResponseVo getTaskRecord(PageInfo pageInfo, String orgName, Integer status) {
        try {
            List<String> orgIdList = getOrgIdList(orgName);
            if (StringUtils.isNotBlank(orgName) && CollectionUtils.isEmpty(orgIdList)) {
                return ResponseVo.successResultNotData();
            }
            if (orgName == null || orgName.trim().length() < 2) {
                return ResponseVo.successResultNotData();
            }
            log.info("PopErpToolController.getTaskRecord#pageInfo:{},orgId:{}", pageInfo, orgIdList);
            PageInfo<PopTaskRecordDto> taskRecordPage = popErpUtilService.getTaskRecordPage(pageInfo, orgIdList, status);
            return ResponseVo.successResult(taskRecordPage);
        } catch (Exception e) {
            log.error("#查询任务执行记录失败#", e);
            return ResponseVo.errRest("查询任务执行记录失败");
        }
    }

    @RequestMapping("/getPopErpSkuData")
    @ResponseBody
    public ResponseVo getPopErpSkuData(PageInfo pageInfo, ErpSkuToolParamDto erpSkuToolDto, String orgName) {
        try {

            List<String> orgIdList = getOrgIdList(orgName);
            if (StringUtils.isNotBlank(orgName) && CollectionUtils.isEmpty(orgIdList)) {
                return ResponseVo.successResultNotData();
            }
            erpSkuToolDto.setOrgIdList(orgIdList);
            log.info("#PopErpToolController.getPopErpSkuData#pageInfo:{},erpSkuToolDto:{}", pageInfo, erpSkuToolDto);

            PageInfo<PopErpSkuToolDto> popErpSkuByOrgId = popErpUtilService.getPopErpSkuList(pageInfo, erpSkuToolDto);
            log.info("#PopErpToolController.getPopErpSkuData#popErpSku:{}", popErpSkuByOrgId);
            return ResponseVo.successResult(popErpSkuByOrgId);
        } catch (Exception e) {
            log.error("#查询商品记录失败#", e);
            return ResponseVo.errRest("查询商品记录失败");
        }
    }

    /**
     * 搜索企业并返回id集合
     *
     * @param name
     * @return
     */
    public List<String> getOrgIdList(String name) {
        if (StringUtils.isEmpty(name)) {
            return Lists.newArrayList();
        }
        List<PopCorporationDto> corpList = popErpUtilService.getCorpList(name);
        if (CollectionUtils.isEmpty(corpList)) {
            return Lists.newArrayList();
        }
        return corpList.stream().map(PopCorporationDto::getOrgId).collect(Collectors.toList());
    }

    public List<String> getOrgIdListByNameAndOrgId(String name, String orgId) {
        if (StringUtils.isEmpty(name) && StringUtils.isEmpty(orgId)) {
            return Lists.newArrayList();
        }
        List<PopCorporationDto> corpList = popErpUtilService.getCorpListByNameAndOrgId(name, orgId);
        if (CollectionUtils.isEmpty(corpList)) {
            return Lists.newArrayList();
        }
        return corpList.stream().map(PopCorporationDto::getOrgId).collect(Collectors.toList());
    }

    @RequestMapping("/edit/taskConfigStatus")
    @ResponseBody
    public ResponseVo updateTaskConfigStatus(String id, Integer status) {
        try {
            if (StringUtils.isBlank(id) || status == null) {
                return ResponseVo.errRest("参数不能为空");
            }
            List<Integer> ids = JSONArray.parseArray(id, Integer.class);
            Boolean aBoolean = popErpUtilService.updateCorpTaskConfigStatus(ids, status);
            return aBoolean ? ResponseVo.successResultNotData() : ResponseVo.errRest("修改失败");
        } catch (Exception e) {
            log.error("#更新企业配置任务状态失败#", e);
            return ResponseVo.errRest("修改状态失败");
        }


    }

    @PostMapping("/edit/delTaskConfig")
    @ResponseBody
    public ResponseVo delTaskConfig(String orgId, Integer taskGroup) {

        try {
            if (StringUtils.isBlank(orgId) || taskGroup == null) {
                return ResponseVo.errRest("参数不能为空");
            }

            Boolean aBoolean = popErpUtilService.delCorpTaskConfig(orgId, taskGroup);
            return aBoolean ? ResponseVo.successResultNotData() : ResponseVo.errRest("修改失败");
        } catch (Exception e) {
            log.error("#更新企业配置任务状态失败#", e);
            return ResponseVo.errRest("修改状态失败");
        }
    }

    @Reference
    private FixProductUtilApi fixProductUtilApi;

    @RequestMapping("/brushProductId")
    @ResponseBody
    public ResponseVo brushProductId(String barcodes) {
        if (StringUtils.isBlank(barcodes)) {
            return ResponseVo.errRest("请输入商品编码");
        }
        List<String> collect = Arrays.stream(barcodes.split(",")).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return ResponseVo.errRest("商品编码填写错误");
        }
        try {
            ApiRPCResult apiRPCResult = fixProductUtilApi.fixProductStandId(barcodes);
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return ResponseVo.errRest("操作失败");
            }
            return ResponseVo.errRest("操作成功");
        } catch (Exception e) {
            log.error("brushProductId error", e);
            return ResponseVo.errRest("操作失败");
        }
    }


    @RequestMapping("/refreshProductId")
    @ResponseBody
    public ResponseVo<NullObject> refreshProductId(String barcode, String productId) {
        if (StringUtils.isBlank(barcode) || StringUtils.isEmpty(productId)) {
            return ResponseVo.errRest("请输入商品编码及标准库id");
        }
        try {
            ApiRPCResult result = popSkuApi.refreshProductId(barcode, productId);
            if(null != result && result.isFail()){
                return ResponseVo.errRest(result.getErrMsg());
            }
            return ResponseVo.errRest("操作成功");
        } catch (Exception e) {
            log.error("refreshProductId error", e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

}
