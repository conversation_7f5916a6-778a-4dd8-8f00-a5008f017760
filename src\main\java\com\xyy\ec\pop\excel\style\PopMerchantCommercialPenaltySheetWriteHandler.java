package com.xyy.ec.pop.excel.style;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.util.List;

/**
 * 商业扣罚样式调整
 * <AUTHOR>
 * @date 2024/9/2 16:51
 */
public class PopMerchantCommercialPenaltySheetWriteHandler implements SheetWriteHandler {
    //扣罚原因下拉
    private final List<String> penaltyReasons;

    public PopMerchantCommercialPenaltySheetWriteHandler(List<String> penaltyReasons) {
        this.penaltyReasons = penaltyReasons;
    }
    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {
        Sheet sheet = context.getWriteSheetHolder().getSheet();
        DataValidationHelper validationHelper = sheet.getDataValidationHelper();
        // 将规则应用于工作表
        sheet.addValidationData(penaltyReason(validationHelper));
        sheet.addValidationData(isCompensateCustomer(validationHelper));
    }

    private  DataValidation penaltyReason(DataValidationHelper validationHelper) {
        // 定义下拉选项
        String[] options = penaltyReasons.toArray(new String[0]);
        // 创建数据验证约束
        DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(options);
        // 设置数据验证规则
        DataValidation dataValidation = validationHelper.createValidation(constraint, new CellRangeAddressList(1, 100, 0, 0));
        return dataValidation;
    }
    private  DataValidation isCompensateCustomer(DataValidationHelper validationHelper) {
        // 定义下拉选项
        String[] options = {"是", "否"};
        // 创建数据验证约束
        DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(options);
        // 设置数据验证规则
        DataValidation dataValidation = validationHelper.createValidation(constraint, new CellRangeAddressList(1, 100, 4, 4));
        return dataValidation;
    }
}
