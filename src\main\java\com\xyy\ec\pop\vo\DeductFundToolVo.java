package com.xyy.ec.pop.vo;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
@Data
public class DeductFundToolVo implements Serializable {
    private static final long serialVersionUID = 199768476816494003L;

    //商户编号
    private String mntNo;
    //扣减保证金金额
    private BigDecimal amount;
    //保证金扣减备注
    private String fundRemarks;
    //佣金记录单号(多个是逗号隔开)
    private String hireNo;
    //佣金记录备注
    private String hireNoRemarks;

    public String validParam() {
        if (StringUtils.isEmpty(mntNo)) {
            return "商户编号不能为空";
        }
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return "扣减金额必须是正数";
        }
        if (StringUtils.isEmpty(fundRemarks)) {
            return "保证金扣减备注不能为空";
        }
        return "";
    }
}
