package com.xyy.ec.pop.utils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public class WebContext {
    /**
     * 得到当前request
     * 
     * @return request
     */
    public static HttpServletRequest currentRequest() {
    	ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		return sra.getRequest();
    }

    /**
     * 得到当前response
     * 
     * @return response
     */
    public static HttpServletResponse currentResponse() {
    	ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		return sra.getResponse();
    }
}
