package com.xyy.ec.pop.redis;

import org.springframework.data.redis.core.ZSetOperations.TypedTuple;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Created by liyang-macbook on 2017/6/22.
 */
public interface IRedisService {
    Boolean setValue(String key, String value, long timeOut);

    Boolean setValue(String key, String value, TimeUnit timeUnit, long timeOut);

    long incrementValue(String key,long value);

    String getValue(String key);
    
    void setHashValue(String key, String hashKey, Object value);

    void setHashAll(String key, Map<String, Object> map);

    Object getHashValue(String key, String hashKey);

    Set<Object> getKey(String key);

    void addSetValue(String key, Set<TypedTuple<String>> values);

    Set<String> getSetValue(String key,int start,int end);

    Long countSet(String key);

    void deleteKey(String key);
    
    boolean expireKey(String key, long timeOut);

    boolean expireKey(String key, long timeOut,TimeUnit timeUnit);

    void setValue(String branchList, String s);

    boolean hasKey(String key);

    Long setNx(String key, String value);

    boolean setNx(String key,String value, long time);

    String getNx(String key);
}
