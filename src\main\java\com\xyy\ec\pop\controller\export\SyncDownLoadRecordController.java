package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.helper.DownloadHelper;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileDto;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileQuery;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.vo.DictVo;
import com.xyy.ec.pop.vo.DownloadFileVo;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.ResultSet;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 查询下载记录
 */
@RestController
@RequestMapping("/downloadRecord")
@Slf4j
public class SyncDownLoadRecordController extends BaseController {
    @Autowired
    private DownloadRemote downloadRemote;

    /**
     * 查询到处类型
     *
     * @return
     */
    @RequestMapping("/typeList")
    public ResponseVo<List<DictVo>> businessTypeList() {
        DownloadTaskBusinessTypeEnum[] values = DownloadTaskBusinessTypeEnum.values();
        List<DownloadTaskBusinessTypeEnum> enums = Arrays.asList(DownloadTaskBusinessTypeEnum.values());
        List<DictVo> list = enums.stream().map(item -> new DictVo(String.valueOf(item.getType()), item.getDesc())).collect(Collectors.toList());
        return ResponseVo.successResult(list);
    }

    @RequestMapping("/list")
    public ResponseVo<PageInfo<DownloadFileVo>> list(DownloadFileQuery query) {
        try {
            log.info("SyncDownLoadRecordController.list#query:{}", JSON.toJSONString(query));
            query.setUser(getUser().getEmail());
            query.setSysType(DownloadTaskSysTypeEnum.ADMIN.getType());
            PageInfo<DownloadFileDto> page = downloadRemote.queryDownloadListForAdmin(query);
            return ResponseVo.successResult(DownloadHelper.buildVoPageInfo(page));
        } catch (Exception e) {
            log.error("SyncDownLoadRecordController.list#query:{} 异常", JSON.toJSONString(query), e);
            return ResponseVo.errRest("查询异常");
        }
    }

    @RequestMapping("/all/list")
    public ResponseVo allList(DownloadFileQuery query){
        try {
            log.info("SyncDownLoadRecordController.allList param {}",JSON.toJSONString(query));
            PageInfo<DownloadFileDto> downloadFileDtoPageInfo = downloadRemote.queryDownloadListForAdmin(query);
            return ResponseVo.successResult(DownloadHelper.buildToolVoPageInfo(downloadFileDtoPageInfo));
        }catch (Exception e){
            log.error("SyncDownLoadRecordController.allList error {}",JSON.toJSONString(query),e);
            return ResponseVo.errRest("查询异常");
        }
    }
}
