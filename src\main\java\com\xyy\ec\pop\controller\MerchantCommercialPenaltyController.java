package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.exception.XyyEcOrderBizNoneCheckRTException;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.backend.pop.dto.PopOrderDto;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.dto.PopMerchantCommercialPenaltyQueryDto;
import com.xyy.ec.pop.dto.PopMerchantFundAuditQueryDto;
import com.xyy.ec.pop.excel.PopMerchantCommercialPenaltyListener;
import com.xyy.ec.pop.excel.style.PopMerchantCommercialPenaltySheetWriteHandler;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.marketing.remote.MerchantRemoteAdapter;
import com.xyy.ec.pop.remote.CorporationRemote;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.remote.EcOrderRemote;
import com.xyy.ec.pop.remote.PopMerchantTotalFundAccountRemoteAdapter;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.merchant.api.admin.PopMerchantCommercialPenaltyApi;
import com.xyy.ec.pop.server.api.merchant.api.enums.MarketingServiceFundChangeEnum;
import com.xyy.ec.pop.server.api.merchant.api.enums.MarketingServiceOperationEnum;
import com.xyy.ec.pop.server.api.merchant.dto.*;
import com.xyy.ec.pop.server.api.merchant.param.*;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.PopMerchantCommercialPenaltyVo;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.pop.vo.order.OrderAdminVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 商户扣罚控制类
 */
@Slf4j
@RestController
@Api(tags = "商户扣罚控制类")
@RequestMapping("/merchant/penalty")
public class MerchantCommercialPenaltyController extends BaseController {

    @Reference
    private PopMerchantCommercialPenaltyApi popMerchantCommercialPenaltyApi;

    @Autowired
    private PopMerchantTotalFundAccountRemoteAdapter popMerchantTotalFundAccountRemoteAdapter;

    @Autowired
    private EcOrderRemote ecOrderRemote;
    @Autowired
    private CorporationRemote corporationRemote;
    @Autowired
    private DownloadRemote downloadRemote;
    /**
     * 手工扣商业资金账户原因
     */
    @Value("${merchant.penalty.reason.config:}")
    private String merchantPenaltyReasonDescConfig;


    /**
     * 扣罚数量条数限制
     */
    @Value("${max.penalty.count:1}")
    private Integer maxPenaltyCount;

    /**
     * 批量扣罚开关
     */
    @Value("${merchant.penalty.batch.enable:true}")
    private Boolean batchPenaltyEnable;

    /**
     * 商户扣罚列表
     * @param queryDto
     * @return
     */
    @GetMapping("/list")
    public ResponseVo<PageInfo<PopMerchantCommercialPenaltyDto>> queryPage(PopMerchantCommercialPenaltyQueryDto queryDto) {
        try {
            log.info("MerchantCommercialPenaltyController.queryPage#param:{}", JSON.toJSONString(queryDto));
            if (queryDto == null || queryDto.getPageNum() == null || queryDto.getPageNum() < 1 || queryDto.getPageSize() == null || queryDto.getPageSize() < 1) {
                return ResponseVo.errRest("请检查分页参数");
            }
            PopMerchantCommercialPenaltyParam param = new PopMerchantCommercialPenaltyParam();
            BeanUtils.copyProperties(queryDto, param);
            param.setStartTime(DateUtil.modifyStartTime(queryDto.getStartTime()));
            param.setEndTime(DateUtil.modifyEndTime(queryDto.getEndTime()));
            param.setProvId(queryDto.getMerchantProvId());
            log.info("popMerchantCommercialPenaltyApi.selectPageList # param:{} ", JSON.toJSONString(param));
            ApiRPCResult<PageInfo<PopMerchantCommercialPenaltyDto>> result = popMerchantCommercialPenaltyApi.selectPageList(param);
            if (Objects.isNull(result) || result.isFail()) {
                return ResponseVo.errRest(result.getErrMsg());
            }
            return ResponseVo.successResult(result.getData());
        } catch (Exception e) {
            log.error("MerchantCommercialPenaltyController.queryPage#error. param:{}", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("查询商户扣罚列表失败");
        }
    }

    /**
     * 查询保证金或营销服务额度
     * @param orgId
     * @param fundPropertyStatus
     * @return
     */
    @GetMapping("/accountInfo")
    public ResponseVo<MerchantTotalFundInfoDto> queryPenalty(String orgId, Integer fundPropertyStatus) {
        log.info("MerchantCommercialPenaltyController.queryPenalty#orgId:{},fundPropertyStatus:{}", orgId, fundPropertyStatus);
        if (orgId == null || fundPropertyStatus == null) {
            return ResponseVo.errRest("商户编码或资金类型不能为空");
        }
        MerchantTotalFundInfoParam param = new MerchantTotalFundInfoParam();
        param.setOrgId(orgId);
        param.setFundPropertyStatus(fundPropertyStatus);
        MerchantTotalFundInfoDto accountInfo = popMerchantTotalFundAccountRemoteAdapter.getAccountInfo(param);
        if (accountInfo == null){
            return ResponseVo.errRest("查询商户资金账户信息失败");
        }
        return ResponseVo.successResult(accountInfo);
    }

    /**
     * 查询订单信息
     * @param orderNo
     * @return
     */
    @GetMapping("/queryOrder")
    public ResponseVo<OrderAdminVo> queryOrder(String orderNo) {
        log.info("MerchantCommercialPenaltyController.queryOrder#orderNo:{}", orderNo);
        if (StringUtils.isEmpty(orderNo)) {
            return ResponseVo.errRest("订单号不能为空");
        }
        PopOrderDto popOrderDto = ecOrderRemote.queryOrderInfo(orderNo);
        if (popOrderDto == null){
            return ResponseVo.errRest("查询订单信息失败");
        }
        List<CorporationDto> corporationList = corporationRemote.queryCorpBaseByOrgIds(Collections.singletonList(popOrderDto.getOrgId()));
        if (CollectionUtils.isEmpty(corporationList)){
            return ResponseVo.errRest("查询商户信息失败");
        }
        CorporationDto corInfo= corporationList.get(0);
        OrderAdminVo vo = new OrderAdminVo();
        BeanUtils.copyProperties(popOrderDto,vo);
        vo.setCorporationName(corInfo.getName());
        vo.setCorporationNo(corInfo.getOrgId());
        vo.setCompanyName(corInfo.getCompanyName());
        return ResponseVo.successResult(vo);
    }

    /**
     * 查询商户信息
     * @param search
     * @return
     */
    @GetMapping("/queryCorporation")
    public ResponseVo<List<CorporationDto>> queryCorporation(String search) {
        log.info("MerchantCommercialPenaltyController.queryCorporation#orgId:{}", search);
        if (StringUtils.isEmpty(search)) {
            return ResponseVo.errRest("商户编码或名称不能为空");
        }
        CorporationDto corporationDto = new CorporationDto();
        corporationDto.setSearch(search);
        List<CorporationDto> corporationList = corporationRemote.queryList(corporationDto);
        if (CollectionUtils.isEmpty(corporationList)){
            return ResponseVo.errRest("查询商户信息失败");
        }
        return ResponseVo.successResult(corporationList);
    }

    /**
     * 商业扣罚原因
     * @return
     */
    @GetMapping("/reason")
    public ResponseVo<List<MerchantPenaltyReasonDto>> queryPenaltyReason() {
        return ResponseVo.successResult(penaltyReasons());
    }

    /**
     * 新增商业扣罚
     * @param param
     * @return
     */
    @PostMapping("/add")
    public ResponseVo<PageInfo<PopMerchantCommercialPenaltyDto>> addPenalty(@RequestBody PopMerchantCommercialPenaltyParam param) {
        try {
            log.info("MerchantCommercialPenaltyController.addPenalty#param:{}", JSON.toJSONString(param));
            if (param == null || param.getPenaltyReason() == null) {
                return ResponseVo.errRest("入参或扣罚原因不能为空");
            }
            //金额负数返回错误
            if (param.getActualReceivedAmount().compareTo(BigDecimal.ZERO) < 0) {
                return ResponseVo.errRest("扣罚金额不能为负数");
            }
            if (Objects.equals(param.getPenaltyReason(), 11)&& Objects.equals(param.getIsCompensateCustomer() ,1)) {
                return ResponseVo.errRest("扣罚原因为商业扣罚退款时，是否赔偿客户必须为否");
            }

            //penaltyReason 1 : 退还保证金 2 : 退营销服务额度
            if (Objects.equals(param.getPenaltyReason(), 1) || Objects.equals(param.getPenaltyReason(), 2)) {
                MerchantAccountDeductionParam deductionParam = new MerchantAccountDeductionParam();
                if (Objects.equals(param.getPenaltyReason(), 1)) {
                    deductionParam = create(param, MarketingServiceFundChangeEnum.REFUND_MARGIN.getCode());
                } else if (Objects.equals(param.getPenaltyReason(), 2)) {
                    param.setDescription("退" + param.getActualReceivedAmount() +"元，实际打款" + param.getPaymentAmount() + "元");
                    deductionParam = create(param, MarketingServiceFundChangeEnum.RETURN_MARKETING_SERVICE_QUOTA.getCode());
                }
                ApiRPCResult<Boolean> booleanApiRPCResult = popMerchantTotalFundAccountRemoteAdapter.accountDeduction(deductionParam);
               if (booleanApiRPCResult.isFail()){
                   return ResponseVo.errRest("扣罚失败:"+booleanApiRPCResult.getErrMsg());
               }
            }else {
                MerchantBatchFailDeductionDto merchantBatchFailDeductionDto = popMerchantTotalFundAccountRemoteAdapter.batchDeduction(conversion(Collections.singletonList(param)));
                if (merchantBatchFailDeductionDto !=null&&!CollectionUtils.isEmpty(merchantBatchFailDeductionDto.getFailDeductionList())) {
                    MerchantFailDeductionDto failDeductionDto = merchantBatchFailDeductionDto.getFailDeductionList().get(0);
                    return ResponseVo.errRest("扣罚失败:"+failDeductionDto.getOrderNo()+":"+failDeductionDto.getFailResult());
                }
            }
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("MerchantCommercialPenaltyController.addPenalty#error. param:{}", JSON.toJSONString(param), e);
            return ResponseVo.errRest("查询商户扣罚列表失败");
        }
    }

    private MerchantAccountDeductionParam create(PopMerchantCommercialPenaltyParam param, Integer fundChangeType) {
        MerchantAccountDeductionParam deductionParam =new MerchantAccountDeductionParam();
        deductionParam.setOrderNo(param.getOrderNo());
        deductionParam.setOrgId(param.getOrgId());
        deductionParam.setDescription(param.getDescription());
        deductionParam.setAmount(param.getPaymentAmount());
        deductionParam.setActualAmount(param.getActualReceivedAmount());
        deductionParam.setIsCompensateCustomer(param.getIsCompensateCustomer());
        deductionParam.setDeductionRatio(param.getDeductionRatio());
        deductionParam.setDeductionType(param.getPenaltyReason());
        deductionParam.setDeductionTypeId(param.getPenaltyReason());
        deductionParam.setFundChangeType(fundChangeType);
        deductionParam.setSubmitter(getUser().getUsername());
        deductionParam.setSubmitterId(getUser().getId());
        deductionParam.setFundChangeStatus(MarketingServiceOperationEnum.DEDUCTION.getCode());
        return deductionParam;
    }

    private  MerchantBatchDeductionParam conversion(List<PopMerchantCommercialPenaltyParam> params) {
        MerchantBatchDeductionParam batchDeductionParam = new MerchantBatchDeductionParam();
        //penaltyReasons 将 id 和 name 映射
        Map<Integer, String> penaltyReasonMap = penaltyReasons().stream().collect(Collectors.toMap(MerchantPenaltyReasonDto::getId, MerchantPenaltyReasonDto::getName));
        List<MerchantOtherDeductionParam> paramList=new ArrayList<>();
        for (PopMerchantCommercialPenaltyParam param : params) {
            MerchantOtherDeductionParam deductionParam = new MerchantOtherDeductionParam();
            deductionParam.setOrderNo(param.getOrderNo());
            deductionParam.setDeductionTypeId(param.getPenaltyReason());
            deductionParam.setDescription(param.getDescription());
            deductionParam.setAmount(param.getPaymentAmount());
            deductionParam.setActualAmount(param.getActualReceivedAmount());
            deductionParam.setIsCompensateCustomer(param.getIsCompensateCustomer());
            deductionParam.setCustomerName(param.getCustomerName());
            deductionParam.setDeductionTypeName(penaltyReasonMap.get(param.getPenaltyReason()));
            deductionParam.setOrgId(param.getOrgId());
            if (Objects.equals(param.getPenaltyReason(),11)) {
                deductionParam.setFundChangeStatus(MarketingServiceOperationEnum.RETURN.getCode());
            }else {
                deductionParam.setFundChangeStatus(MarketingServiceOperationEnum.DEDUCTION.getCode());
            }
            paramList.add(deductionParam);
        }
        batchDeductionParam.setDeductionList(paramList);
        batchDeductionParam.setSubmitter(getUser().getUsername());
        batchDeductionParam.setSubmitterId(getUser().getId());
        return batchDeductionParam;
    }


    /**
     * 商业扣罚模版下载
     * @param response
     * @return
     */
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws IOException {
       List<MerchantPenaltyReasonDto> configList = penaltyReasons();
        if (!StringUtils.isEmpty(configList)) {
            List<String> penaltyReasons = Optional.of(configList).orElse(new ArrayList<>()).stream()
                    .filter(c->!Objects.equals(c.getId(), 1)||!Objects.equals(c.getId(), 2))
                    .map(MerchantPenaltyReasonDto::getName).collect(Collectors.toList());
            // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("商业资金账号扣罚模版", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), PopMerchantCommercialPenaltyVo.class).registerWriteHandler(new PopMerchantCommercialPenaltySheetWriteHandler(penaltyReasons)).sheet("商业资金账号扣罚模版").doWrite(new ArrayList<>());
        }
    }

    /**
     * 商业扣罚导入
     * @param file
     * @return
     */
    @PostMapping("/import")
    @AvoidRepeatableCommit(timeout = 60)
    public ResponseVo batchImportPenalty(MultipartFile file) {

        if(!batchPenaltyEnable){
            return ResponseVo.errRest("批量扣罚功能维护中,请手动录入");
        }
        try {
            PopMerchantCommercialPenaltyListener penaltyListener=new PopMerchantCommercialPenaltyListener();
            EasyExcel.read(file.getInputStream(), PopMerchantCommercialPenaltyVo.class, penaltyListener).sheet().doRead();
            // 获取数据
            List<PopMerchantCommercialPenaltyVo> vos = penaltyListener.getVos();
            if (CollectionUtils.isEmpty(vos)) {
                return ResponseVo.errRest("导入数据为空");
            }
            List<MerchantPenaltyReasonDto> penaltyReasons = penaltyReasons();

            if (CollectionUtils.isEmpty(penaltyReasons)) {
                return ResponseVo.errRest("扣罚原因配置为空");
            }

            Map<String,Integer> penaltyReasonMap = penaltyReasons.stream().collect(Collectors.toMap(MerchantPenaltyReasonDto::getName, MerchantPenaltyReasonDto::getId, (v1, v2) -> v1));
            List<String> orderNos = vos.stream().filter(c -> !StringUtils.isEmpty(c)).map(PopMerchantCommercialPenaltyVo::getOrderNo).collect(Collectors.toList());
            List<PopOrderDto> popOrders = ecOrderRemote.queryOrderBaseList(orderNos);
            Map<String,PopOrderDto> orderDtoMap = popOrders.stream().collect(Collectors.toMap(PopOrderDto::getOrderNo, Function.identity(), (v1, v2) -> v1));
            //校验导入参数
            ResponseVo<Object> checked = checkImport(vos, orderDtoMap, penaltyReasonMap);
            if (Objects.nonNull(checked)) {
                return checked;
            }
            //拼接导入参数
            List<PopMerchantCommercialPenaltyParam> params = new ArrayList<>();
            for (PopMerchantCommercialPenaltyVo vo : vos) {
                PopMerchantCommercialPenaltyParam param = new PopMerchantCommercialPenaltyParam();
                PopOrderDto popOrderDto = orderDtoMap.get(vo.getOrderNo());
                param.setOrderNo(vo.getOrderNo());
                param.setOrderId(popOrderDto.getId());
                param.setOrgId(orderDtoMap.get(vo.getOrderNo()).getOrgId());
                param.setPenaltyReason(penaltyReasonMap.get(vo.getPenaltyReason()));
                param.setDescription(vo.getDescription());
                param.setPaymentAmount(vo.getActualReceivedAmount());
                param.setActualReceivedAmount(vo.getActualReceivedAmount());
                param.setIsCompensateCustomer(Objects.equals(vo.getIsCompensateCustomer(),"是")?1:2);
                param.setOrgId(popOrderDto.getOrgId());
                param.setCustomerName(popOrderDto.getMerchantName());
                params.add(param);
            }

            List<List<PopMerchantCommercialPenaltyParam>> partition = Lists.partition(params, maxPenaltyCount);
            List<MerchantFailDeductionDto> deductionDtos=new ArrayList<>();
            for (List<PopMerchantCommercialPenaltyParam> popMerchantCommercialPenaltyParams : partition) {
                //调用扣罚服务
                MerchantBatchFailDeductionDto merchantBatchFailDeductionDto = popMerchantTotalFundAccountRemoteAdapter.batchDeduction(conversion(popMerchantCommercialPenaltyParams));
                if(Objects.nonNull(merchantBatchFailDeductionDto)&&!CollectionUtils.isEmpty(merchantBatchFailDeductionDto.getFailDeductionList())){
                    deductionDtos.addAll(merchantBatchFailDeductionDto.getFailDeductionList());
                }
            }
            //调用扣罚服务
            if(CollectionUtils.isEmpty(deductionDtos)){
                return ResponseVo.successResultNotData();
            }
            return ResponseVo.errRest("导入数据失败",deductionDtos);
        }catch (Exception e) {
            log.error("MerchantCommercialPenaltyController.batchImportPenalty#error. file:{}", file, e);
            return ResponseVo.errRest("导入数据失败");
        }
    }


    /**
     * 商业扣罚导出
     * @param queryDto
     * @return
     */
    @GetMapping(value = "/export")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> export(PopMerchantCommercialPenaltyQueryDto queryDto) {
        log.info("MerchantCommercialPenaltyController.export#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            PopMerchantCommercialPenaltyParam param = new PopMerchantCommercialPenaltyParam();
            BeanUtils.copyProperties(queryDto, param);
            param.setProvId(queryDto.getMerchantProvId());
            param.setStartTime(DateUtil.modifyStartTime(queryDto.getStartTime()));
            param.setEndTime(DateUtil.modifyEndTime(queryDto.getEndTime()));
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(param)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.COMMERCIAL_DEDUCTIONS)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("MerchantCommercialPenaltyController.export#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("MerchantCommercialPenaltyController.export#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    /**
     * 商业扣罚失败重试
     * @param id
     * @return
     */
    @GetMapping(value = "/retry/{id}")
    @AvoidRepeatableCommit(timeout = 30)
    public ResponseVo<Boolean> retry(@PathVariable Long id) {
        log.info("MerchantCommercialPenaltyController.retry#id:{}", id);
        try {
            if (Objects.isNull(id)) {
                return ResponseVo.errRest("入参异常");
            }
            MerchantRetryDeductionParam param = new MerchantRetryDeductionParam();
            param.setFundFlowId(id);
            if(Objects.nonNull(getUser())){
                param.setSubmitter(getUser().getUsername());
                param.setSubmitterId(getUser().getId());
            }
            ApiRPCResult<Boolean> result=popMerchantTotalFundAccountRemoteAdapter.retryDeduction(param);
            if(Objects.isNull(result)|| result.isFail()){
                return ResponseVo.errRest(result.getMsg());
            }
            return ResponseVo.successResult(result.getData());
        } catch (Exception e) {
            log.error("MerchantCommercialPenaltyController.retry#id:{} 异常",id, e);
            return ResponseVo.errRest("重试失败，请稍后重试或联系管理员");
        }
    }


    private static ResponseVo<Object> checkImport(List<PopMerchantCommercialPenaltyVo> vos, Map<String, PopOrderDto> orderDtoMap, Map<String, Integer> penaltyReasonMap) {
        if (CollectionUtils.isEmpty(vos)) {
            return ResponseVo.errRest("导入数据为空");
        }

        for (int i = 0; i < vos.size(); i++) {
            //YBM单号是否存在，不存在的话，提示订单号XX不存在
            if (StringUtils.isEmpty(vos.get(i).getOrderNo())) {
                return ResponseVo.errRest("第" + (i + 2) + "行，YBM单号不能为空");
            }
            if (!orderDtoMap.containsKey(vos.get(i).getOrderNo())) {
                return ResponseVo.errRest("第" + (i + 2) + "行，YBM单号不存在");
            }

            //扣罚原因是否固定枚举值，且不能为退还保证金/退营销服务额度，不合规的话提示’第几行扣罚原因输入错误‘
            if (StringUtils.isEmpty(vos.get(i).getPenaltyReason())) {
                return ResponseVo.errRest("第" + (i + 2) + "行，扣罚原因不能为空");
            }

            if (!penaltyReasonMap.containsKey(vos.get(i).getPenaltyReason())) {
                return ResponseVo.errRest("第" + (i + 2) + "行，扣罚原因输入错误");
            }
            Integer penaltyReasonId = penaltyReasonMap.get(vos.get(i).getPenaltyReason());
            if (Objects.equals(penaltyReasonId, 1) || Objects.equals(penaltyReasonId, 2)) {
                return ResponseVo.errRest("第" + (i + 2) + "行，扣罚原因不能为退还保证金/退营销服务额度");
            }
            if (Objects.equals(penaltyReasonId, 11)) {
                return ResponseVo.errRest("第" + (i + 2) + "行，扣罚原因不能为商业扣罚退回");
            }
            //扣罚金额必须＞0，最多2位小数
            if (StringUtils.isEmpty(vos.get(i).getActualReceivedAmount())) {
                return ResponseVo.errRest("第" + (i + 2) + "行，扣罚金额不能为空");
            }

            if (vos.get(i).getActualReceivedAmount().signum() <= 0 || vos.get(i).getActualReceivedAmount().stripTrailingZeros().scale() > 2) {
                return ResponseVo.errRest("第" + (i + 2) + "行，扣罚金额最多2位小数");
            }

            //是否赔偿客户，必填，只能为是或者否
            if (StringUtils.isEmpty(vos.get(i).getIsCompensateCustomer())) {
                return ResponseVo.errRest("第" + (i + 2) + "行，是否赔偿客户不能为空");
            }
            if (!Objects.equals(vos.get(i).getIsCompensateCustomer(), "是") && !Objects.equals(vos.get(i).getIsCompensateCustomer(), "否")) {
                return ResponseVo.errRest("第" + (i + 2) + "行，是否赔偿客户输入错误.只能为是或者否");
            }
        }
        return null;
    }

    public List<MerchantPenaltyReasonDto> penaltyReasons() {
        if (StringUtils.isEmpty(merchantPenaltyReasonDescConfig)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(merchantPenaltyReasonDescConfig, MerchantPenaltyReasonDto.class);
    }

}
