package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.server.api.order.api.PopOrderExceptionApi;
import com.xyy.ec.pop.server.api.order.dto.PopOrderExceptionDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class PopOrderExceptionRpc {
    @Reference
    private PopOrderExceptionApi popOrderExceptionApi;

    public List<PopOrderExceptionDto> batchQueryOrderExceptionByOrderNos(List<String> orderNos){
        if (CollectionUtils.isEmpty(orderNos)){
            return Lists.newArrayList();
        }
        try {
            ApiRPCResult<List<PopOrderExceptionDto>> listApiRPCResult = popOrderExceptionApi.batchQueryOrderExceptionByOrderNos(orderNos);
            if (listApiRPCResult == null || listApiRPCResult.isFail()){
                return null;
            }
            return listApiRPCResult.getData();
        }catch (Exception e){
            log.error("PopOrderExceptionRpc.batchQueryOrderExceptionByOrderNos-error",e);
            return null;
        }
    }

    public List<PopOrderExceptionDto> queryExceptionByOrderNo(String orderNo){
        log.info("PopOrderExceptionRpc.queryExceptionByOrderNo request orderNo:{}",orderNo);
        if (StringUtils.isBlank(orderNo)){
            throw new PopAdminException("订单号无效");
        }
        try {
            ApiRPCResult<List<PopOrderExceptionDto>> listApiRPCResult = popOrderExceptionApi.queryOrderExceptionByOrderNo(orderNo);
            log.info("PopOrderExceptionRpc.queryExceptionByOrderNo response order:{}-result:{}",orderNo, JSON.toJSONString(listApiRPCResult));
            if (listApiRPCResult == null || listApiRPCResult.isFail()){
                throw new PopAdminException("查询异常信息失败");
            }
            return listApiRPCResult.getData();
        }catch (PopAdminException pse){
            throw pse;
        }catch (Exception e){
            throw new PopAdminException("查询异常信息失败");
        }
    }

    public List<PopOrderExceptionDto> queryExceptionByExceptionIds(List<Long> exceptionIds) {
        log.info("PopOrderExceptionRpc.queryExceptionByExceptionIds request exceptionIds:{}",exceptionIds);
        if (CollectionUtils.isEmpty(exceptionIds)){
            throw new PopAdminException("异常id无效");
        }
        try {
            ApiRPCResult<List<PopOrderExceptionDto>> listApiRPCResult = popOrderExceptionApi.queryOrderExceptionByExceptionIds(exceptionIds);
            log.info("PopOrderExceptionRpc.queryExceptionByExceptionIds response exceptionIds:{}-result:{}",exceptionIds, JSON.toJSONString(listApiRPCResult));
            if (listApiRPCResult == null || listApiRPCResult.isFail()){
                throw new PopAdminException("查询异常信息失败");
            }
            return listApiRPCResult.getData();
        }catch (PopAdminException pse){
            throw pse;
        }catch (Exception e){
            throw new PopAdminException("查询异常信息失败");
        }
    }
}
