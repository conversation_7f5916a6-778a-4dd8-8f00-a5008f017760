package com.xyy.ec.pop.enums;

/**
 * 退款原因枚举
 */
public enum RefundReasonEnum {

    
    WRONG_ORDER("订单下错"),
    NO_DISCOUNT("未享受优惠)"),
    WRONG_ITEM("商品买错了"),
    TOO_MANY_ITEMS("商品买多了"),
    WRONG_ACCOUNT("账户下错了"),
    TOO_FEW_ITEMS("商品拍少了"),
    UNUSED_COUPON("优惠券未使用"),
    UNUSED_RED_PACKET("红包未使用"),
    MISSED_PROMOTION("未享受活动"),
    FAKE_SHIPMENT("虚假发货"),
    NO_LOGISTICS("快递单号无物流"),
    WRONG_RECIPIENT("收件人信息不符"),
    LOGISTICS_MISMATCH("快递单号物流信息与实际不符");

    private final String description;

    RefundReasonEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return this.description;
    }
}