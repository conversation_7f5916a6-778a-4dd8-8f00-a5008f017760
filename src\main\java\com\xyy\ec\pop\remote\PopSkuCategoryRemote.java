package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuCategoryAdminApi;
import com.xyy.ec.pop.server.api.product.dto.PopSkuCategoryDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuCommissionRatioOperateLogDto;
import com.xyy.ec.pop.server.api.product.dto.ProductCommissionDto;
import com.xyy.ec.pop.server.api.product.query.ProductCommissionQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 商品分类、佣金查询接口
 */
@Slf4j
@Component
public class PopSkuCategoryRemote {
    @Reference
    private PopSkuCategoryAdminApi popSkuCategoryAdminApi;

    public PageInfo<ProductCommissionDto> commissionPage(ProductCommissionQuery query) {
        try {
            log.info("PopSkuCategoryRemote.commissionPage#query:{}", JSON.toJSONString(query));
            ApiRPCResult<PageInfo<ProductCommissionDto>> result = popSkuCategoryAdminApi.commissionPage(query);
            log.info("PopSkuCategoryRemote.commissionPage#query:{} return {}", JSON.toJSONString(query), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("PopSkuCategoryRemote.commissionPage#query:{} 异常", JSON.toJSONString(query), e);
        }
        return PageInfo.of(new ArrayList<>());
    }

    public Boolean updateCommission(ProductCommissionDto commissionDto, String operater) throws ServiceException {
        try {
            log.info("PopSkuCategoryRemote.updateCommission#commissionDto:{},operater:{}", JSON.toJSONString(commissionDto), operater);
            ApiRPCResult<Boolean> result = popSkuCategoryAdminApi.updateCommission(commissionDto, operater);
            log.info("PopSkuCategoryRemote.updateCommission#commissionDto:{},operater:{} return {}", JSON.toJSONString(commissionDto), operater, JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            if (result.getCode() == ApiResultCodeEum.PARAMETER_ERROR.getCode()||result.getCode() == ApiResultCodeEum.SYSTEM_ERROR.getCode()) {
                throw new ServiceException(result.getErrMsg());
            }
            return false;
        } catch (ServiceException e) {
            log.error("PopSkuCategoryRemote.updateCommission#commissionDto:{},operater:{} 失败：{}", JSON.toJSONString(commissionDto), operater, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("PopSkuCategoryRemote.updateCommission#commissionDto:{},operater:{} 异常", JSON.toJSONString(commissionDto), operater, e);
            throw new ServiceException("更新失败");
        }
    }


    public List<PopSkuCommissionRatioOperateLogDto> queryCommissionRatioLog(String barcode) throws ServiceException {
        try {
            log.info("PopSkuCategoryRemote.queryCommissionRatioLog#barcode:{}", barcode);
            ApiRPCResult<List<PopSkuCommissionRatioOperateLogDto>> result = popSkuCategoryAdminApi.queryCommissionRatioLog(barcode);
            log.info("PopSkuCategoryRemote.queryCommissionRatioLog#barcode:{} return {}", barcode, JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            if (result.getCode() == ApiResultCodeEum.PARAMETER_ERROR.getCode() || result.getCode() == ApiResultCodeEum.SYSTEM_ERROR.getCode()) {
                throw new ServiceException(result.getErrMsg());
            }
            return Lists.newArrayList();
        } catch (ServiceException e) {
            log.error("PopSkuCategoryRemote.queryCommissionRatioLog#barcode:{} 失败：{}", barcode, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("PopSkuCategoryRemote.queryCommissionRatioLog#barcode:{} 异常", barcode, e);
            throw new ServiceException("更新失败");
        }
    }

    public List<ProductCommissionDto> batchUpdateCommission(List<ProductCommissionDto> commissionDtos, String operater) throws ServiceException {
        try {
            log.info("PopSkuCategoryRemote.batchUpdateCommission#commissionDtos:{},operater:{}", JSON.toJSONString(commissionDtos), operater);
            ApiRPCResult<List<ProductCommissionDto>> result = popSkuCategoryAdminApi.batchUpdateCommission(commissionDtos, operater);
            log.info("PopSkuCategoryRemote.batchUpdateCommission#commissionDtos:{},operater:{} return {}", JSON.toJSONString(commissionDtos), operater, JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            throw new ServiceException(result.getErrMsg());
        } catch (ServiceException e) {
            log.error("PopSkuCategoryRemote.batchUpdateCommission#commissionDtos:{},operater:{} 失败：{}", JSON.toJSONString(commissionDtos), operater, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("PopSkuCategoryRemote.batchUpdateCommission#commissionDtos:{},operater:{} 异常", JSON.toJSONString(commissionDtos), operater, e);
            throw new ServiceException("更新失败");
        }
    }

    public Boolean deleteCommission(List<String> barcodes, String operater) throws ServiceException {
        try {
            log.info("PopSkuCategoryRemote.deleteCommission#barcodes:{},operater:{}", JSON.toJSONString(barcodes), operater);
            ApiRPCResult<Boolean> result = popSkuCategoryAdminApi.deleteCommission(barcodes, operater);
            log.info("PopSkuCategoryRemote.deleteCommission#barcodes:{},operater:{} return {}", JSON.toJSONString(barcodes), operater, JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
            throw new ServiceException(result.getErrMsg());
        } catch (ServiceException e) {
            log.error("PopSkuCategoryRemote.deleteCommission#barcodes:{},operater:{} 失败：{}", JSON.toJSONString(barcodes), operater, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("PopSkuCategoryRemote.deleteCommission#barcodes:{},operater:{} 异常", JSON.toJSONString(barcodes), JSON.toJSONString(operater), e);
        }
        return false;
    }

    public Map<String, String> firstCateMap(List<String> barcodes) {
        try {
            log.info("PopSkuCategoryRemote.firstCateMap#barcodes:{}", JSON.toJSONString(barcodes));
            ApiRPCResult<Map<String, String>> result = popSkuCategoryAdminApi.firstCateMap(barcodes);
            log.info("PopSkuCategoryRemote.firstCateMap#barcodes:{} return {}", JSON.toJSONString(barcodes), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("PopSkuCategoryRemote.firstCateMap#barcodes:{} 异常", JSON.toJSONString(barcodes), e);
        }
        return new HashMap<>();
    }

    public List<PopSkuCategoryDto> categoryByBarcodes(List<String> barcodes){
        try {
            log.info("PopSkuCategoryRemote.commissionByBarcodes#barcodes:{}", JSON.toJSONString(barcodes));
            ApiRPCResult<List<PopSkuCategoryDto>> result = popSkuCategoryAdminApi.categoryByBarcodes(barcodes);
            log.info("PopSkuCategoryRemote.commissionByBarcodes#barcodes:{} return {}", JSON.toJSONString(barcodes), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("PopSkuCategoryRemote.commissionByBarcodes#barcodes:{} 异常", JSON.toJSONString(barcodes), e);
        }
        return new ArrayList<>();
    }

    public BigDecimal getHighGrossThreshold() throws ServiceException {
        try {
            log.info("PopSkuCategoryRemote.getHighGrossThreshold#");
            ApiRPCResult<BigDecimal> result = popSkuCategoryAdminApi.getHighGrossThreshold();
            if (result.isSuccess()) {
                return result.getData();
            }
            log.info("PopSkuCategoryRemote.getHighGrossThreshold# return {}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("PopSkuCategoryRemote.getHighGrossThreshold# 异常", e);
        }
        throw new ServiceException("查询高毛最低佣金比例失败");
    }
}
