package com.xyy.ec.pop.vo;

import com.xyy.ec.pop.server.api.order.dto.PopOrderConsignmentDetailDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单明细
 */
@Data
public class OrderDetail implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String productName;//
    private String barcode;//
    private String packageId;//
    private String packageCount;//
    private String manufacturer;//
    private String spec;//
    private double productPrice;//
    private int productAmount;//
    private String refundProductAmount;//
    private int type;//
    private int mediumPackageNum;//
    private int isSplit;//
    private int status;
    /** 优惠金额 */
    private BigDecimal discountAmount;
    /** 小计金额 */
    private BigDecimal subTotal;
    /** 实付金额 */
    private BigDecimal realPayAmount;
    private Long skuId;

    private List<PopOrderConsignmentDetailDto> orderConsignmentDetails;

    private BigDecimal commissionRatio;

    /**
     * 单个商品所有批次实际发货数量之和
     */
    private Integer batchConsNumTotal;

    /**
     * 单个商品所有批次实际发货金额之和
     */
    private BigDecimal batchPriceTotal;

    /**
     * 可发货商品数量
     */
    private int canSendSkuCount;
    /**
     * 可发货商品金额
     */
    private BigDecimal canSendSkuAmount;
    /**
     * 商品erp编码
     */
    private String erpCode;

    /** 店铺优惠券金额 */
    private BigDecimal shopVoucherAmount = BigDecimal.ZERO;
    /** 跨平台优惠券金额 */
    private BigDecimal crossPlatformVoucherAmount = BigDecimal.ZERO;

    /** 店铺活动优惠 营销获取*/
    private BigDecimal shopPromoDiscount = BigDecimal.ZERO;
    /** 平台活动优惠 营销获取*/
    private BigDecimal platformPromoDiscount = BigDecimal.ZERO;
    /**
     * 平台优惠提示信息
     */
    private List<String> platformDiscountMsgList;
    /**
     *订单优惠金额
     */
    private BigDecimal originalTotalDiscount = BigDecimal.ZERO;

    /**
     * 原商品总金额
     */
    private BigDecimal originalTotalAmount = BigDecimal.ZERO;

    /**
     * 特价活动id
     */
    private String allPromoId;

    /**
     *特价活动id - 红包可以和其他活动共存，单个id 不能表示所有信息
     */
    @Deprecated
    private Long promotionId;

    /** 商品原价*/
    private BigDecimal origPrice;

    /**
     * 现金实付
     */
    private BigDecimal cashPayAmount;

    /**
     * 购物金
     */
    private BigDecimal virtualGold;

    /** 满赠ID  */
    private Long extraGiftId;

    /** 标记是否为满减赠 满赠赠品 1 赠品 0 不是赠品*/
    private Integer extraGift;

    /**
     * 生产许可证号或备案凭证编号
     */
    private String manufacturingLicenseNo;

    /**
     * 医疗器械注册证或备案凭证编号
     */
    private String approvalNumber;

    private String marketAuthor;

    private String commonName;
    private String dosageForm;


}
