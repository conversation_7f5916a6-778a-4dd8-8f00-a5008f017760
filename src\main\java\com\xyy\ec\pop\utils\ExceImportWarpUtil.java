package com.xyy.ec.pop.utils;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.xyy.ec.pop.exception.ServiceException;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @version v1
 * @Description excel导入工具，通过easypoi完成导入解析
 * <AUTHOR>
 */
public class ExceImportWarpUtil {
    public final static String commonTip = "当前上传文件与模版不一致，请重新选择文件上传";
    public static<T> List<T> importExcel(MultipartFile file, Class<T> tClass, ImportParams importParams, int maxFileSize, int maxRows) throws Exception {
        return importExcel(file,tClass,importParams,maxFileSize,maxRows,null);
    }
    /**
     * 导入工具，同时校验文件大小
     * @param file
     * @param tClass
     * @param importParams
     * @param maxFileSize 最大文件大小，单位M
     * @param maxRows 最大行数
     * @param titles 合理的标题
     * @param <T>
     * @return
     * @throws Exception
     */
    public static<T> List<T> importExcel(MultipartFile file, Class<T> tClass, ImportParams importParams, int maxFileSize, int maxRows,List<String> titles) throws Exception {
        if(file.isEmpty()||file.getSize()>maxFileSize*(1024*1024)){
            throw new ServiceException("文件大小不能超过"+maxFileSize+"M");
        }
        String fileName = file.getOriginalFilename();
        if(StringUtils.isEmpty(fileName)||(!fileName.toLowerCase().endsWith(".xls")&&!fileName.toLowerCase().endsWith(".xlsx"))){
            throw new ServiceException(commonTip);
        }
        //校验标题
        if(!CollectionUtils.isEmpty(titles)){
            //TODO 优化，可否控制只读取1行
            ImportParams titleParams = new ImportParams();
            titleParams.setReadRows(1);
            List<Map<String,Object>> actTitles = ExcelImportUtil.importExcel(file.getInputStream(), Map.class,titleParams);
            if(CollectionUtils.isEmpty(actTitles)||actTitles.get(0)==null||!actTitles.get(0).keySet().containsAll(titles)){
                throw new ServiceException(commonTip);
            }
        }
        List<T> result = ExcelImportUtil.importExcel(file.getInputStream(),tClass,importParams);
        if(CollectionUtils.isEmpty(result)){
            throw new ServiceException(commonTip);
        }
        if(result.size()>maxRows){
            throw new ServiceException("导入内容不能超过"+maxRows+"行");
        }
        return result;
    }
}
