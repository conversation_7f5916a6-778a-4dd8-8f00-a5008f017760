<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.ec.pop.dao.PopOperateRecordLogMapper">
    <resultMap id="BaseResultMap" type="com.xyy.ec.pop.po.PopOperateRecordLogPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="operate_name" jdbcType="VARCHAR" property="operateName"/>
        <result column="event_name" jdbcType="VARCHAR" property="eventName"/>
        <result column="event_type" jdbcType="VARCHAR" property="eventType"/>
        <result column="event_content" jdbcType="VARCHAR" property="eventContent"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, operate_name, event_name, event_type, event_content, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_xyy_pop_operate_record_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tb_xyy_pop_operate_record_log
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xyy.ec.pop.po.PopOperateRecordLogPo"
            useGeneratedKeys="true">
        insert into tb_xyy_pop_operate_record_log (operate_name, event_name, event_type,
                                                   event_content, create_time)
        values (#{operateName,jdbcType=VARCHAR}, #{eventName,jdbcType=VARCHAR}, #{eventType,jdbcType=VARCHAR},
                #{eventContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xyy.ec.pop.po.PopOperateRecordLogPo" useGeneratedKeys="true">
        insert into tb_xyy_pop_operate_record_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operateName != null">
                operate_name,
            </if>
            <if test="eventName != null">
                event_name,
            </if>
            <if test="eventType != null">
                event_type,
            </if>
            <if test="eventContent != null">
                event_content,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operateName != null">
                #{operateName,jdbcType=VARCHAR},
            </if>
            <if test="eventName != null">
                #{eventName,jdbcType=VARCHAR},
            </if>
            <if test="eventType != null">
                #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="eventContent != null">
                #{eventContent,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xyy.ec.pop.po.PopOperateRecordLogPo">
        update tb_xyy_pop_operate_record_log
        <set>
            <if test="operateName != null">
                operate_name = #{operateName,jdbcType=VARCHAR},
            </if>
            <if test="eventName != null">
                event_name = #{eventName,jdbcType=VARCHAR},
            </if>
            <if test="eventType != null">
                event_type = #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="eventContent != null">
                event_content = #{eventContent,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xyy.ec.pop.po.PopOperateRecordLogPo">
        update tb_xyy_pop_operate_record_log
        set operate_name  = #{operateName,jdbcType=VARCHAR},
            event_name    = #{eventName,jdbcType=VARCHAR},
            event_type    = #{eventType,jdbcType=VARCHAR},
            event_content = #{eventContent,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>