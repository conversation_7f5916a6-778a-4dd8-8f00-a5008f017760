package com.xyy.ec.pop.controller.export;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.AvoidRepeatableCommit;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskSysTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.QueryCorporationParam;
import com.xyy.ec.pop.server.api.seller.dto.PopCommissionSettleAdminQueryDto;
import com.xyy.ec.pop.server.api.seller.enums.CommissionSettleTypeEnum;
import com.xyy.ec.pop.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/08/06/17:18
 * @Description:
 */
@Slf4j
@Controller
@RequestMapping("/commission/settle/async")
public class CommissionSettleExportController extends BaseController {
    @Autowired
    private DownloadRemote downloadRemote;

    @ResponseBody
    @GetMapping(value = "/export")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> export(PopCommissionSettleAdminQueryDto queryDto) {
        log.info("CommissionSettleExportController.export#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(provIds);
            queryDto.setSettlementType((byte) CommissionSettleTypeEnum.EVERY_MONTH.getCode());//默认查询月结数据
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.COMMISSION_PAYABLE_ADMIN_RECORD)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("CommissionSettleExportController.export#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("CommissionSettleExportController.export#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }

    @ResponseBody
    @GetMapping(value = "/exportDetail")
    @AvoidRepeatableCommit(timeout = 3)
    public ResponseVo<Boolean> exportDetail(PopCommissionSettleAdminQueryDto queryDto) {
        log.info("CommissionSettleExportController.exportDetail#queryDto:{}", JSON.toJSONString(queryDto));
        try {
            List<Long> provIds = getProvIds(queryDto.getProvId());
            if(CollectionUtils.isEmpty(provIds)){
                return ResponseVo.errRest("导出失败，暂无导出数据");
            }
            queryDto.setProvIds(provIds);
            queryDto.setSettlementType((byte) CommissionSettleTypeEnum.EVERY_MONTH.getCode());//默认查询月结数据
            DownloadFileContent content = DownloadFileContent.builder()
                    .query(queryDto)
                    .operator(getUser().getEmail())
                    .sysType(DownloadTaskSysTypeEnum.ADMIN)
                    .businessType(DownloadTaskBusinessTypeEnum.COMMISSION_PAYABLE_ADMIN_DETAIL)
                    .build();
            boolean b = downloadRemote.saveTask(content);
            log.info("CommissionSettleExportController.exportDetail#queryDto:{} return {}", JSON.toJSONString(queryDto), b);
            return ResponseVo.successResult(b);
        } catch (ServiceRuntimeException e){
            return ResponseVo.errRest(e.getMessage());
        }catch (Exception e) {
            log.error("CommissionSettleExportController.exportDetail#queryDto:{} 异常", JSON.toJSONString(queryDto), e);
            return ResponseVo.errRest("导出失败，请重试");
        }
    }
}
