package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 账期状态
 * <AUTHOR>
 */
public enum SkuOperationLogEnum {

    TYPE_INSERT(1,"新增"),
    TYPE_UPDATE(2,"修改"),
    TYPE_AUDITING_SUCCESS(3,"审核通过"),
    TYPE_UP(4,"上架"),
    TYPE_DOWN(5,"下架"),
    TYPE_DEL(6,"删除"),
    TYPE_AUDITING_FAILED(7,"审核不通过");
    private  int id;
    private  String value;
    SkuOperationLogEnum(int id, String value){
        this.id = id;
        this.value = value;
    }
    private static Map<Integer, SkuOperationLogEnum> controlMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(SkuOperationLogEnum apEnum : SkuOperationLogEnum.values()) {
        	controlMaps.put(apEnum.getId(), apEnum);
            maps.put(apEnum.getId(),apEnum.getValue());
        }
    }
    public static String get(int id) {
        return controlMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
