package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *  退款单
 * <AUTHOR>
 */
@Data
public class RefundOrderDetailAdminVo implements Serializable {

    private static final long serialVersionUID = -7414908477759670925L;

    /**
     * 退款单id
     */
    private Long id;
    /**
     * 客户名称
     */
    private String merchantName;
    /** 会员ID */
    private Long merchantId;
    /* 客户ERP编码 */
    private String merchantErpCode;

    /**
     *  审核进程
     */
    private Integer auditProcessState;

    /**
     * 商户名称
     */
    private String companyName;
    /**
     * 店铺名称
     */
    private String businessName;

    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 退款状态
     */
    private Integer auditState;

    /**
     * 退款单编号
     */
    private String refundOrderNo;

    /**
     * 退款类型 1:部分退款 2:全部退款
     */
    private Integer refundType;

    /** 申请退款渠道 1:用户发起 2:客服介入 3:司机发起  4商家发起 5系统发起 */
    private Integer refundChannel;

    /** 退款原因 */
    private String refundReason;
    /** 退款说明 */
    private String refundExplain;

    /**
     * 审核日期
     */
    private Date customerAuditTime;

    /**
     * 退款日期
     */
    private Date refundAuditTime;
    /**
     * 申请日期
     */
    private Date refundCreateTime;

    /**
     * 退款金额
     */
    private BigDecimal refundFee;
    //退款商品 数量
    private Integer refundVarietyNum;

    /**
     * 实际退款金额 退款完成后展示
     */
    private BigDecimal refundActualFee;
    /**
     * 退款状态名称
     */
    private String auditStatusName;

    /**
     * 支付类型
     */
    public Integer payType;

    /**
     * 退款账户名
     */
    private String owner;

    /**
     * 账号
     */
    private String bankCard;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 机构id
     */
    private String orgId;
    /**
     * 区域省份
     */
    private String branchName;

    /**
     * 商户注册省份id
     */
    private Long provId;

    private String prov;

    private Integer offset;
    private Integer limit;

    /**
     * 下单时间
     */
    private Date createTime;

    /** 上传凭证1 */
    private String evidence1;

    /** 上传凭证2 */
    private String evidence2;

    /** 上传凭证3 */
    private String evidence3;

    /** 上传凭证4 */
    private String evidence4;

    /** 上传凭证5 */
    private String evidence5;
    //电汇图片列表
    private List<String> evidenceImages;
    //是否fbp（0：否；1：是）
    private Integer isFbp;
    /**
     * 紧急程度描述
     */
    private String urgencyinfo;
    /**
     * 现金实付
     */
    private BigDecimal cashPayAmount;

    /**
     * 购物金
     */
    private BigDecimal virtualGold;

    /**
     * 新退款单凭证
     */
    private List<String> imgList;

    /**
     * 退款打款凭证
     */
    private List<String> refundPayEvidence;

    private BigDecimal freightAmount;

    /**
     * 退款支付状态 1：已发起，2：退款成功，3：退款失败，4：处理中
     */
    private Integer payStatus;
    /** 是否第三方（0：否；1：是） */
    private Integer isThirdCompany;


    /**
     * 赔偿金额来源账户:1-营销服务额度;2-保证金
     */
    private Integer indemnitySource;
    /**
     * 赔偿金额
     */
    private BigDecimal indemnityMoney;
}
