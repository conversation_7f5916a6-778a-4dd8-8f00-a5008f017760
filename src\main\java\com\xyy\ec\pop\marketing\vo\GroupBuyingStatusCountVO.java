package com.xyy.ec.pop.marketing.vo;

import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupBuyingStatusCountVO implements Serializable {

    /**
     * 全部的数量
     */
    private Integer totalCount;

    /**
     * 待审核的数量
     */
    private Integer waitCount;

    /**
     * 审核不通过-可修改的数量
     */
    private Integer rejectedCount;

    /**
     * 未启动的数量
     */
    private Integer unStartCount;

    /**
     * 进行中的数量
     */
    private Integer startingCount;

    /**
     * 已结束/已下线的数量
     */
    private Integer stopOrOffLineCount;
}
