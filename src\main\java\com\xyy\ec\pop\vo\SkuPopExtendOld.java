package com.xyy.ec.pop.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class SkuPopExtendOld implements Serializable {
    private static final long serialVersionUID = -4830132218862723737L;
    private Long id;

    private String orgId;

    private String branchCode;

    private Long skuId;

    private BigDecimal marketPrice;

    private Integer isSpecialMedicine;

    private Integer isSpecialOffer;

    private Integer isFranchiseMedicine;

    private String specialMedicineUrl;

    private String specialOfferUrl;

    private String agentUrl;

    private String franchiseMedicineUrl;

    private String purchaseMerchantType;

    private String salesArea;

    private Integer isSalesOn;

    private String erpCode;

    private String subtitle;

    private Date onlineTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode == null ? null : branchCode.trim();
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public Integer getIsSpecialMedicine() {
        return isSpecialMedicine;
    }

    public void setIsSpecialMedicine(Integer isSpecialMedicine) {
        this.isSpecialMedicine = isSpecialMedicine;
    }

    public Integer getIsSpecialOffer() {
        return isSpecialOffer;
    }

    public void setIsSpecialOffer(Integer isSpecialOffer) {
        this.isSpecialOffer = isSpecialOffer;
    }

    public Integer getIsFranchiseMedicine() {
        return isFranchiseMedicine;
    }

    public void setIsFranchiseMedicine(Integer isFranchiseMedicine) {
        this.isFranchiseMedicine = isFranchiseMedicine;
    }

    public String getSpecialMedicineUrl() {
        return specialMedicineUrl;
    }

    public void setSpecialMedicineUrl(String specialMedicineUrl) {
        this.specialMedicineUrl = specialMedicineUrl == null ? null : specialMedicineUrl.trim();
    }

    public String getSpecialOfferUrl() {
        return specialOfferUrl;
    }

    public void setSpecialOfferUrl(String specialOfferUrl) {
        this.specialOfferUrl = specialOfferUrl == null ? null : specialOfferUrl.trim();
    }

    public String getAgentUrl() {
        return agentUrl;
    }

    public void setAgentUrl(String agentUrl) {
        this.agentUrl = agentUrl == null ? null : agentUrl.trim();
    }

    public String getFranchiseMedicineUrl() {
        return franchiseMedicineUrl;
    }

    public void setFranchiseMedicineUrl(String franchiseMedicineUrl) {
        this.franchiseMedicineUrl = franchiseMedicineUrl == null ? null : franchiseMedicineUrl.trim();
    }

    public String getPurchaseMerchantType() {
        return purchaseMerchantType;
    }

    public void setPurchaseMerchantType(String purchaseMerchantType) {
        this.purchaseMerchantType = purchaseMerchantType == null ? null : purchaseMerchantType.trim();
    }

    public String getSalesArea() {
        return salesArea;
    }

    public void setSalesArea(String salesArea) {
        this.salesArea = salesArea == null ? null : salesArea.trim();
    }

    public Integer getIsSalesOn() {
        return isSalesOn;
    }

    public void setIsSalesOn(Integer isSalesOn) {
        this.isSalesOn = isSalesOn;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle == null ? null : subtitle.trim();
    }

    public Date getOnlineTime() {
        return onlineTime;
    }

    public void setOnlineTime(Date onlineTime) {
        this.onlineTime = onlineTime;
    }
}