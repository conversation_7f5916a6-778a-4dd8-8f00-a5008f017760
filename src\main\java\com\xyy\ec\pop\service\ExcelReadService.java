package com.xyy.ec.pop.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.pop.dto.ImpExcelDataDTO;
import com.xyy.ec.pop.utils.easyexcel.EasyExcelDataAnalysisEventListener;
import com.xyy.ec.pop.utils.easyexcel.EasyExcelDataProcessor;
import com.xyy.ec.pop.utils.easyexcel.EasyExcelRowDataWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ExcelReadService {

    /**
     *
     * @param file
     * @param row
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> ImpExcelDataDTO<T> readExcel(MultipartFile file, Class<T> row) throws Exception {
        /* 解析Excel */
        // 表头
        Map<Integer, String> headMaps = Maps.newHashMapWithExpectedSize(16);
        long start = System.currentTimeMillis();
        // 行数据
        List<EasyExcelRowDataWrapper<T>> excelRows = Lists.newArrayList();
        // 读取Excel
        EasyExcel.read(file.getInputStream(), row,
                new EasyExcelDataAnalysisEventListener<T>(new EasyExcelDataProcessor<T>() {
                    @Override
                    public void process(List<EasyExcelRowDataWrapper<T>> rows, AnalysisContext context) {
                        if (CollectionUtils.isNotEmpty(rows)) {
                            excelRows.addAll(rows);
                        }
                    }

                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        Integer rowIndex = context.readRowHolder().getRowIndex();
                        if (Objects.equals(rowIndex, 0) && MapUtils.isNotEmpty(headMap)) {
                            headMaps.putAll(headMap);
                        }
                    }
                })).sheet().headRowNumber(1).doRead();

        List<T> importExcelRows = excelRows.stream()
                .map(EasyExcelRowDataWrapper :: getRowData).collect(Collectors.toList());

        long end = System.currentTimeMillis();
        if(log.isDebugEnabled()){
            log.debug("ExcelReadService.readExcel times:{}ms, fileSize:{}b ", (end - start), file.getSize());
        }
        ImpExcelDataDTO<T> retData = new ImpExcelDataDTO<>();
        retData.setHeadMaps(headMaps);
        retData.setDataList(importExcelRows);
        return retData;
    }
}
