package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.merchant.api.seller.CorporationApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: shiyujie
 * @Date: 2021/06/05
 * @Description:
 */
@Slf4j
@Component
public class CorporationRemote {
    @Reference
    private CorporationApi corporationApi;


    public CorporationDto queryCorpBaseByOrgId(String orgId) {
        try {
            log.info("CorporationRemote.queryCorpBaseByOrgId#orgId:{}", orgId);
            ApiRPCResult<CorporationDto> apiRPCResult = corporationApi.queryCorpBaseByOrgId(orgId);
            log.info("CorporationRemote.queryCorpBaseByOrgId#orgId:{},apiRPCResult:{}", orgId, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return null;
            }
            return apiRPCResult.getData();
        } catch (Exception e) {
            log.error("CorporationRemote.queryCorpBaseByOrgId#orgId:{}", orgId, e);
            return null;
        }
    }

    public List<CorporationDto> queryCorpBaseByOrgIds(List<String> orgIds) {
        try {
            log.info("CorporationRemote.queryCorpBaseByOrgIds#orgIds:{}", JSON.toJSONString(orgIds));
            ApiRPCResult<List<CorporationDto>> result = corporationApi.queryCorpBaseByOrgIds(orgIds);
            log.info("CorporationRemote.queryCorpBaseByOrgIds#orgIds:{} return {}", JSON.toJSONString(orgIds), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("CorporationRemote.queryCorpBaseByOrgIds#orgIds:{} 异常", JSON.toJSONString(orgIds), e);
        }
        return new ArrayList<>();
    }

    public List<CorporationDto> queryList(CorporationDto dto) {
        try {

            log.info("CorporationRemote.queryList#orgIds:{}", JSON.toJSONString(dto));
            ApiRPCResult<List<CorporationDto>> result = corporationApi.queryList(dto);
            log.info("CorporationRemote.queryList#orgIds:{} return {}", JSON.toJSONString(dto), JSON.toJSONString(result));
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("CorporationRemote.queryList#orgIds:{} 异常", JSON.toJSONString(dto), e);
        }
        return new ArrayList<>();
    }
}
