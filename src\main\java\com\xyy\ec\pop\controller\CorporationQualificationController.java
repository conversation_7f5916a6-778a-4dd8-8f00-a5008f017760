package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.remote.DownloadRemote;
import com.xyy.ec.pop.server.api.export.dto.DownloadFileContent;
import com.xyy.ec.pop.server.api.export.enums.DownloadTaskBusinessTypeEnum;
import com.xyy.ec.pop.server.api.merchant.api.seller.CorporationQualificationApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationQualificationAdminDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationQualificationQueryDto;
import com.xyy.ec.pop.vo.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 企业资质信息
 * @version V2.0.1
 * <AUTHOR>
 * @date 2018年10月27日 下午3:44:28
 * @Description:
 */
@Api(tags = "企业资质")
@Controller
@RequestMapping(value = "/corporationQualification")
public class CorporationQualificationController extends BaseController {
	private static final Logger LOG = LoggerFactory.getLogger(CorporationQualificationController.class);

	@Reference(version = "1.0.0")
	private CorporationQualificationApi corporationQualificationApi;

	@Autowired
	private DownloadRemote downloadRemote;


	@ResponseBody
	@PostMapping(value = "/queryPageList")
	public ResponseVo  queryPageList(@RequestBody CorporationQualificationQueryDto corporationQualificationQueryDto){
		if (Objects.isNull(corporationQualificationQueryDto)
				|| Objects.isNull(corporationQualificationQueryDto.getPageNum())
				|| Objects.isNull(corporationQualificationQueryDto.getPageSize())){
			return ResponseVo.errRest("必填参数不能为空");
		}
		ApiRPCResult<PageInfo<CorporationQualificationAdminDto>> pageInfoApiRPCResult = corporationQualificationApi.queryPageList(corporationQualificationQueryDto);
		if (pageInfoApiRPCResult.isFail()){
			return ResponseVo.errRest(pageInfoApiRPCResult.getErrMsg());
		}
		return ResponseVo.successResult(pageInfoApiRPCResult.getData());
	}

	@ResponseBody
	@PostMapping(value = "/export")
	public ResponseVo  export(@RequestBody CorporationQualificationQueryDto corporationQualificationQueryDto){
		if (Objects.isNull(corporationQualificationQueryDto)
				|| Objects.isNull(corporationQualificationQueryDto.getPageNum())
				|| Objects.isNull(corporationQualificationQueryDto.getPageSize())){
			return ResponseVo.errRest("必填参数不能为空");
		}
		DownloadFileContent content = DownloadFileContent.builder()
				.query(corporationQualificationQueryDto)
				.operator(getUser().getEmail())
				.businessType(DownloadTaskBusinessTypeEnum.ADMIN_CORPORATION_QUALIFICATION)
				.build();
		boolean b = downloadRemote.saveTask(content);
		return ResponseVo.successResult(b);
	}

	@GetMapping(value = "/batchUpdateExpireState")
	public void batchUpdateExpireState(){
		corporationQualificationApi.batchUpdateExpireState();
	}
}
