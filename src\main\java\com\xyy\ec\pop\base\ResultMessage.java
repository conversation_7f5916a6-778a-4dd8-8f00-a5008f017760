package com.xyy.ec.pop.base;

import com.xyy.ec.pop.enums.ResultCodeEnum;

public class ResultMessage<T> {
	private int code;	// 0成功
	private String msg;
	private T result;

	public ResultMessage() {
	}
	public ResultMessage(T result) {
		this(ResultCodeEnum.SUCCESS, result);
	}
	public ResultMessage(String message) {
		this(ResultCodeEnum.SUCCESS, message);
	}
	public ResultMessage(ResultCodeEnum resultCodeEnum, T result) {
		this.result = result;
		this.code = resultCodeEnum.getCode();
		this.msg = resultCodeEnum.getMsg();
	}
	public ResultMessage(ResultCodeEnum resultCodeEnum, String message) {
		this.code = resultCodeEnum.getCode();
		this.msg = message;
	}
	public ResultMessage(ResultCodeEnum resultCodeEnum, String message, T result) {
		this.result = result;
		this.code = resultCodeEnum.getCode();
		this.msg = message;
	}

	public int getCode() {
		return code;
	}
	public ResultMessage<T> setCode(int code) {
		this.code = code;
		return this;
	}
	public String getMsg() {
		return msg;
	}
	public ResultMessage<T> setMsg(String msg) {
		this.msg = msg;
		return this;
	}
	public T getResult() {
		return result;
	}
	public ResultMessage<T> setResult(T result) {
		this.result = result;
		return this;
	}

	public static <T> ResultMessage<T> create() {
		return new ResultMessage<T>().setCode(ResultCodeEnum.SUCCESS.getCode());
	}
	public static <T> ResultMessage<T> createSuccess(T result) {
		return new ResultMessage<T>(result);
	}
    public ResultMessage<T> setError(String msg){
    	return this.setCode(ResultCodeEnum.ERROR.getCode()).setMsg(msg);
    }

	public static <T> ResultMessage<T> createError(String message) {
		return new ResultMessage<T>(ResultCodeEnum.ERROR, message);
	}

	@Override
	public String toString() {
		return "ResultMessage{" + "code=" + code + ", msg='" + msg + '\'' + ", result=" + result + '}';
	}
}
