package com.xyy.ec.pop.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * tb_xyy_pop_receiving_report
 * <AUTHOR>
@Data
public class PopReceivingReportVo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 实际收款日期
     */
    private Date actuallyReceivedDate;

    /**
     * 类型(1,收款,2,退款)
     */
    private Byte receivablesType;

    /**
     * 省份编码
     */
    private String branchCode;
    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 商户名称
     */
    private String businessName;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付方式(1,支付宝,2,微信,3,银联)
     */
    private Byte payWay;

    /**
     * 商品金额
     */
    private BigDecimal commodityAmount;

    /**
     * 运费金额
     */
    private BigDecimal freightAmount;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 店铺优惠
     * 店铺优惠券优惠
     */
    private BigDecimal storesDiscount;

    /**
     * 平台优惠
     * 平台优惠券优惠
     */
    private BigDecimal platformDiscount;

    /**
     * 店铺活动优惠
     */
    private BigDecimal storesActivityDiscount;

    /**
     * 平台活动优惠
     */
    private BigDecimal platformActivityDiscount;


    /**
     * 实付金额
     */
    private BigDecimal actuallyPaidAmount;

    /**
     * 支付手续费
     */
    private BigDecimal commission;

    /**
     * 实收金额
     */
    private BigDecimal actuallyReceivedAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 备用字段1
     */
    private String metaData1;

    /**
     * 备用字段2
     */
    private String metaData2;

    /**
     * 备用字段3
     */
    private String metaData3;

    /**
     * 省份code
     */
    private Integer provinceCode;

    /**
     * 1:直连支付 2:富民支付
     */
    private Integer paymentChannel;

    /**
     * 省份
     */
    private String branchName;

    /**
     * 商户名称
     */
    private String name;

    /**
     * 会员ID
     */
    private Long merchantId;

    /**
     * 会员名称
     */
    private String merchantName;

    private static final long serialVersionUID = 1L;
}