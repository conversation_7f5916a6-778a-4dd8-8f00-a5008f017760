package com.xyy.ec.pop.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 商户资金账户打款审核查询Dto
 *
 * <AUTHOR>
 */
@Data
public class PopMerchantFundAuditQueryDto implements Serializable {

    private static final long serialVersionUID = -5025110875532799872L;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 查询关键词 商户编号或商户名称
     */
    private String keyword;
    /**
     * 店铺名称
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 审核状态
     * 1：待审核 2:审核通过 3:已取消 4:审核驳回
     */
    private Integer auditStatus;

    /**
     * 注册省份
     */
    private Long provId;
    private List<Long> provIds;

    /**
     * 提交开始时间
     */
    private Date createStartTime;

    /**
     * 提交结束时间
     */
    private Date createEndTime;

    /**
     * 实际收款开始时间
     */
    private Date actualReceivedStartTime;

    /**
     * 实际收款结束时间
     */
    private Date actualReceivedEndTime;

    /**
     * 资金性质状态（1:保证金，2：营销服务额度）
     */
    private Integer fundPropertyStatus;
    /**
     * 是否首充 （1：是，2：否）
     */
    private Integer isFirstCharge;

    private Integer pageSize;

    private Integer pageNum;

}
