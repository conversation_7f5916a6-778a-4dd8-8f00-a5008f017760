package com.xyy.ec.pop.marketing.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.marketing.insight.params.MarketCustomerGroupMerchantQueryParam;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupMerchantInfoDTO;
import com.xyy.ec.pop.exception.PopAdminException;
import com.xyy.ec.pop.marketing.helpers.MarketCustomerGroupMerchantQueryParamHelper;
import com.xyy.ec.pop.marketing.param.CustomerGroupMerchantQueryParam;
import com.xyy.ec.pop.marketing.param.CustomerGroupQueryParam;
import com.xyy.ec.pop.marketing.service.InsightService;
import com.xyy.ec.pop.marketing.vo.CustomerGroupVO;
import com.xyy.ec.pop.model.XyyJsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 选人
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(value = "/insight")
public class InsightController {

    @Autowired
    private InsightService insightService;

    /**
     * 分页查询人群信息
     *
     * @param customerGroupQueryParam
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public XyyJsonResult list(@RequestBody CustomerGroupQueryParam customerGroupQueryParam) {
        try {
            customerGroupQueryParam.setIsMultiBundle(false);
            PageInfo<CustomerGroupVO> pageInfo = insightService.pagingChosenGroups(customerGroupQueryParam);
            return XyyJsonResult.createSuccess()
                    .addResult("pageNo", pageInfo.getPageNum())
                    .addResult("pageSize", pageInfo.getPageSize())
                    .addResult("totalPage", pageInfo.getPages())
                    .addResult("totalCount", pageInfo.getTotal())
                    .addResult("list", pageInfo.getList());
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("【选人】分页查询人群信息异常，customerGroupQueryParam：{}，msg：{}",
                        JSONObject.toJSONString(customerGroupQueryParam), e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        } catch (Exception e) {
            log.error("【选人】分页查询人群信息异常，customerGroupQueryParam：{}", JSONObject.toJSONString(customerGroupQueryParam), e);
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        }
    }

    /**
     * 查询人群信息
     *
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public XyyJsonResult get(@RequestParam(name = "id", required = false) String id) {
        try {
            if (StringUtils.isEmpty(id)) {
                return XyyJsonResult.createFailure().msg("参数非法");
            }
            Long idLong;
            try {
                idLong = Long.parseLong(id);
            } catch (Exception e) {
                return XyyJsonResult.createFailure().msg("参数非法");
            }
            if (Objects.isNull(idLong) || idLong <= 0L) {
                return XyyJsonResult.createFailure().msg("参数非法");
            }
            CustomerGroupQueryParam customerGroupQueryParam = CustomerGroupQueryParam.builder()
                    .id(idLong).isMultiBundle(false).pageNum(1).pageSize(1).build();
            PageInfo<CustomerGroupVO> pageInfo = insightService.pagingChosenGroups(customerGroupQueryParam);
            CustomerGroupVO customerGroupVO = null;
            if (Objects.nonNull(pageInfo) && CollectionUtils.isNotEmpty(pageInfo.getList())) {
                customerGroupVO = pageInfo.getList().get(0);
            }
            return XyyJsonResult.createSuccess().addResult("info", customerGroupVO);
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("【选人】查询人群信息异常，id：{}，msg：{}", id, e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        } catch (Exception e) {
            log.error("【选人】查询人群信息异常，id：{}", id, e);
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        }
    }

    /**
     * 分页查询人群的药店信息
     *
     * @param param
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/merchant/list", method = RequestMethod.POST)
    public XyyJsonResult listMerchant(@RequestBody CustomerGroupMerchantQueryParam param) {
        try {
            MarketCustomerGroupMerchantQueryParam queryParam = MarketCustomerGroupMerchantQueryParamHelper.create(param);
            PageInfo<MarketCustomerGroupMerchantInfoDTO> pageInfo = insightService.pagingCustomerGroupMerchants(
                    queryParam, param.getPageNum(), param.getPageSize());
            return XyyJsonResult.createSuccess()
                    .addResult("pageNo", pageInfo.getPageNum())
                    .addResult("pageSize", pageInfo.getPageSize())
                    .addResult("totalPage", pageInfo.getPages())
                    .addResult("totalCount", pageInfo.getTotal())
                    .addResult("list", pageInfo.getList());
        } catch (PopAdminException e) {
            if (e.isWarn()) {
                log.error("【选人】分页查询人群的药店信息异常，param：{}，msg：{}", JSONObject.toJSONString(param), e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        } catch (Exception e) {
            log.error("【选人】分页查询人群的药店信息异常, param：{}", JSONObject.toJSONString(param), e);
            return XyyJsonResult.createFailure().msg("查询失败，请稍后重试！");
        }
    }

}
