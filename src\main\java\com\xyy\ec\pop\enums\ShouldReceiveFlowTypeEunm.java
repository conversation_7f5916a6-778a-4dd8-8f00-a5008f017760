package com.xyy.ec.pop.enums;

/**
 * 应收流水类型
 * @version V2.0.1
 * <AUTHOR>
 * @date 2018年10月7日 上午10:44:34
 * @Description:
 */
public enum ShouldReceiveFlowTypeEunm {
	/**
	 * 下单增加应收
	 */
	ADD_ORDER(1, "下单增加应收"),
	/**
	 * 取消订单减少应收
	 */
	RM_ORDER(2, "取消订单减少应收"),
	/**
	 * 客户退款减少应收
	 */
	REFUND(3, "客户退款减少应收");

	private int type;
	private String typeInfo;
	ShouldReceiveFlowTypeEunm(int type, String typeInfo) {
		this.type = type;
		this.typeInfo = typeInfo;
	}
	public int getType() {
		return type;
	}
	public String getTypeInfo() {
		return typeInfo;
	}
	/**
	 * 通过状态码，获取文本信息
	 * @param type {@link Integer}：状态码；
	 * @return {@link String}：文本信息。
	 * @version V2.0.1
	 * <AUTHOR>
	 * @date 2018年10月7日 上午11:31:57
	 */
	public static String getTypeInfo(int type) {
		ShouldReceiveFlowTypeEunm[] vs = ShouldReceiveFlowTypeEunm.values();
		for (int i = 0; i < vs.length; i++) {
			ShouldReceiveFlowTypeEunm e = vs[i];
			int t = e.getType();
			if (t == type)
				return e.getTypeInfo();
		}
		return null;
	}

}
