package com.xyy.ec.pop.controller.helper;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.server.api.erpUtil.api.ErpPopCorporationUtilApi;
import com.xyy.ec.pop.server.api.erpUtil.dto.PopCorporationDto;
import com.xyy.ec.pop.server.api.external.ec.EcPopCorporationApi;
import com.xyy.ec.pop.server.api.product.api.PopSkuApi;
import com.xyy.ec.pop.server.api.product.api.ec.PopSkuEcApi;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.ec.product.back.end.ecp.pop.api.PopStockApi;
import com.xyy.ec.product.back.end.ecp.pop.dto.StockSearchDTO;
import com.xyy.ec.product.back.end.ecp.stock.dto.CsuStockDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/08/02/16:29
 * @Description:
 */
@Component
@Controller
@Slf4j
@RequestMapping("/pop/sku")
public class PopSkuStockHelperController {
    @Reference
    private PopStockApi popStockApi;
    @Reference
    private PopSkuApi popSkuApi;
    @Reference
    private ErpPopCorporationUtilApi popCorporationUtilApi;
    @Reference
    private PopSkuEcApi popSkuEcApi;

    @RequestMapping("/stock")
    @ResponseBody
    public ResponseVo updatePopSkuStock(int start,int length) {
        log.info("PopSkuStockHelperController.updatePopSkuStock 刷商品库存数据");
        try {
            ApiRPCResult<List<PopCorporationDto>> corporationList = popCorporationUtilApi.getCorporationList("");
            if (corporationList.isFail() || CollectionUtils.isEmpty(corporationList.getData())) {
                return ResponseVo.errRest(corporationList.getErrMsg());
            }
            log.info("PopSkuStockHelperController.updatePopSkuStock getCorpList:{}", JSON.toJSONString(corporationList.getData()));
            List<PopCorporationDto> data = corporationList.getData().stream().filter(m-> !StringUtils.isBlank(m.getShopCode())).collect(Collectors.toList());
            Collections.sort(data, Comparator.comparing(PopCorporationDto::getId));
            data = data.subList(Math.min(start,data.size()),Math.min(data.size(),start+length));
            int count = 0;
            int total = data.size();
            for (PopCorporationDto p : data) {
                count += 1;
                log.info("PopSkuStockHelperController.updatePopSkuStock org:{},{}/{}", p.getOrgId(), count, total);
                ApiRPCResult<List<Long>> listApiRPCResult = popSkuApi.findCsuIdsByOrgId(p.getOrgId());
                if (listApiRPCResult.isFail()) {
                    return ResponseVo.errRest(listApiRPCResult.getErrMsg());
                }
                if (CollectionUtils.isEmpty(listApiRPCResult.getData())){
                    continue;
                }
                log.info("PopSkuStockHelperController.updatePopSkuStock barcodes:{}", JSON.toJSONString(listApiRPCResult.getData()));
                List<List<Long>> lists = Lists.partition(listApiRPCResult.getData(), 50);
                int barcodeCount = 1;
                int barcodeTotal = lists.size();
                for (List<Long> l : lists) {
                    log.info("PopSkuStockHelperController.updatePopSkuStock barcode-group:{}-{}/{}",count, barcodeCount, barcodeTotal);
                    ApiRPCResult<List<CsuStockDTO>> popStockApiResult = popStockApi.getStockBySkuIdList(l);
                    if (popStockApiResult != null && popStockApiResult.isSuccess()) {
                        List<CsuStockDTO> stockSearchDTOS = popStockApiResult.getData();
                        List<PopSkuDto> popSkuDtos = Lists.newArrayList();
                        for (CsuStockDTO stockSearchDTO : stockSearchDTOS) {
                            PopSkuDto dto = new PopSkuDto();
                            dto.setCsuid(stockSearchDTO.getCsuId());
                            dto.setStock(stockSearchDTO.getAvailableQty());
                            popSkuDtos.add(dto);
                        }
                        log.info("PopSkuStockHelperController.updatePopSkuStock popSkuDtos:{}", JSON.toJSONString(popSkuDtos));
                        popSkuApi.batchUpdateProductStock(popSkuDtos);
                    }
                    barcodeCount += 1;
                }
            }
            return ResponseVo.successResultNotData();
        } catch (Exception e) {
            log.error("PopSkuStockHelperController.updatePopSkuStock error", e);
            return ResponseVo.errRest(e.getMessage());
        }
    }

    /**
     * 同步csuId
     *
     * @return
     */
    @GetMapping(value = "/syncCsuId")
    public Object syncCsuId() {
        try {
            popSkuApi.syncCsuId();
            return ResponseVo.successResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("ProductV2Controller.syncCsuId#csuId初始化异常", e);
            return ResponseVo.errRest("同步csuId异常");
        }
    }

}
