<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.ec.pop.dao.PopBillDetailMapper">
  <resultMap id="BaseResultMap" type="com.xyy.ec.pop.po.PopBillDetailPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="area_org_id" jdbcType="VARCHAR" property="areaOrgId" />
    <result column="branch_code" jdbcType="VARCHAR" property="branchCode" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="business_no" jdbcType="VARCHAR" property="businessNo" />
    <result column="merchant_name" jdbcType="VARCHAR" property="merchantName" />
    <result column="product_money" jdbcType="DECIMAL" property="productMoney" />
    <result column="total_money" jdbcType="DECIMAL" property="totalMoney" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="freight_amount" jdbcType="DECIMAL" property="freightAmount" />
    <result column="coupon_shop_amount" jdbcType="DECIMAL" property="couponShopAmount" />
    <result column="marketing_shop_amount" jdbcType="DECIMAL" property="marketingShopAmount" />
    <result column="shop_total_discount" jdbcType="DECIMAL" property="shopTotalDiscount" />
    <result column="coupon_platform_amount" jdbcType="DECIMAL" property="couponPlatformAmount" />
    <result column="marketing_platform_amount" jdbcType="DECIMAL" property="marketingPlatformAmount" />
    <result column="platform_total_discount" jdbcType="DECIMAL" property="platformTotalDiscount" />
    <result column="hire_money" jdbcType="DECIMAL" property="hireMoney" />
    <result column="payable_commission" jdbcType="DECIMAL" property="payableCommission" />
    <result column="deducted_commission" jdbcType="DECIMAL" property="deductedCommission" />
    <result column="deducted" jdbcType="TINYINT" property="deducted" />
    <result column="settlement_type" jdbcType="TINYINT" property="settlementType" />
    <result column="penalty_amount" jdbcType="DECIMAL" property="penaltyAmount" />
    <result column="statement_total_money" jdbcType="DECIMAL" property="statementTotalMoney" />
    <result column="order_pay_time" jdbcType="TIMESTAMP" property="orderPayTime" />
    <result column="order_finish_time" jdbcType="TIMESTAMP" property="orderFinishTime" />
    <result column="pay_type" jdbcType="TINYINT" property="payType" />
    <result column="order_settlement_status" jdbcType="TINYINT" property="orderSettlementStatus" />
    <result column="order_settlement_time" jdbcType="TIMESTAMP" property="orderSettlementTime" />
    <result column="bill_create_time" jdbcType="TIMESTAMP" property="billCreateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="actual_commission_money" jdbcType="DECIMAL" property="actualCommissionMoney"/>
    <result column="commission_discount_money" jdbcType="DECIMAL" property="commissionDiscountMoney"/>
    <result column="merchant_erp_code" jdbcType="VARCHAR" property="merchantErpCode"/>
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId"/>
    <result column="cash_pay_amount" jdbcType="DECIMAL" property="cashPayAmount" />
    <result column="virtual_gold" jdbcType="DECIMAL" property="virtualGold" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_id, org_name, area_org_id, branch_code, bill_no, business_type, business_no, merchant_name,
    product_money, total_money, money, freight_amount, coupon_shop_amount, marketing_shop_amount, 
    shop_total_discount, coupon_platform_amount, marketing_platform_amount, platform_total_discount, 
    hire_money,payable_commission,deducted_commission,deducted,settlement_type, penalty_amount, statement_total_money, order_pay_time, order_finish_time,
    pay_type, order_settlement_status, order_settlement_time, bill_create_time, create_time, 
    update_time, actual_commission_money, commission_discount_money,merchant_erp_code,merchant_id, cash_pay_amount, virtual_gold
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_xyy_pop_bill_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tb_xyy_pop_bill_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xyy.ec.pop.po.PopBillDetailPo" useGeneratedKeys="true">
    insert into tb_xyy_pop_bill_detail (org_id, org_name, area_org_id, 
      branch_code, bill_no, business_type, 
      business_no, merchant_name, product_money, total_money,
      money, freight_amount, coupon_shop_amount, 
      marketing_shop_amount, shop_total_discount, 
      coupon_platform_amount, marketing_platform_amount, 
      platform_total_discount, hire_money,payable_commission,settlement_type, penalty_amount,
      statement_total_money, order_pay_time, order_finish_time, 
      pay_type, order_settlement_status, order_settlement_time, 
      bill_create_time, create_time, update_time,merchant_erp_code,merchant_id
      )
    values (#{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, #{areaOrgId,jdbcType=VARCHAR}, 
      #{branchCode,jdbcType=VARCHAR}, #{billNo,jdbcType=VARCHAR}, #{businessType,jdbcType=TINYINT}, 
      #{businessNo,jdbcType=VARCHAR},#{merchantName,jdbcType=VARCHAR}, #{productMoney,jdbcType=DECIMAL}, #{totalMoney,jdbcType=DECIMAL},
      #{money,jdbcType=DECIMAL}, #{freightAmount,jdbcType=DECIMAL}, #{couponShopAmount,jdbcType=DECIMAL}, 
      #{marketingShopAmount,jdbcType=DECIMAL}, #{shopTotalDiscount,jdbcType=DECIMAL}, 
      #{couponPlatformAmount,jdbcType=DECIMAL}, #{marketingPlatformAmount,jdbcType=DECIMAL}, 
      #{platformTotalDiscount,jdbcType=DECIMAL}, #{hireMoney,jdbcType=DECIMAL},#{payableCommission,jdbcType=DECIMAL},#{settlementType,jdbcType=TINYINT}, #{penaltyAmount,jdbcType=DECIMAL},
      #{statementTotalMoney,jdbcType=DECIMAL}, #{orderPayTime,jdbcType=TIMESTAMP}, #{orderFinishTime,jdbcType=TIMESTAMP}, 
      #{payType,jdbcType=TINYINT}, #{orderSettlementStatus,jdbcType=TINYINT}, #{orderSettlementTime,jdbcType=TIMESTAMP}, 
      #{billCreateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{merchantErpCpde,jdbcType=VARCHAR},#{merchantId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xyy.ec.pop.po.PopBillDetailPo" useGeneratedKeys="true">
    insert into tb_xyy_pop_bill_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="areaOrgId != null">
        area_org_id,
      </if>
      <if test="branchCode != null">
        branch_code,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="businessNo != null">
        business_no,
      </if>
      <if test="merchantName != null">
        merchant_name,
      </if>
      <if test="productMoney != null">
        product_money,
      </if>
      <if test="totalMoney != null">
        total_money,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="freightAmount != null">
        freight_amount,
      </if>
      <if test="couponShopAmount != null">
        coupon_shop_amount,
      </if>
      <if test="marketingShopAmount != null">
        marketing_shop_amount,
      </if>
      <if test="shopTotalDiscount != null">
        shop_total_discount,
      </if>
      <if test="couponPlatformAmount != null">
        coupon_platform_amount,
      </if>
      <if test="marketingPlatformAmount != null">
        marketing_platform_amount,
      </if>
      <if test="platformTotalDiscount != null">
        platform_total_discount,
      </if>
      <if test="hireMoney != null">
        hire_money,
      </if>
      <if test="payableCommission != null">
        payable_commission,
      </if>
      <if test="settlementType != null">
        settlement_type,
      </if>
      <if test="penaltyAmount != null">
        penalty_amount,
      </if>
      <if test="statementTotalMoney != null">
        statement_total_money,
      </if>
      <if test="orderPayTime != null">
        order_pay_time,
      </if>
      <if test="orderFinishTime != null">
        order_finish_time,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="orderSettlementStatus != null">
        order_settlement_status,
      </if>
      <if test="orderSettlementTime != null">
        order_settlement_time,
      </if>
      <if test="billCreateTime != null">
        bill_create_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="merchantErpCpde != null">
        merchant_erp_code,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="areaOrgId != null">
        #{areaOrgId,jdbcType=VARCHAR},
      </if>
      <if test="branchCode != null">
        #{branchCode,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="businessNo != null">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantName != null">
        #{merchantName,jdbcType=VARCHAR},
      </if>
      <if test="productMoney != null">
        #{productMoney,jdbcType=DECIMAL},
      </if>
      <if test="totalMoney != null">
        #{totalMoney,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="freightAmount != null">
        #{freightAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponShopAmount != null">
        #{couponShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingShopAmount != null">
        #{marketingShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="shopTotalDiscount != null">
        #{shopTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="couponPlatformAmount != null">
        #{couponPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingPlatformAmount != null">
        #{marketingPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="platformTotalDiscount != null">
        #{platformTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="hireMoney != null">
        #{hireMoney,jdbcType=DECIMAL},
      </if>
      <if test="payableCommission != null">
        #{payableCommission,jdbcType=DECIMAL},
      </if>
      <if test="settlementType  != null">
        #{settlementType ,jdbcType=TINYINT},
      </if>
      <if test="penaltyAmount != null">
        #{penaltyAmount,jdbcType=DECIMAL},
      </if>
      <if test="statementTotalMoney != null">
        #{statementTotalMoney,jdbcType=DECIMAL},
      </if>
      <if test="orderPayTime != null">
        #{orderPayTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderFinishTime != null">
        #{orderFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=TINYINT},
      </if>
      <if test="orderSettlementStatus != null">
        #{orderSettlementStatus,jdbcType=TINYINT},
      </if>
      <if test="orderSettlementTime != null">
        #{orderSettlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billCreateTime != null">
        #{billCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="merchantErpCpde != null">
        #{merchantErpCpde,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xyy.ec.pop.po.PopBillDetailPo">
    update tb_xyy_pop_bill_detail
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="areaOrgId != null">
        area_org_id = #{areaOrgId,jdbcType=VARCHAR},
      </if>
      <if test="branchCode != null">
        branch_code = #{branchCode,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="businessNo != null">
        business_no = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantName != null">
        merchant_name = #{merchantName,jdbcType=VARCHAR},
      </if>
      <if test="productMoney != null">
        product_money = #{productMoney,jdbcType=DECIMAL},
      </if>
      <if test="totalMoney != null">
        total_money = #{totalMoney,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="freightAmount != null">
        freight_amount = #{freightAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponShopAmount != null">
        coupon_shop_amount = #{couponShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingShopAmount != null">
        marketing_shop_amount = #{marketingShopAmount,jdbcType=DECIMAL},
      </if>
      <if test="shopTotalDiscount != null">
        shop_total_discount = #{shopTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="couponPlatformAmount != null">
        coupon_platform_amount = #{couponPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="marketingPlatformAmount != null">
        marketing_platform_amount = #{marketingPlatformAmount,jdbcType=DECIMAL},
      </if>
      <if test="platformTotalDiscount != null">
        platform_total_discount = #{platformTotalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="hireMoney != null">
        hire_money = #{hireMoney,jdbcType=DECIMAL},
      </if>
      <if test="payableCommission != null">
        payable_commission =  #{payableCommission,jdbcType=DECIMAL},
      </if>
      <if test="settlementType  != null">
        settlement_type = #{settlementType ,jdbcType=TINYINT},
      </if>
      <if test="penaltyAmount != null">
        penalty_amount = #{penaltyAmount,jdbcType=DECIMAL},
      </if>
      <if test="statementTotalMoney != null">
        statement_total_money = #{statementTotalMoney,jdbcType=DECIMAL},
      </if>
      <if test="orderPayTime != null">
        order_pay_time = #{orderPayTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderFinishTime != null">
        order_finish_time = #{orderFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=TINYINT},
      </if>
      <if test="orderSettlementStatus != null">
        order_settlement_status = #{orderSettlementStatus,jdbcType=TINYINT},
      </if>
      <if test="orderSettlementTime != null">
        order_settlement_time = #{orderSettlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billCreateTime != null">
        bill_create_time = #{billCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="merchantErpCpde != null">
        merchant_erp_code = #{merchantErpCpde,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xyy.ec.pop.po.PopBillDetailPo">
    update tb_xyy_pop_bill_detail
    set org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      area_org_id = #{areaOrgId,jdbcType=VARCHAR},
      branch_code = #{branchCode,jdbcType=VARCHAR},
      bill_no = #{billNo,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=TINYINT},
      business_no = #{businessNo,jdbcType=VARCHAR},
      merchant_name = #{merchantName,jdbcType=VARCHAR},
      product_money = #{productMoney,jdbcType=DECIMAL},
      total_money = #{totalMoney,jdbcType=DECIMAL},
      money = #{money,jdbcType=DECIMAL},
      freight_amount = #{freightAmount,jdbcType=DECIMAL},
      coupon_shop_amount = #{couponShopAmount,jdbcType=DECIMAL},
      marketing_shop_amount = #{marketingShopAmount,jdbcType=DECIMAL},
      shop_total_discount = #{shopTotalDiscount,jdbcType=DECIMAL},
      coupon_platform_amount = #{couponPlatformAmount,jdbcType=DECIMAL},
      marketing_platform_amount = #{marketingPlatformAmount,jdbcType=DECIMAL},
      platform_total_discount = #{platformTotalDiscount,jdbcType=DECIMAL},
      hire_money = #{hireMoney,jdbcType=DECIMAL},
      payable_commission =  #{payableCommission,jdbcType=DECIMAL},
      settlement_type = #{settlementType ,jdbcType=TINYINT},
      penalty_amount = #{penaltyAmount,jdbcType=DECIMAL},
      statement_total_money = #{statementTotalMoney,jdbcType=DECIMAL},
      order_pay_time = #{orderPayTime,jdbcType=TIMESTAMP},
      order_finish_time = #{orderFinishTime,jdbcType=TIMESTAMP},
      pay_type = #{payType,jdbcType=TINYINT},
      order_settlement_status = #{orderSettlementStatus,jdbcType=TINYINT},
      order_settlement_time = #{orderSettlementTime,jdbcType=TIMESTAMP},
      bill_create_time = #{billCreateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      merchant_erp_code = #{merchantErpCpde,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="queryPopBillDetail" resultMap="BaseResultMap" parameterType="com.xyy.ec.pop.po.PopBillPaymentDetailPo">
    SELECT
    <include refid="Base_Column_List"/>
    FROM tb_xyy_pop_bill_detail
    <where>
      <if test="null != popBillPayDetail.payType">
        AND pay_type = #{popBillPayDetail.payType,jdbcType=TINYINT}
      </if>
      <if test="null != popBillPayDetail.billNo and '' != popBillPayDetail.billNo">
        AND bill_no = #{popBillPayDetail.billNo,jdbcType=VARCHAR}
      </if>
      <if test="popBillPayDetail.businessNo != null and popBillPayDetail.businessNo != '' ">
        AND business_no = #{popBillPayDetail.businessNo}
      </if>
    </where>
    order by create_time desc,id desc
    <!--<if test="pageNum != null and pageSize != null">
      limit #{pageNum, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
    </if>-->
  </select>
  <select id="queryPopBillDetailCount" resultType="java.lang.Long"  parameterType="java.lang.String">
    select count(id) from tb_xyy_pop_bill_detail
    where bill_no in
    <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
  <select id="queryPopBillDetailByBillNoList" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_xyy_pop_bill_detail
    where bill_no in
    <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
    <select id="selectPopBillNoByBusinessNo" resultType="java.lang.String">
      SELECT bill_no FROM tb_xyy_pop_bill_detail where business_no = #{businessNo}
    </select>
</mapper>