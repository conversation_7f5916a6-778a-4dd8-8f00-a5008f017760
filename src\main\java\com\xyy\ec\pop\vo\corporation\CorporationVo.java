package com.xyy.ec.pop.vo.corporation;

import com.xyy.ec.pop.server.api.merchant.dto.ErpApprovalModelDto;
import com.xyy.ec.pop.vo.CorporationAreaInfoVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 机构信息
 */
@Data
public class CorporationVo implements Serializable {
    private Long id;
    private String orgId;
    private String shopCode;
    private String name;
    private String companyName;
    private String regCode;
    private String corporat;
    private String phone;
    private String fixedPhone;
    private String email;
    private String web;
    private Long provId;
    private String prov;
    private Long cityId;
    private String city;
    private Long areaId;
    private String area;
    private String addr;
    private String logoUrl;
    private String brief;
    private String remarks;
    private Byte state;
    private Byte del;
    private Date createTime;
    private Long createId;
    private String createName;
    private Date updateTime;
    private Long updateId;
    private String updateName;
    private String search;
    private Integer corporationType;
    private String customerServicePhone;
    private String erpProcessId;
    /**
     * 药品售卖区域
     */
    private List<CorporationAreaInfoVo> drugsArea;
    /**
     * 非药售卖区域
     */
    private List<CorporationAreaInfoVo> nonDrugArea;
    /**
     * 不通过提示
     */
    private String rejectTips;

    /**
     * 街道ID
     */
    private Integer streetId;
    /**
     * 街道名称
     */
    private String streetName;
    /**
     * 单据编号
     */
    private String erpNumber;
    /**
     * 基础信息审核状态
     */
    private Integer status;
    /**
     * 审核信息
     */
    private ErpApprovalModelVo erpApprovalModelDto;
    //主营业务
    private List<CorporationBusinessVo> corporationBusiness;
    //企业资质
    private List<CorporationQualificationVo> corporationQualifications;
    /**
     * 佣金结算方式，1：非月结；2：月结
     */
    private Byte settlementType;
    /**
     * 供货对象
     */
    private String supplyCustomerType;


    private String shipperCode;

    /**
     * 是否勾选医疗器械二类和三类细项 true-不勾选 false-勾选
     */
    private Boolean selectItemsFlag;
}
