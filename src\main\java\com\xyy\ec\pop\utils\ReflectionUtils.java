package com.xyy.ec.pop.utils;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.lang.reflect.*;
import java.math.BigDecimal;
import java.util.*;

/**
 * 反射的Utils函数集合
 * 提供访问私有变量,获取泛型类型Class,提取集合中元素的属性等Utils函数.
 * @ClassName: ReflectionUtils 
 * <AUTHOR>
 * @date 2016-2-23 下午11:08:03
 */
public class ReflectionUtils {
	private static Logger LOGGER = LoggerFactory.getLogger(ReflectionUtils.class);
	
	/*        基础数据类型定义,类形式              */	

	public  static Class BYTE_CLASS     = byte.class;
 	
	public  static Class INT_CLASS      = int.class;
	
	public  static Class LONG_CLASS     = long.class;
	 
	public  static Class FLOAT_CLASS    = float.class;
	
	public  static Class DOUBLE_CLASS   = double.class;
	
	public  static Class CHAR_CLASS     = char.class;
	
	public  static Class STRING_CLASS   = String.class;
	
	public  static Class DATE_CLASS     = Date.class;
	
	public  static Class BIGDECIMAL_CLASS  = BigDecimal.class;

	
	/**
	 * 直接读取对象属性值,无视private/protected修饰符,不经过getter函数.
	 */
	public static Object getFieldValue(final Object object, final String fieldName) {
		if("HashMap".equals(object.getClass().getSimpleName()))
			return ((HashMap)object).get(fieldName);
		Field field = getDeclaredField(object, fieldName);

		if (field == null)
			throw new IllegalArgumentException("Could not find field [" + fieldName + "] on target [" + object + "]");

		makeAccessible(field);

		Object result = null;
		try {
			result = field.get(object);
		} catch (IllegalAccessException e) {
			LOGGER.error("不可能抛出的异常{}", e.getMessage());
		}
		return result==null?"":result;
	}
	



	/**
	 * 循环向上转型,获取对象的DeclaredField.
	 */
	protected static Field getDeclaredField(final Object object, final String fieldName) {
		Assert.notNull(object, "object不能为空");
		return getDeclaredField(object.getClass(), fieldName);
	}

	/**
	 * 循环向上转型,获取类的DeclaredField.
	 */
	protected static Field getDeclaredField(final Class clazz, final String fieldName) {
		Assert.notNull(clazz, "clazz不能为空");
		Assert.hasText(fieldName, "fieldName");
		for (Class superClass = clazz; superClass != Object.class; superClass = superClass.getSuperclass()) {
			try {
				return superClass.getDeclaredField(fieldName);
			} catch (NoSuchFieldException e) {
				// Field不在当前类定义,继续向上转型
			}
		}
		return null;
	}

	/**
	 * 强制转换fileld可访问.
	 */
	protected static void makeAccessible(final Field field) {
		if (!Modifier.isPublic(field.getModifiers()) || !Modifier.isPublic(field.getDeclaringClass().getModifiers())) {
			field.setAccessible(true);
		}
	}




	


	



	/**
	* Callback optionally used to filter methods to be operated on by a method callback.
	*/
	public interface MethodFilter {

		/**
		 * Determine whether the given method matches.
		 * @param method the method to check
		 */
		boolean matches(Method method);
	}

	public interface MethodCallback {
		/**
		 * Perform an operation using the given method.
		 * @param method the method to operate on
		 */
		void doWith(Method method) throws IllegalArgumentException, IllegalAccessException;
	}
}
