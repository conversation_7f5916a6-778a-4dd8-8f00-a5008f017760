package com.xyy.ec.pop.service;

import cn.afterturn.easypoi.excel.entity.ExportParams;

import java.util.Collection;

/**
 * @version v1
 * @Description 文件服务工具
 * <AUTHOR>
 */
public interface FastDfsUtilService {
    /**
     * 将数据已excel格式写入fastdfs
     * @param entity
     * @param pojoClass
     * @param dataSet
     * @return
     */
    String writeDateToFastDfs(ExportParams entity, Class<?> pojoClass,
                              Collection<?> dataSet);
}
