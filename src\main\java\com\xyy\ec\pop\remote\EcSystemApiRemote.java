package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.xyy.ec.pop.config.XyyEcUriConfig;
import com.xyy.ec.pop.config.XyyPopBaseConfig;
import com.xyy.ec.pop.constants.Constant;
import com.xyy.ec.pop.constants.Constants;
import com.xyy.ec.pop.vo.BranchVo;
import com.xyy.ec.system.business.api.SysActionBusinessApi;
import com.xyy.ec.system.business.api.SysResourceBusinessApi;
import com.xyy.ec.system.business.api.SysRoleBusinessApi;
import com.xyy.ec.system.business.api.SysUserBusinessApi;
import com.xyy.ec.system.business.dto.SysActionDtoBusiness;
import com.xyy.ec.system.business.dto.SysUserBusinessDto;
import com.xyy.pop.framework.core.utils.RestTemplateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ec 系统服务接口代理
 * Created by danshiyu on 2019/10/31.
 */
@Slf4j
@Component("adminEcSystemApiRemote")
public class EcSystemApiRemote {
    @Autowired
    private XyyPopBaseConfig baseConfig;
    @Autowired
    private XyyEcUriConfig ecUriConfig;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private RestTemplate longTransactionRestTemplate;

    @Reference(version = "1.0.0")
    private SysUserBusinessApi sysUserService;

    @Reference(version = "1.0.0")
    private SysRoleBusinessApi sysRoleService;

    @Reference(version = "1.0.0")
    private SysActionBusinessApi sysActionService;

    @Reference(version = "1.0.0")
    private SysResourceBusinessApi sysResourceService;

    /**
     * 202004
     * EC地址CODE优化
     * @param parentCode
     * @return
     */
    public String getNewDicAreaList(String parentCode){
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("parentCode",parentCode);
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(hashMap,header);
        return RestTemplateUtils.postRequest(ecUriConfig.getDicNewAreasUrl(), longTransactionRestTemplate, httpEntity, new ParameterizedTypeReference<String>() {});
    }

    /**
     * 获取所有的域
     * @return
     */
    public List<BranchVo> getAllBranchs(){
        String url = RestTemplateUtils.buildRequestUrl(baseConfig.getEcHost(),ecUriConfig.getAllBranchsUrl());
        JSONObject response = RestTemplateUtils.postRequest(url, restTemplate, getEmptyHttpEntity(), new ParameterizedTypeReference<JSONObject>() {});
        if(response.get(Constant.RESULT_STATUS).equals(Constant.RESULT_SUCCESS)) {
            return response.getObject("branchList", new TypeReference<List<BranchVo>>() {
            });
        }
        return Lists.newArrayList();
    }

    public List<String> sysLoginAction(Long userId,String requestUrl){
        List<String> listAction = new ArrayList<>();
        try {
            List<SysActionDtoBusiness> tempSysActionList = null;
            SysUserBusinessDto loginUser = sysUserService.selectByPrimaryKey(userId);
            if(loginUser.getIsSuper()== Constants.IS0) {
                Map<String, Object> map = new HashMap<>();
                map.put("groupName", requestUrl);
                Collection<Long> roleIdList = sysRoleService.getRoleIdArrayByUser(loginUser.getId());
                map.put("roleIds", roleIdList);
                tempSysActionList = sysResourceService.listActionByParam(map);
            }else {
                SysActionDtoBusiness paramSysAction = new SysActionDtoBusiness();
                paramSysAction.setGroupName(requestUrl);
                tempSysActionList = sysActionService.selectList(paramSysAction);
            }
            listAction = tempSysActionList.stream().map(a -> "["+a.getActionName()+"]").collect(Collectors.toList());
        } catch (Exception e) {
            log.error("左侧加载异常",e);
        }
        return listAction;
    }

    private HttpEntity getEmptyHttpEntity(){
        return RestTemplateUtils.buildRequestFormEntity(new LinkedMultiValueMap());
    }
}
