package com.xyy.ec.pop.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 实体父类
 * @ClassName: BaseEntity 
 * <AUTHOR>
 * @date 2016-4-10 下午2:41:04
 */
public class BaseEntity implements Serializable{
	
	private static final long serialVersionUID = -7109226531635243437L;
	
	public static final String ASC = "ASC";
    public static final String DESC = "DESC";
    @JsonIgnore
	protected String property;				//排序属性
    @JsonIgnore
	protected String direction = DESC; 		//排序描述
    
	public String getProperty() {
		return property;
	}

	public void setProperty(String property) {
		this.property = property;
	}

	public String getDirection() {
		return direction;
	}

	public void setDirection(String direction) {
		this.direction = direction;
	}

	public String toString(){
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
}
