package com.xyy.ec.pop.adapter.mop;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pop.vo.ResponseVo;
import com.xyy.pop.mop.api.common.condition.CallingLogSelectCondition;
import com.xyy.pop.mop.api.remote.CallingLogRemote;
import com.xyy.pop.mop.api.remote.parameter.CallingLogPageParameter;
import com.xyy.pop.mop.api.remote.result.CallingLogBasicDTO;
import com.xyy.pop.mop.api.remote.result.CallingLogPageDTO;
import com.xyy.scm.constant.entity.Result;
import com.xyy.scm.constant.entity.pagination.Paging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class CallingAdapter implements MopBaseAdapter{
    @Reference(version = "1.0.0")
    CallingLogRemote callingLogRemoteApi;
    public ResponseVo<Paging<CallingLogBasicDTO>> queryPageLogByParam(CallingLogSelectCondition callingLogRemote) {
        Result<Paging<CallingLogBasicDTO>> pagingResult = callingLogRemoteApi.queryPageLogByParam(callingLogRemote);
        return to(()->pagingResult);
    }

    public ResponseVo<Map<String, Map<String, String>>> queryEnum() {
        Result<Map<String, Map<String, String>>> mapResult = callingLogRemoteApi.callingEnumQuery();
        return to(()->mapResult);
    }

    public ResponseVo<Paging<CallingLogPageDTO>> queryCallingLogPageByParam(CallingLogPageParameter param) {
        return to(() -> callingLogRemoteApi.queryCallingLogPageByParam(param));
    }
}
