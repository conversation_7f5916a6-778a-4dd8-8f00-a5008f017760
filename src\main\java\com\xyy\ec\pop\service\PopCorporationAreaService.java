package com.xyy.ec.pop.service;

import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.vo.CorporationAreaInfoVo;

import java.util.List;
import java.util.Map;

/**
 * @version v1
 * @Description 区域
 * <AUTHOR>
 */
public interface PopCorporationAreaService {
    List<CorporationAreaInfoVo> getCorporationArea(Long cid) throws ServiceException;

    List<CorporationAreaInfoVo> getCorporationAreaByCids(List<Long> cids) throws ServiceException;

    /**
     * @param orgId
     * @param drugAreas 药品区域
     * @param nonDrugAreas 非药区域
     * @param map
     * @param username
     * @param userId
     */
    void updateCorporationArea(String orgId, List<CorporationAreaInfoVo> drugAreas, List<CorporationAreaInfoVo> nonDrugAreas, Map<Integer, String> map, String username, Long userId) throws ServiceException;
}
