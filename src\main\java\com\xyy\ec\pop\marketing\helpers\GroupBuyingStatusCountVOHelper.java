package com.xyy.ec.pop.marketing.helpers;

import com.xyy.ec.marketing.elephant.results.MarketingGroupBuyingStatusCountDTO;
import com.xyy.ec.pop.marketing.vo.GroupBuyingStatusCountVO;

import java.util.Objects;

public class GroupBuyingStatusCountVOHelper {

    public static GroupBuyingStatusCountVO create(MarketingGroupBuyingStatusCountDTO marketingGroupBuyingStatusCountDTO) {
        if (Objects.isNull(marketingGroupBuyingStatusCountDTO)) {
            return null;
        }
        return GroupBuyingStatusCountVO.builder().totalCount(marketingGroupBuyingStatusCountDTO.getTotalCount())
                .waitCount(marketingGroupBuyingStatusCountDTO.getWaitCount())
                .rejectedCount(marketingGroupBuyingStatusCountDTO.getRejectedCount())
                .unStartCount(marketingGroupBuyingStatusCountDTO.getUnStartCount())
                .startingCount(marketingGroupBuyingStatusCountDTO.getStartingCount())
                .stopOrOffLineCount(marketingGroupBuyingStatusCountDTO.getStopOrOffLineCount())
                .build();
    }
}
