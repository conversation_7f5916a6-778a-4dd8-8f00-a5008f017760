package com.xyy.ec.pop.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * tb_xyy_pop_bill_payment
 * <AUTHOR>
@Data
public class PopBillPaymentPo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 商家名称
     */
    private String orgName;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 入帐单
     */
    private String flowNo;

    /**
     * 商品总金额
     */
    private BigDecimal productMoney;

    /**
     * 单据总金额
     */
    private BigDecimal totalMoney;

    /**
     * 订单实际付款金额
     */
    private BigDecimal money;

    /**
     * 运费
     */
    private BigDecimal freightAmount;

    /**
     * 店铺优惠券金额
     */
    private BigDecimal couponShopAmount;

    /**
     * 店铺营销活动优惠金额
     */
    private BigDecimal marketingShopAmount;

    /**
     * 店铺总优惠金额
     */
    private BigDecimal shopTotalDiscount;

    /**
     * 平台优惠券金额
     */
    private BigDecimal couponPlatformAmount;

    /**
     * 平台营销活动优惠金额
     */
    private BigDecimal marketingPlatformAmount;

    /**
     * 平台总优惠金额
     */
    private BigDecimal platformTotalDiscount;

    /**
     * 佣金
     */
    private BigDecimal hireMoney;

    /**
     * 应收佣金
     */
    private BigDecimal payableCommission;

    /**
     * 冲抵平台优惠后的佣金
     */
    private BigDecimal deductedCommission;

    /**
     * 是否冲抵优惠 0-未冲抵 1-已冲抵
     */
    private Byte deducted;

    /**
     * 处罚金额
     */
    private BigDecimal penaltyAmount;

    /**
     * 应结算金额
     */
    private BigDecimal statementTotalMoney;

    /**
     * 支付类型(1:在线支付 2:货到付款 3:线下转账）
     */
    private Byte payType;

    /**
     * 账单入账状态 0-未入账 1-已入账
     */
    private Byte billPaymentStatus;

    /**
     * 账单入账时间
     */
    private Date billPaymentTime;

    /**
     * 打款状态 0：未打款，1:已打款
     */
    private Byte remitStatus;

    /**
     * 确认打款日期
     */
    private Date remitTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 账单生成开始时间
     */
    private Date startCreateTime;
    /**
     * 账单生成结束时间
     */
    private Date endCreateTime;
    /**
     * 打款开始时间
     */
    private Date startRemitTime;
    /**
     * 打款结束时间
     */
    private Date endRemitTime;

    private List<String> orgIds;

    /**
     * 分润状态 0: 初始化 1:未分润 2:分润成功 3:分润失败
     */
    private Byte billShareStatus;

    /**
     * 分润时间
     */
    private Date billShareTime;

    /**
     * 分润结果
     */
    private String billShareResult;

    /**
     * 支付通道
     */
    private Integer paymentChannel;

    /**
     * 支付平台分润流水号
     */
    private String tradeNo;

    /**
     * 直连订单实付总金额
     */
    private BigDecimal directOrderRealMoney;

    /**
     * 退款单实付总金额
     */
    private BigDecimal refundOrderRealMoney;

    /**
     * 平安订单实付
     */
    private BigDecimal paOrderRealMoney;

    /**
     * 富民订单实付
     */
    private BigDecimal fmOrderRealMoney;

    /**
     * 购物金订单实付
     */
    private BigDecimal virtualGoldRealMoney;

    /**
     * 退款单现金实退
     */
    private BigDecimal refundCashRealMoney;

    /**
     * 退款单购物金实退
     */
    private BigDecimal refundVirtualGoldRealMoney;

    /**
     * 省ID
     */
    private Long provId;

    /**
     * 省名称
     */
    private String prov;

    /**
     * 操作人ID
     */
    private String updaterCode;

    /**
     * 操作人名称
     */
    private String updaterName;

    /**
     * 现金实付金额
     */
    private BigDecimal cashPayAmount;

    /**
     * 购物金实付金额
     */
    private BigDecimal virtualGold;

    private static final long serialVersionUID = 1L;
}