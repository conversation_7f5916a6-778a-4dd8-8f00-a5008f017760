package com.xyy.ec.pop.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.server.api.merchant.api.admin.CorporationAreaAdminApi;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationAreaDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @version v1
 * @Description 区域
 * <AUTHOR>
 */
@Component
@Slf4j
public class CorporationAreaRemoteAdapter {
    @Reference
    private CorporationAreaAdminApi corporationAreaAdminApi;
    public List<CorporationAreaDto> getCorporationArea(Long cId) throws ServiceException {
        ApiRPCResult<List<CorporationAreaDto>> result;
        try {
            log.info("corporationAreaAdminApi.getCorporationArea(cId:{})",cId);
            result = corporationAreaAdminApi.getCorporationArea(cId);
            log.info("corporationAreaAdminApi.getCorporationArea(cId:{}) return {}",cId, JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("corporationAreaAdminApi.getCorporationArea(cId:{}) 异常",cId, e);
            throw new ServiceException("查询经营区域异常");
        }
        if(result.isFail()){
            throw new ServiceException(result.getErrMsg());
        }
        return result.getData();
    }

    public void saveCorporationArea(String orgId, List<CorporationAreaDto> dtos, String username, Long userId) throws ServiceException {
        ApiRPCResult<Boolean> result;
        try {
            log.info("corporationAreaAdminApi.getCorporationArea(orgId:{},username:{},dtos:{})",orgId,username,JSON.toJSONString(dtos));
            result = corporationAreaAdminApi.saveCorporationArea(orgId,dtos,username,userId);
            log.info("corporationAreaAdminApi.getCorporationArea(orgId:{},username:{},dtos:{}) return {}",orgId,username,JSON.toJSONString(dtos), JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("corporationAreaAdminApi.getCorporationArea(orgId:{},username:{},dtos:{}) 异常",orgId,username,JSON.toJSONString(dtos), e);
            throw new ServiceException("更新经营区域异常");
        }
        if(result.isFail()){
            throw new ServiceException(result.getErrMsg());
        }
    }

    public List<CorporationAreaDto> getCorporationAreaByCids(List<Long> cids) throws ServiceException {
        ApiRPCResult<List<CorporationAreaDto>> result;
        try {
            log.info("corporationAreaAdminApi.getCorporationAreaByCids(cids:{})",cids);
            result = corporationAreaAdminApi.getCorporationAreaByCids(cids);
            log.info("corporationAreaAdminApi.getCorporationAreaByCids(cids:{}) return {}",cids, JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("corporationAreaAdminApi.getCorporationAreaByCids(cids:{}) 异常",cids, e);
            throw new ServiceException("查询经营区域异常");
        }
        if(result.isFail()){
            throw new ServiceException(result.getErrMsg());
        }
        return result.getData();
    }
}
