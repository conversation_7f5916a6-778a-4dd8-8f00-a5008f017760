package com.xyy.ec.pop.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PopCorporationDto implements Serializable {

    private static final long serialVersionUID = 217863792570823073L;

    /**
     * 商户编号
     */
    private String orgId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 法人证件号
     */
    private String reprGlobalId;

    /**
     * 法人姓名
     */
    private String reprName;

    private String businessIdType;

    public boolean validParam() {
        return StrUtil.isNotBlank(orgId) && StrUtil.isNotBlank(companyName) && StrUtil.isNotBlank(reprGlobalId) && StrUtil.isNotBlank(reprName);
    }

}
