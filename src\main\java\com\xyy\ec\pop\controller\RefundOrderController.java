package com.xyy.ec.pop.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.base.Page;
import com.xyy.ec.pop.config.XyyConfig;
import com.xyy.ec.pop.config.XyyPopBaseConfig;
import com.xyy.ec.pop.dto.RefundOrderDTO;
import com.xyy.ec.pop.exception.ServiceRuntimeException;
import com.xyy.ec.pop.model.RefundTransferDto;
import com.xyy.ec.pop.model.SysUser;
import com.xyy.ec.pop.remote.EcOrderRemote;
import com.xyy.ec.pop.remote.PopCorporationRemoteAdapter;
import com.xyy.ec.pop.remote.ProductSkuRemoteAdapter;
import com.xyy.ec.pop.server.api.merchant.dto.OrgUserRelationDto;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuAdminApi;
import com.xyy.ec.pop.server.api.product.api.admin.PopSkuCategoryAdminApi;
import com.xyy.ec.pop.server.api.product.dto.PopSkuCategoryDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuDto;
import com.xyy.ec.pop.server.api.product.dto.PopSkuInstructionDto;
import com.xyy.ec.pop.service.*;
import com.xyy.ec.pop.service.domains.OrderDomainService;
import com.xyy.ec.pop.utils.CollectionUtil;
import com.xyy.ec.pop.utils.DateUtil;
import com.xyy.ec.pop.vo.*;
import com.xyy.ec.pop.vo.order.OrderAdminVo;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuDTO;
import com.xyy.ec.product.back.end.ecp.csu.dto.ec.result.EcSkuInstructionDto;
import com.xyy.pop.framework.core.utils.ResponseUtils;
import com.xyy.pop.framework.core.vo.Response;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 退款单管理
 * <AUTHOR>
 */
@Controller
@Slf4j
@RequestMapping("orderRefund")
public class RefundOrderController extends BaseController {
    @Autowired
    private XyyConfig xyyConfig;
    @Autowired
    private XyyPopBaseConfig popBaseConfig;
    @Autowired
    private BranchService branchService;

    @Autowired
    private PopCorporationRemoteAdapter popCorporationRemoteAdapter;
    @Autowired
    private OrderDomainService orderDomainService;
    @Reference
    private PopSkuAdminApi popSkuAdminApi;
    @Autowired
    private PopRefundOrderService popRefundOrderService;

    @Autowired
    private EcOrderRemote ecOrderRemote;

    //订单es 开关 0 开 1关
    @Value("${order.orderes.refund.switch}")
    private String orderRefundEsSwitch;

    @Reference
    private PopSkuCategoryAdminApi popSkuCategoryAdminApi;

    @Autowired
    private ProductSkuRemoteAdapter productSkuRemoteAdapter;

    /**
     * 判断商品为医疗器械的一级分类
     */
    @Value("${product.instrument.category}")
    public String instrumentCategory;
    /**
     * 退款单列表页
    */
    @RequestMapping(value = "/index", method = {RequestMethod.GET })
    public ModelAndView index() {
//        return "refundorder/index";

        ModelAndView orderModel = new ModelAndView("refundorder/index");

        //获取区域列表
        List<BranchVo> branchVos = branchService.getAllProvinces();
        orderModel.addObject("branchList", branchVos);
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH,-1);
        c.set(c.get(Calendar.YEAR),c.get(Calendar.MONTH),1,0,0,0);
        orderModel.addObject("defaultStartCreateTime", DateUtil.date2String(c.getTime(),DateUtil.PATTERN_DATE));
        return orderModel;
    }

    /**
     * 退款单详情页
     */
    @RequestMapping(value = "/toRefund", method = { RequestMethod.GET })
    @ApiOperation("退款单详情页")
    @ApiImplicitParam(name = "refundOrderNo",value = "退款单号")
    public ModelAndView toRefund(String refundOrderNo) {
        String smallImgUrlPrefix = popBaseConfig.getSmallImgUrlPrefix();
        ModelAndView mav = new ModelAndView("refundorder/refund");
        mav.addObject("smallImgUrlPrefix",smallImgUrlPrefix);
        mav.addObject("refundOrderNo",refundOrderNo);
        return mav;
    }

    /**
     * 退款单详情页
     */
    @RequestMapping(value = "/toRefundInfo", method = { RequestMethod.GET })
    @ApiOperation("退款单详情页")
    @ApiImplicitParam(name = "refundOrderNo",value = "退款单号")
    public ModelAndView toRefundInfo(String refundOrderNo) {
        ModelAndView mav = new ModelAndView("refundorder/refundDetail");

        RefundOrderParamVo paramVo = new RefundOrderParamVo();
        paramVo.setRefundOrderNo(refundOrderNo);

        //查询退款单信息
        PageInfo<RefundOrderDetailAdminVo> pageInfo = popRefundOrderService.queryRefundOrderPage(1,1, paramVo);
        //查询订单信息
        if (pageInfo != null && CollectionUtil.isNotEmpty(pageInfo.getList())){
            RefundOrderDetailAdminVo refundOrderVo = pageInfo.getList().get(0);
            OrderAdminVo paramOrder = new OrderAdminVo();
            paramOrder.setIsThirdCompany(-1);
            paramOrder.setOrderNo(refundOrderVo.getOrderNo());
            Page<OrderAdminVo> orderPage = ecOrderRemote.popOrderList(paramOrder, null, 0, 1);
            OrderAdminVo order = orderPage.getRows().get(0);
            List<OrgUserRelationDto> orgUserRelationDtos = orderDomainService.selectOrgUserRelation(Lists.newArrayList(order.getMerchantId()));
            Map<String,OrgUserRelationDto> orgUserRelationDtoMap  = new HashMap<>();
            if (CollectionUtil.isNotEmpty(orgUserRelationDtos)){
                orgUserRelationDtoMap =  orgUserRelationDtos.stream().filter(Objects::nonNull).collect(Collectors.toMap(o -> o.getMerchantId()+o.getOrgId(),Function.identity(),(o1,o2) -> o1));
            }

            if (CollectionUtil.isNotEmpty(orgUserRelationDtos)){
                OrgUserRelationDto orgUserRelationDto = orgUserRelationDtoMap.get(order.getMerchantId() + order.getOrgId());
                if(null != orgUserRelationDto){
                    order.setMerchantErpCode(orgUserRelationDto.getSellerUserId());
                }
            }
            mav.addObject("refundExpress",popRefundOrderService.queryRefundExpressByOrderRefundId(refundOrderVo.getId()));
            mav.addObject("order",order);
            mav.addObject("refundOrder",refundOrderVo);
        }

        mav.addObject("fastDfsUrl",xyyConfig.getFastHostUrl());
        mav.addObject("showUrl",xyyConfig.getCdnConfig().getShowUrl());
        mav.addObject("refundOrderNo",refundOrderNo);
        return mav;
    }

    /**
     * 调整到校验电汇退款单页面
     */
    @RequestMapping(value = "/checkTransferStatus", method = { RequestMethod.GET })
    @ApiOperation("退款单详情页")
    @ApiImplicitParam(name = "refundId",value = "退款单ID")
    public ModelAndView checkTransferStatus(@RequestParam("refundId") Long refundId,
                                            @RequestParam("refundOrderNo") String refundOrderNo) {
        ModelAndView mav = new ModelAndView("refundorder/checkTransferStatus");

        mav.addObject("refundId",refundId);
        mav.addObject("refundOrderNo",refundOrderNo);
        return mav;
    }

    /**
     * 调整到校验电汇退款单页面
     */
    @RequestMapping(value = "/transfer", method = { RequestMethod.POST })
    @ApiOperation("退款单详情页")
    @ApiImplicitParam(name = "refundOrderNo",value = "退款单号")
    @ResponseBody
    public Response transfer(RefundTransferDto refundTransferDto) {
        try {
            log.info("refundOrderController transfer request refundTransferDto:{}",JSONObject.toJSONString(refundTransferDto));
            SysUser user = getUser();
            popRefundOrderService.refundOrder(refundTransferDto.getRefundId(), refundTransferDto.getStatus(), user.getRealName(),refundTransferDto.getEvidenceImages());
            return ResponseUtils.returnSuccess("退款成功");
        } catch (Exception e) {
            log.error("修改退款单失败 信息:{}", JSONObject.toJSONString(refundTransferDto));
            return ResponseUtils.returnException(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/list",method = RequestMethod.GET)
    @ApiOperation("退款单列表")
    @ApiImplicitParam(name = "refundOrderDTO",value = "退款单实体")
    public Page<PageInfo> list(RefundOrderParamVo paramVo) {
        try {
            int offset = paramVo.getOffset();
            int limit = paramVo.getLimit();
            Page page =  new Page<>(offset,limit);
            if(validateProv(paramVo)){
                page.setTotal(0L);
                page.setRows(Collections.EMPTY_LIST);
                return page;
            }

            int pageNum = (offset / limit) + 1;
            if (!Objects.equals("0",orderRefundEsSwitch)) {
                handleOrderParam(paramVo);
            }
            if(null == paramVo.getIsThirdCompany()){
                paramVo.setIsThirdCompany(1);
            }
            log.info("#refundOrderDTO#{}", JSON.toJSONString(paramVo));
            PageInfo<RefundOrderDetailAdminVo> pageInfo = popRefundOrderService.queryRefundOrderPage(pageNum,limit, paramVo);
            if(pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
                page.setTotal(0L);
                page.setRows(Collections.EMPTY_LIST);
                return page;
            }
            List<RefundOrderDetailAdminVo> list = pageInfo.getList();
            Map<Long, String> map = getProv();
            for (RefundOrderDetailAdminVo orderVo : list) {
                if(orderVo.getProvId() != null){
                    orderVo.setProv(map.getOrDefault(orderVo.getProvId(), StringUtils.EMPTY));
                }
            }

            page.setRows(list);
            page.setTotal(pageInfo.getTotal());
            return page;
        } catch (ServiceRuntimeException e) {
            log.error("查询退款订单列表信息异常", e);
            return null;
        }
    }
    /* 处理orgId 参数 关键字搜索 传入参数 corporationNo */
    private void handleOrderParam(RefundOrderParamVo paramOrder) {
        String corporationNo = paramOrder.getCorporationNo();
        String corporationName = paramOrder.getCorporationName();
        String companyName = paramOrder.getCompanyName();
        paramOrder.setOrgId(corporationNo);
        if (StringUtils.isNotEmpty(companyName)|| StringUtils.isNotBlank(corporationName)){
            List<String> orgIds = popCorporationRemoteAdapter.getOrgIdByName(corporationName,companyName );
            if (CollectionUtil.isNotEmpty(orgIds)){
                List<String> orgIdList = paramOrder.getOrgIdList();
                if (orgIdList == null){
                    orgIdList = new ArrayList<>();
                }
                orgIdList.addAll(orgIds);
                paramOrder.setOrgIdList(orgIdList);
            }else{
                orgIds.add("-1");
                paramOrder.setOrgIdList(orgIds);
            }
        }
    }

    private boolean validateProv(RefundOrderParamVo refundOrderVo) {
        List<Long> provIds = getProvIds(refundOrderVo.getProvId());
        if(org.apache.commons.collections.CollectionUtils.isEmpty(provIds)){
            return true;
        }
        refundOrderVo.setProvIds(provIds);
        return false;
    }

    @ResponseBody
    @RequestMapping(value = "/refundOrderDetailList",method = RequestMethod.GET)
    @ApiOperation("退款单明细列表")
    @ApiImplicitParam(name = "refundOrderNo",value = "退款单编号")
    public Page<PageInfo> refundOrderDetailList(RefundOrderDTO refundOrderDTO) {
        try {
            int offset = refundOrderDTO.getOffset();
            int limit = refundOrderDTO.getLimit();
            Page page =  new Page<>(offset,limit);
            int pageNum = (offset / limit) + 1;
            log.info("#refundOrderDTO#{}",refundOrderDTO);
            PageInfo<RefundOrderDetailDto> pageInfo = ecOrderRemote.queryRefundDetailPage(pageNum, limit, refundOrderDTO.getRefundOrderNo());

            if(pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
                page.setTotal(0L);
                page.setRows(Collections.EMPTY_LIST);
                return page;
            }

            List<RefundOrderDetailDto> list = pageInfo.getList();
            List<Long> skuIds = list.stream().map(RefundOrderDetailDto::getSkuId).collect(Collectors.toList());
            Map<Long,PopSkuDto> popSkuDtoMap = new HashMap<>();

            ApiRPCResult<List<PopSkuDto>> apiRPCResult = popSkuAdminApi.findByCsuIdsWithDeleted(skuIds);
            if (apiRPCResult.isSuccess()){
                List<PopSkuDto> popSkuDtos = apiRPCResult.getData();
                popSkuDtoMap = popSkuDtos.stream().collect(Collectors.toMap(PopSkuDto::getCsuid, Function.identity(), (o1, o2) -> o1));
            }

            Map<String, PopSkuInstructionDto> popSkuInstructionDtoMap = new HashMap<>();

            List<String> barcodes = list.stream().map(RefundOrderDetailDto::getBarcode).collect(Collectors.toList());
            ApiRPCResult<List<PopSkuInstructionDto>> popSkuInstructionDtoApi = popSkuAdminApi.findSkuInstructionByBarcodes(barcodes);
            if (popSkuInstructionDtoApi.isSuccess()){
                List<PopSkuInstructionDto> skuInstructionDtos = popSkuInstructionDtoApi.getData();
                popSkuInstructionDtoMap = skuInstructionDtos.stream().collect(Collectors.toMap(PopSkuInstructionDto::getBarcode, Function.identity(), (o1, o2) -> o1));
            }

            Map<String, PopSkuCategoryDto> popSkuCategoryDtoMap = new HashMap<>();
            ApiRPCResult<List<PopSkuCategoryDto>> listApiRPCResult = popSkuCategoryAdminApi.categoryByBarcodes(barcodes);
            if (listApiRPCResult.isSuccess()){
                List<PopSkuCategoryDto> popSkuCategoryDtos = listApiRPCResult.getData();
                popSkuCategoryDtoMap = popSkuCategoryDtos.stream().collect(Collectors.toMap(PopSkuCategoryDto::getBarcode, Function.identity(), (o1, o2) -> o1));
            }
            for (RefundOrderDetailDto orderDetail : list) {
                PopSkuDto popSkuDto = popSkuDtoMap.get(orderDetail.getSkuId());
                PopSkuCategoryDto popSkuCategoryDto = popSkuCategoryDtoMap.get(orderDetail.getBarcode());
                if (Objects.nonNull(popSkuDto)){
                    orderDetail.setErpCode(popSkuDto.getErpCode());
                    if (Objects.nonNull(popSkuCategoryDto) && Objects.equals(instrumentCategory,popSkuCategoryDto.getBusinessFirstCategoryCode())) {
                        orderDetail.setApprovalNumber(popSkuDto.getApprovalNumber());
                    }
                }

                PopSkuInstructionDto popSkuInstructionDto = popSkuInstructionDtoMap.get(orderDetail.getBarcode());
                if (Objects.nonNull(popSkuInstructionDto)){
                    orderDetail.setManufacturingLicenseNo(popSkuInstructionDto.getManufacturingLicenseNo());
                }
            }
            //填充自营商品信息
            fillECSkuInfo(list);
            page.setRows(pageInfo.getList());
            page.setTotal(pageInfo.getTotal());
            return page;
        } catch (ServiceRuntimeException e) {
            log.error("查询退款订单列表信息异常", e);
            return null;
        }
    }

    private void fillECSkuInfo(List<RefundOrderDetailDto> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        //查找没有erpCode 和生产许可证的数据（两个都没有的 去ec查询）
        List<Long> ecSkuIds = list.stream()
                .filter(dto -> StringUtils.isEmpty(dto.getErpCode()) && StringUtils.isEmpty(dto.getApprovalNumber()) && StringUtils.isEmpty(dto.getManufacturingLicenseNo()))
                .map(RefundOrderDetailDto::getSkuId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ecSkuIds)){
            return;
        }
        List<CsuDTO> ecProductBySkuIds = productSkuRemoteAdapter.findECProductBySkuIds(ecSkuIds);
        //商品map
        Map<Long, CsuDTO> csuDTOMap = ecProductBySkuIds.stream()
                .collect(Collectors.toMap(dto -> dto.getId(), Function.identity(), (v1, v2)->v1));
        if(MapUtils.isEmpty(csuDTOMap)){
            return;
        }
        for(RefundOrderDetailDto detail : list){
            if(StringUtils.isNotEmpty(detail.getErpCode())
                    || StringUtils.isNotEmpty(detail.getApprovalNumber())
                    || StringUtils.isNotEmpty(detail.getManufacturingLicenseNo())){
                continue;
            }
            Long skuId = detail.getSkuId();
            CsuDTO csuDTO = csuDTOMap.get(skuId);
            if(null != csuDTO){
                detail.setErpCode(csuDTO.getErpCode());
                detail.setApprovalNumber(csuDTO.getApprovalNumber());
                detail.setManufacturingLicenseNo(csuDTO.getManufacturingLicenseNo());
            }
        }


    }
}
