package com.xyy.ec.pop.po;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR> 
 */
@Data
public class PopBillSettleDetailPo implements Serializable {
    private Long id;

    /**
     * 订单明细id
     */
    private Long orderDetailId;

    /**
     * 单据号
     */
    private String businessNo;

    private Long csuid;

    /**
     * 商业商品编码
     */
    private String erpCode;

    /**
     * 商品一级分类
     */
    private String businessFirstCategoryCode;

    /**
     * 商品原价
     */
    private BigDecimal productOrginPrice;

    /**
     * 数量
     */
    private BigDecimal productAmount;

    /**
     * 商品金额
     */
    private BigDecimal productMoney;

    /**
     * 店铺总优惠
     */
    private BigDecimal shopTotalDiscount;

    /**
     * 平台总优惠金额
     */
    private BigDecimal platformTotalDiscount;

    /**
     * 商品实付金额
     */
    private BigDecimal payAmount;

    /**
     * 佣金金额
     */
    private BigDecimal hireMoney;

    /**
     * 应缴纳佣金
     */
    private BigDecimal payableCommission;

    /**
     * 佣金折扣
     */
    private BigDecimal commissionDiscount;

    /**
     * 折扣原因
     */
    private String discountReason;

    /**
     * 下单时刻佣金比例
     */
    private BigDecimal commissionRatio;

    /**
     * 实际需缴纳佣金
     */
    private BigDecimal actualCommissionMoney;

    /**
     * 佣金优惠
     */
    private BigDecimal commissionDiscountMoney;

    /**
     * POP商品编码
     */
    private String barcode;

    /**
     * 是否校验差异:0、未校验，1、已校验
     */
    private Byte isCheckDiff;

    /**
     * 一级分类佣金比例
     */
    private BigDecimal firstCommissionRatio;

    /**
     * 一级分类佣金金额
     */
    private BigDecimal firstHireMoney;

    private static final long serialVersionUID = 1L;
}