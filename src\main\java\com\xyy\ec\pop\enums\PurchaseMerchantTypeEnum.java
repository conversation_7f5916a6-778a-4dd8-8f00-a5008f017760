package com.xyy.ec.pop.enums;

import java.util.HashMap;
import java.util.Map;

public enum PurchaseMerchantTypeEnum {
        PHARMACY(1, "药店"),
        CLINIC(2, "诊所");
        private int status;
        private String name;

        private PurchaseMerchantTypeEnum(int status, String name) {
            this.status = status;
            this.name = name;
        }

        public int getStatus() {
            return this.status;
        }

        public String getName() {
            return this.name;
        }

    private static final Map<Integer, PurchaseMerchantTypeEnum> controlMaps = new HashMap<>();
    public static final Map<Integer,String> maps = new HashMap<>();
    static {
        for(PurchaseMerchantTypeEnum apEnum : PurchaseMerchantTypeEnum.values()) {
            controlMaps.put(apEnum.getStatus(), apEnum);
            maps.put(apEnum.getStatus(),apEnum.getName());
        }
    }
    public static String get(int id) {
        return controlMaps.get(id).getName();
    }

}
