package com.xyy.ec.pop.controller;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pop.base.BaseController;
import com.xyy.ec.pop.config.OssConfig;
import com.xyy.ec.pop.config.XyyConfig;
import com.xyy.ec.pop.exception.ServiceException;
import com.xyy.ec.pop.service.ProtectSkuService;
import com.xyy.ec.pop.utils.oss.OssUtil;
import com.xyy.ec.pop.vo.ProtectSkuExcelVo;
import com.xyy.ec.pop.vo.ResponseVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version v1
 * @Description 保护品种
 * <AUTHOR>
 */
@Controller
@RequestMapping("/protectSku")
@Slf4j
public class ProtectSkuController extends BaseController {
    @Value("${protectSku.import.excel.title:商品标准库ID}")
    private String title;
    @Value("${protectSku.import.excel.maxFileSize:1}")
    private int maxFileSize;
    @Value("${protectSku.import.excel.maxRows}")
    private int maxRows;
    @Value("${protectSku.import.excel.tempPath}")
    private String tempPath;
    @Autowired
    private ProtectSkuService protectSkuService;
    @Autowired
    private XyyConfig xyyConfig;
    @Autowired
    private OssConfig ossConfig;

    @GetMapping(value = "/index")
    @ApiOperation("跳转商品页面")
    public String index(){
        return "protectProduct/index";
    }
    /**
     * 上传保护商品文件
     * @param file
     * @return
     */
    @PostMapping(value = "/update")
    @ResponseBody
    public ResponseVo update(HttpServletRequest request, @RequestParam("file") MultipartFile file){
        try{
            List<Long> ids = readExcel(file);
            if(ids.size()>maxRows){
                return ResponseVo.errRest("商品个数不超"+maxRows);
            }
            String fileName = UUID.randomUUID().toString()+"_"+file.getOriginalFilename();
//            boolean success = FileUploadUtil.uploadFile(xyyConfig,file.getInputStream(),tempPath,fileName);
            boolean success = OssUtil.uploadFileInputStream(ossConfig, tempPath + "/" + fileName, file.getInputStream());
            if(!success){
                log.info("上传保护商品,文件保存失败");
                return ResponseVo.errRest("上传保护商品失败");
            }
            String path = tempPath+"/"+fileName;
            protectSkuService.update(path,file.getOriginalFilename(),ids, getUser());
            return ResponseVo.successResult(true);
        } catch (ServiceException e){
            log.info("上传保护商品失败：{}",e.getMessage());
            return ResponseVo.errRest("上传保护商品失败");
        }catch (Exception e){
            log.error("上传保护商品失败 ",e);
            return ResponseVo.errRest("上传保护商品失败");
        }
    }

    @GetMapping(value = "/page")
    @ResponseBody
    public ResponseVo page(@RequestParam(value = "pageNum",defaultValue = "1") Integer pageNum,
                           @RequestParam(value = "pageSize",defaultValue = "10") Integer pageSize){
        try{
            PageInfo<ProtectSkuExcelVo> page =  protectSkuService.page(pageNum,pageSize);
            page.getList().forEach(item->{
                item.setFilePath(xyyConfig.getCdnConfig().getShowUrl()+item.getFilePath());
            });
            return ResponseVo.successResult(page);
        } catch (ServiceException e){
            log.info("查询保护商品失败：{}",e.getMessage());
            return ResponseVo.errRest("查询保护商品失败");
        }catch (Exception e){
            log.error("查询保护商品失败 ",e);
            return ResponseVo.errRest("查询保护商品失败");
        }
    }

    /**
     * 读取多个excel
     * @param file
     * @return
     */
    private List<Long> readExcel(MultipartFile file) throws Exception {
        if(file.isEmpty()||file.getSize()>maxFileSize*(1024*1024)){
            throw new ServiceException("文件大小不能超过"+maxFileSize+"M");
        }
        String fileName = file.getOriginalFilename();
        if(StringUtils.isEmpty(fileName)||(!fileName.toLowerCase().endsWith(".xls")&&!fileName.toLowerCase().endsWith(".xlsx"))){
            throw new ServiceException("文件格式错误");
        }
        List<Map<String,String>> list = readExcel(file,title);
        if(CollectionUtils.isEmpty(list)){
            throw new ServiceException("文件不能为空");
        }
        if(list.get(0)==null||!list.get(0).containsKey(title)){
            throw new ServiceException("请按要求一传文件");
        }
        return list.stream().map(item-> NumberUtils.toLong(String.valueOf(item.get(title)))).filter(item->item>0).collect(Collectors.toList());
    }

    /**
     * 读取多个excel
     * @param file
     * @param titles
     * @return
     */
    private List<Map<String, String>> readExcel(MultipartFile file, String... titles) throws ServiceException {
        try {
            Workbook workbook = file.getOriginalFilename().toLowerCase().endsWith(".xlsx")?new XSSFWorkbook(file.getInputStream()):new HSSFWorkbook(file.getInputStream());
            Iterator<Sheet> shets = workbook.sheetIterator();
            List<Map<String, String>> list = new ArrayList<>(256);
            while (shets.hasNext()){
                Sheet sheet = shets.next();
                Map<Integer,String> titleMap = validTitle(sheet,titles);
                list.addAll(readExcelData(sheet,titleMap));
            }
            return list;
        } catch (IOException e) {
            log.error("导入保护商品读取excel失败",e);
            throw new ServiceException("读取excel文件失败");
        }
    }

    private List<Map<String, String>> readExcelData(Sheet sheet, Map<Integer, String> titleMap) {
        List<Map<String, String>> list = new ArrayList<>();
        for(int i = 1;i<=sheet.getLastRowNum();i++){
            Row row = sheet.getRow(i);
            Map<String, String> map = new HashMap<>(titleMap.size());
            for(int j = row.getFirstCellNum();j<row.getLastCellNum();j++){
                Cell cell = row.getCell(j);
                String title = titleMap.get(j);
                if(title!=null){
                    map.put(titleMap.get(j),readeStringValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private String readeStringValue(Cell cell) {
        switch (cell.getCellTypeEnum()){
            case NUMERIC:
                return String.valueOf((long)cell.getNumericCellValue());
            default:
                return StringUtils.trimToNull(cell.getStringCellValue());
        }
    }

    private Map<Integer,String> validTitle(Sheet sheet, String[] titles) throws ServiceException {
        Row row = sheet.getRow(0);
        if(row==null){
            throw new ServiceException(sheet.getSheetName()+"没有标题");
        }
        Map<Integer,String> values = new HashMap<>(row.getLastCellNum()-row.getFirstCellNum());
        for(int i = row.getFirstCellNum();i<row.getLastCellNum();i++){
            Cell cell = row.getCell(i);
            String title = cell.getStringCellValue();
            title = StringUtils.trimToNull(title);
            if(title!=null){
                values.put(i,cell.getStringCellValue());
            }
        }
        if(!values.values().contains(titles[0])){
            throw new ServiceException(sheet.getSheetName()+"标题不符合要求");
        }
        return values;
    }

}
