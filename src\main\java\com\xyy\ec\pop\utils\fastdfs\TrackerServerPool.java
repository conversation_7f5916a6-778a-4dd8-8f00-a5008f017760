package com.xyy.ec.pop.utils.fastdfs;

import com.xyy.ec.pop.config.FastDfsConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.csource.common.MyException;
import org.csource.fastdfs.ClientGlobal;
import org.csource.fastdfs.TrackerServer;

import java.io.IOException;
import java.util.Properties;

import static org.csource.fastdfs.ClientGlobal.*;

/**
 * TrackerServer 对象池
 */
@Slf4j
public class TrackerServerPool {

    /**
     * TrackerServer 对象池.
     * GenericObjectPool 没有无参构造
     */
    private static GenericObjectPool<TrackerServer> trackerServerPool;

    private TrackerServerPool(){};

    private static synchronized GenericObjectPool<TrackerServer> getObjectPool(FastDfsConfig fastDfsConfig){
        if(trackerServerPool == null){
            try {
                Properties properties = new Properties();
                properties.setProperty(PROP_KEY_TRACKER_SERVERS,fastDfsConfig.getTrackerServers());
                properties.setProperty(PROP_KEY_CONNECT_TIMEOUT_IN_SECONDS,String.valueOf(fastDfsConfig.getConnectTimeOut()));
                properties.setProperty(PROP_KEY_NETWORK_TIMEOUT_IN_SECONDS,String.valueOf(fastDfsConfig.getNetWorkTimeout()));
                properties.setProperty(PROP_KEY_CHARSET,fastDfsConfig.getCharSet());
                properties.setProperty(PROP_KEY_HTTP_ANTI_STEAL_TOKEN,String.valueOf(fastDfsConfig.isStealToken()));
                properties.setProperty(PROP_KEY_HTTP_SECRET_KEY,fastDfsConfig.getSecretKey());
                properties.setProperty(PROP_KEY_HTTP_TRACKER_HTTP_PORT,String.valueOf(fastDfsConfig.getHttpPort()));
                ClientGlobal.initByProperties(properties);
            } catch (IOException e) {
                e.printStackTrace();
            } catch (MyException e) {
                e.printStackTrace();
            }

            if(log.isDebugEnabled()){
                log.debug("ClientGlobal configInfo: {}", ClientGlobal.configInfo());
            }

            // Pool配置
            GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
            poolConfig.setMinIdle(2);
            if(fastDfsConfig.getMaxStorageConnection() > 0){
                //poolConfig.setMaxTotal(fastDfsConfig.getMaxStorageConnection());
            }

            trackerServerPool = new GenericObjectPool<>(new TrackerServerFactory(), poolConfig);
        }
        return trackerServerPool;
    }

    /**
     * 获取 TrackerServer
     * @return TrackerServer
     * @throws Exception
     */
    public static TrackerServer borrowObject(FastDfsConfig fastDfsConfig) {
        TrackerServer trackerServer = null;
        try {
            trackerServer = getObjectPool(fastDfsConfig).borrowObject();
        } catch (Exception e) {
            log.warn("borrowObject",e);
        }
        return trackerServer;
    }

    /**
     * 回收 TrackerServer
     * @param trackerServer 需要回收的 TrackerServer
     */
    public static void returnObject(TrackerServer trackerServer,FastDfsConfig fastDfsConfig){
        getObjectPool(fastDfsConfig).returnObject(trackerServer);
    }
}
